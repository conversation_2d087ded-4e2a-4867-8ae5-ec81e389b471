<?php

namespace frontendPc\models;

use common\base\models\BaseArticle;
use common\base\models\BaseCompanyMemberConfig;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobApplyRecordExtra;
use common\base\models\BaseJobCollect;
use common\base\models\BaseJobContact;
use common\base\models\BaseJobContactSynergy;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseMemberActionLog;
use common\base\models\BaseOffSiteJobApply;
use common\base\models\BaseResume;
use common\base\models\BaseTrade;
use common\helpers\DebugHelper;
use common\helpers\FormatConverter;
use common\helpers\UrlHelper;
use common\libs\CompanyAuthority\BaseRule;
use common\libs\CompanyAuthority\CompanyAuthorityClassify;
use common\libs\JobBatchImport;
use common\service\company\PushEditMessageService;
use common\service\companyAuth\ButtonGroupAuthService;
use common\service\companyPackage\CompanyPackageApplication;
use common\service\job\BaseService as BaseServiceAlias;
use common\service\job\BatchJobService;
use frontendPc\models\Area;
use frontendPc\models\Company;
use frontendPc\models\CompanyContact;
use frontendPc\models\CompanyInfoAuth;
use frontendPc\models\Dictionary;
use frontendPc\models\JobApply;
use frontendPc\models\Member;
use frontendPc\models\Resume;
use frontendPc\models\Trade;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobEdit;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseMemberMessage;
use common\base\models\BaseResumeIntention;
use common\base\models\BaseWelfareLabel;
use common\helpers\ArrayHelper;
use common\helpers\FileHelper;
use common\helpers\IpHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\helpers\UUIDHelper;
use common\helpers\ValidateHelper;
use common\libs\Cache;
use common\libs\Excel;
use common\models\File;
use Faker\Provider\Base;
use frontendPc\models\MemberActionLog;
use common\services\announcement\BaseService;
use Mpdf\Tag\B;
use Yii;
use yii\base\Exception;
use yii\helpers\Url;

class Job extends BaseJob
{
    const BASE_JOB_LIST_CONDITION = [
        'and',
        [
            '=',
            'j.is_show',
            BaseJob::IS_SHOW_YES,
        ],
        [
            '<>',
            'j.status',
            BaseJob::STATUS_DELETE,
        ],
        [
            'or',
            //自己建的纯职位都要  公告+职位除了编辑中的
            [
                'and',
                ['j.is_article' => BaseJob::IS_ARTICLE_NO],
                ['j.create_type' => BaseJob::CREATE_TYPE_SELF],
            ],
            [
                'and',
                ['j.is_article' => BaseJob::IS_ARTICLE_YES],
                ['j.create_type' => BaseJob::CREATE_TYPE_SELF],
                [
                    'j.status' => [
                        BaseJob::STATUS_ONLINE,
                        BaseJob::STATUS_OFFLINE,
                    ],
                ],
            ],
            //代理建的编辑中的不要
            [
                'and',
                ['j.create_type' => BaseJob::CREATE_TYPE_AGENT],
                [
                    'not in',
                    'j.status',
                    [
                        BaseJob::STATUS_WAIT,
                        BaseJob::STATUS_DELETE,
                    ],
                ],
            ],
        ],
    ];

    const ORDER_BY_DESC = 1;
    const ORDER_BY_ASC  = 2;

    //    /**
    //     * 获取职位详情
    //     * @param $id
    //     * @return array|\yii\db\ActiveRecord|null
    //     * @throws \Exception
    //     */
    //    public static function getJobDetail($id, $memberId = '')
    //    {
    //        //todo:缺少报名方式、性别要求
    //        $jobDetail = self::find()
    //            ->alias('j')
    //            ->where(['j.id' => $id])
    //            ->leftJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
    //            ->select([
    //                'c.is_cooperation as isCooperation',
    //                'j.id as jobId',
    //                'j.status',
    //                'j.name as jobName',
    //                'j.company_id as companyId',
    //                'j.min_wage as minWage',
    //                'j.max_wage as maxWage',
    //                'j.wage_type as wageType',
    //                'j.experience_type as experienceType',
    //                'j.education_type as educationType',
    //                'j.amount',
    //                'j.job_category_id as jobCategoryId',
    //                'j.welfare_tag as welfareTag',
    //                'j.department',
    //                'j.province_id as provinceId',
    //                'j.city_id as cityId',
    //                'j.district_id',
    //                'j.address',
    //                'j.nature_type as jobNatureType',
    //                'j.major_id as majorId',
    //                'j.title_type as titleType',
    //                'j.age_type as age',
    //                'j.political_type as politicalType',
    //                'j.abroad_type as abroadType',
    //                'j.duty',
    //                'j.requirement',
    //                'j.remark',
    //                'j.gender_type',
    //                'j.period_date as periodDate',
    //                'j.apply_type as applyType',
    //                'j.apply_address as applyAddress',
    //                'j.announcement_id as announcementId',
    //                'j.release_time',
    //                'j.click',
    //                'j.id',
    //                'j.file_ids',
    //                'j.delivery_way',
    //                'j.delivery_type',
    //                'j.refresh_date',
    //            ])
    //            ->asArray()
    //            ->one();
    //
    //
    //        $jobDetail['jobName'] = str_replace(PHP_EOL, '', $jobDetail['jobName']);
    //
    //        //格式化薪资
    //        $jobDetail['wage'] = self::formatWage($jobDetail['minWage'], $jobDetail['maxWage'], $jobDetail['wageType']);
    //        //格式化日期
    //        if ($jobDetail['periodDate'] == TimeHelper::ZERO_TIME) {
    //            // 这里还有一层逻辑,就是如果公告有过期时间,就拿公告的截止日期
    //            $announcementPeriodDate = BaseAnnouncement::find()
    //                ->where(['id' => $jobDetail['announcementId']])
    //                ->select(['period_date'])
    //                ->asArray()
    //                ->one();
    //            if ($announcementPeriodDate && $announcementPeriodDate['period_date'] != TimeHelper::ZERO_TIME) {
    //                $jobDetail['periodDate'] = date('Y-m-d', strtotime($announcementPeriodDate['period_date']));
    //            } else {
    //                $jobDetail['periodDate'] = '详见正文';
    //            }
    //        } else {
    //            $jobDetail['periodDate'] = date('Y-m-d', strtotime($jobDetail['periodDate']));
    //        }
    //        $welfareTagArr                  = BaseWelfareLabel::getWelfareLabelNameList($jobDetail['welfareTag']);
    //        $jobDetail['welfareTagArr']     = [];
    //        $jobDetail['moreWelfareTagArr'] = [];
    //        foreach ($welfareTagArr as $k => $v) {
    //            if ($k < 4) {
    //                array_push($jobDetail['welfareTagArr'], $v);
    //            } else {
    //                array_push($jobDetail['moreWelfareTagArr'], $v);
    //            }
    //        }
    //        $province = BaseArea::getAreaName($jobDetail['provinceId']);
    //        $city     = BaseArea::getAreaName($jobDetail['cityId']);
    //        if ($province == $city) {
    //            $area = $city;
    //        } else {
    //            $area = $province . $city;
    //        }
    //        $jobDetail['fullArea']    = $area . $jobDetail['address'];
    //        $jobDetail['area']        = $jobDetail['city'] = BaseArea::getAreaName($jobDetail['cityId']);
    //        $jobDetail['majorTxt']    = BaseMajor::getAllMajorName(explode(',', $jobDetail['majorId']));
    //        $jobDetail['major']       = BaseMajor::getAllMajorLinkList(explode(',', $jobDetail['majorId']));
    //        $jobDetail['experience']  = BaseDictionary::getExperienceName($jobDetail['experienceType']);
    //        $jobDetail['education']   = BaseDictionary::getEducationName($jobDetail['educationType']);
    //        $jobDetail['jobCategory'] = BaseCategoryJob::getName($jobDetail['jobCategoryId']);
    //        $jobDetail['jobNature']   = BaseDictionary::getNatureName($jobDetail['jobNatureType']);
    //        $jobDetail['title']       = BaseDictionary::getTitleName($jobDetail['titleType']);
    //        $jobDetail['political']   = BaseDictionary::getPoliticalStatusName($jobDetail['politicalType']);
    //        $jobDetail['abroad']      = BaseDictionary::getAbroadName($jobDetail['abroadType']);
    //        $jobDetail['age']         = $jobDetail['age'] ?: '';
    //        //获取申请状态
    //        $jobDetail['applyStatus'] = self::JOB_APPLY_STATUS_NO;
    //        //获取申请类型字段
    //        $jobDetail['isEmailApply']  = "false";
    //        $jobDetail['isOnlineApply'] = false;
    //        $jobDetail['isOtherApply']  = false;
    //        if ($jobDetail['announcementId'] > 0) {
    //            $jobDetail['announcement_file_ids'] = BaseAnnouncement::findOneVal(['id' => $jobDetail['announcementId']],
    //                'file_ids');
    //        }
    //        //保留多一个id，用于判断
    //        $jobDetail['isCooperationId'] = $jobDetail['isCooperation'];
    //
    //        //判断是否合作单位
    //        if ($jobDetail['isCooperation'] == BaseCompany::COOPERATIVE_UNIT_YES) {
    //            $jobDetail['isCooperation'] = 'true';
    //            //如果用户已经登录了，获取用户投递信息
    //            if (!empty($memberId)) {
    //                //获取用户对该职位投递情况
    //                if ($jobDetail['delivery_way'] > 0) {
    //                    if ($jobDetail['delivery_way'] == BaseJob::DELIVERY_WAY_LINK) {
    //                        $jobDetail['applyStatus'] = BaseOffSiteJobApply::checkJobApplyStatus($memberId,
    //                            $jobDetail['jobId']);
    //                    } else {
    //                        //获取用户对该职位投递情况
    //                        $jobDetail['applyStatus'] = BaseJobApply::checkJobApplyStatus($memberId, $jobDetail['jobId']);
    //                    }
    //                } elseif ($jobDetail['announcementId'] > 0) {
    //                    $announcementDeliveryWay = BaseAnnouncement::findOneVal(['id' => $jobDetail['announcementId']],
    //                        'delivery_way');
    //                    if ($announcementDeliveryWay == BaseAnnouncement::DELIVERY_WAY_LINK) {
    //                        $jobDetail['applyStatus'] = BaseOffSiteJobApply::checkJobApplyStatus($memberId,
    //                            $jobDetail['jobId']);
    //                    } else {
    //                        //获取用户对该职位投递情况
    //                        $jobDetail['applyStatus'] = BaseJobApply::checkJobApplyStatus($memberId, $jobDetail['jobId']);
    //                    }
    //                }
    //                //$jobDetail['applyStatus'] = BaseJobApply::checkJobApplyStatus($memberId, $jobDetail['jobId']);
    //            }
    //        } else {
    //            $jobDetail['isCooperation'] = 'false';
    //            $jobDetail['viewingRate']   = '-';
    //            $jobDetail['lastLoginTime'] = '-';
    //            if (!empty($memberId)) {
    //                //获取用户对该职位投递情况
    //                $jobDetail['applyStatus'] = BaseOffSiteJobApply::checkJobApplyStatus($memberId, $jobDetail['jobId']);
    //            }
    //        }
    //
    //        if ($jobDetail['delivery_type']) {
    //            if ($jobDetail['delivery_type'] == BaseJob::DELIVERY_TYPE_INSIDE) {
    //                $jobDetail['applyTypeText'] = BaseJob::getApplyTypeName($jobDetail['applyType']);
    //            } else {
    //                $jobDetail['applyTypeText'] = '站内投递';
    //            }
    //        } else {
    //            if ($jobDetail['announcementId'] > 0) {
    //                $announcementInfo = BaseAnnouncement::findOne(['id' => $jobDetail['announcementId']]);
    //                if ($announcementInfo['delivery_type'] == BaseAnnouncement::DELIVERY_TYPE_INSIDE) {
    //                    $jobDetail['applyTypeText'] = BaseJob::getApplyTypeName($announcementInfo['apply_type']);
    //                } else {
    //                    $jobDetail['applyTypeText'] = '站内投递';
    //                }
    //            }
    //        }
    //        //获取收藏状态
    //        $jobDetail['collectStatus'] = self::JOB_COLLECT_STATUS_NO;
    //        if (!empty($memberId)) {
    //            $collectStatus = BaseJobCollect::getCollectInfo($memberId, $jobDetail['jobId']);
    //            if (!empty($collectStatus)) {
    //                $jobDetail['collectStatus'] = $collectStatus;
    //            }
    //            //获取用户邮箱
    //            $jobDetail['userEmail'] = BaseMember::findOneVal(['id' => $memberId], 'email');
    //        }
    //        $jobDetail['genderType'] = BaseResume::getGenderName($jobDetail['gender_type']); //性别
    //        $jobDetail['companyUrl'] = Url::toRoute([
    //            'company/detail',
    //            'id' => $jobDetail['companyId'],
    //        ]);
    //        $jobDetail['jobTypeUrl'] = Url::toRoute([
    //            '/job',
    //            'jobType' => $jobDetail['jobCategoryId'],
    //        ]);
    //        $jobDetail['jobWorkUrl'] = Url::toRoute([
    //            '/job',
    //            'keyword' => $jobDetail['department'],
    //        ]);
    //
    //        $jobDetail['announcementTitle']  = BaseAnnouncement::findOneVal(['id' => $jobDetail['announcementId']],
    //            'title');
    //        $jobDetail['announcementUrl']    = BaseAnnouncement::getDetailUrl($jobDetail['announcementId']);
    //        $jobDetail['announcementJobUrl'] = BaseAnnouncement::getJobListUrl($jobDetail['announcementId']);
    //
    //        //获取栏目信息
    //        $jobDetail['columnInfo'] = BaseHomeColumn::getInfoListByAnnouncementId($jobDetail['announcementId']);
    //
    //        // 换行前端显示
    //        $jobDetail['requirement'] = str_replace("\n", '<br>', $jobDetail['requirement']);
    //        $jobDetail['remark']      = str_replace("\n", '<br>', $jobDetail['remark']);
    //        $jobDetail['duty']        = str_replace("\n", '<br>', $jobDetail['duty']);
    //
    //        return $jobDetail;
    //    }

    /**
     * 获取职位标题列表
     * @return array
     * @throws Exception
     */
    public static function getNameList($keywords): array
    {
        $memberId  = Yii::$app->user->id;
        $companyId = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'company_id');
        //$companyId = BaseCompany::findOneVal(['member_id' => $memberId], 'id');

        $query = BaseJob::find()
            ->select([
                'id',
                'name',
            ])
            ->where([
                '<>',
                'status',
                BaseJob::STATUS_DELETE,
            ])
            ->andWhere([
                'company_id' => $companyId,
            ]);

        if ($keywords['name']) {
            if (is_numeric($keywords['name']) && strlen($keywords['name']) == 8) {
                $nameChangeUid = UUIDHelper::decryption($keywords['name']);
                $query->andFilterCompare('id', (int)$nameChangeUid);
            } else {
                $query->andFilterCompare('concat(name,id )', $keywords['name'], 'like');
            }
        }

        return $query->asArray()
            ->all();
    }

    /**
     * 获取已通过审核职位地址列表
     */
    public static function getCompanyAddressList(): array
    {
        $memberId = Yii::$app->user->id;
        $company  = BaseCompany::getCompanyInfo($memberId);

        $companyId       = $company['id'];
        $where           = ['and'];
        $where []        = [
            'company_id' => $companyId,
        ];
        $where[]         = [
            'in',
            'status',
            [
                self::STATUS_OFFLINE,
                self::STATUS_ONLINE,
            ],
        ];
        $select          = [
            'province_id',
            'city_id',
            'district_id',
            'address',
        ];
        $result          = [];
        $jobAddressInfos = self::selectInfos($where, $select);
        $areaCache       = BaseArea::setAreaCache();
        foreach ($jobAddressInfos as $k => $jobAddressInfo) {
            $result[$k]['provinceId'] = $jobAddressInfo['province_id'];
            $result[$k]['cityId']     = $jobAddressInfo['city_id'];
            $result[$k]['districtId'] = $jobAddressInfo['district_id'];
            $result[$k]['areaName']   = $areaCache[$jobAddressInfo['province_id']]['name'] . $areaCache[$jobAddressInfo['city_id']]['name'] . $areaCache[$jobAddressInfo['district_id']]['name'];
            $result[$k]['address']    = $jobAddressInfo['address'];
        }
        $result[] = [
            'provinceId' => $company['province_id'],
            'cityId'     => $company['city_id'],
            'districtId' => $company['district_id'],
            'areaName'   => $areaCache[$company['province_id']]['name'] . $areaCache[$company['city_id']]['name'] . $areaCache[$company['district_id']]['name'],
            'address'    => $company['address'],
        ];

        return array_values(array_unique($result, SORT_REGULAR));
    }

    /**
     * 获取职位详情
     * @throws Exception
     */
    public static function getDetails($data)
    {
        if (!$data['id']) {
            throw new Exception('职位Id不能为空');
        }

        $jobStatus  = self::findOne($data['id']);
        $statusData = BaseAnnouncement::getAuditStatus($data['id']);
        if ($statusData['isArticle'] == self::IS_ARTICLE_YES) {
            // 操作职位编辑前置条件
            if ($statusData['announcementAuditStatus'] == BaseAnnouncement::STATUS_AUDIT_AWAIT) {
                throw new Exception('公告待审核状态不支持编辑');
            }
        }
        if ($jobStatus['audit_status'] == BaseJob::STATUS_WAIT_AUDIT) {
            throw new Exception('待审核状态不支编辑');
        }

        //查询职位详情
        $jobWhere   = ['id' => $data['id']];
        $jobSelect  = [
            'id',
            'status',
            'member_id',
            'company_id',
            'is_article',
            'name',
            'period_date',
            'is_stick',
            'code',
            'job_category_id',
            'education_type',
            'major_id',
            'nature_type',
            'is_negotiable',
            'wage_type',
            'min_wage',
            'max_wage',
            'experience_type',
            'age_type',
            'min_age',
            'max_age',
            'title_type',
            'political_type',
            'abroad_type',
            'amount',
            'department',
            'province_id',
            'city_id',
            'district_id',
            'address',
            'welfare_tag',
            'duty',
            'requirement',
            'remark',
            'audit_status',
            'is_show',
            'apply_type',
            'apply_address',
            'announcement_id',
            'delivery_limit_type',
            'delivery_type',
            'file_ids',
            'delivery_way',
            'extra_notify_address',
        ];
        $jobDetails = self::selectInfo($jobWhere, $jobSelect);
        $areaCache  = BaseArea::setAreaCache();
        //投递限制
        $jobDetails['deliveryLimitTypeTxt'] = '';
        if ($jobDetails['delivery_limit_type']) {
            $deliveryLimitTypeArr = explode(',', $jobDetails['delivery_limit_type']);
            foreach ($deliveryLimitTypeArr as $val) {
                $jobDetails['deliveryLimitTypeTxt'] .= self::DELIVERY_LIMIT_LIST[$val] . ';';
            }

            $jobDetails['deliveryLimitTypeTxt'] = substr($jobDetails['deliveryLimitTypeTxt'], 0, -1);
        }

        //职位附件
        if ($jobDetails['file_ids']) {
            $jobDetails['fileList'] = BaseAnnouncement::getAppendixList($jobDetails['file_ids']);
        } else {
            if (!empty($jobDetails['announcement_id'])) {
                $fileIds                = BaseAnnouncement::findOneVal(['id' => $jobDetails['announcement_id']],
                    'file_ids');
                $jobDetails['fileList'] = BaseAnnouncement::getAppendixList($fileIds);
            } else {
                $jobDetails['fileList'] = [];
            }
        }
        $jobDetails['areaName']       = $areaCache[$jobDetails['province_id']]['name'] . $areaCache[$jobDetails['city_id']]['name'];
        $jobDetails['applyTypeTitle'] = BaseJob::getApplyTypeName($jobDetails['apply_type']) ?: '-';

        //薪资code回显
        if ($jobDetails['is_negotiable'] <> 1) {
            $jobDetails['wage_id'] = (string)BaseJob::getWageId($jobDetails['min_wage'], $jobDetails['max_wage']);
        }

        //查询福利标签
        $jobDetails['welfareTag'] = [];
        $welfareLabelWhere        = ['id' => explode(',', $jobDetails['welfare_tag'])];
        $welfareLabelSelect       = [
            'id',
            'name',
        ];
        $welfareLabelList         = BaseWelfareLabel::findList($welfareLabelWhere, $welfareLabelSelect);
        foreach ($welfareLabelList as $k => $welfareLabel) {
            $jobDetails['welfareTag'][$k]['k'] = $welfareLabel['id'];
            $jobDetails['welfareTag'][$k]['v'] = $welfareLabel['name'];
        }
        //投递方式处理
        if (in_array($jobDetails['delivery_way'], BaseJob::DELIVERY_WAY_EMAIL_LINK_LIST)) {
            $jobDetails['delivery_way'] = (string)BaseJob::DELIVERY_WAY_EMAIL_LINK;
        } elseif (empty($jobDetails['delivery_way'])) {
            $jobDetails['delivery_way'] = '';
        }
        $jobDetails['experience_type'] = $jobDetails['experience_type'] == 0 ? '' : $jobDetails['experience_type'];
        $jobDetails['title_type']      = $jobDetails['title_type'] == 0 ? '' : $jobDetails['title_type'];
        $jobDetails['political_type']  = $jobDetails['political_type'] == 0 ? '' : $jobDetails['political_type'];
        $jobDetails['abroad_type']     = $jobDetails['abroad_type'] == 0 ? '' : $jobDetails['abroad_type'];
        $jobDetails['delivery_type']   = $jobDetails['delivery_type'] == 0 ? '' : $jobDetails['delivery_type'];

        //职位联系人
        $contact                      = self::getJobContact($data['id']);
        $jobDetails['job_contact']    = $contact;
        $jobDetails['job_contact_id'] = $contact['company_member_info_id'];
        //职位协同账号
        $contact_synergy                       = self::getJobContactSynergy($data['id']);
        $jobDetails['job_contact_synergy']     = $contact_synergy;
        $jobDetails['job_contact_synergy_num'] = count($contact_synergy);
        $jobDetails['job_contact_synergy_ids'] = array_column($contact_synergy, 'company_member_info_id');

        return $jobDetails;
    }

    /**
     * 发布/编辑职位
     * @throws Exception
     */
    public static function create($data, $auditStatus = "")
    {
        //获取公司、用户信息
        $memberId    = Yii::$app->user->id;
        $companyInfo = BaseCompany::getCompanyInfo($memberId);
        $memberInfo  = BaseMember::getMemberInfo($memberId);

        if ($data['job_id']) {
            // TODO 编辑职位
            $jobId = $data['job_id'];
            $model = BaseJob::findOne($jobId);

            // TODO 修改了【岗位职责、任职要求、其他说明】
            $editList       = [
                'duty'        => $data['duty'],
                'requirement' => $data['requirement'],
                'remark'      => $data['remark'],
            ];
            $select         = [
                'duty',
                'requirement',
                'remark',
                'announcement_id',
                'status',
            ];
            $jobInfo        = self::selectInfo(['id' => $data['job_id']], $select);
            $announcementId = $jobInfo['announcement_id'];
            $jobStatus      = $jobInfo['status'];
            unset($jobInfo['announcement_id'], $jobInfo['status']);

            if (!in_array($jobStatus, BaseJob::JOB_HISTORY_STATUS)) {
                $model->duty             = $data['duty'];
                $model->requirement      = $data['requirement'];
                $model->remark           = $data['remark'];
                $model->status           = self::STATUS_WAIT_AUDIT;
                $model->audit_status     = self::AUDIT_STATUS_WAIT_AUDIT;
                $model->apply_audit_time = CUR_DATETIME;
            } else {
                $modifyAfterList  = array_diff_assoc($editList, $jobInfo);
                $modifyBeforeList = [];
                foreach ($modifyAfterList as $k => $list) {
                    $modifyBeforeList[$k] = $jobInfo[$k];
                }

                if (sizeof($modifyAfterList) > 0) {
                    $editContent = json_encode($modifyAfterList);
                    $list        = [
                        'job_id'          => $jobId,
                        'add_time'        => CUR_DATETIME,
                        'status'          => BaseJobEdit::STATUS_ONLINE,
                        'edit_content'    => $editContent,
                        'editor_id'       => $memberId,
                        'editor_type'     => BaseJobEdit::EDITOR_TYPE_COMPANY,
                        'editor'          => $memberInfo['username'],
                        'announcement_id' => $announcementId,
                    ];
                    // 检测之前是否有职位编辑内容
                    $jobEditInfo = BaseJobEdit::selectInfo(['job_id' => $jobId], ['id']);
                    if ($jobEditInfo['id']) {
                        $condition = ['id' => $jobEditInfo['id']];
                        BaseJobEdit::updateAll($list, $condition);
                    } else {
                        BaseJobEdit::createInfo($list);
                    }
                    //这里存职位操作表
                    $changeModifyBeforeList = [];
                    $changeModifyAfterList  = [];
                    foreach ($modifyBeforeList as $k => $item) {
                        switch ($k) {
                            case 'duty':
                                $changeModifyBeforeList['岗位职责'] = $item;
                                break;
                            case 'requirement':
                                $changeModifyBeforeList['任职要求'] = $item;
                                break;
                            case 'remark':
                                $changeModifyBeforeList['其他说明'] = $item;
                                break;
                        }
                    }

                    foreach ($modifyAfterList as $k => $item) {
                        switch ($k) {
                            case 'duty':
                                $changeModifyAfterList['岗位职责'] = $item;
                                break;
                            case 'requirement':
                                $changeModifyAfterList['任职要求'] = $item;
                                break;
                            case 'remark':
                                $changeModifyAfterList['其他说明'] = $item;
                                break;
                        }
                    }

                    $handleBefore = json_encode($changeModifyBeforeList);
                    $handleAfter  = json_encode($changeModifyAfterList);
                    $jobHandleLog = [
                        'add_time'      => CUR_DATETIME,
                        'job_id'        => $jobId,
                        'handle_type'   => (string)BaseJobHandleLog::HANDLE_TYPE_EDIT,
                        'handler_type'  => BaseJobHandleLog::HANDLER_TYPE_USER,
                        'handler_id'    => $memberId,
                        'handler_name'  => $memberInfo['username'],
                        'handle_before' => $handleBefore,
                        'handle_after'  => $handleAfter,
                        'ip'            => IpHelper::getIpInt(),
                    ];
                    BaseJobHandleLog::createInfo($jobHandleLog);

                    //只有修改--岗位职责、任职要求、其他说明，移至待审核
                    $model->audit_status     = self::AUDIT_STATUS_WAIT_AUDIT;
                    $model->apply_audit_time = CUR_DATETIME;
                    if ($jobStatus != BaseJob::STATUS_ONLINE) {
                        $model->status = self::STATUS_WAIT_AUDIT;
                    }
                }
            }

            $model->update_time     = CUR_DATETIME;
            $model->period_date     = $data['period_date'];
            $model->major_id        = $data['major_id'];
            $model->nature_type     = $data['nature_type'];
            $model->is_negotiable   = $data['is_negotiable'];
            $model->wage_type       = $data['wage_type'];
            $model->min_wage        = $data['min_wage'];
            $model->max_wage        = $data['max_wage'];
            $model->experience_type = $data['experience_type'];
            $model->title_type      = $data['title_type'];
            $model->age_type        = $data['age_type'];
            $model->political_type  = $data['political_type'];
            $model->abroad_type     = $data['abroad_type'];
            $model->amount          = $data['amount'];
            $model->department      = $data['department'];
            $model->province_id     = $data['province_id'];
            $model->city_id         = $data['city_id'];
            $model->district_id     = $data['district_id'];
            $model->address         = $data['address'];
            $model->welfare_tag     = $data['welfare_tag'];
            $model->department      = $data['department'];
            $model->announcement_id = $data['announcement_id'];
            $model->name            = $data['name'];
            $model->code            = $data['code'];
            $model->job_category_id = $data['job_category_id'];
            $model->name            = $data['name'];
            $model->code            = $data['code'];
            $model->education_type  = $data['education_type'];
            $model->is_show         = BaseJob::IS_SHOW_YES;

            if (!$model->save()) {
                throw new Exception($model->getFirstErrorsMessage());
            }
        } else {
            unset($data['job_id'], $data['wage_id'], $data['areaName'], $data['area_name']);
            // TODO 发布职位、保存职位 传$auditStatus->保存 ，不传->发布职位
            $data['status']           = strlen($auditStatus) > 0 ? self::STATUS_WAIT : self::STATUS_WAIT_AUDIT;
            $data['audit_status']     = strlen($auditStatus) > 0 ? $auditStatus : self::STATUS_WAIT_AUDIT;
            $data['apply_audit_time'] = strlen($auditStatus) > 0 ? '0000-00-00 00:00:00' : CUR_DATETIME;
            $data['add_time']         = CUR_DATETIME;
            $data['member_id']        = $memberId;
            $data['company_id']       = $companyInfo['id'];
            $data['creator']          = $memberInfo['username'];

            //发布职位--新增申请审核时间、消耗套餐
            if (strlen($auditStatus) < 1) {
                $data['apply_audit_time']  = CUR_DATETIME;
                $companyPackageApplication = new CompanyPackageApplication();
                $handleType                = BaseCompanyPackageChangeLog::HANDLE_TYPE_JOB_RELEASE;
                $remark                    = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[$handleType];
                $companyPackageApplication->jobRelease($companyInfo['id'], 1, $remark);
            }

            self::createInfo($data);
        }
    }

    /**
     * 返回可发布职位数量和已发布职位数量
     * @return int
     */
    public static function getReleaseAmount(): int
    {
        $memberId = Yii::$app->user->id;

        return (int)BaseCompanyPackageConfig::findOne(['member_id' => $memberId])['job_amount'] ?: 0;
    }

    /**
     *根据搜索关键词获取职位名称列表
     * @throws Exception
     */
    public static function searchJobNameList($request): array
    {
        $keyWords = $request['key_words'];

        $memberId    = Yii::$app->user->id;
        $companyInfo = BaseCompany::getCompanyInfo($memberId);
        if (!$companyInfo) {
            throw new Exception('非法访问');
        }

        $query = BaseJob::find()
            ->select([
                'id',
                'name',
            ])
            ->where([
                'company_id' => $companyInfo['id'],
            ])
            ->andWhere([
                '<>',
                'is_show',
                BaseJob::IS_SHOW_NO,
            ]);

        if (is_numeric($keyWords) && strlen($keyWords) == 8) {
            $id = UUIDHelper::decryption($keyWords);
            if ($id) {
                $query->andFilterCompare('id', $keyWords);
            }
        }

        $query->andFilterCompare('name', $keyWords, 'like');

        $query->andFilterCompare('status', BaseJob::STATUS_DELETE, '<>');

        return $query->groupBy('id')
            ->asArray()
            ->all();
    }

    /**
     *根据搜索关键词获取职位名称列表
     * @throws Exception
     */
    public static function searchDeleteJobNameList($request): array
    {
        $keyWords = $request['keyWords'];

        $memberId    = Yii::$app->user->id;
        $companyInfo = BaseCompany::getCompanyInfo($memberId);
        if (!$companyInfo) {
            throw new Exception('非法访问');
        }

        $query = BaseJob::find()
            ->select([
                'id',
                'name',
            ])
            ->where([
                'company_id' => $companyInfo['id'],
            ])
            ->andWhere([
                '<>',
                'is_show',
                BaseJob::IS_SHOW_NO,
            ]);

        if (is_numeric($keyWords) && strlen($keyWords) == 8) {
            $keyWords = UUIDHelper::decryption($keyWords);
            $query->andFilterCompare('id', $keyWords);
        } else {
            $query->andFilterCompare('name', $keyWords, 'like');
        }

        $query->andFilterCompare('status', BaseJob::STATUS_DELETE);

        return $query->groupBy('id')
            ->asArray()
            ->all();
    }

    /**
     * 获取职位列表状态
     * @return array
     */
    public static function getJobStatusList(): array
    {
        return self::JOB_STATUS_NAME;
    }

    /**
     * 获取职位列表状态(在线、下线)
     * @return array
     */
    public static function getSearchStatusList(): array
    {
        return self::JOB_SEARCH_STATUS_NAME;
    }

    /**
     * 获取职位申请数量接口
     * 投递总数、职位总数、近一周投递总数
     * @return array
     */
    public static function getJobAmount(): array
    {
        $memberId = Yii::$app->user->id;

        $companyId = BaseCompany::findOneVal(['member_id' => $memberId], 'id');

        $time = date("Y-m-d H-i-s", strtotime("-7 day"));

        return BaseJob::find()
            ->alias('j')
            ->leftJoin(['a' => BaseJobApply::tableName()], 'a.job_id = j.id')
            ->select([
                'count(a.id) as resume_amount',
                'count(j.id) as job_amount',
                "COUNT(CASE WHEN a.add_time > '" . $time . "' THEN 1 ElSE null END) as job_week_amount",
            ])
            ->where([
                'j.status'     => BaseJob::STATUS_ONLINE,
                'j.company_id' => $companyId,
            ])
            ->asArray()
            ->all();
    }

    /**
     * 根据条件获取职位列表
     * @param $keywords
     * @return array
     * @throws Exception
     */
    public static function getJobList($keywords): array
    {
        //校验用户关联单位信息
        $memberId    = Yii::$app->user->id;
        $companyInfo = BaseCompany::getCompanyInfo($memberId);

        //职位申请列表
        $jobApplySelect = [
            'id',
            'job_id',
            'add_time',
        ];
        $jobApplyWhere  = ['company_id' => $companyInfo['id']];
        $jobApplyList   = BaseJobApply::selectInfos($jobApplyWhere, $jobApplySelect);

        //职位限定时间内刷新次数
        $countWhere        = [
            'company_id' => $companyInfo['id'],
            'member_id'  => $memberId,
        ];
        $todayRefreshQuery = self::find()
            ->select(['id'])
            ->where($countWhere);;
        $todayRefreshQuery->andWhere([
            '>=',
            'refresh_time',
            TimeHelper::dayToBeginTime(date("Y-m-d")),
        ]);
        $todayRefreshQuery->andWhere([
            '<=',
            'refresh_time',
            TimeHelper::dayToEndTime(date("Y-m-d")),
        ]);
        $todayRefreshCount = $todayRefreshQuery->count();

        //职位列表
        $jobList = self::validateSearchKeyWords($keywords, $companyInfo, $keywords['limit']);

        //数据处理
        $surplus            = intval(BaseJob::ACCOUNT_LIMIT_NUM - $todayRefreshCount);
        $areaCache          = BaseArea::setAreaCache();
        $jobStatusList      = self::JOB_STATUS_NAME;
        $jobAuditStatusList = self::JOB_AUDIT_STATUS_NAME;
        $eductionTypeList   = BaseDictionary::getEducationList();
        $experienceList     = BaseDictionary::getExperienceList();
        $jobOfflineTypeList = BaseJob::JOB_OFFLINE_TYPE_NAME;

        foreach ($jobList['list'] as $k => $job) {
            $amount                                   = 0;
            $weekAmount                               = 0;
            $unreadApplyNum                           = 0;
            $waitInterviewNum                         = 0;
            $jobList['list'][$k]['city']              = $areaCache[$job['city_id']]['name'];
            $jobList['list'][$k]['statusText']        = $jobStatusList[$job['status']];
            $jobList['list'][$k]['auditStatusTitle']  = $jobAuditStatusList[$job['audit_status']];
            $jobList['list'][$k]['educationType']     = intval($job['education_type']);
            $jobList['list'][$k]['experienceType']    = intval($job['experience_type']);
            $jobList['list'][$k]['offlineTypeTitle']  = $jobOfflineTypeList[$job['offline_type']] ?: '不限';
            $jobList['list'][$k]['refreshSurplusNum'] = $surplus;
            $jobList['list'][$k]['status']            = intval($job['status']);
            $jobList['list'][$k]['offlineType']       = intval($job['offlineType']);
            $jobList['list'][$k]['id']                = intval($job['id']);
            $jobList['list'][$k]['isArticle']         = intval($job['isArticle']);
            $jobList['list'][$k]['auditStatus']       = intval($job['audit_status']);
            $jobList['list'][$k]['uid']               = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $job['id']);

            $jobList['list'][$k]['educationTypeTitle'] = $eductionTypeList[$job['education_type']];
            $jobList['list'][$k]['educationTypeTitle'] = $jobList['list'][$k]['educationTypeTitle'] ?: '不限';

            $jobList['list'][$k]['experienceTypeTitle'] = $experienceList[$job['experience_type']];
            $jobList['list'][$k]['experienceTypeTitle'] = $jobList['list'][$k]['experienceTypeTitle'] ?: '不限';

            foreach ($jobApplyList as $jobApply) {
                if ($jobApply['job_id'] == $job['id']) {
                    $amount++;
                    if (strtotime($jobApply['add_time']) + 60 * 60 * 24 * 7 > time()) {
                        $weekAmount++;
                    }
                    if ($jobApply['is_check'] == 0) {
                        $unreadApplyNum++;
                    }
                    if ($jobApply['status'] == BaseJobApply::STATUS_SEND_INVITATION) {
                        $waitInterviewNum++;
                    }
                }
            }
            $jobList['list'][$k]['weekResumeAmount'] = $weekAmount;
            $jobList['list'][$k]['resumeAmount']     = $amount;
            $jobList['list'][$k]['unreadApplyNum']   = $unreadApplyNum;
            $jobList['list'][$k]['waitInterviewNum'] = $waitInterviewNum;

            $jobList['list'][$k]['jobReleaseAgainType']  = 0;
            $jobList['list'][$k]['jobReleaseAgainTitle'] = "";
            $periodDay                                   = floor((strtotime($job['periodDate']) - time()) / 60 / 60 / 24);

            $companyPackageConfig = BaseCompanyPackageConfig::getCompanyPackageConfig($companyInfo['id']);
            if ($companyInfo['is_pay'] == 0) {
                $useDay = floor((time() - strtotime($job['releaseTime'])) / 60 / 60 / 24);
                if ($useDay < self::RELEASE_AGAIN_DAY) {
                    $jobList['list'][$k]['jobReleaseAgainType']  = 1;
                    $surDay                                      = intval(self::RELEASE_AGAIN_DAY - $useDay);
                    $jobList['list'][$k]['jobReleaseAgainTitle'] = "距离上次发布时间不足" . self::RELEASE_AGAIN_DAY . "天，请" . $surDay . "天后重试";
                }
                $jobList['list'][$k]['isRefreshLimit'] = 0;
            } else {
                if (strtotime($job['refreshTime']) + 60 * 60 * 24 * self::REFRESH_LIMIT_DAY < time()) {
                    $jobList['list'][$k]['isRefreshLimit'] = 1;
                } else {
                    $jobList['list'][$k]['isRefreshLimit'] = 2;
                }
            }

            if (TimeHelper::countYears($job['releaseTime']) < 1) {
                $jobList['list'][$k]['releaseTime'] = date('m/d', strtotime($job['releaseTime']));
            } else {
                if (strtotime($job['releaseTime']) < 1) {
                    $jobList['list'][$k]['releaseTime'] = '-';
                } else {
                    $jobList['list'][$k]['releaseTime'] = date('Y-m-d', strtotime($job['releaseTime']));
                }
            }

            if (TimeHelper::countYears($job['refreshTime']) < 1) {
                $jobList['list'][$k]['refreshTime'] = date('m/d', strtotime($job['refreshTime']));
            } else {
                if (strtotime($job['refreshTime']) < 1) {
                    $jobList['list'][$k]['refreshTime'] = '-';
                } else {
                    $jobList['list'][$k]['refreshTime'] = date('Y-m-d', strtotime($job['refreshTime']));
                }
            }

            if ($periodDay > 0 && $periodDay < (self::ATTENTION_PERIOD_DAY + 1)) {
                $jobList['list'][$k]['periodAttention'] = $periodDay . "天后下线";
            } else {
                $jobList['list'][$k]['periodAttention'] = "";
            }

            //输出薪资
            $jobList['list'][$k]['wage'] = BaseJob::formatWage($job['min_wage'], $job['max_wage'], $job['wage_type']);

            // TODO 这里获取违规操作
            $jobList['list'][$k]['is_violation']     = 2;
            $jobList['list'][$k]['violation_reason'] = '';
            if ($job['status'] == BaseJob::STATUS_OFFLINE || $job['audit_status'] == BaseJob::AUDIT_STATUS_OFFLINE) {
                $handleWhere  = [
                    'job_id'       => $job['id'],
                    'handler_type' => BaseJobHandleLog::HANDLER_TYPE_PLAT,
                    'handle_type'  => BaseJobHandleLog::HANDLE_TYPE_OFFLINE,
                ];
                $handleSelect = [
                    'handle_before',
                    'handle_after',
                ];
                $jobHandleLog = BaseJobHandleLog::find()
                    ->where($handleWhere)
                    ->select($handleSelect)
                    ->orderBy('id desc')
                    ->asArray()
                    ->one();

                if ($jobHandleLog) {
                    $handleAfter                             = json_decode($jobHandleLog['handle_after'], true);
                    $jobList['list'][$k]['is_violation']     = 1;
                    $jobList['list'][$k]['violation_reason'] = $handleAfter['reason'] ? $handleAfter['reason'] : '';
                }
            }

            //todo  审核拒绝返回拒绝原因
            $jobList['list'][$k]['opinion'] = '';
            if ($job['audit_status'] == BaseJob::AUDIT_STATUS_REFUSE_AUDIT) {
                $handleLog = BaseJobHandleLog::find()
                    ->select(['handle_after'])
                    ->where(['job_id' => $job['id']])
                    ->asArray()
                    ->one();

                if ($handleLog) {
                    $handleAfter                    = json_decode($handleLog['handle_after'], true);
                    $jobList['list'][$k]['opinion'] = $handleAfter['opinion'];
                }
            }
        }

        return $jobList;
    }

    /**
     * 校验搜索字段并返回查询列表
     * @param     $keywords
     * @param     $companyInfo
     * @param     $limit
     * @return array
     */
    public static function validateSearchKeyWords($keywords, $companyInfo, $limit = 20): array
    {
        $offset  = ($keywords['page'] - 1) * $limit;
        $where   = ['and'];
        $orderBy = '';
        $where[] = [
            '!=',
            'status',
            self::STATUS_DELETE,
        ];
        $where[] = [
            '<>',
            'is_show',
            BaseJob::IS_SHOW_NO,
        ];

        if ($keywords['name'] || $keywords['name'] == 0) {
            $where[] = [
                'or',
                [
                    'like',
                    'name',
                    $keywords['name'],
                ],
                [
                    'like',
                    'id',
                    $keywords['name'],
                ],
            ];
        }
        if ($keywords['announcement'] || $keywords['announcement'] == 0) {
            $where [] = [
                'like',
                'announcement_id',
                $keywords['announcement'],
            ];
        }
        if ($keywords['release_start_time'] && $keywords['release_end_time']) {
            $where[] = [
                'between',
                'release_time',
                $keywords['release_start_time'],
                $keywords['release_end_time'],
            ];
        }
        if ($keywords['job_category_id']) {
            $where[] = [
                'in',
                'job_category_id',
                $keywords['job_category_id'],
            ];
        }
        if ($keywords['city']) {
            $cityId  = BaseArea::getAreaIdByName($keywords['city']);
            $where[] = [
                'or',
                [
                    'city_id' => $cityId,
                ],
                [
                    'like',
                    'address',
                    $keywords['city'],
                ],
            ];
        }
        if ($keywords['code']) {
            $where[] = [
                'like',
                'code',
                $keywords['code'],
            ];
        }
        if ($keywords['column']) {
            if ($keywords['column'] == 1) {
                $where[] = ['status' => 1];
            }
            if ($keywords['column'] == 2) {
                $where[] = [
                    'or',
                    ['status' => 3],
                    ['status' => 7],
                    ['status' => -1],
                ];
            }
            if ($keywords['column'] == 3) {
                if ($keywords['offline_type'] && $keywords['offline_type'] < 3) {
                    $where [] = ['offline_type' => $keywords['offline_type']];
                }
                $where[] = ['status' => 0];
            }
        }
        if ($keywords['sort_release_time'] == self::ORDER_BY_DESC) {
            $orderBy = 'release_time desc';
        } else {
            if ($keywords['sort_release_time'] == self::ORDER_BY_ASC) {
                $orderBy = 'release_time asc';
            }
        }
        if ($keywords['sort_refresh_time'] == self::ORDER_BY_DESC) {
            $orderBy = 'refresh_time desc';
        } else {
            if ($keywords['sort_refresh_time'] == self::ORDER_BY_ASC) {
                $orderBy = 'refresh_time asc';
            }
        }
        if ($keywords['sort_period_date'] == self::ORDER_BY_DESC) {
            $orderBy = 'period_date desc';
        } else {
            if ($keywords['sort_period_date'] == self::ORDER_BY_ASC) {
                $orderBy = 'period_date asc';
            }
        }
        if ($keywords['audit_status'] && $keywords['audit_status'] < 10) {
            $where[] = [
                'audit_status' => $keywords['audit_status'],
            ];
        }
        if ($keywords['sort_name'] == self::ORDER_BY_DESC) {
            $orderBy = 'name desc';
        } else {
            if ($keywords['sort_name'] == self::ORDER_BY_ASC) {
                $orderBy = 'name asc';
            }
        }
        if ($keywords['id']) {
            $where[] = [
                'id' => $keywords['id'],
            ];
        }
        if ($keywords['status'] && $keywords['status'] < 10) {
            $where[] = [
                'status' => $keywords['status'],
            ];
        }
        if ($keywords['release_time']) {
            $where[] = [
                'like',
                'release_time',
                $keywords['release_time'],
            ];
        }
        if ($keywords['sort_status'] == self::ORDER_BY_DESC) {
            $orderBy = 'status desc';
        } else {
            if ($keywords['sort_status'] == self::ORDER_BY_ASC) {
                $orderBy = 'status asc';
            }
        }
        if ($keywords['sort_department'] == self::ORDER_BY_DESC) {
            $orderBy = 'department desc';
        } else {
            if ($keywords['sort_department'] == self::ORDER_BY_ASC) {
                $orderBy = 'department asc';
            }
        }
        $where[] = ['company_id' => $companyInfo['id']];
        $select  = [
            'id',
            'name',
            'update_time',
            'refresh_time as refreshTime',
            'status',
            'department',
            'city_id',
            'period_date as periodDate',
            'code',
            'education_type',
            'min_wage',
            'max_wage',
            'is_article as isArticle',
            'audit_status',
            'release_time as releaseTime',
            'add_time',
            'wage_type',
            'remark',
            'experience_type',
            'offline_type as offlineType',
            'is_negotiable',
            'job_category_id',
        ];

        return [
            'list' => self::selectInfos($where, $select, [], $limit, $offset, $orderBy),
            'page' => [
                'count' => self::countInfos($where),
                'limit' => $limit,
                'page'  => $keywords['page'],
            ],
        ];
    }

    /**
     * 职位批量刷新检查
     * @throws Exception
     */
    public static function jobBatchRefreshCheck($data)
    {
        //前提条件判定
        $memberId  = Yii::$app->user->id;
        $companyId = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'company_id');
        if (!$companyId || !$data['id']) {
            throw new Exception('非法访问');
        }
        $idArr         = explode(',', $data['id']);
        $jobWhere      = [
            'company_id' => $companyId,
            'id'         => $idArr,
        ];
        $jobSelect     = [
            'id',
            'name',
            'refresh_time',
            'real_refresh_time',
        ];
        $job           = BaseJob::find()
            ->select($jobSelect)
            ->where($jobWhere)
            ->asArray()
            ->all();
        $companyConfig = BaseCompanyPackageConfig::findOne(['company_id' => $companyId]);
        $allowNumber   = 0;
        foreach ($job as $list) {
            if ($list['real_refresh_time'] != TimeHelper::ZERO_TIME) {
                // 没有刷新过
                $subDay = TimeHelper::computeDaySub($list['real_refresh_time'], CUR_DATETIME);
                if ($subDay >= $companyConfig->job_refresh_interval_day) {
                    //刷新时间间隔符合说明能刷新
                    $allowNumber++;
                }
            } else {
                $allowNumber++;
            }
        }
        //        if ($allowNumber == 0) {
        //            throw new Exception('当前暂无职位可刷新，请重新选择！');
        //        }

        return [
            'title'       => '职位刷新',
            'title_tips'  => '每个职位7天内限刷新一次',
            'content'     => '共有<span class="color-primary">' . $allowNumber . '</span>个职位可刷新，将消耗<span class="color-primary">' . $allowNumber . '</span>次职位刷新资源，当前可用<span class="color-primary">' . $companyConfig->job_refresh_amount . '</span>次',
            'description' => '刷新能让职位排名跃升，曝光量倍增，有效提高招聘效率',
        ];
    }

    /**
     * 单个职位刷新与批量刷新
     * @throws Exception
     */
    public static function jobRefresh($data)
    {
        try {
            //前提条件判定
            $memberId    = Yii::$app->user->id;
            $companyId   = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'company_id');
            $companyInfo = BaseCompany::findOne(['id' => $companyId]);
            if (!$companyInfo || !$data['id']) {
                throw new Exception('非法访问');
            }
            $idArr = explode(',', $data['id']);

            $jobWhere      = [
                //'member_id'  => $memberId,
                'company_id' => $companyInfo['id'],
                'id'         => $idArr,
            ];
            $jobSelect     = [
                'id',
                'name',
                'refresh_time',
                'real_refresh_time',
            ];
            $job           = self::selectInfos($jobWhere, $jobSelect);
            $companyConfig = BaseCompanyPackageConfig::findOne(['company_id' => $companyInfo['id']]);
            $allowNumber   = 0;
            foreach ($job as $list) {
                if ($list['real_refresh_time'] != TimeHelper::ZERO_TIME) {
                    // 没有刷新过
                    $subDay = TimeHelper::computeDaySub($list['real_refresh_time'], CUR_DATETIME);
                    if ($subDay >= $companyConfig->job_refresh_interval_day) {
                        $allowNumber++;
                    }
                } else {
                    $allowNumber++;
                }
            }
            if ($allowNumber == 0) {
                //当前暂无职位可刷新！
                throw new Exception('当前暂无职位可刷新，请重新选择！');
            }
            if ($allowNumber > $companyConfig->job_refresh_amount) {
                throw new Exception('您当前可用职位刷新资源不足！');
            }

            //职位刷新消耗套餐
            $companyPackageApplication = new CompanyPackageApplication();
            $handleType                = BaseCompanyPackageChangeLog::HANDLE_TYPE_JOB_REFRESH;
            $remark                    = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[$handleType];
            $companyPackageApplication->jobRefresh($companyInfo['id'], $allowNumber, $remark);

            if (!self::updateAll([
                'refresh_time'      => CUR_DATETIME,
                'refresh_date'      => CUR_DATE,
                'real_refresh_time' => CUR_DATETIME,
                'is_first_release'  => BaseJob::IS_FIRST_RELEASE_NO,
            ], [
                'in',
                'id',
                $idArr,
            ])) {
                throw new Exception('失败');
            }
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 职位删除
     * @throws Exception
     */
    public static function JobDelete($data)
    {
        try {
            $memberId    = Yii::$app->user->id;
            $companyInfo = BaseCompany::getCompanyInfo($memberId);
            if (!$companyInfo) {
                throw new Exception('非法访问');
            }
            if (!$data['id']) {
                throw new Exception('职位Id不能为空');
            }
            $idArr   = explode(',', $data['id']);
            $where   = ['and'];
            $where[] = [
                'in',
                'id',
                $idArr,
            ];
            $where[] = ['company_id' => $companyInfo['id']];
            $where[] = ['member_id' => $memberId];
            if (!self::updateAll([
                'status'      => self::STATUS_DELETE,
                'delete_time' => CUR_DATETIME,
            ], $where)) {
                throw new Exception('失败');
            }
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 职位审核撤回
     * @throws Exception
     */
    public static function JobWithdraw($data)
    {
        try {
            $memberId    = Yii::$app->user->id;
            $companyInfo = BaseCompany::getCompanyInfo($memberId);
            if (!$companyInfo) {
                throw new Exception('非法访问');
            }
            if (!$data['id']) {
                throw new Exception('职位Id不能为空');
            }
            $idArr = explode(',', $data['id']);

            foreach ($idArr as $id) {
                $where   = ['and'];
                $where[] = ['id' => $id];
                $jobInfo = BaseJob::find()
                    ->where($where)
                    ->select(['status'])
                    ->asArray()
                    ->one();

                if ($jobInfo['status'] == BaseJob::STATUS_ONLINE) {
                    $changeData = [
                        'audit_status' => self::AUDIT_STATUS_PASS_AUDIT,
                    ];
                } else {
                    $changeData = [
                        'status'       => self::STATUS_WAIT,
                        'audit_status' => self::AUDIT_STATUS_WAIT,
                    ];
                }

                if (!self::updateAll($changeData, $where)) {
                    throw new Exception('失败');
                }
            }
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 职位撤回删除
     * @throws Exception
     */
    public static function JobUndoDelete($data)
    {
        try {
            if (!$data['id']) {
                throw new Exception('传参错误');
            }

            $memberId = Yii::$app->user->id;

            // check 公告状态
            $list = BaseJob::find()
                ->alias('j')
                ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id=j.announcement_id')
                ->leftJoin(['e' => BaseArticle::tableName()], 'e.id=a.article_id')
                ->select([
                    'e.is_delete',
                ])
                ->where([
                    'j.member_id' => $memberId,
                    'j.id'        => $data['id'],
                ])
                ->asArray()
                ->one();

            if ($list['is_delete'] == BaseArticle::IS_DELETE_YES) {
                throw new Exception('当前职位所关联的公告已被删除，请先撤回删除公告信息');
            } else {
                $model = BaseJob::findOne(['id' => $data['id']]);
                if (!$model) {
                    throw new Exception('职位不存在');
                }
                if ($model->status != BaseJob::STATUS_DELETE) {
                    throw new Exception('职位状态错误');
                }
                if ($model->audit_status == BaseJob::AUDIT_STATUS_PASS_DELETE) {
                    $model->audit_status = BaseJob::AUDIT_STATUS_OFFLINE;
                }
                switch ($model->audit_status) {
                    case BaseJob::AUDIT_STATUS_PASS_DELETE:
                        $model->status       = BaseJob::STATUS_OFFLINE;
                        $model->audit_status = BaseJob::AUDIT_STATUS_OFFLINE;
                        break;
                    case BaseJob::AUDIT_STATUS_PASS_DELETE:
                        $model->status       = BaseJob::STATUS_WAIT;
                        $model->audit_status = BaseJob::AUDIT_STATUS_WAIT;
                        break;
                    case BaseJob::AUDIT_STATUS_WAIT_AUDIT:
                    case BaseJob::AUDIT_STATUS_REFUSE_AUDIT:
                        $model->status       = BaseJob::STATUS_REFUSE_AUDIT;
                        $model->audit_status = BaseJob::AUDIT_STATUS_REFUSE_AUDIT;
                        break;
                    default:
                        $model->status       = BaseJob::STATUS_OFFLINE;
                        $model->audit_status = BaseJob::AUDIT_STATUS_OFFLINE;
                }

                if (!$model->save()) {
                    throw new Exception('撤回删除失败');
                }
            }
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 职位下线
     * @throws Exception
     */
    public static function JobOffline($data)
    {
        try {
            $memberId    = Yii::$app->user->id;
            $companyInfo = BaseCompany::getCompanyInfo($memberId);
            if (!$companyInfo) {
                throw new Exception('非法访问');
            }
            if (!$data['id']) {
                throw new Exception('职位Id不能为空');
            }
            $idArr         = explode(',', $data['id']);
            $jobApplyWhere = ['and'];
            //            $jobApplyWhere [] = [
            //                'member_id' => $memberId,
            //            ];
            $jobApplyWhere [] = [
                'company_id' => $companyInfo['id'],
            ];
            $jobApplyWhere [] = [
                'in',
                'id',
                $idArr,
            ];
            $jobSelect        = [
                'id',
                'refresh_time',
            ];
            $job              = self::selectInfos($jobApplyWhere, $jobSelect);
            if (!$job || (count($job) != count($idArr))) {
                throw new Exception('非法请求');
            }
            $where   = ['and'];
            $where[] = [
                'in',
                'id',
                $idArr,
            ];
            $where[] = ['company_id' => $companyInfo['id']];
            //$where[] = ['member_id' => $memberId];
            if (!self::updateAll([
                'status'       => self::STATUS_OFFLINE,
                //'audit_status' => self::AUDIT_STATUS_OFFLINE,下线职位不要把审核状态改为下线
                'offline_type' => self::OFFLINE_TYPE_HAND,
                'offline_time' => date("Y-m-d h-i-s"),
            ], $where)) {
                throw new Exception('失败');
            }
            //下线成功推送一下消息
            foreach ($idArr as $jobId) {
                (new PushEditMessageService())->offline(['jobId' => $jobId]);
            }
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 职位再发布
     * @throws Exception
     */
    public static function jobReleaseAgain($data)
    {
        try {
            $memberId    = Yii::$app->user->id;
            $companyInfo = BaseCompany::getCompanyInfo($memberId);
            if (!$companyInfo) {
                throw new Exception('非法访问');
            }
            if (!$data['id']) {
                throw new Exception('职位Id不能为空');
            }
            $idArr      = explode(',', $data['id']);
            $jobWhere   = ['and'];
            $jobWhere[] = [
                'member_id'  => $memberId,
                'company_id' => $companyInfo['id'],
                'id'         => $idArr,
            ];
            $jobSelect  = [
                'id',
                'name',
                'release_time',
            ];
            $job        = self::selectInfos($jobWhere, $jobSelect);
            if (!$job || (count($job) != count($idArr))) {
                throw new Exception('非法请求');
            }
            //此处关联公告，若公告为在线状态，再发布成功后，职位即变成”在线“状态；若公告下线，提示公告要先上线，
            //才能再发布职位；若公告处于待发布状态，提示公告需要审核通过后，才能再发布职位；
            if ($companyInfo['is_pay'] == 0) {
                foreach ($job as $k => $list) {
                    if (floor((time() - strtotime($list['release_time'])) / 60 / 60 / 24) < self::RELEASE_AGAIN_DAY) {
                        throw new Exception('职位：' . $list['name'] . '暂时不能再发布，距离上次发布未超过' . self::RELEASE_AGAIN_DAY . '天');
                    }
                }
            }
            $periodDate = Date("Y-m-d H-i-s", (time() + 60 * 60 * 24 * self::ADD_RELEASE_AGAIN_DAY));
            if (!self::updateAll([
                'release_time' => date("Y-m-d h-i-s"),
                'status'       => self::STATUS_ONLINE,
                'period_date'  => $periodDate,
            ], [
                'in',
                'id',
                $idArr,
            ])) {
                throw new Exception('失败');
            }
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 获取职位申请数量接口
     * @return array
     * @throws Exception
     */
    public static function getColumnAmount(): array
    {
        $memberId    = Yii::$app->user->id;
        $companyInfo = BaseCompany::getCompanyInfo($memberId);
        if (!$companyInfo) {
            throw new Exception('非法访问');
        }
        //column=1 在线
        $where_1   = ['and'];
        $where_1[] = [
            '!=',
            'status',
            self::STATUS_DELETE,
        ];
        $where_1[] = ['company_id' => $companyInfo['id']];
        $where_1[] = ['member_id' => $memberId];
        $where_1[] = ['status' => 1];
        //        $where_1[] = [
        //            '<>',
        //            'is_article',
        //            BaseJob::IS_ARTICLE_WAIT,
        //        ];

        //column=2 待发布
        $where_2   = ['and'];
        $where_2[] = [
            '!=',
            'status',
            self::STATUS_DELETE,
        ];
        $where_2[] = ['company_id' => $companyInfo['id']];
        $where_2[] = ['member_id' => $memberId];
        $where_2[] = [
            'or',
            ['status' => 3],
            ['status' => 7],
            ['status' => -1],
        ];
        //        $where_2[] = [
        //            '<>',
        //            'is_article',
        //            BaseJob::IS_ARTICLE_WAIT,
        //        ];

        //column=3 已下线
        $where_3   = ['and'];
        $where_3[] = [
            '!=',
            'status',
            self::STATUS_DELETE,
        ];
        $where_3[] = ['company_id' => $companyInfo['id']];
        $where_3[] = ['member_id' => $memberId];
        $where_3[] = ['status' => 0];
        //        $where_3[] = [
        //            '<>',
        //            'is_article',
        //            BaseJob::IS_ARTICLE_WAIT,
        //        ];

        //column=4 全部
        $where_4   = ['and'];
        $where_4[] = [
            '!=',
            'status',
            self::STATUS_DELETE,
        ];
        $where_4[] = ['company_id' => $companyInfo['id']];
        $where_4[] = ['member_id' => $memberId];
        //        $where_4[] = [
        //            '<>',
        //            'is_article',
        //            BaseJob::IS_ARTICLE_WAIT,
        //        ];

        $select = [
            'id',
        ];

        $andWhere = [
            'or',
            [
                'create_type' => BaseJob::CREATE_TYPE_SELF,
                'is_show'     => BaseJob::IS_SHOW_YES,
            ],
            [
                'and',
                'create_type' => BaseJob::CREATE_TYPE_AGENT,
                'is_show'     => BaseJob::IS_SHOW_YES,
                [
                    '<>',
                    'first_release_time',
                    TimeHelper::ZERO_TIME,
                ],
            ],
        ];

        $list             = [];
        $list['column_1'] = count(self::selectInfos($where_1, $select, $andWhere));
        $list['column_2'] = count(self::selectInfos($where_2, $select, $andWhere));
        $list['column_3'] = count(self::selectInfos($where_3, $select, $andWhere));
        $list['column_4'] = count(self::selectInfos($where_4, $select, $andWhere));

        return $list;
    }

    /**
     * 按职位查看-根据条件获取职位列表
     * @throws Exception
     */
    public static function getSearchList($keywords): array
    {
        $memberId    = Yii::$app->user->id;
        $companyInfo = BaseCompany::getCompanyInfo($memberId);
        if (!$companyInfo) {
            throw new Exception('非法访问');
        }

        $jobApplyWhere  = [
            'and',
            'company_id' => $companyInfo['id'],
        ];
        $jobApplySelect = [
            'id',
            'job_id',
            'add_time',
        ];
        //职位申请列表
        $jobApplyList = BaseJobApply::selectInfos($jobApplyWhere, $jobApplySelect);

        $where   = ['and'];
        $where[] = [
            'in',
            'status',
            [
                BaseJob::STATUS_OFFLINE,
                BaseJob::STATUS_ONLINE,
            ],
        ];
        $where[] = [
            '<>',
            'is_show',
            BaseJob::IS_SHOW_NO,
        ];
        $orderBy = '';
        if ($keywords['name'] || $keywords['name'] == 0) {
            $where[] = [
                'or',
                [
                    'like',
                    'name',
                    $keywords['name'],
                ],
                [
                    'like',
                    'id',
                    $keywords['name'],
                ],
            ];
        }
        if (strlen($keywords['status']) > 0) {
            $where[] = ['status' => $keywords['status']];
        }
        if ($keywords['release_time_start'] && $keywords['release_time_end']) {
            $where[] = [
                'between',
                'release_time',
                $keywords['release_start_time'],
                $keywords['release_time_end'],
            ];
        }
        if ($keywords['job_category_id']) {
            $where[] = [
                'in',
                'job_category_id',
                $keywords['job_category_id'],
            ];
        }
        if ($keywords['sort_name'] == self::ORDER_BY_DESC) {
            $orderBy = 'name desc';
        } else {
            if ($keywords['sort_name'] == self::ORDER_BY_ASC) {
                $orderBy = 'name asc';
            }
        }
        if ($keywords['sort_release_time'] == self::ORDER_BY_DESC) {
            $orderBy = 'name desc';
        } else {
            if ($keywords['sort_release_time'] == self::ORDER_BY_ASC) {
                $orderBy = 'name asc';
            }
        }
        if ($keywords['sort_refresh_time'] == self::ORDER_BY_DESC) {
            $orderBy = 'name desc';
        } else {
            if ($keywords['sort_name'] == self::ORDER_BY_ASC) {
                $orderBy = 'name asc';
            }
        }
        if ($keywords['sort_status'] == self::ORDER_BY_DESC) {
            $orderBy = 'name desc';
        } else {
            if ($keywords['sort_name'] == self::ORDER_BY_ASC) {
                $orderBy = 'name asc';
            }
        }
        if ($keywords['sort_department'] == self::ORDER_BY_DESC) {
            $orderBy = 'department desc';
        } else {
            if ($keywords['sort_department'] == self::ORDER_BY_ASC) {
                $orderBy = 'department asc';
            }
        }
        if ($keywords['sort_city_id'] == self::ORDER_BY_DESC) {
            $orderBy = 'city_id desc';
        } else {
            if ($keywords['sort_city_id'] == self::ORDER_BY_ASC) {
                $orderBy = 'city_id asc';
            }
        }

        $where[] = [
            'company_id' => $companyInfo['id'],
        ];

        $select        = [
            'id',
            'name',
            'job_category_id as jobCategoryId',
            'education_type as educationType',
            'department',
            'city_id as cityId',
            'refresh_time as refreshTime',
            'release_time as releaseTime',
            'status',
        ];
        $limit         = $keywords['limit'];
        $offset        = ($keywords['page'] - 1) * $keywords['limit'];
        $list          = BaseJob::find()
            ->where($where)
            ->select($select)
            ->limit($limit)
            ->offset($offset)
            ->orderBy($orderBy)
            ->asArray()
            ->all();
        $jobStatusList = self::JOB_STATUS_NAME;
        foreach ($list as $k => $job) {
            $amount                       = 0;
            $weekAmount                   = 0;
            $list[$k]['weekResumeAmount'] = $weekAmount;
            $list[$k]['resumeAmount']     = $amount;
            $list[$k]['status']           = intval($job['status']);
            foreach ($jobApplyList as $jobApply) {
                if ($jobApply['job_id'] == $job['id']) {
                    $amount++;
                    if (strtotime($jobApply['add_time']) + 60 * 60 * 24 * 7 > time()) {
                        $weekAmount++;
                    }
                }
                $list[$k]['weekResumeAmount'] = $weekAmount;
                $list[$k]['resumeAmount']     = $amount;
            }
            $refreshTime = strtotime($job['refreshTime']);
            if ($refreshTime > 0) {
                $list[$k]['refreshTime'] = date('Y-m-d', $refreshTime);
            } else {
                $list[$k]['refreshTime'] = "";
            }
            $releaseTime = strtotime($job['releaseTime']);
            if ($releaseTime > 0) {
                $list[$k]['releaseTime'] = date('Y-m-d', $releaseTime);
            } else {
                $list[$k]['releaseTime'] = "";
            }
            $list[$k]['city']        = Area::getAreaName($list[$k]['cityId']);
            $list[$k]['statusTitle'] = $jobStatusList[$job['status']];
        }

        $count        = self::countInfos($where);
        $resumeAmount = sizeof($jobApplyList);

        return [
            'list'   => $list,
            'amount' => [
                'job'    => intval($count),
                'resume' => $resumeAmount,
            ],
            'page'   => [
                'count' => intval($count),
                'limit' => intval($limit),
                'page'  => intval($keywords['page']),
            ],
        ];
    }

    /**
     * 职位批量导入数据
     * @throws Exception
     */
    public static function jobImport($filePath, $handle): array
    {
        $memberId    = Yii::$app->user->id;
        $companyInfo = BaseCompany::getCompanyInfo($memberId);
        $memberInfo  = BaseMember::getMemberInfo($memberId);
        $fieldArr    = [
            '职位名称'     => 'name',
            '职位有效期'   => 'period_date',
            '职位代码'     => 'code',
            '职位类别'     => 'category_job',
            '学历要求'     => 'education_type',
            '需要专业'     => 'major',
            '工作性质'     => 'nature_type',
            '薪资类型'     => 'wage_type',
            '最低薪资'     => 'min_wage',
            '最高薪资'     => 'max_wage',
            '招聘人数'     => 'amount',
            '用人部门'     => 'department',
            '省级'         => 'province',
            '市级'         => 'city',
            '区级'         => 'district',
            '地址'         => 'address',
            '岗位职责'     => 'duty',
            '任职要求'     => 'requirement',
            '其他说明'     => 'remark',
            '职称类型'     => 'title_type',
            '政治面貌类型' => 'political_type',
            '海外经历类型' => 'abroad_type',
            '公告编号'     => 'announcement_id',
        ];

        if (!$filePath) {
            throw new Exception('文件记录ID为空');
        }

        // 读取文件数据
        $excel  = new Excel();
        $insert = $excel->import($filePath, $fieldArr);
        if (empty($insert)) {
            throw new Exception('Excel数据为空');
        }

        $count = 0;
        $Job   = new Job();
        if ($handle == 1) {
            $status      = BaseJob::STATUS_WAIT;
            $auditStatus = BaseJob::AUDIT_STATUS_WAIT;
        } else {
            $status      = BaseJob::STATUS_WAIT_AUDIT;
            $auditStatus = BaseJob::AUDIT_STATUS_WAIT_AUDIT;
        }

        foreach ($insert as $key => &$item) {
            $key = $key + 2;
            if (empty($item['name'])) {
                throw new Exception('第' . $key . '行，职位名称不能为空');
            }
            if (empty($item['period_date'])) {
                throw new Exception('第' . $key . '行，职位有效期不能为空');
            }
            if (!empty($item['category_job'])) {
                throw new Exception('第' . $key . '行，职位类别不能为空');
            }
            if (!empty($item['education_type'])) {
                throw new Exception('第' . $key . '行，学历要求不能为空');
            }
            if (!empty($item['major'])) {
                throw new Exception('第' . $key . '行，需要专业不能为空');
            }
            if (!empty($item['nature_type'])) {
                throw new Exception('第' . $key . '行，工作性质不能为空');
            }
            if (!empty($item['wage_type'])) {
                throw new Exception('第' . $key . '行，薪资类型不能为空');
            }
            if (!empty($item['province'])) {
                throw new Exception('第' . $key . '行，省级不能为空');
            }
            if (!empty($item['city'])) {
                throw new Exception('第' . $key . '行，市级不能为空');
            }
            if (!empty($item['district'])) {
                throw new Exception('第' . $key . '行，区级不能为空');
            }
            if (!empty($item['address'])) {
                throw new Exception('第' . $key . '行，地址不能为空');
            }
            if (!empty($item['duty'])) {
                throw new Exception('第' . $key . '行，岗位职责不能为空');
            }
            if (!empty($item['requirement'])) {
                throw new Exception('第' . $key . '行，任职要求不能为空');
            }

            // 省市区
            $province = mb_substr($item['province'], 0, 2);
            $city     = mb_substr($item['city'], 0, 2);
            $district = mb_substr($item['district'], 0, 2);

            $province_id = Area::findOneVal([
                'like',
                'name',
                $province,
                ['level' => 1],
            ], 'id');
            $city_id     = Area::findOneVal([
                'like',
                'name',
                $city,
                ['level' => 2],
            ], 'id');
            $district_id = Area::findOneVal([
                'like',
                'name',
                $district,
                ['level' => 3],
            ], 'id');

            $majorId       = BaseMajor::findOneVal(['name' => $item['major']], 'id') ?: '';
            $jobCategoryId = BaseCategoryJob::findOneVal(['name' => $item['category_job']], 'id') ?: '';

            $list = [
                'add_time'        => CUR_DATETIME,
                'update_time'     => CUR_DATETIME,
                'status'          => $status,
                'member_id'       => $memberId,
                'company_id'      => $companyInfo['id'],
                'is_article'      => 2,
                'name'            => $item['name'],
                'period_date'     => $item['period_date'],
                'is_stick'        => 2,
                'code'            => $item['code'],
                'job_category_id' => $jobCategoryId,
                'education_type'  => $item['education_type'],
                'major_id'        => $majorId,
                'nature_type'     => $item['nature_type'],
                'is_negotiable'   => BaseJob::IS_NEGOTIABLE_YES,
                'wage_type'       => $item['wage_type'],
                'min_wage'        => $item['min_wage'],
                'max_wage'        => $item['max_wage'],
                'experience_type' => $item['experience_type'],
                'age_type'        => $item['age_type'],
                'title_type'      => $item['title_type'],
                'amount'          => $item['amount'],
                'department'      => $item['department'],
                'district_id'     => $district_id,
                'province_id'     => $province_id,
                'city_id'         => $city_id,
                'address'         => $item['address'],
                'duty'            => $item['duty'],
                'requirement'     => $item['requirement'],
                'remark'          => $item['remark'],
                'audit_status'    => $auditStatus,
                'is_show'         => BaseJob::IS_SHOW_YES,
                'create_type'     => BaseJob::CREATE_TYPE_SELF,
                'create_id'       => $memberId,
                'creator'         => $memberInfo['username'],
            ];

            $Job->load($list);
            if (!$Job->save()) {
                throw new Exception($Job->getFirstErrorsMessage());
            }
            $count++;
        }

        unlink($filePath);

        return ['msgTxt' => '成功批量导入' . $count . '条数据'];
    }

    /**
     * 回收箱职位列表
     * @throws Exception
     */
    public static function getRecoveryJobList($keywords): array
    {
        $memberId = Yii::$app->user->id;
        $select   = [
            'j.id',
            'j.status',
            'j.name',
            'j.period_date',
            'j.code',
            'j.job_category_id',
            'j.education_type',
            'j.is_negotiable',
            'j.wage_type',
            'j.min_wage',
            'j.max_wage',
            'j.experience_type',
            'j.department',
            'j.city_id',
            'j.announcement_id',
            'j.delete_time',
            'a.status as announcement_status',
            'a.title as announcement_title',
            'j.release_time',
            'COUNT(p.job_id) as allApplyAccount',
            'COUNT(CASE WHEN p.is_invitation!=1 THEN 1 ElSE null END) as waitInterviewAccount',
            'COUNT(CASE WHEN p.is_check!=1 THEN 1 ElSE null END) as unreadAccount',
        ];

        $where = [
            '<>',
            'j.is_show',
            BaseJob::IS_SHOW_NO,
        ];
        $query = BaseJob::find()
            ->alias('j')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id=j.announcement_id')
            ->leftJoin(['p' => JobApply::tableName()], 'j.id = p.job_id')
            ->groupBy(['j.id'])
            ->select($select)
            ->where($where);

        $query->andFilterCompare('j.status', BaseJob::STATUS_DELETE);
        $query->andFilterCompare('j.member_id', $memberId);
        $query->andFilterCompare('concat(a.title,a.id )', $keywords['announcement'], 'like');
        $query->andFilterCompare('j.job_category_id', $keywords['job_category_id']);
        $query->andFilterCompare('j.code', $keywords['code']);

        //检测职位编号并搜索
        if ($keywords['name']) {
            if (is_numeric($keywords['name']) && strlen($keywords['name']) == 8) {
                $nameChangeUid = UUIDHelper::decryption($keywords['name']);
                $query->andFilterCompare('j.id', (int)$nameChangeUid);
            } else {
                $query->andFilterCompare('concat(j.name,j.id )', $keywords['name'], 'like');
            }
        }

        if ($keywords['delete_time_start']) {
            $query->andWhere([
                '>=',
                'j.delete_time',
                TimeHelper::dayToBeginTime($keywords['delete_time_start']),
            ]);
        }
        if ($keywords['delete_time_end']) {
            $query->andWhere([
                '<=',
                'j.delete_time',
                TimeHelper::dayToEndTime($keywords['delete_time_end']),
            ]);
        }
        if ($keywords['city']) {
            $cityId = BaseArea::getAreaIdByName($keywords['city']);
            $query->andFilterCompare('j.city_id', $cityId);
        }

        $orderBy  = ' j.period_date desc';
        $count    = $query->count();
        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $jobList  = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        //todo 处理返回值
        $educationTypeList = BaseDictionary::getEducationList();
        $experienceList    = BaseDictionary::getExperienceList();
        foreach ($jobList as $k => $list) {
            // 返回uid
            $jobList[$k]['uid'] = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $list['id']);

            // 关联公告标题
            if ($list['announcement_id'] < 1) {
                $jobList[$k]['announcement_title'] = '';
            }

            // 信息展示，用人部门｜经验｜学历要求｜城市｜薪资
            $experienceTypeTitle = $experienceList[intval($list['experienceType'])];
            $educationTypeTitle  = $educationTypeList[$list['educationType']];
            if ($list['educationType'] == 0) {
                $educationTypeTitle = '学历不限';
            }
            if ($list['experienceType'] == 0) {
                $experienceTypeTitle = '经验不限';
            }

            $wage = BaseJob::formatWage($list['min_wage'], $list['max_wage'], $list['wage_type']);
            $city = Area::getAreaName($list['city_id']);

            $jobList[$k]['message'] = $list['department'] . '|' . $experienceTypeTitle . '|' . $educationTypeTitle . '|' . $city . '|' . $wage;

            //发布时间格式
            if (TimeHelper::countYears($list['release_time']) < 1) {
                $jobList[$k]['release_time'] = date('m/d', strtotime($list['release_time']));
            } else {
                if (strtotime($list['releaseTime']) < 1) {
                    $jobList[$k]['release_time'] = '-';
                } else {
                    $jobList[$k]['release_time'] = date('Y-m-d', strtotime($list['release_time']));
                }
            }

            //删除时间
            if (TimeHelper::countYears($list['delete_time']) < 1) {
                $jobList[$k]['delete_time'] = date('m/d', strtotime($list['delete_time']));
            } else {
                if (strtotime($list['delete_time']) < 1) {
                    $jobList[$k]['delete_time'] = '-';
                } else {
                    $jobList[$k]['delete_time'] = date('Y-m-d', strtotime($list['delete_time']));
                }
            }
        }

        return [
            'list' => $jobList,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
        ];
    }

    /**
     * 职位列表(lemon)
     * @param $keywords
     * @return array
     * @throws Exception
     */
    public static function JobList($keywords): array
    {
        $select = [
            'j.id',
            'j.uuid',
            'j.add_time',
            'j.status',
            'j.member_id',
            'j.company_id',
            'j.is_article',
            'j.name',
            'j.period_date',
            'j.is_stick',
            'j.code',
            'j.job_category_id',
            'j.education_type',
            'j.major_id',
            'j.nature_type',
            'j.is_negotiable',
            'j.wage_type',
            'j.min_wage',
            'j.max_wage',
            'j.experience_type',
            'j.age_type',
            'j.title_type',
            'j.political_type',
            'j.abroad_type',
            'j.amount',
            'j.department',
            'j.district_id',
            'j.province_id',
            'j.city_id',
            'j.address',
            'j.welfare_tag',
            // 'j.duty',
            // 'j.requirement',
            // 'j.remark',
            'j.refresh_time',
            'j.audit_status',
            'j.click',
            // 'j.lat',
            // 'j.lng',
            'j.release_time',
            'j.offline_type',
            'j.offline_time',
            'j.announcement_id',
            'j.gender_type',
            'j.create_type',
            // 'j.creator',
            'j.download_amount',
            'j.apply_audit_time',
            'j.delete_time',
            'j.apply_type',
            'j.apply_address',
            'j.offline_reason',
            'j.real_refresh_time',
            'j.first_release_time',
            'j.delivery_way',
            'j.delivery_type',
            'a.title',
            'a.status as announcement_status',
            'a.delivery_way as announcement_delivery_way',
            'a.delivery_type as announcement_delivery_type',
        ];

        $query = BaseJob::find()
            ->alias('j')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id=j.announcement_id')
            ->select($select)
            ->andWhere(self::BASE_JOB_LIST_CONDITION);

        $memberId       = Yii::$app->user->id;
        $authorityModel = new CompanyAuthorityClassify();
        $authorityList  = $authorityModel->run([
            'memberId'         => $memberId,
            'query'            => $query,
            'returnType'       => BaseRule::DATA_JOB_COOPERATE,
            'companyAuthority' => 'jobList',
        ]);

        $companyAuthorityList = [];
        if ($authorityList) {
            $query                = $authorityList['query'];
            $companyAuthorityList = $authorityList['companyAuthorityList'];
        }

        //todo 查询条件
        if ($keywords['column']) {
            switch ($keywords['column']) {
                case 1:
                    $query->andFilterCompare('j.status', BaseJob::STATUS_ONLINE);
                    break;
                case 2:
                    $query->andFilterCompare('j.status', BaseJob::STATUS_WAIT);
                    break;
                case 3:
                    $query->andFilterCompare('j.status', BaseJob::STATUS_OFFLINE);
                    break;
                case 4:
                    break;
                default:
            }
        }

        if ($keywords['announcement_id']) {
            if (is_numeric($keywords['announcement_id']) && strlen($keywords['announcement_id']) == 8) {
                $nameChangeUid = UUIDHelper::decryption($keywords['announcement_id']);
                $query->andFilterCompare('a.id', (int)$nameChangeUid);
            } else {
                $query->andFilterCompare('a.id', $keywords['announcement_id']);
            }
        }

        if ($keywords['name']) {
            if (is_numeric($keywords['name']) && strlen($keywords['name']) == 8) {
                $nameChangeUid = UUIDHelper::decryption($keywords['name']);
                $query->andFilterCompare('j.id', (int)$nameChangeUid);
            } else {
                $query->andFilterCompare('concat(j.name,j.id )', $keywords['name'], 'like');
            }
        }

        if ($keywords['announcement']) {
            if (is_numeric($keywords['announcement']) && strlen($keywords['announcement']) == 8) {
                $query->andFilterCompare('a.uuid', $keywords['announcement']);
            } else {
                $query->andFilterCompare('concat(a.title,a.id )', $keywords['announcement'], 'like');
            }
        }

        if ($keywords['announcement_title']) {
            if (is_numeric($keywords['announcement_title']) && strlen($keywords['announcement_title']) == 8) {
                $query->andFilterCompare('a.uuid', $keywords['announcement_title']);
            } else {
                $query->andFilterCompare('concat(a.title,a.id )', $keywords['announcement_title'], 'like');
            }
        }

        if ($keywords['release_start_time']) {
            $query->andWhere([
                '>=',
                'j.release_time',
                TimeHelper::dayToBeginTime($keywords['release_start_time']),
            ]);
        }
        if ($keywords['release_end_time']) {
            $query->andWhere([
                '<=',
                'j.release_time',
                TimeHelper::dayToEndTime($keywords['release_end_time']),
            ]);
        }

        if ($keywords['city']) {
            $query->andWhere([
                'in',
                'j.city_id',
                StringHelper::changeStrToFilterArr($keywords['city'])
            ]);
        }

        if ($keywords['job_category_id']) {
            $jobCategoryId = explode(',', $keywords['job_category_id']);
            $query->andFilterCompare('j.job_category_id', $jobCategoryId, 'in');
        }

        $query->andFilterCompare('j.code', $keywords['code']);
        $query->andFilterCompare('j.audit_status', $keywords['audit_status']);
        $query->andFilterCompare('j.offline_type', $keywords['offline_type']);
        $query->andFilterCompare('j.delivery_type', $keywords['delivery_type']);
        if ($keywords['delivery_way']) {
            $query->andWhere([
                'or',
                ['j.delivery_way' => $keywords['delivery_way']],
                [
                    'j.delivery_way' => 0,
                    'a.delivery_way' => $keywords['delivery_way'],
                ],
            ]);
        }

        $orderBy = ' j.add_time desc';
        if ($keywords['sort_release_time']) {
            $sort    = $keywords['sort_release_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.release_time ' . $sort;
        }
        if ($keywords['sort_refresh_time']) {
            $sort    = $keywords['sort_refresh_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.real_refresh_time ' . $sort;
        }
        if ($keywords['sort_period_date']) {
            $sort    = $keywords['sort_period_date'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.period_date ' . $sort;
        }
        if ($keywords['sort_name']) {
            $sort    = $keywords['sort_name'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.name ' . $sort;
        }

        //联系人与协同联系人的搜索
        if ($keywords['contact']) {
            $ids = BaseJobContact::find()
                ->alias('jc')
                ->select('jc.job_id')
                ->where(['jc.company_member_info_id' => $keywords['contact']])
                ->asArray()
                ->column() ?: [];
            $query->andWhere([
                'j.id' => $ids,
            ]);
        }
        //处理联系人与协同联系人的搜索
        if ($keywords['cooperate_account']) {
            if ($keywords['cooperate_account'] > 0) {
                $ids = BaseJobContactSynergy::find()
                    ->alias('jcs')
                    ->select('jcs.job_id')
                    ->where(['jcs.company_member_info_id' => $keywords['cooperate_account']])
                    ->asArray()
                    ->column() ?: [];
                $query->andWhere([
                    'j.id' => $ids,
                ]);
            } else {
                $companyId = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'company_id');
                $ids       = BaseJobContactSynergy::find()
                    ->alias('jcs')
                    ->select('jcs.job_id')
                    ->where(['jcs.company_id' => $companyId])
                    ->groupBy('jcs.job_id')
                    ->asArray()
                    ->column() ?: [];
                $query->andWhere([
                    'NOT IN',
                    'j.id',
                    $ids,
                ]);
            }
        }

        $count    = $query->count();
        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $jobList  = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        DebugHelper::writeLog($query->createCommand()
            ->getRawSql(), '单位端职位sql');

        //todo 返回数组处理

        // 日期时间格式数据数组
        $timeArray = [
            'add_time',
            'period_date',
            'offline_time',
            'refresh_time',
            'release_time',
            'delete_time',
            'apply_audit_time',
            'real_refresh_time',
        ];

        // 相关数据数组准备
        $jobAuditStatusList = self::JOB_AUDIT_STATUS_NAME;
        $educationTypeList  = BaseDictionary::getEducationList();
        $experienceList     = BaseDictionary::getExperienceList();
        $jobOfflineTypeList = BaseJob::JOB_OFFLINE_TYPE_NAME;

        // 单位权益信息
        $jobRefreshIntervalDay = 0;
        $jobReleaseIntervalDay = 0;
        $jobRefreshAmount      = 0;
        $jobReleaseAmount      = 0;
        $companyRole           = 1;
        $dateTime              = CUR_DATETIME;

        //        $companyId            = BaseCompany::findOneVal(['member_id' => $memberId], 'id');
        $companyId            = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'company_id');
        $companyPackageConfig = BaseCompanyPackageConfig::getCompanyPackageConfig($companyId);

        if ($companyPackageConfig) {
            $jobRefreshIntervalDay = $companyPackageConfig['job_refresh_interval_day'];
            $jobReleaseIntervalDay = $companyPackageConfig['job_release_interval_day'];
            if ($companyPackageConfig['companyRole'] == BaseCompanyPackageConfig::COMPANY_ROLE_EXPIRE) {
                $jobRefreshAmount = 0;
                $jobReleaseAmount = 0;
            } else {
                $jobRefreshAmount = $companyPackageConfig['job_refresh_amount'];
                $jobReleaseAmount = $companyPackageConfig['job_amount'];
            }
            $companyRole = $companyPackageConfig['companyRole'];
        }
        $company_config_info = BaseCompanyMemberConfig::getInfo($companyId);

        foreach ($jobList as &$list) {
            // 调整跳转链接
            // https://zentao.jugaocai.com/index.php?m=story&f=view&id=1180
            if ($list['first_release_time'] != TimeHelper::ZERO_TIME) {
                $list['jobUrl']          = UrlHelper::createJobDetailPath($list['id']);
                $list['announcementUrl'] = UrlHelper::createAnnouncementDetailPath($list['announcement_id']);
            }

            $list['wage']                  = BaseJob::formatWage($list['min_wage'], $list['max_wage'],
                $list['wage_type']);
            $list['experience_type_title'] = ($list['experience_type'] == 0) ? '未填' : $experienceList[$list['experience_type']];
            $list['education_type_title']  = ($list['education_type'] == 0) ? '未填' : $educationTypeList[$list['education_type']];
            $list['status_title']          = BaseJob::JOB_STATUS_NAME[$list['status']];
            $list['audit_status_title']    = ($list['audit_status'] == 0) ? '未填' : $jobAuditStatusList[$list['audit_status']];
            $list['city']                  = BaseArea::getAreaName($list['city_id']);
            $list['offline_type_title']    = $jobOfflineTypeList[$list['offline_type']];

            //处理职位联系人与协同账号
            $list['job_contact']             = BaseJob::getJobContact($list['id']);
            $list['job_contact_synergy']     = BaseJob::getJobContactSynergy($list['id'], 1);
            $list['job_contact_synergy_num'] = count($list['job_contact_synergy']);
            $list['sub_used']                = intval($company_config_info['used']);

            //todo 职位刷新、发布的限制
            if ($jobRefreshAmount == 0) {
                $list['is_allow_refresh']       = 2;
                $list['is_allow_refresh_title'] = "当前职位不可刷新";
            } else {
                if (TimeHelper::reduceDates($dateTime, $list['refresh_time']) < $jobRefreshIntervalDay) {
                    $list['is_allow_refresh']       = 2;
                    $list['is_allow_refresh_title'] = "当前职位不可刷新";
                } else {
                    $list['is_allow_refresh']       = 1;
                    $list['is_allow_refresh_title'] = "当前职位可刷新";
                }
            }
            if ($companyRole == BaseCompanyPackageConfig::COMPANY_ROLE_FREE) {
                $list['is_allow_refresh']       = 3;
                $list['is_allow_refresh_title'] = "您当前是免费用户，暂不支持此项服务。开通套餐，可享超值优质服务！如有任何疑问，欢迎联系您的专属顾问！电话：***********";
            }

            if ($jobReleaseAmount == 0) {
                $list['is_allow_release']       = 2;
                $list['is_allow_release_title'] = "当前职位发布数量以用完";
            } else {
                if (TimeHelper::reduceDates($dateTime, $list['release_time']) < $jobReleaseIntervalDay) {
                    $list['is_allow_release']       = 2;
                    $list['is_allow_release_title'] = "当前职位不可发布";
                } else {
                    $list['is_allow_release']       = 1;
                    $list['is_allow_release_title'] = "当前职位可发布";
                }
            }

            // TODO 这里获取审核拒绝
            $list['opinion'] = '';

            if ($list['status'] == BaseJob::STATUS_REFUSE_AUDIT || $list['audit_status'] == BaseJob::AUDIT_STATUS_REFUSE_AUDIT) {
                $jobHandleLog = BaseJobHandleLog::find()
                    ->select(['handle_after'])
                    ->where([
                        'job_id'       => $list['id'],
                        'handler_type' => BaseJobHandleLog::HANDLER_TYPE_PLAT,
                        'handle_type'  => BaseJobHandleLog::HANDLE_TYPE_AUDIT,
                    ])
                    ->orderBy('id desc')
                    ->asArray()
                    ->one();

                if ($jobHandleLog) {
                    $handleAfter     = json_decode($jobHandleLog['handle_after'], true);
                    $list['opinion'] = $handleAfter['审核原因'] ?: '';
                }
            }

            //处理投递方式回显
            //投递方式
            $list['delivery_way_txt'] = '';
            if (empty($list['delivery_way'])) {//职位没有就显示公告的投递类型
                if (!empty($list['announcement_id'])) {
                    //                    $announcementInfo      = BaseAnnouncement::findOne($list['announcement_id']);
                    //                    $deliveryWay           = $announcementInfo->delivery_way;
                    //                    $list['delivery_way']  = strval($deliveryWay);
                    //                    $list['delivery_type'] = strval($announcementInfo->delivery_type);

                    $deliveryWay           = $list['announcement_delivery_way'];
                    $list['delivery_way']  = strval($deliveryWay);
                    $list['delivery_type'] = strval($list['announcement_delivery_type']);
                    if (in_array($deliveryWay, BaseAnnouncement::DELIVERY_WAY)) {
                        $list['delivery_way_txt'] = BaseAnnouncement::DELIVERY_WAY_NAME[$deliveryWay];
                    }
                }
            } else {//显示职位的投递类型
                if (in_array($list['delivery_way'], BaseJob::DELIVERY_WAY)) {
                    $list['delivery_way_txt'] = BaseJob::DELIVERY_WAY_NAME[$list['delivery_way']];
                }
            }
            $list['unread_account']         = 0;
            $list['wait_interview_account'] = 0;
            $list['all_apply_account']      = 0;
            $list['out_apply_account']      = 0;
            // 职位投递统计
            if ($list['delivery_type'] == BaseJob::DELIVERY_TYPE_INSIDE) {
                $jobApplyList = BaseJobApply::jobApplyStatistics($list['id']);
                //$list['jobApplyList']           = $jobApplyList;
                $list['unread_account']         = $jobApplyList['unreadApplyNum'] ?: 0;
                $list['wait_interview_account'] = $jobApplyList['jobInterviewNum'] ?: 0;
                $list['all_apply_account']      = $jobApplyList['allJobApplyNum'] ?: 0;
            } else {
                // 站外职位的投递
                $list['out_apply_account'] = BaseOffSiteJobApply::find()
                    ->andWhere(['job_id' => $list['id']])
                    ->count();
            }

            //下线时候的职位特殊处理一下下线时间
            if ($list['status'] == BaseJob::STATUS_OFFLINE && $list['offline_type'] == BaseJob::OFFLINE_TYPE_AUTO) {
                if ($list['period_date'] == '0000-00-00 00:00:00') {
                    $dateBool = true;
                    if ($list['announcement_id'] > 0) {
                        $announcementInfo = BaseAnnouncement::findOne($list['announcement_id']);
                        if ($announcementInfo->period_date != '0000-00-00 00:00:00' && $announcementInfo->status == BaseAnnouncement::STATUS_OFFLINE) {
                            $periodDate = $announcementInfo->period_date;
                            $dateBool   = false;
                        }
                    }
                    if ($dateBool) {
                        if (strtotime($list['release_time']) < 1) {
                            $releaseTime = CUR_DATETIME;
                        } else {
                            $releaseTime = $list['release_time'];
                        }
                        // 没有填截止日期，默认给发布时间后的365天
                        $periodDate = date('Y-m-d H:i:s',
                            strtotime($releaseTime) + 60 * 60 * 24 * self::ADD_RELEASE_AGAIN_DAY);
                    }
                } else {
                    $periodDate = $list['period_date'];
                }

                $list['offline_time'] = $periodDate;
            }

            // 日期时间类型处理
            foreach ($timeArray as $item) {
                if (strtotime($list[$item]) < 1) {
                    $list[$item] = "";
                } else {
                    $list[$item] = TimeHelper::formatDateByYear($list[$item]);
                }
            }

            //按钮组
            $list['buttonGroup'] = (new ButtonGroupAuthService())->setType(ButtonGroupAuthService::TYPE_JOB)
                ->run($list['id']);
        }

        return [
            'list'                 => $jobList,
            'page'                 => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
            'companyAuthorityList' => $companyAuthorityList,
        ];
    }

    public static function getDetailUrl($id)
    {
        return Url::toRoute([
            '/job/detail',
            'id' => $id,
        ]);
    }

    /**
     * 获取公告下职位列表
     * @param $announcementId
     * @throws \Exception
     */
    public static function getAnnouncementChildJobList($searchData)
    {
        $memberId = Yii::$app->user->id;
        $query    = self::find()
            ->alias('j')
            ->leftJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id = j.announcement_id')
            ->where([
                'j.announcement_id' => $searchData['id'],
                'j.status'          => [
                    self::STATUS_OFFLINE,
                    self::STATUS_ONLINE,
                ],
                'j.is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->select([
                'j.name as jobName',
                'j.id as jobId',
                'j.status',
                'j.wage_type',
                'j.min_wage',
                'j.max_wage',
                'j.education_type',
                'j.amount',
                'j.province_id',
                'j.city_id',
                'j.major_id',
                'j.nature_type',
                'j.delivery_way',
                'a.delivery_way as announcement_delivery_way',
                'j.apply_type as applyType',
                'c.is_cooperation as isCooperation',
            ]);
        //筛选学历
        $query->andFilterWhere(['j.education_type' => $searchData['educationId']]);
        //职位类型
        $query->andFilterWhere(['j.job_category_id' => $searchData['jobType']]);
        //专业
        if ($searchData['majorId']) {
            $query->leftJoin(['jar' => BaseJobMajorRelation::tableName()], 'j.id=jar.job_id')
                ->groupBy('j.id')
                ->andWhere(['jar.major_id' => $searchData['majorId']]);
        }
        $count = $query->count();

        $pageSize = $searchData['pageSize'] ?: Yii::$app->params['announcementJobListDefaultPageSize'];

        $pages = self::setPage($count, $searchData['page'], $pageSize);
        $list  = $query->offset($pages['offset'])
            ->orderBy('j.status desc,j.refresh_time desc,j.id asc')
            ->limit($pages['limit'])
            ->asArray()
            ->all();
        if ($memberId) {
            //获取简历信息
            $resume_info = BaseResume::findOne([
                'member_id' => $memberId,
                'status'    => BaseResume::STATUS_ACTIVE,
            ]);
        }
        foreach ($list as &$job) {
            $job['url']       = Url::toRoute([
                '/job/detail',
                'id' => $job['jobId'],
            ]);
            $job['wage']      = BaseJob::formatWage($job['min_wage'], $job['max_wage'], $job['wage_type']);
            $job['education'] = BaseDictionary::getEducationName($job['education_type']);
            //            $job['experience'] = BaseDictionary::getExperienceName($job['experience_type']);
            $area        = BaseArea::getAreaName($job['province_id']) . BaseArea::getAreaName($job['city_id']);
            $job['area'] = StringHelper::subtractString($area, '-');

            $job['major']  = BaseMajor::getAllMajorName(explode(',', $job['major_id']));
            $job['nature'] = BaseDictionary::getNatureName($job['nature_type']);

            //判断是否合作单位
            $job['applyStatus'] = self::JOB_APPLY_STATUS_NO;
            if ($job['isCooperation'] == BaseCompany::COOPERATIVE_UNIT_YES) {
                $job['isCooperation'] = 'true';
            } else {
                $job['isCooperation'] = 'false';
            }
            //如果用户已经登录了，获取用户投递信息
            if (!empty($memberId)) {
                //获取用户对该职位投递情况
                $job['applyStatus'] = BaseJobApplyRecord::checkJobApplyStatus($resume_info->id, $job['jobId']);
            }
            //如果存在用户，查看用户是否已经收藏了职位
            $job['collectStatus'] = self::JOB_COLLECT_STATUS_NO;
            if (!empty($memberId)) {
                //获取收藏状态
                $isCollect = BaseJobCollect::checkIsCollect($memberId, $job['jobId']);
                if ($isCollect) {
                    $job['collectStatus'] = self::JOB_COLLECT_STATUS_YES;
                }
            }

            //判断职位投递方式是否是邮箱投递
            $applyTypeArr        = explode(',', $job['applyType']);
            $job['isEmailApply'] = 'false';

            if (in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr)) {
                $job['isEmailApply'] = 'true';
            }
        }

        return [
            'list'        => $list,
            'pageSize'    => $pageSize,
            'currentPage' => $searchData['page'],
            'totalNum'    => $count,

        ];
    }

    /**
     * 获取（单位端）公告下职位列表
     * @param $params
     * @param $type
     * @throws \Exception
     */
    public static function getCompanyAnnouncementJobList($params, $type = 0)
    {
        if ($type == self::STATUS_WAIT) {
            $where = [
                'j.status' => [
                    self::STATUS_WAIT,
                ],
            ];
        } else {
            $where = [
                'j.status' => [
                    self::STATUS_ACTIVE,
                    self::STATUS_OFFLINE,
                ],
            ];
        }

        $query = self::find()
            ->alias('j')
            ->leftJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
            ->where(['announcement_id' => $params['id']])
            ->andWhere($where)
            ->select([
                'j.name as jobName',
                'j.id as jobId',
                'j.status',
                'j.wage_type',
                'j.min_wage',
                'j.max_wage',
                'j.education_type',
                'j.amount',
                'j.province_id',
                'j.city_id',
                'j.major_id',
                'j.nature_type',
                'j.department',
                'j.apply_type as applyType',
                'c.is_cooperation as isCooperation',
            ]);

        $count    = $query->count();
        $pageSize = $params['pageSize'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $params['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->asArray()
            ->all();

        foreach ($list as &$job) {
            $job['url']       = Url::toRoute([
                '/job/detail',
                'id' => $job['jobId'],
            ]);
            $job['wage']      = BaseJob::formatWage($job['min_wage'], $job['max_wage'], $job['wage_type']);
            $job['education'] = BaseDictionary::getEducationName($job['education_type']);
            //            $job['experience'] = BaseDictionary::getExperienceName($job['experience_type']);
            $area        = BaseArea::getAreaName($job['province_id']) . '-' . BaseArea::getAreaName($job['city_id']);
            $job['area'] = StringHelper::subtractString($area, '-');

            $job['major']  = BaseMajor::getMajorName(explode(',', $job['major_id'])[0]);
            $job['nature'] = BaseDictionary::getNatureName($job['nature_type']);
        }

        return [
            'list'        => $list,
            'pageSize'    => $pageSize,
            'currentPage' => $params['page'],
            'totalNum'    => $count,

        ];
    }

    /**
     * 判断是否是合作单位
     * @param $jobId     int 职位ID
     * @return bool
     */
    public static function checkCooperation($jobId)
    {
        $rel = self::find()
            ->alias('j')
            ->leftJoin(['c' => Company::tableName()], 'c.id=j.company_id')
            ->where(['j.id' => $jobId])
            ->select('c.is_cooperation')
            ->asArray()
            ->one();
        if ($rel['is_cooperation'] == Company::COOPERATIVE_UNIT_YES) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断是否邮箱投递
     * @param $jobId
     * @return array
     */
    public static function checkIsEmailApply($jobId)
    {
        $job = self::find()
            ->where(['id' => $jobId])
            ->select('apply_type')
            ->asArray()
            ->one();

        $data['isEmailApply']  = false;
        $data['isOnlineApply'] = false;
        $data['isOtherApply']  = false;
        if (!empty($job['apply_type'])) {
            $applyTypeArr = explode(',', $job['apply_type']);
            if (in_array(self::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr)) {
                $data['isEmailApply'] = true;
            } elseif (in_array(self::ATTRIBUTE_APPLY_ONLINE, $applyTypeArr)) {
                $data['isOnlineApply'] = true;
            } else {
                $data['isOtherApply'] = true;
            }
        }

        return $data;
    }

    /**
     * 职位批量导入数据
     * @throws Exception
     */
    public static function jobTemporaryBatchImport($keywords): array
    {
        $filePath = Yii::getAlias('@frontendPc') . '/web/' . $keywords['filePath'];
        // 插入数据
        $memberId    = Yii::$app->user->id;
        $companyInfo = BaseCompany::findOne(['member_id' => $memberId]);
        $companyId   = $companyInfo->id;
        // 读取数据
        $model = new JobBatchImport();
        $model->identify($filePath, BaseCompany::getDeliveryTypeCate($companyInfo->delivery_type));
        $data = $model->clearData;

        $jobTempData = [];
        foreach ($data as $item) {
            $jobData                    = array_column($item, 'value', 'key');
            $jobData                    = FormatConverter::convertHump($jobData);
            $jobData['announcement_id'] = $keywords['announcementId'];
            $jobData['member_id']       = $memberId;
            $jobData['company_id']      = $companyId;
            $jobData['status']          = $keywords['status'] ?: BaseJob::STATUS_WAIT;

            $batchJobService = new BatchJobService();
            $batchJobService->setOperator($memberId, BaseServiceAlias::OPERATOR_TYPE_COMPANY)
                ->setData($jobData)
                ->run();

            $jobTempData[] = $batchJobService->jobData;
        }

        //删除文件
        unlink($filePath);

        return $jobTempData;
    }

    /**
     * 获取模版职位详情
     * @throws Exception
     */
    public static function getJobDetails($data)
    {
        if (!$data['id']) {
            throw new Exception('职位Id不能为空');
        }

        //查询职位详情
        $jobWhere   = ['id' => $data['id']];
        $jobSelect  = [
            'member_id',
            'company_id',
            'is_article',
            'name',
            'period_date',
            'is_stick',
            'code',
            'job_category_id',
            'education_type',
            'major_id',
            'nature_type',
            'is_negotiable',
            'wage_type',
            'min_wage',
            'max_wage',
            'experience_type',
            'age_type',
            'min_age',
            'max_age',
            'title_type',
            'political_type',
            'abroad_type',
            'amount',
            'department',
            'province_id',
            'city_id',
            'district_id',
            'address',
            'welfare_tag',
            'duty',
            'requirement',
            'remark',
            'audit_status',
            'apply_type',
            'apply_address',
            'file_ids',
            'delivery_limit_type',
            'delivery_type',
            'delivery_way',
            'extra_notify_address',
        ];
        $jobDetails = self::selectInfo($jobWhere, $jobSelect);

        $areaCache = BaseArea::setAreaCache();

        $jobDetails['areaName']       = $areaCache[$jobDetails['province_id']]['name'] . $areaCache[$jobDetails['city_id']]['name'];
        $jobDetails['applyTypeTitle'] = BaseJob::getApplyTypeName($jobDetails['apply_type']) ?: '-';

        //薪资code回显
        if ($jobDetails['is_negotiable'] <> 1) {
            $jobDetails['wage_id'] = (string)BaseJob::getWageId($jobDetails['min_wage'], $jobDetails['max_wage']);
        }

        //查询福利标签
        $jobDetails['welfareTag'] = [];
        $welfareLabelWhere        = ['id' => explode(',', $jobDetails['welfare_tag'])];
        $welfareLabelSelect       = [
            'id',
            'name',
        ];
        $welfareLabelList         = BaseWelfareLabel::findList($welfareLabelWhere, $welfareLabelSelect);

        foreach ($welfareLabelList as $k => $welfareLabel) {
            $jobDetails['welfareTag'][$k]['k'] = $welfareLabel['id'];
            $jobDetails['welfareTag'][$k]['v'] = $welfareLabel['name'];
        }

        $jobDetails['experience_type'] = $jobDetails['experience_type'] == 0 ? '' : $jobDetails['experience_type'];
        $jobDetails['title_type']      = $jobDetails['title_type'] == 0 ? '' : $jobDetails['title_type'];
        $jobDetails['political_type']  = $jobDetails['political_type'] == 0 ? '' : $jobDetails['political_type'];
        $jobDetails['abroad_type']     = $jobDetails['abroad_type'] == 0 ? '' : $jobDetails['abroad_type'];
        $jobDetails['fileList']        = BaseAnnouncement::getAppendixList($jobDetails['file_ids']);

        return $jobDetails;
    }

    /**
     * 获取职位各个状态数量
     * @return array
     * @throws Exception
     */
    public static function getJobColumnAmount(): array
    {
        $memberId    = Yii::$app->user->id;
        $companyInfo = BaseCompany::getCompanyInfo($memberId);
        if (!$companyInfo) {
            throw new Exception('非法访问');
        }

        $query = BaseJob::find()
            ->alias('j')
            ->select([
                'j.id',
                'j.status',
                'j.audit_status',
            ])
            ->andWhere(self::BASE_JOB_LIST_CONDITION);

        $authorityList = (new CompanyAuthorityClassify())->run([
            'associatedField' => 'j.company_id',
            'memberId'        => Yii::$app->user->id,
            'query'           => $query,
            'returnType'      => CompanyAuthorityClassify::DATA_JOB_COOPERATE,
        ]);
        if ($authorityList) {
            $query = $authorityList['query'];
        }

        $jobList = $query->asArray()
            ->all();

        $column_1 = 0;
        $column_2 = 0;
        $column_3 = 0;
        $column_4 = 0;
        foreach ($jobList as $item) {
            if ($item['status'] == BaseJob::STATUS_ONLINE) {
                $column_1++;
            }

            if ($item['status'] == BaseJob::STATUS_WAIT) {
                $column_2++;
            }

            if ($item['status'] == BaseJob::STATUS_OFFLINE) {
                $column_3++;
            }

            $column_4++;
        }

        $list             = [];
        $list['column_1'] = $column_1;
        $list['column_2'] = $column_2;
        $list['column_3'] = $column_3;
        $list['column_4'] = $column_4;

        return $list;
    }

}