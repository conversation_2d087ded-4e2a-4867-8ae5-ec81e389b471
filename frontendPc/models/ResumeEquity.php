<?php

namespace frontendPc\models;

use common\base\models\BaseResume;
use common\base\models\BaseResumeEquity;
use common\base\models\BaseResumeEquityPackage;
use common\base\models\BaseResumeEquityPackageCategorySetting;
use common\base\models\BaseResumeEquityPackageSetting;
use common\base\models\BaseResumeEquitySetting;
use common\base\models\BaseResumeSetting;
use common\base\models\BaseSystemConfig;
use common\libs\Cache;
use phpDocumentor\Reflection\Types\Self_;

class ResumeEquity extends BaseResumeEquity
{
    /**
     * 查我的服务-顶部提示消息
     */
    public static function getMyServicesTopTips($resumeId)
    {
        // 是否有效会员
        $resumeInfo = BaseResume::findOne($resumeId);
        //获取简历设置
        $resumeSetting     = BaseResumeSetting::findOne(['resume_id' => $resumeId]);
        $is_tips           = false;
        $message_title     = '';
        $message_content   = '';
        $tips              = '';
        $toast_message_arr = [];
        if ($resumeInfo->vip_level == BaseResume::VIP_LEVEL_DIAMOND) {
            $config_complete = BaseSystemConfig::getValue(BaseSystemConfig::RESUME_LIBRARY_COMPLETE_MIN_KEY);
            if ($resumeInfo->complete < $config_complete) {
                $is_tips          = true;
                $complete_message = '<div><span class="required">*</span>您当前在线简历完善度不足（低于' . $config_complete . '%）；<a href="/member/person/resume" target="_blank" id="complete">立即完善</a></div>';
                array_push($toast_message_arr, $complete_message);
            }
            if ($resumeSetting->is_hide_resume == BaseResumeSetting::IS_HIDE_RESUME_YES) {
                $is_tips      = true;
                $hide_message = '<div class="resume-status"><span class="required">*</span>您目前的简历隐私设置为隐藏状态。<a href="javascript:;" onclick="openResume(event)">立即公开</a>';
                array_push($toast_message_arr, $hide_message);
            }
        }
        if ($is_tips) {
            $tips           = '温馨提示：建议您目前不要使用简历置顶等特权功能！';
            $message_title  = '温馨提示';
            $message_before = '<div>由于以下原因，将导致您的曝光特权使用失效： </div>';
            array_unshift($toast_message_arr, $message_before);
            array_push($toast_message_arr, '</div>');
            $message_content = implode('', $toast_message_arr);
        }

        //找出所有增值服务，查看是否有要过期的
        $expireTime   = date('Y-m-d H:i:s', strtotime('+7 days'));
        $expireRecord = BaseResumeEquityPackage::find()
            ->select([
                'package_category_id as packageCategoryId',
                'expire_time as expireTime',
            ])
            ->where([
                'resume_id'     => $resumeId,
                'expire_status' => BaseResumeEquityPackage::STATUS_EXPIRE,
            ])
            ->andWhere([
                '<',
                'expire_time',
                $expireTime,
            ])
            ->groupBy('package_category_id')
            ->orderBy('equity_id asc')
            ->asArray()
            ->all();
        $expireTips   = [];
        if (!empty($expireRecord)) {
            foreach ($expireRecord as $k => $item) {
                $productName                 = BaseResumeEquityPackageCategorySetting::VIP_TYPE_TEXT_LIST[$item['packageCategoryId']];
                $expireDay                   = BaseResumeEquityPackage::countResidueDaysByExpireTime($item['expireTime']);
                $expireDayText               = $expireDay == 0 ? '今' : $expireDay;
                $expireTips[$k]['content']   = '温馨提示：您的' . $productName . '服务将在' . $expireDayText . '天后过期，请及时使用资源！续费可继续享受专属权益';
                $expireTips[$k]['btnText']   = '去续费';
                $expireTips[$k]['url']       = BaseResumeEquityPackageCategorySetting::VIP_TYPE_BUY_URL[$item['packageCategoryId']];
                $expireTips[$k]['expireDay'] = $expireDay;
            }
        }
        $expireDayList = array_column($expireTips, 'expireDay');
        array_multisort($expireDayList, $expireTips);

        return [
            'is_tips'         => $is_tips,
            'tips'            => $tips,
            'expireTips'      => $expireTips,
            'message_title'   => $message_title,
            'message_content' => $message_content,
        ];
    }

    /**
     * 查询我的服务
     */
    public static function getMyServices($resumeId)
    {
        // 我的服务配置
        $myServicesConfig = ResumeEquitySetting::MY_SERVICES_CONFIG;
        // 是否有效会员
        $resumeInfo = BaseResume::findOne($resumeId);
        if ($resumeInfo->vip_type == Resume::VIP_TYPE_ACTIVE) {
            $vipEquityIds = $resumeInfo->vip_level == BaseResume::VIP_LEVEL_GOLD ? BaseResumeEquitySetting::ID_GOLD_VIP_SERVICES : BaseResumeEquitySetting::ID_DIAMOND_VIP_SERVICES;
            $vipPackageId = $resumeInfo->vip_level == BaseResume::VIP_LEVEL_GOLD ? BaseResumeEquityPackageCategorySetting::ID_GOLD_VIP : BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP;
            // 获取会员用户权益
            $vipEquityRows = BaseResumeEquityPackage::getEquityList($resumeId, $vipEquityIds,
                BaseResumeEquityPackage::STATUS_EXPIRE, $vipPackageId);
        } else {
            $vipEquityRows = [];
        }

        // 追加配置
        $vipEquityRowsNew = [];
        foreach ($vipEquityRows as $row) {
            $item = array_merge($row, $myServicesConfig[$row['equity_id']]);
            if (in_array($row['equity_id'], BaseResumeEquitySetting::ID_JOB_FAST_SERVICES)) {
                if ($row['equity_id'] == BaseResumeEquitySetting::ID_RESUME_REFRESH) {
                    $tips_tag = '刷新中';
                } elseif ($row['equity_id'] == BaseResumeEquitySetting::ID_RESUME_TOP) {
                    $tips_tag     = $row['amount'] > 0 ? "剩余{$row['amount']}天" : '已用完';
                    $item['tips'] = $row['amount'] > 0 ? $item['tips'] : '去购买';
                } elseif ($row['equity_id'] == BaseResumeEquitySetting::ID_DELIVERY_TOP) {
                    $tips_tag     = $row['amount'] > 0 ? "剩余{$row['amount']}次" : '已用完';
                    $item['tips'] = $row['amount'] > 0 ? $item['tips'] : '去购买';
                } else {
                    $tips_tag = '';
                }
                $item['tips_tag'] = $tips_tag;
                array_unshift($vipEquityRowsNew, $item);
            } elseif ($row['equity_id'] == BaseResumeEquitySetting::ID_ESTABLISHMENT_QUERY) {
                //写死前端显示
                $item['badge'] = '功能升级';
                array_push($vipEquityRowsNew, $item);
            } else {
                $item['tips_tag'] = '';
                array_push($vipEquityRowsNew, $item);
            }
        }

        // 获取洞察用户权益
        $insightEquityRows = BaseResumeEquityPackage::getEquityList($resumeId,
            BaseResumeEquitySetting::ID_VALUE_ADDED_SERVICES, self::STATUS_EXPIRE,
            BaseResumeEquityPackageCategorySetting::ID_INSIGHT);
        $isInsightEquity   = false;
        if (empty($insightEquityRows)) { //未开通或已过期
            $insightEquityRows = BaseResumeEquitySetting::find()
                ->select('id AS equity_id, name')
                ->where(['id' => BaseResumeEquitySetting::ID_VALUE_ADDED_SERVICES])
                ->asArray()
                ->all();
        } else {
            $isInsightEquity = true;
        }

        // 追加配置
        $insightEquityExpire  = '';
        $insightEquityJumpUrl = '';
        foreach ($insightEquityRows as &$row) {
            $row = array_merge($row, $myServicesConfig[$row['equity_id']] ?? '');
            if (!$insightEquityExpire) {
                $insightEquityExpire = $row['expire_date'];
            }
            if (!$insightEquityJumpUrl) {
                $insightEquityJumpUrl = $row['jumpUrl'];
            }
        }

        //求职快
        $jobFastRows     = BaseResumeEquityPackage::getEquityList($resumeId,
            BaseResumeEquitySetting::ID_JOB_FAST_SERVICES, self::STATUS_EXPIRE,
            BaseResumeEquityPackageCategorySetting::ID_JOB_FAST);
        $isJobFastEquity = false;
        if (empty($jobFastRows)) {
            $jobFastRows = BaseResumeEquitySetting::find()
                ->select('id AS equity_id, name')
                ->where(['id' => BaseResumeEquitySetting::ID_JOB_FAST_SERVICES])
                ->asArray()
                ->all();
        } else {
            $isJobFastEquity = true;
        }

        // 追加配置
        $jobFastEquityExpire  = '';
        $jobFastEquityJumpUrl = '';
        foreach ($jobFastRows as &$row) {
            $row = array_merge($row, $myServicesConfig[$row['equity_id']] ?? '');
            if (!$jobFastEquityExpire) {
                $jobFastEquityExpire = $row['expire_date'];
            }
            if (!$jobFastEquityJumpUrl) {
                $jobFastEquityJumpUrl = $row['jumpUrl'];
            }
            if (isset($row['expire_status']) && $row['expire_status'] == BaseResumeEquityPackage::STATUS_EXPIRE) {
                if ($row['equity_id'] == BaseResumeEquitySetting::ID_RESUME_REFRESH) {
                    $tips_tag = '刷新中';
                } elseif ($row['equity_id'] == BaseResumeEquitySetting::ID_RESUME_TOP) {
                    $tips_tag    = $row['amount'] > 0 ? "剩余{$row['amount']}天" : '已用完';
                    $row['tips'] = $row['amount'] > 0 ? $row['tips'] : '去购买';
                } elseif ($row['equity_id'] == BaseResumeEquitySetting::ID_DELIVERY_TOP) {
                    $tips_tag    = $row['amount'] > 0 ? "剩余{$row['amount']}次" : '已用完';
                    $row['tips'] = $row['amount'] > 0 ? $row['tips'] : '去购买';
                } else {
                    $tips_tag = '';
                }
            } else {
                $tips_tag = '';
            }

            $row['tips_tag'] = $tips_tag;
        }

        $res = [
            'vipEquityList'        => $vipEquityRowsNew,
            'insightEquityList'    => $insightEquityRows,
            'isInsightEquity'      => $isInsightEquity,
            'insightEquityExpire'  => $insightEquityExpire,
            'insightEquityJumpUrl' => $insightEquityJumpUrl,
            'jobFastRows'          => $jobFastRows,
            'isJobFastEquity'      => $isJobFastEquity,
            'jobFastEquityExpire'  => $jobFastEquityExpire,
            'jobFastEquityJumpUrl' => $jobFastEquityJumpUrl,
        ];

        return $res;
    }

    /**
     * 查询求职工具
     */
    public static function getJobTools($resumeId)
    {
        return ResumeEquitySetting::JOB_TOOLS_CONFIG;
    }

    /**
     * 获取服务即将过期提醒弹窗
     * @param $resumeId
     * @return array|void
     */
    public static function getServicesExpirePopInfo($resumeId)
    {
        $data = [];
        //查看7天是否黄金会员过期
        $goldVipExpireDays = BaseResumeEquityPackage::getExpireDays(BaseResumeEquityPackageCategorySetting::ID_GOLD_VIP,
            $resumeId, 7);
        if ($goldVipExpireDays) {
            //获取钻石vip30天的信息
            $diamondPackage            = BaseResumeEquityPackageSetting::find()
                ->where([
                    'equity_package_category_id' => BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP,
                    'status'                     => self::STATUS_ACTIVE,
                    'days'                       => 30,
                ])
                ->select([
                    'id',
                    'days',
                    'real_amount as realAmount',
                    '0 + CAST(original_amount AS CHAR) AS originalAmount',
                ])
                ->orderBy('real_amount asc')
                ->asArray()
                ->one();
         
            $data['goldVipExpireDays'] = $goldVipExpireDays;
            $data['realAmount']        = $diamondPackage['realAmount'];
            $data['originalAmount']    = $diamondPackage['originalAmount'];
            $data['url']               = BaseResume::BUY_URL_VIP;
            $data['popType']           = 1;

            return self::setExpirePopInfo($data);
        }
        //如果是其他类型的要过期
        $otherExpireRecord = BaseResumeEquityPackage::find()
            ->select([
                'package_category_id as packageCategoryId',
                'expire_time as expireTime',
                'package_category_id as packageCategoryId',
            ])
            ->where([
                'resume_id'           => $resumeId,
                'package_category_id' => [
                    BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP,
                    BaseResumeEquityPackageCategorySetting::ID_INSIGHT,
                    BaseResumeEquityPackageCategorySetting::ID_JOB_FAST,
                ],
                'expire_status'       => BaseResumeEquityPackage::STATUS_EXPIRE,
            ])
            ->groupBy('package_category_id')
            ->asArray()
            ->all();
        if (empty($otherExpireRecord)) {
            return [];
        }
        $remainPackageList = [];
        //默认排序
        $sortList = [
            BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP => 1,
            BaseResumeEquityPackageCategorySetting::ID_JOB_FAST    => 2,
            BaseResumeEquityPackageCategorySetting::ID_INSIGHT     => 3,
        ];
        foreach ($otherExpireRecord as $k => $item) {
            //判断是否有钻石、求职快、竞争力洞察要过期的
            $remainDays         = BaseResumeEquityPackage::countResidueDaysByExpireTime($item['expireTime']);
            $isDiamondOrJobFast = in_array($item['packageCategoryId'], [
                BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP,
                BaseResumeEquityPackageCategorySetting::ID_JOB_FAST,
            ]);
            if (($remainDays < 7 && $isDiamondOrJobFast) || ($remainDays < 3 && $item['packageCategoryId'] == BaseResumeEquityPackageCategorySetting::ID_INSIGHT)) {
                $remainPackageList[] = [
                    'packageCategoryId' => $item['packageCategoryId'],
                    'remainDays'        => $remainDays,
                    'sort'              => $sortList[$item['packageCategoryId']],
                ];
            }
        }
        //没有要过期的，返回空
        if (empty($remainPackageList)) {
            return [];
        }
        $sort = array_column($remainPackageList, 'sort');
        array_multisort($sort, SORT_ASC, $remainPackageList);
        foreach ($remainPackageList as $item) {
            $remainDaysText = $item['remainDays'] == 0 ? '今' : $item['remainDays'];

            return self::setExpirePopInfo([
                'packageCategoryId' => $item['packageCategoryId'],
                'remainDaysText'    => $remainDaysText,
            ]);
        }
    }

    /**
     * 设置弹窗内容
     * @param $data
     * @return array
     */
    private static function setExpirePopInfo($data)
    {
        if ($data['popType'] == 1) {
            $info['title']            = "您的黄金VIP服务将在<span>" . $data['goldVipExpireDays'] . "天</span>后过期";
            $info['subTitle']         = '升级钻石VIP，可享限时优惠';
            $info['lowerPrice']       = $data['realAmount'];
            $info['label']            = '专享价';
            $info['originalPrice']    = $data['originalAmount'];
            $info['updateTipTitle']   = '升级后您还可以获得';
            $info['updateTipContent'] = [
                '简历排名靠前',
                '曝光翻倍提升',
                '更多投递反馈',
            ];
            $info['btnText']          = '立即升级';
            $info['url']              = BaseResume::BUY_URL_VIP;

            return $info;
        }
        $packageCateName   = BaseResumeEquityPackageCategorySetting::VIP_TYPE_TEXT_LIST[$data['packageCategoryId']];
        $lowestPackageInfo = BaseResumeEquityPackageSetting::getLowestPricePackageInfo($data['packageCategoryId']);
        $url               = ResumeEquityPackageCategorySetting::VIP_URL_LIST[$data['packageCategoryId']];

        $info['title']         = '您的' . $packageCateName . '服务将在<span>' . $data['remainDaysText'] . '天</span>后过期';
        $info['subTitle']      = '请及时使用资源';
        $info['lowerPrice']    = $lowestPackageInfo['realAmount'];
        $info['originalPrice'] = $lowestPackageInfo['originalAmount'];
        $info['label']         = $lowestPackageInfo['discount'] . '折';
        $info['tips']          = '可继续享' . $lowestPackageInfo['days'] . '天' . $lowestPackageInfo['subname'] . '服务专属权益';
        $info['btnText']       = '立即续费';
        $info['url']           = $url;

        return $info;
    }

    /**
     * 获取用户是否关闭个人中心底部广告缓存
     * @param $memberId
     * @return bool
     */
    public static function getClosePersonCenterVipShowcase($memberId)
    {
        $cacheKey = Cache::PC_PERSON_CENTER_VIP_SHOWCASE_CLOSE . ':' . $memberId;
        $isClose  = Cache::get($cacheKey);
        if ($isClose) {
            return true;
        }

        return false;
    }

    /**
     * 设置用户关闭个人中心底部广告缓存7天
     * @param $memberId
     * @return void
     */
    public static function setClosePersonCenterVipShowcase($memberId)
    {
        $cacheKey   = Cache::PC_PERSON_CENTER_VIP_SHOWCASE_CLOSE . ':' . $memberId;
        $expireTime = strtotime('+7 days');
        $time       = $expireTime - time();
        Cache::set($cacheKey, $memberId, $time);
    }

    /**
     * 设置用户每天首次访问个人中心底部广告
     * @param $memberId
     * @return void
     */
    public static function setDailyPersonCenterVipShowcase($memberId)
    {
        $cacheKey   = Cache::PC_PERSON_CENTER_VIP_SHOWCASE_DAILY . ':' . $memberId;
        $expireTime = strtotime(date('Y-m-d') . ' 23:59:59') - time();
        Cache::set($cacheKey, $memberId, $expireTime);
    }

    /**
     * 查询用户是否每天首次访问个人中心
     * @param $memberId
     * @return false
     */
    public static function getDailyPersonCenterVipShowcase($memberId): bool
    {
        $cacheKey = Cache::PC_PERSON_CENTER_VIP_SHOWCASE_DAILY . ':' . $memberId;
        $info     = Cache::get($cacheKey);
        if (!$info) {
            return true;
        }

        return false;
    }

    /**
     * 设置每天我的服务页面vip过期提醒
     * @param $memberId
     * @return void
     */
    public static function setDailyMyServiceExpirePop($memberId)
    {
        $cacheKey   = Cache::PC_MY_SERVICES_EXPIRE_TIPS_DAILY . ':' . $memberId;
        $expireTime = strtotime(date('Y-m-d') . ' 23:59:59') - time();
        Cache::set($cacheKey, $memberId, $expireTime);
    }

    /**
     * 获取每天我的服务页面vip过期提醒
     * @param $memberId
     * @return bool
     */
    public static function getDailyMyServiceExpirePop($memberId)
    {
        $cacheKey = Cache::PC_MY_SERVICES_EXPIRE_TIPS_DAILY . ':' . $memberId;

        $info = Cache::get($cacheKey);
        if (!$info) {
            return true;
        }

        return false;
    }
}