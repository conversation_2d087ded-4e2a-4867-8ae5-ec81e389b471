<?php

namespace frontendPc\models;

use common\base\models\BaseCompany;
use common\base\models\BaseCompanyInterview;
use common\base\models\BaseJobApply;
use common\base\models\BaseMemberSchedule;
use common\service\job\BaseService;
use common\service\job\JobApplyHandleService;
use yii\base\Exception;
use yii\db\ActiveRecord;

class CompanyInterview extends BaseCompanyInterview
{
    /**
     * 创建面试邀请记录
     * @param $request
     * @param $memberId
     * @throws Exception
     */
    public static function createCompanyInterview($request, $memberId)
    {
        if (!$request['job_apply_id']) {
            throw new Exception('标识Id不能为空');
        }
        if (!$request['interview_time']) {
            throw new Exception('面试时间不能为空');
        }
        if (!$request['contact']) {
            throw new Exception('联系人不能为空');
        }
        if (!$request['telephone']) {
            throw new Exception('联系电话不能为空');
        }
        if (!$request['address']) {
            throw new Exception('联系地址不能为空');
        }

        // 迁移到服务层,但是
        $auditService = new JobApplyHandleService();
        $params       = [
            'id'                  => $request['job_apply_id'],
            'handle_type'         => JobApplyHandleService::HANDEL_TYPE_SEND_INVITATION,
            'status'              => $request['status'],
            'company_mark_status' => $request['company_mark_status'] ?: 0,
            'baseData'            => $request,
        ];

        $auditService->setOperator($memberId, BaseService::OPERATOR_HANDLE_TYPE_COMPANY)
            ->setData($params)
            ->run();

        // 以下代码不再执行
        return true;

        $where      = [
            'job_apply_id' => $request['job_apply_id'],
        ];
        $select     = [
            'id',
            'interview_time',
        ];
        $orderBy    = 'interview_time desc';
        $selectInfo = self::selectInfo($where, $select, '', $orderBy);

        //        if ($selectInfo['interview_time'] > CUR_DATETIME) {
        //            throw new Exception('未到上次邀请面试时间，无法再次邀请');
        //        }

        $where = [
            'id' => $request['job_apply_id'],
        ];

        $select = [
            'job_name',
            'company_member_id',
            'resume_member_id',
        ];

        $jobApplyInfo        = BaseJobApply::selectInfo($where, $select);
        $request['job_name'] = $jobApplyInfo['job_name'];

        self::createCompanyInterviewInfo($request);

        //todo 这里添加面试日程
        $remindTime = date('Y-m-d H:i:s',
            (strtotime($request['interview_time']) - 60 * BaseMemberSchedule::REMIND_TYPE_LIST[BaseMemberSchedule::REMIND_TYPE_FIVE]));
        $data       = [
            'add_time'       => CUR_DATETIME,
            'update_time'    => CUR_DATETIME,
            'status'         => BaseMemberSchedule::STATUS_ONLINE,
            'title'          => '面试职位：' . $jobApplyInfo['job_name'],
            'content'        => $jobApplyInfo['job_name'],
            'begin_time'     => $request['interview_time'],
            'place'          => $request['address'],
            'remind_time'    => $remindTime,
            'remind_type'    => BaseMemberSchedule::REMIND_TYPE_FIVE,
            'is_need_remind' => BaseMemberSchedule::IS_NEED_REMIND_YES,
            'main_id'        => $request['job_apply_id'],
        ];
        // 企业面试日程
        $company_data = array_merge($data, [
            'member_id' => $jobApplyInfo['company_member_id'],
            'type'      => BaseMemberSchedule::TYPE_COMPANY_INTERVIEW,
        ]);
        //用户面试日程
        $person_data = array_merge($data, [
            'member_id' => $jobApplyInfo['resume_member_id'],
            'type'      => BaseMemberSchedule::TYPE_PERSON_INTERVIEW,
        ]);
        BaseMemberSchedule::createNormal($company_data, BaseMemberSchedule::TYPE_COMPANY_INTERVIEW);
        BaseMemberSchedule::createNormal($person_data, BaseMemberSchedule::TYPE_PERSON_INTERVIEW);

        // 整个过程都应该是在这里面的,所以现在只能把整个request传过去
        $auditService = new JobApplyHandleService();
        $params       = [
            'id'                  => $request['job_apply_id'],
            'handle_type'         => $request['handle_type'],
            'status'              => $request['status'],
            'company_mark_status' => $request['company_mark_status'] ?: 0,
            'baseData'            => $request,
        ];
        $auditService->setOperator($memberId, BaseService::OPERATOR_HANDLE_TYPE_COMPANY)
            ->setData($params)
            ->run();
        //JobApply::saveStatus($request, BaseJobApply::STATUS_SEND_INVITATION, $memberId);
    }

    /**
     * 获取邀请面试信息
     * @param $request
     * @return array|ActiveRecord|null
     * @throws Exception
     */
    public static function getCompanyInterviewInfo($request)
    {
        $jobApplyId       = explode('_', $request['job_apply_id'])[0];
        $memberScheduleId = explode('_', $request['job_apply_id'])[1];
        if (!$jobApplyId) {
            throw new Exception('标识Id不能为空');
        }

        $sortNumber         = 0;
        $memberScheduleList = BaseMemberSchedule::find()
            ->select(['id'])
            ->where([
                'main_id' => $jobApplyId,
                'type'    => BaseMemberSchedule::TYPE_COMPANY_INTERVIEW,
            ])
            ->orderBy('add_time desc,id desc')
            ->asArray()
            ->all();

        foreach ($memberScheduleList as $k => $item) {
            if ($memberScheduleId == $item['id']) {
                $sortNumber = $k;
            }
        }

        $where   = [
            'job_apply_id' => $jobApplyId,
        ];
        $select  = [
            'id',
            'job_apply_id',
            'job_name',
            'contact',
            'telephone',
            'address',
            'interview_time',
            'content',
            'is_look',
        ];
        $orderBy = 'add_time desc,id desc';

        $companyInterviewList = BaseCompanyInterview::find()
            ->select($select)
            ->where($where)
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        foreach ($companyInterviewList as $k => $value) {
            if ($k == $sortNumber) {
                $data = $value;
            }
        }

        return $data ?: BaseCompanyInterview::selectInfo($where, $select, [], $orderBy);
    }
}