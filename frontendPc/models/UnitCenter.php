<?php

namespace frontendPc\models;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArticle;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyAdminChangeLog;
use common\base\models\BaseCompanyInfoAuth;
use common\base\models\BaseCompanyInfoAuthLog;
use common\base\models\BaseCompanyInterview;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseCompanyPackageSystemConfig;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyHandleLog;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseMember;
use common\base\models\BaseMemberActionLog;
use common\base\models\BaseMemberMessage;
use common\base\models\BaseMemberSchedule;
use common\base\models\BaseMemberSuggest;
use common\base\models\BaseNews;
use common\base\models\BaseResume;
use common\helpers\FileHelper;
use common\helpers\TimeHelper;
use common\helpers\UUIDHelper;
use common\libs\Cache;
use common\libs\CompanyAuthority\CompanyAuthorityClassify;
use common\libs\Excel;
use yii\base\Exception;
use yii;

class UnitCenter extends BaseResume
{
    /**
     * 单位中心--企业信息
     */
    public static function getCompanyInfo($memberId): array
    {
        $select = [
            'c.id',
            'c.status',
            'c.full_name',
            'c.logo_url',
            'c.introduce',
            'l.id as ids',
            'l.reason',
            'c.delivery_type',
            'c.package_type',
            'cmi.id as company_member_info_id',
            'cmi.contact',
            'cmi.department',
            'cmi.member_rule',
            'cmi.company_member_type',
        ];

        //企业信息
        $companyInfo = BaseCompany::find()
            ->select($select)
            ->alias('c')
            ->leftJoin(['l' => BaseCompanyInfoAuthLog::tableName()], 'l.company_id = c.id')
            ->leftJoin(['cmi' => BaseCompanyMemberInfo::tableName()], 'cmi.company_id = c.id')
            ->where([
                'cmi.member_id' => $memberId,
            ])
            ->orderBy('l.id desc')
            ->asArray()
            ->one();

        //上次登录信息
        $actionLog = BaseMemberActionLog::find()
            ->select(['add_time'])
            ->where(['member_id' => $memberId])
            ->orderBy('id desc')
            ->asArray()
            ->one();

        //返回数据处理
        $Datetime     = date('H');
        $statusTitle  = "";
        $memberAction = "最近一次登录:" . $actionLog['add_time'];
        $code         = 1;
        $viewingRate  = "0%";
        $handleTime   = "0天";

        // 这里应该是看审核状态
        switch ($companyInfo['status']) {
            case 7:
            case 0:
                // 其实这里还有另外一个问题,就是有可能状态是7,但是审核状态是6,所以要去找审核状态
                $authInfo = BaseCompanyInfoAuth::find()
                    ->select(['audit_status'])
                    ->where(['company_id' => $companyInfo['id']])
                    ->orderBy('id desc')
                    ->asArray()
                    ->one();
                if ($authInfo['audit_status'] == 6) {
                    $statusTitle = '您的身份认证审核拒绝，原因：' . $companyInfo['reason'] . '。请重新认证。';
                    $code        = 3;
                } else {
                    $statusTitle = '您尚未进行身份认证，请进行身份认证。';
                    $code        = 2;
                }
                break;
            case 9:
            case 8:
                $statusTitle = '您的身份认证审核中...';
                $code        = 5;
                break;
            case 6:
            case -8:
            case -9:
                $statusTitle = '您的身份认证审核拒绝，原因：' . $companyInfo['reason'] . '。请重新认证。';
                $code        = 3;
                break;
        }
        if ($companyInfo['status'] == 1 && strlen($companyInfo['introduce']) < 1) {
            $statusTitle = '您尚未填写单位介绍，完善单位介绍更受求职者欢迎。';
            $code        = 4;
        }

        //计算简历查看率，简历处理用时
        if ($companyInfo['status'] == 1 && strlen($companyInfo['introduce']) > 1) {
            $isCheckAccount = 0;
            $jobApplyQuery  = BaseJobApply::find()
                ->alias('ja')
                ->leftJoin(['j' => BaseJob::tableName()], 'j.id = ja.job_id')
                ->select([
                    'ja.status',
                    'ja.is_check',
                    'ja.add_time',
                    'ja.id',
                    'ja.job_id',
                ]);

            $authorityList = (new CompanyAuthorityClassify())->run([
                'associatedField' => 'ja.company_id',
                'memberId'        => $memberId,
                'query'           => $jobApplyQuery,
                'returnType'      => CompanyAuthorityClassify::DATA_JOB_COOPERATE,
            ]);
            if ($authorityList) {
                $jobApplyQuery = $authorityList['query'];
            }

            $allAccount   = $jobApplyQuery->count();
            $jobApplyList = $jobApplyQuery->asArray()
                ->all();
            foreach ($jobApplyList as $list) {
                if ($list['is_check'] == 1) {
                    $isCheckAccount++;
                }
            }
            if ($isCheckAccount > 0) {
                $viewingRate = round($isCheckAccount / $allAccount * 100, 2) . '%';
            }

            $handleTime = BaseJobApply::jobApplyHandleTime($memberId);
        }

        $memberFullName = BaseMember::findOneVal(['id' => $memberId], 'username');
        if ($Datetime > 6 && $Datetime < 12) {
            $greetings = '早安，';
        } elseif ($Datetime > 12 && $Datetime < 18) {
            $greetings = '下午好，';
        } else {
            $greetings = '晚上好，';
        }
        $greetings = $greetings . $memberFullName;
        $logoUrl   = FileHelper::getFullUrl($companyInfo['logo_url']);
        $avatar    = BaseMember::findOneVal(['id' => $memberId], 'avatar');
        if ($avatar) {
            $avatarUrl = FileHelper::getFullUrl($avatar);
        } else {
            if ($companyInfo['logo_url']) {
                $avatarUrl = $logoUrl;
            } else {
                $avatarUrl = '/static/assets/avatar/company.png';
            }
        }

        $sonAccountNum = BaseCompanyMemberInfo::find()
                ->select(['id'])
                ->where([
                    'company_id' => $companyInfo['id'],
                ])
                ->count() - 1;

        $visitUrl = 'http://' . str_replace('http://', '', Yii::$app->params['pcHost']);

        return [
            'logoUrl'             => $logoUrl ?: '/static/assets/avatar/company.png',
            'fullName'            => $companyInfo['full_name'],
            'greetings'           => $greetings,
            'status'              => (int)$companyInfo['status'],
            'statusTitle'         => $statusTitle,
            'memberAction'        => $memberAction,
            'code'                => $code,
            'viewingRate'         => strlen($viewingRate) > 0 ? $viewingRate : '0%',
            'handleTime'          => strlen($handleTime) > 0 ? $handleTime : '0天',
            'deliveryType'        => (int)$companyInfo['delivery_type'],
            'packageType'         => (int)$companyInfo['package_type'],
            'packageTypeText'     => BaseCompany::PACKAGE_TYPE_LIST[$companyInfo['package_type']],
            'sonAccountNum'       => $sonAccountNum,
            'companyLink'         => $visitUrl . '/company/detail/' . $companyInfo['id'] . '.html',
            'contact'             => $companyInfo['contact'],
            'department'          => $companyInfo['department'],
            'companyMemberInfoId' => $companyInfo['company_member_info_id'],
            'memberRule'          => $companyInfo['member_rule'],
            'memberRuleName'      => BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_LIST_TAG[$companyInfo['member_rule']] . '登录',
            'company_member_type' => (int)$companyInfo['company_member_type'],
            'avatarUrl'           => $avatarUrl,
            'memberId'            => (string)$memberId,
            'companyId'           => $companyInfo['id'],
        ];
    }

    /**
     * 单位中心--获取消息列
     */
    public static function getNewsList($memberId): array
    {
        //        $select = [
        //            'id',
        //            'title',
        //        ];
        //        $where  = [
        //            'member_id' => $memberId,
        //            'type'      => BaseMemberMessage::TYPE_COMPANY_SYSTEM,
        //            'is_delete' => BaseMemberMessage::IS_DELETE_NO,
        //        ];
        //
        //        return BaseMemberMessage::find()
        //            ->where($where)
        //            ->select($select)
        //            ->orderBy('is_read asc,add_time desc')
        //            ->limit(5)
        //            ->asArray()
        //            ->all();

        //todo 新改成配置消息
        return BaseNews::COMPANY_NEWS_LIST;
    }

    /**
     * 单位中心--统计信息
     */
    public static function getStatisticsList($memberId): array
    {
        //增加一个缓存2小时
        $key  = Cache::COMPANY_STATISTICS_KEY . ':' . $memberId;
        $data = Cache::get($key);
        if ($data) {
            return json_decode($data, true);
        }
        //获取单位的信息
        $companyMemberInfo = BaseCompanyMemberInfo::findOne(['member_id' => $memberId]);
        if ($companyMemberInfo) {
            $jobApplyStatistics     = self::getCompanyJobApplyStatistics($memberId);
            $interviewStatistics    = self::getCompanyInterviewStatistics($memberId);
            $jobStatistics          = self::getCompanyJobStatistics($memberId);
            $announcementStatistics = self::getCompanyAnnouncementStatistics($memberId);
        } else {
            $jobApplyStatistics     = [];
            $interviewStatistics    = [];
            $jobStatistics          = [];
            $announcementStatistics = [];
        }

        $jobApplyAccount    = $jobApplyStatistics['allStatusAmount'] ?: 0;
        $applyCheckAccount  = $jobApplyStatistics['waitCheckAmount'] ?: 0;
        $applyHandleAccount = $jobApplyStatistics['waitStatusAmount'] ?: 0;

        $companyInterviewAccount = $interviewStatistics['interviewAmount'] ?: 0;
        $interviewWaitAccount    = $interviewStatistics['waitInterviewAmount'] ?: 0;
        $interviewedAccount      = $interviewStatistics['interviewedAmount'] ?: 0;

        $jobAccount       = $jobStatistics['allAmount'] ?: 0;
        $jobOnlineAccount = $jobStatistics['onlineAmount'] ?: 0;
        $jobExpireAccount = $jobStatistics['attentionAmount'] ?: 0;

        $announcementAccount       = $announcementStatistics['allAmount'] ?: 0;
        $announcementOnlineAccount = $announcementStatistics['onlineAmount'] ?: 0;
        $announcementExpireAccount = $announcementStatistics['attentionAmount'] ?: 0;

        $activityAccount = 0;
        $conductAccount  = 0;
        $finishAccount   = 0;

        $data = [
            [
                'fieldNo1'   => '我的简历',
                'accountNo1' => (int)$jobApplyAccount,
                'fieldNo2'   => '新简历',
                'accountNo2' => (int)$applyCheckAccount,
                'fieldNo3'   => '待处理简历',
                'accountNo3' => (int)$applyHandleAccount,
            ],
            [
                'fieldNo1'   => '面试邀约',
                'accountNo1' => (int)$companyInterviewAccount,
                'fieldNo2'   => '待面试',
                'accountNo2' => (int)$interviewWaitAccount,
                'fieldNo3'   => '已面试',
                'accountNo3' => (int)$interviewedAccount,
            ],
            [
                'fieldNo1'   => '我的职位',
                'accountNo1' => (int)$jobAccount,
                'fieldNo2'   => '在线职位',
                'accountNo2' => (int)$jobOnlineAccount,
                'fieldNo3'   => '7天内将下线',
                'accountNo3' => (int)$jobExpireAccount,
            ],
            [
                'fieldNo1'   => '公告&简章',
                'accountNo1' => (int)$announcementAccount,
                'fieldNo2'   => '在线公告',
                'accountNo2' => (int)$announcementOnlineAccount,
                'fieldNo3'   => '7天内将下线',
                'accountNo3' => (int)$announcementExpireAccount,
            ],
            [
                'fieldNo1'   => '我的活动',
                'accountNo1' => (int)$activityAccount,
                'fieldNo2'   => '进行中活动',
                'accountNo2' => (int)$conductAccount,
                'fieldNo3'   => '已结束活动',
                'accountNo3' => (int)$finishAccount,
            ],
        ];
        //缓存2小时
        Cache::set($key, json_encode($data), 7200);

        //返回值
        return $data;
    }

    /**
     * 单位中心--我的服务
     */
    public static function getMyService($memberId): array
    {
        $companyMemberInfo = BaseCompanyMemberInfo::findOne(['member_id' => $memberId]);
        if (!$companyMemberInfo) {
            $effectTime     = date('Y-m-d', strtotime(BaseCompany::findOneVal(['member_id' => $memberId], 'add_time')));
            $expireTime     = date('Y-m-d', (strtotime($effectTime) + 3600 * 24 * 180));
            $packageType    = BaseCompany::PACKAGE_TYPE_FREE;
            $expireDateTime = $expireTime;
        } else {

            $list = BaseCompanyPackageConfig::findOne(['company_id' => $companyMemberInfo->company_id]);
            if ($list) {
                $packageTypeInfo = BaseCompanyPackageSystemConfig::getPackageType($list->package_id);
                $packageType = $packageTypeInfo['packageType'];
                
                if ($list['expire_time'] && strtotime($list['expire_time']) < time()) {
                    $packageType = BaseCompany::PACKAGE_TYPE_OVER;
                }
                $effectTime = date('Y-m-d', strtotime($list['effect_time']));
                $expireTime = date('Y-m-d', strtotime($list['expire_time']));
            } else {
                $packageType = BaseCompany::PACKAGE_TYPE_FREE;
                $effectTime  = date('Y-m-d',
                    strtotime(BaseCompany::findOneVal(['member_id' => $memberId], 'add_time')));
                $expireTime  = date('Y-m-d', (strtotime($effectTime) + 3600 * 24 * 180));
            }
            $expireDateTime = $list->expire_time;
        }

        // 这里拿一些单位的配置
        $companyPackageConfig = BaseCompanyPackageConfig::getCompanyPackageConfig($companyMemberInfo->company_id);

        // 文案
        $timeDay = '';
        if ($packageType != BaseCompany::PACKAGE_TYPE_OVER) {
            //不是过期会员
            //计算剩余天数
            $expireTimeNumber = strtotime($expireDateTime) - time();
            $expireTimeDay    = $expireTimeNumber > 0 ? $expireTimeNumber / 86400 : 0;
            if ($expireTimeDay > 0) {
                $timeDay = $expireTimeDay > 1 ? floor($expireTimeDay) . '天' : '<1天';
            }
        }

        return [
            'package_type'           => $packageType,
            'package_type_name'      => BaseCompany::PC_PACKAGE_TYPE_LIST[$packageType],
            'effect_time'            => $effectTime,
            'expire_time'            => $expireTime,
            'timeDay'                => $timeDay,
            'chat_amount'            => $companyPackageConfig['chat_amount'] ?: 0,
            'resume_download_amount' => $companyPackageConfig['resume_download_amount'] ?: 0,
        ];
    }

    /**
     * 单位中心--专属客服
     */
    public static function customerService(): array
    {
        // TODO: 专属客服数据暂时虚拟
        return [
            'is_pay'     => 1,
            'logoUrl'    => 'https://img.gaoxiaojob.com/uploads/static/image/logo/logo_5.png',
            'memberName' => '高小才',
            'qq'         => '2881224205',
            'wechat'     => '1235666',
            'wechatUrl'  => 'https://img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/20.png',
            'telephone'  => '15920573323',
            'fixedLine'  => '020-85611139',
            'hotLine'    => '020-85611139',
        ];
    }

    /**
     * 单位中心--我的日程
     */
    public static function getMySchedule($request, $memberId): array
    {
        $data                = [];
        $groupBy             = ' date(`begin_time`)';
        $myScheduleGroupList = BaseMemberSchedule::getMemberSchedule($request, $memberId, $groupBy);
        $myScheduleList      = BaseMemberSchedule::getMemberSchedule($request, $memberId);

        foreach ($myScheduleGroupList as $key => $list) {
            $schedule = [];
            $account  = 0;
            foreach ($myScheduleList as $k => $scheduleList) {
                if ($scheduleList['begin_date'] == $list['begin_date']) {
                    $beginTimeSuffix = date('H', strtotime($scheduleList['begin_time'])) < 12 ? 'AM' : 'PM';
                    $endTimeSuffix   = date('H', strtotime($scheduleList['end_time'])) < 12 ? 'AM' : 'PM';
                    $beginTime       = date('h:i', strtotime($scheduleList['begin_time'])) . $beginTimeSuffix;
                    $endTime         = date('h:i', strtotime($scheduleList['end_time'])) . $endTimeSuffix;

                    if (strtotime($scheduleList['end_time']) < 1) {
                        $timeRange = $beginTime;
                    } else {
                        $timeRange = $beginTime . ' - ' . $endTime;
                    }

                    if ($scheduleList['type'] == BaseMemberSchedule::TYPE_COMPANY_INTERVIEW) {
                        $jobApplyInfo = BaseJobApply::find()
                            ->select([
                                'resume_name',
                                'job_name',
                                'id',
                            ])
                            ->where(['id' => $scheduleList['main_id']])
                            ->asArray()
                            ->one();
                        $resumeName   = $jobApplyInfo['resume_name'];
                        $jobName      = $jobApplyInfo['job_name'];
                        $jobApplyId   = $jobApplyInfo['id'];
                    } else {
                        $resumeName = '';
                        $jobName    = '';
                        $jobApplyId = '';
                    }

                    $schedule[] = [
                        'timeRange'  => $timeRange,
                        'type'       => (int)$scheduleList['type'],
                        'resumeName' => $resumeName,
                        'title'      => $scheduleList['title'],
                        'id'         => $scheduleList['id'],
                        'jobName'    => $jobName,
                        'jobApplyId' => $jobApplyId,
                    ];
                    $account++;
                }
            }
            $data[$key]['date']     = $list['begin_date'];
            $data[$key]['account']  = $account;
            $data[$key]['schedule'] = $schedule;
        }

        return $data;
    }

    /**
     * 单位中心--获取日程信息
     */
    public static function getMemberSchedule($request): array
    {
        return BaseMemberSchedule::getScheduleDetails($request['id']);
    }

    /**
     * 单位中心--删除日程
     * @throws Exception
     */
    public static function deleteMemberSchedule($request)
    {
        try {
            $where = ['id' => $request['id']];
            if (!BaseMemberSchedule::deleteAll($where)) {
                throw new Exception('失败');
            }
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 套餐明细
     */
    public static function packageDetail(): array
    {
        $memberId          = Yii::$app->user->id;
        $companyMemberInfo = BaseCompanyMemberInfo::findOne(['member_id' => $memberId]);
        $list              = BaseCompanyPackageConfig::findOne(['company_id' => $companyMemberInfo->company_id]);
        $isPool            = BaseCompanyPackageConfig::IS_POOL_NO;
        if (!$list) {
            $companyId   = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'company_id');
            $effectTime  = BaseCompany::findOneVal(['id' => $companyId], 'add_time');
            $expireTime  = date('Y-m-d h-i-s', (strtotime($effectTime) + 3600 * 24 * 180));
            $packageType = BaseCompany::PACKAGE_TYPE_FREE;
        } else {
            $packageTypeInfo = BaseCompanyPackageSystemConfig::getPackageType($list->package_id);
            $packageType = $packageTypeInfo['packageType'];
            if ($list->code == 'B') {
                $isPool      = BaseCompanyPackageConfig::IS_POOL_YES;
            }

            $expireTime = $list['expire_time'];
            $effectTime = $list['effect_time'];
        }

        //todo 这里取单位权益详情 剩余以及当次配置的总数
        $loopList = BaseCompanyPackageChangeLog::RELEVANT_TYPE_LIST;
        if (strtotime($expireTime) < time()) {
            $packageType = BaseCompany::PACKAGE_TYPE_OVER;
            foreach ($loopList as &$item) {
                $item['surplus'] = 0;
                $item['total']   = 0;
            }
        } else {
            $systemConfig = BaseCompanyPackageSystemConfig::findOne(['code' => $list['code']]);
            foreach ($loopList as &$item) {
                $name            = $item['field'];
                $item['surplus'] = $list[$name];
                if ($item['field'] == 'resume_download_amount' || $item['field'] == 'chat_amount' || $item['field'] == 'sms_amount') {
                    $item['total'] = $list['cycle_' . $item['field']] ?: $list['package_amount'] * $systemConfig[$name] + $systemConfig['base_' . $name];
                } else {
                    $item['total'] = $list['package_amount'] * $systemConfig[$name] + $systemConfig['base_' . $name];
                }
            }
        }

        $effectTime = explode(' ', $effectTime)[0];
        $expireTime = explode(' ', $expireTime)[0];


        return [
            'package_type'           => $packageType,
            'package_type_name'      => BaseCompany::PACKAGE_TYPE_LIST[$packageType],
            'expire_time'            => $expireTime,
            'effect_time'            => $effectTime,
            'is_pool'                => $isPool,
            'list'                   => $loopList,
            'member_rule'            => $companyMemberInfo->member_rule,
            'company_member_type'    => $companyMemberInfo->company_member_type,
        ];
    }

    /**
     * 获取用户短信邀约方式的操作记录 V2.3.2
     * @param $memberId
     * @param $companyId
     * @return array
     */
    public static function getCompanyIsRememberSms($memberId): array
    {
        $companyMemberInfo = BaseCompanyMemberInfo::findOne(['member_id' => $memberId]);
        return [
            'is_remember_sms_chat'   => $companyMemberInfo->is_remember_sms_chat,
            'is_remember_sms_invite' => $companyMemberInfo->is_remember_sms_invite,
        ];
    }
    
    /**
     * 权益明细列表
     * @param $keywords
     * @return array
     * @throws Exception
     */
    public static function getCompanyPackageChangeLog($keywords): array
    {
        $select = [
            'l.id',
            'l.add_time',
            'l.type',
            'l.identify',
            'l.change_amount',
            'l.surplus',
            'l.package_surplus',
            'l.member_id',
            'l.member_name',
            'l.company_id',
            'l.company_name',
            'l.handle_before',
            'l.handle_after',
            'l.handler_type',
            'l.handler',
            'l.handler_id',
            'l.content',
            'l.remark',
            'l.handle_type',
        ];

        $query = BaseCompanyPackageChangeLog::find()
            ->alias('l')
            ->select($select);

        $memberId      = Yii::$app->user->id;
        $authorityList = (new CompanyAuthorityClassify())->run([
            'associatedField' => 'l.company_id',
            'memberId'        => $memberId,
            'query'           => $query,
            'returnType'      => CompanyAuthorityClassify::DATA_MEMBER,
            'handleField'     => 'l.handler_id',
        ]);
        if ($authorityList) {
            $query = $authorityList['query'];
        }

        $query->andFilterCompare('handler_id', $keywords['handler_id']);
        //变更前跟变更后的数量，至少有一个是不为0的，即都为0的情况下不展示出来
        $query->andWhere([
            'or',
            [
                '<>',
                'handle_before',
                0,
            ],
            [
                '<>',
                'handle_after',
                0,
            ],
        ]);

        $query->andWhere([
            'not in',
            'l.handle_type',
            [
                BaseCompanyPackageChangeLog::HANDLE_TYPE_CONFIGURE_SUB,
            ],
        ]);

        if ($keywords['add_time_start']) {
            $query->andWhere([
                '>=',
                'add_time',
                TimeHelper::dayToBeginTime($keywords['add_time_start']),
            ]);
        }
        if ($keywords['add_time_end']) {
            $query->andWhere([
                '<=',
                'add_time',
                TimeHelper::dayToEndTime($keywords['add_time_end']),
            ]);
        }

        $query->andFilterCompare('type', $keywords['type']);
        $query->andFilterCompare('handle_type', $keywords['handle_type']);

        $orderBy = 'id desc';

        $count    = $query->count();
        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        foreach ($list as $k => $v) {
            $list[$k]['company_uid']       = UUIDHelper::encrypt(UUIDHelper::TYPE_COMPANY, $v['company_id']);
            $list[$k]['typeTitle']         = BaseCompanyPackageChangeLog::TYPE_NAME[$v['type']];
            $list[$k]['identifyTitle']     = $v['identify'] == 1 ? '增加' : '减少';
            $list[$k]['package_surplus']   = json_decode($v['package_surplus'], true);
            $list[$k]['handle_type_title'] = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[$v['handle_type']];

            if ($v['type'] > 4) {
                $list[$k]['surplus'] = json_decode($v['package_surplus'], true);
            }
            if ($v['change_amount'] > 0) {
                if ($v['identify'] == 1) {
                    $list[$k]['change_amount'] = "+" . $v['change_amount'];
                } else {
                    $list[$k]['change_amount'] = "-" . $v['change_amount'];
                }
            }
            if ($v['handler_type'] == BaseCompanyPackageChangeLog::HANDLER_TYPE_PLATFORM || $v['handler'] == '管理员' || in_array($v['handle_type'],
                    BaseCompanyPackageChangeLog::PLATFORM_HANDLE_TYPE)) {
                $list[$k]['handler'] = '--';
                $list[$k]['remark']  = '--';
            } else {
                if (!in_array($v['handle_type'], BaseCompanyPackageChangeLog::PLATFORM_HANDLE_TYPE)) {
                    $companyMemberInfo = BaseCompanyMemberInfo::findOne(['member_id' => $v['handler_id']]);
                    if ($companyMemberInfo) {
                        $list[$k]['handler'] = $companyMemberInfo['contact'] . '(' . $companyMemberInfo['department'] . ')';
                        if ($companyMemberInfo['department']) {
                            $list[$k]['handler'] = $companyMemberInfo['contact'] . '(' . $companyMemberInfo['department'] . ')';
                        } else {
                            $list[$k]['handler'] = $companyMemberInfo['contact'];
                        }
                    }
                }
            }

            // 过滤掉不需要再单位显示的备注
            if (in_array($v['type'], BaseCompanyPackageChangeLog::HIDE_IN_COMPANY_REMARK_TYPE)) {
                $list[$k]['remark'] = '--';
            }

            if ($v['type'] == BaseCompanyPackageChangeLog::TYPE_SMS_SEND) {
                $list[$k]['remark'] = (mb_strlen($list[$k]['remark'])) > 4 ? mb_substr($list[$k]['remark'], 0, 4) : '--';
            }
        }

        return [
            'list' => $list,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
        ];
    }

    /**
     *  意见建议反馈
     * @throws Exception
     */
    public static function memberSuggestion($request, $memberId)
    {
        $baseMember = BaseMember::find()
            ->where(['id' => $memberId])
            ->one();
        $memberType = $baseMember['type'] == BaseMember::TYPE_PERSON ? 1 : 2;
        $data       = [
            'add_time'    => CUR_DATETIME,
            'member_type' => $memberType,
            'member_id'   => (int)$memberId,
            'member_name' => $baseMember['username'],
            'content'     => $request['content'],
        ];
        BaseMemberSuggest::createMemberSuggest($data);
    }

    /**
     * 单位简历统计信息
     * 简历总数、待处理、通过初筛、面试邀约、不合适、已录用
     */
    public static function getCompanyJobApplyStatistics($memberId)
    {
        $query = BaseJobApply::find()
            ->alias('a')
            ->leftJoin(['j' => BaseJob::tableName()], 'j.id=a.job_id')
            ->select([
                'count(a.id) as allStatusAmount',
                'count(CASE a.status WHEN 1 THEN 1 ElSE null END) as waitStatusAmount',
                'count(CASE WHEN a.is_check != 1 THEN 1 ELSE null END) as waitCheckAmount',
            ])
            ->where([
                'j.status' => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
                //'a.company_member_id' => $memberId,
                // 'j.is_show'           => BaseJob::IS_SHOW_YES,
            ]);

        $authorityList = (new CompanyAuthorityClassify())->run([
            'associatedField' => 'a.company_id',
            'memberId'        => $memberId,
            'query'           => $query,
            'returnType'      => CompanyAuthorityClassify::DATA_JOB_COOPERATE,
        ]);
        if ($authorityList) {
            $query = $authorityList['query'];
        }

        return $query->asArray()
            ->one();
    }

    /**
     * 单位面试邀约统计信息
     * 面试邀约、待面试
     */
    public static function getCompanyInterviewStatistics($memberId)
    {
        $nowTime = CUR_DATETIME;

        $query = BaseJobApply::find()
            ->alias('a')
            ->leftJoin(['j' => BaseJob::tableName()], 'j.id=a.job_id')
            ->leftJoin(['i' => BaseCompanyInterview::tableName()], 'i.job_apply_id=a.id')
            ->select([
                'i.id',
                'i.interview_time',
            ])
            ->where([
                'j.status'        => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
                //'a.company_member_id' => $memberId,
                // 'j.is_show'           => BaseJob::IS_SHOW_YES,
                'a.is_invitation' => BaseJobApply::IS_INVITATION_YES,
            ])
            ->groupBy('i.job_apply_id');

        $authorityList = (new CompanyAuthorityClassify())->run([
            'associatedField' => 'a.company_id',
            'memberId'        => $memberId,
            'query'           => $query,
            'returnType'      => CompanyAuthorityClassify::DATA_JOB_COOPERATE,
        ]);
        if ($authorityList) {
            $query = $authorityList['query'];
        }

        $list = $query->asArray()
            ->all();

        $interviewAmount     = 0;
        $interviewedAmount   = 0;
        $waitInterviewAmount = 0;
        foreach ($list as $item) {
            $interviewAmount++;
            if ($item['interview_time'] < $nowTime) {
                $interviewedAmount++;
            } else {
                $waitInterviewAmount++;
            }
        }

        return [
            'interviewAmount'     => $interviewAmount,
            'interviewedAmount'   => $interviewedAmount,
            'waitInterviewAmount' => $waitInterviewAmount,
        ];
    }

    /**
     * 单位职位统计信息
     * 职位总数、在线职位、将过期职位
     */
    public static function getCompanyJobStatistics($memberId): array
    {
        $query = BaseJob::find()
            ->alias('j')
            ->select([
                'j.id',
                "j.status",
                "j.period_date",
            ])
            ->where([
                //'j.member_id' => $memberId,
                'j.is_show' => BaseJob::IS_SHOW_YES,
            ])
            ->andWhere([
                '<>',
                'j.status',
                BaseJob::STATUS_DELETE,
            ])
            ->andWhere([
                '<>',
                'j.is_article',
                BaseJob::IS_ARTICLE_WAIT,
            ])
            ->andWhere([
                'or',
                [
                    'j.create_type' => BaseJob::CREATE_TYPE_SELF,
                ],
                [
                    'and',
                    'j.create_type' => BaseJob::CREATE_TYPE_AGENT,
                    [
                        '<>',
                        'j.first_release_time',
                        TimeHelper::ZERO_TIME,
                    ],
                ],
            ]);

        $authorityList = (new CompanyAuthorityClassify())->run([
            'associatedField' => 'j.company_id',
            'memberId'        => $memberId,
            'query'           => $query,
            'returnType'      => CompanyAuthorityClassify::DATA_JOB_COOPERATE,
        ]);
        if ($authorityList) {
            $query = $authorityList['query'];
        }
        $list = $query->asArray()
            ->all();

        $allAmount       = 0;
        $onlineAmount    = 0;
        $attentionAmount = 0;
        $sevenTime       = date('Y-m-d H:i:s', strtotime('+7 day'));
        foreach ($list as $v) {
            $allAmount++;
            if ($v['status'] == BaseJob::STATUS_ONLINE) {
                $onlineAmount++;
                if ($v['period_date'] > CUR_DATETIME && $v['period_date'] < $sevenTime) {
                    $attentionAmount++;
                }
            }
        }

        return [
            'allAmount'       => $allAmount,
            'onlineAmount'    => $onlineAmount,
            'attentionAmount' => $attentionAmount,
        ];
    }

    /**
     * 单位公告统计信息
     * 职位总数、在线职位、将过期职位
     */
    public static function getCompanyAnnouncementStatistics($memberId)
    {
        $statusList = [
            BaseAnnouncement::STATUS_AUDIT_STAGING,
            BaseAnnouncement::STATUS_AUDIT_AWAIT,
            BaseAnnouncement::STATUS_AUDIT_REFUSE,
        ];
        $status     = [
            BaseArticle::STATUS_ONLINE,
            BaseArticle::STATUS_OFFLINE,
        ];
        $query      = BaseAnnouncement::find()
            ->alias('a')
            ->leftJoin(['art' => BaseArticle::tableName()], 'art.id = a.article_id')
            ->leftJoin(['j' => BaseJob::tableName()], 'j.announcement_id = a.id')
            ->select([
                'a.id',
                'a.period_date',
                'art.status',
            ])
            ->where([
                //'member_id'   => $memberId,
                'art.is_show' => BaseArticle::IS_SHOW_YES,
                //'a.member_id' => $memberId,
                'art.type'    => BaseArticle::TYPE_ANNOUNCEMENT,
            ])
            ->andWhere([
                '<>',
                'art.is_delete',
                BaseArticle::IS_DELETE_YES,
            ])
            ->andWhere([
                'or',
                [
                    'and',
                    [
                        'in',
                        'art.status',
                        $status,
                    ],
                    [
                        '<>',
                        'art.first_release_time',
                        TimeHelper::ZERO_TIME,
                    ],
                ],
                [
                    'and',
                    [
                        'in',
                        'a.audit_status',
                        $statusList,
                    ],
                    [
                        '=',
                        'a.create_type',
                        BaseAnnouncement::CREATE_TYPE_COMPANY,
                    ],
                ],
            ])
            ->groupBy('a.id');

        $authorityList = (new CompanyAuthorityClassify())->run([
            'associatedField' => 'a.company_id',
            'memberId'        => $memberId,
            'query'           => $query,
            'returnType'      => CompanyAuthorityClassify::DATA_JOB_COOPERATE,
        ]);
        if ($authorityList) {
            $query = $authorityList['query'];
        }
        $list = $query->asArray()
            ->all();

        $allAmount       = 0;
        $onlineAmount    = 0;
        $attentionAmount = 0;
        $nowTime         = CUR_DATETIME;
        $sevenTime       = date('Y-m-d H:i:s', strtotime('+7 day'));
        foreach ($list as $v) {
            $allAmount++;
            if ($v['status'] == BaseArticle::STATUS_ONLINE) {
                $onlineAmount++;
                if ($v['period_date'] > $nowTime && $v['period_date'] < $sevenTime) {
                    $attentionAmount++;
                }
            }
        }

        return [
            'allAmount'       => $allAmount,
            'onlineAmount'    => $onlineAmount,
            'attentionAmount' => $attentionAmount,
        ];
    }

    /**
     * 单位中心--广告
     */
    public static function getShowcase(): array
    {
        return [
            [
                'image_url'   => 'https://img.gaoxiaojob.com/uploads/static/image/company/home/<USER>',
                'target_link' => 'https://boshihou.gaoxiaojob.com',
            ],
            [
                //                'image_url'   => '/static/assets/company/companyShowcase002.jpg',
                'image_url'   => 'https://img.gaoxiaojob.com/uploads/static/image/company/home/<USER>',
                'target_link' => 'https://haiwai.gaoxiaojob.com',
            ],
        ];
    }

}