<link rel="stylesheet" href="/static/css/company2025.css?v=0.2">

<div id="component">
    <div class="el-main">
        <div class="company-container">
            <div class="search-container">
                <div class="search-content">
                    <!-- 由后端渲染 -->
                    <!-- 推荐单位轮播骨架屏 -->
                    <?= frontendPc\components\T1Widget::widget() ?>
<!--                    <div class="main-header-showcase-skeleton" v-if="recommendLoading">-->
<!--                        <div class="showcase-skeleton-container">-->
<!--                            <div class="showcase-skeleton-item" v-for="n in 7" :key="n"></div>-->
<!--                        </div>-->
<!--                    </div>-->

<!--                    &lt;!&ndash; 推荐单位轮播 &ndash;&gt;-->
<!--                    <div class="main-header-showcase__container" v-if="!recommendLoading && recommendData.length > 0">-->
<!--                        &lt;!&ndash; 7 个 showcase-browse 为一组 carousel-item 。如果只有一组：indicator-position=“none”；否则 indicator-position=“outside” &ndash;&gt;-->
<!--                        <el-carousel height="70px" arrow="never" trigger="click" :indicator-position="recommendData.length > 7 ? 'outside' : 'none'" :autoplay="true" :interval="5000">-->
<!--                            <el-carousel-item v-for="(chunk, index) in getRecommendChunks()" :key="index">-->
<!--                                <a v-for="item in chunk" :key="item.id" class="showcase-browse" :href="item.link" target="_blank" :data-showcase-id="item.id" :title="item.title">-->
<!--                                    <img :src="item.src" :alt="item.title" />-->
<!--                                </a>-->
<!--                            </el-carousel-item>-->
<!--                        </el-carousel>-->
<!--                    </div>-->

                    <div class="search-main">
                        <div class="el-input el-input-group el-input--prefix el-input-group--append">
                            <input v-model.trim="keyword" class="search-input el-input__inner" type="text" autocomplete="off" placeholder="请输入搜索关键词" @keydown.enter="handleSearch" />

                            <span class="search-options el-input__prefix">
                                        <span class="el-input__suffix-inner">
                                            <el-cascader
                                                    ref="industryCascader"
                                                    class="search-type"
                                                    v-model="industryId"
                                                    :options="industryOptions"
                                                    placeholder="行业类别"
                                                    :clearable="true"
                                                    :show-all-levels="false"
                                                    :props="{
                                                    checkStrictly: true,
                                                    emitPath: false,
                                                    expandTrigger: 'hover',
                                                    label: 'label',
                                                    value: 'value',
                                                    children: 'children'
                                                }"
                                                    @change="handleIndustryChange"
                                            />
                                        </span>
                                    </span>

                            <div class="el-input-group__append">
                                <button class="search-button el-button el-button--primary" @click="handleSearch"></button>
                            </div>
                        </div>

                        <!-- 热门搜索骨架屏 -->
                        <div class="search-hot-skeleton" v-if="configLoading">
                            <div class="skeleton-text skeleton-text-label"></div>
                            <div class="skeleton-hot-items">
                                <div class="skeleton-hot-item" v-for="n in 6" :key="n"></div>
                            </div>
                        </div>

                        <!-- 热门搜索实际内容 -->
                        <div class="search-hot" v-if="!configLoading && hotSearchList.length" v-cloak>
                            热门搜索：
                            <a v-for="item in hotSearchList" :key="item.id" @click="handleHotSearchClick(item.text)" style="cursor: pointer"> {{ item.text }} </a>
                        </div>
                    </div>

                    <!-- <div class="swiper mySwiper recommend-company" v-cloak>
                        <div class="swiper-wrapper company-pane">
                            <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[0]">
                                <el-carousel :height="paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[0]">
                                    <el-carousel-item>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    </el-carousel-item>
                                    <el-carousel-item>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    </el-carousel-item>
                                    <el-carousel-item>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    </el-carousel-item>
                                </el-carousel>
                            </div>
                            <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[1]">
                                <el-carousel :height=" paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[1]">
                                    <el-carousel-item>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                        <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                        <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    </el-carousel-item>
                                    <el-carousel-item>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    </el-carousel-item>
                                </el-carousel>
                            </div>
                            <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[2]">
                                <el-carousel :height=" paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[2]">
                                    <el-carousel-item>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                        <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                    </el-carousel-item>
                                </el-carousel>
                            </div>
                            <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[3]">
                                <el-carousel :height=" paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[3]">
                                    <el-carousel-item>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                        <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                    </el-carousel-item>
                                </el-carousel>
                            </div>
                            <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[4]">
                                <el-carousel :height=" paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[4]">
                                    <el-carousel-item>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                        <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                    </el-carousel-item>
                                </el-carousel>
                            </div>
                            <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[5]">
                                <el-carousel :height=" paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[5]">
                                    <el-carousel-item>
                                        <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                        <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                        <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                    </el-carousel-item>
                                </el-carousel>
                            </div>
                        </div>

                        <div class="recommend-tabs">
                            <div class="label"><span>HOT</span>推荐单位：</div>
                            <div class="swiper-pagination tabs" @click="handleRecommend"></div>
                        </div>
                    </div> -->
                </div>
            </div>


            <div class="company-content">
                <!-- 筛选区域骨架屏 -->
                <div class="search-filter-skeleton" v-if="configLoading">
                    <div class="filter-skeleton-row" v-for="n in 4" :key="n">
                        <div class="skeleton-text skeleton-filter-label"></div>
                        <div class="skeleton-filter-tags">
                            <div class="skeleton-filter-tag" v-for="m in 6" :key="m"></div>
                        </div>
                    </div>
                </div>

                <!-- 筛选区域 -->
                <div class="search-filter" v-if="!configLoading && hasConfigData" v-cloak>
                    <div class="filter-pane area special is-show">
                        <h6 class="filter-label">工作地点</h6>
                        <div class="filter-value">
                            <span class="el-tag" :class="{ active: areaId.length === 0 }" @click="handleAreaFilter('')" style="cursor: pointer"> 全部 </span>
                            <span
                                    v-for="area in areaOptions"
                                    :key="area.value"
                                    class="el-tag"
                                    :class="{ active: areaId.includes(area.value) }"
                                    @click="handleAreaFilter(area.value)"
                                    style="cursor: pointer"
                            >
                                        {{ area.label }}
                                        <i v-if="areaId.includes(area.value)" class="el-icon el-icon-close el-tag__close" @click.stop="handleAreaFilter(area.value)"></i>
                                    </span>
                        </div>
                        <span class="filter-more is-reverse" @click="handleShow">更多</span>
                    </div>

                    <div class="filter-pane nature special">
                        <h6 class="filter-label">单位性质</h6>
                        <div class="filter-value">
                            <span class="el-tag" :class="{ active: companyNature.length === 0 }" @click="handleNatureFilter('')" style="cursor: pointer"> 全部 </span>
                            <span
                                    v-for="nature in natureOptions"
                                    :key="nature.value"
                                    class="el-tag"
                                    :class="{ active: companyNature.includes(nature.value) }"
                                    @click="handleNatureFilter(nature.value)"
                                    style="cursor: pointer"
                            >
                                        {{ nature.label }}
                                        <i v-if="companyNature.includes(nature.value)" class="el-icon el-icon-close el-tag__close" @click.stop="handleNatureFilter(nature.value)"></i>
                                    </span>
                        </div>
                    </div>

                    <div class="filter-pane type special is-show">
                        <h6 class="filter-label">单位类型</h6>
                        <div class="filter-value">
                            <span class="el-tag" :class="{ active: companyType.length === 0 }" @click="handleTypeFilter('')" style="cursor: pointer"> 全部 </span>
                            <span
                                    v-for="type in typeOptions"
                                    :key="type.value"
                                    class="el-tag"
                                    :class="{ active: companyType.includes(type.value) }"
                                    @click="handleTypeFilter(type.value)"
                                    style="cursor: pointer"
                            >
                                        {{ type.label }}
                                        <i v-if="companyType.includes(type.value)" class="el-icon el-icon-close el-tag__close" @click.stop="handleTypeFilter(type.value)"></i>
                                    </span>
                        </div>
                        <span class="filter-more is-reverse" @click="handleShow">更多</span>
                    </div>

                    <div class="filter-pane weflare special">
                        <h6 class="filter-label">职位福利</h6>
                        <div class="filter-value">
                            <span class="el-tag" :class="{ active: welfareType.length === 0 }" @click="handleWelfareFilter('')" style="cursor: pointer"> 全部 </span>
                            <span
                                    v-for="welfare in welfareOptions"
                                    :key="welfare.value"
                                    class="el-tag"
                                    :class="{ active: welfareType.includes(welfare.value) }"
                                    @click="handleWelfareFilter(welfare.value)"
                                    style="cursor: pointer"
                            >
                                        {{ welfare.label }}
                                        <i v-if="welfareType.includes(welfare.value)" class="el-icon el-icon-close el-tag__close" @click.stop="handleWelfareFilter(welfare.value)"></i>
                                    </span>
                        </div>
                        <span class="filter-clear" @click="handleClear">清空筛选条件</span>
                    </div>

                    <div class="filter-pane filter-pane-more">
                        <h6 class="filter-label">更多筛选</h6>
                        <div class="filter-value">
                            <el-select v-model="companyScaleType" placeholder="单位规模" clearable="true" @change="(val) => handleFilter(val, 'companyScaleType')">
                                <el-option v-for="{label, value} in scaleOptions" :key="value" :label="label" :value="value"> </el-option>
                            </el-select>
                        </div>
                    </div>
                </div>

                <!-- 搜索结果区域骨架屏 -->
                <div class="search-result-skeleton" v-if="companyListLoading">
                    <div class="result-header-skeleton">
                        <div class="skeleton-text skeleton-result-title"></div>
                    </div>
                    <div class="result-list-skeleton">
                        <div class="result-item-skeleton" v-for="n in 8" :key="n">
                            <div class="company-data-skeleton">
                                <div class="skeleton-logo"></div>
                                <div class="company-info-skeleton">
                                    <div class="skeleton-text skeleton-company-name"></div>
                                    <div class="skeleton-text skeleton-company-nature"></div>
                                </div>
                            </div>
                            <div class="company-stats-skeleton">
                                <div class="stat-item-skeleton" v-for="m in 3" :key="m">
                                    <div class="skeleton-text skeleton-stat-number"></div>
                                    <div class="skeleton-text skeleton-stat-label"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 搜索结果区域 -->
                <div id="company-list-top" class="search-result" v-if="!companyListLoading" v-cloak>
                    <div class="result-header">
                        <h4 class="result-title">单位列表</h4>

                        <!-- <div class="result-sort">
                            <span :class="sort === 'default' ? 'active' : ''" @click="handleSort('default')">综合排序</span>
                            <span :class="sort === 'update' ? 'active' : ''" @click="handleSort('update')">更新时间</span>
                        </div> -->
                    </div>

                    <div class="result-list" v-loading="loading">
                        <div class="result-item" v-for="company in companyList" :key="company.companyId">
                            <a :href="company.url" target="_blank">
                                <div class="company-data">
                                    <img class="logo" :src="company.logoUrl" :alt="company.name" />

                                    <div class="data">
                                        <h5>{{ company.name }}</h5>
                                        <span class="nature">{{ company.nature }}</span>
                                    </div>
                                </div>

                                <div class="company-info">
                                            <span>
                                                <strong>{{ company.announcementAmount }}</strong>
                                                招聘公告
                                            </span>
                                    <span>
                                                <strong>{{ company.jobAmount }}</strong>
                                                招聘职位
                                            </span>
                                    <span>
                                                <strong>{{ company.lastLoginTime }}</strong>
                                                活跃时间
                                            </span>
                                </div>
                            </a>
                            <div class="collect-button" :class="{ 'is-collected': company.isCollect==1 }" :data-id="company.companyId" @click="handleCollectClick(company.companyId)">
                                <span>{{ company.isCollect==1 ? '已收藏' : '收藏' }}</span>
                            </div>
                        </div>

                        <!-- 空状态 -->
                        <div v-if="!companyListLoading && !loading && companyList.length === 0" class="empty-state">
                            <p>暂无相关单位信息</p>
                        </div>
                    </div>

                    <!-- 分页骨架屏 -->
                    <div class="pagination-skeleton" v-if="companyListLoading">
                        <div class="skeleton-pagination-item skeleton-pagination-prev"></div>
                        <div class="skeleton-pagination-item skeleton-pagination-number" v-for="n in 5" :key="n"></div>
                        <div class="skeleton-pagination-item skeleton-pagination-next"></div>
                    </div>

                    <el-pagination
                            v-if="!companyListLoading && companyList.length > 0"
                            background
                            layout="prev, pager, next"
                            v-model:current-page="currentPage"
                            :page-size="20"
                            :total="totalNum"
                            @current-change="handleCurrentPageChange"
                    >
                    </el-pagination>
                </div>
            </div>
        </div>
    </div>
</div>


<script>
    $(function () {
        const component = {
            data() {
                return {
                    // 搜索相关
                    keyword: '',
                    industryId: '',
                    industryOptions: [],
                    companyScaleType: '',
                    scaleOptions: [], // 将从配置接口获取真实的单位规模ID和名称
                    isCollect: '', // 收藏筛选：1=已收藏，2=未收藏，''=全部

                    // 筛选相关（参数名与jobList保持一致）
                    areaId: [],
                    companyNature: [],
                    companyType: [],
                    welfareType: [],

                    // 推荐数据
                    recommendData: [],

                    // 筛选选项（从接口获取）
                    areaOptions: [], // 将从配置接口获取真实的地区ID和名称

                    // 热门搜索数据
                    hotSearchList: [],
                    // 配置选项（从接口获取）
                    natureOptions: [], // 将从配置接口获取真实的单位性质ID和名称
                    typeOptions: [], // 将从配置接口获取真实的单位类型ID和名称
                    welfareOptions: [], // 将从配置接口获取真实的福利类型ID和名称

                    // 排序和分页
                    sort: 'default',
                    currentPage: 1,
                    totalNum: 0,

                    // 数据状态
                    companyList: [],
                    recommendLoading: true, // 推荐数据加载状态
                    configLoading: true, // 配置数据加载状态
                    companyListLoading: true, // 公司列表加载状态
                    loading: false, // 列表内容loading状态（用于切换筛选条件时的loading效果）

                    // 用户状态
                    isLogin: false,
                    isVip: false,

                    // 筛选条件限制
                    maxFilterPerCategory: 5, // 每个类别最多选择5个
                    maxFilterTotal: 10, // 总共最多选择10个

                    // 轮播相关（保持原有功能）
                    paneHeight: '70px',
                    runTime: 3000,
                    running: [true, false, false, false, false, false],
                    delayTime: []
                }
            },

            computed: {
                // 计算当前已选择的筛选条件总数
                totalSelectedFilters() {
                    return this.areaId.length + this.companyNature.length + this.companyType.length + this.welfareType.length
                },

                // 检查是否有配置数据
                hasConfigData() {
                    return this.areaOptions.length > 0 || this.natureOptions.length > 0 || this.typeOptions.length > 0 || this.welfareOptions.length > 0 || this.scaleOptions.length > 0
                }
            },

            async mounted() {
                // 初始化参数
                this.initParams()

                // 获取用户信息（并行执行，不阻塞其他数据加载）
                this.getUserInfo()

                // 按照优化的顺序加载数据
                await this.loadDataInSequence()
            },

            methods: {
                // 按顺序加载数据，优化用户体验
                async loadDataInSequence() {
                    try {
                        // 第一步：加载推荐单位轮播数据（广告位）
                        // console.log('开始加载推荐数据...')
                        // await this.getRecommendData()
                        console.log('推荐数据加载完成')
                        // 第二步：加载配置数据（包含筛选参数选项和热词数据）
                        console.log('开始加载配置数据...')
                        await this.getConfigData()
                        console.log('配置数据加载完成')

                        // 第三步：加载公司列表数据（搜索结果）
                        console.log('开始加载公司列表...')
                        await this.getCompanyList()
                        console.log('公司列表加载完成')

                        // 初始化轮播（在推荐数据加载完成后）
                        this.initCarousel()
                    } catch (error) {
                        console.error('数据加载过程中出现错误:', error)
                        // 即使出错也要确保基本功能可用
                        this.configLoading = false
                        this.companyListLoading = false
                    }
                },

                // 初始化URL参数
                initParams() {
                    const search = window.location.search
                    const [mark, query] = search.split('?')
                    const getParams = (key, targetKey) => {
                        if (new RegExp(`${key}=([^&]*)`).test(query)) {
                            const value = decodeURIComponent(RegExp.$1)
                            const target = targetKey || key
                            if (target === 'currentPage') {
                                this[target] = /^-?\d+$/.test(value) ? value * 1 : this[target]
                            } else if (target === 'areaId' || target === 'companyNature' || target === 'companyType' || target === 'welfareType') {
                                // 处理筛选数组参数（与jobList保持一致）
                                if (value) {
                                    const values = value.split('_')
                                    // 地区ID保持字符串类型，其他转为数字类型（根据接口文档）
                                    if (target === 'areaId') {
                                        this[target] = values // 地区ID为字符串
                                    } else {
                                        this[target] = values.map((v) => (/^-?\d+$/.test(v) ? v * 1 : v)) // 其他为数字
                                    }
                                } else {
                                    this[target] = []
                                }
                            } else if (target === 'industryId') {
                                // 行业ID需要转为数字类型以匹配级联选择器
                                this[target] = /^-?\d+$/.test(value) ? value * 1 : value
                            } else {
                                this[target] = /^-?\d+$/.test(value) ? value * 1 : value
                            }
                        }
                    }

                    if (query) {
                        getParams('keyword')
                        getParams('industryId')
                        getParams('companyScaleType')
                        getParams('isCollect')
                        getParams('page', 'currentPage')
                        getParams('areaId')
                        getParams('companyNature')
                        getParams('companyType')
                        getParams('welfareType')

                        // 调试信息
                        console.log('URL参数初始化完成:', {
                            keyword: this.keyword,
                            industryId: this.industryId,
                            currentPage: this.currentPage,
                            areaId: this.areaId,
                            companyNature: this.companyNature,
                            companyType: this.companyType,
                            welfareType: this.welfareType
                        })
                    }
                },

                // 获取用户信息
                async getUserInfo() {
                    try {
                        const data = await httpGet('/company/home-user-info')
                        this.isLogin = data.isLogin || false
                        this.isVip = data.isVip || false
                    } catch (error) {
                        console.error('获取用户信息失败:', error)
                    }
                },

                // 获取配置数据
                async getConfigData() {
                    this.configLoading = true
                    try {
                        const data = await httpGet('/company/config')

                        // 根据接口文档处理配置数据
                        // 行业数据：industryList（2级结构）
                        if (data.industryList) {
                            this.industryOptions = data.industryList.map((item) => ({
                                label: item.v,
                                value: item.k,
                                children: item.children
                                    ? item.children.map((child) => ({
                                        label: child.v,
                                        value: child.k
                                    }))
                                    : undefined
                            }))
                            console.log('行业数据（2级结构）:', this.industryOptions)

                            // 配置数据加载完成后，重新设置行业ID以确保级联选择器正确显示
                            this.refreshIndustrySelection()
                        }

                        // 地区数据：areaList（热门城市）
                        if (data.areaList) {
                            this.areaOptions = data.areaList.map((item) => ({
                                label: item.v,
                                value: item.k
                            }))
                        }

                        // 单位性质：companyNatureList
                        if (data.companyNatureList) {
                            this.natureOptions = data.companyNatureList.map((item) => ({
                                label: item.v,
                                value: typeof item.k === 'string' ? parseInt(item.k) : item.k // 确保为数字类型
                            }))
                        }

                        // 单位类型：companyTypeList
                        if (data.companyTypeList) {
                            console.log('原始 companyTypeList:', data.companyTypeList)
                            this.typeOptions = data.companyTypeList.map((item) => ({
                                label: item.v,
                                value: typeof item.k === 'string' ? parseInt(item.k) : item.k // 确保为数字类型
                            }))
                            console.log('处理后的 typeOptions:', this.typeOptions)
                        }

                        // 单位规模：companyScaleList
                        if (data.companyScaleList) {
                            this.scaleOptions = data.companyScaleList.map((item) => ({
                                label: item.v,
                                value: typeof item.k === 'string' ? parseInt(item.k) : item.k // 确保为数字类型
                            }))
                        }

                        // 福利类型：welfareList
                        if (data.welfareList) {
                            this.welfareOptions = data.welfareList.map((item) => ({
                                label: item.v,
                                value: item.k
                            }))
                        }

                        // 热词数据：hotSearchList
                        if (data.hotSearchList && data.hotSearchList.length > 0) {
                            this.hotSearchList = data.hotSearchList.map((item, index) => ({
                                id: item.id || index + 1,
                                text: item.keyword || item.text || item.name || item
                            }))
                            console.log('从config接口获取热词数据:', this.hotSearchList)
                        } else {
                            console.log('config接口未返回热词数据，使用默认热词')
                        }
                    } catch (error) {
                        console.error('获取配置数据失败:', error)
                        // 使用默认数据
                        this.industryOptions = [
                            {
                                label: '教育/培训',
                                value: 1,
                                children: [
                                    { label: '学前教育', value: 18 },
                                    { label: '初等教育', value: 19 },
                                    { label: '中等教育', value: 20 },
                                    { label: '高等教育', value: 21 },
                                    { label: '职业教育', value: 22 },
                                    { label: '教育培训', value: 23 }
                                ]
                            }
                        ]
                    } finally {
                        this.configLoading = false
                    }
                },

                // 获取公司列表
                async getCompanyList() {
                    // 首次加载时使用companyListLoading，后续切换使用loading
                    if (this.companyListLoading) {
                        // 首次加载，整个区域隐藏
                    } else {
                        // 切换筛选条件，显示loading层
                        this.loading = true
                    }
                    try {
                        const params = this.getSearchParams()
                        const data = await httpGet('/company/list', params)

                        this.companyList = data.list || []
                        this.totalNum = data.totalNum || 0
                    } catch (error) {
                        console.error('获取公司列表失败:', error)
                        // 请求失败时显示空状态
                        this.companyList = []
                        this.totalNum = 0
                        ElementPlus.ElMessage.error('获取单位列表失败，请稍后重试')
                    } finally {
                        this.companyListLoading = false
                        this.loading = false
                    }
                },

                // 获取搜索参数（根据接口文档调整）
                getSearchParams() {
                    const params = {
                        page: this.currentPage,
                        keyword: this.keyword,
                        industryId: this.industryId,
                        companyScaleType: this.companyScaleType,
                        isCollect: this.isCollect
                    }

                    // 处理数组参数，按接口文档要求用下划线分隔
                    if (this.areaId && this.areaId.length > 0) {
                        params.areaId = this.areaId.join('_')
                    }
                    if (this.companyNature && this.companyNature.length > 0) {
                        params.companyNature = this.companyNature.join('_')
                    }
                    if (this.companyType && this.companyType.length > 0) {
                        params.companyType = this.companyType.join('_')
                    }
                    if (this.welfareType && this.welfareType.length > 0) {
                        params.welfareType = this.welfareType.join('_')
                    }

                    // 移除空值参数
                    Object.keys(params).forEach((key) => {
                        if (params[key] === '' || params[key] === null || params[key] === undefined) {
                            delete params[key]
                        }
                    })

                    // 添加调试信息
                    console.log('搜索参数:', params)
                    console.log('当前状态:', {
                        keyword: this.keyword,
                        industryId: this.industryId,
                        currentPage: this.currentPage,
                        areaId: this.areaId,
                        companyNature: this.companyNature,
                        companyType: this.companyType,
                        welfareType: this.welfareType
                    })

                    return params
                },

                // 获取推荐数据
                // async getRecommendData() {
                //     this.recommendLoading = true
                //     try {
                //         const data = await httpGet('/company/home-showcase-list')
                //         // 根据接口文档，推荐数据在data.recommend中
                //         this.recommendData = data.recommend || []
                //         console.log('推荐数据:', this.recommendData)
                //     } catch (error) {
                //         console.error('获取推荐数据失败:', error)
                //         // 使用空数组作为默认值
                //         this.recommendData = []
                //     } finally {
                //         // 推荐数据加载完成
                //         this.recommendLoading = false
                //     }
                // },

                updQuery(data) {
                    const base = window.location.href
                    const hasParams = base.indexOf('?') > -1
                    const baseUrl = base + (hasParams ? '' : '?')
                    const keys = Object.keys(data)

                    const result = keys.reduce((previous, current) => {
                        const value = data[current]
                        const isValid = value === null ? false : value !== ''
                        const isExist = new RegExp(`(${current}=[^&]*)`).test(previous)
                        const keyValue = isExist ? RegExp.$1 : ''

                        if (isValid) {
                            if (isExist) {
                                previous = previous.replace(keyValue, `${current}=${value}`)
                            } else {
                                previous += `&${current}=${encodeURIComponent(value)}`
                            }
                        } else {
                            previous = previous.replace(new RegExp(`&?${keyValue}`), '')
                        }

                        return previous.replace(/\?&/, '?')
                    }, baseUrl)

                    return result.replace(/\?$/, '')
                },

                // 搜索处理
                handleSearch() {
                    this.currentPage = 1
                    this.updateURL()
                    this.getCompanyList()
                },

                // 当前页变化
                handleCurrentPageChange(val) {
                    this.currentPage = val
                    this.updateURL()
                    this.getCompanyList()

                    // 滚动到列表顶部，提升用户体验
                    this.scrollToListTop()
                },

                // 收藏处理
                async handleCollectClick(companyId) {
                    if (!this.isLogin) {
                        // 显示登录弹窗
                        window.globalComponents?.loginDialogComponent?.showLoginDialog()
                        return
                    }

                    try {
                        // 获取当前收藏状态
                        const company = this.companyList.find((item) => item.companyId === companyId)
                        const currentCollected = company ? company.isCollect === 1 : false

                        // 根据接口文档，type: 1=收藏，0=取消收藏
                        const type = currentCollected ? 0 : 1

                        const data = await httpPost('/api/person/company/collect', {
                            companyId,
                            type
                        })

                        // 更新本地状态 - 根据接口返回的isCollect状态
                        if (company && data && typeof data.isCollect !== 'undefined') {
                            company.isCollect = data.isCollect
                        } else if (company) {
                            // 如果接口没有返回状态，则根据操作类型更新
                            // 1=收藏，2=未收藏
                            company.isCollect = type === 1 ? 1 : 2
                        }

                        // ElementPlus.ElMessage.success(company.isCollect === 1 ? '收藏成功' : '取消收藏成功')
                    } catch (error) {
                        console.error('收藏操作失败:', error)
                        ElementPlus.ElMessage.error('操作失败，请稍后重试')
                    }
                },

                // 筛选处理
                handleFilter(val, key) {
                    this[key] = val
                    if (key !== 'currentPage') {
                        this.currentPage = 1
                    }
                    this.updateURL()
                    this.getCompanyList()
                },

                // 地区筛选处理
                handleAreaFilter(value) {
                    if (value === '') {
                        this.areaId = []
                        this.currentPage = 1
                        this.updateURL()
                        this.getCompanyList()
                    } else {
                        // 忽略占位数据的点击
                        if (String(value).startsWith('placeholder_')) {
                            return
                        }
                        // 地区ID为字符串类型，确保类型一致
                        const stringValue = String(value)
                        const index = this.areaId.indexOf(stringValue)
                        if (index > -1) {
                            // 移除选中的条件
                            this.areaId.splice(index, 1)
                            this.currentPage = 1
                            this.updateURL()
                            this.getCompanyList()
                        } else {
                            // 添加新的条件前检查限制，超出限制时阻止添加
                            if (!this.canAddFilter(this.areaId, '地区')) {
                                return // 阻止添加，不发起API请求
                            }
                            this.areaId.push(stringValue)
                            this.currentPage = 1
                            this.updateURL()
                            this.getCompanyList()
                        }
                    }
                },

                // 性质筛选处理
                handleNatureFilter(value) {
                    if (value === '') {
                        this.companyNature = []
                        this.currentPage = 1
                        this.updateURL()
                        this.getCompanyList()
                    } else {
                        // 单位性质ID为数字类型
                        const numValue = typeof value === 'string' ? parseInt(value) : value
                        const index = this.companyNature.indexOf(numValue)
                        if (index > -1) {
                            // 移除选中的条件
                            this.companyNature.splice(index, 1)
                            this.currentPage = 1
                            this.updateURL()
                            this.getCompanyList()
                        } else {
                            // 添加新的条件前检查限制，超出限制时阻止添加
                            if (!this.canAddFilter(this.companyNature, '单位性质')) {
                                return // 阻止添加，不发起API请求
                            }
                            this.companyNature.push(numValue)
                            this.currentPage = 1
                            this.updateURL()
                            this.getCompanyList()
                        }
                    }
                },

                // 类型筛选处理
                handleTypeFilter(value) {
                    if (value === '') {
                        this.companyType = []
                        this.currentPage = 1
                        this.updateURL()
                        this.getCompanyList()
                    } else {
                        // 单位类型ID为数字类型
                        const numValue = typeof value === 'string' ? parseInt(value) : value
                        const index = this.companyType.indexOf(numValue)
                        if (index > -1) {
                            // 移除选中的条件
                            this.companyType.splice(index, 1)
                            this.currentPage = 1
                            this.updateURL()
                            this.getCompanyList()
                        } else {
                            // 添加新的条件前检查限制，超出限制时阻止添加
                            if (!this.canAddFilter(this.companyType, '单位类型')) {
                                return // 阻止添加，不发起API请求
                            }
                            this.companyType.push(numValue)
                            this.currentPage = 1
                            this.updateURL()
                            this.getCompanyList()
                        }
                    }
                },

                // 福利筛选处理
                handleWelfareFilter(value) {
                    if (value === '') {
                        this.welfareType = []
                        this.currentPage = 1
                        this.updateURL()
                        this.getCompanyList()
                    } else {
                        // 福利类型为字符串类型（如"settlement_fee"）
                        const stringValue = String(value)
                        const index = this.welfareType.indexOf(stringValue)
                        if (index > -1) {
                            // 移除选中的条件
                            this.welfareType.splice(index, 1)
                            this.currentPage = 1
                            this.updateURL()
                            this.getCompanyList()
                        } else {
                            // 添加新的条件前检查限制，超出限制时阻止添加
                            if (!this.canAddFilter(this.welfareType, '福利类型')) {
                                return // 阻止添加，不发起API请求
                            }
                            this.welfareType.push(stringValue)
                            this.currentPage = 1
                            this.updateURL()
                            this.getCompanyList()
                        }
                    }
                },

                // 初始化轮播
                initCarousel() {
                    var $panes = $('.recommend-company .pane')

                    // 获取每个分类的页数，赋予对应轮播时间
                    for (var i = 0; i < $panes.length; i++) {
                        let time = 0
                        let length = $panes.eq(i).find('.el-carousel__item').length

                        // 当分类的页数超过2页才显示轮播点
                        if (length > 1) {
                            $panes.eq(i).find('.el-carousel__indicators').css('visibility', 'visible')
                        }

                        time = length * this.runTime
                        this.delayTime.push(time)
                    }
                },

                // 重置当前tab图片自动轮播状态
                handleRecommend(e) {
                    const { nodeName } = e.target
                    const index = $(e.target).index()
                    const $recommend = $('.recommend-company .pane').eq(index)

                    if (nodeName === 'SPAN') {
                        $recommend.find('.el-carousel__indicator')[0].click()
                        this.running.fill(false)[$(e.target).index()] = true
                    }
                },

                handleShow(e) {
                    const $this = $(e.target)
                    const $parent = $this.parent()

                    $this.toggleClass('is-reverse')
                    $parent.toggleClass('is-show')
                },

                // 热门搜索点击处理
                handleHotSearchClick(text) {
                    this.keyword = text
                    this.currentPage = 1
                    this.updateURL()
                    this.getCompanyList()
                },

                // 行业类别变化处理
                handleIndustryChange(value) {
                    this.industryId = value
                    this.currentPage = 1
                    this.updateURL()
                    this.getCompanyList()
                },

                // 更新URL（参考jobList实现）
                updateURL() {
                    const data = this.getSearchParams()
                    const query = Object.keys(data).reduce(function (previous, current) {
                        let value = data[current]

                        // 当前页为1时不显示在URL中
                        if (current === 'page' && value === 1) {
                            return previous
                        }

                        // 过滤空值
                        if (value === '' || value === 0 || (Array.isArray(value) && value.length === 0)) {
                            return previous
                        }

                        const prefix = `${previous}${previous === '' ? '?' : '&'}`

                        if (Array.isArray(value)) {
                            if (value.length) {
                                return `${prefix}${current}=${value.join('_')}`
                            } else {
                                return previous
                            }
                        } else {
                            return `${prefix}${current}=${encodeURIComponent(value)}`
                        }
                    }, '')

                    // 更新浏览器URL
                    const search = decodeURIComponent(location.search)
                    if (search !== query) {
                        window.history.pushState(JSON.stringify(data), '', query || location.pathname)
                    }
                },

                handleClear() {
                    // 清空所有筛选条件，保留关键词和行业
                    this.areaId = []
                    this.companyNature = []
                    this.companyType = []
                    this.welfareType = []
                    this.companyScaleType = ''
                    this.isCollect = ''
                    this.currentPage = 1

                    // 更新URL并重新获取数据
                    this.updateURL()
                    this.getCompanyList()
                },

                // 获取推荐数据分组（每组7个）
                getRecommendChunks() {
                    const chunkSize = 7
                    const chunks = []
                    for (let i = 0; i < this.recommendData.length; i += chunkSize) {
                        chunks.push(this.recommendData.slice(i, i + chunkSize))
                    }
                    return chunks
                },

                // 检查是否可以添加筛选条件
                canAddFilter(categoryArray, categoryName) {
                    // 检查单个类别是否超出限制
                    if (categoryArray.length >= this.maxFilterPerCategory) {
                        ElementPlus.ElMessage.warning(`${categoryName}最多只能选择${this.maxFilterPerCategory}个选项`)
                        return false
                    }

                    // 检查总数是否超出限制
                    if (this.totalSelectedFilters >= this.maxFilterTotal) {
                        ElementPlus.ElMessage.warning(`所有筛选条件总共最多只能选择${this.maxFilterTotal}个`)
                        return false
                    }

                    return true
                },

                // 滚动到列表顶部
                scrollToListTop() {
                    // 使用 nextTick 确保 DOM 更新完成后再滚动
                    this.$nextTick(() => {
                        const listElement = document.getElementById('company-list-top')
                        if (listElement) {
                            // 平滑滚动到列表顶部，偏移一些距离以获得更好的视觉效果
                            const offsetTop = listElement.offsetTop - 20
                            window.scrollTo({
                                top: offsetTop,
                                behavior: 'smooth'
                            })
                        }
                    })
                },

                // 刷新行业选择状态
                refreshIndustrySelection() {
                    // 如果URL中有行业ID参数，在配置数据加载完成后重新设置
                    const urlParams = new URLSearchParams(window.location.search)
                    const industryIdParam = urlParams.get('industryId')

                    if (industryIdParam && this.industryOptions.length > 0) {
                        // 尝试不同的数据类型
                        const industryIdString = String(industryIdParam)
                        const industryIdNumber = /^-?\d+$/.test(industryIdParam) ? parseInt(industryIdParam) : null

                        // 检查行业ID是否在选项中存在，并返回匹配的值
                        const findIndustry = (options, targetString, targetNumber) => {
                            for (const option of options) {
                                if (option.value === targetString || option.value === targetNumber) {
                                    return option.value
                                }

                                if (option.children) {
                                    for (const child of option.children) {
                                        if (child.value === targetString || child.value === targetNumber) {
                                            return child.value
                                        }
                                    }
                                }
                            }
                            return null
                        }

                        const matchedValue = findIndustry(this.industryOptions, industryIdString, industryIdNumber)

                        if (matchedValue !== null) {
                            this.industryId = matchedValue
                        } else {
                            console.warn('URL中的行业ID在选项中不存在:', industryIdParam)
                        }
                    }
                }
            }
        }

        Vue.createApp(component)
            .use(ElementPlus, {
                locale: {
                    name: 'zh-cn',
                    el: {
                        pagination: {
                            goto: '前往',
                            pagesize: '条/页',
                            total: '共 {total} 条',
                            pageClassifier: '页',
                            deprecationWarning: '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档'
                        }
                    }
                }
            })
            .mount('#component')
    })
</script>

