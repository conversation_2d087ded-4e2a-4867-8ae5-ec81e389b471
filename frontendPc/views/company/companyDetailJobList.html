<link rel="stylesheet" href="/static/css/companyDetail.css" />

<div id="component">
<div class="el-main auth">
    <div class="detail-container">
        <?= frontendPc\components\CompanyDetailBannerWidget::widget(['companyId'=>$info['companyId']]) ?>

        <div class="detail-main">
            <section id="companyTemplate" class="section">
                <div class="tabs-common detail-cotent-auth">
                    <div class="tab-content company-job active">
                        <div class="wrapper">
                            <div class="top">
                                <div class="title">招聘职位</div>
                            </div>
                            <div class="filter">
                                <el-select :class="{'is-select': positionData.areaId}" v-model="positionData.areaId" :clearable="true" placeholder="地区">
                                    <el-option v-for="item in jobAreaOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                                </el-select>

                                <el-select :class="{'is-select': positionData.jobType}" v-model="positionData.jobType" :clearable="true" placeholder="职位类型">
                                    <el-option v-for="item in jobCategoryList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                                </el-select>

                                <el-select :class="{'is-select': positionData.majorId}" v-model="positionData.majorId" :clearable="true" placeholder="专业">
                                    <el-option v-for="item in majorOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                                </el-select>

                                <el-select :class="{'is-select': positionData.educationId}" v-model="positionData.educationId" :clearable="true" placeholder="学历">
                                    <el-option v-for="item in educationOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                                </el-select>
                            </div>

                            <!-- 首屏需判断if -->
                            <?php if(count($info['jobList']) > 0):?>
                                <div class="result">
                                    <div class="result-header">
                                        <div>职位信息</div>
                                        <div>需求专业</div>
                                        <div>操作</div>
                                    </div>
                                    <ul class="result-body">
                                        <?php foreach($info['jobList'] as $k=>$job):?>
                                            <li class="<?php if($job['status'] == 0): ?>offline-mark<?php endif?>">
                                                <a href="<?=$job['url']?>" target="_blank">
                                                    <div>
                                                        <div class="job-top">
                                                            <h5 class="name"><?=$job["jobName"]?></h5>
                                                            <span class="date"> <?=$job["refreshTime"]?>发布</span>
                                                        </div>
                                                        <div class="job-bottom">
                                                            <div class="salary"><?=$job["wage"]?></div>
                                                            <div class="tags">
                                                                <span><?=$job["areaName"]?></span>
                                                                <span><?=$job["education"]?></span>
                                                                <span>招<?=$job["amount"]?>人</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="major"><?=$job["major"]?></div>
                                                    <div>
                                                        <?php if($job["status"] == "0"):?>
                                                        <button disabled data-id="<?=$job['jobId']?>" class="el-button el-button--primary job-apply-button">已下线</button>
                                                        <?php elseif($job["applyStatus"] == "1"):?>
                                                        <button disabled data-id="<?=$job['jobId']?>" class="el-button el-button--primary job-apply-button has-applied">已申请</button>
                                                        <?php else:?>
                                                        <button data-id="<?=$job['jobId']?>" class="el-button el-button--primary job-apply-button">立即申请</button>
                                                        <?php endif;?>
                                                    </div>
                                                </a>
                                                <?php if($job["announcementId"]):?>
                                                <a href="<?=$job['announcementUrl']?>" target="_blank" class="announcement-detail offline-gray-first"><?=$job['announcementName']?></a>
                                                <?php endif;?>
                                            </li>
                                        <?php endforeach?>
                                    </ul>
                                </div>
                            <?php else:?>
                                <el-empty v-else description="暂无数据"></el-empty>
                            <?php endif?>
                            <el-pagination
                                background
                                :layout="'total, sizes, prev, pager, next, jumper'"
                                v-model:current-page="positionPagination.page"
                                v-model:page-size="positionPagination.size"
                                :total="positionPagination.total"
                            >
                            </el-pagination>
                        </div>
                    </div>
                </div>
            </section>

            <?= frontendPc\components\CompanyDetailRightWidget::widget(['companyId'=>$info['companyId'],'predicationTalentPreviewList'=>$info['activityList']['previewList']]) ?>
        </div>
    </div>
</div>
</div>

<script src="/static/lib/jquery-throttle-debounce/jquery-throttle-debounce.min.js"></script>
<script src="/static/lib/popper/popper.min.js?v=2"></script>

<script>
    $(function () {
        Vue.createApp({}).use(ElementPlus).mount('#tagsTemplate')

        const companyOptions = {
            data() {
                return {
                    companyId: <?= $info['companyId']?>,
                    jobAreaOptions: [
                        <?php foreach($info['jobCityList'] as $k=>$city):?>
                        { label: '<?=$city["v"]?>', value: '<?=$city["k"]?>' },
                        <?php endforeach?>
                    ],
                    jobCategoryList: [
                        <?php foreach($info['jobCategoryList'] as $k=>$jobCategory):?>
                        { label: '<?=$jobCategory["v"]?>', value: '<?=$jobCategory["k"]?>' },
                        <?php endforeach?>
                    ],
                    majorOptions: [
                        <?php foreach($info['majorList'] as $k=>$major):?>
                        { label: '<?=$major["v"]?>', value: '<?=$major["k"]?>' },
                        <?php endforeach?>
                    ],

                    educationOptions: [
                        <?php foreach($info['educationList'] as $k=>$education):?>
                        { label: '<?=$education["v"]?>', value: '<?=$education["k"]?>' },
                        <?php endforeach?>
                    ],

                    positionApi: '/api/person/company/get-job-list-html',

                    positionData: {
                        areaId: '',
                        jobType: '',
                        majorId: '',
                        educationId: ''
                    },

                    positionPagination: {
                        page: 1,
                        size: 20,
                        total: <?= $info['jobAmount']?$info['jobAmount']:0?>,
                    }
                }
            },

            watch: {
                positionData: {
                    handler(val) {
                        this.handleFilter()
                    },
                    deep: true
                },
                positionPagination:{
                    handler() {
                        this.handleFilter()
                    },
                    deep: true
                }
            },
            mounted() {
                this.initSwiper()
            },

            methods: {
                initSwiper() {
                    var swiper = new Swiper('.company-style-swiper', {
                        loop: true,
                        autoplay: {
                            pauseOnMouseEnter: true,
                            disableOnInteraction: false,
                            delay: 3000
                        },
                        navigation: {
                            nextEl: '.company-style-next',
                            prevEl: '.company-style-prev'
                        },
                        preventClicks: false,
                        pagination: {
                            el: '.swiper-pagination'
                        },
                        allowTouchMove: false
                    })
                },

               handleFilter() {
                    const { jobType: jobCategoryId, ...data } = this.positionData
                    const { page, size: pageSize } = this.positionPagination
                    const { companyId, positionApi } = this
                    const api = positionApi
                    const query = { ...data, jobCategoryId, page, pageSize, companyId }

                    httpGet(api, query).then((res) => {
                        const { list, page, size, total } = res

                        $('.company-job').find('.result-body').html(list)
                        this.positionPagination.total = total
                    })
                }
            }
        }

        Vue.createApp(companyOptions)
            .use(ElementPlus, {
                locale: {
                    name: 'zh-cn',
                    el: {
                        pagination: {
                            goto: '前往',
                            pagesize: '条/页',
                            total: '共 {total} 条',
                            pageClassifier: '页',
                            deprecationWarning: '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档'
                        }
                    }
                }
            })
            .mount('#companyTemplate')

        var id = "<?= $info['companyId']?>"
        var $collectButtons = $('.company-collect-button')

        // get miniprogram qrcode
        var miniprogramSlot = $('.to-miniprogram')

        httpGet('/company/get-detail-mini-code?id=' + id).then(function (data) {
            miniprogramSlot.append($('<img src="' + data.url + '" />'))
        })

        $collectButtons.on('click', function () {
            var $this = $(this)
            var isCollected = $this.hasClass('el-button--default')

            httpPost('/api/person/company/collect', { companyId: id }).then(function () {
                if (isCollected) {
                    $this.removeClass('el-button--default').addClass('el-button--primary').find('span').text('立即关注')
                } else {
                    $this.removeClass('el-button--primary').addClass('el-button--default').find('span').text('已关注')
                }
            })
        })

        // 立即申请
        $('body').on('click', '.job-apply-button', function (e) {
            e.preventDefault()
            var $this = $(this)
            var jobId = $this.attr('data-id')

            window.globalComponents.applyDialogComponent.beforeApply(jobId, function () {
                $this.prop('disabled', true).addClass('has-applied').text('已申请')
            })
        })

        // 职位需求专业气泡
        !(function majorPopper() {
            let el = document.createElement('div')
            el.className = 'major-popper'
            el.id = 'custom-popper'
            el.innerHTML = '<span class="arrow"></span><div class="content"></div>'
            document.body.appendChild(el)

            let popover = null
            $('body').on('mouseover', '.major', function () {
                let referenceEl = $(this)
                let isOverflow = $(this)[0].clientWidth < $(this)[0].scrollWidth
                if (!isOverflow) return
                let popperEl = $('.major-popper')
                $('.major-popper .content').text(referenceEl.text())
                popover = Popper.createPopper(referenceEl[0], popperEl[0], {
                    placement: 'top',
                    modifiers: [
                        {
                            name: 'offset',
                            options: {
                                offset: [0, 10]
                            }
                        }
                    ]
                })
            })
            $('body').on('mouseout', '.major', function () {
                if (popover) popover.destroy()
            })
        })()

    })
</script>
