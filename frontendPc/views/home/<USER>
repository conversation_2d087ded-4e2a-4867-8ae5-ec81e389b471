<link rel="stylesheet" href="/static/css/columnGovernment.css">
<link rel="stylesheet" href="/static/css/footerLink.css">

<?= frontendPc\components\HengFuWidget::widget(['columnId'=>$columnId]) ?>

<div class="main" id="component">
    <!-- 当前位置 -->
    <?= frontendPc\components\CurrentLocationWidget::widget(['columnId'=>$columnId]) ?>

    <!-- headlines.html -->
    <?=$headlines?>

    <!-- A1-->
    <?= frontendPc\components\TopBannerWidget::widget(['columnId'=>$columnId,'templateType'=>$templateType]) ?>

    <!-- 热门单位 -->
    <?= frontendPc\components\HotUnitWidget::widget(['columnId'=>$columnId,'templateType'=>$templateType]) ?>

    <div class="propaganda-container w">
        <?=$left_hot_announcement?>

        <?=$right_hot_announcement?>
    </div>

    <div class="job-container flex">
        <div class="left-content">
            <!-- 博士人才交流 -->
            <?= frontendPc\components\CommunicationWidget::widget() ?>

            <!-- 高校招聘地区导航 -->
            <?= frontendPc\components\RegionNavWidget::widget(['columnId'=>$columnId]) ?>

            <!-- Z1区 -->
            <?=frontendPc\components\ReservedWidget::widget(['columnId'=>$columnId])?>

            <!-- 按学科查看职位 -->
            <div class="sidebar job-classification">
                <?= frontendPc\components\JobNavWidget::widget() ?>
            </div>

            <!-- 招人求职 -->
            <?=frontendPc\components\FastManWidget::widget()?>

            <!-- 热门专题 -->
            <?=frontendPc\components\HotTopicWidget::widget()?>
        </div>

        <div class="right-content">
            <!-- 最新公告&简章 -->
            <?=$government_latest_announcement?>

            <!-- 精选职位 -->
            <?=$government_job?>

            <!-- 推荐单位 -->
            <?= frontendPc\components\RightRecommendUnitWidget::widget(['columnId'=>
            $columnId,'templateType'=>$templateType]) ?>
        </div>
    </div>
</div>
<!-- 锚点导航 -->
<div class="fixed-tool">
    <ul>
        <li class="current">头条</li>
        <li>热门单位</li>
        <li>热门公告</li>
        <li>最新公告</li>
        <li>精选职位</li>
        <li>推荐单位</li>
    </ul>
</div>
<!-- 友情链接 -->
<?= frontendPc\components\FriendLinkWidget::widget() ?>
<?= frontendPc\components\InternalLinkWidget::widget() ?>

<script>
    $(function () {
        let columnId = Number("<?php echo $columnId;?>");

        let selectJobPageSize = Number("<?php echo $jobPageSize;?>");
        let selectJobAccount = {
            account_1: Number("<?php echo $jobAccount_1;?>"),
            account_2: Number("<?php echo $jobAccount_2;?>"),
            // account_3: Number("<?php echo $jobAccount_3;?>")
        }

        // 每切换n页循环一次
        let maxPage = 6
        let recommendPage = 3

        const component = {
            data() {
                return {
                    hotAnnouncementForm: {
                        columnId: columnId,
                        key: 'left_hot_announcement',
                        page: 2,
                    },
                    recommendAnnouncementForm: {
                        columnId: columnId,
                        key: 'right_hot_announcement',
                        page: 2,
                    },

                    // 精选职位
                    selectJobMouseIndex: 1,
                    selectJobPageMap: {
                        page_1: 1,
                        page_2: 1,
                        page_3: 1
                    },
                    selectJobPage: 1,
                    selectJobPageSize: selectJobPageSize,
                    selectJobCount: selectJobAccount.account_1,
                }
            },
            mounted() {
            },
            methods: {
                // 热门公告&简章
                handleHotAnnouncement() {
                    httpGet('/home/<USER>', this.hotAnnouncementForm).then((resp) => {
                        const content = resp.list
                        const msg = resp.msg
                        const page = resp.page
                        const reg = /(<ul class="announcement-news"[\s\S]*<\/ul>)/g
                        const html = content.match(reg)[0]

                        if (page) {
                            this.hotAnnouncementForm.page = page
                        } else {
                            const curPage = this.hotAnnouncementForm.page
                            this.hotAnnouncementForm.page = curPage > maxPage ? 1 : curPage + 1
                        }

                        if (msg) {
                            ElementPlus.ElMessage.warning(msg)
                        } else {
                            $('.propaganda-hot .announcement-content').html(html)
                        }
                    })
                },

                // 推荐公告&简章
                handleRecommendAnnouncement() {
                    httpGet('/home/<USER>', this.recommendAnnouncementForm).then((resp) => {
                        const content = resp.list
                        const msg = resp.msg
                        const page = resp.page
                        const reg = /(<ul class="component">[\s\S]*<\/ul>)/g
                        const html = content.match(reg)[0]

                        if (page) {
                            this.recommendAnnouncementForm.page = page
                        } else {
                            const curPage = this.recommendAnnouncementForm.page
                            this.recommendAnnouncementForm.page = curPage > recommendPage ? 1 : curPage + 1
                        }

                        if (msg) {
                            ElementPlus.ElMessage.warning(msg)
                        } else {
                            $('.propaganda-recommend .recommend-content').html(html)
                        }
                    })
                },

                // 精选职位-tab鼠标移动事件
                selectJobMouseover(index) {
                    const k = `page_${index}`
                    this.selectJobMouseIndex = index
                    this.selectJobPage = this.selectJobPageMap[k]
                    this.selectJobCount = selectJobAccount['account_' + index]
                },
                // 精选职位-分页
                handleSelectJobPagination(page = 1) {
                    const k = `page_${this.selectJobMouseIndex}`
                    const index = this.selectJobMouseIndex
                    this.selectJobPageMap[k] = page
                    this.selectJobPage = page
                    // recommend推荐职位，hot热门职位，''最新职位
                    let key = 'new'
                    switch (index) {
                        // case 1: key='recommend'; break;
                        case 1: key = 'new'; break;
                        case 2: key = 'hot'; break;
                        default: break;
                    }
                    httpGet('/home/<USER>', {
                        columnId, page, key
                    }).then((resp) => {
                        $('.selected-job .tab-con .college-information').eq(index - 1).html(resp.list)
                    })
                }
            }
        }

        Vue.createApp(component).use(ElementPlus, {
            locale: {
                name: 'zh-cn',
                el: {
                    pagination: {
                        goto: '前往',
                        pagesize: '条/页',
                        total: '共 {total} 条',
                        pageClassifier: '页',
                        deprecationWarning: '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档',
                    }
                }
            }
        }).mount('#component')
    });
</script>

<script src="/static/js/column.js?v=2"></script>
<script src="/static/js/citySwitch.js?v=2"></script>