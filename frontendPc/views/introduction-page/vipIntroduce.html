<link rel="stylesheet" href="/static/css/vipIntroduce.css?v=1.1.0">
<script src="https://img.gaoxiaojob.com/uploads/static/lib/crypto/index.js"></script>

<div id="component" v-cloak>
    <div class="banner"></div>

    <div class="tabs-wrapper" id="tabs-wrapper">
        <div class="main-wrapper">
            <div class="tabs">
                <a class="item" :class="{'active': vipType === 1}" @click="changeVipType(1)" href="javascript:;">
                    <span class="tag">更多人选择</span>
                    尊享<span class="amount">11+</span>求职权益
                </a>
                <a class="item" :class="{'active': vipType === 2}" @click="changeVipType(2)" href="javascript:;">尊享<span class="amount">8+</span>求职权益</a>
            </div>
            <!--
                forbidden-status :不支出开通黄金会员
                update-status :黄金升钻石
             -->
            <div class="tab-panes" :class="{'forbidden-status': openType === 3, 'update-status': openType === 2}">
                <div v-if="vipType === 1" class="pane diamond">
                    <div class="list" :class="{'active': index === hoverPackageIndex}" v-for="(item, index) in diamondList" :key="index" @mouseenter="packageSelect(item, index)">
                        <span v-if="item.buyTypeTxt" class="tag">{{item.buyTypeTxt}}</span>
                        <div class="title">钻石VIP∙{{item.days}}天</div>
                        <div class="desc">{{item.buyDesc}}</div>
                        <div class="price">
                            <span class="real-price">{{item.realAmount}}</span>
                            <span class="original-price">¥{{item.originalAmount}}</span>
                        </div>
                        <div class="avg-price">已优惠￥{{item.discountAmount}}</div>
                        <div class="deduction-content">
                            <div class="deduction">
                                <span class="label">升级抵扣</span>
                                <span class="money">¥{{item.upgradeData.convertPrice}}</span>
                                <el-tooltip
                                    effect="dark"
                                    content="升级钻石VIP服务：黄金VIP会员可按剩余时长天数，折算抵扣对应金额升级为钻石VIP会员，享受更多求职权益！（最高抵扣金额不得高于所升级套餐金额）"
                                    placement="top"
                                >
                                    <span slot="default" class="what"></span>
                                </el-tooltip>
                            </div>
                        </div>
                        <button class="vip-status" @click="openpayDialog(item.equityPackageCategoryId, index)">
                            <div class="open">立即开通</div>
                            <div class="update">实付<span class="money">{{item.upgradeData.realAmount}}</span>立即升级</div>
                        </button>
                        <div class="detail">
                            <div class="detail-item" v-for="(equity, i) in item.equityList" :key="i">
                                <div class="label">{{equity.name}}</div>
                                <div class="value">
                                    <div class="text" v-html="equity.description"></div>
                                    <i v-if="equity.icon == 1" class="icon"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="vipType === 2" class="pane gold">
                    <div class="list" :class="{'active': index === hoverPackageIndex}" v-for="(item, index) in goldList" @mouseenter="packageSelect(item, index)">
                        <span v-if="item.buyTypeTxt" class="tag">{{item.buyTypeTxt}}</span>
                        <div class="title">黄金VIP∙{{item.days}}天</div>
                        <div class="desc">{{item.buyDesc}}</div>
                        <div class="price">
                            <span class="real-price">{{item.realAmount}}</span>
                            <span class="original-price">¥{{item.originalAmount}}</span>
                        </div>
                        <div class="avg-price">{{item.dailyAmount}}元/天</div>
                        <button class="vip-status" @click="openpayDialog(item.equityPackageCategoryId, index)" :disabled="openType === 3">
                            <div class="open">立即开通</div>
                            <div class="forbidden">
                                不支持开通
                                <div class="reason">钻石VIP服务生效中</div>
                            </div>
                        </button>
                        <div class="detail">
                            <div class="detail-item" v-for="(equity, i) in item.equityList" :key="i">
                                <div class="label">{{equity.name}}</div>
                                <div class="value">
                                    <div class="text" v-html="equity.description"></div>
                                    <i v-if="equity.icon == 1" class="icon"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="agreement">* 购买即表示同意 <a href="/agreement/value-added-services?type=1" target="_blank">《高校人才网增值服务协议》</a></div>
        </div>
    </div>

    <div class="contrast-wrapper" id="contrast-wrapper">
        <div class="main-wrapper" :class="{'show': showLimit}">
            <div class="wrapper-title">VIP 会员权益对比</div>
            <div class="subheading">海量招聘信息，心仪岗位轻松get，从此快速便捷找工作</div>
            <div class="detail" :class="[vipType === 2 ? 'gold' : 'diamond']">
                <span class="hot">推荐</span>
                <table>
                    <thead>
                        <tr>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="name">简历置顶</div>
                                <div class="instructions">您的简历将在单位搜索时优先置顶展示，让单位一眼可见</div>
                            </td>
                            <td>
                                <span class="error"></span>
                            </td>
                            <td>
                                <span class="error"></span>
                            </td>
                            <td>
                                <span class="contain"></span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="name">投递置顶</div>
                                <div class="instructions">您投递的简历将在单位的应聘列表中置顶展示，便于单位快速查看</div>
                            </td>
                            <td>
                                <span class="error"></span>
                            </td>
                            <td>
                                <span class="error"></span>
                            </td>
                            <td>
                                <span class="contain"></span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="name">简历刷新</div>
                                <div class="instructions">每天9:00自动刷新简历+无限次手动刷新，有效提升简历搜索排名</div>
                            </td>
                            <td>1次/天</td>
                            <td>1次/天</td>
                            <td>无限次</td>
                        </tr>
                        <tr>
                            <td>
                                <div class="name">竞争力分析</div>
                                <div class="instructions">查看职位热度、人岗匹配度、简历竞争力，实时了解自身竞争优势</div>
                            </td>
                            <td>
                                <span class="error"></span>
                            </td>
                            <td>10次/天</td>
                            <td>10次/天</td>
                        </tr>
                        <tr>
                            <td>
                                <div class="name">公告热度</div>
                                <div class="instructions">查看招聘公告的热度数据及投递者的画像分布</div>
                            </td>
                            <td>
                                <span class="error"></span>
                            </td>
                            <td>10次/天</td>
                            <td>10次/天</td>
                        </tr>
                        <tr>
                            <td>
                                <div class="name">
                                    高级筛选
                                    <span class="privilege-escalation-tag">权益升级</span>
                                </div>
                                <div class="instructions">一键筛选有编制、低热度岗位，“上岸”快人一步</div>
                            </td>
                            <td>
                                <span class="error"></span>
                            </td>
                            <td>
                                <span class="contain"></span>
                            </td>
                            <td>
                                <span class="contain"></span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="name">简历模板</div>
                                <div class="instructions">多款高层次人才专属简历模板，精美简历一键生成</div>
                            </td>
                            <td>
                                <span class="error"></span>
                            </td>
                            <td>
                                <span class="contain"></span>
                            </td>
                            <td>
                                <span class="contain"></span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="name">浏览足迹</div>
                                <div class="instructions">支持查看近90天的公告&职位浏览记录，优质岗位不再错过</div>
                            </td>
                            <td>
                                <span class="error"></span>
                            </td>
                            <td>
                                <span class="contain"></span>
                            </td>
                            <td>
                                <span class="contain"></span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="name">收藏查看</div>
                                <div class="instructions">心仪岗位可一键收藏，可查看全部收藏的职位&公告信息</div>
                            </td>
                            <td>限50条</td>
                            <td>无限制</td>
                            <td>无限制</td>
                        </tr>
                        <tr>
                            <td>
                                <div class="name">求职资源</div>
                                <div class="instructions">免费获取硕博求职资源干货包，包括年度招聘报告</div>
                            </td>
                            <td>
                                <span class="error"></span>
                            </td>
                            <td>
                                <span class="contain"></span>
                            </td>
                            <td>
                                <span class="contain"></span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="name">专属标识</div>
                                <div class="instructions">拥有会员专属身份标识，彰显会员尊贵权益</div>
                            </td>
                            <td>
                                <span class="error"></span>
                            </td>
                            <td>
                                <span class="contain"></span>
                            </td>
                            <td>
                                <span class="contain"></span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="name">高才优课</div>
                                <div class="instructions">海量硕博人才精选课程，求职路上时刻“充电”</div>
                            </td>
                            <td>
                                <span class="error"></span>
                            </td>
                            <td>开通180天会员可加赠</td>
                            <td>
                                <span class="contain"></span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="show-more">
                <button class="more" @click="showLimit = !showLimit">{{showLimit ? '收起': '查看更多'}}</button>
            </div>
        </div>
    </div>

    <div>
        <div class="introduce-wrapper">
            <div class="main-wrapper">
                <div class="wrapper-title">权益介绍</div>
                <div>
                    <div class="list">
                        <div class="title diamond-vip">简历置顶</div>
                        <div class="desc">
                            想让用人单位第一眼就看到你的简历？使用「简历置顶」功能，能够在用人单位搜索人才时，让你的简历优先展示在搜索结果顶部，有效提升你的简历曝光度，提高进面概率！
                        </div>
                    </div>
                    <div class="list">
                        <div class="title diamond-vip">投递置顶</div>
                        <div class="desc">同一岗位有大量求职者，不想自己的简历被刷下去？快试试「投递置顶」功能！投递后，你的简历将在用人单位的应聘列表中置顶展示，更快速被单位浏览和处理。</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="introduce-wrapper reverse">
            <div class="main-wrapper">
                <div class="list">
                    <div class="title diamond-vip">简历刷新</div>
                    <div class="desc">
                        简历总是石沉大海，可能是因为你没有使用「简历刷新」功能。「简历刷新」功能，每天9:00自动帮你刷新简历，让你的简历在用人单位搜索时排名更靠前，被更多单位第一时间看到！
                    </div>
                </div>
                <div class="list">
                    <div class="title">
                        高级筛选
                        <span class="privilege-escalation-tag">权益升级</span>
                    </div>
                    <div class="desc">在海量招聘信息中，一键筛选有编制、低热度的职位&公告，助力选岗提质增效。无论你是想上岸编制岗，捡漏冷门岗，还是跟投热门岗，都能快速把握，省时省力更省心！</div>
                </div>
            </div>
        </div>
        <div class="introduce-wrapper">
            <div class="main-wrapper">
                <div class="list">
                    <div class="title">竞争力分析</div>
                    <div class="desc">
                        基于智能匹配算法进行人岗适配分析，多维度查看职位热度数据&趋势变化；同时根据竞争者的画像数据分析，直观了解自身竞争优劣势，帮助你更有针对性地投递。
                    </div>
                </div>
                <div class="list">
                    <div class="title">公告热度</div>
                    <div class="desc">根据公告感兴趣人群、投递数据和同类公告热度对比等条件综合分析得出，不仅可及时掌握热度趋势变化，还可以获取竞争者的群体特征，知己知彼，让求职更有把握！</div>
                </div>
            </div>
        </div>
    </div>

    <div class="more-contrast-wrapper">
        <div class="main-wrapper">
            <div class="title"></div>
            <div class="content">
                <span
                    class="pre"
                    :class="{'disabled': moreContrastScrollIndex === 0}"
                    @click="moreContrastScrollIndex = moreContrastScrollIndex = moreContrastScrollIndex - 1 >=0 ? moreContrastScrollIndex - 1 : 0"
                ></span>
                <div class="scroll-content">
                    <div class="contrast" :style="scrollStyle">
                        <div class="item">
                            <div class="name">简历模板</div>
                            <div class="sub">高层次人才专属模板 一键应用专业吸睛</div>
                        </div>
                        <div class="item">
                            <div class="name">浏览足迹</div>
                            <div class="sub">查看近90天浏览记录 历史信息快速查找</div>
                        </div>
                        <div class="item">
                            <div class="name">收藏查看</div>
                            <div class="sub">查看全部收藏记录 心仪岗位不再错过</div>
                        </div>
                        <div class="item">
                            <div class="name">求职资源</div>
                            <div class="sub">免费获取硕博求职资源 干货包<span>(需前往服务号领取)</span></div>
                        </div>
                        <div class="item">
                            <div class="name">专属标识</div>
                            <div class="sub">拥有会员专属身份标识 彰显会员尊贵权益</div>
                        </div>
                        <div class="item">
                            <div class="name">高才优课</div>
                            <div class="sub">免费获取硕博人才精选 课程包<span>(需前往服务号领取)</span></div>
                        </div>
                    </div>
                </div>
                <span
                    class="next"
                    :class="{'disabled': moreContrastScrollIndex === 2}"
                    @click="moreContrastScrollIndex = moreContrastScrollIndex + 1 > 2 ? 2 : moreContrastScrollIndex + 1"
                ></span>
            </div>
        </div>
    </div>

    <div class="user-wrapper">
        <div class="main-wrapper">
            <div class="wrapper-title">用户评价</div>
            <div class="user-description">
                <div class="right-content">
                    <div class="right-catalogue" v-for="(item, index) in userList" :key="item.Id">
                        <div class="head">
                            <div class="avatar">
                                <img :src="item.img" alt="" />
                                <div :class="item.sex"></div>
                            </div>
                            <div class="user-content">
                                <div class="name">{{item.name}}</div>
                                <div class="category">
                                    <div class="icon">{{item.icon1}}</div>
                                    <div class="icon">{{item.icon2}}</div>
                                    <div class="icon">{{item.icon3}}</div>
                                </div>
                            </div>
                        </div>

                        <div class="text">{{item.text}}</div>
                    </div>
                </div>
            </div>
            <div class="search-content">
                <div class="search-title">服务说明</div>
                <div class="text-content">
                    <div class="left-text">
                        <p>1、请在本页面通过官方支付方式——微信扫码支付，并向唯一官方收款方“高校人才网”完成服务费用的支付。请勿尝试任何私下转账方式。</p>
                        <p>2、服务将于付款成功后自动开通，服务过期则所有权益失效。购买后请合理安排时间并尽快使用。</p>
                        <p>3、竞争力分析&公告热度权益，在服务期内，按自然日，每项服务可使用的次数上限为10次，不累计。</p>
                        <p>4、使用简历置顶 & 简历刷新权益时，请务必确认您未“隐藏简历”，且在线简历完整度≥65%（以平台显示的完整度为准），否则无法提升简历曝光效果。</p>
                        <p>5、投递置顶权益限“报名方式”为“站内投递”的职位使用；您可在投递简历时选择是否使用该项服务。同一职位，30天内只能使用一次投递置顶权益.</p>
                        <p>6、您屏蔽的单位无法搜索到您的简历，请放心使用服务。</p>
                        <p>7、不同城市、职位类型下的曝光效果不同，实际曝光效果可能会存在波动。</p>
                        <p>8、本产品为虚拟服务，不支持退款，敬请谅解。</p>
                        <p>
                            9、购买即表示同意<a href="/agreement/value-added-services?type=1" target="_blank"><span>《高校人才网增值服务协议》</span></a
                            >。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div v-if="showFooterFixed" class="fixed-wrapper">
        <!--
            forbidden-status :不支出开通黄金会员
            update-status :黄金升钻石
            -->
        <div class="main-wrapper" :class="{'forbidden-status': openType === 3 && vipType === 2, 'update-status': openType === 2 && vipType === 1}">
            <div class="place">
                <a href="#contrast-wrapper">权益对比</a>
                |
                <a href="#tabs-wrapper">服务套餐</a>
            </div>
            <div class="buy-info">
                {{selectPackage.name}}：
                <span class="price">
                    <span class="tag">￥</span>
                    {{selectPackage.realAmount}}
                </span>
                <button class="pay" :disabled="openType === 3 &&  vipType === 2" @click="openVipDialog">
                    <div class="open">立即开通</div>
                    <div class="update">
                        <div class="deduction">立减¥{{selectPackage.upgradeData.convertPrice}}</div>
                        实付<span class="real">{{selectPackage.upgradeData.realAmount}}</span>立即升级
                    </div>
                    <div class="forbidden">
                        不支持开通
                        <div class="reason">钻石VIP服务生效中</div>
                    </div>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    $(function () {
        const component = {
            data() {
                return {
                    vipType: 2,
                    // 开通类型 1开通，2升级，3不可购买黄金
                    openType: 1,
                    hoverPackageIndex: 1,
                    diamondList: [],
                    goldList: [],
                    selectPackage: {
                        equityPackageCategoryId: 1,
                        upgradeData: {}
                    },
                    showLimit: false,
                    moreContrastScrollIndex: 0,
                    userList: [
                        {
                            img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/user-avatar.png',
                            name: '陈**',
                            sex: 'male',
                            id: '1',
                            text: '求职焦虑期购买了服务。我主要使用投递置顶和简历置顶功能，使用后简历查看率有明显提升，这半个月已经拿到了2个不错的offer！ 总体来说，超值！好评！',
                            icon1: '硕士',
                            icon2: '会计学',
                            icon3: '湖南大学'
                        },
                        {
                            img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/female-avatar.png',
                            name: '林**',
                            sex: 'female',
                            id: '1',
                            text: '试了下那个竞争力分析，竟然可以跟同岗位的求职者对比，看到自己是什么水平，有点意思，就是这结果确实很卷……',
                            icon1: '硕士',
                            icon2: '教育学',
                            icon3: '北京师范大学'
                        },
                        {
                            img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/male-avatar2.png',
                            name: '王**  ',
                            sex: 'male',
                            id: '1',
                            text: '开通了钻石VIP后，我最常用的就是“简历置顶”，用了之后职位邀约明显增加了，很多单位联系我，最近已经开始在面试了，比我预想的还要快！',
                            icon1: '博士',
                            icon2: '机械工程',
                            icon3: '华南理工大学'
                        },
                        {
                            img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/male-avatar3.png',
                            name: '郑**  ',
                            sex: 'male',
                            id: '1',
                            text: '“高级筛选”太实用了，不仅能查热度，还能查编制岗，对于想入编的人来说很方便！而且还能一键置顶投递，让招聘单位优先看到我，反馈真的快很多。',
                            icon1: '博士',
                            icon2: '有机化学',
                            icon3: '南京大学'
                        },
                        {
                            img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/male-avatar4.png',
                            name: '黄**  ',
                            sex: 'female',
                            id: '1',
                            text: '开通了会员后，简历模板就可以无限次使用了。这些模板精美且专业，还可以一键套用样式，不用重新调整排版设计，很方便！适合我这种懒人哈哈。',
                            icon1: '博士',
                            icon2: '海洋生物学',
                            icon3: '中国海洋大学'
                        },
                        {
                            img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/male-avatar5.png',
                            name: '孙*  ',
                            sex: 'female',
                            id: '1',
                            text: '投递简历前，查一下公告热度还蛮有用的，我主要用来看公告热度趋势和投递竞争比例，了解哪公告的受欢迎程，哪些单位受求职者追捧，竞争情况怎么样。',
                            icon1: '博士',
                            icon2: '计算机应用技术',
                            icon3: '复旦大学'
                        }
                    ],
                    showFooterFixed: false
                }
            },
            computed: {
                scrollStyle() {
                    const x = -238 * this.moreContrastScrollIndex
                    return `transform:translateX(${x}px)`
                }
            },
            methods: {
                scroll() {
                    const _this = this
                    window.addEventListener('scroll', function () {
                        const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
                        const showHeight = document.body.clientHeight
                        _this.showFooterFixed = scrollTop > showHeight * 2
                    })
                },
                packageSelect(item, index) {
                    this.hoverPackageIndex = index
                    this.selectPackage = item
                },
                changeVipType(type) {
                    //-------------用户付费转化数据埋点-开始---------------
                    // let tabName = type === 1 ? '钻石VIP' : '黄金VIP'
                    // let logData = {
                    //     params : { tabName },
                    //     actionType : '1',
                    //     actionId : '10010002'
                    // }
                    // this.payBuriedPoint(logData)
                    httpGet("/showcase-browse-log/vip-tab-point-log?uuid=<?=$data['uuid']?>&type="+type)
                    //-------------用户付费转化数据埋点-结束---------------

                    const { vipType } = this
                    if (type === vipType) return
                    this.vipType = type
                    this.hoverPackageIndex = 1
                    this.showLimit = false

                    this.getPackage(type === 1 ? 3 : 1)
                },
                openVipDialog() {
                    const {
                        selectPackage: { equityPackageCategoryId },
                        hoverPackageIndex
                    } = this
                    this.openpayDialog(equityPackageCategoryId, hoverPackageIndex, 2)
                },
                openpayDialog(id, index, position = 1) {
                    //-------------用户付费转化数据埋点-开始---------------
                    // let productName = $('.list:eq('+index+') .title').text()
                    // let logData = {
                    //     params : { productId:id, productName:productName, clickPosition:position },
                    //     actionType : '1',
                    //     actionId : '10010003'
                    // }
                    // this.payBuriedPoint(logData)
                    //-------------用户付费转化数据埋点-结束---------------

                    let api = "<?=$data['api_popup']?>"
                    let uuid = "<?=$data['uuid']?>"
                    window.globalComponents.PayDialogAlertComponent.show(api, id, index,uuid,position)
                },
                getPackage(type) {
                    // id 1黄金 2钻石
                    let api = "<?=$data['api_buy']?>"
                    httpGet(api + '?equityPackageCategoryId=' + type).then((r) => {
                        const { isDiamondVip, isGoldVip, list } = r
                        this[type === 1 ? 'goldList' : 'diamondList'] = list
                        this.selectPackage = list[1]

                        let openType = 1
                        if (isGoldVip) {
                            openType = 2
                        }
                        if (isDiamondVip) {
                            openType = 3
                        }
                        this.openType = openType
                    })
                },

            },
            mounted() {
                let that = this
                this.scroll()
                this.getPackage(1)

                //-------------用户付费转化数据埋点-开始---------------
                window.onbeforeunload = function (){
                     httpGet("/showcase-browse-log/update-vip-view-point-log?uuid=<?=$data['uuid']?>")
                }
                //-------------用户付费转化数据埋点-结束---------------
            }
        }

        Vue.createApp(component).use(ElementPlus).mount('#component')
    })
</script>

<?= \frontendPc\components\DialogPaymentSuccess::widget() ?>
<?= \frontendPc\components\DialogResumePayWidget::widget() ?>