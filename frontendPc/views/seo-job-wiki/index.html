<!-- 头部挂件 -->
<link rel="stylesheet" href="/static/css/cyclopedia.css"/>

<div class="cyclopedia-container">

    <div class="job-desc-wrapper">
        <div class="view-content">
            <h1 class="job-name"><?=$detail['keyword']?></h1>
            <?php if($detail['jobIntroduction'] != ''):?>
            <div class="desc">
                <?=$detail['jobIntroduction']?>
            </div>
            <?php endif;?>

        </div>
    </div>

    <div class="main-wrapper view-content">
        <div class="main-content">
            <div class="list-wrapper">

                <?php if($detail['jobContent'] != ''):?>
                <div class="list">
                    <div class="info">
                        <div class="top">
                            <a href="<?=$detail['jumpLink']?>" title="<?=$detail['keyword']?>" class="name" target="_blank"><?=$detail['keyword']?></a>
                        </div>
                    </div>

                    <div class="content">
                        <div class="desc <?=((count($relatedJobList) == 0 && count($recommendJobList) == 0) ? 'down' : '')?>">
                            <?=$detail['jobContent']?>
                        </div>
                        <div class="trigger"></div>
                    </div>
                </div>
                <?php endif;?>

                <?php foreach ($relatedJobList as $jobInfo) {?>
                <div class="list">
                    <div class="info">
                        <div class="top">
                            <a href="<?=$jobInfo['url']?>" title="<?=$jobInfo['jobName']?>" class="name" target="_blank"><?=$jobInfo['jobName']?></a>
                            <a class="company" href="<?=$jobInfo['companyUrl']?>" title="<?=$jobInfo['companyName']?>" target="_blank"><?=$jobInfo['companyName']?></a>
                        </div>
                        <div class="bottom">
                            <div class="aside">
                                <span class="salary"><?=$jobInfo['wage']?></span>
                                <span><?=$jobInfo['cardTagText']?></span>
                            </div>
                            <div class="type"><?=$jobInfo['companyTypeAndNatureText']?></div>
                        </div>
                    </div>

                    <div class="content">
                        <div class="desc">
                            <?php if($jobInfo['isShowDuty'] == 1):?>
                            岗位职责：
                            <br>
                            <?=$jobInfo['duty']?>
                            <?php endif;?>

                            <?php if($jobInfo['isShowRequirement'] == 1 && $jobInfo['isShowDuty'] == 1):?>
                            <div style="margin-top: 20px"></div>
                            <?php endif;?>

                            <?php if($jobInfo['isShowRequirement'] == 1):?>
                            任职要求：
                            <br>
                            <?=$jobInfo['requirement']?>
                            <?php endif;?>
                        </div>
                        <div class="trigger"></div>
                    </div>
                </div>
                <?php } ?>

            </div>

            <?php if(count($recommendJobList) > 0):?>
            <div class="more-wrapper">
                <div class="title-wrapper">
                    <div class="title">相关职位推荐</div>

                    <a class="arrow" href="<?=$detail['moreLink']?>" target="_blank">查看更多</a>
                </div>

                <div class="list-content">
                    <?php foreach ($recommendJobList as $jobInfo) {?>
                    <a href="<?=$jobInfo['url']?>" title="<?=$jobInfo['jobName']?>" class="list" target="_blank">
                        <div class="top">
                            <div class="name"><?=$jobInfo['jobName']?></div>
                            <div class="salary"><?=$jobInfo['wage']?></div>
                        </div>
                        <div class="tag-content">
                            <?php if($jobInfo['education']):?>
                            <span class="tag"><?=$jobInfo['education']?></span>
                            <?php endif;?>
                            <?php if($jobInfo['jobRecord']):?>
                            <span class="tag"><?=$jobInfo['jobRecord']?></span>
                            <?php endif;?>
                            <?php if($jobInfo['amount']):?>
                            <span class="tag"><?=$jobInfo['amount']?></span>
                            <?php endif;?>

                        </div>
                        <div class="bottom">
                            <div class="company"><?=$jobInfo['companyName']?></div>
                            <div class="address"><?=$jobInfo['cityName']?></div>
                        </div>
                    </a>
                    <?php } ?>
                </div>
            </div>
            <?php endif;?>
        </div>

        <div class="aside-content">

            <!-- 登录组件---挂件 -->
            <?php if(empty(Yii::$app->params['user'])):?>
            <?= frontendPc\components\RightLoginForm::widget() ?>
            <?php else:?>
            <?= frontendPc\components\DetailGuideCard::widget() ?>
            <?php endif;?>

            <div class="more-wrapper">
                <div class="title">更多词条推荐</div>

                <div class="content">
                    <?php foreach ($recommendJobWikiList as $wikiInfo) {?>
                    <a class="item" href="<?=$wikiInfo['jumpLink']?>" title="<?=$wikiInfo['title']?>" target="_blank"><?=$wikiInfo['title']?></a>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部挂架 -->
</div>

<script>
    $(function () {
        $('.main-wrapper .list-wrapper .list .desc:not(.down)').each(function () {
            const $this = $(this)
            // 六行高度 行高24px
            const packUpHeight = 144
            const height = $this.height()
            const isHide = height <= packUpHeight

            if (isHide) {
                $this.siblings('.trigger').remove()
            } else {
                $this.siblings('.trigger').show()
                $this.addClass('up')
            }
        })

        $('.main-wrapper .list-wrapper .trigger').click(function () {
            const $this = $(this)
            const $desc = $this.siblings('.desc')
            const isUp = $desc.hasClass('up')

            if (isUp) {
                $desc.removeClass('up')
                $desc.addClass('down')
            } else {
                $desc.removeClass('down')
                $desc.addClass('up')
            }
        })
    })
</script>