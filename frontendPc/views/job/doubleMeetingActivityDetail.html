<link rel="stylesheet" href="/static/css/doubleJobDetail.css?v=20241230">
<div id="component">
    <div class="el-main">
        <div class="detail-container">
            <div class="detail-header">
                <div class="detail-header-container">
                    <div class="breadcrumb">
                        位置：
                        <a href="/">高校人才网</a>
                        <?php if($info['columnInfo']):?>＞
                        <?php foreach($info['columnInfo'] as $item):?>
                        <a href="<?= $item['url'] ?>"><?= $item['name'] ?></a>＞
                        <?php endforeach;?>
                        <?php endif;?>
                        <a href="<?= $info['announcementUrl']?>"><?= $info['announcementTitle']?></a>＞
                        <a href="/job">场次列表</a>＞
                        场次详情
                    </div>

                    <div class="main">
                        <section>
                            <div class="title">
                                <h1 title="<?= \common\helpers\StringHelper::changeQuotationMark($info['jobName']) ?>"><?= $info['jobName'] ?></h1>
                            </div>
                            <div class="unit">
                                <a href="<?= $info['companyUrl']?>">
                                    <?= $info['companyName'] ?>
                                </a>
                            </div>

                            <div class="tips" id="tipsTemplate">
                                <?php if($info['area']): ?><span><?= $info['area'] ?></span><?php endif?>
                                <?php if($info['experience']): ?> <span><?= $info['experience'] ?></span><?php endif?>
                                <?php if($info['education']): ?><span><?= $info['education'] ?></span><?php endif?>
                                <?php if($info['amount']): ?><span><?= $info['amount'] ?>人</span><?php endif?>
                                <?php foreach ($info['welfareTagArr'] as $k => $welfareTag): ?>
                                <span class="boon"> <?= $welfareTag ?></span>
                                <?php endforeach; ?>
                                <?php if($info['moreWelfareTagArr']):?>
                                <el-popover placement="bottom" :width="430" trigger="hover" v-cloak>
                                    <template #reference>
                                        <i class="el-icon boon-more" style="--font-size: 12px;">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                                <path fill="currentColor"
                                                      d="M176 416a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224z">
                                                </path>
                                            </svg>
                                        </i>
                                    </template>
                                    <?php foreach ($info['moreWelfareTagArr'] as $k => $welfareTag): ?>
                                    <span class="boon"><?=$welfareTag?></span>
                                    <?php endforeach; ?>
                                </el-popover>
                                <?php endif;?>
                            </div>
                        </section>

                        <aside>
                            <div class="emit">
                                <!-- <div class="share-mini-code-container">
                                    <div class="share-mini-code-trigger share-mini-code-trigger--primary font-color">
                                        分享
                                    </div>

                                    <div class="share-mini-code-popup">
                                        <div class="share-mini-code">
                                            <img src="<?=$shareUrlCode?>" alt="" class="share-mini-code-img"/>
                                        </div>
                                        <div class="share-mini-code-title">微信扫一扫</div>
                                        <div class="share-mini-code-tips">分享给你的朋友吧</div>
                                    </div>
                                </div> -->

                                <?php if($info['status'] == 0): ?>
                                <button class="el-button el-button--info is-plain is-disabled" disabled>
                                    <span>已下线</span>
                                </button>
                                <?php else:?>
                                <?php if($info['applyStatus'] == 1):?>
                                <button class="el-button el-button--primary is-disabled" disabled>
                                    <span>已报名</span>
                                </button>
                                <?php else:?>
                                <button class="el-button el-button--primary job-apply-button">
                                    <span>立即报名</span>
                                </button>
                                <?php endif;?>

                                <?php endif;?>
                            </div>
                            <p class="tips">
                                <!-- <span class="view">浏览次数：<?=$info['click']?>次</span> -->
                                <!-- <span class="share">分享</span> -->
                            </p>

                        </aside>
                        <?php if(!empty($info['announcementId'])):?>
                        <a class="el-button el-button--primary view-button" href="<?=$info['announcementUrl']?>">
                            <span>查看招聘会详情</span>
                        </a>
                        <?php endif;?>
                    </div>
                </div>
            </div>

            <div class="detail-main">
                <section>
                    <div class="detail-subtitle">本场活动详情</div>
                    <div class="detail-list">
                        <div class="detail-item">
                            <div class="detail-title">基本信息</div>
                            <div class="detail-content el-row">
                                <?php if($info['jobCategory']): ?>
                                <div class="el-col">职位类型：<a class="color-primary"
                                                                href="<?= $info['jobTypeUrl']?>"><?= $info['jobCategory'] ?></a>
                                </div>
                                <?php endif;?>
                                <?php if($info['fullArea']): ?>
                                <div class="el-col" title="<?= \common\helpers\StringHelper::changeQuotationMark($info['fullArea']) ?>">
                                    招聘会地点：<?= $info['fullArea'] ?></div>
                                <?php endif;?>
                                <?php if($info['amount']): ?>
                                <div class="el-col">招聘人数： <?= $info['amount'] ?></div>
                                <?php endif;?>
                                <?php if(!empty($info['applyTypeText'])):?>
                                <div class="el-col">报名方式：<?= $info['applyTypeText']?></div>
                                <?php endif;?>
                                <div class="el-col">截止时间：<?=$info['periodDate']?></div>
                                <?php if(!empty($info['department'])):?>
                                <div class="el-col">用人部门：<a class="color-primary"
                                                                href="<?= $info['jobWorkUrl']?>"><?= $info['department'] ?></a>
                                </div>
                                <?php endif;?>
                                <?php if(!empty($info['jobNature'])):?>
                                <div class="el-col">工作性质：<?= $info['jobNature'] ?></div>
                                <?php endif;?>
                                <?php if($info['isCooperation'] == 'true' && $info['isOnlineApply'] == 'true' && $info['isEmailApply'] != 'true'):?>
                                <!--                                <div class="attention">-->
                                <!--                                    <p>注意：该单位要求通过单位招聘系统进行简历投递。当前页面投递(站内投递)，单位可能不做处理，请您知悉。</p>-->
                                <!--                                    <a href="<?= $info['onlineApply'] ?>" target="_blank">></a></div>-->
                                <?php endif;?>
                                <?php if($info['isCooperation'] == 'true' && $info['isOtherApply'] == 'true' && $info['isEmailApply'] != 'true' && $info['isOnlineApply'] != 'true'):?>
                                <div class="attention">
                                    <p>注意：该单位要求通过<?= $info['otherTxt'] ?>
                                        形式进行简历投递。当前页面投递(站内投递)，单位可能不做处理，请您知悉。</p>
                                </div>
                                <?php endif;?>
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-title">其他要求</div>
                            <div class="detail-content el-row">
                                <?php if($info['education']): ?>
                                <div class="el-col">学历要求：<?= $info['education'] ?></div>
                                <?php endif;?>
                                <?php if($info['title']):?>
                                <div class="el-col">职称要求：<?= $info['title'] ?></div>
                                <?php endif;?>
                                <?php if($info['experience']):?>
                                <div class="el-col">工作经验：<?= $info['experience'] ?></div>
                                <?php endif;?>
                                <?php if($info['age']):?>
                                <div class="el-col">年龄要求：<?= $info['age'] ?></div>
                                <?php endif;?>
                                <?php if($info['abroad']):?>
                                <div class="el-col">海外经历：<?= $info['abroad'] ?></div>
                                <?php endif;?>
                                <?php if($info['political']):?>
                                <div class="el-row">
                                    <div class="el-col">政治面貌：<?= $info['political'] ?></div>
                                </div>
                                <?php endif;?>
                                <?php if($info['major']): ?>
                                <div class="el-row">
                                    <div class="el-col show-complete" title="<?= \common\helpers\StringHelper::changeQuotationMark($info['majorTxt']) ?>">
                                        <span class="label">需求专业：</span>
                                        <div class="value">
                                            <?php foreach($info['major'] as $list){?>
                                            <a class="color-primary" href="<?=$list['url']?>" target="_blank"><?php echo $list['majorText'];?></a>
                                            <?php }?>
                                        </div>
                                    </div>
                                </div>
                                <?php endif;?>
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-title">活动详情</div>
                            <div class="detail-content single">
                                <?= $info['duty'] ?>
                            </div>
                        </div>
                        <?php if($info['remark']):?>
                        <div class="detail-item">
                            <div class="detail-title">其他说明</div>
                            <div class="detail-content single">
                                <?= $info['remark'] ?>
                            </div>
                        </div>
                        <?php endif;?>

                    </div>

                    <?php if(!empty($info['fileList'])):?>
                    <div class="detail-subtitle">附件下载</div>
                    <div class="file-list">
                        <?php foreach($info['fileList'] as $item):?>
                        <el-popover placement="bottom" :width="300" trigger="hover" content="<?=$item['name']?>">
                            <template #reference>
                                <a href="<?=$item['path']?>" download="<?=$item['name']?>" class="file" target="_blank">
                                    <div class="<?=$item['suffix']?>"></div>
                                    <div class="file-name"><?=$item['name']?></div>
                                </a>
                            </template>
                        </el-popover>
                        <?php endforeach;?>
                    </div>
                    <?php endif;?>

                    <div class="detail-emit">
                        <?php if(!empty($info['announcementId'])):?>
                        <a class="el-button el-button--primary" href="<?=$info['announcementJobUrl']?>">查看其他场次</a>
                        <a class="el-button el-button--primary" href="<?=$info['announcementUrl']?>">查看招聘会详情</a>
                        <?php endif;?>

                        <?php if($info['status'] == 0): ?>
                        <button class="el-button el-button--info is-plain is-disabled" disabled>
                            <span>已下线</span>
                        </button>
                        <?php else:?>
                        <?php if($info['applyStatus'] == 1):?>
                        <button class="el-button el-button--primary is-disabled" disabled>
                            <span>已报名</span>
                        </button>
                        <?php else:?>
                        <button class="el-button el-button--primary job-apply-button">
                            <span>立即报名</span>
                        </button>
                        <?php endif;?>

                        <?php endif;?>
                    </div>

                    <div class="qr-code">
                        <div class="qr-tips">扫下方二维码加入本场微信群，随时了解招聘会最新动态。</div>
                        <img class="code" src="https://img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/8.jpg" alt="">
                    </div>
                    <!-- <div class="share-custom">
                        <div class="sina-weibo">
                            <html xmlns:wb="http://open.weibo.com/wb">
                            <wb:follow-button uid="3702192203" type="red_3" width="100%" height="24">
                            </wb:follow-button>
                        </div>
                        <div>
                            <div class="bshare-custom">
                                <a title="分享到微信" class="bshare-weixin"></a>
                                <a title="分享到新浪微博" class="bshare-sinaminiblog"></a>
                                <a title="分享到QQ空间" class="bshare-qzone"></a>
                                <a title="分享到Facebook" class="bshare-facebook"></a>
                                <a title="分享到Twitter" class="bshare-twitter"></a>
                                <a title="更多平台" onclick="javascript:bShare.more(event);return false;" class="custom-more-btn">
                                </a>
                                <span class="BSHARE_COUNT bshare-share-count">0</span>
                            </div>
                            <script
                                src="http://static.bshare.cn/b/buttonLite.js#style=-1&amp;uuid=40cb6f46-7685-42c6-8cf8-7e18be117d11&amp;pophcol=1&amp;lang=zh"></script>
                            <script src="http://static.bshare.cn/b/bshareC0.js"></script>
                        </div>
                    </div> -->
                </section>

                <aside>
                    <div class="unit">
                        <a href="http://www.gaoxiaojob.com/zhaopin/zhuanti/yca_bsqdjobfair2022/index.html" target="_blank">
                            <div class="unit-name">
                                <img class="logo" src="<?= $info['logo'] ?>" alt="">
                                <div class="name">
                                    <h3><?=$info['companyName'] ?></h3>
                                    <h5> <?=$info['englishName'] ?></h5>
                                </div>
                            </div>
                            <div class="unit-tips category"><?= $info['industry'] ?></div>
                            <div class="unit-tips number"><?= $info['scale'] ?></div>
                            <div class="unit-tips type"> <?=$info['type']?> . <?= $info['nature'] ?></div>

                            <div class="unit-data el-row">
                                <div class="el-col el-col-8">
                                    <strong class="color-primary">16</strong>
                                    <span>招聘场次</span>
                                </div>
                                <div class="el-col el-col-8">
                                    <strong class="color-primary">500+</strong>
                                    <span>参会单位</span>
                                </div>
                                <div class="el-col el-col-8">
                                    <strong class="color-primary">2000+</strong>
                                    <span>招聘职位</span>
                                </div>
                            </div>
                        </a>
                    </div>
                    <span style="display: none">read:<?=$info['click']?></span>
                    <?php if(empty(Yii::$app->params['user'])):?>
                    <?= frontendPc\components\RightLoginForm::widget() ?>
                    <?php endif;?>
                </aside>
            </div>
        </div>
    </div>
</div>

<script src="https://tjs.sjs.sinajs.cn/open/api/js/wb.js" type="text/javascript" charset="utf-8"></script>

<script>
    $(function () {
        Vue.createApp({}).use(ElementPlus).mount('#tipsTemplate')
        Vue.createApp({}).use(ElementPlus).mount('.file-list')

        var jobId = "<?= $info['jobId'] ?>"
        var $applyButtons = $('.job-apply-button')
        let checkApi = '/api/person/member/check-user-apply-status'
        let applyApi = '/api/person/job/apply'

        $applyButtons.on('click', function () {
            window.globalComponents.activityDialogApplyJob.beforeApply(jobId, function () {
                $applyButtons.prop('disabled', true).addClass('is-disabled').find('span').text('已报名')
            })

        })
    });
</script>
