<?php

namespace frontendPc\controllers\api\person;

use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeComplete;
use common\base\models\BaseSeoUserAgent;
use common\libs\JwtAuth;
use frontendPc\controllers\api\BaseFrontPcApiController;
use frontendPc\models\Member;
use Yii;

class BaseFrontPcApiPersonController extends BaseFrontPcApiController
{

    public string $errorResult = '';

    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }
        if (in_array($action->uniqueID, $this->ignoreLogin())) {
            return parent::beforeAction($action);
        }

        // 判断用户是否完成简历完善了
        $this->checkAuth($action->uniqueID);
        if (!empty($this->errorResult)) {
            echo $this->errorResult;
            exit;
        }

        return parent::beforeAction($action);
    }

    public function ignoreLogin()
    {
        return [
            'api/person/member/save-email-account',
            'api/person/member/get-vip-filter-info',
            'api/person/home/<USER>',
            'api/person/resume/upload',
            'api/person/resume/view-resume',
            'api/person/home/<USER>',
            'api/person/job/search',
            'api/person/job/detail',
            'api/person/job/get-data-list',
            'api/person/job/test',
            'api/person/company/get-announcement-list',
            'api/person/company/get-job-list',
            'api/person/job/get-recommend-list',

            // 生成登录qrcode
            'api/person/member/login-qrcode',
            'api/person/announcement/get-recommend-list',
            'api/person/job/get-recommend-list',
            // 支付回调
            'api/person/resume-order/notify',
            // 订单状态修改
            'api/person/resume-order/update-order',
            // 购买权益组合查询
            'api/person/resume-equity-package/get-buy-package-list',
            // 购买权益组合查询(活动)
            'api/person/resume-equity-package/get-activity-buy-package-list',
            'api/person/company/get-activity-list',
            'api/person/company/get-job-list-html',
            'api/person/company/get-announcement-list-html',
            // 购买更多包
            'api/person/resume-equity-package/get-more-package',
            // 关闭弹窗
            'api/person/resume-equity-package/close-package',
            'api/person/resume/get-login-pop-tips',
            'api/person/resume/add-login-pop-tips-amount',
            // 取消注销
            'api/person/resume/cancel-apply-cancel',

        ];
    }

    public function getPerfectResumeList()
    {
        return [
            //获取用户基本信息
            'api/person/resume/get-user-base-info',
            //保存用户基本信息
            'api/person/resume/save-user-base-info',
            //获取简历第二步信息
            'api/person/resume/get-step-two-info',
            //保存简历第二步信息
            'api/person/resume/save-step-two-info',
            //获取简历第三步信息
            'api/person/resume/get-step-three-info',
            //保存简历第三步信息
            'api/person/resume/save-step-three-info',
            //获取简历第三步下拉框信息
            'api/person/resume/get-step-three-params',
            //发送绑定手机号验证码
            'api/person/home/<USER>',
            //验证绑定手机号验证码
            'api/person/home/<USER>',
            //更新用户状态
            'api/person/resume/update-user-resume-status',
            //获取用户信息
            'api/person/member/get-user-info',
        ];
    }

    /**
     * 获取通用接口列表(未完成前三部也可以用的)
     */
    public function getCurrencyApiList()
    {
        return [
            //保存学术论文
            'api/person/resume-academic-page/save',
            //删除学术论文
            'api/person/resume-academic-page/del',
            //保存学术专利
            'api/person/resume-academic-patent/save',
            //删除学术专利
            'api/person/resume-academic-patent/del',
            //保存学术专著
            'api/person/resume-academic-book/save',
            //删除学术专著
            'api/person/resume-academic-book/del',
            //保存学术奖励信息
            'api/person/resume-academic-reward/save',
            //删除学术奖励信息
            'api/person/resume-academic-reward/del',
            //保存其他奖励信息
            'api/person/resume-other-reward/save',
            //删除其他奖励信息
            'api/person/resume-other-reward/del',
            //保存资质证书
            'api/person/resume-certificate/save',
            //删除资质证书
            'api/person/resume-certificate/del',
            //保存技能语言
            'api/person/resume-skill/save',
            //删除技能语言
            'api/person/resume-skill/del',
            //获取用户信息
            'api/person/member/get-user-info',
            //获取简历第四步信息
            'api/person/resume/get-step-four-info',
            //保存简历第四步信息
            'api/person/resume/save-step-four-info',
            //获取简历完成度
            'api/person/resume/get-complete-percent',
            'api/person/company/collect',
            'api/person/job/collect',
            'api/person/announcement/collect',
            // 绑定qrcode
            'api/person/member/bind-qrcode',
            //检查用户是否可以投递
            'api/person/job/check-member-complete-status',
            // 获取用户强提醒
            'api/person/resume-remind/get-all',
            // 用户动态
            'api/person/resume/job-trend',
            // 弹窗权益组合查询
            'api/person/resume-equity-package/get-popup-package-list',
            // 弹窗权益组合查询(活动)
            'api/person/resume-equity-package/get-activity-popup-package-list',
            // 支付拉起
            'api/person/resume-order/pay',
            // 支付查询
            'api/person/resume-order/query',
            // 公告报告入口校验
            'api/person/announcement/check-generate-report',
            // 职位报告入口校验
            'api/person/job/check-generate-report',
            // 公告报告创建
            'api/person/announcement/create-report',
            // 职位报告创建
            'api/person/job/create-report',
            // 我的服务
            'api/person/resume-equity/get-my-services',
            'api/person/resume-equity/get-my-services-top-tips',
            // 购买记录
            'api/person/resume-order/get-list',
            // 求职工具
            'api/person/resume-equity/get-job-tools',
            // 职位报告
            'api/person/job/get-report-record-list',
            // 公告报告
            'api/person/announcement/get-report-record-list',
            // 消息中心
            'api/person/member/get-head-nav',
            // 微信绑定检查
            'api/member/check-bind-qrcode',
            // 求职资源包获取
            'api/person/resume-equity/get-job-resources',
            //用户PV
            'api/person/resume/pv-exposure',
            //置顶设置
            'api/person/resume/check-resume-top',
            'api/person/resume/validate-resume-top',
            'api/person/resume/add-resume-top',
            // 创建聊天室
            'api/person/chat/create-room',
            'api/person/member/check-user-apply-status',
            'api/person/announcement/check-announcement-apply',
            'api/person/resume-equity/get-services-expire-pop-info',
            'api/person/new-resume-activity/create',
            'api/person/job/apply',
        ];
    }

    /**
     * 判断用户状态步数的url
     * @return string[]
     */
    public function checkUserStatusList()
    {
        return [];
    }

    /**
     * 根据情况来检查这个用户有没有权限操作
     */
    public function checkAuth($actionId)
    {
        if (Yii::$app->user->isGuest) {
            $this->setErrorResult();
        }
        //登录时缓存company表status字段，初审通过/终审通过需要更新缓存
        $info   = BaseMember::getLoginInfo();
        $status = $info['status'];
        $type   = $info['type'];
        if ($type != Member::TYPE_PERSON) {
            $this->setErrorResult();
        }
        //先判断连接是否是特殊的，需要返回用户步数的
        if (in_array($actionId, $this->checkUserStatusList())) {
            //获取用户状态
            $resumeId     = BaseMember::getMainId($info['id']);
            $resumeStatus = BaseResume::findOneVal(['id' => $resumeId], 'status');
            //状态是未完成的，获取步数
            if ($resumeStatus == BaseResume::STATUS_WAIT_AUDIT) {
                $resumeStepNum = BaseResumeComplete::getResumeStep($resumeId);
                $this->setErrorResult(301, 0, (int)$resumeStepNum);
            }
        } else {
            if ($status == Member::STATUS_ACTIVE) {
                // 正常用户
                if (in_array($actionId, $this->getPerfectResumeList()) && !in_array($actionId,
                        $this->getCurrencyApiList())) {
                    $this->setErrorResult();
                }
            }
            if ($status == Member::STATUS_WAIT_PERFECT_RESUME) {
                //未完成简历模块的用户
                //判断当前路由是否可以访问
                if (!in_array($actionId, $this->getPerfectResumeList()) && !in_array($actionId,
                        $this->getCurrencyApiList())) {
                    $this->setErrorResult();
                }
            }
        }
    }

    /**
     * 设置错误返回信息
     * @param $code
     * @param $result
     * @param $msg
     * @return void
     */
    public function setErrorResult($code = 403, $result = 0, $msg = '非法操作')
    {
        $rel = [
            'code'   => $code,
            'result' => $result,
        ];
        if (is_string($msg)) {
            $rel['msg'] = $msg;
        } elseif (is_int($msg)) {
            //前端要求，此处返回data
            $rel['data'] = $msg;
        }

        $this->errorResult = json_encode($rel);
    }

    public function getResumeId()
    {
        $memberId = Yii::$app->user->id;
        $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id');

        return $resumeId;
    }

}