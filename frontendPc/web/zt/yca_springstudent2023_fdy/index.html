<?php
/* @var $this \yii\web\View */

/* @var $content string */

use yii\helpers\Html;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>春招极速招聘活动-高校人才网</title>
    <meta name="keywords" content="应届生求职,应届生春招,招聘活动,硕士进高校,高校就业">
    <meta name="description"
          content="为减少人才的求职等待，提升求职体验，高校人才网特别推出「春招极速招聘」活动，联合600+高校、科研、中小学及医疗单位，力求让你的求职过程“速战速决”！活动期间，热招公告实时更新，快来探索适合你的职位吧！">
    <meta name="applicable-device" content="pc">
    <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon">
    <?=\common\helpers\HtmlHelper::canonicalHtml()?>
    <link rel="stylesheet" href="//img.gaoxiaojob.com/uploads/static/lib/normalize/normalize.min.css?v=0.1">
    <link rel="stylesheet" href="//img.gaoxiaojob.com/uploads/static/lib/element-plus/index.min.css"/>
    <link rel="stylesheet" href="//img.gaoxiaojob.com/uploads/static/lib/swiper/swiper.min.css">
    <link rel="stylesheet" href="/static/css/common.css?t=20240802">
    <script src="//img.gaoxiaojob.com/uploads/static/lib/vue/vue.min.js"></script>
    <script src="//img.gaoxiaojob.com/uploads/static/lib/element-plus/index.min.js?v=0.1"></script>
    <script src="//img.gaoxiaojob.com/uploads/static/lib/qs/qs.min.js"></script>
    <script src="//img.gaoxiaojob.com/uploads/static/lib/axios/axios.min.js?v=0.1"></script>
    <script src="//img.gaoxiaojob.com/uploads/static/lib/jquery/jquery.min.js"></script>
    <script src="//img.gaoxiaojob.com/uploads/static/lib/swiper/swiper.min.js?v=0.1"></script>
    <script src="/static/js/config.js"></script>
    <script src="/static/js/public.js"></script>
    <script src="/static/js/request.js"></script>
    <script src="/static/js/common.js"></script>
    <link rel="stylesheet" href="<?=$webFile?>/libs/swiper/index.min.css"/>
    <link rel="stylesheet" href="<?=$webFile?>/css/index.min.css"/>
</head>

<body>
<!-- header start -->
<header class="header-container">
    <div class="header-content">
        <a href="/" class="company-logo"></a>

        <nav>
            <a href="/" target="_blank" class="nav-active">首页</a>
            <a href="/zhaopin/zhuanti/zt_jiheye/index.html" target="_blank">往期专题</a>
            <a href="/column/10.html" target="_blank">热门资讯</a>
            <a href="#" target="_blank">招聘活动</a>
        </nav>
    </div>

    <div class="letter-container">
        <div class="letter-border">
            <div class="letter-content">
                <p>
                    <span>投递简历总是石沉大海，不知道简历是否被查看，不甘心继续等又怕浪费时间？投递简历后的等待是求职过程中最焦虑的时刻，怕等不到结果，更怕接了其他offer后才等到结果。</span>
                </p>
                <p class="font-big">
                    为减少人才的求职等待，提升求职体验，高校人才网特别推出<span>「春招极速招聘」</span>活动，<span>联合600+高校、科研、中小学及医疗单位，</span>力求让你的求职过程<span>“速战速决”</span>！活动期间，热招公告<span>实时更新</span>，点击立即投递即可申请心仪职位。优质单位<span>覆盖全国各地，学科专业需求繁多</span>，快来探索适合你的职位吧！
                </p>
            </div>
        </div>
    </div>
</header>
<!-- header end -->

<!-- main start -->
<section class="main-container">
    <div class="hot-container">
        <img src="<?=$webFile?>/assets/title-first.png" alt="热招公告"/>

        <div class="notice-content">
            <div class="location">
                <div class="location-content active">华东地区</div>
                <div class="location-content">华中、华南地区</div>
                <div class="location-content">华北、东北地区</div>
                <div class="location-content">西南、西北地区</div>
            </div>

            <div class="company">
                <div class="swiper company-content firstSwiper active">
                    <div class="swiper-wrapper">
                        <?php foreach ($list[0] as $k => $v) { ?>
                        <div class="swiper-slide company-item">   
                            <a href="javascript:;" onclick="showDetail(<?=$v['id'];?>)" class="content-header">
                                <div class="company-info">
                                    <img src="<?=$v['logo'];?>" alt="<?=$v['title'];?>"/>
                                    <div class="company-title">
                                        <h3><?=$v['title'];?></h3>
                                    </div>
                                </div>
                                <div class="position-info">
                                    <p title="<?=$v['categoryText'];?>">职位类型：<?=$v['categoryText'];?></p>
                                    <p title="<?=$v['educationText'];?>">学历要求：<?=$v['educationText'];?></p>
                                    <p title="<?=$v['majorText'];?>">需求专业：<?=$v['majorText'];?></p>
                                </div>
                            </a>

                            <div class="function-btn">
                                <a href="javascript:;" onclick="showDetail(<?=$v['id'];?>)">公告详情</a>
                                <a href="javascript:;" onclick="apply(<?=$v['id'];?>)">立即投递</a>
                            </div>
                        </div>
                        <?php } ?>
                    </div>

                    <div class="function-bar">
                        <div class="swiper-button-prev"></div>
                        <div class="swiper-pagination"></div>
                        <div class="swiper-button-next"></div>
                    </div>
                </div>

                <div class="swiper company-content secondSwiper">
                    <div class="swiper-wrapper">
                        <?php foreach ($list[1] as $k => $v) { ?>
                        <div class="swiper-slide company-item">
                            <a href="javascript:;" onclick="showDetail(<?=$v['id'];?>)" class="content-header">
                                <div class="company-info">
                                    <img src="<?=$v['logo'];?>" alt="<?=$v['title'];?>"/>
                                    <div class="company-title">
                                        <h3><?=$v['title'];?></h3>
                                    </div>
                                </div>
                                
                                <div class="position-info">
                                    <p title="<?=$v['categoryText'];?>">职位类型：<?=$v['categoryText'];?></p>
                                    <p title="<?=$v['educationText'];?>">学历要求：<?=$v['educationText'];?></p>
                                    <p title="<?=$v['majorText'];?>">需求专业：<?=$v['majorText'];?></p>
                                </div>
                            </a>

                            <div class="function-btn">
                                <a href="javascript:;" onclick="showDetail(<?=$v['id'];?>)">公告详情</a>
                                <a href="javascript:;" onclick="apply(<?=$v['id'];?>)">立即投递</a>
                            </div>
                        </div>
                        <?php } ?>
                    </div>

                    <div class="function-bar">
                        <div class="swiper-button-prev"></div>
                        <div class="swiper-pagination"></div>
                        <div class="swiper-button-next"></div>
                    </div>
                </div>

                <div class="swiper company-content thirdSwiper">
                    <div class="swiper-wrapper">
                        <?php foreach ($list[2] as $k => $v) { ?>
                        <div class="swiper-slide company-item">
                            <a href="javascript:;" onclick="showDetail(<?=$v['id'];?>)" class="content-header">
                                <div class="company-info">
                                    <img src="<?=$v['logo'];?>" alt="<?=$v['title'];?>"/>
                                    <div class="company-title">
                                        <h3><?=$v['title'];?></h3>
                                    </div>
                                </div>

                                <div class="position-info">
                                    <p title="<?=$v['categoryText'];?>">职位类型：<?=$v['categoryText'];?></p>
                                    <p title="<?=$v['educationText'];?>">学历要求：<?=$v['educationText'];?></p>
                                    <p title="<?=$v['majorText'];?>">需求专业：<?=$v['majorText'];?></p>
                                </div>
                            </a>

                            <div class="function-btn">
                                <a href="javascript:;" onclick="showDetail(<?=$v['id'];?>)">公告详情</a>
                                <a href="javascript:;" onclick="apply(<?=$v['id'];?>)">立即投递</a>
                            </div>
                        </div>
                        <?php } ?>
                    </div>

                    <div class="function-bar">
                        <div class="swiper-button-prev"></div>
                        <div class="swiper-pagination"></div>
                        <div class="swiper-button-next"></div>
                    </div>
                </div>

                <div class="swiper company-content forthSwiper">
                    <div class="swiper-wrapper">
                        <?php foreach ($list[3] as $k => $v) { ?>
                        <div class="swiper-slide company-item">
                            <a href="javascript:;" onclick="showDetail(<?=$v['id'];?>)" class="content-header">
                                <div class="company-info">
                                    <img src="<?=$v['logo'];?>" alt="<?=$v['title'];?>"/>
                                    <div class="company-title">
                                        <h3><?=$v['title'];?></h3>
                                    </div>
                                </div>

                                <div class="position-info">
                                    <p title="<?=$v['categoryText'];?>">职位类型：<?=$v['categoryText'];?></p>
                                    <p title="<?=$v['educationText'];?>">学历要求：<?=$v['educationText'];?></p>
                                    <p title="<?=$v['majorText'];?>">需求专业：<?=$v['majorText'];?></p>
                                </div>
                            </a>

                            <div class="function-btn">
                                <a href="javascript:;" onclick="showDetail(<?=$v['id'];?>)">公告详情</a>
                                <a href="javascript:;" onclick="apply(<?=$v['id'];?>)">立即投递</a>
                            </div>
                        </div>
                        <?php } ?>
                    </div>

                    <div class="function-bar">
                        <div class="swiper-button-prev"></div>
                        <div class="swiper-pagination"></div>
                        <div class="swiper-button-next"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="new-container">
        <img src="<?=$webFile?>/assets/title-first-second.png" alt="最新发布"/>

        <div class="swiper newSwiper notice-content">
            <div class="swiper-wrapper">
                <?php foreach ($list[4] as $item ) { ?>
                <div class="swiper-slide">
                    <?php foreach ($item as $v ) { ?>
                    <div class="new-item">
                        <div class="new-info">
                            <p class="new-title"><?=$v['title']?></p>
                            <p class="new-location"><?=$v['city']?></p>
                        </div>
                        <div class="new-btn">
                            <a href="javascript:;" onclick="showDetail(<?=$v['id']?>)">公告详情</a>
                            <a href="javascript:;" onclick="apply(<?=$v['id']?>)">立即投递</a>
                        </div>
                    </div>
                    <?php } ?>
                </div>
                <?php } ?>
            </div>
            
            <div class="function-bar">
                <div class="swiper-button-prev"></div>
                <div class="swiper-pagination"></div>
                <div class="swiper-button-next"></div>
            </div>
        </div>
    </div>

    <div class="join-container">
        <img src="<?=$webFile?>/assets/title-second.png" alt="参与方式"/>

        <div class="join-content">
            <img src="<?=$webFile?>/assets/join-img1.png" alt=""/>
            <img src="<?=$webFile?>/assets/join-img2.png" alt=""/>
            <img src="<?=$webFile?>/assets/join-img3.png" alt=""/>
            <img src="<?=$webFile?>/assets/join-img4.png" alt=""/>
        </div>
    </div>

    <div class="tip-container">
        <img src="<?=$webFile?>/assets/title-third.png" alt="注意事项"/>

        <div class="tip-content">
            <p>本期职位的活动时间为<strong>3月1日-5月31日</strong>，招聘人数有限，如有意向职位请<strong>尽快投递</strong>
            </p>
            <p>为提高求职成功率，请<strong>提前完善在线简历并上传相关附件材料</strong>，让单位更全面了解你</p>
            <p>
                如您对活动参与方式有疑问，或有其他问题需要咨询反馈，
                <strong
                >点击此处联系我们
                    <span>
                                <img src="<?=$webFile?>/assets/code.jpg" alt=""/>
                                <i>微信扫码添加高才小助手</i>
                            </span>
                </strong>
            </p>
            <img src="<?=$webFile?>/assets/tips-bg.png" alt=""/>
        </div>
    </div>

    <div class="preview-container">
        <img src="<?=$webFile?>/assets/title-forth.png" alt="场次预告"/>

        <div class="preview-content">
            <table>
                <thead>
                <tr>
                    <td>活动场次名称</td>
                    <td>活动亮点</td>
                </tr>
                </thead>
                <tr>
                    <td><a href="/z/yca_springstudent2023.html">普通本科院校专场</a></td>
                    <td><a href="/z/yca_springstudent2023.html">公办院校，硕博可报</a></td>
                </tr>
                <tr>
                    <td><a href="/z/yca_springstudent2023_fdy.html">辅导员专场</a></td>
                    <td><a href="/z/yca_springstudent2023_fdy.html">硕士进高校最优选</a></td>
                </tr>
                <tr>
                    <td><a href="/z/yca_springstudent2023_zrjs.html">专任教师专场</a></td>
                    <td><a href="/z/yca_springstudent2023_zrjs.html">安家费、科研经费等福利众多</a></td>
                </tr>
                <tr>
                    <td><a href="/z/yca_springstudent2023_yxs.html">医院及医疗机构专场</a></td>
                    <td><a href="/z/yca_springstudent2023_yxs.html">部分有编，待遇丰厚</a></td>
                </tr>
                <tr>
                    <td><strong>更多专场筹备中，敬请期待！</strong></td>
                </tr>
            </table>
        </div>
    </div>

    <div class="to-top">返回顶部</div>
</section>
<!-- main end -->

<!-- footer start -->
<footer class="footer-container">
    <div class="footer-content-box">
        <div class="footer-left">
            <ul class="footer-nav">
                <li><a href="/zhaopin/aboutUs/index.html" rel="nofollow">关于我们</a></li>
                <li><a href="/zhaopin/aboutUs/contactUs.html" rel="nofollow">联系我们</a></li>
                <li><a href="/zhaopin/aboutUs/joinUs.html" rel="nofollow">人才招聘</a></li>
                <li><a href="/zhaopin/aboutUs/productService.html" rel="nofollow">产品服务</a></li>
                <li><a href="/zhaopin/aboutUs/disclaimers.html" rel="nofollow">免责声明</a></li>
                <li><a href="/data/sitemap.html">网站导航</a></li>
            </ul>
            <p class="num-one">Copyright &copy; 2007-<?= date('Y') ?> 高校人才网 粤ICP备13048400号 粤公网安备：44010602004138号
                本站由广州高才信息科技有限公司运营</p>
            <p>中华人民共和国增值电信业务经营许可证：粤B2-20180648 人力资源服务许可证编号：440106160023
                企业统一社会信用代码：91440106MA59BTXW56</p>
            <p>
                高校人才网——国内访问量、信息量领先的高层次人才需求信息平台
                <span id="busuanzi_container_page_pv">-SP-<b id="busuanzi_value_page_pv"></b></span>
            </p>
        </div>

        <div class="footer-right">
            <img src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png" alt="高校人才网"/>
        </div>
    </div>
</footer>
<!-- footer end -->
</body>
</html>
<?= \frontendPc\components\DialogLogin::widget()?>
<?= \frontendPc\components\DialogApplyJob::widget()?>
<?= \frontendPc\components\DialogActivityApplyJob::widget()?>

<script src="/static/js/tongji.js"></script>
<script src="<?=$webFile?>/libs/jquery/index.min.js"></script>
<script src="<?=$webFile?>/libs/swiper/index.min.js"></script>
<script src="<?=$webFile?>/js/index.js"></script>
<script src="https://www.layuicdn.com/layui-v2.7.6/layui.js"></script>
<script>
    var apply = function (id) {
        // 先出loading
        window.globalComponents.applyDialogComponent.announcementApply(id)
    }

    var showDetail = function (id) {
        var loadingIndex = layer.load(1)
        $.ajax({
            url: `https://api.gaoxiaojob.com/announcement/get-detail?id=${id}`,
            type: 'get',
            success: function (data) {
                var html = `https://api.gaoxiaojob.com/announcement/get-html?id=${id}`
                var title = data.data.title
                layer.open({
                    type: 2,
                    title: title,
                    content: html,
                    area: ['1200px', '700px'],
                    success: function (layero) {
                        var title = layero.find('.layui-layer-title')
                        title.css({'text-align': 'center', 'font-weight': 'blod', 'font-size': '18px'})
                        layer.close(loadingIndex)
                    }
                })
            }
        })
    }
</script>