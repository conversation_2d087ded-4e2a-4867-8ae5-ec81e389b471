!(function () {
    let swiperArr = ['first', 'second', 'third', 'forth']

    swiperArr.forEach((item) => {
        new Swiper(`.${item}Swiper`, {
            slidesPerView: 3,
            grid: {
                fill: 'row',
                rows: 2
            },
            slidesPerGroup: 3,
            spaceBetween: 25,
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev'
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true
            }
        })
    })

    new Swiper('.newSwiper', {
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev'
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
            renderBullet: function (index, className) {
                return '<span class="' + className + '">' + (index + 1) + '</span>'
            }
        },
        simulateTouch: false
    })

    $('.location-content').on('click', function () {
        let index = $(this).index()
        $(this).siblings().removeClass('active')
        $(this).addClass('active')
        $('.company-content').siblings().removeClass('active')
        $('.company-content').eq(index).addClass('active')
    })

    $('.company-item').on('mouseover', function () {
        $(this).css('transform', 'translateY(-5px)')
        $(this).css('transition', 'all 0.3s ease')
    })

    $('.company-item').on('mouseleave', function () {
        $(this).css('transform', 'none')
    })

    $('.to-top').on('click', function () {
        $('html,body').animate({ scrollTop: 0 }, 500)
    })
})()
