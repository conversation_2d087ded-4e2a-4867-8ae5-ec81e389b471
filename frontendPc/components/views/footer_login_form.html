<link rel="stylesheet" href="/static/css/loginPanel.css">

<div id="loginPanelTemplate" class="login-panel-template" v-cloak>
    <div class="login-panel-container" v-if="visible">
        <el-form ref="mobileLoginRef" :model="mobileLoginForm" :rules="mobileLoginRules">
            <el-form-item prop="mobile" :show-message="false">
                <el-input
                        class="mobile"
                        v-model="mobileLoginForm.mobile"
                        oninput="value=value.replace(/[^0-9]/g, '')"
                        name="gaoxiaojobMobile"
                        autocomplete="on"
                        placeholder="请输入手机号"
                        :maxlength="mobileMaxLength"
                >
                    <!-- 号段 -->
                    <template #prepend>
                        <el-select v-model="mobileLoginForm.mobileCode" popper-class="panel-mobile-prefix-popper" class="mobile-prefix-select">
                            <el-option-group v-for="{ type, list } in prefixOptions" :key="type" :label="type">
                                <el-option v-for="{ country, code } in list" :key="code" :value="code">
                                    <span style="float: left">{{ country }}</span>
                                    <span style="float: right"> {{ code }} </span>
                                </el-option>
                            </el-option-group>
                        </el-select>
                    </template>
                </el-input>
            </el-form-item>

            <el-form-item prop="code" :show-message="false">
                <el-input v-model="mobileLoginForm.code" placeholder="请输入验证码">
                    <template #append>
                        <el-button class="send-code" :disabled="codeDisabled" @click="handleSendCode"
                                   style="background: initial">
                            {{ codeText }}
                        </el-button>
                    </template>
                </el-input>
            </el-form-item>

            <el-button class="mobile-login-confirm" :loading="mobileLogining" @click="handleLogin">
                登录/注册
            </el-button>

            <el-button class="close-button" @click="visible = false"><i class="el-icon el-icon-close"></i>
            </el-button>
        </el-form>
    </div>
</div>

<script>
    $(function () {
        const loginPanelOptions = {
            data() {
                function validMobile(mobile) {
                    const reg = /^1[3-9]\d{9}$/
                    return reg.test(mobile)
                }

                function validNumber(number) {
                    return /^\d+$/.test(number)
                }

                const validateMobile = (rule, value, callback) => {
                    const { mobileLoginForm: { mobileCode } } = this
                    if (mobileCode === '+86') {
                        if (validMobile(value)) {
                            callback()
                        } else {
                            callback('请输入正确的手机号码')
                        }
                    } else if (validNumber(value)) {
                        callback()
                    } else {
                        callback('请输入正确的手机号码')
                    }
                }

                return {
                    visible: true,
                    captcha: null,
                    captchaAppId: '',

                    prefixOptions: [],

                    mobileLoginForm: {
                        mobileCode: '+86',
                        mobile: '',
                        code: ''
                    },

                    codeDisabled: false,
                    codeText: '获取',
                    codeTime: 60,
                    codeTimer: null,

                    mobileLogining: false,

                    mobileLoginRules: {
                        mobile: [
                            { required: true, message: '请输入手机号码', trigger: 'blur' },
                            { validator: validateMobile, trigger: 'blur' }
                        ],
                        code: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
                    }
                }
            },

            computed: {
                mobileMaxLength: function () {
                    return this.mobileLoginForm.mobileCode === '+86' ? 11 : 20
                }
            },

            methods: {
                async getMobilePrefix() {
                    const data = await httpGet('/api/config/load-country-mobile-code')
                    this.prefixOptions = data
                },

                async getMobileCode(data) {
                    const { mobileLoginForm: { mobileCode, mobile } } = this
                    await httpPost('/api/member/send-mobile-login-code', { mobileCode, mobile, type: 1, ...data })
                },

                async getCaptchaConfig() {
                    const { captchaAppId: appId } = await httpGet('/api/member/get-captcha-config')
                    this.captchaAppId = appId
                },

                handleCaptcha(callback) {
                    try {
                        this.captcha = new TencentCaptcha(this.captchaAppId, (res) => {
                            const { ret, ticket, randstr } = res
                            if (ret === 0) {
                                callback && callback({ ticket, randstr })
                            }
                        })
                        this.captcha.show()
                    } catch (err) {
                        console.log(err)
                    }
                },

                countDown() {
                    this.codeDisabled = true
                    this.codeTimer = setInterval(() => {
                        if (this.codeTime === 1) {
                            clearInterval(this.codeTimer)
                            this.codeTime = 60
                            this.codeText = '重新发送'
                            this.codeDisabled = false
                        } else {
                            this.codeTime -= 1
                            this.codeText = `${this.codeTime}秒`
                        }
                    }, 1000)
                },

                handleSendCode() {
                    this.$refs.mobileLoginRef.validateField('mobile', (errMsg) => {
                        if (errMsg.length === 0) {
                            this.handleCaptcha((data) => {
                                this.getMobileCode(data)
                                this.countDown()
                            })
                        } else {
                            this.$message.error(errMsg)
                        }
                    })
                },

                async handleSign() {
                    const loginApi = '/api/member/validate-mobile-login-code'
                    const loading = 'mobileLogining'
                    const cancelApplyCancelApi = '/api/person/resume/cancel-apply-cancel'

                    this[loading] = true

                    let formData = { type: 1 }

                    const { mobileLoginForm: { mobileCode, mobile, code } } = this
                    formData = { ...formData, mobileCode, mobile, code }

                    try {
                        const { token, expireTime, cancelStatus, msg } = await httpPost(loginApi, formData)
                        if (cancelStatus === 9) {
                            ElementPlus.ElMessageBox.confirm(msg, '账号注销中', {
                                distinguishCancelAndClose: true,
                                confirmButtonText: '我知道了',
                                cancelButtonText: '放弃注销'
                            })
                                .then(() => {})
                                .catch(async (action) => {
                                    if (action === 'cancel') {
                                        await httpPost(cancelApplyCancelApi, { token })
                                    }
                                })
                        } else {
                            setToken(token, expireTime)
                            window.location.reload()
                        }
                    } finally {
                        this[loading] = false
                    }
                },

                handleLogin() {
                    const formRef = this.$refs['mobileLoginRef']

                    formRef.validate((valid) => {
                        if (valid) {
                            this.handleSign()
                        } else {
                            let isValid = true
                            formRef.validateField(['mobile', 'code'], (errMsg) => {
                                if (errMsg.length) {
                                    if (isValid) {
                                        this.$message.error(errMsg)
                                    }
                                    isValid = false
                                }
                            })
                        }
                    })
                }
            },

            mounted() {
                this.getMobilePrefix()
                this.getCaptchaConfig()
            }
        }
        const loginPanelComponent = Vue.createApp(loginPanelOptions).use(ElementPlus).mount('#loginPanelTemplate')
    });
</script>