<?php
return [
    'default_page_size'          => 20,
    'defaultPageSize'            => 10,
    'recommendAnnouncementCount' => 2,
    'recommendJobCount'          => 2,
    'recommendCompanyCount'      => 2,
    //求职者端详情页码福利标签显示条数
    'welfareLabelViewAmount'     => 6,
    //路由去掉footer
    'footerNotRoute'             => [
        'introduction-page/vip',
        'introduction-page/activity-vip',
        'introduction-page/job-fast',
        'introduction-page/competition',
    ],
    /**
     * 一级栏目页模块
     */
    'firstLevelColumn'           => [

        'announcement' => [
            'headlines'              => [
                'count' => 4,
            ],
            'left_hot_announcement'  => [
                'count' => 12,
            ],
            'right_hot_announcement' => [
                'count' => 24,
            ],
            //            'right_announcement_classification' => [
            //                'count' => 28,
            //            ],

            'latest_announcement_tiled' => [
                'count' => 12,
            ],

        ],

        'showcase' => [
            'hot_unit'             => [
                'count' => 24,
            ],
            'right_recommend_unit' => [
                'count' => 24,
            ],
            'top_banner'           => [
                'count' => 8,
            ],
            'news_banner'          => [
                'count' => 3,
            ],
        ],

        'job' => [
            'right_job_classification' => [
                'count' => 12,
            ],
        ],

        'configure' => [
            'left_hot_topics' => [
                'count' => 7,
            ],
        ],

        'news' => [
            'recommend_news'        => [
                'count' => 5,
            ],
            'right_click_news'      => [
                'count' => 9,
            ],
            'news_list'             => [
                'count' => 8,
            ],
            'recommend_news_detail' => [
                'count' => 6,
            ],
        ],

    ],

    /**
     * 栏目广告位对应区块配置（暂时,未定）配置
     */
    'levelColumnList'            => [
        '1' => [
            'hot_unit'             => [
                'college_recruitment_hot_unit_1',
                'college_recruitment_hot_unit_2',
                'college_recruitment_hot_unit_3',
                'college_recruitment_hot_unit_4',
                'college_recruitment_hot_unit_5',
            ],
            'right_recommend_unit' => [
                'college_recruitment_recommend_1',
                'college_recruitment_recommend_2',
                'college_recruitment_recommend_3',
                'college_recruitment_recommend_4',
                'college_recruitment_recommend_5',
                'college_recruitment_recommend_6',
                'college_recruitment_recommend_7',
                'college_recruitment_recommend_8',
            ],
            'top_banner'           => [
                'college_recruitment_top_banner',
            ],
        ],

        '10'  => [
            'news_banner' => [
                'rotation_news_banner',
                'min_news_banner',
            ],
        ],
        '278' => [
            'news_banner' => [
                'rotation_news_banner',
                'min_news_banner',
            ],
        ],
        '279' => [
            'news_banner' => [
                'rotation_news_banner',
                'min_news_banner',
            ],
        ],
        '280' => [
            'news_banner' => [
                'rotation_news_banner',
                'min_news_banner',
            ],
        ],
        '281' => [
            'news_banner' => [
                'rotation_news_banner',
                'min_news_banner',
            ],
        ],
        '282' => [
            'news_banner' => [
                'rotation_news_banner',
                'min_news_banner',
            ],
        ],
        '283' => [
            'news_banner' => [
                'rotation_news_banner',
                'min_news_banner',
            ],
        ],

        '238' => [
            'top_banner' => [
                'discipline_leader_top_banner',
            ],
            'poster'     => [
                'poster',
            ],
        ],

        '4' => [
            'hot_unit'             => [
                'government_hot_unit_1',
                'government_hot_unit_2',
                'government_hot_unit_3',
                'government_hot_unit_4',
                'government_hot_unit_5',
            ],
            'right_recommend_unit' => [
                'government_recommend_1',
                'government_recommend_2',
                'government_recommend_3',
                'government_recommend_4',
                'government_recommend_5',
                'government_recommend_6',
                'government_recommend_7',
                'government_recommend_8',
            ],
            'top_banner'           => [
                'government_top_banner',
            ],
        ],
    ],

    /**
     * 二级栏目页
     */
    'secondLevelColumn'          => [

        'announcement' => [
            'latest_announcement' => [
                'count' => 12,
            ],
            'focus_announcement'  => [
                'count' => 10,
            ],
        ],

        'showcase' => [
            'top_banner' => [
                'count' => 8,
            ],
        ],

        'job' => [
            'latest_job_classification' => [
                'count' => 12,
            ],
        ],
    ],

    /**
     * 政府与事业单位栏目页
     */
    'governmentColumn'           => [

        'announcement' => [
            'headlines'                      => [
                'count' => 4,
            ],
            'left_hot_announcement'          => [
                'count' => 12,
            ],
            'right_hot_announcement'         => [
                'count' => 24,
            ],
            'government_latest_announcement' => [
                'count' => 12,
            ],
        ],

        'showcase' => [
            'hot_unit'             => [
                'count' => 24,
            ],
            'right_recommend_unit' => [
                'count' => 24,
            ],
            'top_banner'           => [
                'count' => 8,
            ],
        ],

        'job' => [
            'government_job' => [
                'count' => 10,
            ],
        ],
    ],

    /**
     * 省区栏目页
     */
    'provinceColumn'             => [
        'announcement' => [
            'headlines'                         => [
                'count' => 4,
            ],
            'left_hot_announcement'             => [
                'count' => 12,
            ],
            'right_hot_announcement'            => [
                'count' => 24,
            ],
            'right_announcement_classification' => [
                'count' => 12,
            ],
        ],

        'showcase' => [
            'hot_unit'             => [
                'count' => 24,
            ],
            'right_recommend_unit' => [
                'count' => 24,
            ],
            'top_banner'           => [
                'count' => 8,
            ],
        ],

        'job' => [
            //            'right_job_classification' => [
            //                'count' => 12,
            //            ],
            //省区栏目的精选职位，现在要做单独处理，这里把模板给改了
            'area_right_job_classification' => [
                'count' => 12,
            ],
        ],
    ],

    /**
     * 博士后栏目页
     */
    'postDoctorColumn'           => [
        'announcement' => [
            'headlines'                         => [
                'count' => 4,
            ],
            'left_hot_announcement'             => [
                'count' => 12,
            ],
            'right_hot_announcement'            => [
                'count' => 24,
            ],
            'right_announcement_classification' => [
                'count' => 12,
            ],
        ],

        'showcase' => [
            'hot_unit'             => [
                'count' => 24,
            ],
            'right_recommend_unit' => [
                'count' => 24,
            ],
            'top_banner'           => [
                'count' => 8,
            ],
        ],

        'job' => [
            'right_job_classification' => [
                'count' => 12,
            ],
        ],
    ],

    'newsColumn'     => [
        'zixun_A01' => ['count' => 0],
        'zixun_A02' => ['count' => 1],
        'zixun_A03' => ['count' => 1],
    ],

    /**
     * 专题模版B
     */
    'specialBColumn' => [

        'announcement' => [
            'headlines'                   => [
                'count' => 4,
            ],
            'left_hot_announcement'       => [
                'count' => 12,
            ],
            'right_hot_announcement'      => [
                'count' => 24,
            ],
            'special_latest_announcement' => [
                'count' => 12,
            ],
        ],

        'showcase' => [
            'hot_unit'             => [
                'count' => 24,
            ],
            'right_recommend_unit' => [
                'count' => 24,
            ],
            'top_banner'           => [
                'count' => 8,
            ],
        ],

        'job' => [
            'special_latest_job' => [
                'count' => 24,
            ],
        ],
    ],

    /**
     * 配置数据
     */
    'configure'      => [
        /**
         * 二级栏目招聘广告发布数据
         */
        'poster'             => [
            'hot_line' => '15920573323',
            'phone'    => '020-85611139',
            'qq'       => '2881224205',

        ],

        /**
         * 登录注册信息
         */
        'login_registration' => [
            'img_url' => 'http://test.static.gaoxiaojob.com/uploads/image/20220111/20220111150218_81088.jpeg',
            'stream'  => [
                '0' => [
                    'name'  => '注册简历',
                    'link'  => '/',
                    'class' => 'registered-resume',
                ],
                '1' => [
                    'name'  => '发布职位',
                    'link'  => '/',
                    'class' => 'release-job',
                ],
            ],
        ],

        /**
         * 友情链接
         */
        'friend-link'        => [
            [
                'name' => '人才招聘',
                'link' => 'https://www.gaoxiaojob.com/rczhaopin',
            ],
            [
                'name' => '中国政府网',
                'link' => 'http://www.gov.cn/',
            ],
            [
                'name' => '湖南人才网',
                'link' => 'http://www.hnrcsc.com/',
            ],
            //            '2'  => [
            //                'name' => '北京人才网',
            //                'link' => 'https://www.zhaopin.com/beijing/',
            //            ],
            [
                'name' => '教师招聘',
                'link' => 'https://www.job910.com/',
            ],
            [
                'name' => '中国卫生人才网',
                'link' => 'https://www.21wecan.com/',
            ],
            //            '5'  => [
            //                'name' => '中国人才热线',
            //                'link' => 'https://www.cjol.com/',
            //            ],
            //            '6'  => [
            //                'name' => '中国公共招聘网',
            //                'link' => 'http://www.cjob.gov.cn/',
            //            ],
            //            '7'  => [
            //                'name' => '中国国家人才网',
            //                'link' => 'http://www.newjobs.com.cn/',
            //            ],
            //            '8'  => [
            //                'name' => '上海高校毕业生就业信息网',
            //                'link' => 'http://www.firstjob.shec.edu.cn/',
            //            ],
            //            '9'  => [
            //                'name' => '北京高校毕业生就业信息网',
            //                'link' => 'http://www.bjbys.net.cn/',
            //            ],
            //            '10' => [
            //                'name' => '山东高校毕业生就业信息网',
            //                'link' => 'https://www.sdgxbys.cn/index.html',
            //            ],
            //            '11' => [
            //                'name' => '广州市劳动保障信息网',
            //                'link' => 'http://www.gzlss.gov.cn/',
            //            ],
            [
                'name' => '国家公务员局',
                'link' => 'http://www.scs.gov.cn/',
            ],
            [
                'name' => '人力资源和社会保障部',
                'link' => 'http://www.mohrss.gov.cn/',
            ],
            //            '14' => [
            //                'name' => '南京大学就业信息网',
            //                'link' => 'http://job.nju.edu.cn/',
            //            ],
            //            '15' => [
            //                'name' => '湛江人才网',
            //                'link' => 'http://www.0759job.com/',
            //            ],
            //            '16' => [
            //                'name' => '华南理工大学就业在线',
            //                'link' => 'http://jyzx.6ihnep7.cas.scut.edu.cn/jyzx/',
            //            ],
            [
                'name' => '北极星电力招聘',
                'link' => 'https://hr.bjx.com.cn/',
            ],
            [
                'name' => '拉勾招聘',
                'link' => 'https://www.lagou.com/',
            ],
            [
                'name' => '医疗人才网',
                'link' => 'https://www.120job.cn/',
            ],
            [
                'name' => '育路教育',
                'link' => 'http://www.yuloo.com/',
            ],
            [
                'name' => '名校网',
                'link' => 'https://www.mingxiaow.com/',
            ],
            [
                'name' => '在职研究生',
                'link' => 'https://www.eduego.com/',
            ],
            [
                'name' => '公务员考试网',
                'link' => 'https://www.gwy.com/',
            ],
            [
                'name' => '职友集',
                'link' => 'https://www.jobui.com/',
            ],
            //            '18' => [
            //                'name' => '中国人才招聘网',
            //                'link' => 'http://www.cnjob.com/',
            //            ],
        ],

        /**
         * 热门地区
         */
        'hot_area'           => [
            '0' => [
                'name' => '北京',
                'id'   => 11,
            ],
            '1' => [
                'name' => '广州',
                'id'   => 46,
            ],
            '2' => [
                'name' => '杭州',
                'id'   => 51,
            ],
            '3' => [
                'name' => '成都',
                'id'   => 47,
            ],
            '4' => [
                'name' => '武汉',
                'id'   => 48,
            ],
            '5' => [
                'name' => '上海',
                'id'   => 12,
            ],
        ],

        /**
         * 博士后人才交流
         */
        'communication'      => [
            // 'concat_title'  => "硕博求职分享群:",
            // 'number'        => "187348455",
            'concat_title1' => "扫码进入小程序，可快速查看编制岗",
            'image_url'     => "https://img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/22.png",
        ],

        /**
         * 高校招聘地区导航
         */
        'city_nav'           => [
            0  => [
                'id'   => 11,
                'name' => '北京',
            ],
            1  => [
                'id'   => 13,
                'name' => '天津',
            ],
            2  => [
                'id'   => 15,
                'name' => '河北',
            ],
            3  => [
                'id'   => 16,
                'name' => '山西',
            ],
            4  => [
                'id'   => 17,
                'name' => '内蒙古',
            ],
            5  => [
                'id'   => 18,
                'name' => '辽宁',
            ],
            6  => [
                'id'   => 19,
                'name' => '吉林',
            ],
            7  => [
                'id'   => 20,
                'name' => '黑龙江',
            ],
            8  => [
                'id'   => 12,
                'name' => '上海',
            ],
            9  => [
                'id'   => 26,
                'name' => '山东',
            ],
            10 => [
                'id'   => 21,
                'name' => '江苏',
            ],
            11 => [
                'id'   => 23,
                'name' => '安徽',
            ],
            12 => [
                'id'   => 25,
                'name' => '江西',
            ],
            13 => [
                'id'   => 22,
                'name' => '浙江',
            ],
            14 => [
                'id'   => 24,
                'name' => '福建',
            ],
            15 => [
                'id'   => 27,
                'name' => '河南',
            ],
            16 => [
                'id'   => 28,
                'name' => '湖北',
            ],
            17 => [
                'id'   => 29,
                'name' => '湖南',
            ],
            18 => [
                'id'   => 30,
                'name' => '广东',
            ],
            19 => [
                'id'   => 31,
                'name' => '广西',
            ],
            20 => [
                'id'   => 32,
                'name' => '海南',
            ],
            21 => [
                'id'   => 14,
                'name' => '重庆',
            ],
            22 => [
                'id'   => 33,
                'name' => '四川',
            ],
            23 => [
                'id'   => 35,
                'name' => '云南',
            ],
            24 => [
                'id'   => 34,
                'name' => '贵州',
            ],
            25 => [
                'id'   => 36,
                'name' => '西藏',
            ],
            26 => [
                'id'   => 37,
                'name' => '陕西',
            ],
            27 => [
                'id'   => 38,
                'name' => '甘肃',
            ],
            28 => [
                'id'   => 40,
                'name' => '宁夏',
            ],
            29 => [
                'id'   => 39,
                'name' => '青海',
            ],
            30 => [
                'id'   => 41,
                'name' => '新疆',
            ],
            31 => [
                'id'   => 48,
                'name' => '武汉',
            ],
            32 => [
                'id'   => 49,
                'name' => '南京',
            ],
            33 => [
                'id'   => 51,
                'name' => '杭州',
            ],
            34 => [
                'id'   => 50,
                'name' => '西安',
            ],
            35 => [
                'id'   => 46,
                'name' => '广州',
            ],

        ],

        /**
         * 栏目跟广告位关联
         */
        'columnToPosition'   => [
            'rotation'   => [
                0 => [
                    'number' => 'HF',
                    'name'   => '栏目轮播图',
                ],
            ],
            'top_banner' => [
                0 => [
                    'number' => 'A1',
                    'name'   => '顶部A1区',
                ],
            ],
            'hot_unit'   => [
                0 => [
                    'number' => 'K1_1',
                    'name'   => '推荐单位',
                ],
                1 => [
                    'number' => 'K1_2',
                    'name'   => '华东地区',
                ],
                2 => [
                    'number' => 'K1_3',
                    'name'   => '华中、华南地区',
                ],
                3 => [
                    'number' => 'K1_4',
                    'name'   => '华北、东北地区',
                ],
                4 => [
                    'number' => 'K1_5',
                    'name'   => '西南、西北地区',
                ],
            ],

            'hot_unit_province' => [
                0 => [
                    'number' => 'K1_2',
                    'name'   => '本科院校',
                ],
                1 => [
                    'number' => 'K1_1',
                    'name'   => '双一流',
                ],
                2 => [
                    'number' => 'K1_3',
                    'name'   => '高职高专',
                ],
                3 => [
                    'number' => 'K1_4',
                    'name'   => '博士后',
                ],
                4 => [
                    'number' => 'K1_5',
                    'name'   => '企事业单位',
                ],
            ],

            'right_recommend_unit' => [
                0 => [
                    'number' => 'tuijiandanwei',
                    'name'   => '推荐单位',
                ],
            ],

            'reserved' => [
                0 => [
                    'number' => 'Z1',
                    'name'   => 'Z1图片广告位',
                ],
            ],

            'left_reserve' => [
                0 => [
                    'number' => 'B1',
                    'name'   => 'B1广告区',
                ],
            ],

            'middle_reserve' => [
                0 => [
                    'number' => 'redianzixun',
                    'name'   => '热点资讯',
                ],
            ],

            'right_reserve' => [
                0 => [
                    'number' => 'B2',
                    'name'   => 'B2广告区',
                ],
            ],

            'hot_unit_b' => [
                0  => [
                    'number' => 'K1_PD0',
                    'name'   => '推荐单位',
                    'title'  => '',
                ],
                1  => [
                    'number' => 'K1_PD1',
                    'name'   => '跨学科及综合领域',
                    'title'  => '',
                ],
                2  => [
                    'number' => 'K1_PD2',
                    'name'   => '生物与生命科学领域',
                    'title'  => '一级学科方向：生物学 | 生物工程',
                ],
                3  => [
                    'number' => 'K1_PD3',
                    'name'   => '医学与医药领域',
                    'title'  => '一级学科方向：基础医学 | 临床医学 | 口腔医学 | 公共卫生与预防医学 | 中医学 | 中西医结合 | 药学 | 中药学 | 特种医学
                                          医学技术 | 护理学 | 生物医学工程',
                ],
                4  => [
                    'number' => 'K1_PD4',
                    'name'   => '人文艺术与社会科学领域',
                    'title'  => '一级学科方向：外国语言文学 | 新闻传播学 | 中国语言文学 | 哲学 | 艺术学理论 | 音乐与舞蹈学 | 戏剧与影视学
                                             美术学 | 设计学 | 考古学 | 中国史 | 世界史 | 教育学 | 心理学 | 体育学 | 法学 | 政治学 | 社会学 | 民族学 | 马克思主义理论 | 公安学 | 军事学',
                ],
                5  => [
                    'number' => 'K1_PD5',
                    'name'   => '管理与经济金融领域',
                    'title'  => '一级学科方向：理论经济学 | 应用经济学 | 管理科学与工程 | 工商管理 | 农林经济管理 | 公共管理 | 图书情报与档案管理',
                ],
                6  => [
                    'number' => 'K1_PD6',
                    'name'   => '数统与物理天文力学领域',
                    'title'  => '一级学科方向：数学 | 统计学 | 物理学 | 天文学 | 核科学与技术 | 系统科学 | 科学技术史 | 力学',
                ],
                7  => [
                    'number' => 'K1_PD7',
                    'name'   => '光学电子与仪器领域',
                    'title'  => '一级学科方向：电子科学与技术 |光学工程 | 仪器科学与技术',
                ],
                8  => [
                    'number' => 'K1_PD8',
                    'name'   => '信息科学与计算机领域',
                    'title'  => '一级学科方向：信息与通信工程 | 计算机科学与技术 | 软件工程 | 网络空间安全',
                ],
                9  => [
                    'number' => 'K1_PD9',
                    'name'   => '地球环境与地质绘测领域',
                    'title'  => '一级学科方向：生态学 | 环境科学与工程 | 地理学 | 大气科学 | 海洋科学 | 地球物理学 | 地质学
                                         测绘科学与技术 | 地质资源与地质工程',
                ],
                10 => [
                    'number' => 'K1_PD10',
                    'name'   => '土木建筑与园林水利领域',
                    'title'  => '一级学科方向：建筑学 | 土木工程 | 城乡规划学 | 风景园林学 | 水利工程',
                ],
                11 => [
                    'number' => 'K1_PD11',
                    'name'   => '交通船舶与航空航天领域',
                    'title'  => '一级学科方向：交通运输工程 | 航空宇航科学与技术 | 船舶与海洋工程',
                ],
                12 => [
                    'number' => 'K1_PD12',
                    'name'   => '材料冶金与能源矿业领域',
                    'title'  => '一级学科方向：材料科学与工程 | 冶金工程 | 石油与天然气工程 | 矿业工程 | 安全科学与工程',
                ],
                13 => [
                    'number' => 'K1_PD13',
                    'name'   => '机械控制与动力电气领域',
                    'title'  => '一级学科方向：机械工程 | 控制科学与工程 | 兵器科学与技术 | 仪器科学与技术 | 电气工程 | 动力工程及工程热物理',
                ],
                14 => [
                    'number' => 'K1_PD14',
                    'name'   => '化学轻工与纺织食品领域',
                    'title'  => '一级学科方向：化学 | 化学工程与技术 | 纺织科学与工程 | 轻工技术与工程 | 食品科学与工程',
                ],
                15 => [
                    'number' => 'K1_PD15',
                    'name'   => '农林水产领域',
                    'title'  => '一级学科方向：作物学 | 园艺学 | 农业资源与环境 | 植物保护 | 畜牧学 | 兽医学 | 林学 | 水产 | 草学 | 农业工程 | 林业工程',
                ],
            ],
        ],

        /**
         * 高校地区导航
         */
        'college_nav'        => [
            0  => [
                'id'        => 284,
                'name'      => '北京',
                'real_name' => '北京高校',
            ],
            1  => [
                'id'        => 296,
                'name'      => '天津',
                'real_name' => '天津高校',
            ],
            2  => [
                'id'        => 308,
                'name'      => '河北',
                'real_name' => '河北高校',
            ],
            3  => [
                'id'        => 314,
                'name'      => '山西',
                'real_name' => '山西高校',
            ],
            4  => [
                'id'        => 320,
                'name'      => '内蒙古',
                'real_name' => '内蒙古高校',
            ],
            5  => [
                'id'        => 326,
                'name'      => '辽宁',
                'real_name' => '辽宁高校',
            ],
            6  => [
                'id'        => 332,
                'name'      => '吉林',
                'real_name' => '吉林高校',
            ],
            7  => [
                'id'        => 338,
                'name'      => '黑龙江',
                'real_name' => '黑龙江高校',
            ],
            8  => [
                'id'        => 290,
                'name'      => '上海',
                'real_name' => '上海高校',
            ],
            9  => [
                'id'        => 374,
                'name'      => '山东',
                'real_name' => '山东高校',
            ],
            10 => [
                'id'        => 344,
                'name'      => '江苏',
                'real_name' => '江苏高校',
            ],
            11 => [
                'id'        => 356,
                'name'      => '安徽',
                'real_name' => '安徽高校',
            ],
            12 => [
                'id'        => 368,
                'name'      => '江西',
                'real_name' => '江西高校',
            ],
            13 => [
                'id'        => 350,
                'name'      => '浙江',
                'real_name' => '浙江高校',
            ],
            14 => [
                'id'        => 362,
                'name'      => '福建',
                'real_name' => '福建高校',
            ],
            15 => [
                'id'        => 380,
                'name'      => '河南',
                'real_name' => '河南高校',
            ],
            16 => [
                'id'        => 386,
                'name'      => '湖北',
                'real_name' => '湖北高校',
            ],
            17 => [
                'id'        => 392,
                'name'      => '湖南',
                'real_name' => '湖南高校',
            ],
            18 => [
                'id'        => 398,
                'name'      => '广东',
                'real_name' => '广东高校',
            ],
            19 => [
                'id'        => 404,
                'name'      => '广西',
                'real_name' => '广西高校',
            ],
            20 => [
                'id'        => 410,
                'name'      => '海南',
                'real_name' => '海南高校',
            ],
            21 => [
                'id'        => 302,
                'name'      => '重庆',
                'real_name' => '重庆高校',
            ],
            22 => [
                'id'        => 416,
                'name'      => '四川',
                'real_name' => '四川高校',
            ],
            23 => [
                'id'        => 428,
                'name'      => '云南',
                'real_name' => '云南高校',
            ],
            24 => [
                'id'        => 422,
                'name'      => '贵州',
                'real_name' => '贵州高校',
            ],
            25 => [
                'id'        => 434,
                'name'      => '西藏',
                'real_name' => '西藏高校',
            ],
            26 => [
                'id'        => 440,
                'name'      => '陕西',
                'real_name' => '陕西高校',
            ],
            27 => [
                'id'        => 446,
                'name'      => '甘肃',
                'real_name' => '甘肃高校',
            ],
            28 => [
                'id'        => 458,
                'name'      => '宁夏',
                'real_name' => '宁夏高校',
            ],
            29 => [
                'id'        => 452,
                'name'      => '青海',
                'real_name' => '青海高校',
            ],
            30 => [
                'id'        => 464,
                'name'      => '新疆',
                'real_name' => '新疆高校',
            ],
            31 => [
                'id'        => 506,
                'name'      => '武汉',
                'real_name' => '武汉高校',
            ],
            32 => [
                'id'        => 512,
                'name'      => '南京',
                'real_name' => '南京高校',
            ],
            33 => [
                'id'        => 524,
                'name'      => '杭州',
                'real_name' => '杭州高校',
            ],
            34 => [
                'id'        => 518,
                'name'      => '西安',
                'real_name' => '西安高校',
            ],
            35 => [
                'id'        => 494,
                'name'      => '广州',
                'real_name' => '广州高校',
            ],
            36 => [
                'id'        => 488,
                'name'      => '深圳',
                'real_name' => '深圳高校',
            ],
            37 => [
                'id'        => 500,
                'name'      => '成都',
                'real_name' => '成都高校',
            ],
        ],

        /**
         * 科研地区导航
         */
        'scientific_nav'     => [
            0  => [
                'id'        => 287,
                'name'      => '北京',
                'real_name' => '北京科研',
            ],
            1  => [
                'id'        => 299,
                'name'      => '天津',
                'real_name' => '天津科研',
            ],
            2  => [
                'id'        => 311,
                'name'      => '河北',
                'real_name' => '河北科研',
            ],
            3  => [
                'id'        => 317,
                'name'      => '山西',
                'real_name' => '山西科研',
            ],
            4  => [
                'id'        => 323,
                'name'      => '内蒙古',
                'real_name' => '内蒙古科研',
            ],
            5  => [
                'id'        => 329,
                'name'      => '辽宁',
                'real_name' => '辽宁科研',
            ],
            6  => [
                'id'        => 335,
                'name'      => '吉林',
                'real_name' => '吉林科研',
            ],
            7  => [
                'id'        => 341,
                'name'      => '黑龙江',
                'real_name' => '黑龙江科研',
            ],
            8  => [
                'id'        => 293,
                'name'      => '上海',
                'real_name' => '上海科研',
            ],
            9  => [
                'id'        => 377,
                'name'      => '山东',
                'real_name' => '山东科研',
            ],
            10 => [
                'id'        => 347,
                'name'      => '江苏',
                'real_name' => '江苏科研',
            ],
            11 => [
                'id'        => 359,
                'name'      => '安徽',
                'real_name' => '安徽科研',
            ],
            12 => [
                'id'        => 371,
                'name'      => '江西',
                'real_name' => '江西科研',
            ],
            13 => [
                'id'        => 353,
                'name'      => '浙江',
                'real_name' => '浙江科研',
            ],
            14 => [
                'id'        => 365,
                'name'      => '福建',
                'real_name' => '福建科研',
            ],
            15 => [
                'id'        => 383,
                'name'      => '河南',
                'real_name' => '河南科研',
            ],
            16 => [
                'id'        => 389,
                'name'      => '湖北',
                'real_name' => '湖北科研',
            ],
            17 => [
                'id'        => 395,
                'name'      => '湖南',
                'real_name' => '湖南科研',
            ],
            18 => [
                'id'        => 401,
                'name'      => '广东',
                'real_name' => '广东科研',
            ],
            19 => [
                'id'        => 407,
                'name'      => '广西',
                'real_name' => '广西科研',
            ],
            20 => [
                'id'        => 413,
                'name'      => '海南',
                'real_name' => '海南科研',
            ],
            21 => [
                'id'        => 305,
                'name'      => '重庆',
                'real_name' => '重庆科研',
            ],
            22 => [
                'id'        => 419,
                'name'      => '四川',
                'real_name' => '四川科研',
            ],
            23 => [
                'id'        => 431,
                'name'      => '云南',
                'real_name' => '云南科研',
            ],
            24 => [
                'id'        => 425,
                'name'      => '贵州',
                'real_name' => '贵州科研',
            ],
            25 => [
                'id'        => 437,
                'name'      => '西藏',
                'real_name' => '西藏科研',
            ],
            26 => [
                'id'        => 443,
                'name'      => '陕西',
                'real_name' => '陕西科研',
            ],
            27 => [
                'id'        => 449,
                'name'      => '甘肃',
                'real_name' => '甘肃科研',
            ],
            28 => [
                'id'        => 461,
                'name'      => '宁夏',
                'real_name' => '宁夏科研',
            ],
            29 => [
                'id'        => 455,
                'name'      => '青海',
                'real_name' => '青海科研',
            ],
            30 => [
                'id'        => 467,
                'name'      => '新疆',
                'real_name' => '新疆科研',
            ],
            31 => [
                'id'        => 509,
                'name'      => '武汉',
                'real_name' => '武汉科研',
            ],
            32 => [
                'id'        => 515,
                'name'      => '南京',
                'real_name' => '南京科研',
            ],
            33 => [
                'id'        => 527,
                'name'      => '杭州',
                'real_name' => '杭州科研',
            ],
            34 => [
                'id'        => 521,
                'name'      => '西安',
                'real_name' => '西安科研',
            ],
            35 => [
                'id'        => 497,
                'name'      => '广州',
                'real_name' => '广州科研',
            ],
            36 => [
                'id'        => 491,
                'name'      => '深圳',
                'real_name' => '深圳科研',
            ],
            37 => [
                'id'        => 503,
                'name'      => '成都',
                'real_name' => '成都科研',
            ],
        ],

        /**
         * 机关单位地区导航
         */
        'office_nav'         => [
            0  => [
                'id'        => 285,
                'name'      => '北京',
                'real_name' => '北京机关事业',
            ],
            1  => [
                'id'        => 297,
                'name'      => '天津',
                'real_name' => '天津机关事业',
            ],
            2  => [
                'id'        => 309,
                'name'      => '河北',
                'real_name' => '河北机关事业',
            ],
            3  => [
                'id'        => 315,
                'name'      => '山西',
                'real_name' => '山西机关事业',
            ],
            4  => [
                'id'        => 321,
                'name'      => '内蒙古',
                'real_name' => '内蒙古机关事业',
            ],
            5  => [
                'id'        => 327,
                'name'      => '辽宁',
                'real_name' => '辽宁机关事业',
            ],
            6  => [
                'id'        => 333,
                'name'      => '吉林',
                'real_name' => '吉林机关事业',
            ],
            7  => [
                'id'        => 339,
                'name'      => '黑龙江',
                'real_name' => '黑龙江机关事业',
            ],
            8  => [
                'id'        => 291,
                'name'      => '上海',
                'real_name' => '上海机关事业',
            ],
            9  => [
                'id'        => 375,
                'name'      => '山东',
                'real_name' => '山东机关事业',
            ],
            10 => [
                'id'        => 345,
                'name'      => '江苏',
                'real_name' => '江苏机关事业',
            ],
            11 => [
                'id'        => 357,
                'name'      => '安徽',
                'real_name' => '安徽机关事业',
            ],
            12 => [
                'id'        => 369,
                'name'      => '江西',
                'real_name' => '江西机关事业',
            ],
            13 => [
                'id'        => 351,
                'name'      => '浙江',
                'real_name' => '浙江机关事业',
            ],
            14 => [
                'id'        => 363,
                'name'      => '福建',
                'real_name' => '福建机关事业',
            ],
            15 => [
                'id'        => 381,
                'name'      => '河南',
                'real_name' => '河南机关事业',
            ],
            16 => [
                'id'        => 387,
                'name'      => '湖北',
                'real_name' => '湖北机关事业',
            ],
            17 => [
                'id'        => 393,
                'name'      => '湖南',
                'real_name' => '湖南机关事业',
            ],
            18 => [
                'id'        => 399,
                'name'      => '广东',
                'real_name' => '广东机关事业',
            ],
            19 => [
                'id'        => 405,
                'name'      => '广西',
                'real_name' => '广西机关事业',
            ],
            20 => [
                'id'        => 411,
                'name'      => '海南',
                'real_name' => '海南机关事业',
            ],
            21 => [
                'id'        => 303,
                'name'      => '重庆',
                'real_name' => '重庆机关事业',
            ],
            22 => [
                'id'        => 417,
                'name'      => '四川',
                'real_name' => '四川机关事业',
            ],
            23 => [
                'id'        => 429,
                'name'      => '云南',
                'real_name' => '云南机关事业',
            ],
            24 => [
                'id'        => 423,
                'name'      => '贵州',
                'real_name' => '贵州机关事业',
            ],
            25 => [
                'id'        => 435,
                'name'      => '西藏',
                'real_name' => '西藏机关事业',
            ],
            26 => [
                'id'        => 441,
                'name'      => '陕西',
                'real_name' => '陕西机关事业',
            ],
            27 => [
                'id'        => 447,
                'name'      => '甘肃',
                'real_name' => '甘肃机关事业',
            ],
            28 => [
                'id'        => 459,
                'name'      => '宁夏',
                'real_name' => '宁夏机关事业',
            ],
            29 => [
                'id'        => 453,
                'name'      => '青海',
                'real_name' => '青海机关事业',
            ],
            30 => [
                'id'        => 465,
                'name'      => '新疆',
                'real_name' => '新疆机关事业',
            ],
            31 => [
                'id'        => 507,
                'name'      => '武汉',
                'real_name' => '武汉机关事业',
            ],
            32 => [
                'id'        => 513,
                'name'      => '南京',
                'real_name' => '南京机关事业',
            ],
            33 => [
                'id'        => 525,
                'name'      => '杭州',
                'real_name' => '杭州机关事业',
            ],
            34 => [
                'id'        => 519,
                'name'      => '西安',
                'real_name' => '西安机关事业',
            ],
            35 => [
                'id'        => 495,
                'name'      => '广州',
                'real_name' => '广州机关事业',
            ],
            36 => [
                'id'        => 489,
                'name'      => '深圳',
                'real_name' => '深圳机关事业',
            ],
            37 => [
                'id'        => 501,
                'name'      => '成都',
                'real_name' => '成都机关事业',
            ],
        ],

        /**
         * 中小学地区导航
         */
        'school_nav'         => [
            0  => [
                'id'        => 286,
                'name'      => '北京',
                'real_name' => '北京中小学',
            ],
            1  => [
                'id'        => 298,
                'name'      => '天津',
                'real_name' => '天津中小学',
            ],
            2  => [
                'id'        => 310,
                'name'      => '河北',
                'real_name' => '河北中小学',
            ],
            3  => [
                'id'        => 316,
                'name'      => '山西',
                'real_name' => '山西中小学',
            ],
            4  => [
                'id'        => 322,
                'name'      => '内蒙古',
                'real_name' => '内蒙古中小学',
            ],
            5  => [
                'id'        => 328,
                'name'      => '辽宁',
                'real_name' => '辽宁中小学',
            ],
            6  => [
                'id'        => 334,
                'name'      => '吉林',
                'real_name' => '吉林中小学',
            ],
            7  => [
                'id'        => 340,
                'name'      => '黑龙江',
                'real_name' => '黑龙江中小学',
            ],
            8  => [
                'id'        => 292,
                'name'      => '上海',
                'real_name' => '上海中小学',
            ],
            9  => [
                'id'        => 376,
                'name'      => '山东',
                'real_name' => '山东中小学',
            ],
            10 => [
                'id'        => 346,
                'name'      => '江苏',
                'real_name' => '江苏中小学',
            ],
            11 => [
                'id'        => 358,
                'name'      => '安徽',
                'real_name' => '安徽中小学',
            ],
            12 => [
                'id'        => 370,
                'name'      => '江西',
                'real_name' => '江西中小学',
            ],
            13 => [
                'id'        => 352,
                'name'      => '浙江',
                'real_name' => '浙江中小学',
            ],
            14 => [
                'id'        => 364,
                'name'      => '福建',
                'real_name' => '福建中小学',
            ],
            15 => [
                'id'        => 382,
                'name'      => '河南',
                'real_name' => '河南中小学',
            ],
            16 => [
                'id'        => 388,
                'name'      => '湖北',
                'real_name' => '湖北中小学',
            ],
            17 => [
                'id'        => 394,
                'name'      => '湖南',
                'real_name' => '湖南中小学',
            ],
            18 => [
                'id'        => 400,
                'name'      => '广东',
                'real_name' => '广东中小学',
            ],
            19 => [
                'id'        => 406,
                'name'      => '广西',
                'real_name' => '广西中小学',
            ],
            20 => [
                'id'        => 412,
                'name'      => '海南',
                'real_name' => '海南中小学',
            ],
            21 => [
                'id'        => 304,
                'name'      => '重庆',
                'real_name' => '重庆中小学',
            ],
            22 => [
                'id'        => 418,
                'name'      => '四川',
                'real_name' => '四川中小学',
            ],
            23 => [
                'id'        => 430,
                'name'      => '云南',
                'real_name' => '云南中小学',
            ],
            24 => [
                'id'        => 424,
                'name'      => '贵州',
                'real_name' => '贵州中小学',
            ],
            25 => [
                'id'        => 436,
                'name'      => '西藏',
                'real_name' => '西藏中小学',
            ],
            26 => [
                'id'        => 442,
                'name'      => '陕西',
                'real_name' => '陕西中小学',
            ],
            27 => [
                'id'        => 448,
                'name'      => '甘肃',
                'real_name' => '甘肃中小学',
            ],
            28 => [
                'id'        => 460,
                'name'      => '宁夏',
                'real_name' => '宁夏中小学',
            ],
            29 => [
                'id'        => 454,
                'name'      => '青海',
                'real_name' => '青海中小学',
            ],
            30 => [
                'id'        => 466,
                'name'      => '新疆',
                'real_name' => '新疆中小学',
            ],
            31 => [
                'id'        => 508,
                'name'      => '武汉',
                'real_name' => '武汉中小学',
            ],
            32 => [
                'id'        => 514,
                'name'      => '南京',
                'real_name' => '南京中小学',
            ],
            33 => [
                'id'        => 526,
                'name'      => '杭州',
                'real_name' => '杭州中小学',
            ],
            34 => [
                'id'        => 520,
                'name'      => '西安',
                'real_name' => '西安中小学',
            ],
            35 => [
                'id'        => 496,
                'name'      => '广州',
                'real_name' => '广州中小学',
            ],
            36 => [
                'id'        => 490,
                'name'      => '深圳',
                'real_name' => '深圳中小学',
            ],
            37 => [
                'id'        => 502,
                'name'      => '成都',
                'real_name' => '成都中小学',
            ],
        ],

        /**
         * 医学地区导航
         */
        'medical_nav'        => [
            0  => [
                'id'        => 288,
                'name'      => '北京',
                'real_name' => '北京医学',
            ],
            1  => [
                'id'        => 300,
                'name'      => '天津',
                'real_name' => '天津医学',
            ],
            2  => [
                'id'        => 312,
                'name'      => '河北',
                'real_name' => '河北医学',
            ],
            3  => [
                'id'        => 318,
                'name'      => '山西',
                'real_name' => '山西医学',
            ],
            4  => [
                'id'        => 324,
                'name'      => '内蒙古',
                'real_name' => '内蒙古医学',
            ],
            5  => [
                'id'        => 330,
                'name'      => '辽宁',
                'real_name' => '辽宁医学',
            ],
            6  => [
                'id'        => 336,
                'name'      => '吉林',
                'real_name' => '吉林医学',
            ],
            7  => [
                'id'        => 342,
                'name'      => '黑龙江',
                'real_name' => '黑龙江医学',
            ],
            8  => [
                'id'        => 294,
                'name'      => '上海',
                'real_name' => '上海医学',
            ],
            9  => [
                'id'        => 378,
                'name'      => '山东',
                'real_name' => '山东医学',
            ],
            10 => [
                'id'        => 348,
                'name'      => '江苏',
                'real_name' => '江苏医学',
            ],
            11 => [
                'id'        => 360,
                'name'      => '安徽',
                'real_name' => '安徽医学',
            ],
            12 => [
                'id'        => 372,
                'name'      => '江西',
                'real_name' => '江西医学',
            ],
            13 => [
                'id'        => 354,
                'name'      => '浙江',
                'real_name' => '浙江医学',
            ],
            14 => [
                'id'        => 366,
                'name'      => '福建',
                'real_name' => '福建医学',
            ],
            15 => [
                'id'        => 384,
                'name'      => '河南',
                'real_name' => '河南医学',
            ],
            16 => [
                'id'        => 390,
                'name'      => '湖北',
                'real_name' => '湖北医学',
            ],
            17 => [
                'id'        => 396,
                'name'      => '湖南',
                'real_name' => '湖南医学',
            ],
            18 => [
                'id'        => 402,
                'name'      => '广东',
                'real_name' => '广东医学',
            ],
            19 => [
                'id'        => 408,
                'name'      => '广西',
                'real_name' => '广西医学',
            ],
            20 => [
                'id'        => 414,
                'name'      => '海南',
                'real_name' => '海南医学',
            ],
            21 => [
                'id'        => 306,
                'name'      => '重庆',
                'real_name' => '重庆医学',
            ],
            22 => [
                'id'        => 420,
                'name'      => '四川',
                'real_name' => '四川医学',
            ],
            23 => [
                'id'        => 432,
                'name'      => '云南',
                'real_name' => '云南医学',
            ],
            24 => [
                'id'        => 426,
                'name'      => '贵州',
                'real_name' => '贵州医学',
            ],
            25 => [
                'id'        => 438,
                'name'      => '西藏',
                'real_name' => '西藏医学',
            ],
            26 => [
                'id'        => 444,
                'name'      => '陕西',
                'real_name' => '陕西医学',
            ],
            27 => [
                'id'        => 450,
                'name'      => '甘肃',
                'real_name' => '甘肃医学',
            ],
            28 => [
                'id'        => 462,
                'name'      => '宁夏',
                'real_name' => '宁夏医学',
            ],
            29 => [
                'id'        => 456,
                'name'      => '青海',
                'real_name' => '青海医学',
            ],
            30 => [
                'id'        => 468,
                'name'      => '新疆',
                'real_name' => '新疆医学',
            ],
            31 => [
                'id'        => 510,
                'name'      => '武汉',
                'real_name' => '武汉医学',
            ],
            32 => [
                'id'        => 516,
                'name'      => '南京',
                'real_name' => '南京医学',
            ],
            33 => [
                'id'        => 528,
                'name'      => '杭州',
                'real_name' => '杭州医学',
            ],
            34 => [
                'id'        => 522,
                'name'      => '西安',
                'real_name' => '西安医学',
            ],
            35 => [
                'id'        => 498,
                'name'      => '广州',
                'real_name' => '广州医学',
            ],
            36 => [
                'id'        => 492,
                'name'      => '深圳',
                'real_name' => '深圳医学',
            ],
            37 => [
                'id'        => 504,
                'name'      => '成都',
                'real_name' => '成都医学',
            ],
        ],

        /**
         * 企业地区导航
         */
        'enterprise_nav'     => [
            0  => [
                'id'        => 289,
                'name'      => '北京',
                'real_name' => '北京企业',
            ],
            1  => [
                'id'        => 301,
                'name'      => '天津',
                'real_name' => '天津企业',
            ],
            2  => [
                'id'        => 313,
                'name'      => '河北',
                'real_name' => '河北企业',
            ],
            3  => [
                'id'        => 319,
                'name'      => '山西',
                'real_name' => '山西企业',
            ],
            4  => [
                'id'        => 325,
                'name'      => '内蒙古',
                'real_name' => '内蒙古企业',
            ],
            5  => [
                'id'        => 331,
                'name'      => '辽宁',
                'real_name' => '辽宁企业',
            ],
            6  => [
                'id'        => 337,
                'name'      => '吉林',
                'real_name' => '吉林企业',
            ],
            7  => [
                'id'        => 343,
                'name'      => '黑龙江',
                'real_name' => '黑龙江企业',
            ],
            8  => [
                'id'        => 295,
                'name'      => '上海',
                'real_name' => '上海企业',
            ],
            9  => [
                'id'        => 379,
                'name'      => '山东',
                'real_name' => '山东企业',
            ],
            10 => [
                'id'        => 349,
                'name'      => '江苏',
                'real_name' => '江苏企业',
            ],
            11 => [
                'id'        => 361,
                'name'      => '安徽',
                'real_name' => '安徽企业',
            ],
            12 => [
                'id'        => 373,
                'name'      => '江西',
                'real_name' => '江西企业',
            ],
            13 => [
                'id'        => 355,
                'name'      => '浙江',
                'real_name' => '浙江企业',
            ],
            14 => [
                'id'        => 367,
                'name'      => '福建',
                'real_name' => '福建企业',
            ],
            15 => [
                'id'        => 385,
                'name'      => '河南',
                'real_name' => '河南企业',
            ],
            16 => [
                'id'        => 391,
                'name'      => '湖北',
                'real_name' => '湖北企业',
            ],
            17 => [
                'id'        => 397,
                'name'      => '湖南',
                'real_name' => '湖南企业',
            ],
            18 => [
                'id'        => 403,
                'name'      => '广东',
                'real_name' => '广东企业',
            ],
            19 => [
                'id'        => 409,
                'name'      => '广西',
                'real_name' => '广西企业',
            ],
            20 => [
                'id'        => 415,
                'name'      => '海南',
                'real_name' => '海南企业',
            ],
            21 => [
                'id'        => 307,
                'name'      => '重庆',
                'real_name' => '重庆企业',
            ],
            22 => [
                'id'        => 421,
                'name'      => '四川',
                'real_name' => '四川企业',
            ],
            23 => [
                'id'        => 433,
                'name'      => '云南',
                'real_name' => '云南企业',
            ],
            24 => [
                'id'        => 427,
                'name'      => '贵州',
                'real_name' => '贵州企业',
            ],
            25 => [
                'id'        => 439,
                'name'      => '西藏',
                'real_name' => '西藏企业',
            ],
            26 => [
                'id'        => 445,
                'name'      => '陕西',
                'real_name' => '陕西企业',
            ],
            27 => [
                'id'        => 451,
                'name'      => '甘肃',
                'real_name' => '甘肃企业',
            ],
            28 => [
                'id'        => 463,
                'name'      => '宁夏',
                'real_name' => '宁夏企业',
            ],
            29 => [
                'id'        => 457,
                'name'      => '青海',
                'real_name' => '青海企业',
            ],
            30 => [
                'id'        => 469,
                'name'      => '新疆',
                'real_name' => '新疆企业',
            ],
            31 => [
                'id'        => 511,
                'name'      => '武汉',
                'real_name' => '武汉企业',
            ],
            32 => [
                'id'        => 517,
                'name'      => '南京',
                'real_name' => '南京企业',
            ],
            33 => [
                'id'        => 529,
                'name'      => '杭州',
                'real_name' => '杭州企业',
            ],
            34 => [
                'id'        => 523,
                'name'      => '西安',
                'real_name' => '西安企业',
            ],
            35 => [
                'id'        => 499,
                'name'      => '广州',
                'real_name' => '广州企业',
            ],
            36 => [
                'id'        => 493,
                'name'      => '深圳',
                'real_name' => '深圳企业',
            ],
            37 => [
                'id'        => 505,
                'name'      => '成都',
                'real_name' => '成都企业',
            ],
        ],
    ],

    'newAndCompanyListHotArea' => [
        [
            'id'   => '2',
            'name' => '北京',
        ],
        [
            'id'   => '802',
            'name' => '上海',
        ],
        [
            'id'   => '20',
            'name' => '天津',
        ],
        [
            'id'   => '2324',
            'name' => '重庆',
        ],
        [
            'id'   => '1965',
            'name' => '广州',
        ],
        [
            'id'   => '1988',
            'name' => '深圳',
        ],
        [
            'id'   => '1710',
            'name' => '武汉',
        ],
        [
            'id'   => '821',
            'name' => '南京',
        ],
        [
            'id'   => '2899',
            'name' => '西安',
        ],
        [
            'id'   => '2368',
            'name' => '成都',
        ],
        [
            'id'   => '934',
            'name' => '杭州',
        ],
        [
            'id'   => '1828',
            'name' => '长沙',
        ],
        [
            'id'   => '1047',
            'name' => '合肥',
        ],
        [
            'id'   => '1533',
            'name' => '郑州',
        ],
        [
            'id'   => '1376',
            'name' => '济南',
        ],

    ],

];
