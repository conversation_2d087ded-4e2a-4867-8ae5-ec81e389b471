<?php

// 其中，“海外优青2025”，年份规则为：若是在当前年份的6月1日前，获取当前的年份；若是在当前年份的6月1日以及之后，则获取的时间为当前年份+1；
// $haiYouYear = date('m') < 6 ? date('Y') : date('Y') + 1;
$haiYouYear = 2025;

return [
    // 首页头部导航
    'homeNav' => [
        [
            'name'     => '高端人才',
            'id'       => '9',
            'children' => [
                [
                    'name' => '双一流院校人才招聘',
                    'id'   => 272,
                ],
                [
                    'name' => '本科院校人才招聘',
                    'id'   => 277,
                ],
                [
                    'name' => '高职高专人才招聘',
                    'id'   => 273,
                ],
                [
                    'name' => '党校（行政学院）人才招聘',
                    'id'   => 274,
                ],
                [
                    'name' => '博士人才引进',
                    'id'   => 275,
                ],
                [
                    'name' => '政府引才活动',
                    'id'   => 276,
                ],
            ],
        ],
        [
            'name'     => '高校招聘',
            'id'       => '1',
            'children' => [
                [
                    'name' => '学科带头人、教授招聘',
                    'id'   => 238,
                ],
                [
                    'name' => '校长、院长、系主任、中高层干部招聘',
                    'id'   => 239,
                ],
                [
                    'name' => '教学科研人才招聘',
                    'id'   => 240,
                ],
                [
                    'name' => '教辅、行政、实验、助理人员招聘',
                    'id'   => 241,
                ],
                [
                    'name' => '辅导员招聘',
                    'id'   => 242,
                ],
                [
                    'name' => '高职高专人才招聘',
                    'id'   => 273,
                ],
                [
                    'name' => '党校（行政学院）人才招聘',
                    'id'   => 274,
                ],
            ],
        ],
        [
            'name'     => '科技人才',
            'id'       => '3',
            'children' => [
                [
                    'name' => '中国科学院系统研究机构招聘',
                    'id'   => 247,
                ],
                [
                    'name' => '人文社科研究机构招聘',
                    'id'   => 248,
                ],
                [
                    'name' => '自然与应用科研机构招聘',
                    'id'   => 249,
                ],
                [
                    'name' => '企业研发机构招聘',
                    'id'   => 250,
                ],
            ],
        ],
        [
            'name'     => '政府与事业单位',
            'id'       => '4',
            'children' => [
                [
                    'name' => '公务员招考',
                    'id'   => 251,
                ],
                // [
                //     'name' => '遴选与选调',
                //     'id'   => 252,
                // ],
                [
                    'name' => '机关与事业单位统一招考',
                    'id'   => 253,
                ],
                [
                    'name' => '机关与事业单位自主招聘',
                    'id'   => 254,
                ],
                [
                    'name' => '军队武警招聘',
                    'id'   => 255,
                ],
            ],
        ],
        [
            'name'     => '中小学校',
            'id'       => '2',
            'children' => [
                [
                    'name' => '教育系统招考',
                    'id'   => 243,
                ],
                [
                    'name' => '中小学自主招聘',
                    'id'   => 244,
                ],
                [
                    'name' => '职业学校、中专、技师学院招聘',
                    'id'   => 245,
                ],
                [
                    'name' => '幼儿园教师招聘',
                    'id'   => 246,
                ],
            ],
        ],
        [
            'name'     => '医学人才',
            'id'       => '5',
            'children' => [
                [
                    'name' => '卫生系统统一招聘',
                    'id'   => 256,
                ],
                [
                    'name' => '医疗单位自主招聘',
                    'id'   => 257,
                ],
                [
                    'name' => '医卫院校（院系）招聘',
                    'id'   => 258,
                ],
                [
                    'name' => '医疗单位博士后招收',
                    'id'   => 266,
                ],
            ],
        ],
        [
            'name'     => '企业招聘',
            'id'       => '6',
            'children' => [
                [
                    'name' => '知名企业招聘',
                    'id'   => 259,
                ],
                [
                    'name' => '国有（政府）企业招聘',
                    'id'   => 260,
                ],
                [
                    'name' => '金融机构招聘',
                    'id'   => 261,
                ],
                [
                    'name' => '中小创新型企业招聘',
                    'id'   => 262,
                ],
            ],
        ],
        [
            'name'     => '',
            // 特殊class
            'class'    => 'postdoctor-title',
            'action'   => 'getBoShiHouHome',
            'children' => [
                [
                    'action' => 'getBoShiHouGongGao',
                    'name'   => '博后公告&职位',
                ],
                [
                    'action' => 'getBoShiHouDanWei',
                    'name'   => '招收单位&PI大厅',
                ],
                [
                    'action' => 'getBoShiHouHuoDong',
                    'name'   => '博后活动',
                ],
                [
                    'action' => 'getBoShiHouFuWu',
                    'name'   => '博后需求发布',
                ],
            ],
        ],
        [
            'name'     => '',
            // 特殊class
            'class'    => 'abroad-title',
            'variable' => 'haiHaiHome',
            'children' => [
                [
                    'name'     => '出海引才',
                    'variable' => 'chuHaiUrl',
                ],
                [
                    'name'     => '归国活动',
                    'variable' => 'guiGuoUrl',
                ],
                [
                    'name'     => '求贤公告',
                    'variable' => 'qiuXianUrl',
                ],
                [
                    'name'     => '海外优青' . $haiYouYear,
                    'variable' => 'haiYouUrl',
                ],

            ],
        ],
        [
            'name'     => '资讯',
            'id'       => 10,
            'children' => [
                [
                    'id'   => 278,
                    'name' => '求职攻略',
                ],
                [
                    'id'   => 279,
                    'name' => '职场分享',
                ],
                [
                    'id'   => 280,
                    'name' => '就业形势',
                ],
                [
                    'id'   => 281,
                    'name' => '观点热议',
                ],
                [
                    'id'   => 282,
                    'name' => '学术科研',
                ],
                [
                    'id'   => 283,
                    'name' => '新闻动态',
                ],
            ],
        ],
    ],

    'homeNavArea'             => [
        'province' => [
            [
                'id'   => 11,
                'name' => '北京',
            ],
            [
                'id'   => 12,
                'name' => '上海',
            ],
            [
                'id'   => 13,
                'name' => '天津',
            ],
            [
                'id'   => 14,
                'name' => '重庆',
            ],
            [
                'id'   => 23,
                'name' => '安徽',
            ],
            [
                'id'   => 24,
                'name' => '福建',
            ],
            [
                'id'   => 38,
                'name' => '甘肃',
            ],
            [
                'id'   => 30,
                'name' => '广东',
            ],
            [
                'id'   => 31,
                'name' => '广西',
            ],
            [
                'id'   => 34,
                'name' => '贵州',
            ],
            [
                'id'   => 32,
                'name' => '海南',
            ],
            [
                'id'   => 15,
                'name' => '河北',
            ],
            [
                'id'   => 20,
                'name' => '黑龙江',
            ],
            [
                'id'   => 27,
                'name' => '河南',
            ],
            [
                'id'   => 28,
                'name' => '湖北',
            ],
            [
                'id'   => 29,
                'name' => '湖南',
            ],
            [
                'id'   => 17,
                'name' => '内蒙古',
            ],
            [
                'id'   => 21,
                'name' => '江苏',
            ],
            [
                'id'   => 25,
                'name' => '江西',
            ],
            [
                'id'   => 19,
                'name' => '吉林',
            ],
            [
                'id'   => 18,
                'name' => '辽宁',
            ],
            [
                'id'   => 40,
                'name' => '宁夏',
            ],
            [
                'id'   => 39,
                'name' => '青海',
            ],
            [
                'id'   => 37,
                'name' => '陕西',
            ],
            [
                'id'   => 26,
                'name' => '山东',
            ],
            [
                'id'   => 16,
                'name' => '山西',
            ],
            [
                'id'   => 33,
                'name' => '四川',
            ],
            [
                'id'   => 36,
                'name' => '西藏',
            ],
            [
                'id'   => 41,
                'name' => '新疆',
            ],
            [
                'id'   => 35,
                'name' => '云南',
            ],
            [
                'id'   => 22,
                'name' => '浙江',
            ],
            [
                'id'   => 43,
                'name' => '香港',
            ],
            [
                'id'   => 44,
                'name' => '澳门',
            ],
            [
                'id'   => 42,
                'name' => '台湾',
            ],
            [
                'id'   => 541,
                'name' => '海外',
            ],
        ],
        'city'     => [
            [
                'id'   => 11,
                'name' => '北京',
            ],
            [
                'id'   => 12,
                'name' => '上海',
            ],
            [
                'id'   => 13,
                'name' => '天津',
            ],
            [
                'id'   => 14,
                'name' => '重庆',
            ],
            [
                'id'   => 46,
                'name' => '广州',
            ],
            [
                'id'   => 45,
                'name' => '深圳',
            ],
            [
                'id'   => 48,
                'name' => '武汉',
            ],
            [
                'id'   => 49,
                'name' => '南京',
            ],
            [
                'id'   => 50,
                'name' => '西安',
            ],
            [
                'id'   => 47,
                'name' => '成都',
            ],
            [
                'id'   => 51,
                'name' => '杭州',
            ],
            [
                'id'   => 109,
                'name' => '长春',
            ],
            [
                'id'   => 85,
                'name' => '长沙',
            ],
            [
                'id'   => 112,
                'name' => '大连',
            ],
            [
                'id'   => 71,
                'name' => '福州',
            ],
            [
                'id'   => 114,
                'name' => '贵阳',
            ],
            [
                'id'   => 111,
                'name' => '哈尔滨',
            ],
            [
                'id'   => 99,
                'name' => '海口',
            ],
            [
                'id'   => 68,
                'name' => '合肥',
            ],
            [
                'id'   => 104,
                'name' => '呼和浩特',
            ],
            [
                'id'   => 78,
                'name' => '济南',
            ],
            [
                'id'   => 116,
                'name' => '昆明',
            ],
            [
                'id'   => 118,
                'name' => '拉萨',
            ],
            [
                'id'   => 107,
                'name' => '兰州',
            ],
            [
                'id'   => 76,
                'name' => '南昌',
            ],
            [
                'id'   => 91,
                'name' => '南宁',
            ],
            [
                'id'   => 113,
                'name' => '沈阳',
            ],
            [
                'id'   => 101,
                'name' => '石家庄',
            ],
            [
                'id'   => 55,
                'name' => '苏州',
            ],
            [
                'id'   => 100,
                'name' => '太原',
            ],
            [
                'id'   => 108,
                'name' => '乌鲁木齐',
            ],
            [
                'id'   => 105,
                'name' => '西宁',
            ],
            [
                'id'   => 73,
                'name' => '厦门',
            ],
            [
                'id'   => 106,
                'name' => '银川',
            ],
            [
                'id'   => 87,
                'name' => '郑州',
            ],
            [
                'id'   => 96,
                'name' => '珠海',
            ],
        ],
    ],

    // 首页全国站切换的配置
    'homeStation'             => [
        [
            'id'   => '11',
            'name' => '北京',
        ],
        [
            'id'   => '12',
            'name' => '上海',
        ],
        [
            'id'   => '13',
            'name' => '天津',
        ],
        [
            'id'   => '14',
            'name' => '重庆',
        ],
        [
            'id'   => '15',
            'name' => '河北',
        ],
        [
            'id'   => '16',
            'name' => '山西',
        ],
        [
            'id'   => '17',
            'name' => '内蒙古',
        ],
        [
            'id'   => '18',
            'name' => '辽宁',
        ],
        [
            'id'   => '19',
            'name' => '吉林',
        ],
        [
            'id'   => '20',
            'name' => '黑龙江',
        ],
        [
            'id'   => '21',
            'name' => '江苏',
        ],
        [
            'id'   => '22',
            'name' => '浙江',
        ],
        [
            'id'   => '23',
            'name' => '安徽',
        ],
        [
            'id'   => '24',
            'name' => '福建',
        ],
        [
            'id'   => '25',
            'name' => '江西',
        ],
        [
            'id'   => '26',
            'name' => '山东',
        ],
        [
            'id'   => '27',
            'name' => '河南',
        ],
        [
            'id'   => '28',
            'name' => '湖北',
        ],
        [
            'id'   => '29',
            'name' => '湖南',
        ],
        [
            'id'   => '30',
            'name' => '广东',
        ],
        [
            'id'   => '31',
            'name' => '广西',
        ],
        [
            'id'   => '32',
            'name' => '海南',
        ],
        [
            'id'   => '33',
            'name' => '四川',
        ],
        [
            'id'   => '34',
            'name' => '贵州',
        ],
        [
            'id'   => '35',
            'name' => '云南',
        ],
        [
            'id'   => '36',
            'name' => '西藏',
        ],
        [
            'id'   => '37',
            'name' => '陕西',
        ],
        [
            'id'   => '38',
            'name' => '甘肃',
        ],
        [
            'id'   => '39',
            'name' => '青海',
        ],
        [
            'id'   => '40',
            'name' => '宁夏',
        ],
        [
            'id'   => '41',
            'name' => '新疆',
        ],
        [
            'id'   => '42',
            'name' => '台湾',
        ],
        [
            'id'   => '43',
            'name' => '香港',
        ],
        [
            'id'   => '44',
            'name' => '澳门',
        ],
    ],

    // 首页栏目一级页面(头部banner左边的导航配置)
    'homeSubNav'              => [
        'area'    => [
            'province' => [
                [
                    'id'   => 26,
                    'name' => '山东',
                ],
                [
                    'id'   => 21,
                    'name' => '江苏',
                ],
                [
                    'id'   => 33,
                    'name' => '四川',
                ],
                [
                    'id'   => 14,
                    'name' => '重庆',
                ],
                [
                    'id'   => 27,
                    'name' => '河南',
                ],
                [
                    'id'   => 11,
                    'name' => '北京',
                ],
                [
                    'id'   => 30,
                    'name' => '广东',
                ],
                [
                    'id'   => 13,
                    'name' => '天津',
                ],
                [
                    'id'   => 12,
                    'name' => '上海',
                ],
                [
                    'id'   => 23,
                    'name' => '安徽',
                ],
                [
                    'id'   => 16,
                    'name' => '山西',
                ],
                [
                    'id'   => 29,
                    'name' => '湖南',
                ],
                [
                    'id'   => 25,
                    'name' => '江西',
                ],
                [
                    'id'   => 28,
                    'name' => '湖北',
                ],
                [
                    'id'   => 24,
                    'name' => '福建',
                ],
                [
                    'id'   => 31,
                    'name' => '广西',
                ],
                [
                    'id'   => 15,
                    'name' => '河北',
                ],
                [
                    'id'   => 37,
                    'name' => '陕西',
                ],
                [
                    'id'   => 22,
                    'name' => '浙江',
                ],
                [
                    'id'   => 18,
                    'name' => '辽宁',
                ],
                [
                    'id'   => 42,
                    'name' => '台湾',
                ],
                [
                    'id'   => 32,
                    'name' => '海南',
                ],
                [
                    'id'   => 43,
                    'name' => '香港',
                ],
                [
                    'id'   => 44,
                    'name' => '澳门',
                ],
                [
                    'id'   => 17,
                    'name' => '内蒙古',
                ],
                [
                    'id'   => 20,
                    'name' => '黑龙江',
                ],
                [
                    'id'   => 19,
                    'name' => '吉林',
                ],
                [
                    'id'   => 38,
                    'name' => '甘肃',
                ],
                [
                    'id'   => 40,
                    'name' => '宁夏',
                ],
                [
                    'id'   => 41,
                    'name' => '新疆',
                ],
                [
                    'id'   => 39,
                    'name' => '青海',
                ],
                [
                    'id'   => 34,
                    'name' => '贵州',
                ],
                [
                    'id'   => 35,
                    'name' => '云南',
                ],
                [
                    'id'   => 36,
                    'name' => '西藏',
                ],
                [
                    'id'   => 271,
                    'name' => '海外',
                ],
            ],
            'city'     => [
                [
                    'id'   => 11,
                    'name' => '北京',
                ],
                [
                    'id'   => 12,
                    'name' => '上海',
                ],
                [
                    'id'   => 46,
                    'name' => '广州',
                ],
                [
                    'id'   => 45,
                    'name' => '深圳',
                ],
                [
                    'id'   => 47,
                    'name' => '成都',
                ],
                [
                    'id'   => 49,
                    'name' => '南京',
                ],
                [
                    'id'   => 48,
                    'name' => '武汉',
                ],
                [
                    'id'   => 14,
                    'name' => '重庆',
                ],
                [
                    'id'   => 51,
                    'name' => '杭州',
                ],
                [
                    'id'   => 68,
                    'name' => '合肥',
                ],
                [
                    'id'   => 50,
                    'name' => '西安',
                ],
                [
                    'id'   => 85,
                    'name' => '长沙',
                ],
                [
                    'id'   => 87,
                    'name' => '郑州',
                ],
                [
                    'id'   => 13,
                    'name' => '天津',
                ],
                [
                    'id'   => 80,
                    'name' => '青岛',
                ],
                [
                    'id'   => 55,
                    'name' => '苏州',
                ],
            ],
        ],
        'major'   => [
            [
                'name' => '工学',
                'list' => [
                    [
                        'id'   => 151,
                        'name' => '力学',
                    ],
                    [
                        'id'   => 152,
                        'name' => '机械工程',
                    ],
                    [
                        'id'   => 153,
                        'name' => '光学工程',
                    ],
                    [
                        'id'   => 154,
                        'name' => '仪器科学与技术',
                    ],
                    [
                        'id'   => 155,
                        'name' => '材料科学与工程',
                    ],
                    [
                        'id'   => 535,
                        'name' => '纳米科学与工程',
                    ],
                    [
                        'id'   => 156,
                        'name' => '冶金工程',
                    ],
                    [
                        'id'   => 546,
                        'name' => '能源动力',
                    ],
                    [
                        'id'   => 157,
                        'name' => '动力工程及工程热物理',
                    ],
                    [
                        'id'   => 158,
                        'name' => '电气工程',
                    ],
                    [
                        'id'   => 159,
                        'name' => '电子科学与技术',
                    ],
                    [
                        'id'   => 160,
                        'name' => '信息与通信工程',
                    ],
                    [
                        'id'   => 161,
                        'name' => '控制科学与工程',
                    ],
                    [
                        'id'   => 162,
                        'name' => '计算机科学与技术',
                    ],
                    [
                        'id'   => 536,
                        'name' => '智能科学与技术',
                    ],
                    [
                        'id'   => 537,
                        'name' => '人工智能',
                    ],
                    [
                        'id'   => 163,
                        'name' => '建筑学',
                    ],
                    [
                        'id'   => 164,
                        'name' => '土木工程与土木水利',
                    ],
                    [
                        'id'   => 165,
                        'name' => '水利工程',
                    ],
                    [
                        'id'   => 166,
                        'name' => '测绘科学与技术',
                    ],
                    [
                        'id'   => 538,
                        'name' => '遥感科学与技术',
                    ],
                    [
                        'id'   => 167,
                        'name' => '化学工程与技术',
                    ],
                    [
                        'id'   => 168,
                        'name' => '地质资源与地质工程',
                    ],
                    [
                        'id'   => 169,
                        'name' => '矿业工程',
                    ],
                    [
                        'id'   => 170,
                        'name' => '石油与天然气工程',
                    ],
                    [
                        'id'   => 171,
                        'name' => '纺织科学与工程',
                    ],
                    [
                        'id'   => 172,
                        'name' => '轻工技术与工程',
                    ],
                    [
                        'id'   => 173,
                        'name' => '交通运输工程',
                    ],
                    [
                        'id'   => 174,
                        'name' => '船舶与海洋工程',
                    ],
                    [
                        'id'   => 175,
                        'name' => '航空宇航科学与技术',
                    ],
                    [
                        'id'   => 176,
                        'name' => '兵器科学与技术',
                    ],
                    [
                        'id'   => 177,
                        'name' => '核科学与技术',
                    ],
                    [
                        'id'   => 178,
                        'name' => '农业工程',
                    ],
                    [
                        'id'   => 179,
                        'name' => '林业工程',
                    ],
                    [
                        'id'   => 180,
                        'name' => '环境科学与工程',
                    ],
                    [
                        'id'   => 181,
                        'name' => '生物医学工程',
                    ],
                    [
                        'id'   => 182,
                        'name' => '食品科学与工程',
                    ],
                    [
                        'id'   => 183,
                        'name' => '城乡规划学',
                    ],
                    [
                        'id'   => 184,
                        'name' => '风景园林学',
                    ],
                    [
                        'id'   => 185,
                        'name' => '软件工程',
                    ],
                    [
                        'id'   => 186,
                        'name' => '生物工程',
                    ],
                    [
                        'id'   => 187,
                        'name' => '安全科学与工程',
                    ],
                    [
                        'id'   => 188,
                        'name' => '公安技术',
                    ],
                    [
                        'id'   => 189,
                        'name' => '网络空间安全',
                    ],
                    // [
                    //     'id'   => 190,
                    //     'name' => '电子信息',
                    // ],
                    [
                        'id'   => 531,
                        'name' => '工科大类（未明确具体学科）',
                    ],
                ],
            ],
            [
                'name' => '理学',
                'list' => [
                    [
                        'id'   => 137,
                        'name' => '数学',
                    ],
                    [
                        'id'   => 138,
                        'name' => '物理学',
                    ],
                    [
                        'id'   => 139,
                        'name' => '化学',
                    ],
                    [
                        'id'   => 140,
                        'name' => '天文学',
                    ],
                    [
                        'id'   => 141,
                        'name' => '地理学',
                    ],
                    [
                        'id'   => 142,
                        'name' => '大气科学',
                    ],
                    [
                        'id'   => 143,
                        'name' => '海洋科学',
                    ],
                    [
                        'id'   => 144,
                        'name' => '地球物理学',
                    ],
                    [
                        'id'   => 145,
                        'name' => '地质学',
                    ],
                    [
                        'id'   => 146,
                        'name' => '生物学',
                    ],
                    [
                        'id'   => 147,
                        'name' => '系统科学',
                    ],
                    [
                        'id'   => 148,
                        'name' => '科学技术史',
                    ],
                    [
                        'id'   => 149,
                        'name' => '生态学',
                    ],
                    [
                        'id'   => 150,
                        'name' => '统计学',
                    ],
                    [
                        'id'   => 532,
                        'name' => '理科大类（未明确具体学科）',
                    ],
                ],
            ],
            [
                'name' => '经济学',
                'list' => [
                    [
                        'id'   => 120,
                        'name' => '理论经济学',
                    ],
                    [
                        'id'   => 121,
                        'name' => '应用经济学',
                    ],
                    [
                        'id'   => 545,
                        'name' => '金融学',
                    ],
                    [
                        'id'   => 533,
                        'name' => '数字经济',
                    ],
                ],
            ],
            [
                'name' => '管理学',
                'list' => [
                    [
                        'id'   => 221,
                        'name' => '管理科学与工程',
                    ],
                    [
                        'id'   => 222,
                        'name' => '工商管理',
                    ],
                    [
                        'id'   => 547,
                        'name' => '审计',
                    ],
                    [
                        'id'   => 539,
                        'name' => '旅游与酒店管理',
                    ],
                    [
                        'id'   => 223,
                        'name' => '农林经济管理',
                    ],
                    [
                        'id'   => 224,
                        'name' => '公共管理学',
                    ],
                    [
                        'id'   => 225,
                        'name' => '信息资源管理（图书馆、情报与档案管理）',
                    ],
                ],
            ],
            [
                'name' => '法学',
                'list' => [
                    [
                        'id'   => 122,
                        'name' => '法学与法律',
                    ],
                    [
                        'id'   => 123,
                        'name' => '政治学',
                    ],
                    [
                        'id'   => 124,
                        'name' => '社会学',
                    ],
                    [
                        'id'   => 125,
                        'name' => '民族学',
                    ],
                    [
                        'id'   => 126,
                        'name' => '马克思主义理论',
                    ],
                    [
                        'id'   => 127,
                        'name' => '公安/警务/纪检监察学',
                    ],
                ],
            ],
            [
                'name' => '哲学',
                'list' => [
                    [
                        'id'   => 119,
                        'name' => '哲学',
                    ],
                ],
            ],
            [
                'name' => '历史学',
                'list' => [
                    [
                        'id'   => 134,
                        'name' => '考古学及博物馆',
                    ],
                    [
                        'id'   => 135,
                        'name' => '中国史',
                    ],
                    [
                        'id'   => 136,
                        'name' => '世界史',
                    ],
                ],
            ],
            [
                'name' => '教育学',
                'list' => [
                    [
                        'id'   => 128,
                        'name' => '教育学',
                    ],
                    [
                        'id'   => 129,
                        'name' => '心理学',
                    ],
                    [
                        'id'   => 130,
                        'name' => '体育学',
                    ],
                ],
            ],
            [
                'name' => '文学',
                'list' => [
                    [
                        'id'   => 131,
                        'name' => '中国语言文学',
                    ],
                    [
                        'id'   => 132,
                        'name' => '外国语言文学',
                    ],
                    [
                        'id'   => 534,
                        'name' => '翻译',
                    ],
                    [
                        'id'   => 133,
                        'name' => '新闻传播学',
                    ],
                ],
            ],
            [
                'name' => '艺术学',
                'list' => [
                    [
                        'id'   => 226,
                        'name' => '艺术学理论',
                    ],
                    [
                        'id'   => 227,
                        'name' => '音乐与舞蹈学',
                    ],
                    [
                        'id'   => 228,
                        'name' => '戏剧、戏曲、影视学',
                    ],
                    [
                        'id'   => 229,
                        'name' => '美术与书法',
                    ],
                    [
                        'id'   => 230,
                        'name' => '设计学',
                    ],
                ],
            ],
            [
                'name' => '医学',
                'list' => [
                    [
                        'id'   => 200,
                        'name' => '基础医学',
                    ],
                    [
                        'id'   => 201,
                        'name' => '临床医学',
                    ],
                    [
                        'id'   => 202,
                        'name' => '口腔医学',
                    ],
                    [
                        'id'   => 203,
                        'name' => '公共卫生与预防医学',
                    ],
                    [
                        'id'   => 204,
                        'name' => '中医学',
                    ],
                    [
                        'id'   => 205,
                        'name' => '中西医结合',
                    ],
                    [
                        'id'   => 206,
                        'name' => '药学',
                    ],
                    [
                        'id'   => 207,
                        'name' => '中药学',
                    ],
                    [
                        'id'   => 208,
                        'name' => '特种医学',
                    ],
                    [
                        'id'   => 209,
                        'name' => '医学技术',
                    ],
                    [
                        'id'   => 210,
                        'name' => '护理学',
                    ],
                ],
            ],
            [
                'name' => '交叉学科',
                'list' => [
                    [
                        'id'   => 231,
                        'name' => '集成电路科学与工程',
                    ],
                    [
                        'id'   => 232,
                        'name' => '国家安全学',
                    ],
                    [
                        'id'   => 540,
                        'name' => '区域国别学',
                    ],
                ],
            ],
            [
                'name' => '农学',
                'list' => [
                    [
                        'id'   => 191,
                        'name' => '作物学',
                    ],
                    [
                        'id'   => 192,
                        'name' => '园艺学',
                    ],
                    [
                        'id'   => 193,
                        'name' => '农业与农业资源利用',
                    ],
                    [
                        'id'   => 194,
                        'name' => '植物保护',
                    ],
                    [
                        'id'   => 195,
                        'name' => '畜牧学',
                    ],
                    [
                        'id'   => 196,
                        'name' => '兽医学',
                    ],
                    [
                        'id'   => 197,
                        'name' => '林学',
                    ],
                    [
                        'id'   => 198,
                        'name' => '水产',
                    ],
                    [
                        'id'   => 199,
                        'name' => '草学',
                    ],
                ],
            ],
            [
                'name' => '军事学',
                'list' => [
                    [
                        'id'   => 211,
                        'name' => '军事思想及军事历史',
                    ],
                    [
                        'id'   => 212,
                        'name' => '战略、作战学',
                    ],
                    [
                        'id'   => 213,
                        'name' => '战役学',
                    ],
                    [
                        'id'   => 214,
                        'name' => '战术学',
                    ],
                    [
                        'id'   => 215,
                        'name' => '军队指挥、作战指挥与军事智能',
                    ],
                    [
                        'id'   => 216,
                        'name' => '军事管理学',
                    ],
                    [
                        'id'   => 217,
                        'name' => '军队政治工作学与战时政治工作',
                    ],
                    [
                        'id'   => 218,
                        'name' => '军事后勤学（含后勤与装备保障）',
                    ],
                    [
                        'id'   => 219,
                        'name' => '军事装备学',
                    ],
                    [
                        'id'   => 220,
                        'name' => '军事训练学（含军事训练与管理） ',
                    ],
                ],
            ],
            [
                'name' => '专业不限/未分类',
                'list' => [
                    [
                        'id'   => 235,
                        'name' => '专业不限',
                    ],
                    [
                        'id'   => 236,
                        'name' => '专业未分类',
                    ],
                ],
            ],
        ],
        // 特色专栏
        // Web
        // 热门专题
        'column'  => [
            [
                'name'     => '专题展',
                'moreLink' => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/zt_jiheye/index.html',
                'list'     => [
                    // [
                    //     'name' => '喜迎国庆·招聘专题系列',
                    //     'url'  => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/ycc_nationalday2022/index.html',
                    // ],
                    [
                        'name' => '不同“编制”有什么区别？',
                        'url'  => 'http://www.gaoxiaojob.com/zhaopin/ycb_bzngangweituijian/index.html',
                    ],
                    [
                        'name' => '利弊分析：博士后到底值不值得做',
                        'url'  => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/ycb_boshihou/index.html',
                    ],
                    [
                        'name' => '「高校辅导员」岗位分析&求职攻略',
                        'url'  => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/ycb_fdyanalysis/index.html',
                    ],
                    [
                        'name' => '高校教学支撑岗位分析专题',
                        'url'  => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/jobAnalysis2022/index.html',
                    ],
                    // [
                    //     'name' => '八一建军节丨国防军工类名校推荐',
                    //     'url'  => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/armyday2022/index.html',
                    // ],
                    //
                    // [
                    //     'name' => '高校人才网15周年庆',
                    //     'url'  => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/anniversary2022/index.html',
                    // ],
                    [
                        'name' => '研究生毕业为什么越来越难？',
                        'url'  => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/graduatehard2022/index.html',
                    ],
                    // [
                    //     'name' => '高校人才网第六届征文活动',
                    //     'url'  => 'http://t.jugaocai.com/gczhengwen',
                    // ],
                    // [
                    //     'name' => '七一建党节招聘专题',
                    //     'url'  => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/jiandangjie/index.html',
                    // ],
                    // [
                    //     'name' => '申硕建设高校引才专题',
                    //     'url'  => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/gaoxiaoshenshuo/index.html',
                    // ],
                    // [
                    //     'name' => '高才优课：讲师入驻申请',
                    //     'url'  => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/gaocaiyouke/index.html',
                    // ],
                    // [
                    //     'name' => '高校人才网15周年庆',
                    //     'url'  => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/anniversary2022/index.html',
                    // ],
                    // [
                    //     'name' => '找工作防骗指南',
                    //     'url'  => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/laborDay2022/index.html',
                    // ],
                    // [
                    //     'name' => '博士就业三座大山',
                    //     'url'  => 'http://gaoxiaojob.com/zhaopin/zhuanti/boshijieyenan/index.html',
                    // ],
                    // [
                    //     'name' => '三八妇女节专题：致敬了不起的「她」',
                    //     'url'  => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/goddess-festival/index.html',
                    // ],
                    // [
                    //     'name' => '高才2021年度回顾',
                    //     'url'  => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/annual-report/index.html',
                    // ],
                    // [
                    //     'name' => '2022国家公务员考试专题·面试篇',
                    //     'url'  => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/civil-servants-topic/index.html',
                    // ],
                    // [
                    //     'name' => '政府引才之窗系列专题',
                    //     'url'  => 'http://www.gaoxiaojob.com/zhaopin/sydw/zhengfuyincai/index.html',
                    // ],
                    // [
                    //     'name' => '观点热议：读研，你后悔了吗？',
                    //     'url'  => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/kaoyanzhuanti/index.html',
                    // ],
                ],
            ],
            [
                'name'     => '情报局',
                // 这里其实是搜索结果页
                'moreLink' => 'http://www.gaoxiaojob.com/search?keyword=%E6%83%85%E6%8A%A5%E5%B1%80&type=2',
                'list'     => [
                    [
                        'name' => 'Vol.56 硕博进高校的“蹊径”',
                        'url'  => 'http://www.gaoxiaojob.com/news/detail/1492.html',
                    ],
                    [
                        'name' => 'Vol.55 985博导硬核相亲',
                        'url'  => 'http://www.gaoxiaojob.com/news/detail/1463.html',
                    ],
                    [
                        'name' => 'Vol.54 薪酬vs稳定，选哪个',
                        'url'  => 'http://www.gaoxiaojob.com/news/detail/1428.html',
                    ],

                    [
                        'name' => 'Vol.53 读博后待遇就会更好吗？',
                        'url'  => 'http://www.gaoxiaojob.com/news/detail/1404.html',
                    ],
                    [
                        'name' => 'Vol.52 博士去末流高校值得吗？',
                        'url'  => 'http://www.gaoxiaojob.com/news/detail/1357.html',
                    ],
                    [
                        'name' => 'Vol.51 “教师退出机制”有何影响？',
                        'url'  => 'http://www.gaoxiaojob.com/news/detail/1327.html',
                    ],
                    [
                        'name' => 'Vol.50 “双非”博士到底有没有用？',
                        'url'  => 'http://www.gaoxiaojob.com/news/detail/1281.html',
                    ],
                    [
                        'name' => 'Vol.49 复旦「非升即走」替代计划',
                        'url'  => 'http://www.gaoxiaojob.com/news/detail/1242.html',
                    ],
                    [
                        'name' => 'Vol.48 博士毕业找不到工作',
                        'url'  => 'http://www.gaoxiaojob.com/news/detail/1212.html',
                    ],
                    // [
                    //     'name' => 'Vol.47 盘点高校“奇葩”引才',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/1170.html',
                    // ],
                    // [
                    //     'name' => 'Vol.46 民办高校利弊分析',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/1113.html',
                    // ],
                    // [
                    //     'name' => 'Vol.45 寒门博士逆袭靠专业？',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/1049.html',
                    // ],
                    // [
                    //     'name' => 'Vol.44 高校青年教师收入待遇',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/1017.html',
                    // ],
                    // [
                    //     'name' => 'Vol.43 未来还有“铁饭碗”岗位吗？',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/1015.html',
                    // ],
                    // [
                    //     'name' => 'Vol.42 高校“退休潮”要来了',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/962.html',
                    // ],
                    // [
                    //     'name' => 'Vol.41 建议取消教师带薪寒暑假？',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/943.html',
                    // ],
                    // [
                    //     'name' => 'Vol.40 上百名硕博新生租房读研',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/874.html',
                    // ],
                    // [
                    //     'name' => 'Vol.39 七成高校教师无博士学位？',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/817.html',
                    // ],
                    // [
                    //     'name' => 'Vol.38 上财研究生送外卖？',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/789.html',
                    // ],
                    // [
                    //     'name' => 'Vol.37 硕博男女婚恋实录',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/747.html',
                    // ],
                    // [
                    //     'name' => 'Vol.36 高校批量“水博”影响有多大？',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/714.html',
                    // ],
                    // [
                    //     'name' => 'Vol.35 如何判断高校待遇是否真实？',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/683.html',
                    // ],
                    // [
                    //     'name' => 'Vol.34 「小镇做题家」到底招惹了谁？',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/647.html',
                    // ],
                    // [
                    //     'name' => 'Vol.33 高校教师抄袭事件',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/613.html',
                    // ],
                    // [
                    //     'name' => 'Vol.32 郑州670名硕博被坑',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/545.html',
                    // ],
                    // [
                    //     'name' => 'Vol.31 大城市vs小县城选哪个？',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/530.html',
                    // ],
                    // [
                    //     'name' => 'Vol.30 高校图书馆员如何？',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/495.html',
                    // ],
                    // [
                    //     'name' => 'Vol.29 高校科研助理岗值得做吗',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/459.html',
                    // ],
                    // [
                    //     'name' => 'Vol.28 走后门进高校的多吗',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/414.html',
                    // ],
                    // [
                    //     'name' => 'Vol.27 硕博都找到工作了吗',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/391.html',
                    // ],
                    // [
                    //     'name' => 'Vol.26 35岁职场危机',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/357.html',
                    // ],
                    // [
                    //     'name' => 'Vol.25 博士不做科研有意义吗',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/321.html',
                    // ],
                    // [
                    //     'name' => 'Vol.24 教师降薪现象',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/290.html',
                    // ],
                    // [
                    //     'name' => 'Vol.23 高校实验室事故',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/278.html',
                    // ],
                    // [
                    //     'name' => 'Vol.22 “反向背调”成新风向',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/238.html',
                    // ],
                    // [
                    //     'name' => 'Vol.21 女博士真的不好嫁吗？',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/202.html',
                    // ],
                    // [
                    //     'name' => 'Vol.20 中国博士是不是太多了？',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/166.html',
                    // ],
                    // [
                    //     'name' => 'Vol.19 博士第一学历重要吗？',
                    //     'url'  => 'http://www.gaoxiaojob.com/news/detail/143.html',
                    // ],
                ],
            ],
            [
                'name' => '服务站',
                'list' => [
                    [
                        'name' => '海内外青年科技人才招聘会',
                        'url'  => 'http://t.jugaocai.com/JXFmKG',
                    ],

                    [
                        'name' => '高才优课丨求职学习服务平台',
                        'url'  => 'http://t.jugaocai.com/UwEwH4',
                    ],
                    [
                        'name' => '博士后简历代投服务',
                        'url'  => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/gaoxiaoboshihou/index.html',
                    ],
                    [
                        'name' => '硕博人才简历直推服务',
                        'url'  => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/gaocaizhitui/index.html',
                    ],
                    [
                        'name' => '高校人才网宣讲直播间',
                        'url'  => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/kongzhongzhibojian2020/index.html',
                    ],
                    [
                        'name' => '高校人才网特色人才服务项目',
                        'url'  => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/rpo_item/index.html',
                    ],

                ],
            ],
        ],
        'job'     => [
            [
                'name' => '高校教学、教辅、管理岗',
                'list' => [
                    [
                        'name'    => '博士后',
                        // 'categoryJobId' => 29,
                        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1077
                        'fullUrl' => 'https://boshihou.gaoxiaojob.com',
                        'isHot'   => 1,
                    ],
                    [
                        'name'          => '辅导员岗',
                        'categoryJobId' => 34,
                        'isHot'         => 1,
                    ],
                    [
                        'name'          => '专职教师/教学科研岗',
                        'categoryJobId' => 33,
                        'isHot'         => 1,
                    ],
                    [
                        'name'          => '教授/副教授',
                        'categoryJobId' => 32,
                        'isHot'         => 1,
                    ],
                    [
                        'name'          => '实验技术岗',
                        'categoryJobId' => 36,
                        'isHot'         => 1,
                    ],
                    [
                        'name'          => '教务岗',
                        'categoryJobId' => 35,
                        'isHot'         => 1,
                    ],
                    [
                        'name'          => '图书馆岗',
                        'categoryJobId' => 37,
                        'isHot'         => 1,
                    ],
                    [
                        'name'          => '党务行政岗',
                        'categoryJobId' => 38,
                        'isHot'         => 1,
                    ],
                    [
                        'name'          => '技术支撑岗',
                        'categoryJobId' => 39,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '其他支撑岗',
                        'categoryJobId' => 40,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '学科带头人/学术骨干',
                        'categoryJobId' => 31,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '学术领军人才',
                        'categoryJobId' => 30,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '高校/科研机构中层党政部门负责人',
                        'categoryJobId' => 46,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '系/研究所/实验室负责人',
                        'categoryJobId' => 45,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '二级学院院长/副院长',
                        'categoryJobId' => 44,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '高校校长/校领导/单位负责人',
                        'categoryJobId' => 41,
                        'isHot'         => 0,
                    ],
                ],
            ],
            [
                'name' => '高校/科研单位科学研究岗',
                'list' => [
                    [
                        'name'          => '专职科研岗',
                        'categoryJobId' => 61,
                        'isHot'         => 1,
                    ],
                    [
                        'name'          => '科研助理岗',
                        'categoryJobId' => 62,
                        'isHot'         => 1,
                    ],
                    [
                        'name'          => '实验技术岗',
                        'categoryJobId' => 63,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '医学教学/科研人才',
                        'categoryJobId' => 64,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '高级研究人员（正/副研究员）',
                        'categoryJobId' => 60,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '科研领军人才',
                        'categoryJobId' => 59,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '其他科研支撑岗',
                        'categoryJobId' => 65,
                        'isHot'         => 0,
                    ],
                ],
            ],
            [
                'name' => '中小学及技术职业院校岗',
                'list' => [
                    [
                        'name'          => '中小学普通教师岗',
                        'categoryJobId' => 49,
                        'isHot'         => 1,
                    ],
                    [
                        'name'          => '中小学骨干教师岗',
                        'categoryJobId' => 48,
                        'isHot'         => 1,
                    ],
                    [
                        'name'          => '中职教师岗（公共课类）',
                        'categoryJobId' => 50,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '中职教师岗（专业课类）',
                        'categoryJobId' => 51,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '专职班主任/辅导员',
                        'categoryJobId' => 56,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '教务岗',
                        'categoryJobId' => 53,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '实验技术岗',
                        'categoryJobId' => 54,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '教研员',
                        'categoryJobId' => 58,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '生活老师',
                        'categoryJobId' => 55,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '学前教师/幼师',
                        'categoryJobId' => 52,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '幼儿园园长',
                        'categoryJobId' => 43,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '中小学校中层党政部门负责人',
                        'categoryJobId' => 47,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '中小学校长/校领导/单位负责人',
                        'categoryJobId' => 42,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '其他教学/行政支撑岗',
                        'categoryJobId' => 57,
                        'isHot'         => 0,
                    ],

                ],
            ],
            [
                'name' => '政府机关/事业单位/军队工作人员岗',
                'list' => [
                    [
                        'name'          => '公务员',
                        'categoryJobId' => 73,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '选调生（毕业生）',
                        'categoryJobId' => 74,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '军队文职人员',
                        'categoryJobId' => 75,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '工作人员（综合类）',
                        'categoryJobId' => 76,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '工作人员（技术人员岗类）',
                        'categoryJobId' => 77,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '工作人员（工勤技能类）',
                        'categoryJobId' => 78,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '辅助协助岗人员',
                        'categoryJobId' => 79,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '社区工作人员/村官/网格员',
                        'categoryJobId' => 80,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '领导干部岗（处级以上）',
                        'categoryJobId' => 72,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '其他政府与事业单位岗',
                        'categoryJobId' => 81,
                        'isHot'         => 0,
                    ],
                ],
            ],
            [
                'name' => '医疗卫生专业岗',
                'list' => [
                    [
                        'name'          => '主治医师/住院医师/医生',
                        'categoryJobId' => 85,
                        'isHot'         => 1,
                    ],
                    [
                        'name'          => '主任医师/副主任医师',
                        'categoryJobId' => 84,
                        'isHot'         => 1,
                    ],
                    [
                        'name'          => '普通医技岗',
                        'categoryJobId' => 92,
                        'isHot'         => 1,
                    ],
                    [
                        'name'          => '普通药师岗',
                        'categoryJobId' => 88,
                        'isHot'         => 1,
                    ],
                    [
                        'name'          => '普通护理岗',
                        'categoryJobId' => 90,
                        'isHot'         => 1,
                    ],
                    [
                        'name'          => '中高级医技岗',
                        'categoryJobId' => 91,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '中高级药师岗',
                        'categoryJobId' => 87,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '中高级护理岗',
                        'categoryJobId' => 89,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '学科带头人/学术骨干',
                        'categoryJobId' => 83,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '院长/副院长',
                        'categoryJobId' => 82,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '规培岗',
                        'categoryJobId' => 86,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '党务行政岗',
                        'categoryJobId' => 94,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '校医人员',
                        'categoryJobId' => 95,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '医务医辅人员',
                        'categoryJobId' => 93,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '其他医疗卫生岗',
                        'categoryJobId' => 96,
                        'isHot'         => 0,
                    ],
                ],
            ],
            [
                'name' => '企事业单位科学研究岗',
                'list' => [
                    [
                        'name'          => '专职科研岗',
                        'categoryJobId' => 68,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '科研助理岗',
                        'categoryJobId' => 69,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '实验技术岗',
                        'categoryJobId' => 70,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '高级研究人员（正/副研究员）',
                        'categoryJobId' => 67,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '科研领军人才',
                        'categoryJobId' => 66,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '其他科研支撑岗',
                        'categoryJobId' => 71,
                        'isHot'         => 0,
                    ],
                ],
            ],
            [
                'name' => '企业单位岗',
                'list' => [
                    [
                        'name'          => '人力资源管理岗',
                        'categoryJobId' => 102,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '行政管理岗',
                        'categoryJobId' => 109,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '党务党群岗',
                        'categoryJobId' => 111,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '财务管理岗',
                        'categoryJobId' => 115,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '法务',
                        'categoryJobId' => 124,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '知识产权与专利',
                        'categoryJobId' => 130,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '营销策划岗',
                        'categoryJobId' => 141,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '编辑/作家/撰稿人',
                        'categoryJobId' => 149,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '技术管理岗',
                        'categoryJobId' => 173,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '前后端程序开发岗',
                        'categoryJobId' => 175,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '银行岗',
                        'categoryJobId' => 234,
                        'isHot'         => 0,
                    ],
                    [
                        'name'          => '管培生/培训干部',
                        'categoryJobId' => 255,
                        'isHot'         => 0,
                    ],

                ],
            ],

        ],
        'company' => [
            [
                'name' => '高校与党校单位',
                'list' => [
                    [
                        'name'   => '双一流院校',
                        'typeId' => 1,
                        'isHot'  => 1,
                    ],
                    [
                        'name'   => '普通本科院校',
                        'typeId' => 2,
                        'isHot'  => 1,
                    ],
                    [
                        'name'   => '高职高专院校',
                        'typeId' => 3,
                        'isHot'  => 1,
                    ],
                    [
                        'name'   => '党校与行政学院',
                        'typeId' => 4,
                        'isHot'  => 0,
                    ],
                ],
            ],
            [
                'name' => '科研单位',
                'list' => [
                    [
                        'name'   => '中国科学院系统研究机构',
                        'typeId' => 16,
                        'isHot'  => 1,
                    ],
                    [
                        'name'   => '人文社科研究机构',
                        'typeId' => 17,
                        'isHot'  => 0,
                    ],
                    [
                        'name'   => '自然与应用科研机构',
                        'typeId' => 18,
                        'isHot'  => 0,
                    ],
                    [
                        'name'   => '企业研发机构',
                        'typeId' => 19,
                        'isHot'  => 0,
                    ],
                ],
            ],
            [
                'name' => '中小学&幼儿园单位',
                'list' => [
                    [
                        'name'   => '中小学',
                        'typeId' => 13,
                        'isHot'  => 1,
                    ],
                    [
                        'name'   => '中专&职业中学&技师学院',
                        'typeId' => 14,
                        'isHot'  => 0,
                    ],
                    [
                        'name'   => '幼儿园',
                        'typeId' => 15,
                        'isHot'  => 0,
                    ],

                ],
            ],
            [
                'name' => '政府与事业单位',
                'list' => [
                    [
                        'name'   => '机关单位',
                        'typeId' => 5,
                        'isHot'  => 0,
                    ],
                    [
                        'name'   => '军队武警单位',
                        'typeId' => 6,
                        'isHot'  => 0,
                    ],
                    [
                        'name'   => '事业单位',
                        'typeId' => 7,
                        'isHot'  => 0,
                    ],
                ],
            ],
            [
                'name' => '医疗单位',
                'list' => [
                    [
                        'name'   => '医院',
                        'typeId' => 21,
                        'isHot'  => 1,
                    ],
                    [
                        'name'   => '其他医疗机构',
                        'typeId' => 22,
                        'isHot'  => 0,
                    ],
                ],
            ],
            [
                'name' => '企业单位',
                'list' => [
                    [
                        'name'   => '政府国有企业',
                        'typeId' => 8,
                        'isHot'  => 0,
                    ],
                    [
                        'name'   => '知名企业',
                        'typeId' => 9,
                        'isHot'  => 0,
                    ],
                    [
                        'name'   => '中小成长型企业',
                        'typeId' => 10,
                        'isHot'  => 0,
                    ],
                    [
                        'name'   => '银行、信用社等金融机构',
                        'typeId' => 11,
                        'isHot'  => 0,
                    ],

                ],
            ],
            [
                'name' => '热门地区',
                'list' => [
                    [
                        'name'   => '北京',
                        'areaId' => 1,
                    ],
                    [
                        'name'   => '天津',
                        'areaId' => 19,
                    ],
                    [
                        'name'   => '哈尔滨',
                        'areaId' => 656,
                    ],
                    [
                        'name'   => '上海',
                        'areaId' => 801,
                    ],
                    [
                        'name'   => '南京',
                        'areaId' => 821,
                    ],
                    [
                        'name'   => '杭州',
                        'areaId' => 934,
                    ],
                    [
                        'name'   => '合肥',
                        'areaId' => 1047,
                    ],
                    [
                        'name'   => '福州',
                        'areaId' => 1169,
                    ],
                    [
                        'name'   => '济南',
                        'areaId' => 1376,
                    ],
                    [
                        'name'   => '郑州',
                        'areaId' => 1533,
                    ],
                    [
                        'name'   => '武汉',
                        'areaId' => 1710,
                    ],
                    [
                        'name'   => '长沙',
                        'areaId' => 1828,
                    ],
                    [
                        'name'   => '广州',
                        'areaId' => 1965,
                    ],
                    [
                        'name'   => '深圳',
                        'areaId' => 1988,
                    ],
                    [
                        'name'   => '珠海',
                        'areaId' => 1999,
                    ],
                    [
                        'name'   => '重庆',
                        'areaId' => 2323,
                    ],
                    [
                        'name'   => '成都',
                        'areaId' => 2368,
                    ],
                    [
                        'name'   => '西安',
                        'areaId' => 2899,
                    ],
                ],
            ],
        ],
    ],

    // 首页职位类型分类导航(最终是跳转到职位列表页)
    'homeJobNav'              => [
        [
            'name' => '博士后',
            'list' => [
                [
                    'name' => '博士后',
                    'id'   => 29,
                ],
            ],
        ],
        [
            'name' => '教学岗（高等院校）',
            'list' => [
                [
                    'name' => '学术领军人才',
                    'id'   => 30,
                ],
                [
                    'name' => '学科带头人/学术骨干',
                    'id'   => 31,
                ],
                [
                    'name' => '教授/副教授',
                    'id'   => 32,
                ],
                [
                    'name' => '专职教师/教学科研岗',
                    'id'   => 33,
                ],
                [
                    'name' => '助理教授/助理副教授',
                    'id'   => 259,
                ],
            ],
        ],
        [
            'name' => '教学支撑岗（高等院校）',
            'list' => [
                [
                    'name' => '辅导员岗',
                    'id'   => 34,
                ],
                [
                    'name' => '教务岗',
                    'id'   => 35,
                ],
                [
                    'name' => '实验技术岗',
                    'id'   => 36,
                ],
                [
                    'name' => '图书馆岗',
                    'id'   => 37,
                ],
                [
                    'name' => '党务行政岗',
                    'id'   => 38,
                ],
                [
                    'name' => '技术支撑岗',
                    'id'   => 39,
                ],
                [
                    'name' => '其他支撑岗',
                    'id'   => 40,
                ],
            ],
        ],
        [
            'name' => '中高级管理岗（教育/科研机构）',
            'list' => [
                [
                    'name' => '高校校长/校领导/单位负责人',
                    'id'   => 41,
                ],
                [
                    'name' => '中小学校长/校领导/单位负责人',
                    'id'   => 42,
                ],
                [
                    'name' => '幼儿园园长',
                    'id'   => 43,
                ],
                [
                    'name' => '二级学院院长/副院长',
                    'id'   => 44,
                ],
                [
                    'name' => '系/研究所/实验室负责人',
                    'id'   => 45,
                ],
                [
                    'name' => '高校/科研机构中层党政部门负责人',
                    'id'   => 46,
                ],
                [
                    'name' => '中小学校中层党政部门负责人',
                    'id'   => 47,
                ],
            ],
        ],
        [
            'name' => '教学岗（中小学及幼儿园）',
            'list' => [
                [
                    'name' => '中小学骨干教师岗',
                    'id'   => 48,
                ],
                [
                    'name' => '中小学普通教师岗',
                    'id'   => 49,
                ],
                [
                    'name' => '中职教师岗（公共课类）',
                    'id'   => 50,
                ],
                [
                    'name' => '中职教师岗（专业课类）',
                    'id'   => 51,
                ],
                [
                    'name' => '学前教师/幼师',
                    'id'   => 52,
                ],
                [
                    'name' => '特殊教育教师/康复教师/其他教师岗',
                    'id'   => 260,
                ],
            ],
        ],
        [
            'name' => '教学支撑岗（中小学及幼儿园）',
            'list' => [
                [
                    'name' => '教务岗',
                    'id'   => 53,
                ],
                [
                    'name' => '实验技术岗',
                    'id'   => 54,
                ],
                [
                    'name' => '生活老师',
                    'id'   => 55,
                ],
                [
                    'name' => '专职班主任/辅导员',
                    'id'   => 56,
                ],
                [
                    'name' => '其他教学/行政支撑岗',
                    'id'   => 57,
                ],
                [
                    'name' => '教研员',
                    'id'   => 58,
                ],
                [
                    'name' => '教练员',
                    'id'   => 261,
                ],
            ],
        ],
        [
            'name' => '科学研究岗（教育/科研/卫生单位）',
            'list' => [
                [
                    'name' => '科研领军人才',
                    'id'   => 59,
                ],
                [
                    'name' => '高级研究人员（正/副研究员）',
                    'id'   => 60,
                ],
                [
                    'name' => '助理研究员/研究实习员',
                    'id'   => 262,
                ],
                [
                    'name' => '专职科研岗',
                    'id'   => 61,
                ],
                [
                    'name' => '特别研究助理',
                    'id'   => 263,
                ],
                [
                    'name' => '科研助理岗',
                    'id'   => 62,
                ],
                [
                    'name' => '实验技术岗',
                    'id'   => 36,
                ],
                [
                    'name' => '医学教学/科研人才',
                    'id'   => 64,
                ],
                [
                    'name' => '其他科研支撑岗',
                    'id'   => 65,
                ],
            ],
        ],
        [
            'name' => '科学研究岗（企事业单位）',
            'list' => [
                [
                    'name' => '科研领军人才',
                    'id'   => 66,
                ],
                [
                    'name' => '高级研究人员（正/副研究员）',
                    'id'   => 67,
                ],
                [
                    'name' => '助理研究员/研究实习员',
                    'id'   => 264,
                ],
                [
                    'name' => '专职科研岗',
                    'id'   => 68,
                ],
                [
                    'name' => '科研助理岗',
                    'id'   => 69,
                ],
                [
                    'name' => '实验技术岗',
                    'id'   => 70,
                ],
                [
                    'name' => '其他科研支撑岗',
                    'id'   => 71,
                ],
            ],
        ],
        [
            'name' => '公务员/事业单位/军队工作人员',
            'list' => [
                [
                    'name' => '领导干部岗（处级以上）',
                    'id'   => 72,
                ],
                [
                    'name' => '公务员',
                    'id'   => 73,
                ],
                [
                    'name' => '选调生（毕业生）',
                    'id'   => 74,
                ],
                [
                    'name' => '军队文职人员',
                    'id'   => 75,
                ],
                [
                    'name' => '工作人员（综合类）',
                    'id'   => 76,
                ],
                [
                    'name' => '工作人员（技术人员岗类）',
                    'id'   => 77,
                ],
                [
                    'name' => '工作人员（工勤技能类）',
                    'id'   => 78,
                ],
                [
                    'name' => '辅助协助岗人员',
                    'id'   => 79,
                ],
                [
                    'name' => '社区工作人员/村官/网格员',
                    'id'   => 80,
                ],
                [
                    'name' => '高层次人才',
                    'id'   => 265,
                ],
                [
                    'name' => '其他政府与事业单位岗',
                    'id'   => 81,
                ],
            ],
        ],
        [
            'name' => '医疗卫生专业岗',
            'list' => [
                [
                    'name' => '院长/副院长',
                    'id'   => 82,
                ],
                [
                    'name' => '学科带头人/学术骨干',
                    'id'   => 31,
                ],
                [
                    'name' => '科室主任/副主任中高层管理岗',
                    'id'   => 266,
                ],
                [
                    'name' => '主任医师/副主任医师',
                    'id'   => 84,
                ],
                [
                    'name' => '主治医师/住院医师/医生',
                    'id'   => 85,
                ],
                [
                    'name' => '规培岗',
                    'id'   => 86,
                ],
                [
                    'name' => '中高级药师岗',
                    'id'   => 87,
                ],
                [
                    'name' => '医务医辅人员',
                    'id'   => 93,
                ],
                [
                    'name' => '党务行政岗',
                    'id'   => 94,
                ],
                [
                    'name' => '校医人员',
                    'id'   => 95,
                ],
                [
                    'name' => '其他医疗卫生岗',
                    'id'   => 96,
                ],
            ],
        ],
        [
            'name' => '中高级管理岗（企业）',
            'list' => [
                [
                    'id'   => 97,
                    'name' => '总裁/总经理/CEO',
                ],
                [
                    'id'   => 98,
                    'name' => '副总裁/副总经理/VP/高级合伙人',
                ],
                [
                    'id'   => 99,
                    'name' => '总监/部门高级经理',
                ],
                [
                    'id'   => 100,
                    'name' => '区域/分公司/代表处负责人',
                ],
                [
                    'id'   => 101,
                    'name' => '其他中高级管理岗',
                ],
            ],
        ],
        [
            'isMultiple' => true,
            'name'       => '市场销售咨询服务与职能岗',
            'list'       => [
                [
                    'name' => '人力/行政/财务岗',
                    'list' => [
                        [
                            'id'   => 102,
                            'name' => '人力资源管理岗',
                        ],
                        [
                            'id'   => 103,
                            'name' => '人力资源专业岗',
                        ],
                        [
                            'id'   => 104,
                            'name' => '招聘岗',
                        ],
                        [
                            'id'   => 105,
                            'name' => '薪酬福利岗',
                        ],
                        [
                            'id'   => 106,
                            'name' => '绩效岗',
                        ],
                        [
                            'id'   => 107,
                            'name' => '员工培训岗',
                        ],
                        [
                            'id'   => 108,
                            'name' => '其他人力资源岗',
                        ],
                        [
                            'id'   => 109,
                            'name' => '行政管理岗',
                        ],
                        [
                            'id'   => 110,
                            'name' => '行政事务岗',
                        ],
                        [
                            'id'   => 111,
                            'name' => '党务党群岗',
                        ],
                        [
                            'id'   => 112,
                            'name' => '秘书文员岗',
                        ],
                        [
                            'id'   => 113,
                            'name' => '档案管理岗',
                        ],
                        [
                            'id'   => 114,
                            'name' => '其他行政支撑岗',
                        ],
                        [
                            'id'   => 115,
                            'name' => '财务管理岗',
                        ],
                        [
                            'id'   => 116,
                            'name' => '会计/出纳岗',
                        ],
                        [
                            'id'   => 117,
                            'name' => '审计/税务岗',
                        ],
                        [
                            'id'   => 118,
                            'name' => '数据分析与统计岗',
                        ],
                        [
                            'id'   => 119,
                            'name' => '成本管理岗',
                        ],
                        [
                            'id'   => 120,
                            'name' => '资产管理岗',
                        ],
                        [
                            'id'   => 121,
                            'name' => '其他财务岗',
                        ],
                    ],
                ],
                [
                    'name' => '法务/翻译/咨询/教培岗',
                    'list' => [
                        [
                            'id'   => 122,
                            'name' => '律师',
                        ],
                        [
                            'id'   => 123,
                            'name' => '法律顾问',
                        ],
                        [
                            'id'   => 124,
                            'name' => '法务',
                        ],
                        [
                            'id'   => 125,
                            'name' => '翻译',
                        ],
                        [
                            'id'   => 126,
                            'name' => '商务咨询顾问',
                        ],
                        [
                            'id'   => 127,
                            'name' => '心理/婚恋咨询师',
                        ],
                        [
                            'id'   => 128,
                            'name' => '经纪人/中介',
                        ],
                        [
                            'id'   => 129,
                            'name' => '猎头顾问',
                        ],
                        [
                            'id'   => 130,
                            'name' => '知识产权与专利',
                        ],
                        [
                            'id'   => 131,
                            'name' => '培训产品研发与支持',
                        ],
                        [
                            'id'   => 132,
                            'name' => '中小学学科培训教师',
                        ],
                        [
                            'id'   => 133,
                            'name' => '社会职业培训教师',
                        ],
                        [
                            'id'   => 134,
                            'name' => '兴趣特长培训教师',
                        ],
                        [
                            'name' => '其他法律岗',
                            'id'   => 267,
                        ],
                        [
                            'id'   => 135,
                            'name' => '其他培训岗',
                        ],
                    ],
                ],
                [
                    'name' => '销售/商务/客服/招生岗',
                    'list' => [
                        [
                            'id'   => 136,
                            'name' => '销售管理',
                        ],
                        [
                            'id'   => 137,
                            'name' => '销售顾问/商务拓展',
                        ],
                        [
                            'id'   => 138,
                            'name' => '销售支持及商务支撑',
                        ],
                        [
                            'id'   => 139,
                            'name' => '客服及支持',
                        ],
                        [
                            'id'   => 140,
                            'name' => '招生顾问',
                        ],
                    ],
                ],
                [
                    'name' => '市场/公关/广告/会展岗',
                    'list' => [
                        [
                            'id'   => 141,
                            'name' => '营销策划岗',
                        ],
                        [
                            'id'   => 142,
                            'name' => '市场研究岗',
                        ],
                        [
                            'id'   => 143,
                            'name' => '市场渠道推广岗',
                        ],
                        [
                            'id'   => 144,
                            'name' => '媒介公关岗',
                        ],
                        [
                            'id'   => 145,
                            'name' => '品牌形象岗',
                        ],
                        [
                            'id'   => 146,
                            'name' => '广告事务岗',
                        ],
                        [
                            'id'   => 147,
                            'name' => '会展会务岗',
                        ],
                        [
                            'name' => '其他相关岗',
                            'id'   => 268,
                        ],
                    ],
                ],
                [
                    'name' => '编辑/出版/传媒/文化岗',
                    'list' => [
                        [
                            'id'   => 148,
                            'name' => '社长/总编/副总编',
                        ],
                        [
                            'id'   => 149,
                            'name' => '编辑/作家/撰稿人',
                        ],
                        [
                            'id'   => 150,
                            'name' => '记者采编岗',
                        ],
                        [
                            'id'   => 151,
                            'name' => '出版发行岗',
                        ],
                        [
                            'id'   => 152,
                            'name' => '设计排版印刷岗',
                        ],
                        [
                            'id'   => 153,
                            'name' => '其他编辑出版岗',
                        ],
                        [
                            'id'   => 154,
                            'name' => '影视制作策划岗',
                        ],
                        [
                            'id'   => 155,
                            'name' => '主播/主持人岗',
                        ],
                        [
                            'id'   => 156,
                            'name' => '拍摄与后期制作岗',
                        ],
                        [
                            'id'   => 157,
                            'name' => '导演/编导岗',
                        ],
                        [
                            'id'   => 158,
                            'name' => '其他影视传媒岗',
                        ],
                        [
                            'id'   => 159,
                            'name' => '图书馆岗',
                        ],
                        [
                            'id'   => 160,
                            'name' => '演艺演出岗',
                        ],
                        [
                            'id'   => 161,
                            'name' => '文宣策划岗',
                        ],
                        [
                            'id'   => 162,
                            'name' => '文物保护与考古岗',
                        ],
                        [
                            'id'   => 163,
                            'name' => '文博展览岗',
                        ],
                    ],
                ],
                [
                    'name' => '管培生/其他',
                    'list' => [
                        [
                            'id'   => 255,
                            'name' => '管理培训生/储备干部',
                        ],
                        [
                            'id'   => 256,
                            'name' => '社工/志愿者',
                        ],
                        [
                            'id'   => 257,
                            'name' => '其他',
                        ],
                        [
                            'id'   => 258,
                            'name' => '实习生',
                        ],
                        [
                            'name' => '企业综合类岗位',
                            'id'   => 273,
                        ],
                        [
                            'name' => '企业类高层次人才',
                            'id'   => 274,
                        ],
                        [
                            'name' => '工勤支撑岗',
                            'id'   => 275,
                        ],

                    ],
                ],
            ],
        ],
        [
            'isMultiple' => true,
            'name'       => '互联网及软硬件研发岗',
            'list'       => [
                [
                    'name' => '互联网产品/设计/运营岗',
                    'list' => [
                        [
                            'id'   => 164,
                            'name' => '产品管理岗',
                        ],
                        [
                            'id'   => 165,
                            'name' => '产品经理岗',
                        ],
                        [
                            'id'   => 166,
                            'name' => '视觉/交互设计岗',
                        ],
                        [
                            'id'   => 167,
                            'name' => '工业/艺术设计岗',
                        ],
                        [
                            'id'   => 168,
                            'name' => '运营管理岗',
                        ],
                        [
                            'id'   => 169,
                            'name' => '电商运营岗',
                        ],
                        [
                            'id'   => 170,
                            'name' => '新媒体/内容运营岗',
                        ],
                        [
                            'id'   => 171,
                            'name' => '产品/用户/活动运营岗',
                        ],
                        [
                            'id'   => 172,
                            'name' => '其他运营岗',
                        ],
                    ],
                ],
                [
                    'name' => '技术研究/开发/测试/运维岗',
                    'list' => [
                        [
                            'id'   => 173,
                            'name' => '技术管理岗',
                        ],
                        [
                            'id'   => 174,
                            'name' => '项目经理/专员岗',
                        ],
                        [
                            'id'   => 175,
                            'name' => '前后端程序开发岗',
                        ],
                        [
                            'id'   => 176,
                            'name' => '人工智能岗',
                        ],
                        [
                            'id'   => 177,
                            'name' => '数据库开发管理岗',
                        ],
                        [
                            'name' => '其他计算机专业人员岗',
                            'id'   => 269,
                        ],
                        [
                            'id'   => 178,
                            'name' => '测试/运维/网络支持岗',
                        ],
                    ],
                ],
                [
                    'name' => '电子/通信/硬件/半导体岗',
                    'list' => [
                        [
                            'id'   => 179,
                            'name' => '电子工程师/专业人员岗',
                        ],
                        [
                            'id'   => 180,
                            'name' => '通信工程师/专业人员岗',
                        ],
                        [
                            'id'   => 181,
                            'name' => '硬件设计与开发测试岗',
                        ],
                        [
                            'id'   => 182,
                            'name' => '仪器仪表计量专业人员岗',
                        ],
                        [
                            'id'   => 183,
                            'name' => '半导体/芯片专业人员岗',
                        ],
                    ],
                ],
            ],
        ],
        [
            'isMultiple' => true,
            'name'       => '细分行业专业领域岗',
            'list'       => [
                [
                    'name' => '生产制造/机械/汽车专业岗',
                    'list' => [
                        [
                            'id'   => 184,
                            'name' => '生产管理岗',
                        ],
                        [
                            'id'   => 185,
                            'name' => '设备管理专业人员岗',
                        ],
                        [
                            'id'   => 186,
                            'name' => '质量管理专业人员岗',
                        ],
                        [
                            'id'   => 187,
                            'name' => '机械设计专业人员岗',
                        ],
                        [
                            'id'   => 188,
                            'name' => '机械制造专业人员岗',
                        ],
                        [
                            'id'   => 189,
                            'name' => '机电工程师/专业人员岗',
                        ],
                        [
                            'id'   => 190,
                            'name' => '自动化/电气工程师专业人员岗',
                        ],
                        [
                            'id'   => 191,
                            'name' => '飞行器设计与制造专业人员岗',
                        ],
                        [
                            'id'   => 192,
                            'name' => '船舶设计与制造专业人员岗',
                        ],
                        [
                            'id'   => 193,
                            'name' => '兵器工程师/专业人员岗',
                        ],
                        [
                            'id'   => 194,
                            'name' => '汽车设计与制造专业人员岗',
                        ],
                        [
                            'id'   => 195,
                            'name' => '新能源汽车专业人员岗',
                        ],
                        [
                            'id'   => 196,
                            'name' => '轨道交通专业人员岗',
                        ],
                        [
                            'id'   => 197,
                            'name' => '技术工人',
                        ],
                        [
                            'id'   => 198,
                            'name' => '其他制造业专业岗',
                        ],
                    ],
                ],
                [
                    'name' => '能源/矿产/电力/环保专业岗',
                    'list' => [
                        [
                            'id'   => 199,
                            'name' => '石油/天然气专业人员岗',
                        ],
                        [
                            'id'   => 200,
                            'name' => '水利水电工程师/专业人员岗',
                        ],
                        [
                            'id'   => 201,
                            'name' => '热能与动力工程师/专业人员岗',
                        ],
                        [
                            'id'   => 202,
                            'name' => '材料工程师/专业人员岗',
                        ],
                        [
                            'id'   => 203,
                            'name' => '冶金工程师/专业人员岗',
                        ],
                        [
                            'id'   => 204,
                            'name' => '光伏及新能源工程师/专业人员岗',
                        ],
                        [
                            'id'   => 205,
                            'name' => '地质勘察/选矿/采矿专业人员岗',
                        ],
                        [
                            'id'   => 206,
                            'name' => '电力工程师/专业人员岗',
                        ],
                        [
                            'id'   => 207,
                            'name' => '环保工程师/专业人员岗',
                        ],
                        [
                            'id'   => 208,
                            'name' => '环保检测与环境评价专业人员岗',
                        ],
                        [
                            'id'   => 209,
                            'name' => '气象/测绘/水文工程师',
                        ],
                        [
                            'id'   => 210,
                            'name' => '安全工程师/专业人员岗',
                        ],
                        [
                            'id'   => 211,
                            'name' => '其他相关岗',
                        ],
                    ],
                ],
                [
                    'name' => '化工/轻工/食品专业岗',
                    'list' => [
                        [
                            'id'   => 212,
                            'name' => '化工技术专业人员岗',
                        ],
                        [
                            'id'   => 213,
                            'name' => '轻化工专业人员岗',
                        ],
                        [
                            'id'   => 214,
                            'name' => '印刷/包装专业人员岗',
                        ],
                        [
                            'id'   => 215,
                            'name' => '纺织/服装专业人员岗',
                        ],
                        [
                            'id'   => 216,
                            'name' => '食品/粮食专业人员岗',
                        ],
                    ],
                ],
                [
                    'name' => '生物/医药/医疗器械专业岗',
                    'list' => [
                        [
                            'id'   => 217,
                            'name' => '生物工程/生物制药专业人员岗',
                        ],
                        [
                            'id'   => 218,
                            'name' => '医药研发管理岗',
                        ],
                        [
                            'id'   => 219,
                            'name' => '药品研发岗',
                        ],
                        [
                            'id'   => 220,
                            'name' => '药品生产/质量管理岗',
                        ],
                        [
                            'id'   => 221,
                            'name' => '临床测试与研究岗',
                        ],
                        [
                            'id'   => 222,
                            'name' => '药品/医疗器械注册岗',
                        ],
                        [
                            'id'   => 223,
                            'name' => '医疗器械研发岗',
                        ],
                        [
                            'id'   => 224,
                            'name' => '医疗器械生产/质量管理岗',
                        ],
                        [
                            'id'   => 225,
                            'name' => '医疗市场事务岗',
                        ],
                    ],
                ],
                [
                    'name' => '金融专业岗',
                    'list' => [
                        [
                            'id'   => 226,
                            'name' => '金融管理岗',
                        ],
                        [
                            'id'   => 227,
                            'name' => '行业研究分析岗',
                        ],
                        [
                            'id'   => 228,
                            'name' => '金融产品岗',
                        ],
                        [
                            'id'   => 229,
                            'name' => '风控岗',
                        ],
                        [
                            'id'   => 230,
                            'name' => '投融资岗',
                        ],
                        [
                            'id'   => 231,
                            'name' => '证券期货岗',
                        ],
                        [
                            'id'   => 232,
                            'name' => '保险岗',
                        ],
                        [
                            'id'   => 233,
                            'name' => '基金岗',
                        ],
                        [
                            'id'   => 234,
                            'name' => '银行岗',
                        ],
                        [
                            'id'   => 235,
                            'name' => '互联网金融岗',
                        ],
                        [
                            'id'   => 236,
                            'name' => '其他金融岗',
                        ],
                    ],
                ],
                [
                    'name' => '房地产/建筑/物业专业岗',
                    'list' => [
                        [
                            'id'   => 237,
                            'name' => '房地产开发岗',
                        ],
                        [
                            'id'   => 238,
                            'name' => '建筑规划设计与市政建设岗',
                        ],
                        [
                            'id'   => 239,
                            'name' => '建筑工程技术人员岗',
                        ],
                        [
                            'id'   => 240,
                            'name' => '工程管理与质量安防岗',
                        ],
                        [
                            'id'   => 241,
                            'name' => '物业运营与管理岗',
                        ],
                        [
                            'id'   => 242,
                            'name' => '其他房地产/建筑/物业岗',
                        ],
                        [
                            'name' => '建筑行业招投标岗',
                            'id'   => 271,
                        ],
                        [
                            'name' => '项目管理',
                            'id'   => 270,
                        ],
                    ],
                ],
                [
                    'name' => '供应链/物流/运输/采购/贸易专业岗',
                    'list' => [
                        [
                            'id'   => 243,
                            'name' => '供应链岗',
                        ],
                        [
                            'id'   => 244,
                            'name' => '物流与仓储岗',
                        ],
                        [
                            'id'   => 245,
                            'name' => '交通运输岗',
                        ],
                        [
                            'id'   => 246,
                            'name' => '采购岗',
                        ],
                        [
                            'name' => '招投标岗',
                            'id'   => 272,
                        ],
                        [
                            'id'   => 247,
                            'name' => '进出口贸易岗',
                        ],
                    ],
                ],
                [
                    'name' => '零售/生活服务/农林牧渔专业岗',
                    'list' => [

                        [
                            'id'   => 248,
                            'name' => '百货零售相关岗',
                        ],
                        [
                            'id'   => 249,
                            'name' => '休闲娱乐体育相关岗',
                        ],
                        [
                            'id'   => 250,
                            'name' => '酒店旅游餐饮相关岗',
                        ],
                        [
                            'id'   => 251,
                            'name' => '家政婚庆美容相关岗',
                        ],
                        [
                            'id'   => 252,
                            'name' => '其他生活服务岗',
                        ],
                        [
                            'id'   => 253,
                            'name' => '农林牧渔管理岗',
                        ],
                        [
                            'id'   => 254,
                            'name' => '农林牧渔技术人员岗',
                        ],
                    ],
                ],
            ],
        ],
    ],

    // 地区栏目页配置
    'homeRegionColumn'        => [
        'hot'  => [
            [
                'name' => '北京',
                'id'   => '11',
            ],
            [
                'name' => '上海',
                'id'   => '12',
            ],
            [
                'name' => '广州',
                'id'   => '46',
            ],
            [
                'name' => '深圳',
                'id'   => '45',
            ],
            [
                'name' => '成都',
                'id'   => '47',
            ],
            [
                'name' => '南京',
                'id'   => '49',
            ],
            [
                'name' => '武汉',
                'id'   => '48',
            ],
            [
                'name' => '重庆',
                'id'   => '14',
            ],
            [
                'name' => '杭州',
                'id'   => '51',
            ],
            [
                'name' => '合肥',
                'id'   => '68',
            ],
            [
                'name' => '西安',
                'id'   => '50',
            ],
            [
                'name' => '长沙',
                'id'   => '85',
            ],
            [
                'name' => '郑州',
                'id'   => '87',
            ],
            [
                'name' => '天津',
                'id'   => '13',
            ],
        ],
        'list' => [
            [
                'name'     => '华东地区',
                'province' => [
                    [
                        'name' => '上海',
                        'id'   => '12',
                        'city' => [
                            [
                                'name' => '上海',
                                'id'   => '12',
                            ],
                        ],
                    ],
                    [
                        'name' => '江苏',
                        'id'   => '21',
                        'city' => [
                            [
                                'name' => '常州',
                                'id'   => '52',
                            ],
                            [
                                'name' => '连云港',
                                'id'   => '53',
                            ],
                            [
                                'name' => '南通',
                                'id'   => '54',
                            ],
                            [
                                'name' => '南京',
                                'id'   => '49',
                            ],
                            [
                                'name' => '苏州',
                                'id'   => '55',
                            ],
                            [
                                'name' => '泰州',
                                'id'   => '56',
                            ],
                            [
                                'name' => '无锡',
                                'id'   => '57',
                            ],
                            [
                                'name' => '徐州',
                                'id'   => '58',
                            ],
                            [
                                'name' => '扬州',
                                'id'   => '59',
                            ],
                            [
                                'name' => '镇江',
                                'id'   => '60',
                            ],
                        ],
                    ],
                    [
                        'name' => '浙江',
                        'id'   => '22',
                        'city' => [

                            [
                                'name' => '湖州',
                                'id'   => '61',
                            ],
                            [
                                'name' => '杭州',
                                'id'   => '51',
                            ],
                            [
                                'name' => '嘉兴',
                                'id'   => '62',
                            ],
                            [
                                'name' => '宁波',
                                'id'   => '63',
                            ],
                            [
                                'name' => '绍兴',
                                'id'   => '64',
                            ],
                            [
                                'name' => '台州',
                                'id'   => '65',
                            ],
                            [
                                'name' => '温州',
                                'id'   => '66',
                            ],
                            [
                                'name' => '舟山',
                                'id'   => '67',
                            ],

                        ],
                    ],
                    [
                        'name' => '安徽',
                        'id'   => '23',
                        'city' => [

                            [
                                'name' => '合肥',
                                'id'   => '68',
                            ],
                            [
                                'name' => '马鞍山',
                                'id'   => '69',
                            ],
                            [
                                'name' => '芜湖',
                                'id'   => '70',
                            ],

                        ],
                    ],
                    [
                        'name' => '福建',
                        'id'   => '24',
                        'city' => [
                            [
                                'name' => '福州',
                                'id'   => '71',
                            ],
                            [
                                'name' => '泉州',
                                'id'   => '72',
                            ],
                            [
                                'name' => '厦门',
                                'id'   => '73',
                            ],
                            [
                                'name' => '漳州',
                                'id'   => '74',
                            ],
                        ],
                    ],
                    [
                        'name' => '江西',
                        'id'   => '25',
                        'city' => [

                            [
                                'name' => '九江',
                                'id'   => '75',
                            ],
                            [
                                'name' => '南昌',
                                'id'   => '76',
                            ],

                        ],
                    ],
                    [
                        'name' => '山东',
                        'id'   => '26',
                        'city' => [

                            [
                                'name' => '济宁',
                                'id'   => '77',
                            ],
                            [
                                'name' => '济南',
                                'id'   => '78',
                            ],
                            [
                                'name' => '临沂',
                                'id'   => '79',
                            ],
                            [
                                'name' => '青岛',
                                'id'   => '80',
                            ],
                            [
                                'name' => '威海',
                                'id'   => '81',
                            ],
                            [
                                'name' => '烟台',
                                'id'   => '82',
                            ],

                        ],
                    ],
                    [
                        'name' => '台湾',
                        'id'   => '42',
                        'city' => [
                            [
                                'name' => '台湾',
                                'id'   => '42',
                            ],
                        ],
                    ],
                ],

            ],
            [
                'name'     => '华中地区',
                'province' => [
                    [
                        'name' => '湖北',
                        'id'   => '28',
                        'city' => [
                            [
                                'name' => '武汉',
                                'id'   => '48',
                            ],
                            [
                                'name' => '襄阳',
                                'id'   => '83',
                            ],
                            [
                                'name' => '宜昌',
                                'id'   => '84',
                            ],
                        ],
                    ],
                    [
                        'name' => '湖南',
                        'id'   => '29',
                        'city' => [
                            [
                                'name' => '长沙',
                                'id'   => '85',
                            ],
                        ],
                    ],
                    [
                        'name' => '河南',
                        'id'   => '27',
                        'city' => [
                            [
                                'name' => '洛阳',
                                'id'   => '86',
                            ],
                            [
                                'name' => '郑州',
                                'id'   => '87',
                            ],
                        ],
                    ],
                ],
            ],
            [
                'name'     => '华南地区',
                'province' => [
                    [
                        'name' => '广西',
                        'id'   => '31',
                        'city' => [

                            [
                                'name' => '北海',
                                'id'   => '88',
                            ],
                            [
                                'name' => '桂林',
                                'id'   => '89',
                            ],
                            [
                                'name' => '柳州',
                                'id'   => '90',
                            ],
                            [
                                'name' => '南宁',
                                'id'   => '91',
                            ],

                        ],
                    ],
                    [
                        'name' => '广东',
                        'id'   => '30',
                        'city' => [
                            [
                                'name' => '东莞',
                                'id'   => '92',
                            ],
                            [
                                'name' => '佛山',
                                'id'   => '93',
                            ],
                            [
                                'name' => '广州',
                                'id'   => '46',
                            ],
                            [
                                'name' => '惠州',
                                'id'   => '94',
                            ],
                            [
                                'name' => '深圳',
                                'id'   => '45',
                            ],
                            [
                                'name' => '汕头',
                                'id'   => '95',
                            ],
                            [
                                'name' => '珠海',
                                'id'   => '96',
                            ],
                            [
                                'name' => '中山',
                                'id'   => '97',
                            ],
                            [
                                'name' => '湛江',
                                'id'   => '98',
                            ],
                        ],
                    ],
                    [
                        'name' => '海南',
                        'id'   => '32',
                        'city' => [

                            [
                                'name' => '海口',
                                'id'   => '99',
                            ],

                        ],
                    ],
                    [
                        'name' => '香港',
                        'id'   => '43',
                        'city' => [

                            [
                                'name' => '香港',
                                'id'   => '43',
                            ],

                        ],
                    ],
                    [
                        'name' => '澳门',
                        'id'   => '44',
                        'city' => [
                            [
                                'name' => '澳门',
                                'id'   => '44',
                            ],
                        ],
                    ],
                ],
            ],
            [
                'name'     => '华北地区',
                'province' => [
                    [
                        'name' => '北京',
                        'id'   => '11',
                        'city' => [

                            [
                                'name' => '北京',
                                'id'   => '11',
                            ],

                        ],
                    ],
                    [
                        'name' => '天津',
                        'id'   => '13',
                        'city' => [

                            [
                                'name' => '天津',
                                'id'   => '13',
                            ],

                        ],
                    ],
                    [
                        'name' => '山西',
                        'id'   => '16',
                        'city' => [

                            [
                                'name' => '太原',
                                'id'   => '100',
                            ],

                        ],
                    ],
                    [
                        'name' => '河北',
                        'id'   => '15',
                        'city' => [

                            [
                                'name' => '石家庄',
                                'id'   => '101',
                            ],
                            [
                                'name' => '唐山',
                                'id'   => '102',
                            ],

                        ],
                    ],
                    [
                        'name' => '内蒙古',
                        'id'   => '17',
                        'city' => [

                            [
                                'name' => '包头',
                                'id'   => '103',
                            ],
                            [
                                'name' => '呼和浩特',
                                'id'   => '104',
                            ],

                        ],
                    ],
                ],
            ],
            [
                'name'     => '西北地区',
                'province' => [
                    [
                        'name' => '青海',
                        'id'   => '39',
                        'city' => [

                            [
                                'name' => '西宁',
                                'id'   => '105',
                            ],

                        ],
                    ],
                    [
                        'name' => '宁夏',
                        'id'   => '40',
                        'city' => [

                            [
                                'name' => '银川',
                                'id'   => '106',
                            ],

                        ],
                    ],
                    [
                        'name' => '陕西',
                        'id'   => '37',
                        'city' => [

                            [
                                'name' => '西安',
                                'id'   => '50',
                            ],

                        ],
                    ],
                    [
                        'name' => '甘肃',
                        'id'   => '38',
                        'city' => [

                            [
                                'name' => '兰州',
                                'id'   => '107',
                            ],

                        ],
                    ],
                    [
                        'name' => '新疆',
                        'id'   => '41',
                        'city' => [

                            [
                                'name' => '乌鲁木齐',
                                'id'   => '108',
                            ],

                        ],
                    ],
                ],
            ],
            [
                'name'     => '东北地区',
                'province' => [
                    [
                        'name' => '吉林',
                        'id'   => '19',
                        'city' => [
                            [
                                'name' => '长春',
                                'id'   => '109',
                            ],
                            [
                                'name' => '吉林市',
                                'id'   => '110',
                            ],
                        ],
                    ],
                    [
                        'name' => '黑龙江',
                        'id'   => '20',
                        'city' => [

                            [
                                'name' => '哈尔滨',
                                'id'   => '111',
                            ],

                        ],
                    ],
                    [
                        'name' => '辽宁',
                        'id'   => '18',
                        'city' => [

                            [
                                'name' => '大连',
                                'id'   => '112',
                            ],
                            [
                                'name' => '沈阳',
                                'id'   => '113',
                            ],

                        ],
                    ],
                ],
            ],
            [
                'name'     => '西南地区',
                'province' => [
                    [
                        'name' => '重庆',
                        'id'   => '14',
                        'city' => [

                            [
                                'name' => '重庆',
                                'id'   => '14',
                            ],

                        ],
                    ],
                    [
                        'name' => '贵州',
                        'id'   => '34',
                        'city' => [

                            [
                                'name' => '贵阳',
                                'id'   => '114',
                            ],

                            [
                                'name' => '遵义',
                                'id'   => '115',
                            ],

                        ],
                    ],
                    [
                        'name' => '云南',
                        'id'   => '35',
                        'city' => [

                            [
                                'name' => '昆明',
                                'id'   => '116',
                            ],

                        ],
                    ],
                    [
                        'name' => '四川',
                        'id'   => '33',
                        'city' => [
                            [
                                'name' => '成都',
                                'id'   => '47',
                            ],
                            [
                                'name' => '绵阳',
                                'id'   => '117',
                            ],

                        ],
                    ],
                    [
                        'name' => '西藏',
                        'id'   => '36',
                        'city' => [

                            [
                                'name' => '拉萨',
                                'id'   => '118',
                            ],

                        ],
                    ],
                ],
            ],
            [
                'name'     => '其他',
                'province' => [
                    [
                        'name' => '海外',
                        'id'   => '8',
                    ],
                ],
            ],
        ],
    ],

    // 首页信息nav配置
    'homeMessageNav'          => [
        // 这里直接使用上面的 homeNav就可以了
        'column'   => [
            9,
            1,
            2,
            3,
            4,
            5,
            6,
        ],
        'city'     => [
            [
                'id'   => 11,
                'name' => '北京',
            ],
            [
                'id'   => 12,
                'name' => '上海',
            ],
            [
                'id'   => 13,
                'name' => '天津',
            ],
            [
                'id'   => 14,
                'name' => '重庆',
            ],
            [
                'id'   => 45,
                'name' => '深圳',
            ],
            [
                'id'   => 46,
                'name' => '广州',
            ],
            [
                'id'   => 47,
                'name' => '成都',
            ],
            [
                'id'   => 48,
                'name' => '武汉',
            ],
            [
                'id'   => 49,
                'name' => '南京',
            ],
            [
                'id'   => 50,
                'name' => '西安',
            ],
            [
                'id'   => 51,
                'name' => '杭州',
            ],
            [
                'id'   => 55,
                'name' => '苏州',
            ],
            [
                'id'   => 68,
                'name' => '合肥',
            ],
            [
                'id'   => 80,
                'name' => '青岛',
            ],
            [
                'id'   => 85,
                'name' => '长沙',
            ],
            [
                'id'   => 87,
                'name' => '郑州',
            ],
        ],
        'province' => [],
    ],

    // 首页学科导航栏
    'homeMajorNav'            => [
        [
            'name'  => '工学',
            'class' => 'engineering',
            'list'  => [
                [
                    'name' => '工学',
                    'list' => [
                        [
                            'name' => '力学',
                            'id'   => 151,
                        ],
                        [
                            'name' => '机械工程',
                            'id'   => 152,
                        ],
                        [
                            'name' => '光学工程',
                            'id'   => 153,
                        ],
                        [
                            'name' => '仪器科学与技术',
                            'id'   => 154,
                        ],
                        [
                            'name' => '材料科学与工程',
                            'id'   => 155,
                        ],
                        [
                            'name' => '纳米科学与工程',
                            'id'   => 535,
                        ],
                        [
                            'name' => '冶金工程',
                            'id'   => 156,
                        ],
                        [
                            'id'   => 546,
                            'name' => '能源动力',
                        ],
                        [
                            'name' => '动力工程及工程热物理',
                            'id'   => 157,
                        ],
                        [
                            'name' => '电气工程',
                            'id'   => 158,
                        ],
                        [
                            'name' => '电子科学与技术',
                            'id'   => 159,
                        ],
                        [
                            'name' => '信息与通信工程',
                            'id'   => 160,
                        ],
                        [
                            'name' => '控制科学与工程',
                            'id'   => 161,
                        ],
                        [
                            'name' => '计算机科学与技术',
                            'id'   => 162,
                        ],
                        [
                            'name' => '智能科学与技术',
                            'id'   => 536,
                        ],
                        [
                            'name' => '人工智能',
                            'id'   => 537,
                        ],
                        [
                            'name' => '建筑学',
                            'id'   => 163,
                        ],
                        [
                            'name' => '土木工程与土木水利',
                            'id'   => 164,
                        ],
                        [
                            'name' => '水利工程',
                            'id'   => 165,
                        ],
                        [
                            'name' => '测绘科学与技术',
                            'id'   => 166,
                        ],
                        [
                            'name' => '遥感科学与技术',
                            'id'   => 538,
                        ],
                        [
                            'name' => '化学工程与技术',
                            'id'   => 167,
                        ],
                        [
                            'name' => '地质资源与地质工程',
                            'id'   => 168,
                        ],
                        [
                            'name' => '矿业工程',
                            'id'   => 169,
                        ],
                        [
                            'name' => '石油与天然气工程',
                            'id'   => 170,
                        ],
                        [
                            'name' => '纺织科学与工程',
                            'id'   => 171,
                        ],
                        [
                            'name' => '轻工技术与工程',
                            'id'   => 172,
                        ],
                        [
                            'name' => '交通运输工程',
                            'id'   => 173,
                        ],
                        [
                            'name' => '船舶与海洋工程',
                            'id'   => 174,
                        ],
                        [
                            'name' => '航空宇航科学与技术',
                            'id'   => 175,
                        ],
                        [
                            'name' => '兵器科学与技术',
                            'id'   => 176,
                        ],
                        [
                            'name' => '核科学与技术',
                            'id'   => 177,
                        ],
                        [
                            'name' => '农业工程',
                            'id'   => 178,
                        ],
                        [
                            'name' => '林业工程',
                            'id'   => 179,
                        ],
                        [
                            'name' => '环境科学与工程',
                            'id'   => 180,
                        ],
                        [
                            'name' => '生物医学工程',
                            'id'   => 181,
                        ],
                        [
                            'name' => '食品科学与工程',
                            'id'   => 182,
                        ],
                        [
                            'name' => '城乡规划学',
                            'id'   => 183,
                        ],
                        [
                            'name' => '风景园林学',
                            'id'   => 184,
                        ],
                        [
                            'name' => '软件工程',
                            'id'   => 185,
                        ],
                        [
                            'name' => '生物工程',
                            'id'   => 186,
                        ],
                        [
                            'name' => '安全科学与工程',
                            'id'   => 187,
                        ],
                        [
                            'name' => '公安技术',
                            'id'   => 188,
                        ],
                        [
                            'name' => '网络空间安全',
                            'id'   => 189,
                        ],
                        // [
                        //     'name' => '电子信息',
                        //     'id'   => 190,
                        // ],
                        [
                            'name' => '工科大类（未明确具体学科）',
                            'id'   => 531,
                        ],
                    ],

                ],
            ],
        ],
        [
            'name'  => '理学',
            'class' => 'science',
            'list'  => [
                [
                    'name' => '理学',
                    'list' => [
                        [
                            'name' => '数学',
                            'id'   => 137,
                        ],
                        [
                            'name' => '物理学',
                            'id'   => 138,
                        ],
                        [
                            'name' => '化学',
                            'id'   => 139,
                        ],
                        [
                            'name' => '天文学',
                            'id'   => 140,
                        ],
                        [
                            'name' => '地理学',
                            'id'   => 141,
                        ],
                        [
                            'name' => '大气科学',
                            'id'   => 142,
                        ],
                        [
                            'name' => '海洋科学',
                            'id'   => 143,
                        ],
                        [
                            'name' => '地球物理学',
                            'id'   => 144,
                        ],
                        [
                            'name' => '地质学',
                            'id'   => 145,
                        ],
                        [
                            'name' => '生物学',
                            'id'   => 146,
                        ],
                        [
                            'name' => '系统科学',
                            'id'   => 147,
                        ],
                        [
                            'name' => '科学技术史',
                            'id'   => 148,
                        ],
                        [
                            'name' => '生态学',
                            'id'   => 149,
                        ],
                        [
                            'name' => '统计学',
                            'id'   => 150,
                        ],
                        [
                            'name' => '理科大类（未明确具体学科）',
                            'id'   => 532,
                        ],
                    ],
                ],
            ],
        ],
        [
            'name'  => '经济学/管理学',
            'class' => 'economics',
            'list'  => [
                [
                    'name' => '经济学',

                    'list' => [
                        [
                            'name' => '理论经济学',
                            'id'   => '120',
                        ],
                        [
                            'name' => '应用经济学',
                            'id'   => '121',
                        ],
                        [
                            'name' => '金融学',
                            'id'   => '545',
                        ],
                        [
                            'name' => '数字经济',
                            'id'   => '533',
                        ],
                    ],
                ],
                [
                    'name' => '管理学',
                    'list' => [
                        [
                            'name' => '管理科学与工程',
                            'id'   => 221,
                        ],
                        [
                            'name' => '工商管理',
                            'id'   => 222,
                        ],
                        [
                            'name' => '审计',
                            'id'   => 547,
                        ],
                        [
                            'name' => '旅游与酒店管理',
                            'id'   => 539,
                        ],
                        [
                            'name' => '农林经济管理',
                            'id'   => 223,
                        ],
                        [
                            'name' => '公共管理学',
                            'id'   => 224,
                        ],
                        [
                            'name' => '信息资源管理（图书馆、情报与档案管理）',
                            'id'   => 225,
                        ],
                    ],
                ],
            ],
        ],
        [
            'name'  => '法学/哲学',
            'class' => 'philosophy',
            'list'  => [
                [
                    'name' => '法学',
                    'list' => [
                        [
                            'name' => '法学与法律',
                            'id'   => '122',
                        ],
                        [
                            'name' => '政治学',
                            'id'   => '123',
                        ],
                        [
                            'name' => '社会学',
                            'id'   => '124',
                        ],
                        [
                            'name' => '民族学',
                            'id'   => '125',
                        ],
                        [
                            'name' => '马克思主义理论',
                            'id'   => '126',
                        ],
                        [
                            'name' => '公安/警务/纪检监察学',
                            'id'   => '127',
                        ],
                    ],
                ],
                [
                    'name' => '哲学',
                    'list' => [
                        [
                            'name' => '哲学',
                            'id'   => '119',
                        ],
                    ],
                ],
            ],
        ],
        [
            'name'  => '历史学/教育学',
            'class' => 'literature',
            'list'  => [
                [
                    'name' => '历史学',

                    'list' => [
                        [
                            'name' => '考古学及博物馆',
                            'id'   => '134',
                        ],
                        [
                            'name' => '中国史',
                            'id'   => '135',
                        ],
                        [
                            'name' => '世界史',
                            'id'   => '136',
                        ],
                    ],
                ],
                [
                    'name' => '教育学',
                    'list' => [
                        [
                            'name' => '教育学',
                            'id'   => '128',
                        ],
                        [
                            'name' => '心理学',
                            'id'   => '129',
                        ],
                        [
                            'name' => '体育学',
                            'id'   => '130',
                        ],
                    ],
                ],
            ],
        ],
        [
            'name'  => '文学/艺术学',
            'class' => 'education',
            'list'  => [
                [
                    'name' => '文学',
                    'list' => [
                        [
                            'name' => '中国语言文学',
                            'id'   => '131',
                        ],
                        [
                            'name' => '外国语言文学',
                            'id'   => '132',
                        ],
                        [
                            'name' => '翻译',
                            'id'   => '534',
                        ],
                        [
                            'name' => '新闻传播学',
                            'id'   => '133',
                        ],
                    ],
                ],
                [
                    'name' => '艺术学',
                    'list' => [
                        [
                            'name' => '艺术学理论',
                            'id'   => '226',
                        ],
                        [
                            'name' => '音乐与舞蹈学',
                            'id'   => '227',
                        ],
                        [
                            'name' => '戏剧、戏曲、影视学',
                            'id'   => '228',
                        ],
                        [
                            'name' => '美术与书法',
                            'id'   => '229',
                        ],
                        [
                            'name' => '设计学',
                            'id'   => '230',
                        ],
                    ],
                ],
            ],
        ],
        [
            'name'  => '医学/交叉学科',
            'class' => 'medical',
            'list'  => [
                [
                    'name' => '医学',

                    'list' => [
                        [
                            'name' => '基础医学',
                            'id'   => '200',
                        ],
                        [
                            'name' => '临床医学',
                            'id'   => '201',
                        ],
                        [
                            'name' => '口腔医学',
                            'id'   => '202',
                        ],
                        [
                            'name' => '公共卫生与预防医学',
                            'id'   => '203',
                        ],
                        [
                            'name' => '中医学',
                            'id'   => '204',
                        ],
                        [
                            'name' => '中西医结合',
                            'id'   => '205',
                        ],
                        [
                            'name' => '药学',
                            'id'   => '206',
                        ],
                        [
                            'name' => '中药学',
                            'id'   => '207',
                        ],
                        [
                            'name' => '特种医学',
                            'id'   => '208',
                        ],
                        [
                            'name' => '医学技术',
                            'id'   => '209',
                        ],
                        [
                            'name' => '护理学',
                            'id'   => '210',
                        ],
                    ],
                ],
                [
                    'name' => '交叉学科',
                    'list' => [
                        [
                            'name' => '集成电路科学与工程',
                            'id'   => '231',
                        ],
                        [
                            'name' => '国家安全学',
                            'id'   => '232',
                        ],
                        [
                            'name' => '区域国别学',
                            'id'   => '540',
                        ],
                    ],
                ],
            ],
        ],
        [
            'name'  => '农学/军事学',
            'class' => 'agronomy',
            'list'  => [
                [
                    'name' => '农学',

                    'list' => [
                        [
                            'name' => '作物学',
                            'id'   => '191',
                        ],
                        [
                            'name' => '园艺学',
                            'id'   => '192',
                        ],
                        [
                            'name' => '农业与农业资源利用',
                            'id'   => '193',
                        ],
                        [
                            'name' => '植物保护',
                            'id'   => '194',
                        ],
                        [
                            'name' => '畜牧学',
                            'id'   => '195',
                        ],
                        [
                            'name' => '兽医学',
                            'id'   => '196',
                        ],
                        [
                            'name' => '林学',
                            'id'   => '197',
                        ],
                        [
                            'name' => '水产',
                            'id'   => '198',
                        ],
                        [
                            'name' => '草学',
                            'id'   => '199',
                        ],
                    ],
                ],
                [
                    'name' => '军事学',
                    'list' => [
                        [
                            'name' => '军事思想及军事历史',
                            'id'   => '211',
                        ],
                        [
                            'name' => '战略、作战学',
                            'id'   => '212',
                        ],
                        [
                            'name' => '战役学',
                            'id'   => '213',
                        ],
                        [
                            'name' => '战术学',
                            'id'   => '214',
                        ],
                        [
                            'name' => '军队指挥、作战指挥与军事智能',
                            'id'   => '215',
                        ],
                        [
                            'name' => '军事管理学',
                            'id'   => '216',
                        ],
                        [
                            'name' => '军队政治工作学与战时政治工作',
                            'id'   => '217',
                        ],
                        [
                            'name' => '军事后勤学（含后勤与装备保障）',
                            'id'   => '218',
                        ],
                        [
                            'name' => '军事装备学',
                            'id'   => '219',
                        ],
                        [
                            'name' => '军事训练学（含军事训练与管理）',
                            'id'   => '220',
                        ],
                    ],
                ],
            ],
        ],
        [
            'name'  => '专业不限/未分类',
            'class' => 'other',
            'list'  => [
                [
                    'name' => '专业不限/未分类',

                    'list' => [
                        [
                            'name' => '专业不限',
                            'id'   => '235',
                        ],
                        [
                            'name' => '专业未分类',
                            'id'   => '236',
                        ],
                    ],
                ],
            ],
        ],

    ],

    /**
     * 首页最新公告 & 简章配置
     * 1、“最新公告&简章”模块
     *
     * 1、调用各一级栏目（招聘）最新发布的、文档属性标识“首页”的公告信息，按时间倒序排列；其中“高校招聘"栏目展示30条信息；”中小学校“栏目展示20条信息；”科研人才“栏目展示20条信息；”政府与事业单位“展示30条信息；”医学人才“单位展示15条信息；”企业招聘“栏目展示20条信息（条数暂不完全确定，还要参看最终的UI页面）
     *
     * 条数不足，按公告发布时间来调用内容；
     *
     * 3、公告标题展示二级栏目名称；点击公告标题，新页面打开公告的详情页面。
     *
     * 4、框架15高度固定，若”置顶“模块信息条数变多，则最新”公告&简章“-“企业招聘”栏目条数相应减少；若“置顶”模块信息条数减少，则最新”公告&简章“-“企业招聘”栏目条数相应增加。
     *
     * 6、相同栏目的公告信息，展示在一起；
     */
    'homeNewest'              => [
        [
            'title' => '高校招聘',
            'id'    => '1',
            'count' => 45,
        ],
        [
            'title' => '中小学校',
            'id'    => '2',
            'count' => 25,
        ],
        [
            'title' => '科技人才',
            'id'    => '3',
            'count' => 20,
        ],
        [
            'title' => '政府与事业单位',
            'id'    => '4',
            'count' => 30,
        ],
        [
            'title' => '医学人才',
            'id'    => '5',
            'count' => 20,
        ],
        [
            'title' => '企业招聘',
            'id'    => '6',
            'count' => 35,
        ],
    ],

    /**
     * ”圈子“模块
     *
     * 1、可切换展示QQ/微信社群联系方式；
     *
     * 2、鼠标移入”加入“按钮上时，展示对应的群二维码；
     * ①群：<a target="_blank" href="https://qm.qq.com/cgi-bin/qm/qr?k=-eYbp--EGzQ4kEErM2l-GPh05RiKf7Ni&jump_from=webapi"><img border="0" src="//pub.idqqimg.com/wpa/images/group.png" alt="高校人才网博硕交流①" title="高校人才网博硕交流①"></a>
     *
     * ②群：<a target="_blank" href="https://qm.qq.com/cgi-bin/qm/qr?k=YoPVhRZF7YXZF2hqjb_LkIi7Ax5z2Fyf&jump_from=webapi"><img border="0" src="//pub.idqqimg.com/wpa/images/group.png" alt="高校人才网博硕交流②" title="高校人才网博硕交流②"></a>
     *
     * ③群：<a target="_blank" href="https://qm.qq.com/cgi-bin/qm/qr?k=mYwg459irUiCqLq9yf8JOL6HFcq0GlRI&jump_from=webapi"><img border="0" src="//pub.idqqimg.com/wpa/images/group.png" alt="高校人才网博硕交流③" title="高校人才网博硕交流③"></a>
     *
     * ④群：<a target="_blank" href="https://qm.qq.com/cgi-bin/qm/qr?k=YZPS4-_w71_zwCLvDRUdvoEFPSMj_o3Y&jump_from=webapi"><img border="0" src="//pub.idqqimg.com/wpa/images/group.png" alt="高校人才网博硕交流④" title="高校人才网博硕交流④"></a>
     *
     * ⑤群：<a target="_blank" href="https://qm.qq.com/cgi-bin/qm/qr?k=3msxr3ZAxFBkmo_2isnugh-c6FaL47sA&jump_from=webapi"><img border="0" src="//pub.idqqimg.com/wpa/images/group.png" alt="高校人才网博硕交流⑤" title="高校人才网博硕交流⑤"></a>
     *
     * ⑥群：<a target="_blank" href="https://qm.qq.com/cgi-bin/qm/qr?k=xVtYEGzKVLimKJ61bLKPQiOOT8Z-XLCL&jump_from=webapi"><img border="0" src="//pub.idqqimg.com/wpa/images/group.png" alt="高校人才网博硕交流⑥" title="高校人才网博硕交流⑥"></a>
     */
    /**
     * ①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯⑰⑱⑲⑳㉑㉒㉓㉔㉕㉖㉗㉘㉙㉚㉛㉜㉝㉞㉟㊱㊲㊳㊴㊵㊶㊷㊸㊹㊺㊻㊼㊽㊾㊿
     */
    'homeGroup'               => [
        'qq' => [
            // [
            //     'name'   => '博硕交流①群',
            //     'url'    => 'https://qm.qq.com/cgi-bin/qm/qr?k=-eYbp--EGzQ4kEErM2l-GPh05RiKf7Ni&jump_from=webapi',
            //     'isFull' => 0,
            // ],
            // [
            //     'name'   => '博硕交流②群',
            //     'url'    => 'https://qm.qq.com/cgi-bin/qm/qr?k=YoPVhRZF7YXZF2hqjb_LkIi7Ax5z2Fyf&jump_from=webapi',
            //     'isFull' => 1,
            //     'class'  => 'exchange-list2',
            // ],
            // [
            //     'name'   => '博硕交流③群',
            //     'url'    => 'https://qm.qq.com/cgi-bin/qm/qr?k=mYwg459irUiCqLq9yf8JOL6HFcq0GlRI&jump_from=webapi',
            //     'isFull' => 1,
            //     'class'  => 'exchange-list3',
            // ],
            // [
            //     'name'   => '博硕交流④群',
            //     'url'    => 'https://qm.qq.com/cgi-bin/qm/qr?k=YZPS4-_w71_zwCLvDRUdvoEFPSMj_o3Y&jump_from=webapi',
            //     'isFull' => 1,
            //     'class'  => 'exchange-list4',
            // ],
            // [
            //     'name'   => '博硕交流⑤群',
            //     'url'    => 'https://qm.qq.com/cgi-bin/qm/qr?k=3msxr3ZAxFBkmo_2isnugh-c6FaL47sA&jump_from=webapi',
            //     'isFull' => 1,
            // ],
            // [
            //     'name'   => '博硕交流⑥群',
            //     'url'    => 'https://qm.qq.com/cgi-bin/qm/qr?k=xVtYEGzKVLimKJ61bLKPQiOOT8Z-XLCL&jump_from=webapi',
            //     'isFull' => 1,
            //     'class'  => 'exchange-list2',
            // ],
            // [
            //     'name'   => '博硕交流⑦群',
            //     'url'    => 'https://qm.qq.com/cgi-bin/qm/qr?k=6uduj8dwTgqVDDCXJacBsukpULTvJT_y&jump_from=webapi',
            //     'isFull' => 1,
            //     'class'  => 'exchange-list3',
            // ],
            // [
            //     'name'   => '博硕交流⑧群',
            //     'url'    => 'https://qm.qq.com/cgi-bin/qm/qr?k=JfN6dBJoFrUJyhD02HX12tWxzPOc2QK0&jump_from=webapi',
            //     'isFull' => 1,
            //     'class'  => 'exchange-list4',
            // ],
            // [
            //     'name'   => '博硕交流⑨群',
            //     'url'    => 'https://jq.qq.com/?_wv=1027&k=MDMT0ncY',
            //     'isFull' => 1,
            // ],
            // [
            //     'name'   => '博硕交流⑩群',
            //     'url'    => 'https://jq.qq.com/?_wv=1027&k=qjREi7am',
            //     'isFull' => 1,
            //     'class'  => 'exchange-list2',
            // ],
            // [
            //     'name'   => '博硕交流⑪群',
            //     'url'    => 'https://jq.qq.com/?_wv=1027&k=PUOUJbAl',
            //     'isFull' => 1,
            //     'class'  => 'exchange-list3',
            // ],
            // [
            //     'name'   => '博硕交流⑫群',
            //     'url'    => 'https://jq.qq.com/?_wv=1027&k=Gj4o18DS',
            //     'isFull' => 1,
            //     'class'  => 'exchange-list4',
            // ],
            // [
            //     'name'   => '博硕交流⑬群',
            //     'url'    => 'https://jq.qq.com/?_wv=1027&k=y6Q7Hclb',
            //     'isFull' => 1,
            //     'class'  => 'exchange-list1',
            // ],
            // [
            //     'name'   => '博硕交流⑭群',
            //     'url'    => 'https://jq.qq.com/?_wv=1027&k=Uwiqj2SL',
            //     'isFull' => 1,
            //     'class'  => 'exchange-list2',
            // ],
            [
                'name'   => '博硕交流⑮群',
                'url'    => 'https://jq.qq.com/?_wv=1027&k=FIWhtMCV',
                'isFull' => 1,
                'class'  => 'exchange-list3',
            ],
            [
                'name'   => '博硕交流⑯群',
                'url'    => 'https://jq.qq.com/?_wv=1027&k=QNAIz8Da',
                'isFull' => 1,
                'class'  => 'exchange-list4',
            ],
            [
                'name'   => '博硕交流⑰群',
                'url'    => 'https://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=RVE-Ll0ATMYxryfRSeUHEdiSfDKl-8ah&authKey=WLYgKuPw2RyKghz5uXV3ixYno2bY45IGQ42Q9OQRLZ3vECj8LDRcwqcEFj%2FkdzgU&noverify=0&group_code=168462001',
                'isFull' => 1,
                'class'  => 'exchange-list1',
            ],
            [
                'name'   => '博硕交流⑱群',
                'url'    => 'https://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=BRdpKiMwUjU6-TxkV-gdg__WE0KHIZY3&authKey=CemkqqVqzv7QWNAOh57ZUxYDh54bnE8vHooRSdUqlY7WoRsiMpxnxeUlgjWjBbK1&noverify=0&group_code=568627905',
                'isFull' => 1,
                'class'  => 'exchange-list2',
            ],
            [
                'name'   => '博硕交流⑲群',
                'url'    => 'https://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=o4q5dp5LFD8oujXM7sfx9Lj-eWZvL1iU&authKey=POHMA%2BX4mKBiKfbz1eZP2wM%2FmwIpQs9nfDvPf7BI31T1dhko4l%2FdTmSgB7R9sSvo&noverify=0&group_code=829927007',
                'isFull' => 0,
                'class'  => 'exchange-list3',
            ],
            [
                'name'   => '博硕交流㉒群',
                'url'    => 'http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=hOvFofARVgXTj_h0WBKIF4ugX53TlaLv&authKey=n4FGO4HSHoE0mN9GzV0olv6z9m2UyM5Zu7ScrBJkc1ddIVjuughYACxNv%2F1O7xnA&noverify=0&group_code=468088413',
                'isFull' => 0,
                'class'  => 'exchange-list4',
            ],
            [
                'name'   => '博硕交流㉓群',
                'url'    => 'http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=HPy9JLtbkl3wvESPQTbLXXljCOUKL1BA&authKey=YayyQ1ejZ%2FnctRECt%2FXPeyFdrdZ9%2F6ndu0RD4Outzid0QgoSuI9eGve4lJqXQ%2Fot&noverify=0&group_code=707404079',
                'isFull' => 0,
                'class'  => 'exchange-list1',
            ],
            [
                'name'   => '博硕交流㉔群',
                'url'    => 'http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=pJ9vRH7GaAEaBF9umzKoCbvc1FNlZW1g&authKey=LCDGCUL2c7kFf4LIYFanMKHlA8UbgB41DnPdFcdr1dZO82C%2BnLKLAs4a2ZtYH1qk&noverify=0&group_code=859097949',
                'isFull' => 0,
                'class'  => 'exchange-list2',
            ],
            /**
             * ①  ②  ③  ④  ⑤  ⑥  ⑦  ⑧  ⑨  ⑩
             *
             * ⑪  ⑫  ⑬  ⑭  ⑮  ⑯  ⑰  ⑱  ⑲  ⑳
             *
             * ㉑ ㉒ ㉓ ㉔ ㉕ ㉖ ㉗ ㉘ ㉙ ㉚
             *
             * ㉛ ㉜ ㉝ ㉞ ㉟ ㊱ ㊲  ㊳ ㊴ ㊵
             *
             * ㊶ ㊷ ㊸ ㊹ ㊺ ㊻ ㊼  ㊽  ㊾ ㊿
             */
            // [
            //     'name'   => '博硕交流⑳群',
            //     'url'    => 'https://jq.qq.com/?_wv=1027&k=5Q5Q5Q5Q',
            //     'isFull' => 0,
            //     'class'  => 'exchange-list4',
            // ],
        ],
        'wx' => [
            [
                'name' => '专任教师事业编群',
                'img'  => 'https://img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/300.png',
            ],
            [
                'name'  => '辅导员事业编群',
                'img'   => 'https://img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/300.png',
                'class' => 'exchange-list2',
            ],
            [
                'name'  => '硕士事业编制群',
                'img'   => 'https://img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/300.png',
                'class' => 'exchange-list3',
            ],
            [
                'name'  => '硕士城市求职群',
                'img'   => 'https://img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/300.png',
                'class' => 'exchange-list4',
            ],
            [
                'name' => '硕士学科求职群',
                'img'  => 'https://img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/300.png',
            ],
            [
                'name'  => '博士事业编制群',
                'img'   => 'https://img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/300.png',
                'class' => 'exchange-list2',
            ],
            [
                'name'  => '博士城市求职群',
                'img'   => 'https://img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/300.png',
                'class' => 'exchange-list3',
            ],
            [
                'name'  => '博士学科求职群',
                'img'   => 'https://img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/300.png',
                'class' => 'exchange-list4',
            ],
        ],
    ],

    /**
     * 首页广告位配置
     */
    'homePosition'            => [
        // 真正售卖的广告位
        'showcase'      => [
            'T01'                  => [
                // 暂时只有数量,往后再扩展
                'count' => 5,
            ],
            'HF'                   => [
                // 2023-10-9 郑鑫从6改为7
                'count' => 7,
            ],
            'A1'                   => [
                'count' => 10,
            ],
            'A2'                   => [
                'count' => 2,
            ],
            'A3'                   => [
                'count' => 28,
            ],
            'A4'                   => [
                'count' => 10,
            ],
            'A5'                   => [
                'count' => 10,
            ],
            'B1'                   => [
                'count' => 2,
            ],
            'B2'                   => [
                'count' => 10,
            ],
            'B3'                   => [
                // 2022-9-6 温新要求从28改为42
                // 2023-9-15 代佳莲从42改成28
                // 2023-9-15 郑鑫从42改成35
                'count' => 35,
            ],
            'B4'                   => [
                'count' => 21,
            ],
            'C1'                   => [
                'count' => 10,
            ],
            'C2'                   => [
                'count' => 29,
            ],
            'C3'                   => [
                'count' => 15,
            ],
            'C4'                   => [
                'count' => 27,
            ],
            'C5'                   => [
                'count' => 32,
            ],
            'D1_1'                 => [
                'count' => 0,
            ],
            'D1_2'                 => [
                'count' => 0,
            ],
            'D1_3'                 => [
                'count' => 0,
            ],
            'D1_4'                 => [
                'count' => 0,
            ],
            'D1_5'                 => [
                'count' => 0,
            ],
            'E1'                   => [
                'count' => 18,
            ],
            // http://zentao.jugaocai.com/index.php?m=story&f=view&id=729
            'E2'                   => [
                'count' => 60,
            ],
            // 'E3'                   => [
            //     'count' => 20,
            // ],
            // 'E4'                   => [
            //     'count' => 20,
            // ],
            'F'                    => [
                'count' => 60,
            ],
            'F1'                   => [
                'count' => 48,
            ],
            'H1'                   => [
                'count' => 1,
            ],
            'ZD01'                 => [
                'count' => 5,
            ],
            'H2_1'                 => [
                'count' => 1,
            ],
            'H2_2'                 => [
                'count' => 1,
            ],
            'H2_3'                 => [
                'count' => 1,
            ],
            'H2_4'                 => [
                'count' => 1,
            ],
            'H2_5'                 => [
                'count' => 1,
            ],
            'L01'                  => [
                'count' => 1,
            ],
            'L02'                  => [
                'count' => 1,
            ],
            'L03'                  => [
                'count' => 1,
            ],
            'L04'                  => [
                'count' => 1,
            ],
            'L05'                  => [
                'count' => 1,
            ],
            'L06'                  => [
                'count' => 1,
            ],
            'L07'                  => [
                'count' => 1,
            ],
            'SHOUYE_ZUIXIN_ZUOCE3' => [
                'count' => 1,
            ],
            'R'                    => [
                'count' => 4,
            ],
        ],
        // 其他广告位(其实只是假装广告位)
        'otherShowcase' => [
            // 热门专题(一个头有7个,然后只有第一个有图片,下面的有某一个有hot?
            'hotTopic'   => [
                // [
                //     'url'   => 'http://t.jugaocai.com/9W6YL3',
                //     'title' => '广州天河博士人才专场招聘会',
                //     'img'   => 'https://img.gaoxiaojob.com/uploads/static/image/20220909/%E7%83%AD%E9%97%A8%E4%B8%93%E9%A2%98%E5%9B%BE.png',
                //     'hot'   => 0,
                // ],
                [
                    'url'   => 'http://www.gaoxiaojob.com/zhaopin/ycb_bzngangweituijian/index.html',
                    'title' => '不同“编制”有什么区别？丨事业编制岗位推荐！',
                    'img'   => 'http://img.gaoxiaojob.com/uploads/static/image/20221205/%E9%A6%96%E9%A1%B5%E7%83%AD%E9%97%A8%E4%B8%93%E9%A2%98.jpg',
                    'hot'   => 0,
                ],
                [
                    'url'   => 'http://t.jugaocai.com/JXFmKG',
                    'title' => '2022海交会之海内外青年科技人才招聘会',
                    'hot'   => 0,
                ],
                [
                    'url'   => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/ycb_boshihou/index.html',
                    'title' => '利弊分析：博士后到底值不值得做',
                    'hot'   => 0,
                ],
                [
                    'url'   => 'http://t.jugaocai.com/UwEwH4',
                    'title' => '「高才优课」上线啦！',
                    'hot'   => 0,
                ],
                [
                    'url'   => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/ycb_fdyanalysis/index.html',
                    'title' => '「高校辅导员」岗位分析&求职攻略',
                    'hot'   => 0,
                ],
                [
                    'url'   => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/jobAnalysis2022/index.html',
                    'title' => '高校教学支撑岗位分析',
                    'hot'   => 0,
                ],
                // [
                //     'url'   => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/graduatehard2022/index.html',
                //     'title' => '研究生毕业为什么越来越难？',
                //     'hot'   => 0,
                // ],
                // [
                //     'url'   => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/jiandangjie/index.html',
                //     'img'   => 'https://img.gaoxiaojob.com/uploads/static/image/20220624/%E7%83%AD%E9%97%A8%E4%B8%93%E9%A2%98%E5%A4%B4%E5%9B%BE.jpg',
                //     'title' => '七一建党节招聘专题',
                //     'hot'   => 0,
                // ],
                // [
                //     'url'   => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/laborDay2022/index.html',
                //     'title' => '找工作防骗指南',
                //     'hot'   => 0,
                // ],
                // [
                //     'url'   => 'http://gaoxiaojob.com/zhaopin/zhuanti/boshijieyenan/index.html',
                //     'img'   => '',
                //     'title' => '中国博士就业的三座大山',
                //     'hot'   => 0,
                // ],
                // [
                //     'url'   => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/goddess-festival/index.html',
                //     'img'   => '',
                //     'title' => '妇女节：致敬了不起的「她」',
                //     'hot'   => 0,
                // ],
                // [
                //     'url'   => 'http://www.gaoxiaojob.com/zhaopin/sydw/zhengfuyincai/index.html',
                //     'img'   => '',
                //     'title' => '政府引才之窗系列专题',
                //     'hot'   => 0,
                // ],
                // [
                //     'url'   => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/annual-report/index.html',
                //     'img'   => '',
                //     'title' => '高校人才网年终回顾',
                //     'hot'   => 0,
                // ],
                // [
                //     'url'   => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/kaoyanzhuanti/index.html',
                //     'img'   => '',
                //     'title' => '读研，你后悔了吗？',
                //     'hot'   => 0,
                // ],
                // [
                //     'url'   => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/civil-servants-topic/index.html',
                //     'img'   => '',
                //     'title' => '2022国家公务员考试专题',
                //     'hot'   => 0,
                // ],
                // [
                //     'url'   => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/post-doctoral/index.html',
                //     'img'   => '',
                //     'title' => '高校课题组简历代投专题',
                //     'hot'   => 0,
                // ],
            ],
            // 活动
            'activity'   => [
                [
                    'title' => '海内外青年科技人才专场招聘会',
                    'time'  => '2023/3/1',
                    'url'   => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/yca_haijiaohui2022/index.html',
                    'isRun' => 1,
                ],
                [
                    'title' => '求职者使用问题反馈及意见收集',
                    'time'  => '2023/1/1',
                    'url'   => 'https://wj.qq.com/s2/11217885/ad63/',
                    'isRun' => 1,
                ],
                [
                    'title' => '人才线上面试会第五场【活动详情】',
                    'time'  => '2022/12/14',
                    'url'   => 'http://www.gaoxiaojob.com/news/detail/1372.html',
                    'isRun' => 0,
                ],
                [
                    'url'   => 'http://www.gaoxiaojob.com/news/detail/1273.html',
                    'time'  => '2022-11-24',
                    'title' => '人才线上面试会第四场【活动详情】',
                    'isRun' => 0,
                ],
                [
                    'url'   => 'http://www.gaoxiaojob.com/news/detail/1180.html',
                    'time'  => '2022-11-3',
                    'title' => '人才线上面试会第三场【活动详情】',
                    'isRun' => 0,
                ],

            ],
            // 高才情报局
            'CIA'        => [
                'top'  => [
                    'url'   => '/',
                    'title' => '这是很长很长的测试标题测试标题',
                    'img'   => '/static/assets/home/<USER>',
                ],
                'list' => [
                    [
                        'url'   => '',
                        'title' => '这是很长很长的测试标题测试标1',
                        'hot'   => 1,
                    ],
                    [
                        'url'   => '',
                        'title' => '这是很长很长的测试标题测试标2',
                        'hot'   => 1,
                    ],
                    [
                        'url'   => '',
                        'title' => '这是很长很长的测试标题测试标3',
                        'hot'   => 0,
                    ],
                    [
                        'url'   => '',
                        'title' => '这是很长很长的测试标题测试标题4',
                        'hot'   => 0,
                    ],
                    [
                        'url'   => '',
                        'title' => '这是很长很长的测试标题测试标题5',
                        'hot'   => 0,
                    ],
                ],
            ],

            // 这里其实也是广告位,只是没有按照模板的样式
            'hotCompany' => [
                'area' => [
                    [
                        'number' => 'shouye_remendanwei_diqu_1',
                        'name'   => '广东',
                    ],
                    [
                        'number' => 'shouye_remendanwei_diqu_2',
                        'name'   => '江苏',
                    ],
                    [
                        'number' => 'shouye_remendanwei_diqu_3',
                        'name'   => '上海',
                    ],
                    [
                        'number' => 'shouye_remendanwei_diqu_4',
                        'name'   => '山东',
                    ],
                    [
                        'number' => 'shouye_remendanwei_diqu_5',
                        'name'   => '湖北',
                    ],
                    [
                        'number' => 'shouye_remendanwei_diqu_6',
                        'name'   => '北京',
                    ],
                    [
                        'number' => 'shouye_remendanwei_diqu_7',
                        'name'   => '四川',
                    ],
                    [
                        'number' => 'shouye_remendanwei_diqu_8',
                        'name'   => '浙江',
                    ],

                ],
                'type' => [
                    [
                        'number' => 'shouye_remendanwei_danweileixing_1',
                        'name'   => '双一流高校',
                    ],
                    [
                        'number' => 'shouye_remendanwei_danweileixing_2',
                        'name'   => '本科院校',
                    ],
                    [
                        'number' => 'shouye_remendanwei_danweileixing_3',
                        'name'   => '高职高专院校 ',
                    ],
                    [
                        'number' => 'shouye_remendanwei_danweileixing_4',
                        'name'   => '中科院研究机构',
                    ],
                    [
                        'number' => 'shouye_remendanwei_danweileixing_5',
                        'name'   => '事业单位',
                    ],
                    [
                        'number' => 'shouye_remendanwei_danweileixing_6',
                        'name'   => '知名企业',
                    ],
                    [
                        'number' => 'shouye_remendanwei_danweileixing_7',
                        'name'   => '中小学校',
                    ],
                    // [
                    //     'number' => 'shouye_remendanwei_danweileixing_8',
                    //     'name'   => '人文社科研究机构',
                    // ],
                ],
            ],
            'search'     => [
                'sousuoye_youce_01' => [
                    'count' => 1,
                ],
                'sousuoye_youce_02' => [
                    'count' => 1,
                ],
            ],
        ],
    ],

    // 公告和简章
    'announcementAndChapters' => [
        'hotColumn'      => [
            [
                'name' => '高端人才',
                'id'   => '9',
            ],
            [
                'name' => '高校招聘',
                'id'   => '1',
            ],
            [
                'name' => '科技人才',
                'id'   => '3',
            ],
            [
                'name' => '政府与事业单位',
                'id'   => '4',
            ],
            [
                'name' => '中小学校',
                'id'   => '2',
            ],
            [
                'name' => '医学人才',
                'id'   => '5',
            ],
            [
                'name' => '企业招聘',
                'id'   => '6',
            ],
            [
                //                'name' => '博士后',
                //                'id'   => '7',
                'name'   => '高才博士后',
                'action' => 'getBoShiHouHome',
            ],
            [
                'name'   => '高才海外',
                'action' => 'getHaiwaiHome',
                //                'id'   => '541',
            ],
        ],
        'provinceColumn' => [],
        'cityColumn'     => [],
        'majorColumn'    => [
            [
                'name' => '计算机科学与技术',
                'id'   => 162,
            ],
            [
                'name' => '生物学',
                'id'   => 146,
            ],
            [
                'name' => '管理科学与工程',
                'id'   => 221,
            ],
            [
                'name' => '临床医学',
                'id'   => 201,
            ],
            // [
            //     'name' => '电子信息',
            //     'id'   => 190,
            // ],
            [
                'name' => '基础医学',
                'id'   => 200,
            ],
            [
                'name' => '应用经济学',
                'id'   => 121,
            ],
            [
                'name' => '马克思主义理论',
                'id'   => 126,
            ],
            [
                'name' => '化学',
                'id'   => 139,
            ],
            [
                'name' => '材料科学与工程',
                'id'   => 155,
            ],
            [
                'name' => '机械工程',
                'id'   => 152,
            ],
            [
                'name' => '信息与通信工程',
                'id'   => 160,
            ],
            [
                'name' => '公共卫生与预防医学',
                'id'   => 203,
            ],
            [
                'name' => '中医学',
                'id'   => 204,
            ],
            [
                'name' => '教育学',
                'id'   => 128,
            ],
            [
                'name' => '数学',
                'id'   => 137,
            ],
            [
                'name' => '中国语言文学',
                'id'   => 131,
            ],
            [
                'name' => '药学',
                'id'   => 206,
            ],
            [
                'name' => '物理学',
                'id'   => 138,
            ],
            [
                'name' => '外国语言文学',
                'id'   => 132,
            ],
        ],
    ],

    'sitemap' => [
        [
            'name' => '高校招聘',
            'id'   => 1,
            'list' => [
                [
                    'id'   => 238,
                    'name' => '学科带头人、教授招聘',
                ],
                [
                    'id'   => 239,
                    'name' => '中高层干部',
                ],
                [
                    'id'   => 240,
                    'name' => '教学科研人才',
                ],
                [
                    'id'   => 241,
                    'name' => '教辅、行政、实验、助理人员招聘',
                ],
                [
                    'id'   => 242,
                    'name' => '高校辅导员',
                ],
            ],
        ],
        [
            'name' => '科技人才',
            'id'   => 3,
            'list' => [
                [
                    'id'   => 247,
                    'name' => '中国科学院系统研究机构',
                ],
                [
                    'id'   => 248,
                    'name' => '人文社科研究机构',
                ],
                [
                    'id'   => 249,
                    'name' => '自然与应用科研机构',
                ],
                [
                    'id'   => 250,
                    'name' => '企业研发机构',
                ],
            ],
        ],
        [
            'name' => '政府与事业单位',
            'id'   => 4,
            'list' => [
                [
                    'id'   => 251,
                    'name' => '公务员招考',
                ],
                //                [
                //                    'id'   => 252,
                //                    'name' => '遴选与选调',
                //                ],
                [
                    'id'   => 253,
                    'name' => '机关与事业单位统一招考',
                ],
                [
                    'id'   => 254,
                    'name' => '机关与事业单位自主招聘',
                ],
                [
                    'id'   => 255,
                    'name' => '军队武警招聘',
                ],
            ],
        ],
        [
            'name' => '中小学校',
            'id'   => 2,
            'list' => [
                [
                    'id'   => 243,
                    'name' => '教育系统招考',
                ],
                [
                    'id'   => 244,
                    'name' => '中小学自主招聘',
                ],
                [
                    'id'   => 245,
                    'name' => '职业中学招聘',
                ],
                [
                    'id'   => 246,
                    'name' => '幼儿园教师招聘',
                ],
            ],

        ],
        [
            'name' => '医学人才',
            'id'   => 5,
            'list' => [
                [
                    'id'   => 256,
                    'name' => '卫生系统统一招聘',
                ],
                [
                    'id'   => 257,
                    'name' => '医疗单位自主招聘',
                ],
                [
                    'id'   => 258,
                    'name' => '医卫院校（院系）招聘',
                ],
            ],

        ],
        [
            'name' => '企业招聘',
            'id'   => 6,
            'list' => [
                [
                    'id'   => 259,
                    'name' => '知名企业',
                ],
                [
                    'id'   => 260,
                    'name' => '国有（政府）企业',
                ],
                [
                    'id'   => 261,
                    'name' => '金融机构',
                ],
                [
                    'id'   => 262,
                    'name' => '中小创新型企业',
                ],
            ],
        ],
        [
            'name'   => '高才博士后',
            'action' => 'getBoShiHouHome',
            'list'   => [
                [
                    'action' => 'getBoShiHouGongGao',
                    'name'   => '博后公告&职位',
                ],
                [
                    'action' => 'getBoShiHouDanWei',
                    'name'   => '招收单位&PI大厅',
                ],
                [
                    'action' => 'getBoShiHouHuoDong',
                    'name'   => '博后活动',
                ],
                [
                    'action' => 'getBoShiHouFuWu',
                    'name'   => '博后需求发布',
                ],
            ],
        ],
        // [
        //     'name' => '高才海外',
        //     'id'   => 8,
        //     'list' => [
        //         [
        //             'id'   => 267,
        //             'name' => '华东海归人才',
        //         ],
        //         [
        //             'id'   => 268,
        //             'name' => '华中、华南海归人才',
        //         ],
        //         [
        //             'id'   => 269,
        //             'name' => '华北、东北海归人才',
        //         ],
        //         [
        //             'id'   => 270,
        //             'name' => '西南、西北海归人才',
        //         ],
        //         [
        //             'id'   => 271,
        //             'name' => '海外地区人才招聘',
        //         ],
        //     ],
        // ],
        [
            'name'     => '高才海外',
            'variable' => 'haiHaiHome',
            'list'     => [
                [
                    'name'     => '出海引才',
                    'variable' => 'chuHaiUrl',
                ],
                [
                    'name'     => '归国活动',
                    'variable' => 'guiGuoUrl',
                ],
                [
                    'name'     => '求贤公告',
                    'variable' => 'qiuXianUrl',
                ],
                [
                    'name'     => '海外优青' . $haiYouYear,
                    'variable' => 'haiYouUrl',
                ],
                [
                    'name'     => '单位大厅',
                    'variable' => 'danWeiUrl',
                ],
            ],
        ],
        [
            'name' => '高端人才',
            'id'   => 9,
            'list' => [
                [
                    'id'   => 272,
                    'name' => '双一流院校人才招聘',
                ],
                [
                    'id'   => 273,
                    'name' => '高职高专人才招聘',
                ],
                [
                    'id'   => 274,
                    'name' => '党校（行政学院）招聘',
                ],
                [
                    'id'   => 275,
                    'name' => '博士人才招聘',
                ],
                [
                    'id'   => 276,
                    'name' => '政府引才活动',
                ],
                [
                    'id'   => 277,
                    'name' => '本科院校人才招聘',
                ],
                [
                    'id'   => 530,
                    'name' => '招聘会专场',
                ],
            ],
        ],
        [
            'name' => '资讯',
            'id'   => 10,
            'list' => [
                [
                    'id'   => 278,
                    'name' => '求职攻略',
                ],
                [
                    'id'   => 279,
                    'name' => '职场分享',
                ],
                [
                    'id'   => 280,
                    'name' => '就业形势',
                ],
                [
                    'id'   => 281,
                    'name' => '观点热议',
                ],
                [
                    'id'   => 282,
                    'name' => '学术科研',
                ],
                [
                    'id'   => 283,
                    'name' => '新闻动态',
                ],
            ],
        ],
        [
            'name' => '省区导航',
            'url'  => 'region.html',
            'list' => [
                [
                    'id'   => 11,
                    'name' => '北京',
                ],
                [
                    'id'   => 284,
                    'name' => '北京高校',
                ],
                [
                    'id'   => 285,
                    'name' => '北京机关事业',
                ],
                [
                    'id'   => 286,
                    'name' => '北京中小学',
                ],
                [
                    'id'   => 287,
                    'name' => '北京科研',
                ],
                [
                    'id'   => 288,
                    'name' => '北京医学',
                ],
                [
                    'id'   => 289,
                    'name' => '北京企业',
                ],
                [
                    'id'   => 12,
                    'name' => '上海',
                ],
                [
                    'id'   => 290,
                    'name' => '上海高校',
                ],
                [
                    'id'   => 291,
                    'name' => '上海机关事业',
                ],
                [
                    'id'   => 292,
                    'name' => '上海中小学',
                ],
                [
                    'id'   => 293,
                    'name' => '上海科研',
                ],
                [
                    'id'   => 294,
                    'name' => '上海医学',
                ],
                [
                    'id'   => 295,
                    'name' => '上海企业',
                ],
                [
                    'id'   => 13,
                    'name' => '天津',
                ],
                [
                    'id'   => 296,
                    'name' => '天津高校',
                ],
                [
                    'id'   => 297,
                    'name' => '天津机关事业',
                ],
                [
                    'id'   => 298,
                    'name' => '天津中小学',
                ],
                [
                    'id'   => 299,
                    'name' => '天津科研',
                ],
                [
                    'id'   => 300,
                    'name' => '天津医学',
                ],
                [
                    'id'   => 301,
                    'name' => '天津企业',
                ],
                [
                    'id'   => 14,
                    'name' => '重庆',
                ],
                [
                    'id'   => 302,
                    'name' => '重庆高校',
                ],
                [
                    'id'   => 303,
                    'name' => '重庆机关事业',
                ],
                [
                    'id'   => 304,
                    'name' => '重庆中小学',
                ],
                [
                    'id'   => 305,
                    'name' => '重庆科研',
                ],
                [
                    'id'   => 306,
                    'name' => '重庆医学',
                ],
                [
                    'id'   => 307,
                    'name' => '重庆企业',
                ],
                [
                    'id'   => 15,
                    'name' => '河北',
                ],
                [
                    'id'   => 308,
                    'name' => '河北高校',
                ],
                [
                    'id'   => 309,
                    'name' => '河北机关事业',
                ],
                [
                    'id'   => 310,
                    'name' => '河北中小学',
                ],
                [
                    'id'   => 311,
                    'name' => '河北科研',
                ],
                [
                    'id'   => 312,
                    'name' => '河北医学',
                ],
                [
                    'id'   => 313,
                    'name' => '河北企业',
                ],
                [
                    'id'   => 16,
                    'name' => '山西',
                ],
                [
                    'id'   => 314,
                    'name' => '山西高校',
                ],
                [
                    'id'   => 315,
                    'name' => '山西机关事业',
                ],
                [
                    'id'   => 316,
                    'name' => '山西中小学',
                ],
                [
                    'id'   => 317,
                    'name' => '山西科研',
                ],
                [
                    'id'   => 318,
                    'name' => '山西医学',
                ],
                [
                    'id'   => 319,
                    'name' => '山西企业',
                ],
                [
                    'id'   => 17,
                    'name' => '内蒙古',
                ],
                [
                    'id'   => 320,
                    'name' => '内蒙古高校',
                ],
                [
                    'id'   => 321,
                    'name' => '内蒙古机关事业',
                ],
                [
                    'id'   => 322,
                    'name' => '内蒙古中小学',
                ],
                [
                    'id'   => 323,
                    'name' => '内蒙古科研',
                ],
                [
                    'id'   => 324,
                    'name' => '内蒙古医学',
                ],
                [
                    'id'   => 325,
                    'name' => '内蒙古企业',
                ],
                [
                    'id'   => 18,
                    'name' => '辽宁',
                ],
                [
                    'id'   => 326,
                    'name' => '辽宁高校',
                ],
                [
                    'id'   => 327,
                    'name' => '辽宁机关事业',
                ],
                [
                    'id'   => 328,
                    'name' => '辽宁中小学',
                ],
                [
                    'id'   => 329,
                    'name' => '辽宁科研',
                ],
                [
                    'id'   => 330,
                    'name' => '辽宁医学',
                ],
                [
                    'id'   => 331,
                    'name' => '辽宁企业',
                ],
                [
                    'id'   => 19,
                    'name' => '吉林',
                ],
                [
                    'id'   => 332,
                    'name' => '吉林高校',
                ],
                [
                    'id'   => 333,
                    'name' => '吉林机关事业',
                ],
                [
                    'id'   => 334,
                    'name' => '吉林中小学',
                ],
                [
                    'id'   => 335,
                    'name' => '吉林科研',
                ],
                [
                    'id'   => 336,
                    'name' => '吉林医学',
                ],
                [
                    'id'   => 337,
                    'name' => '吉林企业',
                ],
                [
                    'id'   => 20,
                    'name' => '黑龙江',
                ],
                [
                    'id'   => 338,
                    'name' => '黑龙江高校',
                ],
                [
                    'id'   => 339,
                    'name' => '黑龙江机关事业',
                ],
                [
                    'id'   => 340,
                    'name' => '黑龙江中小学',
                ],
                [
                    'id'   => 341,
                    'name' => '黑龙江科研',
                ],
                [
                    'id'   => 342,
                    'name' => '黑龙江医学',
                ],
                [
                    'id'   => 343,
                    'name' => '黑龙江企业',
                ],
                [
                    'id'   => 21,
                    'name' => '江苏',
                ],
                [
                    'id'   => 344,
                    'name' => '江苏高校',
                ],
                [
                    'id'   => 345,
                    'name' => '江苏机关事业',
                ],
                [
                    'id'   => 346,
                    'name' => '江苏中小学',
                ],
                [
                    'id'   => 347,
                    'name' => '江苏科研',
                ],
                [
                    'id'   => 348,
                    'name' => '江苏医学',
                ],
                [
                    'id'   => 349,
                    'name' => '江苏企业',
                ],
                [
                    'id'   => 22,
                    'name' => '浙江',
                ],
                [
                    'id'   => 350,
                    'name' => '浙江高校',
                ],
                [
                    'id'   => 351,
                    'name' => '浙江机关事业',
                ],
                [
                    'id'   => 352,
                    'name' => '浙江中小学',
                ],
                [
                    'id'   => 353,
                    'name' => '浙江科研',
                ],
                [
                    'id'   => 354,
                    'name' => '浙江医学',
                ],
                [
                    'id'   => 355,
                    'name' => '浙江企业',
                ],
                [
                    'id'   => 23,
                    'name' => '安徽',
                ],
                [
                    'id'   => 356,
                    'name' => '安徽高校',
                ],
                [
                    'id'   => 357,
                    'name' => '安徽机关事业',
                ],
                [
                    'id'   => 358,
                    'name' => '安徽中小学',
                ],
                [
                    'id'   => 359,
                    'name' => '安徽科研',
                ],
                [
                    'id'   => 360,
                    'name' => '安徽医学',
                ],
                [
                    'id'   => 361,
                    'name' => '安徽企业',
                ],
                [
                    'id'   => 24,
                    'name' => '福建',
                ],
                [
                    'id'   => 362,
                    'name' => '福建高校',
                ],
                [
                    'id'   => 363,
                    'name' => '福建机关事业',
                ],
                [
                    'id'   => 364,
                    'name' => '福建中小学',
                ],
                [
                    'id'   => 365,
                    'name' => '福建科研',
                ],
                [
                    'id'   => 366,
                    'name' => '福建医学',
                ],
                [
                    'id'   => 367,
                    'name' => '福建企业',
                ],
                [
                    'id'   => 25,
                    'name' => '江西',
                ],
                [
                    'id'   => 368,
                    'name' => '江西高校',
                ],
                [
                    'id'   => 369,
                    'name' => '江西机关事业',
                ],
                [
                    'id'   => 370,
                    'name' => '江西中小学',
                ],
                [
                    'id'   => 371,
                    'name' => '江西科研',
                ],
                [
                    'id'   => 372,
                    'name' => '江西医学',
                ],
                [
                    'id'   => 373,
                    'name' => '江西企业',
                ],
                [
                    'id'   => 26,
                    'name' => '山东',
                ],
                [
                    'id'   => 374,
                    'name' => '山东高校',
                ],
                [
                    'id'   => 375,
                    'name' => '山东机关事业',
                ],
                [
                    'id'   => 376,
                    'name' => '山东中小学',
                ],
                [
                    'id'   => 377,
                    'name' => '山东科研',
                ],
                [
                    'id'   => 378,
                    'name' => '山东医学',
                ],
                [
                    'id'   => 379,
                    'name' => '山东企业',
                ],
                [
                    'id'   => 27,
                    'name' => '河南',
                ],
                [
                    'id'   => 380,
                    'name' => '河南高校',
                ],
                [
                    'id'   => 381,
                    'name' => '河南机关事业',
                ],
                [
                    'id'   => 382,
                    'name' => '河南中小学',
                ],
                [
                    'id'   => 383,
                    'name' => '河南科研',
                ],
                [
                    'id'   => 384,
                    'name' => '河南医学',
                ],
                [
                    'id'   => 385,
                    'name' => '河南企业',
                ],
                [
                    'id'   => 28,
                    'name' => '湖北',
                ],
                [
                    'id'   => 386,
                    'name' => '湖北高校',
                ],
                [
                    'id'   => 387,
                    'name' => '湖北机关事业',
                ],
                [
                    'id'   => 388,
                    'name' => '湖北中小学',
                ],
                [
                    'id'   => 389,
                    'name' => '湖北科研',
                ],
                [
                    'id'   => 390,
                    'name' => '湖北医学',
                ],
                [
                    'id'   => 391,
                    'name' => '湖北企业',
                ],
                [
                    'id'   => 29,
                    'name' => '湖南',
                ],
                [
                    'id'   => 392,
                    'name' => '湖南高校',
                ],
                [
                    'id'   => 393,
                    'name' => '湖南机关事业',
                ],
                [
                    'id'   => 394,
                    'name' => '湖南中小学',
                ],
                [
                    'id'   => 395,
                    'name' => '湖南科研',
                ],
                [
                    'id'   => 396,
                    'name' => '湖南医学',
                ],
                [
                    'id'   => 397,
                    'name' => '湖南企业',
                ],
                [
                    'id'   => 30,
                    'name' => '广东',
                ],
                [
                    'id'   => 398,
                    'name' => '广东高校',
                ],
                [
                    'id'   => 399,
                    'name' => '广东机关事业',
                ],
                [
                    'id'   => 400,
                    'name' => '广东中小学',
                ],
                [
                    'id'   => 401,
                    'name' => '广东科研',
                ],
                [
                    'id'   => 402,
                    'name' => '广东医学',
                ],
                [
                    'id'   => 403,
                    'name' => '广东企业',
                ],
                [
                    'id'   => 31,
                    'name' => '广西',
                ],
                [
                    'id'   => 404,
                    'name' => '广西高校',
                ],
                [
                    'id'   => 405,
                    'name' => '广西机关事业',
                ],
                [
                    'id'   => 406,
                    'name' => '广西中小学',
                ],
                [
                    'id'   => 407,
                    'name' => '广西科研',
                ],
                [
                    'id'   => 408,
                    'name' => '广西医学',
                ],
                [
                    'id'   => 409,
                    'name' => '广西企业',
                ],
                [
                    'id'   => 32,
                    'name' => '海南',
                ],
                [
                    'id'   => 410,
                    'name' => '海南高校',
                ],
                [
                    'id'   => 411,
                    'name' => '海南机关事业',
                ],
                [
                    'id'   => 412,
                    'name' => '海南中小学',
                ],
                [
                    'id'   => 413,
                    'name' => '海南科研',
                ],
                [
                    'id'   => 414,
                    'name' => '海南医学',
                ],
                [
                    'id'   => 415,
                    'name' => '海南企业',
                ],
                [
                    'id'   => 33,
                    'name' => '四川',
                ],
                [
                    'id'   => 416,
                    'name' => '四川高校',
                ],
                [
                    'id'   => 417,
                    'name' => '四川机关事业',
                ],
                [
                    'id'   => 418,
                    'name' => '四川中小学',
                ],
                [
                    'id'   => 419,
                    'name' => '四川科研',
                ],
                [
                    'id'   => 420,
                    'name' => '四川医学',
                ],
                [
                    'id'   => 421,
                    'name' => '四川企业',
                ],
                [
                    'id'   => 34,
                    'name' => '贵州',
                ],
                [
                    'id'   => 422,
                    'name' => '贵州高校',
                ],
                [
                    'id'   => 423,
                    'name' => '贵州机关事业',
                ],
                [
                    'id'   => 424,
                    'name' => '贵州中小学',
                ],
                [
                    'id'   => 425,
                    'name' => '贵州科研',
                ],
                [
                    'id'   => 426,
                    'name' => '贵州医学',
                ],
                [
                    'id'   => 427,
                    'name' => '贵州企业',
                ],
                [
                    'id'   => 35,
                    'name' => '云南',
                ],
                [
                    'id'   => 428,
                    'name' => '云南高校',
                ],
                [
                    'id'   => 429,
                    'name' => '云南机关事业',
                ],
                [
                    'id'   => 430,
                    'name' => '云南中小学',
                ],
                [
                    'id'   => 431,
                    'name' => '云南科研',
                ],
                [
                    'id'   => 432,
                    'name' => '云南医学',
                ],
                [
                    'id'   => 433,
                    'name' => '云南企业',
                ],
                [
                    'id'   => 36,
                    'name' => '西藏',
                ],
                [
                    'id'   => 434,
                    'name' => '西藏高校',
                ],
            ],
        ],
        [
            'name' => '城市导航',
            'url'  => 'region.html',
            'list' => [
                [
                    'id'   => 45,
                    'name' => '深圳',
                ],
                [
                    'id'   => 488,
                    'name' => '深圳高校',
                ],
                [
                    'id'   => 489,
                    'name' => '深圳机关事业',
                ],
                [
                    'id'   => 490,
                    'name' => '深圳中小学',
                ],
                [
                    'id'   => 491,
                    'name' => '深圳科研',
                ],
                [
                    'id'   => 492,
                    'name' => '深圳医学',
                ],
                [
                    'id'   => 493,
                    'name' => '深圳企业',
                ],
                [
                    'id'   => 46,
                    'name' => '广州',
                ],
                [
                    'id'   => 494,
                    'name' => '广州高校',
                ],
                [
                    'id'   => 495,
                    'name' => '广州机关事业',
                ],
                [
                    'id'   => 496,
                    'name' => '广州中小学',
                ],
                [
                    'id'   => 497,
                    'name' => '广州科研',
                ],
                [
                    'id'   => 498,
                    'name' => '广州医学',
                ],
                [
                    'id'   => 499,
                    'name' => '广州企业',
                ],
                [
                    'id'   => 47,
                    'name' => '成都',
                ],
                [
                    'id'   => 500,
                    'name' => '成都高校',
                ],
                [
                    'id'   => 501,
                    'name' => '成都机关事业',
                ],
                [
                    'id'   => 502,
                    'name' => '成都中小学',
                ],
                [
                    'id'   => 503,
                    'name' => '成都科研',
                ],
                [
                    'id'   => 504,
                    'name' => '成都医学',
                ],
                [
                    'id'   => 505,
                    'name' => '成都企业',
                ],
                [
                    'id'   => 48,
                    'name' => '武汉',
                ],
                [
                    'id'   => 506,
                    'name' => '武汉高校',
                ],
                [
                    'id'   => 507,
                    'name' => '武汉机关事业',
                ],
                [
                    'id'   => 508,
                    'name' => '武汉中小学',
                ],
                [
                    'id'   => 509,
                    'name' => '武汉科研',
                ],
                [
                    'id'   => 510,
                    'name' => '武汉医学',
                ],
                [
                    'id'   => 511,
                    'name' => '武汉企业',
                ],
                [
                    'id'   => 49,
                    'name' => '南京',
                ],
                [
                    'id'   => 512,
                    'name' => '南京高校',
                ],
                [
                    'id'   => 513,
                    'name' => '南京机关事业',
                ],
                [
                    'id'   => 514,
                    'name' => '南京中小学',
                ],
                [
                    'id'   => 515,
                    'name' => '南京科研',
                ],
                [
                    'id'   => 516,
                    'name' => '南京医学',
                ],
                [
                    'id'   => 517,
                    'name' => '南京企业',
                ],
                [
                    'id'   => 50,
                    'name' => '西安',
                ],
                [
                    'id'   => 518,
                    'name' => '西安高校',
                ],
                [
                    'id'   => 519,
                    'name' => '西安机关事业',
                ],
                [
                    'id'   => 520,
                    'name' => '西安中小学',
                ],
                [
                    'id'   => 521,
                    'name' => '西安科研',
                ],
                [
                    'id'   => 522,
                    'name' => '西安医学',
                ],
                [
                    'id'   => 523,
                    'name' => '西安企业',
                ],
                [
                    'id'   => 51,
                    'name' => '杭州',
                ],
                [
                    'id'   => 524,
                    'name' => '杭州高校',
                ],
                [
                    'id'   => 525,
                    'name' => '杭州机关事业',
                ],
                [
                    'id'   => 526,
                    'name' => '杭州中小学',
                ],
                [
                    'id'   => 527,
                    'name' => '杭州科研',
                ],
                [
                    'id'   => 528,
                    'name' => '杭州医学',
                ],
                [
                    'id'   => 529,
                    'name' => '杭州企业',
                ],
                [
                    'id'   => 52,
                    'name' => '常州',
                ],
                [
                    'id'   => 53,
                    'name' => '连云港',
                ],
                [
                    'id'   => 54,
                    'name' => '南通',
                ],
                [
                    'id'   => 55,
                    'name' => '苏州',
                ],
                [
                    'id'   => 56,
                    'name' => '泰州',
                ],
                [
                    'id'   => 57,
                    'name' => '无锡',
                ],
                [
                    'id'   => 58,
                    'name' => '徐州',
                ],
                [
                    'id'   => 59,
                    'name' => '扬州',
                ],
                [
                    'id'   => 60,
                    'name' => '镇江',
                ],
                [
                    'id'   => 61,
                    'name' => '湖州',
                ],
                [
                    'id'   => 62,
                    'name' => '嘉兴',
                ],
                [
                    'id'   => 63,
                    'name' => '宁波',
                ],
                [
                    'id'   => 64,
                    'name' => '绍兴',
                ],
                [
                    'id'   => 65,
                    'name' => '台州',
                ],
                [
                    'id'   => 66,
                    'name' => '温州',
                ],
                [
                    'id'   => 67,
                    'name' => '舟山',
                ],
                [
                    'id'   => 68,
                    'name' => '合肥',
                ],
                [
                    'id'   => 69,
                    'name' => '马鞍山',
                ],
                [
                    'id'   => 70,
                    'name' => '芜湖',
                ],
                [
                    'id'   => 71,
                    'name' => '福州',
                ],
                [
                    'id'   => 72,
                    'name' => '泉州',
                ],
                [
                    'id'   => 73,
                    'name' => '厦门',
                ],
                [
                    'id'   => 74,
                    'name' => '漳州',
                ],
                [
                    'id'   => 75,
                    'name' => '九江',
                ],
                [
                    'id'   => 76,
                    'name' => '南昌',
                ],
                [
                    'id'   => 77,
                    'name' => '济宁',
                ],
                [
                    'id'   => 78,
                    'name' => '济南',
                ],
                [
                    'id'   => 79,
                    'name' => '临沂',
                ],
                [
                    'id'   => 80,
                    'name' => '青岛',
                ],
                [
                    'id'   => 81,
                    'name' => '威海',
                ],
                [
                    'id'   => 82,
                    'name' => '烟台',
                ],
                [
                    'id'   => 83,
                    'name' => '襄阳',
                ],
                [
                    'id'   => 84,
                    'name' => '宜昌',
                ],
                [
                    'id'   => 85,
                    'name' => '长沙',
                ],
                [
                    'id'   => 86,
                    'name' => '洛阳',
                ],
                [
                    'id'   => 87,
                    'name' => '郑州',
                ],
                [
                    'id'   => 88,
                    'name' => '北海',
                ],
                [
                    'id'   => 89,
                    'name' => '桂林',
                ],
                [
                    'id'   => 90,
                    'name' => '柳州',
                ],
                [
                    'id'   => 91,
                    'name' => '南宁',
                ],
                [
                    'id'   => 92,
                    'name' => '东莞',
                ],
                [
                    'id'   => 93,
                    'name' => '佛山',
                ],
                [
                    'id'   => 94,
                    'name' => '惠州',
                ],
                [
                    'id'   => 95,
                    'name' => '汕头',
                ],
                [
                    'id'   => 96,
                    'name' => '珠海',
                ],
                [
                    'id'   => 97,
                    'name' => '中山',
                ],
                [
                    'id'   => 98,
                    'name' => '湛江',
                ],
                [
                    'id'   => 99,
                    'name' => '海口',
                ],
                [
                    'id'   => 100,
                    'name' => '太原',
                ],
                [
                    'id'   => 101,
                    'name' => '石家庄',
                ],
                [
                    'id'   => 102,
                    'name' => '唐山',
                ],
                [
                    'id'   => 103,
                    'name' => '包头',
                ],
                [
                    'id'   => 104,
                    'name' => '呼和浩特',
                ],
                [
                    'id'   => 105,
                    'name' => '西宁',
                ],
                [
                    'id'   => 106,
                    'name' => '银川',
                ],
                [
                    'id'   => 107,
                    'name' => '兰州',
                ],
                [
                    'id'   => 108,
                    'name' => '乌鲁木齐',
                ],
                [
                    'id'   => 109,
                    'name' => '长春',
                ],
                [
                    'id'   => 110,
                    'name' => '吉林',
                ],
                [
                    'id'   => 111,
                    'name' => '哈尔滨',
                ],
                [
                    'id'   => 112,
                    'name' => '大连',
                ],
                [
                    'id'   => 113,
                    'name' => '沈阳',
                ],
                [
                    'id'   => 114,
                    'name' => '贵阳',
                ],
                [
                    'id'   => 115,
                    'name' => '遵义',
                ],
                [
                    'id'   => 116,
                    'name' => '昆明',
                ],
                [
                    'id'   => 117,
                    'name' => '绵阳',
                ],
                [
                    'id'   => 118,
                    'name' => '拉萨',
                ],
            ],
        ],
        [
            'name' => '需求学科',
            'url'  => 'major.html',
            'list' => [
                [
                    'id'   => 119,
                    'name' => '哲学',
                ],
                [
                    'id'   => 120,
                    'name' => '理论经济学',
                ],
                [
                    'id'   => 121,
                    'name' => '应用经济学',
                ],
                [
                    'id'   => 122,
                    'name' => '法学',
                ],
                [
                    'id'   => 123,
                    'name' => '政治学',
                ],
                [
                    'id'   => 124,
                    'name' => '社会学',
                ],
                [
                    'id'   => 125,
                    'name' => '民族学',
                ],
                [
                    'id'   => 126,
                    'name' => '马克思主义理论',
                ],
                [
                    'id'   => 127,
                    'name' => '公安学',
                ],
                [
                    'id'   => 128,
                    'name' => '教育学',
                ],
                [
                    'id'   => 129,
                    'name' => '心理学',
                ],
                [
                    'id'   => 130,
                    'name' => '体育学',
                ],
                [
                    'id'   => 131,
                    'name' => '中国语言文学',
                ],
                [
                    'id'   => 132,
                    'name' => '外国语言文学',
                ],
                [
                    'id'   => 133,
                    'name' => '新闻传播学',
                ],
                [
                    'id'   => 134,
                    'name' => '考古学',
                ],
                [
                    'id'   => 135,
                    'name' => '中国史',
                ],
                [
                    'id'   => 136,
                    'name' => '世界史',
                ],
                [
                    'id'   => 137,
                    'name' => '数学',
                ],
                [
                    'id'   => 138,
                    'name' => '物理学',
                ],
                [
                    'id'   => 139,
                    'name' => '化学',
                ],
                [
                    'id'   => 140,
                    'name' => '天文学',
                ],
                [
                    'id'   => 141,
                    'name' => '地理学',
                ],
                [
                    'id'   => 142,
                    'name' => '大气科学',
                ],
                [
                    'id'   => 143,
                    'name' => '海洋科学',
                ],
                [
                    'id'   => 144,
                    'name' => '地球物理学',
                ],
                [
                    'id'   => 145,
                    'name' => '地质学',
                ],
                [
                    'id'   => 146,
                    'name' => '生物学',
                ],
                [
                    'id'   => 147,
                    'name' => '系统科学',
                ],
                [
                    'id'   => 148,
                    'name' => '科学技术史',
                ],
                [
                    'id'   => 149,
                    'name' => '生态学',
                ],
                [
                    'id'   => 150,
                    'name' => '统计学',
                ],
                [
                    'id'   => 151,
                    'name' => '力学',
                ],
                [
                    'id'   => 152,
                    'name' => '机械工程',
                ],
                [
                    'id'   => 153,
                    'name' => '光学工程',
                ],
                [
                    'id'   => 154,
                    'name' => '仪器科学与技术',
                ],
                [
                    'id'   => 155,
                    'name' => '材料科学与工程',
                ],
                [
                    'id'   => 156,
                    'name' => '冶金工程',
                ],
                [
                    'id'   => 546,
                    'name' => '能源动力',
                ],
                [
                    'id'   => 157,
                    'name' => '动力工程及工程热物理',
                ],
                [
                    'id'   => 158,
                    'name' => '电气工程',
                ],
                [
                    'id'   => 159,
                    'name' => '电子科学与技术',
                ],
                [
                    'id'   => 160,
                    'name' => '信息与通信工程',
                ],
                [
                    'id'   => 161,
                    'name' => '控制科学与工程',
                ],
                [
                    'id'   => 162,
                    'name' => '计算机科学与技术',
                ],
                [
                    'id'   => 163,
                    'name' => '建筑学',
                ],
                [
                    'id'   => 164,
                    'name' => '土木工程',
                ],
                [
                    'id'   => 165,
                    'name' => '水利工程',
                ],
                [
                    'id'   => 166,
                    'name' => '测绘科学与技术',
                ],
                [
                    'id'   => 167,
                    'name' => '化学工程与技术',
                ],
                [
                    'id'   => 168,
                    'name' => '地质资源与地质工程',
                ],
                [
                    'id'   => 169,
                    'name' => '矿业工程',
                ],
                [
                    'id'   => 170,
                    'name' => '石油与天然气工程',
                ],
                [
                    'id'   => 171,
                    'name' => '纺织科学与工程',
                ],
                [
                    'id'   => 172,
                    'name' => '轻工技术与工程',
                ],
                [
                    'id'   => 173,
                    'name' => '交通运输工程',
                ],
                [
                    'id'   => 174,
                    'name' => '船舶与海洋工程',
                ],
                [
                    'id'   => 175,
                    'name' => '航空宇航科学与技术',
                ],
                [
                    'id'   => 176,
                    'name' => '兵器科学与技术',
                ],
                [
                    'id'   => 177,
                    'name' => '核科学与技术',
                ],
                [
                    'id'   => 178,
                    'name' => '农业工程',
                ],
                [
                    'id'   => 179,
                    'name' => '林业工程',
                ],
                [
                    'id'   => 180,
                    'name' => '环境科学与工程',
                ],
                [
                    'id'   => 181,
                    'name' => '生物医学工程',
                ],
                [
                    'id'   => 182,
                    'name' => '食品科学与工程',
                ],
                [
                    'id'   => 183,
                    'name' => '城乡规划学',
                ],
                [
                    'id'   => 184,
                    'name' => '风景园林学',
                ],
                [
                    'id'   => 185,
                    'name' => '软件工程',
                ],
                [
                    'id'   => 186,
                    'name' => '生物工程',
                ],
                [
                    'id'   => 187,
                    'name' => '安全科学与工程',
                ],
                [
                    'id'   => 188,
                    'name' => '公安技术',
                ],
                [
                    'id'   => 189,
                    'name' => '网络空间安全',
                ],
                // [
                //     'id'   => 190,
                //     'name' => '电子信息',
                // ],
                [
                    'id'   => 191,
                    'name' => '作物学',
                ],
                [
                    'id'   => 192,
                    'name' => '园艺学',
                ],
                [
                    'id'   => 193,
                    'name' => '农业资源利用',
                ],
                [
                    'id'   => 194,
                    'name' => '植物保护',
                ],
                [
                    'id'   => 195,
                    'name' => '畜牧学',
                ],
                [
                    'id'   => 196,
                    'name' => '兽医学',
                ],
                [
                    'id'   => 197,
                    'name' => '林学',
                ],
                [
                    'id'   => 198,
                    'name' => '水产',
                ],
                [
                    'id'   => 199,
                    'name' => '草学',
                ],
                [
                    'id'   => 200,
                    'name' => '基础医学',
                ],
                [
                    'id'   => 201,
                    'name' => '临床医学',
                ],
                [
                    'id'   => 202,
                    'name' => '口腔医学',
                ],
                [
                    'id'   => 203,
                    'name' => '公共卫生与预防医学',
                ],
                [
                    'id'   => 204,
                    'name' => '中医学',
                ],
                [
                    'id'   => 205,
                    'name' => '中西医结合',
                ],
                [
                    'id'   => 206,
                    'name' => '药学',
                ],
                [
                    'id'   => 207,
                    'name' => '中药学',
                ],
                [
                    'id'   => 208,
                    'name' => '特种医学',
                ],
                [
                    'id'   => 209,
                    'name' => '医学技术',
                ],
                [
                    'id'   => 210,
                    'name' => '护理学',
                ],
                [
                    'id'   => 211,
                    'name' => '军事思想及军事历史',
                ],
                [
                    'id'   => 212,
                    'name' => '战略、作战学',
                ],
                [
                    'id'   => 213,
                    'name' => '战役学',
                ],
                [
                    'id'   => 214,
                    'name' => '战术学',
                ],
                [
                    'id'   => 215,
                    'name' => '军队指挥、作战指挥与军事智能',
                ],
                [
                    'id'   => 216,
                    'name' => '军事管理学',
                ],
                [
                    'id'   => 217,
                    'name' => '军队政治工作学与战时政治工作',
                ],
                [
                    'id'   => 218,
                    'name' => '军事后勤学（含后勤与装备保障）',
                ],
                [
                    'id'   => 219,
                    'name' => '军事装备学',
                ],
                [
                    'id'   => 220,
                    'name' => '军事训练学（含军事训练与管理）',
                ],
                [
                    'id'   => 221,
                    'name' => '管理科学与工程',
                ],
                [
                    'id'   => 222,
                    'name' => '工商管理',
                ],
                [
                    'id'   => 223,
                    'name' => '农林经济管理',
                ],
                [
                    'id'   => 224,
                    'name' => '公共管理',
                ],
                [
                    'id'   => 225,
                    'name' => '图书馆、情报与档案管理',
                ],
                [
                    'id'   => 226,
                    'name' => '艺术学理论',
                ],
                [
                    'id'   => 227,
                    'name' => '音乐与舞蹈学',
                ],
                [
                    'id'   => 228,
                    'name' => '戏剧与影视学',
                ],
                [
                    'id'   => 229,
                    'name' => '美术学',
                ],
                [
                    'id'   => 230,
                    'name' => '设计学',
                ],
                [
                    'id'   => 231,
                    'name' => '集成电路科学与工程',
                ],
                [
                    'id'   => 232,
                    'name' => '国家安全学',
                ],
                [
                    'id'   => 233,
                    'name' => '人文社科',
                ],
                [
                    'id'   => 234,
                    'name' => '理工农医',
                ],
                [
                    'id'   => 235,
                    'name' => '专业不限',
                ],
                [
                    'id'   => 236,
                    'name' => '其他',
                ],
            ],
        ],
        [
            'name' => '每日汇总',
            'url'  => 'daily.html',
        ],
        [
            'name' => '用人单位中心',
            'url'  => 'company',
        ],
        [
            'name' => '职位信息中心',
            'url'  => 'job',
        ],
        [
            'name' => '地区导航中心',
            'url'  => 'region.html',
        ],
    ],
];
