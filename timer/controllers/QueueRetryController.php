<?php

namespace timer\controllers;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArticleColumn;
use common\base\models\BaseEmailLog;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseMember;
use common\base\models\BaseSmsLog;
use common\helpers\ArrayHelper;
use common\helpers\TimeHelper;
use common\libs\Cache;
use common\libs\EmailQueue;
use common\libs\SmsQueue;
use common\libs\WxWork;
use queue\EmailJob;
use queue\Producer;
use Yii;

/**
 *
 */
class QueueRetryController extends BaseTimerController
{

    // php ./timer_yii queue-retry/last-day
    public function actionLastDay()
    {
        // 超过一小时都没发出去
        $yesterday = TimeHelper::getYesterday();
        $beginTime = date('Y-m-d H:i:s', strtotime($yesterday));
        // 超过一小时都没发出去
        $endTime = date('Y-m-d H:i:s', strtotime($yesterday . ' +1 day'));

        // 这里处理2的问题，5天内的
        $beginTimeSending = date('Y-m-d H:i:s', CUR_TIMESTAMP - 86400 * 5);
        $endTimeSending   = $endTime;
        $listWait         = BaseEmailLog::find()
            ->select([
                'id',
            ])
            ->where([
                'status' => [
                    BaseEmailLog::STATUS_SENDING,
                ],
                'type'   => [
                    EmailQueue::EMAIL_PLATFORM_DELIVERY,
                    EmailQueue::EMAIL_POST_DELIVERY,
                    EmailQueue::EMAIL_NOT_WILL_DELIVERY,
                    EmailQueue::EMAIL_RESUME_INVITE_JOB_APPLY,
                ],
            ])
            ->andWhere([
                'between',
                'add_time',
                $beginTimeSending,
                $endTimeSending,
            ])
            ->orderBy('id desc')
            ->asArray()
            ->limit(1000)
            ->all();

        $list = BaseEmailLog::find()
            ->select([
                'id',
            ])
            ->where([
                'status' => [
                    BaseEmailLog::STATUS_WAIT,
                    BaseEmailLog::STATUS_FAIL,
                ],
                'type'   => [
                    EmailQueue::EMAIL_PLATFORM_DELIVERY,
                    EmailQueue::EMAIL_POST_DELIVERY,
                    EmailQueue::EMAIL_NOT_WILL_DELIVERY,
                    EmailQueue::EMAIL_RESUME_INVITE_JOB_APPLY,
                ],
            ])
            ->andWhere([
                'between',
                'add_time',
                $beginTime,
                $endTime,
            ])
            ->orderBy('id desc')
            ->asArray()
            ->limit(1000)
            ->all();

        $list = ArrayHelper::merge($list, $listWait);

        foreach ($list as $item) {
            // 一个一个入队列
            $id               = $item['id'];
            $emailLog         = BaseEmailLog::findOne($id);
            $emailLog->status = BaseEmailLog::STATUS_WAIT;
            $emailLog->save();
            self::log($id . ' 重新入队');
            Yii::$app->emailQueue->push(new EmailJob([
                'id'       => $id,
                'userType' => BaseMember::TYPE_PERSON,
            ]));
        }
    }

    // 每小时运行一次,找到过去2个小时到半个小时内没法出去的
    public function actionLastHour()
    {
        // 超过一小时都没发出去
        $beginTime = CUR_TIMESTAMP - 7200;
        $endTime   = CUR_TIMESTAMP - 1800;

        // 转时间格式
        $beginTime = date('Y-m-d H:i:s', $beginTime);
        $endTime   = date('Y-m-d H:i:s', $endTime);

        $list = BaseEmailLog::find()
            ->select([
                'id',
            ])
            ->where([
                'status' => [
                    BaseEmailLog::STATUS_WAIT,
                    BaseEmailLog::STATUS_FAIL,
                ],
                'type'   => [
                    EmailQueue::EMAIL_PLATFORM_DELIVERY,
                    EmailQueue::EMAIL_POST_DELIVERY,
                    EmailQueue::EMAIL_NOT_WILL_DELIVERY,
                    EmailQueue::EMAIL_RESUME_INVITE_JOB_APPLY,
                ],
            ])
            ->andWhere([
                'between',
                'add_time',
                $beginTime,
                $endTime,
            ])
            ->orderBy('id desc')
            ->asArray()
            ->limit(200)
            ->all();

        if (count($list) > 10) {
            // 通知管理员
            $app = WxWork::getInstance();
            $app->messageToSystem('一共重试了' . count($list) . '封邮件');
        }

        foreach ($list as $item) {
            // 一个一个入队列
            $id               = $item['id'];
            $emailLog         = BaseEmailLog::findOne($id);
            $emailLog->status = BaseEmailLog::STATUS_WAIT;
            $emailLog->save();
            self::log($id . ' 重新入队');
            Yii::$app->emailQueue->push(new EmailJob([
                'id'       => $id,
                'userType' => BaseMember::TYPE_PERSON,
            ]));
        }
    }

    // php ./timer_yii queue-retry/job-apply-success
    public function actionJobApplySuccess()
    {
        $beginTime = date('Y-m-d H:i:s', strtotime(CUR_DATETIME . " -10 day"));
        // 超过一小时都没发出去
        $end = CUR_TIMESTAMP - 3600;

        $list = BaseEmailLog::find()
            ->select([
                'id',
            ])
            ->where([
                'status' => BaseEmailLog::STATUS_WAIT,
                'type'   => [
                    EmailQueue::EMAIL_PLATFORM_DELIVERY,
                    EmailQueue::EMAIL_POST_DELIVERY,
                    EmailQueue::EMAIL_NOT_WILL_DELIVERY,
                ],
            ])
            ->orderBy('id desc')
            ->asArray()
            ->limit(100)
            ->all();

        foreach ($list as $item) {
            // 一个一个入队列
            $id               = $item['id'];
            $emailLog         = BaseEmailLog::findOne($id);
            $emailLog->status = BaseEmailLog::STATUS_WAIT;
            $emailLog->save();
            self::log($id . ' 重新入队');
            Yii::$app->emailQueue->push(new EmailJob([
                'id'       => $id,
                'userType' => BaseMember::TYPE_PERSON,
            ]));
        }
    }

    /**
     * 邮件重试,其实就是重新塞到队列中
     */
    public function actionEmail()
    {
        $timer = date('Y-m-d H:i:s', strtotime(CUR_DATETIME . " -3 day"));
        // 找到有问题的邮件
        $list = BaseEmailLog::find()
            ->select([
                'id',
            ])
            ->where([
                'status' => BaseEmailLog::STATUS_FAIL,
                'type'   => [
                    EmailQueue::EMAIL_TYPE_OFF_SITE_JOB_APPLY,
                    EmailQueue::EMAIL_TYPE_JOB_APPLY,
                    EmailQueue::EMAIL_PLATFORM_DELIVERY,
                    EmailQueue::EMAIL_POST_DELIVERY,
                    EmailQueue::EMAIL_NOT_WILL_DELIVERY,
                    EmailQueue::EMAIL_SEEKER_DELIVERY,
                ],
            ])
            ->andWhere([
                '>',
                'add_time',
                $timer,
            ])
            ->orderBy('id desc')
            ->asArray()
            ->limit(100)
            ->all();
        foreach ($list as $item) {
            // 一个一个入队列
            $id               = $item['id'];
            $emailLog         = BaseEmailLog::findOne($id);
            $emailLog->status = BaseEmailLog::STATUS_WAIT;
            $emailLog->save();
            self::log($id . ' 重新入队');
            Yii::$app->emailQueue->push(new EmailJob([
                'id'       => $id,
                'userType' => BaseMember::TYPE_PERSON,
            ]));
        }
    }

    // 晚上3点把昨天0点到24点没发出去的邮件重新入队（专门状态是9然后是邀约的）
    // php ./timer_yii queue-retry/invite
    public static function actionInvite()
    {
        // 昨天0点
        $yesterday = TimeHelper::getYesterday();
        $beginTime = TimeHelper::dayToBeginTime($yesterday);
        $endTime   = TimeHelper::dayToEndTime($yesterday);

        // 找到有问题的邮件
        $list = BaseEmailLog::find()
            ->select([
                'id',
            ])
            ->where([
                'status' => BaseEmailLog::STATUS_WAIT,
                'type'   => [
                    EmailQueue::EMAIL_RESUME_INVITE_JOB_APPLY,
                ],
            ])
            ->andWhere([
                'between',
                'add_time',
                $beginTime,
                $endTime,
            ])
            ->orderBy('id desc')
            ->asArray()
            ->limit(3000)
            ->all();
        foreach ($list as $item) {
            // 一个一个入队列
            $id = $item['id'];
            self::log($id . ' 重新入队');
            Yii::$app->emailQueue->push(new EmailJob([
                'id'       => $id,
                'userType' => BaseMember::TYPE_PERSON,
            ]));
        }
    }

    // 晚上3点把昨天0点到24点没发出去的邮件重新入队（专门状态是9然后是邀约的）
    // php ./timer_yii queue-retry/invite-today
    public static function actionInviteToday()
    {
        // 昨天0点
        $today     = CUR_DATE;
        $beginTime = TimeHelper::dayToBeginTime($today);
        $endTime   = TimeHelper::dayToEndTime($today);

        // 找到有问题的邮件
        $list = BaseEmailLog::find()
            ->select([
                'id',
            ])
            ->where([
                'status' => BaseEmailLog::STATUS_WAIT,
                'type'   => [
                    EmailQueue::EMAIL_RESUME_INVITE_JOB_APPLY,
                ],
            ])
            ->andWhere([
                'between',
                'add_time',
                $beginTime,
                $endTime,
            ])
            ->orderBy('id desc')
            ->asArray()
            ->limit(3000)
            ->all();
        foreach ($list as $item) {
            // 一个一个入队列
            $id = $item['id'];
            self::log($id . ' 重新入队');
            Yii::$app->emailQueue->push(new EmailJob([
                'id'       => $id,
                'userType' => BaseMember::TYPE_PERSON,
            ]));
        }

        // 短信的
        $list = BaseSmsLog::find()
            ->select([
                'id',
            ])
            ->where([
                'status' => BaseEmailLog::STATUS_WAIT,
                'type'   => [
                    SmsQueue::TYPE_COMPANY_JOB_INVITE,
                ],
            ])
            ->andWhere([
                'between',
                'add_time',
                $beginTime,
                $endTime,
            ])
            ->orderBy('id desc')
            ->asArray()
            ->limit(100)
            ->all();

        foreach ($list as $item) {
            // 一个一个入队列
            $id = $item['id'];
            self::log($id . ' 短信重新入队');
            Yii::$app->smsQueue->push(new \queue\SmsJob([
                'id'       => $id,
                'userType' => BaseMember::TYPE_PERSON,
            ]));
        }
    }

    // php ./timer_yii queue-retry/email-one 1,2,3
    public function actionEmailOne($ids)
    {
        // 找到有问题的邮件
        $idArr = explode(',', $ids);
        $list  = BaseEmailLog::find()
            ->select([
                'id',
            ])
            ->where([
                'id' => $idArr,
            ])
            ->andWhere([
                '<>',
                'status',
                BaseEmailLog::STATUS_SUCCESS,
            ])
            ->orderBy('id desc')
            ->asArray()
            ->limit(300)
            ->all();

        self::log('一共找到' . count($list) . '条');

        foreach ($list as $item) {
            // 一个一个入队列
            $id               = $item['id'];
            $emailLog         = BaseEmailLog::findOne($id);
            $emailLog->status = BaseEmailLog::STATUS_WAIT;
            $emailLog->save();
            self::log($id . ' 重新入队');
            Yii::$app->emailQueue->push(new EmailJob([
                'id'       => $id,
                'userType' => BaseMember::TYPE_PERSON,
            ]));
        }
    }

    // php ./timer_yii queue-retry/meili-announcement-one
    public function actionMeiliAnnouncementOne($ids)
    {
        // 找到有问题的邮件
        $idArr = explode(',', $ids);

        $service = new \common\service\meilisearch\announcement\SimpleAddService();
        foreach ($idArr as $item) {
            $id = $item;
            try {
                $service->saveById($id);
                self::log('公告ID：' . $id . '，添加成功');
            } catch (\Exception $e) {
                self::log('公告ID：' . $id . '，添加失败' . $e->getMessage());
            }
            Cache::set($key, $id);
        }
    }

    // php ./timer_yii queue-retry/column-announcement 6
    // 重新匹配某个栏目下的全部公告
    public function actionColumnAnnouncement($id)
    {
        // 找到栏目信息
        $detail = BaseHomeColumn::findOne($id);

        if (!$detail) {
            self::log('栏目ID：' . $id . '，不存在');

            return;
        }

        $name = $detail->name;

        self::log('栏目ID：' . $id . '，栏目名称：' . $name);

        // 找到所有的公告
        $list = BaseArticleColumn::find()
            ->alias('b')
            ->innerJoin(['a' => BaseAnnouncement::tableName()], 'a.article_id=b.article_id')
            ->select([
                'a.id',
            ])
            ->where([
                'column_id' => $id,
            ])
            ->andWhere([
                'status' => BaseArticleColumn::STATUS_ACTIVE,
            ])
            ->orderBy('id desc')
            ->asArray()
            ->all();
        //
        // $sql = BaseArticleColumn::find()
        //     ->alias('b')
        //     ->innerJoin(['a' => BaseAnnouncement::tableName()], 'a.article_id=b.article_id')
        //     ->select([
        //         'a.id',
        //     ])
        //     ->where([
        //         'column_id' => $id,
        //     ])
        //     ->andWhere([
        //         'status' => BaseArticleColumn::STATUS_ACTIVE,
        //     ])
        //     ->createCommand()
        //     ->getRawSql();

        // bb($sql);

        self::log('栏目ID：' . $id . '，栏目名称：' . $name . '，共找到' . count($list) . '条');
        // bb($list);

        // 批量入库
        foreach ($list as $item) {
            $id = $item['id'];

            Producer::afterAnnouncementUpdateJob($id);

            self::log($id . ' 重新入队');
        }
    }
}
