<?php

namespace timer\controllers;

use admin\models\Admin;
use admin\models\Company;
use admin\models\CompanyContact;
use admin\models\Dictionary;
use admin\models\Job;
use admin\models\JobApply;
use admin\models\Member;
use admin\models\MemberActionLog;
use admin\models\Trade;
use common\base\models\BaseAdmin;
use common\base\models\BaseAdminDownloadTask;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementCollect;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyCollect;
use common\base\models\BaseFile;
use common\base\models\BaseJobCollect;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseResumeEducation;
use common\helpers\TimeHelper;
use common\helpers\UUIDHelper;
use common\libs\Cache;
use common\libs\Excel;
use common\libs\Qiniu;
use common\libs\WxWork;
use common\service\search\CommonSearchApplication;
use common\service\search\PcJobListService;
use common\service\stat\OperationDepartmentService;
use common\service\stat\StateApplication;
use queue\Producer;
use Yii;

/**
 *
 */
class StatController extends BaseTimerController
{
    // php timer_yii stat/all-company-job-apply
    public function actionAllCompanyJobApply()
    {
        try {
            $service = new StateApplication();
            $service->allCompanyJobApply();
        } catch (\Exception $e) {
            self::log($e->getMessage());
        }
    }

    public function actionDoubleMeetingResumeList($adminId = '', $isNow = false)
    {
        // 拿双会的数据
        $app = StateApplication::getInstance();

        try {
            $app->doubleMeetingResumeList($adminId, $isNow);
        } catch (\Exception $e) {
            self::log($e->getMessage());
        }
    }

    public static function actionCompanyList($adminId)
    {
        $select = [
            'c.id',
            'c.member_id',
            'c.full_name',
            'c.admin_id',
            'c.province_id',
            'c.city_id',
            'c.district_id',
            'c.nature',
            'c.type',
            'c.industry_id',
            'c.source_type',
            'c.admin_id',
            'c.create_admin_id',
            'c.is_cooperation',
            'c.status as statusCompany',
            'c.address',
            'c.package_type',
            'c.click',
            'm.status as statusMember',
            'm.username',
            'm.mobile',
            'm.add_time',
            'm.last_login_time',
            'cc.name as contact',
            'cc.mobile as cMobile',
        ];

        $query = BaseCompany::find()
            ->alias('c')
            ->leftJoin(['cc' => CompanyContact::tableName()], 'cc.company_id = c.id')
            ->innerJoin(['m' => Member::tableName()], 'm.id = c.member_id')
            ->leftJoin(['a' => Admin::tableName()], 'a.id = c.admin_id')
            ->where(['c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES]);

        // 审核已通过单位
        $query->andFilterWhere([
            'c.status' => BaseCompany::STATUS_ACTIVE,
            'm.status' => [
                Member::STATUS_ACTIVE,
                Member::STATUS_ILLEGAL,
            ],
        ]);

        //所在地区

        //创建人帐号
        if (!empty($params['createUserName'])) {
            $createmMemberId = Member::findOneVal(['username' => $params['createUserName']], 'id');
            $createAdminId   = Admin::findOneVal(['username' => $params['createUserName']], 'id');

            if ($createmMemberId) {
                //自主创建
                $query->andFilterWhere([
                    'c.create_admin_id' => $createmMemberId,
                ]);
            } else {
                //运营创建
                $query->andFilterWhere([
                    'c.create_admin_id' => $createAdminId,
                ]);
            }
        }

        $orderBy = 'm.add_time desc';

        $list = $query->select($select)
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        //获取地区表缓存
        $cache     = Yii::$app->cache;
        $areaCache = $cache->get(Cache::PC_ALL_AREA_TABLE_KEY);
        if (!$areaCache) {
            $areaCache = BaseArea::setAreaCache();
        }

        foreach ($list as &$item) {
            $item['packageNameTxt']  = BaseCompany::PACKAGE_TYPE_LIST[$item['package_type']];
            $item['uid']             = UUIDHelper::encrypt(UUIDHelper::TYPE_COMPANY, $item['id']);
            $item['natureTxt']       = Dictionary::getCompanyNatureName($item['nature']);
            $item['industryTxt']     = Trade::getIndustryName($item['industry_id']);
            $item['typeTxt']         = Dictionary::getCompanyTypeName($item['type']);
            $item['statusMemberTxt'] = Member::COMPANY_ACCOUNTS_STATUS_LIST[$item['statusMember']];
            $item['sourceTypeName']  = Company::TYPE_SOURCE_LIST[$item['source_type']];
            $item['name']            = Admin::findOneVal(['id' => $item['admin_id']], 'name') ?: '';
            $item['address']         = str_replace(',', '',
                $areaCache[$item['city_id']]['full_name'] . $item['address']);

            if ($item['source_type'] == Company::TYPE_SOURCE_ADD) {
                $item['createUserName'] = Admin::findOneVal(['id' => $item['create_admin_id']], 'name') ?: '';
            } else {
                $item['createUserName'] = Member::findOneVal(['id' => $item['create_admin_id']], 'username') ?: '';
            }
            // 如果是邮箱注册回显联系号码
            $item['mobile']      = $item['cMobile'] ?: $item['mMobile'];
            $item['viewingRate'] = BaseCompany::statCompanyViewingRate($item['id']);
        }

        // 导出的时候,全部字段都给,不用管前端应该要什么字段,这里面有
        // UID
        // 单位名称
        // 用户名
        // 联系人
        // 联系电话
        // 会员类型
        // 创建时间
        // 最近登陆
        // 所在地
        // 操作
        // 单位性质
        // 单位类型
        // 所属行业
        // 单位状态
        // 创建人
        // 入网来源
        // 业务员
        // 登录次数
        // 简历下载点数
        //    查看简历数（去重，查看同一用户简历记为一次）
        //    收藏人才数
        //    简历分享次数
        //    邀约投递数
        //    简历扣点下载数
        /**
         * 2022-10-24 梁凤妍 提出的需求
         * 单位维度：单位主页的收藏量【无】、单位主页的阅读量【无】、单位风采图和横幅图片更新维护情况【前端可查看】……
         * 公告维度：公告发布数量、公告阅读次数、公告收藏数量【无】……
         * 职位维度：职位发布个数、职位收藏量【无】、职位总阅读量、职位刷新次数、
         */
        $headers = [
            'UID',
            '单位名称',
            '用户名',
            '联系人',
            '联系电话',
            '会员类型',
            '创建时间',
            '最近登陆',
            '所在地',
            '单位性质',
            '单位类型',
            '所属行业',
            '单位状态',
            '创建人',
            '入网来源',
            '业务员',
            '简历查看率',
            '投递次数',
            '硕士投递次数',
            '博士投递次数',
            '登录次数',
            '简历下载点数',
            '查看简历数',
            '收藏人才数',
            '简历分享次数',
            '邀约投递数',
            '简历扣点下载数',
            '单位主页的收藏量',
            '单位主页的阅读量',
            '公告发布数量',
            '公告阅读次数',
            '公告收藏数量',
            '职位发布个数',
            '职位收藏量',
            '职位总阅读量',
            '职位刷新次数',
        ];

        $data = [];

        foreach ($list as $item) {
            // 剩余简历下载点数	查看简历数（去重，查看同一用户简历记为一次）	收藏人才数	简历分享次数	邀约投递数	简历扣点下载数
            $item = array_merge($item, BaseCompany::resumeLibraryData($item['id']));
            // 偷偷添加一些运营数据,站外投递,站内投递
            // 找到站外投递次数,站内投递次数
            $onSiteSApplyAmount = JobApply::find()
                ->where(['company_id' => $item['id']])
                ->count();

            // 投递里面分硕士和博士投递
            // 硕士投递次数
            $masterApplyAmount = JobApply::find()
                ->where(['company_id' => $item['id']])
                ->innerJoin('resume', 'resume.id = job_apply.resume_id')
                ->andWhere(['resume.top_education_code' => BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE])
                ->count();
            // 博士投递次数
            $doctorApplyAmount = JobApply::find()
                ->where(['company_id' => $item['id']])
                ->innerJoin('resume', 'resume.id = job_apply.resume_id')
                ->andWhere(['resume.top_education_code' => BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE])
                ->count();

            $loginCount = MemberActionLog::find()
                ->where([
                    'member_id' => $item['member_id'],
                    'is_login'  => MemberActionLog::IS_LOGIN_YES,
                ])
                ->count();

            // 收藏量
            $collectCount = BaseCompanyCollect::find()
                ->where([
                    'company_id' => $item['id'],
                ])
                ->count();

            // 找到所有有审核通过历史的公告和职位
            $announcementIds = BaseAnnouncement::find()
                ->alias('a')
                ->innerJoin(['b' => BaseArticle::tableName()], 'a.article_id = b.id')
                ->select('a.id,b.click')
                ->where([
                    'company_id' => $item['id'],
                ])
                ->andWhere([
                    '<>',
                    'b.refresh_time',
                    TimeHelper::ZERO_TIME,
                ])
                ->asArray()
                ->all();

            $announcementCollectCount = 0;
            $announcementClickCount   = 0;
            $jobCollectCount          = 0;
            $jobRefreshCount          = 0;
            $jobClickCount            = 0;

            // 找职位
            $jobIds = Job::find()
                ->select('id,click')
                ->where([
                    'company_id' => $item['id'],
                ])
                ->andWhere([
                    '<>',
                    'refresh_time',
                    TimeHelper::ZERO_TIME,
                ])
                ->asArray()
                ->all();

            // 循环公告去找收藏量
            foreach ($announcementIds as $announcementId) {
                $announcementClickCount   += $announcementId['click'];
                $announcementCollectCount += BaseAnnouncementCollect::find()
                    ->where([
                        'announcement_id' => $announcementId['id'],
                    ])
                    ->count();
            }

            // 循环职位去找收藏量和刷新量
            foreach ($jobIds as $jobId) {
                $jobClickCount   += $jobId['click'];
                $jobCollectCount += BaseJobCollect::find()
                    ->where([
                        'job_id' => $jobId['id'],
                    ])
                    ->count();
                $jobRefreshCount += BaseJobHandleLog::find()
                    ->where([
                        'job_id'      => $jobId['id'],
                        'handle_type' => BaseJobHandleLog::HANDLE_TYPE_REFRESH,
                    ])
                    ->count();
            }

            $data[] = [
                $item['uid'],
                $item['full_name'],
                $item['username'],
                $item['contact'],
                $item['mobile'],
                $item['packageNameTxt'],
                $item['add_time'],
                $item['last_login_time'],
                $item['address'],
                $item['natureTxt'],
                $item['typeTxt'],
                $item['industryTxt'],
                $item['statusMemberTxt'],
                $item['createUserName'],
                $item['sourceTypeName'],
                $item['name'],
                $item['viewingRate'],
                $onSiteSApplyAmount,
                $masterApplyAmount,
                $doctorApplyAmount,
                $loginCount,
                $item['remainResumeDownloadPoint'],
                $item['resumeView'],
                $item['resumeCollect'],
                $item['resumeShare'],
                $item['resumeInvite'],
                $item['consumeResumeDownloadPoint'],
                $collectCount,
                $item['click'],
                count($announcementIds),
                $announcementClickCount,
                $announcementCollectCount,
                count($jobIds),
                $jobCollectCount,
                $jobClickCount,
                $jobRefreshCount,
            ];
        }

        $excel    = new Excel();
        $fileName = $excel->export($data, $headers);

        // try {
        //     $wxWork = new WxWork();
        //     $wxWork->downLoadMessage($adminId, $fileName, '单位列表');
        // } catch (\Exception $e) {
        // }

        return [
            'excelUrl' => $fileName,
        ];
    }

    /**
     * 导出搜索相关的数据用于分析
     */
    public function actionSearchListData()
    {
        try {
            $app = CommonSearchApplication::getInstance();
            $rs  = $app->exportPcJobList();
            self::log($rs);
        } catch (\Exception $e) {
            bb($e->getMessage());
        }
    }

    public function actionUpdateParamsTxt()
    {
        try {
            $app = CommonSearchApplication::getInstance();
            $app->updateParamsTxt();
        } catch (\Exception $e) {
            bb($e->getMessage());
        }
    }

    public function actionAllMyCompanyList()
    {
        // 找到所有有关联业务员的单位
        $adminArray = BaseCompany::find()
            ->select('admin_id')
            ->where([
                'status' => BaseCompany::STATUS_ACTIVE,
            ])
            ->andWhere([
                '<>',
                'admin_id',
                0,
            ])
            ->distinct()
            ->asArray()
            ->column();

        self::log('一共找到' . count($adminArray) . '个业务员');

        foreach ($adminArray as $k => $item) {
            // 开始,找到业务员名字
            $adminName = BaseAdmin::findOneVal(['id' => $item], 'name');
            self::log('开始处理第' . ($k + 1) . '个业务员:' . $adminName);

            // 写入队列
            BaseAdminDownloadTask::create($item, BaseAdminDownloadTask::TYPE_MY_COMPANY_LIST, ['adminId' => $item]);
        }
    }

    // 网站运营部周报月报
    //
    public function actionOperationDepartment()
    {
        try {
            OperationDepartmentService::getInstance()
                ->daily();
        } catch (\Exception $e) {
            self::log($e->getMessage());
        }
    }

    // 七牛日志发送
    //
    public function actionQiniuLog()
    {
        $qiniu = new Qiniu('file');

        $data = $qiniu->getTopAccessInfo();

        $urlList1 = array_slice($data['urlList'], 0, 40);

        if (count($urlList1) > 0) {
            $markDown = '';
            $markDown .= "### 七牛文件流量列表 " . $data['startTime'] . ' - ' . $data['endTime'] . "\n\n";
            $markDown .= "| 文件链接 | 流量 |\n";
            $markDown .= "|----------|------|\n";
            // 前50
            foreach ($urlList1 as $item) {
                $markDown .= "| {$item['url']} | {$item['size']} |\n";
            }

            $wxWork = new WxWork();
            $wxWork->robotMessageToCrawler($markDown, true);
        }

        // 50以后
        $urlList2 = array_slice($data['urlList'], 40);

        if (count($urlList2) > 0) {
            $markDown = '';
            $markDown .= "### 七牛文件流量列表 " . $data['startTime'] . ' - ' . $data['endTime'] . "\n\n";
            $markDown .= "| 文件链接 | 流量 |\n";
            $markDown .= "|----------|------|\n";
            foreach ($urlList2 as $item) {
                $markDown .= "| {$item['url']} | {$item['size']} |\n";
            }

            $wxWork = new WxWork();
            $wxWork->robotMessageToCrawler($markDown, true);
        }

        $preIpList = [];

        $markDown        = "### 七牛文件流量列表 " . $data['startTime'] . ' - ' . $data['endTime'] . "\n\n";
        $markDown        .= "| ip | 城市 | 流量 |\n";
        $markDown        .= "|-----|-----|------|\n";
        $isSendIpMessage = false;
        foreach ($data['ipList'] as $k => $item) {
            if ($k <= 30) {
                $markDown        .= "| {$item['ip']} | {$item['city']} | {$item['size']} |\n";
                $isSendIpMessage = true;
            }
            $preIp                             = explode('.', $item['ip']);
            $preIp                             = implode('.', array_slice($preIp, 0, 3)) . '.0/24';
            $preIpList[$preIp]['count']        += 1;
            $preIpList[$preIp]['originalSize'] += $item['originalSize'];
        }

        if ($isSendIpMessage) {
            $wxWork->robotMessageToCrawler($markDown, true);
        }

        // 找出超过3次的ip段
        $preIpList = array_filter($preIpList, function ($count) {
            return $count['count'] >= 2;
        });

        $markDown = "### 七牛ip段列表 " . $data['startTime'] . ' - ' . $data['endTime'] . "\n\n";
        $markDown .= "| ip段 | 数量 | 流量 ｜\n";
        $markDown .= "|-----|-----|----|\n";

        if (count($preIpList) > 0) {
            foreach ($preIpList as $ip => $item) {
                $size     = BaseFile::sizeToTxt($item['originalSize']);
                $markDown .= "{$ip}| {$item['count']} | {$size} |\n";
            }

            $wxWork->robotMessageToCrawler($markDown, true);
        }
    }

}
