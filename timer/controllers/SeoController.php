<?php

namespace timer\controllers;

use admin\models\Seo;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArticle;
use common\base\models\BaseBaiduZzPushLog;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyInfoAuth;
use common\base\models\BaseCompanyInfoAuthLog;
use common\base\models\BaseJob;
use common\base\models\BaseNews;
use common\helpers\ArrayHelper;
use common\helpers\UrlHelper;
use common\libs\Cache;
use common\libs\IndexNow;
use Yii;

class SeoController extends BaseTimerController
{
    /**
     * 定时更新公告url到百度站长平台,这个脚本其实是30分钟运行一次的,也就是1800秒,但是为了避免运行的误差,所以拿数据的时候就拿2000秒前的,然后过滤一下已经更新过的
     *
     * https://weixin.processon.com/diagraming/629f3605e4b0ee4bd9707714
     */
    public function actionPushZzAnnouncement()
    {
        try {
            $date = date('Ymd');
            // $time  = date('Y-m-d H:i:s', CUR_TIMESTAMP - 2000);
            $time  = date('Y-m-d H:i:s', CUR_TIMESTAMP - 2000);
            $query = BaseArticle::find()
                ->alias('a')
                ->innerJoin(['b' => BaseAnnouncement::tableName()], 'a.id=b.article_id')
                ->select([
                    'b.id',
                ])
                ->where([
                    'a.status'    => BaseArticle::STATUS_ACTIVE,
                    'a.is_show'   => BaseArticle::IS_DELETE_YES,
                    'a.is_delete' => BaseArticle::IS_DELETE_NO,
                    'a.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                ]);

            $query1 = clone $query;

            $list = $query->andWhere([
                '>',
                'first_release_time',
                $time,
            ])
                ->asArray()
                ->column();

            // 初次发布
            self::log('初次发布的公告数量: ' . count($list));

            $list1 = $query1->andWhere([
                '>',
                'release_time',
                $time,
            ])
                ->andWhere([
                    '<>',
                    'first_release_time',
                    'release_time',
                ])
                ->andWhere([
                    'not in',
                    'b.id',
                    $list,
                ])
                ->asArray()
                ->column();

            self::log('再次发布的公告数量: ' . count($list1));

            $allList = ArrayHelper::merge($list, $list1);

            // 拼接pc和h5的url
            $params = Yii::$app->params['baiduZZ'];
            $pcSite = $params['pcSite'];
            $h5Site = $params['h5Site'];

            // 缓存里面只保存两天的数据,更多的数据无需保存
            $cachePcPushUrlList = $this->getTodayPushUrl($date, Seo::URL_TYPE_PC);
            $cacheH5PushUrlList = $this->getTodayPushUrl($date, Seo::URL_TYPE_H5);
            $thisPcPushUrlList  = [];
            $thisH5PushUrlList  = [];

            foreach ($allList as $item) {
                $pcDetailUrl  = 'https://' . $pcSite . UrlHelper::createAnnouncementDetailPath($item);
                $pcJobListUrl = 'https://' . $pcSite . UrlHelper::createPcAnnouncementJobListPath($item);
                $h5DetailUrl  = 'https://' . $h5Site . UrlHelper::createAnnouncementDetailPath($item);

                if (!in_array($pcDetailUrl, $cachePcPushUrlList)) {
                    $thisPcPushUrlList[] = $pcDetailUrl;
                }

                if (!in_array($pcJobListUrl, $cachePcPushUrlList)) {
                    $thisPcPushUrlList[] = $pcJobListUrl;
                }

                if (!in_array($h5DetailUrl, $cacheH5PushUrlList)) {
                    $thisH5PushUrlList[] = $h5DetailUrl;
                }
            }

            self::log('过滤以后的pcvUrl数量为: ' . count($thisPcPushUrlList));
            if (!empty($thisPcPushUrlList)) {
                // 因为一次最多只能推送2000个url,所以要分批次推送
                $pcPushUrlList = array_chunk($thisPcPushUrlList, 2000);
                foreach ($pcPushUrlList as $pcPushUrl) {
                    Seo::baiduZzPush($pcPushUrl, $pcSite);
                    $this->setTodayPushUrl($pcPushUrl, $date, Seo::URL_TYPE_PC);
                }
            }

            self::log('过滤以后的h5vUrl数量为: ' . count($thisH5PushUrlList));
            if (!empty($thisH5PushUrlList)) {
                // 因为一次最多只能推送2000个url,所以要分批次推送
                $h5PushUrlList = array_chunk($thisH5PushUrlList, 2000);
                foreach ($h5PushUrlList as $h5PushUrl) {
                    Seo::baiduZzPush($h5PushUrl, $h5Site);
                    $this->setTodayPushUrl($h5PushUrl, $date, Seo::URL_TYPE_H5);
                }
            }

            // 开始做indexnow推送逻辑，为了避免和百度那边有逻辑冲突，就重新去处理一下数据
            $cachePcPushUrlList = $this->getTodayPushUrl($date, Seo::URL_TYPE_PC, BaseBaiduZzPushLog::TYPE_INDEXNOW);
            $cacheH5PushUrlList = $this->getTodayPushUrl($date, Seo::URL_TYPE_H5, BaseBaiduZzPushLog::TYPE_INDEXNOW);
            $thisPcPushUrlList  = [];
            $thisH5PushUrlList  = [];

            foreach ($allList as $item) {
                $pcDetailUrl  = 'https://' . $pcSite . UrlHelper::createAnnouncementDetailPath($item);
                $pcJobListUrl = 'https://' . $pcSite . UrlHelper::createPcAnnouncementJobListPath($item);
                $h5DetailUrl  = 'https://' . $h5Site . UrlHelper::createAnnouncementDetailPath($item);

                if (!in_array($pcDetailUrl, $cachePcPushUrlList)) {
                    $thisPcPushUrlList[] = $pcDetailUrl;
                }

                if (!in_array($pcJobListUrl, $cachePcPushUrlList)) {
                    $thisPcPushUrlList[] = $pcJobListUrl;
                }

                if (!in_array($h5DetailUrl, $cacheH5PushUrlList)) {
                    $thisH5PushUrlList[] = $h5DetailUrl;
                }
            }

            self::log('过滤以后的pcvUrl数量为: ' . count($thisPcPushUrlList));
            if (!empty($thisPcPushUrlList)) {
                Seo::indexNowPush($thisPcPushUrlList, Seo::URL_TYPE_PC);
                $this->setTodayPushUrl($thisPcPushUrlList, $date, Seo::URL_TYPE_PC, BaseBaiduZzPushLog::TYPE_INDEXNOW);
            }

            self::log('过滤以后的h5vUrl数量为: ' . count($thisH5PushUrlList));
            if (!empty($thisH5PushUrlList)) {
                Seo::indexNowPush($thisH5PushUrlList, Seo::URL_TYPE_H5);
                $this->setTodayPushUrl($thisH5PushUrlList, $date, Seo::URL_TYPE_H5, BaseBaiduZzPushLog::TYPE_INDEXNOW);
            }
        } catch (\Exception $e) {
            self::log($e->getMessage());

            return;
        }
    }

    public function actionPushZzJob()
    {
        try {
            $date  = date('Ymd');
            $time  = date('Y-m-d H:i:s', CUR_TIMESTAMP - 2000);
            $query = BaseJob::find()
                ->alias('a')
                ->select([
                    'a.id',
                ])
                ->where([
                    'a.status'  => BaseJob::STATUS_ONLINE,
                    'a.is_show' => BaseJob::IS_SHOW_YES,

                ]);

            $query1 = clone $query;

            $list = $query->andWhere([
                '>',
                'first_release_time',
                $time,
            ])
                ->asArray()
                ->column();

            // 初次发布
            self::log('初次发布的职位数量: ' . count($list));

            $list1 = $query1->andWhere([
                '>',
                'release_time',
                $time,
            ])
                ->andWhere([
                    '<>',
                    'first_release_time',
                    'release_time',
                ])
                ->andWhere([
                    'not in',
                    'a.id',
                    $list,
                ])
                ->asArray()
                ->column();

            self::log('再次发布的职位数量: ' . count($list1));

            $allList = ArrayHelper::merge($list, $list1);

            // 拼接pc和h5的url
            $params = Yii::$app->params['baiduZZ'];
            $pcSite = $params['pcSite'];
            $h5Site = $params['h5Site'];

            // 缓存里面只保存两天的数据,更多的数据无需保存
            $cachePcPushUrlList = $this->getTodayPushUrl($date, Seo::URL_TYPE_PC);
            $cacheH5PushUrlList = $this->getTodayPushUrl($date, Seo::URL_TYPE_H5);
            $thisPcPushUrlList  = [];
            $thisH5PushUrlList  = [];

            foreach ($allList as $item) {
                $pcDetailUrl = 'https://' . $pcSite . UrlHelper::createJobDetailPath($item);
                $h5DetailUrl = 'https://' . $h5Site . UrlHelper::createJobDetailPath($item);

                if (!in_array($pcDetailUrl, $cachePcPushUrlList)) {
                    $thisPcPushUrlList[] = $pcDetailUrl;
                }

                if (!in_array($h5DetailUrl, $cacheH5PushUrlList)) {
                    $thisH5PushUrlList[] = $h5DetailUrl;
                }
            }

            self::log('过滤以后的pcvUrl数量为: ' . count($thisPcPushUrlList));
            if (!empty($thisPcPushUrlList)) {
                // 因为一次最多只能推送2000个url,所以要分批次推送
                $pcPushUrlList = array_chunk($thisPcPushUrlList, 2000);
                foreach ($pcPushUrlList as $pcPushUrl) {
                    Seo::baiduZzPush($pcPushUrl, $pcSite);
                    $this->setTodayPushUrl($pcPushUrl, $date, Seo::URL_TYPE_PC);
                }
            }

            self::log('过滤以后的h5vUrl数量为: ' . count($thisH5PushUrlList));
            if (!empty($thisH5PushUrlList)) {
                // 因为一次最多只能推送2000个url,所以要分批次推送
                $h5PushUrlList = array_chunk($thisH5PushUrlList, 2000);
                foreach ($h5PushUrlList as $h5PushUrl) {
                    Seo::baiduZzPush($h5PushUrl, $h5Site);
                    $this->setTodayPushUrl($h5PushUrl, $date, Seo::URL_TYPE_H5);
                }
            }

            // 开始做indexnow推送逻辑，为了避免和百度那边有逻辑冲突，就重新去处理一下数据
            $cachePcPushUrlList = $this->getTodayPushUrl($date, Seo::URL_TYPE_PC, BaseBaiduZzPushLog::TYPE_INDEXNOW);
            $cacheH5PushUrlList = $this->getTodayPushUrl($date, Seo::URL_TYPE_H5, BaseBaiduZzPushLog::TYPE_INDEXNOW);
            $thisPcPushUrlList  = [];
            $thisH5PushUrlList  = [];

            foreach ($allList as $item) {
                $pcDetailUrl = 'https://' . $pcSite . UrlHelper::createJobDetailPath($item);
                $h5DetailUrl = 'https://' . $h5Site . UrlHelper::createJobDetailPath($item);

                if (!in_array($pcDetailUrl, $cachePcPushUrlList)) {
                    $thisPcPushUrlList[] = $pcDetailUrl;
                }

                if (!in_array($h5DetailUrl, $cacheH5PushUrlList)) {
                    $thisH5PushUrlList[] = $h5DetailUrl;
                }
            }

            self::log('过滤以后的pcvUrl数量为: ' . count($thisPcPushUrlList));
            if (!empty($thisPcPushUrlList)) {
                Seo::indexNowPush($thisPcPushUrlList, Seo::URL_TYPE_PC);
                $this->setTodayPushUrl($thisPcPushUrlList, $date, Seo::URL_TYPE_PC, BaseBaiduZzPushLog::TYPE_INDEXNOW);
            }

            self::log('过滤以后的h5vUrl数量为: ' . count($thisH5PushUrlList));
            if (!empty($thisH5PushUrlList)) {
                Seo::indexNowPush($thisH5PushUrlList, Seo::URL_TYPE_H5);
                $this->setTodayPushUrl($thisH5PushUrlList, $date, Seo::URL_TYPE_H5, BaseBaiduZzPushLog::TYPE_INDEXNOW);
            }
        } catch (\Exception $e) {
            self::log($e->getMessage());

            return;
        }
    }

    public function actionPushZzNews()
    {
        //article里面的refresh_time并且是在线的，显示的，非删除的
        // status=1,is_show=1,is_delete=2
        try {
            $date  = date('Ymd');
            $time  = date('Y-m-d H:i:s', CUR_TIMESTAMP - 2000);
            $query = BaseArticle::find()
                ->alias('a')
                ->innerJoin(['b' => BaseNews::tableName()], 'a.id=b.article_id')
                ->select([
                    'b.id',
                ])
                ->where([
                    'a.status'    => BaseArticle::STATUS_ACTIVE,
                    'a.is_show'   => BaseArticle::IS_DELETE_YES,
                    'a.is_delete' => BaseArticle::IS_DELETE_NO,
                    'a.type'      => BaseArticle::TYPE_NEWS,
                ]);

            $allList = $query->andWhere([
                '>',
                'release_time',
                $time,
            ])
                ->asArray()
                ->column();

            // 初次发布

            // 拼接pc和h5的url
            $params = Yii::$app->params['baiduZZ'];
            $pcSite = $params['pcSite'];
            $h5Site = $params['h5Site'];

            // 缓存里面只保存两天的数据,更多的数据无需保存
            $cachePcPushUrlList = $this->getTodayPushUrl($date, Seo::URL_TYPE_PC);
            $cacheH5PushUrlList = $this->getTodayPushUrl($date, Seo::URL_TYPE_H5);
            $thisPcPushUrlList  = [];
            $thisH5PushUrlList  = [];

            foreach ($allList as $item) {
                $pcDetailUrl = 'https://' . $pcSite . UrlHelper::createNewsDetailPath($item);
                $h5DetailUrl = 'https://' . $h5Site . UrlHelper::createNewsDetailPath($item);

                if (!in_array($pcDetailUrl, $cachePcPushUrlList)) {
                    $thisPcPushUrlList[] = $pcDetailUrl;
                }

                if (!in_array($h5DetailUrl, $cacheH5PushUrlList)) {
                    $thisH5PushUrlList[] = $h5DetailUrl;
                }
            }

            self::log('过滤以后的pcvUrl数量为: ' . count($thisPcPushUrlList));
            if (!empty($thisPcPushUrlList)) {
                // 因为一次最多只能推送2000个url,所以要分批次推送
                $pcPushUrlList = array_chunk($thisPcPushUrlList, 2000);
                foreach ($pcPushUrlList as $pcPushUrl) {
                    Seo::baiduZzPush($pcPushUrl, $pcSite);
                    $this->setTodayPushUrl($pcPushUrl, $date, Seo::URL_TYPE_PC);
                }
            }

            self::log('过滤以后的h5vUrl数量为: ' . count($thisH5PushUrlList));
            if (!empty($thisH5PushUrlList)) {
                // 因为一次最多只能推送2000个url,所以要分批次推送
                $h5PushUrlList = array_chunk($thisH5PushUrlList, 2000);
                foreach ($h5PushUrlList as $h5PushUrl) {
                    Seo::baiduZzPush($h5PushUrl, $h5Site);
                    $this->setTodayPushUrl($h5PushUrl, $date, Seo::URL_TYPE_H5);
                }
            }

            // 开始做indexnow推送逻辑，为了避免和百度那边有逻辑冲突，就重新去处理一下数据
            $cachePcPushUrlList = $this->getTodayPushUrl($date, Seo::URL_TYPE_PC, BaseBaiduZzPushLog::TYPE_INDEXNOW);
            $cacheH5PushUrlList = $this->getTodayPushUrl($date, Seo::URL_TYPE_H5, BaseBaiduZzPushLog::TYPE_INDEXNOW);
            $thisPcPushUrlList  = [];
            $thisH5PushUrlList  = [];

            foreach ($allList as $item) {
                $pcDetailUrl = 'https://' . $pcSite . UrlHelper::createNewsDetailPath($item);
                $h5DetailUrl = 'https://' . $h5Site . UrlHelper::createNewsDetailPath($item);

                if (!in_array($pcDetailUrl, $cachePcPushUrlList)) {
                    $thisPcPushUrlList[] = $pcDetailUrl;
                }

                if (!in_array($h5DetailUrl, $cacheH5PushUrlList)) {
                    $thisH5PushUrlList[] = $h5DetailUrl;
                }
            }

            self::log('过滤以后的pcvUrl数量为: ' . count($thisPcPushUrlList));
            if (!empty($thisPcPushUrlList)) {
                Seo::indexNowPush($thisPcPushUrlList, Seo::URL_TYPE_PC);
                $this->setTodayPushUrl($thisPcPushUrlList, $date, Seo::URL_TYPE_PC, BaseBaiduZzPushLog::TYPE_INDEXNOW);
            }

            self::log('过滤以后的h5vUrl数量为: ' . count($thisH5PushUrlList));
            if (!empty($thisH5PushUrlList)) {
                Seo::indexNowPush($thisH5PushUrlList, Seo::URL_TYPE_H5);
                $this->setTodayPushUrl($thisH5PushUrlList, $date, Seo::URL_TYPE_H5, BaseBaiduZzPushLog::TYPE_INDEXNOW);
            }
        } catch (\Exception $e) {
            self::log($e->getMessage());

            return;
        }
    }

    public function actionPushZzCompany()
    {
        // 首先找非合作客户的主页信息
        try {
            $date  = date('Ymd');
            $time  = date('Y-m-d H:i:s', CUR_TIMESTAMP - 2000);
            $list1 = BaseCompany::find()
                ->select('id')
                ->where(['is_cooperation' => BaseCompany::COOPERATIVE_UNIT_NO])
                ->andWhere([
                    '>',
                    'add_time',
                    $time,
                ])
                ->asArray()
                ->column();

            $list2 = BaseCompanyInfoAuthLog::find()
                ->select('company_id as id')
                ->where([
                    'audit_status' => BaseCompanyInfoAuth::AUDIT_STATUS_PASS,
                    'phase'        => BaseCompanyInfoAuth::PHASE_THREE,
                ])
                ->andWhere([
                    '>',
                    'add_time',
                    $time,
                ])
                ->asArray()
                ->column();

            $list = ArrayHelper::merge($list1, $list2);
            // 拼接pc和h5的url
            $params = Yii::$app->params['baiduZZ'];
            $pcSite = $params['pcSite'];
            $h5Site = $params['h5Site'];

            // 缓存里面只保存两天的数据,更多的数据无需保存
            $cachePcPushUrlList = $this->getTodayPushUrl($date, Seo::URL_TYPE_PC);
            $cacheH5PushUrlList = $this->getTodayPushUrl($date, Seo::URL_TYPE_H5);
            $thisPcPushUrlList  = [];
            $thisH5PushUrlList  = [];

            foreach ($list as $item) {
                $pcDetailUrl = 'https://' . $pcSite . UrlHelper::createCompanyDetailPath($item);
                $h5DetailUrl = 'https://' . $h5Site . UrlHelper::createCompanyDetailPath($item);
                if (!in_array($pcDetailUrl, $cachePcPushUrlList)) {
                    $thisPcPushUrlList[] = $pcDetailUrl;
                }

                if (!in_array($h5DetailUrl, $cacheH5PushUrlList)) {
                    $thisH5PushUrlList[] = $h5DetailUrl;
                }
            }

            self::log('过滤以后的pcvUrl数量为: ' . count($thisPcPushUrlList));
            if (!empty($thisPcPushUrlList)) {
                // 因为一次最多只能推送2000个url,所以要分批次推送
                $pcPushUrlList = array_chunk($thisPcPushUrlList, 2000);
                foreach ($pcPushUrlList as $pcPushUrl) {
                    Seo::baiduZzPush($pcPushUrl, $pcSite);
                    $this->setTodayPushUrl($pcPushUrl, $date, Seo::URL_TYPE_PC);
                }
            }

            self::log('过滤以后的h5vUrl数量为: ' . count($thisH5PushUrlList));
            if (!empty($thisH5PushUrlList)) {
                // 因为一次最多只能推送2000个url,所以要分批次推送
                $h5PushUrlList = array_chunk($thisH5PushUrlList, 2000);
                foreach ($h5PushUrlList as $h5PushUrl) {
                    Seo::baiduZzPush($h5PushUrl, $h5Site);
                    $this->setTodayPushUrl($h5PushUrl, $date, Seo::URL_TYPE_H5);
                }
            }

            // 开始做indexnow推送逻辑，为了避免和百度那边有逻辑冲突，就重新去处理一下数据
            $cachePcPushUrlList = $this->getTodayPushUrl($date, Seo::URL_TYPE_PC, BaseBaiduZzPushLog::TYPE_INDEXNOW);
            $cacheH5PushUrlList = $this->getTodayPushUrl($date, Seo::URL_TYPE_H5, BaseBaiduZzPushLog::TYPE_INDEXNOW);
            $thisPcPushUrlList  = [];
            $thisH5PushUrlList  = [];

            foreach ($list as $item) {
                $pcDetailUrl = 'https://' . $pcSite . UrlHelper::createCompanyDetailPath($item);
                $h5DetailUrl = 'https://' . $h5Site . UrlHelper::createCompanyDetailPath($item);
                if (!in_array($pcDetailUrl, $cachePcPushUrlList)) {
                    $thisPcPushUrlList[] = $pcDetailUrl;
                }

                if (!in_array($h5DetailUrl, $cacheH5PushUrlList)) {
                    $thisH5PushUrlList[] = $h5DetailUrl;
                }
            }

            self::log('过滤以后的pcvUrl数量为: ' . count($thisPcPushUrlList));
            if (!empty($thisPcPushUrlList)) {
                Seo::indexNowPush($thisPcPushUrlList, Seo::URL_TYPE_PC);
                $this->setTodayPushUrl($thisPcPushUrlList, $date, Seo::URL_TYPE_PC, BaseBaiduZzPushLog::TYPE_INDEXNOW);
            }

            self::log('过滤以后的h5vUrl数量为: ' . count($thisH5PushUrlList));
            if (!empty($thisH5PushUrlList)) {
                Seo::indexNowPush($thisH5PushUrlList, Seo::URL_TYPE_H5);
                $this->setTodayPushUrl($thisH5PushUrlList, $date, Seo::URL_TYPE_H5, BaseBaiduZzPushLog::TYPE_INDEXNOW);
            }
        } catch (\Exception $e) {
            self::log($e->getMessage());

            return;
        }
    }

    private function getTodayPushUrl($date, $urlType, $platform = BaseBaiduZzPushLog::TYPE_BAIDU)
    {
        if ($platform == BaseBaiduZzPushLog::TYPE_BAIDU) {
            $key = $urlType == Seo::URL_TYPE_PC ? Cache::ALL_BAIDUZZ_PUSH_PC_RECORD_KEY : Cache::ALL_BAIDUZZ_PUSH_H5_RECORD_KEY;
        }

        if ($platform == BaseBaiduZzPushLog::TYPE_INDEXNOW) {
            $key = $urlType == Seo::URL_TYPE_PC ? Cache::ALL_INDEXNOW_PUSH_PC_RECORD_KEY : Cache::ALL_INDEXNOW_PUSH_H5_RECORD_KEY;
        }

        $cacheData = Cache::get($key);
        if (!empty($cacheData)) {
            $cacheData = json_decode($cacheData, true);
            if (isset($cacheData[$date])) {
                $pcPushUrlList = $cacheData[$date];
            }
        }

        return $pcPushUrlList;
    }

    private function setTodayPushUrl($urls, $date, $urlType, $platform = BaseBaiduZzPushLog::TYPE_BAIDU)
    {
        if ($platform == BaseBaiduZzPushLog::TYPE_BAIDU) {
            $key = $urlType == Seo::URL_TYPE_PC ? Cache::ALL_BAIDUZZ_PUSH_PC_RECORD_KEY : Cache::ALL_BAIDUZZ_PUSH_H5_RECORD_KEY;
        }

        if ($platform == BaseBaiduZzPushLog::TYPE_INDEXNOW) {
            $key = $urlType == Seo::URL_TYPE_PC ? Cache::ALL_INDEXNOW_PUSH_PC_RECORD_KEY : Cache::ALL_INDEXNOW_PUSH_H5_RECORD_KEY;
        }

        $cacheData = Cache::get($key) ?: [];
        $cacheData = json_decode($cacheData, true);
        if (isset($cacheData[$date])) {
            $cacheData[$date] = array_unique(ArrayHelper::merge($cacheData[$date], $urls));
        } else {
            $cacheData[$date] = $urls;
        }
        Cache::set($key, json_encode($cacheData));
    }

}
