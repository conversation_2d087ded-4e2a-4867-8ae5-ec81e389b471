<?php

namespace admin\controllers;

use admin\models\Admin;
use admin\models\CompanyGroup;
use admin\models\Major;
use admin\models\News;
use admin\models\ResumeTag;
use common\base\controllers\BaseConfigController;
use common\base\models\BaseCompanyFeatureTag;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseMember;
use common\helpers\ArrayHelper;
use common\helpers\PingYinHelper;
use common\libs\Cache;
use Yii;
use yii\console\Response;

class ConfigController extends BaseAdminController
{

    use BaseConfigController;

    /**
     *
     */
    public function actionSetTableStagingField()
    {
        $key   = Yii::$app->request->post('key');
        $value = Yii::$app->request->post('value');

        // TODO 这里做一下key和value的限制
        if (!$key || !$value) {
            return $this->fail();
        }
        $adminId = Yii::$app->user->id;
        $realKey = Cache::PC_ADMIN_TABLE_STAGING_FIELD_KEY . ':' . $key . ':' . $adminId;

        Cache::set($realKey, $value);

        return $this->success('');
    }

    /**
     *
     */
    public function actionGetTableStagingField()
    {
        $key = Yii::$app->request->get('key');
        if (!$key) {
            return $this->fail();
        }
        $adminId = Yii::$app->user->id;
        $realKey = Cache::PC_ADMIN_TABLE_STAGING_FIELD_KEY . ':' . $key . ':' . $adminId;

        return $this->success(['value' => Cache::get($realKey) ?: '']);
    }

    public function actionGetAllSale()
    {
        // 获取所有的业务员
        return $this->success(Admin::getAllSaleList());
    }

    public function actionMajorAiRecognition()
    {
        $text = Yii::$app->request->post('text');

        return $this->success(Major::aiRecognition($text));
    }

    /**
     * 获取投递方式与报名方式列表
     * @return Response|\yii\web\Response
     */
    public function actionGetDeliveryApply()
    {
        $result = [
            'delivery_way' => ArrayHelper::obj2Arr(BaseJob::DELIVERY_WAY_SELECT_NAME),
            'apply_type'   => BaseJob::getSignUpList(),
        ];

        return $this->success($result);
    }

    /**
     * 获取是否绑定微信类型
     * @return Response|\yii\web\Response
     */
    public function actionGetWxBind()
    {
        $result = [
            [
                'k' => '不限',
                'v' => '',
            ],
            [
                'k' => '是',
                'v' => 1,
            ],
            [
                'k' => '否',
                'v' => 2,
            ],
        ];

        return $this->success($result);
    }

    public function actionChangeIsMiniapp()
    {
        // 这里是否需要设置一下权限？
        // 直接在这里做?
        try {
            BaseDictionary::changeIsMiniapp(Yii::$app->request->post());

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionChangeIsAbroad()
    {
        // 这里是否需要设置一下权限？
        // 直接在这里做?
        try {
            BaseDictionary::changeIsAbroad(Yii::$app->request->post());

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionCompanyGroup()
    {
        return $this->success(CompanyGroup::filter());
    }

    /**
     * 获取单位查询时的特色标签列表
     * @return Response|\yii\web\Response
     */
    public function actionGetFeaturedTagList()
    {
        return $this->success(BaseCompanyFeatureTag::getFeaturedTagList());
    }

    // 获取所有人才标签
    public function actionGetResumeTagList()
    {
        return $this->success(ArrayHelper::object2LV(ResumeTag::getAllList()));
    }

    /**
     * 获取首写字母的缩写
     */
    public function actionGetFirstLetter()
    {
        return $this->success(['text' => PingYinHelper::get(Yii::$app->request->get('text'), 1, '', '', 1)]);
    }

    public function actionGetUseSiteTypeList()
    {
        return $this->success(ArrayHelper::obj22Arr(News::USE_SITE_TYPE_LIST));
    }

    /**
     * 获取用户状态（只适用于人才查询）
     */
    public function actionMemberStatusList()
    {
        return $this->success(ArrayHelper::obj22Arr(BaseMember::SELECT_STATUS_LIST));
    }

}