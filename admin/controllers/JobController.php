<?php

namespace admin\controllers;

use admin\models\Job;
use admin\models\JobHandleLog;
use admin\models\UploadForm;
use common\base\models\BaseAdminDownloadTask;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseJob;
use common\components\MessageException;
use common\helpers\ArrayHelper;
use common\helpers\FormatConverter;
use common\helpers\ValidateHelper;
use common\service\CommonService;
use common\service\company\SubMemberService;
use common\service\downloadTask\DownLoadTaskApplication;
use common\service\downloadTask\ExecuteAdminService;
use common\service\job\AuditService;
use common\service\job\BaseService;
use common\service\v2\job\AddBatchImportService;
use common\service\v2\job\AddService;
use common\service\v2\job\AuditDetailService;
use common\service\v2\job\AuditHistoryLogService;
use common\service\v2\job\AuditStatusService;
use common\service\v2\job\ContactService;
use common\service\v2\job\DeleteService;
use common\service\v2\job\DetailService;
use common\service\v2\job\EditInitService;
use common\service\v2\job\EditService;
use common\service\v2\job\EstablishmentService;
use common\service\v2\job\IndexService;
use common\service\v2\job\IsShowService;
use common\service\v2\job\LimitAttachmentService;
use common\service\v2\job\LimitEducationService;
use common\service\v2\job\RefreshService;
use common\service\v2\job\RepublishService;
use common\service\v2\job\OfflineService;
use common\service\v2\job\TemplateInitAddService;
use common\service\v2\job\TemplateListService;
use common\service\v2\job\WelfareService;
use queue\Producer;
use Yii;
use yii\base\Exception;
use yii\console\Response;

class JobController extends BaseAdminController
{
    /**
     * 新增、添加纯职位
     * @return Response|\yii\web\Response
     */
    public function actionAdd()
    {
        $tran = Yii::$app->db->beginTransaction();
        try {
            (new AddService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();
            $tran->commit();

            return $this->success();
        } catch (\Exception $e) {
            $tran->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位模板列表
     * @return Response|\yii\web\Response
     */
    public function actionTemplateList()
    {
        try {
            return $this->success((new TemplateListService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->setParams(Yii::$app->request->get())
                ->run());
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位模板初始化
     * @return Response|\yii\web\Response
     */
    public function actionTemplateInitAdd()
    {
        try {
            return $this->success((new TemplateInitAddService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run());
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量导入新增职位
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAddBatchImport()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $transaction->commit();
            (new AddBatchImportService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();

            return $this->success(['msgTxt' => '成功批量导入数据']);
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 编辑初始化职位
     * @return Response|\yii\web\Response
     */
    public function actionEditInit()
    {
        try {
            return $this->success((new EditInitService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run());
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 编辑职位
     * 编辑纯职位、公告下职位
     * @return Response|\yii\web\Response
     */
    public function actionEdit()
    {
        $tran = Yii::$app->db->beginTransaction();
        try {
            (new EditService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();
            $tran->commit();

            return $this->success();
        } catch (Exception $e) {
            $tran->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位详情
     * @return Response|\yii\web\Response
     */
    public function actionDetail()
    {
        try {
            return $this->success((new DetailService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run());
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 单位检索
     * @return Response|\yii\web\Response
     */
    public function actionGetCompanyList()
    {
        $request = Yii::$app->request->get();
        try {
            $list = Job::getCompanyList(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取单位体系下的账号列表
     */
    public function actionGetCompanySubMemberList()
    {
        try {
            return $this->success((new SubMemberService())->getCompanySubMemberList());
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位审核详情
     * @return Response|\yii\web\Response
     */
    public function actionAuditDetail()
    {
        try {
            return $this->success((new AuditDetailService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run());
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位审核历史日志
     * @return Response|\yii\web\Response
     */
    public function actionAuditHistoryLog()
    {
        try {
            return $this->success((new AuditHistoryLogService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run());
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告审核通过/拒绝
     * @return Response|\yii\web\Response
     */
    public function actionAuditStatus()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $res = (new AuditStatusService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();
            $transaction->commit();

            return $this->success($res);
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 上传临时Excel文件
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionUploadExcel()
    {
        $model = new UploadForm();
        $model->setUploadType('file');

        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $path = 'job_temp_excel';
            $data = $model->temporaryUploadExcel('file', $path);
            $transaction->commit();

            return $this->success([
                'url'     => $data['path'],
                'fullUrl' => \Yii::$app->params['homeUrl'] . '/' . $data['path'],
            ]);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 审核状态
     * @return Response|\yii\web\Response
     */
    public function actionJobAuditStatusList()
    {
        try {
            $list = ArrayHelper::obj2Arr(BaseJob::JOB_AUDIT_STATUS_NAME);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 招聘状态
     * @return Response|\yii\web\Response
     */
    public function actionJobStatusList()
    {
        try {
            $list = ArrayHelper::obj2Arr(BaseJob::STATUS_NAME);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 根据条件获取职位列表、职位查询--b
     * @return Response|\yii\web\Response
     */
    public function actionSliceJobList()
    {
        $request = Yii::$app->request->get();
        $adminId = Yii::$app->user->id;
        // 关闭内存限制
        ini_set('memory_limit', '1024M');
        // 关闭超时限制
        set_time_limit(0);
        try {
            if (!$adminId) {
                throw new Exception('非法访问');
            }
            if ($request['export'] == 1) {
                //导出===去到下载中心
                $app = DownLoadTaskApplication::getInstance();
                $app->createAdmin($adminId, BaseAdminDownloadTask::TYPE_SLICE_JOB_LIST,
                    FormatConverter::convertHump($request));

                return $this->success('数据开始导出,成功下载后会在企业微信通知,请后续留意');
            }
            $list = Job::getSliceJobList(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 模糊查找职位列表
     * @return Response|\yii\web\Response
     */
    public function actionSearchJobInfoList()
    {
        $request = Yii::$app->request->get();
        try {
            $list = Job::searchJobInfoList($request);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取筛选投递类型配置项
     * @return Response|\yii\web\Response
     */
    public function actionDeliveryTypeSearch()
    {
        return $this->success(ArrayHelper::obj2Arr(BaseJob::DELIVERY_TYPE_NAME));
    }

    /**
     * 职位刷新
     * @return Response|\yii\web\Response
     */
    public function actionRefresh()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            (new RefreshService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位批量刷新
     * @return Response|\yii\web\Response
     */
    public function actionRefreshBatch()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $res = (new RefreshService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->setBatch()
                ->run();
            $transaction->commit();

            return $this->success([], $res, $res === true ? 200 : 201);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位再发布
     * @return Response|\yii\web\Response
     */
    public function actionRepublish()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $res = (new RepublishService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();
            $transaction->commit();

            return $this->success($res);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位批量再发布
     * @return Response|\yii\web\Response
     */
    public function actionRepublishBatch()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $res = (new RepublishService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->setBatch()
                ->run();
            $transaction->commit();

            return $this->success([], $res, $res === true ? 200 : 201);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位单条操作前置检查
     * @return Response|\yii\web\Response
     */
    public function actionOfflineCheck()
    {
        try {
            return $this->success((new OfflineService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->runCheck());
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位下线
     * @return Response|\yii\web\Response
     */
    public function actionOffline()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            (new OfflineService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();
            $transaction->commit();

            return $this->success('下线成功');
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位批量下线
     * @return Response|\yii\web\Response
     */
    public function actionOfflineBatch()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $res = (new OfflineService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->setBatch()
                ->run();
            $transaction->commit();

            return $this->success([], $res, $res === true ? 200 : 201);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位日志列表
     * @return Response|\yii\web\Response
     */
    public function actionGetHandleLogList()
    {
        $request = Yii::$app->request->get();
        try {
            return $this->success(JobHandleLog::getJobHandleList(FormatConverter::convertHump($request)));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 识别职位协同子账号
     */
    public function actionMemberAccountIdentify()
    {
        try {
            //获取单位ID
            $companyId = Yii::$app->request->get('companyId');
            if ($companyId <= 0) {
                throw new Exception('参数错误');
            }
            //获取填写的投递地址
            $apply_address = Yii::$app->request->get('applyAddress');
            if (empty($apply_address)) {
                throw new Exception('识别失败，请手动选择协同子账号');
            }
            //将投递地址转换为数组
            $apply_address_arr = explode(',', $apply_address);
            if (count($apply_address_arr) > 3) {
                throw new Exception('识别失败，邮箱地址不能超过3个');
            }
            //去重
            $apply_address_arr = array_unique($apply_address_arr);
            //循环验证邮箱地址
            $identify = [];
            foreach ($apply_address_arr as $item) {
                //对邮箱进行格式验证
                if (!ValidateHelper::isEmail($item)) {
                    //格式错误
                    throw new Exception('识别失败，邮箱格式错误');
                }
                $record_id = BaseCompanyMemberInfo::validateEmailMember($companyId, $item);
                if ($record_id) {
                    array_push($identify, $record_id);
                }
            }
            if (empty($identify)) {
                throw new Exception('邮箱地址不匹配，请手动选择协同子账号');
            } else {
                return $this->success([
                    'contact_identify' => $identify[0],
                    'identify'         => $identify,
                    'msg'              => '识别成功',
                ]);
            }
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位变更联系人及协同联系人
     * @return Response|\yii\web\Response
     */
    public function actionContact()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            (new ContactService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位批量变更联系人及协同联系人
     * @return Response|\yii\web\Response
     */
    public function actionContactBatch()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            (new ContactService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->setBatch()
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位修改编制
     * @return Response|\yii\web\Response
     */
    public function actionEstablishment()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            (new EstablishmentService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位批量修改编制
     * @return Response|\yii\web\Response
     */
    public function actionEstablishmentBatch()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            (new EstablishmentService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->setBatch()
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量修改职位的福利前置检校验
     */
    public function actionBatchEditWelfareCheck()
    {
        try {
            return $this->success((new WelfareService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->runCheck());
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量修改职位的福利待遇
     */
    public function actionBatchEditWelfare()
    {
        try {
            (new WelfareService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->setBatch()
                ->run();

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 非合作单位职位列表
     */
    public function actionUnCooperationIndex()
    {
        ini_set('memory_limit', '2048M');
        set_time_limit(0);
        try {
            $params = \Yii::$app->request->get();

            $service = new IndexService(IndexService::CHANNEL_UNCOOPERATION);

            return $this->success($service->setPlatform(CommonService::PLATFORM_ADMIN)
                ->unCooperationRun($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 非合作单位待审核职位列表（纯职位版本）
     */
    public function actionUnCooperationAuditIndex()
    {
        try {
            $params = \Yii::$app->request->get();

            $service = new IndexService(IndexService::CHANNEL_UNCOOPERATION_AUDIT);

            return $this->success($service->setPlatform(CommonService::PLATFORM_ADMIN)
                ->unCooperationAuditRun($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 合作单位职位列表
     */
    public function actionCooperationIndex()
    {
        ini_set('memory_limit', '2048M');
        set_time_limit(0);
        try {
            $params = \Yii::$app->request->get();

            $service = new IndexService(IndexService::CHANNEL_COOPERATION);

            if ($params['export'] == 1) {
                //导出===去到下载中心
                $app = DownLoadTaskApplication::getInstance();
                $app->createAdmin(Yii::$app->user->id, BaseAdminDownloadTask::TYPE_COOPERATION_JOB_LIST, $params);

                return $this->success('数据开始导出,成功下载后会在企业微信通知,请后续留意');
            }

            return $this->success($service->setPlatform(CommonService::PLATFORM_ADMIN)
                ->cooperationRun($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 合作单位待审核职位列表（纯职位版本）
     */
    public function actionCooperationAuditIndex()
    {
        try {
            $params = \Yii::$app->request->get();

            $service = new IndexService(IndexService::CHANNEL_COOPERATION_AUDIT);

            return $this->success($service->setPlatform(CommonService::PLATFORM_ADMIN)
                ->cooperationAuditRun($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位删除
     */
    public function actionDelete()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $params = \Yii::$app->request->post();
            if (empty($params['jobId'])) {
                throw new MessageException('参数错误');
            }

            (new DeleteService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->delete($params);
            $transaction->commit();

            return $this->success('删除成功');
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位删除
     */
    public function actionDeleteBatch()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $params = \Yii::$app->request->post();
            if (empty($params['jobId'])) {
                throw new \Exception('参数错误');
            }

            $res = (new DeleteService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->deleteBatch($params);
            $transaction->commit();

            return $this->success([], $res['failMessage'], $res['failMessage'] ? 201 : 200);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 限制学历
     */
    public function actionLimitEducation()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $params = \Yii::$app->request->post();
            if (empty($params['jobId']) || empty($params['isLimit'])) {
                throw new MessageException('参数错误');
            }

            (new LimitEducationService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->limitEducation($params);
            $transaction->commit();

            return $this->success('操作成功');
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 限制学历-批量
     */
    public function actionLimitEducationBatch()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $params = \Yii::$app->request->post();
            if (empty($params['jobId']) || empty($params['isLimit'])) {
                throw new \Exception('参数错误');
            }

            $res = (new LimitEducationService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->limitEducationBatch($params);
            $transaction->commit();

            return $this->success([], $res['failMessage'], $res['failMessage'] ? 201 : 200);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 限制投递附件
     */
    public function actionLimitAttachment()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $params = \Yii::$app->request->post();
            if (empty($params['jobId']) || empty($params['isLimit'])) {
                throw new MessageException('参数错误');
            }

            (new LimitAttachmentService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->limitAttachment($params);
            $transaction->commit();

            return $this->success('操作成功');
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 限制投递附件-批量
     */
    public function actionLimitAttachmentBatch()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $params = \Yii::$app->request->post();
            if (empty($params['jobId']) || empty($params['isLimit'])) {
                throw new \Exception('参数错误');
            }

            $res = (new LimitAttachmentService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->limitAttachmentBatch($params);
            $transaction->commit();

            return $this->success([], $res['failMessage'], $res['failMessage'] ? 201 : 200);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 隐藏前置检查
     * @return Response|\yii\web\Response
     */
    public function actionHideCheck()
    {
        try {
            return $this->success((new IsShowService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->runHideCheck());
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 显示隐藏
     */
    public function actionIsShow()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $params = \Yii::$app->request->post();
            if (empty($params['jobId']) || empty($params['isShow'])) {
                throw new MessageException('参数错误');
            }

            (new IsShowService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->isShow($params);
            $transaction->commit();

            return $this->success('操作成功');
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 限制投递附件-批量
     */
    public function actionIsShowBatch()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $params = \Yii::$app->request->post();
            if (empty($params['jobId']) || empty($params['isShow'])) {
                throw new \Exception('参数错误');
            }
            $res = (new IsShowService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->isShowBatch($params);
            $transaction->commit();

            return $this->success([], $res['failMessage'], $res['failMessage'] ? 201 : 200);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }


    /**
     * 业务面试邀约列表
     * @return Response|\yii\web\Response
     */
    public function actionJobInvitationList()
    {
        $request = Yii::$app->request->get();
        try {
            return $this->success(Job::jobInvitationList(FormatConverter::convertHump($request)));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}