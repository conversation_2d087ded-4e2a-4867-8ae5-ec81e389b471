<?php
/**
 * create user：伍彦川
 * create time：2025/4/2 10:43
 */
namespace admin\controllers;

use admin\models\SeoJobWiki;
use admin\models\UploadForm;
use Yii;
use yii\base\Exception;

class SeoJobWikiController extends BaseAdminController
{
    /**
     * 上传临时Excel文件
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionUploadBatchSaveExcel()
    {
        $model = new UploadForm();
        $model->setUploadType('file');

        try {
            $path = 'seo_job_wiki';
            $data = $model->temporaryUploadExcel('file', $path);

            return $this->success([
                'url'     => $data['path'],
                'fullUrl' => \Yii::$app->params['homeUrl'] . '/' . $data['path'],
            ]);
        } catch (\Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 职位批量导入
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionBatchSaveSeoJobWiki()
    {
        $request = Yii::$app->request->post();

        $filePath = $request['filePath'];
        if (!$filePath) {
            return $this->fail('参数缺失');
        }

        try {
            $data = (new SeoJobWiki())->addExcelData($filePath);

            return $this->success($data, $data['msg'] ?? '成功批量导入数据');
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 保存数据
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSaveSeoJobWiki()
    {
        try {
            $params = Yii::$app->request->post();

            return $this->success((new SeoJobWiki())->saveDetail($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除数据
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelSeoJobWiki()
    {
        try {
            $params = Yii::$app->request->post();
            (new SeoJobWiki())->deleteSeoJobWiki($params['ids'] ?? '');
            return $this->success('删除成功！');
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 列表数据
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetList()
    {
        try {
            $params = Yii::$app->request->get();

            //返回热词列表
            return $this->success((new SeoJobWiki())->getPageList($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 详情数据
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetDetail()
    {
        try {
            $params = Yii::$app->request->get();

            return $this->success((new SeoJobWiki())->getDetail($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

}