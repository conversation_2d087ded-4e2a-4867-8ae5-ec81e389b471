<?php

namespace admin\models;

use app\controller\Index;
use common\base\models\BaseBaiduZzPushLog;
use common\helpers\TimeHelper;
use common\libs\BaiduZZ;
use common\libs\IndexNow;
use Yii;

class Seo extends BaseBaiduZzPushLog
{
    public static function baiduZzPush($urls, $site, $wayType = self::WAY_TYPE_SCRIPT)
    {
        $token = Yii::$app->params['baiduZZ']['token'];

        $urlType           = self::getUrlTypeBySite($site);
        $model             = new self();
        $model->type       = self::TYPE_BAIDU;
        $model->send_url   = json_encode($urls);
        $model->send_count = count($urls);
        $model->way_type   = $wayType;
        $model->url_type   = $urlType;
        $model->date       = date('Y-m-d');
        $zz                = BaiduZZ::getInstance($token);
        if ($zz->push($urls, $site)) {
            $model->status = self::STATUS_SUCCESS;

            // 写日志
            $returnData = $zz->returnData;
            // 非同一个site下面的域名不能同时推送
            $notSameSite = $returnData['not_same_site'];
            // 非法的url不能推送
            $notValid = $returnData['not_valid'];
            // 剩余的数量
            $remain = $returnData['remain'];
            // 成功的数量
            $success = $returnData['success'];

            $model->return_log    = json_encode($returnData);
            $model->success_count = $success;
            $model->fail_url      = json_encode(array_merge($notSameSite, $notValid));
            $model->fail_count    = count($notSameSite) + count($notValid);
            $model->save();

            return $returnData;
        } else {
            $model->status = self::STATUS_FAIL;
            // 写日志
            $model->return_log = json_encode($zz->returnData);
            $model->save();

            // 这里给一个异常提示
            // throw new \Exception($zz->errorMsg);
        }
    }

    public static function indexNowPush($urls, $urlType = self::URL_TYPE_PC, $wayType = self::WAY_TYPE_SCRIPT)
    {
        $sendCount = count($urls);
        if ($sendCount == 0) {
            return true;
        }
        $model             = new self();
        $model->send_url   = json_encode($urls);
        $model->type       = self::TYPE_INDEXNOW;
        $model->fail_url   = 'null';
        $model->send_count = $sendCount;
        $model->way_type   = $wayType;
        $model->url_type   = $urlType;
        $model->date       = date('Y-m-d');
        $result            = IndexNow::getInstance()
            ->push($urls, $urlType);
        //        [
        //            'httpCode' => $httpCode,
        //            'result'   => $result,
        //        ];
        $model->return_log = json_encode($result);
        if ($result['httpCode'] == 200 || $result['httpCode'] == 202) {
            $model->status        = self::STATUS_SUCCESS;
            $model->success_count = $sendCount;
            $model->fail_count    = 0;
            $model->save();

            return $result;
        } else {
            $model->status        = self::STATUS_FAIL;
            $model->success_count = 0;
            $model->fail_count    = $sendCount;
            $model->save();
            // throw new \Exception('推送失败:' . json_encode($result));
        }
    }

    public static function setBaiduZzRemain()
    {
    }

    public static function getBaiduZzPushList($params)
    {
        $query = self::find()
            ->select('id,status,date,send_count,success_count,fail_count,add_time,way_type,url_type');

        if ($params['date'][0]) {
            $query->andWhere([
                '>=',
                'add_time',
                TimeHelper::dayToBeginTime($params['date'][0]),
            ]);
        }

        if ($params['date'][1]) {
            $query->andWhere([
                '<=',
                'add_time',
                TimeHelper::dayToEndTime($params['date'][1]),
            ]);
        }

        $query->andWhere([
            'type' => $params['type'] ?? self::TYPE_BAIDU,
        ]);
        $query->andFilterCompare('url_type', $params['urlType']);
        $query->andFilterCompare('way_type', $params['wayType']);

        $pageSize = $params['pageSize'] ?: \Yii::$app->params['defaultPageSize'];
        $count    = $query->count();
        $pages    = self::setPage($count, $params['page'], $pageSize);
        $list     = $query->limit($pages['limit'])
            ->offset($pages['offset'])
            ->orderBy('id desc')
            ->asArray()
            ->all();
        foreach ($list as &$item) {
            $item['statusTxt'] = self::STATUS_LIST[$item['status']];
            $item['wayTxt']    = self::WAY_TYPE_LIST[$item['way_type']];
            $item['urlTxt']    = self::URL_TYPE_LIST[$item['url_type']];
        }

        return [
            'list'  => $list,
            'pages' => [
                'limit' => $pages['limit'],
                'count' => (int)$count,
            ],
        ];
    }

    public static function getBaiduZzPushDetail($id)
    {
        $detail = self::find()
            ->select('send_url,send_count,success_count,fail_count,fail_url,return_log,way_type,url_type,status')
            ->where(['id' => $id])
            ->asArray()
            ->one();

        $detail['send_url']   = json_decode($detail['send_url'], true);
        $detail['fail_url']   = json_decode($detail['fail_url'], true);
        $detail['return_log'] = json_decode($detail['return_log'], true);
        $detail['statusTxt']  = self::STATUS_LIST[$detail['status']];
        $detail['wayTxt']     = self::WAY_TYPE_LIST[$detail['way_type']];
        $detail['urlTxt']     = self::URL_TYPE_LIST[$detail['url_type']];

        return $detail;
    }

    public static function getBaiduZzPushDaily($type)
    {
        // 拿昨天的数据和今天的数据
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        $today     = date('Y-m-d');

        $yesterdayH5Data = self::find()
            ->select('sum(success_count) as success_total,sum(fail_count) as fail_total')
            ->where([
                'date'     => $yesterday,
                'type'     => $type,
                'url_type' => self::URL_TYPE_H5,
            ])
            ->asArray()
            ->one();

        $yesterdayPcData = self::find()
            ->select('sum(success_count) as success_total,sum(fail_count) as fail_total')
            ->where([
                'date'     => $yesterday,
                'type'     => $type,
                'url_type' => self::URL_TYPE_PC,
            ])
            ->asArray()
            ->one();

        $todayPcData = self::find()
            ->select('sum(success_count) as success_total,sum(fail_count) as fail_total')
            ->where([
                'date'     => $today,
                'type'     => $type,
                'url_type' => self::URL_TYPE_PC,
            ])
            ->asArray()
            ->one();

        $todayH5Data = self::find()
            ->select('sum(success_count) as success_total,sum(fail_count) as fail_total')
            ->where([
                'date'     => $today,
                'type'     => $type,
                'url_type' => self::URL_TYPE_H5,
            ])
            ->asArray()
            ->one();

        return [
            'yesterday' => [
                'pc'    => [
                    'success_total' => $yesterdayPcData['success_total'] ?: '0',
                    'fail_total'    => $yesterdayPcData['fail_total'] ?: '0',
                    'total'         => $yesterdayPcData['success_total'] + $yesterdayPcData['fail_total'],
                ],
                'h5'    => [
                    'success_total' => $yesterdayH5Data['success_total'] ?: '0',
                    'fail_total'    => $yesterdayH5Data['fail_total'] ?: '0',
                    'total'         => $yesterdayH5Data['success_total'] + $yesterdayH5Data['fail_total'],
                ],
                'total' => [
                    'success_total' => $yesterdayPcData['success_total'] + $yesterdayH5Data['success_total'] ?: '0',
                    'fail_total'    => $yesterdayPcData['fail_total'] + $yesterdayH5Data['fail_total'] ?: '0',
                    'total'         => $yesterdayPcData['success_total'] + $yesterdayPcData['fail_total'] + $yesterdayH5Data['success_total'] + $yesterdayH5Data['fail_total'],
                ],
            ],
            'today'     => [
                'pc'    => [
                    'success_total' => $todayPcData['success_total'] ?: '0',
                    'fail_total'    => $todayPcData['fail_total'] ?: '0',
                    'total'         => $todayPcData['success_total'] + $todayPcData['fail_total'],
                ],
                'h5'    => [
                    'success_total' => $todayH5Data['success_total'] ?: '0',
                    'fail_total'    => $todayH5Data['fail_total'] ?: '0',
                    'total'         => $todayH5Data['success_total'] + $todayH5Data['fail_total'],
                ],
                'total' => [
                    'success_total' => $todayPcData['success_total'] + $todayH5Data['success_total'] ?: '0',
                    'fail_total'    => $todayPcData['fail_total'] + $todayH5Data['fail_total'] ?: '0',
                    'total'         => $todayPcData['success_total'] + $todayPcData['fail_total'] + $todayH5Data['success_total'] + $todayH5Data['fail_total'],
                ],
            ],
        ];
    }

    public static function getWayTypeList()
    {
        return self::WAY_TYPE_LIST;
    }

    public static function getUrlTypeList()
    {
        return self::URL_TYPE_LIST;
    }
}
