<?php

namespace admin\models;

use common\base\models\BaseAnnouncementHandleLog;
use yii\base\Exception;

class AnnouncementHandleLog extends BaseAnnouncementHandleLog
{
    /**
     * 公告审核处理历史
     * @param $params
     * @return array
     * @throws Exception
     */
    public static function announcementHandleLog($params): array
    {
        $select = [
            'add_time',
            'id',
            'handler_name',
            'handle_after',
        ];

        $query = self::find()
            ->where([
                'announcement_id' => $params['id'],
                'handle_type'     => self::HANDLE_TYPE_AUDIT,
            ])
            ->select($select);

        $orderBy = 'add_time desc';

        $count     = $query->count();
        $pageSize  = $params['pageSize'] ?: \Yii::$app->params['defaultPageSize'];
        $pages     = self::setPage($count, $params['page'], $pageSize);
        $handleLog = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        $auditStatusList = Announcement::STATUS_AUDIT_LIST;
        foreach ($handleLog as $k => $log) {
            $handleLog[$k]['auditStatus']      = 99;
            $handleLog[$k]['auditStatusTitle'] = "";
            $handleLog[$k]['opinion']          = "";
            $handleAfter                       = json_decode($log['handle_after'], true);
            $handleLog[$k]['handle_after']     = $handleAfter;
            if ($handleAfter['audit_status']) {
                $handleLog[$k]['auditStatus']      = intval($handleAfter['audit_status']);
                $handleLog[$k]['auditStatusTitle'] = $auditStatusList[$handleAfter['audit_status']];
            }
            if ($handleAfter['opinion']) {
                $handleLog[$k]['opinion'] = $handleAfter['opinion'];
            }
        }

        return [
            'list' => $handleLog,
            'page' => [
                'total' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$params['page'],
            ],
        ];
    }

    /**
     * 创建公告操作日志
     * @throws Exception
     */
    public static function createLogInfo($data)
    {
        $handleLog                  = new self();
        $handleLog->add_time        = $data['add_time'];
        $handleLog->announcement_id = $data['announcement_id'];
        $handleLog->handle_type     = $data['handle_type'];
        $handleLog->handler_type    = $data['handler_type'];
        $handleLog->handler_id      = $data['handler_id'];
        $handleLog->handler_name    = $data['handler_name'];
        $handleLog->handle_before   = $data['handle_before'];
        $handleLog->handle_after    = $data['handle_after'];
        $handleLog->ip              = $data['ip'];
        if (!$handleLog->save()) {
            throw new Exception($handleLog->getFirstErrorsMessage());
        }
    }
}
