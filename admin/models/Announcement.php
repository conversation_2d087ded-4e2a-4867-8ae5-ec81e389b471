<?php

namespace admin\models;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseArticleColumn;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyInterview;
use common\base\models\BaseCompanyMemberConfig;
use common\base\models\BaseDictionary;
use common\base\models\BaseFile;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHwActivity;
use common\base\models\BaseHwActivityAnnouncement;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseJobTemp;
use common\base\models\BaseMajor;
use common\base\models\BaseOffSiteJobApply;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAdditionalInfo;
use common\base\models\BaseResumeAttachment;
use common\base\models\BaseResumeDownloadLog;
use common\helpers\ArrayHelper;
use common\helpers\DebugHelper;
use common\helpers\FileHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\helpers\UUIDHelper;
use common\libs\Cache;
use common\libs\Editor;
use common\libs\Excel;
use Yii;
use yii\base\Exception;
use yii\db\Expression;

class Announcement extends BaseAnnouncement
{

    /**
     * 获取类型单位列表
     * @param $params
     * @return array
     * @throws \Exception
     */
    public static function searchCompanyList($params)
    {
        $select = [
            'id',
            'full_name',
            'type',
            'nature',
            'is_cooperation',
            'delivery_type',
        ];
        if ($params['type'] == Company::COOPERATIVE_UNIT_YES) {
            $query = Company::find()
                ->select($select)
                ->where(['is_cooperation' => Company::COOPERATIVE_UNIT_YES]);
        } else {
            $query = Company::find()
                ->select($select)
                ->where(['is_cooperation' => Company::COOPERATIVE_UNIT_NO]);
        }

        $query->andWhere([
            'status' => Company::STATUS_ACTIVE,
        ]);

        if (!empty($params['fullName'])) {
            $query->andFilterCompare('full_name', $params['fullName'], 'like');
        }

        $pageSize = \Yii::$app->params['defaultPageSize'];

        $list = $query->orderBy('add_time desc')
            ->limit($pageSize)
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['typeTxt']         = Dictionary::getCompanyTypeName($item['type']);
            $item['natureTxt']       = Dictionary::getCompanyNatureName($item['nature']);
            $item['deliveryTypeTxt'] = BaseCompany::DELIVERY_TYPE_NAME[$item['delivery_type']];
            if ($item['is_cooperation'] == BaseCompany::COOPERATIVE_UNIT_YES) {
                $item['sub_account_config'] = BaseCompanyMemberConfig::getInfo($item['id']);
            }
        }

        return [
            'list' => $list,
        ];
    }

    /**
     * 获取所有单位列表
     * @param $params
     * @return array
     */
    public static function getAllCompanyList($params)
    {
        $select = [
            'id',
            'full_name',
            'is_cooperation',
        ];

        $query = Company::find()
            ->select($select)
            ->where(['status' => Company::STATUS_ACTIVE]);

        if (!empty($params['fullName'])) {
            $query->andFilterCompare('full_name', $params['fullName'], 'like');
        }

        $pageSize = \Yii::$app->params['defaultPageSize'];

        $list = $query->orderBy('add_time desc')
            ->limit($pageSize)
            ->asArray()
            ->all();

        return [
            'list' => $list,
        ];
    }

    /**
     * 公告内容识别填充
     * @param $html
     * @return array
     * @throws Exception
     */
    public function identityEditor($html)
    {
        $editor  = new Editor($html);
        $rs      = $editor->identify();
        $content = $rs['content'];
        /**
         * 'mainColumn'        => '所属栏目',
         * 'subColumn'         => '副栏目',
         * 'company'           => '合作单位',
         * 'applyType'         => '应聘方式',
         * 'documentAttribute' => '文档属性',
         * 'periodDate'        => '截止日期',
         * 'memberType'        => '合作类型',
         * 'companyName'       => '单位名称',
         * 'companyType'       => '单位类型',
         * 'companyNature'     => '单位性质',
         * 'applyAddress'      => '投递地址',
         * 'tag'               => '特色标签',
         */
        $head = $rs['data'];
        // 首先把内容给识别出来
        if ($head['mainColumn']) {
            // 主栏目,去找主栏目的id
            $columnId = HomeColumn::findOneVal(['name' => $head['mainColumn']], 'id');
        }
        // 副栏目
        if ($head['subColumn']) {
            $subColumnArr    = explode('；', $head['subColumn']);
            $subColumnIds    = ArrayHelper::getColumn(HomeColumn::find()
                ->select('id,name')
                ->where([
                    'name' => $subColumnArr,
                ])
                ->asArray()
                ->all(), 'id');
            $subColumnIdsStr = implode(',', $subColumnIds);
        }

        // 文档属性
        if ($head['documentAttribute']) {
            $documentAttributeArr = explode('；', $head['documentAttribute']);
            $attributeDocument    = [];
            $abroadDocument       = [];
            $notAbroadDocument    = [];
            $attributeDocument    = [];
            foreach ($documentAttributeArr as $index => $item) {
                if ($key = array_search($item, ArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_LIST)) {
                    if (in_array($item, ArticleAttribute::ANNOUNCEMENT_ATTRIBUTE_ABROAD_LIST)) {
                        $abroadDocument[] = strval($key);
                    } else {
                        $notAbroadDocument[] = strval($key);
                    }
                }
            }
        }

        if ($head['periodDate']) {
            // 变为年月日
            $periodDate = str_replace([
                '年',
                '月',
            ], '-', $head['periodDate']);
            $periodDate = str_replace('日', '', $periodDate);
            $periodDate = date('Y-m-d', strtotime($periodDate));
        }

        // 然后和数据库查询,拿到合适的数据
        $isCooperation = Company::COOPERATIVE_UNIT_NO;

        // 单位名称
        if ($head['companyName']) {
            // 如果是合作单位,就只能去找名称,不能新建,如果是非合作,那么如果没有,就新建?
            // 反正最后需要出来一个单位的id

            // 先去找这个公司是否有
            $companyInfo = Company::find()
                ->andWhere(['full_name' => $head['companyName']])
                ->andWhere(['status' => BaseCompany::STATUS_ACTIVE])
                ->select('id,is_cooperation,type,nature,full_name,delivery_type')
                ->asArray()
                ->one();
            $companyId   = $companyInfo['id'];
            // 判断是否找得到就有合作单位
            if (!$companyId) {
                $companyTypeId = Dictionary::getCodeByName(Dictionary::TYPE_COMPANY, $head['companyType']);
                if (!$companyTypeId) {
                    $this->ThrowException('不存在"' . $head['companyType'] . '"的单位类型');
                }
                $companyNatureId = Dictionary::getCodeByName(Dictionary::TYPE_COMPANY_NATURE, $head['companyNature']);
                if (!$companyNatureId) {
                    $this->ThrowException('不存在"' . $head['companyNature'] . '"的单位类型');
                }
                // 没有id,得去创建
                $companyData = [
                    'type'     => $companyTypeId,
                    'fullName' => $head['companyName'],
                    'nature'   => $companyNatureId,
                ];
                $companyId   = CompanyInfoAuth::freeCreate($companyData);

                $companyInfo = Company::find()
                    ->andWhere(['full_name' => $head['companyName']])
                    ->andWhere(['status' => BaseCompany::STATUS_ACTIVE])
                    ->select('id,is_cooperation,type,nature,full_name,delivery_type')
                    ->asArray()
                    ->one();
            } else {
                $isCooperation = $companyInfo['is_cooperation'];
            }

            $companyInfo['natureTxt'] = Dictionary::getCompanyNatureName($companyInfo['nature']);
            $companyInfo['typeTxt']   = Dictionary::getCompanyTypeName($companyInfo['type']);
        }

        $deliveryWay        = '';
        $extraNotifyAddress = '';
        if ($head['applyType']) {
            // 应聘方式
            $applyTypeArr = explode('；', $head['applyType']);
            if (in_array('平台投递', $applyTypeArr)) {
                if ($companyInfo['is_cooperation'] == BaseCompany::COMPANY_SYN_CONTACT_YES) {
                    if (count($applyTypeArr) == 1) {
                        $deliveryWay        = BaseJob::DELIVERY_WAY_PLATFORM;
                        $extraNotifyAddress = $head['applyAddress'];
                        $applyAddress       = '';
                    } else {
                        $this->ThrowException('报名方式填写了平台投递则不应该填写其他');
                    }
                } else {
                    $this->ThrowException('报名方式填写错误');
                }
            } else {
                $applyTypeIds = [];
                foreach ($applyTypeArr as $item) {
                    if ($key = array_search($item, Dictionary::getSignUpList())) {
                        $applyTypeIds[]  = (string)$key;
                        $applyTypeIdsStr = implode(',', $applyTypeIds);
                    }
                }
                if (count($applyTypeIdsStr) <= 0) {
                    $this->ThrowException('报名方式填写错误');
                }
                if ($companyInfo['is_cooperation'] == BaseCompany::COMPANY_SYN_CONTACT_YES) {
                    $deliveryWay = BaseJob::DELIVERY_WAY_EMAIL_LINK;
                }
                if ($head['applyAddress']) {
                    // 应聘方式
                    $applyAddress = $head['applyAddress'];
                }
            }
        }

        // 特色标签
        if ($head['tag']) {
            // 应聘方式
            $tagArr         = explode('；', $head['tag']);
            $featuresTagIds = [];
            foreach ($tagArr as $item) {
                if ($key = array_search($item, BaseArticle::ATTRIBUTE_TAG_LIST)) {
                    $featuresTagIds[]  = (string)$key;
                    $featuresTagIdsStr = implode(',', $featuresTagIds);
                }
            }
        }

        if ($head['attachmentNotice'] == 1) {
            $isAttachmentNotice = '1';
        } else {
            $isAttachmentNotice = '2';
        }
        if ($head['addressHideStatus'] == 1) {
            $addressHideStatus = self::ADDRESS_HIDE_STATUS_YES;
        } else {
            $addressHideStatus = self::ADDRESS_HIDE_STATUS_NO;
        }

        // 然后和数据库查询,拿到合适的数据

        return [
            'homeColumnId'           => $columnId,
            'homeSubColumnIds'       => $subColumnIdsStr ?: '',
            //'comboAttribute'         => $attributeDocument ?: [],
            'comboAttribute'         => $notAbroadDocument ?: [],
            'overseasAttribute'      => $abroadDocument ?: [],
            'periodDate'             => $periodDate ?: '',
            'isCooperation'          => (string)$isCooperation,
            'companyId'              => $companyId,
            'companyDeliveryType'    => $companyInfo['delivery_type'],
            'companyDeliveryTypeTxt' => BaseCompany::DELIVERY_TYPE_NAME[$companyInfo['delivery_type']],
            'companyTxt'             => $companyInfo['full_name'],
            'companyNatureTxt'       => $companyInfo['natureTxt'],
            'companyTypeTxt'         => $companyInfo['typeTxt'],
            'deliveryWay'            => (string)$deliveryWay,
            'extraNotifyAddress'     => $extraNotifyAddress,
            'applyAddress'           => $applyAddress,
            'applyType'              => $applyTypeIdsStr ?: '',
            'tagIds'                 => $featuresTagIdsStr ?: '',
            'isAttachmentNotice'     => $isAttachmentNotice,
            'content'                => $content,
            'title'                  => $head['title'],
            'addressHideStatus'      => $addressHideStatus,
        ];
    }

    /**
     * 获取公告列表
     * @param $params
     * @return array
     * @throws \yii\base\Exception
     */
    public static function getList($params)
    {
        self::openDb2();
        $adminId = $params['admin'];
        ini_set('memory_limit', '512M');
        // 公告列表
        $list   = self::validateSearchKeywords($params);
        $allJob = 0;
        foreach ($list['list'] as &$item) {
            // 分割获取栏目名称
            $subColumnArr  = explode(',', $item['home_sub_column_ids']);
            $subColumnData = HomeColumn::find()
                ->select('name')
                ->where(['id' => $subColumnArr])
                ->asArray()
                ->all();
            // 组合栏目显示
            foreach ($subColumnData as $k => $v) {
                $subColumnNames[$k]    = $v['name'];
                $item['subColumnInfo'] = implode(';', $subColumnNames);
            }

            //文档属性
            $item['attribute'] = BaseArticleAttribute::getArticleAttribute($item['article_id']);
            $attribute         = explode(',', $item['attribute']);
            if ($attribute > 1) {
                $tip = [];
                foreach ($attribute as $val) {
                    $tip[] = ArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_LIST[$val];
                }
                $item['tip'] = implode(' ', $tip);
            } else {
                $item['tip'] = ArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_LIST[$item['attribute']];
            }

            // 获取最新审核拒绝操作记录
            $refuseAuditHandleLog = AnnouncementHandleLog::find()
                ->select('handle_after')
                ->where([
                    'announcement_id' => $item['aid'],
                    'handle_type'     => AnnouncementHandleLog::HANDLE_TYPE_AUDIT,
                ])
                ->orderBy('id desc')
                ->asArray()
                ->one();
            $opinion              = json_decode($refuseAuditHandleLog['handle_after'], true);

            $item['opinion']          = $opinion ?: '';
            $item['auditStatusTxt']   = self::STATUS_AUDIT_LIST[$item['audit_status']] ?: '';
            $item['recruitStatusTxt'] = Article::STATUS_LIST[$item['status']] ?: '';
            $item['isShowTxt']        = Article::IS_SHOW_LIST[$item['is_show']] ?: '';
            $item['columnName']       = HomeColumn::findOneVal(['id' => $item['home_column_id']], 'name');
            $item['announcementUid']  = UUIDHelper::encrypt(UUIDHelper::TYPE_ANNOUNCEMENT, $item['aid']);
            $item['fullName']         = $item['full_name'] . '(' . UUIDHelper::encrypt(UUIDHelper::TYPE_COMPANY,
                    $item['company_id']) . ')';

            $item['isMiniappTxt'] = self::IS_MINIAPP_LIST[$item['is_miniapp']];

            // 统计所有职位信息
            $allJob += intval($item['jobNum']);

            // 获取公告下的职位
            $jobSelect = [
                'id',
                'audit_status',
                'first_release_time',
                'create_type',
            ];
            $jobList   = BaseJob::getAnnouncementJobList($item['aid'], $jobSelect);
            foreach ($jobList as $v) {
                $isCooperation = Company::findOneVal(['id' => $item['company_id']], 'is_cooperation');
                if ($isCooperation == Company::COOPERATIVE_UNIT_YES) {
                    //检查是否有投递数据
                    $jobApply = JobApply::findOne(['job_id' => $v['id']]);
                } else {
                    $jobApply = OffSiteJobApply::findOne(['job_id' => $v['id']]);
                }
                // 有投递数据or待审核状态or合作单位&有审核通过历史or单位发布&没有审核通过历史&审核拒绝职位不可删除
                if ($jobApply) {
                    $item['canDel'] = false;
                } elseif (!empty($item['first_release_time']) && $item['first_release_time'] != TimeHelper::ZERO_TIME && $isCooperation == Company::COOPERATIVE_UNIT_YES) {
                    $item['canDel'] = false;
                } elseif ($item['audit_status'] == self::STATUS_AUDIT_AWAIT) {
                    $item['canDel'] = false;
                } elseif (!empty($item['first_release_time']) && $item['first_release_time'] == TimeHelper::ZERO_TIME && $item['create_type'] == self::CREATE_TYPE_COMPANY && $item['audit_status'] == self::STATUS_AUDIT_REFUSE) {
                    $item['canDel'] = false;
                } else {
                    $item['canDel'] = true;
                }
            }

            //职位招聘人数统计
            $item['amount'] = Job::getAnnouncementJobListAmount($item['aid'], $item['status']);

            if ($item['create_type'] == self::CREATE_TYPE_ADMIN) {
                $item['creatorName'] = Admin::findOneVal(['id' => $item['creator_id']], 'name');
            } else {
                $item['creatorName'] = Company::findOneVal(['member_id' => $item['creator_id']], 'full_name');
            }
            if ($item['release_time'] == TimeHelper::ZERO_TIME) {
                $item['releaseTime'] = '-';
            }
            if ($item['offline_time'] == TimeHelper::ZERO_TIME) {
                $item['offlineTime'] = '-';
            }
            if ($item['refresh_time'] == TimeHelper::ZERO_TIME) {
                $item['refreshTime'] = '-';
            }
            if ($item['real_refresh_time'] == TimeHelper::ZERO_TIME) {
                $item['real_refresh_time'] = '-';
            }
            if ($item['first_release_time'] == TimeHelper::ZERO_TIME) {
                $item['first_release_time'] = '';
            }

            // 学科
            $item['majorTxt'] = BaseAnnouncement::getAllMajorName($item['aid']);
        }

        // 招聘人数
        if ($params['sortRecruitCount'] == self::ORDER_BY_DESC) {
            $list['list'] = ArrayHelper::arraySorts($list['list'], 'amount', 'desc');
        } else {
            if ($params['sortRecruitCount'] == self::ORDER_BY_ASC) {
                $list['list'] = ArrayHelper::arraySorts($list['list'], 'amount', 'asc');
            }
        }

        // 初次发布时间

        if ($params['export']) {
            $data    = [];
            $headers = [
                '公告ID',
                '公告标题',
                '所属单位',
                '招聘人数',
                '招聘职位',
                '在线职位',
                '投递次数',
                '面试邀约',
                '招聘状态',
                '发布时间',
                '刷新时间',
                '点击量',
                '审核状态',
                '发布模式',
                '创建人',
                '初次发布时间',
                '属性',
                '硕士投递次数',
                '博士投递次数',
                '职位总阅读量',
            ];

            /**
             * Array
             * (
             * [aid] => 21798
             * [title] => 海聚英才，携手同行——中国科大诚邀海内外英才加盟
             * [company_id] => 37
             * [audit_status] => 1
             * [article_id] => 22480
             * [creator_id] => 122
             * [create_type] => 2
             * [period_date] => 2023-06-23 16:44:35
             * [relation_company_ids] =>
             * [add_time] => 2022-06-22 16:08:25
             * [offline_time] => 2022-06-23 16:44:18
             * [home_sort] => 0
             * [is_show] => 1
             * [update_time] => 2022-06-23 16:44:49
             * [refresh_time] => 2022-06-23 16:44:49
             * [arid] => 22480
             * [status] => 1
             * [home_column_id] => 1
             * [home_sub_column_ids] =>
             * [sort] => 0
             * [click] => 10
             * [release_time] => 2022-06-23 16:44:35
             * [real_refresh_time] => 2022-06-23 16:44:49
             * [first_release_time] => 2022-06-22 16:08:42
             * [cid] => 37
             * [full_name] => 中国科学技术大学
             * [is_cooperation] => 1
             * )
             */

            foreach ($list['list'] as $val) {
                // 拿公告属性

                $data[] = [
                    UUIDHelper::encrypt(UUIDHelper::TYPE_ANNOUNCEMENT, $val['aid']),
                    $val['title'],
                    $val['full_name'],
                    $val['amount'],
                    $val['job_num'],
                    $val['online_count'],
                    $val['job_apply_num'],
                    $val['job_interview_num'],
                    self::STATUS_LIST[$val['status']],
                    $val['refresh_time'],
                    $val['real_refresh_time'],
                    $val['click'],
                    self::STATUS_AUDIT_LIST[$val['audit_status']],
                    $val['create_type'] == 1 ? '站内发布' : '站外发布',
                    $val['creatorName'],
                    $val['first_release_time'],
                    BaseArticleAttribute::getArticleAttributeName($val['arid']),
                    $val['master_num'],
                    $val['doctor_num'],
                    $val['job_click_num'],
                ];
            }

            $excel    = new Excel();
            $fileName = $excel->export($data, $headers);

            return [
                'excelUrl' => $fileName,
            ];
        } else {
            $key      = Cache::ADMIN_JOB_LIST_STATISTICS_KEY;
            $keyValue = Cache::get($key) ?: '';

            if ($keyValue) {
                $amount = json_decode($keyValue, true);
            } else {
                //统计合作单位公告下的职位信息（投递总数、面试总数、点击总数）
                $jobListStatistics = BaseJob::getAnnouncementJobListStatistics();
                $amount            = [
                    'apply'       => (int)$jobListStatistics['dataCount']['jobApplyNum'] ?: 0,
                    'interview'   => (int)$jobListStatistics['dataCount']['jobInterviewNum'] ?: 0,
                    'click'       => (int)$jobListStatistics['dataCount']['allClick'] ?: 0,
                    'allJob'      => (int)$jobListStatistics['allJobCount'],
                    'platformNum' => (int)$jobListStatistics['apply']['platformNum'] ?: 0,
                    'emailNum'    => (int)$jobListStatistics['apply']['emailNum'] ?: 0,
                    'linkNum'     => (int)$jobListStatistics['apply']['linkNum'] ?: 0,
                ];

                // 1小时更新一次就差不多了
                Cache::set($key, json_encode($amount), 3600);
            }
            $list['amount'] = $amount;

            return $list;
        }
        self::closeDb2();
    }

    // 简单版本公告列表
    // 标题
    // 单位
    // 排序?
    // 招聘状态
    // 发布时间
    // 操作
    // 创建人
    public static function getsSimpleList($params)
    {
        // 内存开到2g
        ini_set('memory_limit', '2048M');
        $select      = [
            'a.id as aid',
            'a.title',
            'a.company_id',
            'a.audit_status',
            'a.article_id',
            'a.creator_id',
            'a.create_type',
            'a.period_date',
            'a.relation_company_ids',
            'a.add_time',
            'a.offline_time',
            'a.home_sort',
            'a.is_miniapp',
            'ar.is_show',
            'ar.update_time',
            'ar.refresh_time',
            'ar.id as arid',
            'ar.status',
            'ar.home_column_id',
            'ar.home_sub_column_ids',
            'ar.sort',
            'ar.click',
            'ar.release_time',
            'ar.real_refresh_time',
            'ar.first_release_time',
            'c.id as cid',
            'c.full_name',
            'c.is_cooperation',
        ];
        $auditStatus = [
            self::STATUS_AUDIT_PASS,
            self::STATUS_AUDIT_REFUSE,
            self::STATUS_AUDIT_STAGING,
        ];

        $query = self::find()
            ->alias('a')
            ->innerJoin(['ar' => Article::tableName()], 'ar.id = a.article_id')
            ->innerJoin(['c' => Company::tableName()], 'c.id = a.company_id')
            ->where([
                'ar.is_delete' => self::STATUS_DELETE,
            ])
            ->andWhere(['a.audit_status' => $auditStatus]);

        // 合作单位公展示
        if ($params['isCooperation'] == Company::COOPERATIVE_UNIT_YES) {
            $query->orWhere([
                'and',
                [
                    '<>',
                    'ar.status',
                    BaseArticle::STATUS_STAGING,
                ],
                [
                    '=',
                    'a.create_type',
                    self::CREATE_TYPE_COMPANY,
                ],
            ]);
        }

        // 发布人名称
        if ($params['creator']) {
            // 发布人
            $createId = Admin::findOneVal([
                'name' => $params['creator'],
            ], 'id');
            if (!$createId) {
                $createId = Company::findOneVal([
                    'full_name' => $params['creator'],
                ], 'member_id');
            }

            $query->andFilterCompare('a.creator_id', $createId);
        }

        // 是否合作单位的公告
        $query->andFilterCompare('c.is_cooperation', $params['isCooperation']);
        // 审核状态
        $query->andFilterCompare('a.audit_status', $params['auditStatus']);
        // companyName like
        $query->andFilterWhere([
            'like',
            'c.full_name',
            $params['companyName'],
        ]);
        $query->andFilterCompare('a.audit_status', $params['auditStatus']);
        $query->andFilterCompare('ar.status', $params['status']);

        if ($params['releaseTimeStart']) {
            $query->andWhere([
                '>=',
                'ar.release_time',
                TimeHelper::dayToBeginTime($params['releaseTimeStart']),
            ]);
        }
        if ($params['releaseTimeEnd']) {
            $query->andWhere([
                '<=',
                'ar.release_time',
                TimeHelper::dayToEndTime($params['releaseTimeEnd']),
            ]);
        }

        // 发布时间
        if ($params['refreshTimeStart']) {
            $query->andWhere([
                '>=',
                'ar.refresh_time',
                TimeHelper::dayToBeginTime($params['refreshTimeStart']),
            ]);
        }
        if ($params['refreshTimeEnd']) {
            $query->andWhere([
                '<=',
                'ar.refresh_time',
                TimeHelper::dayToEndTime($params['refreshTimeEnd']),
            ]);
        }

        // 首次发布时间
        if ($params['firstReleaseTimeStart']) {
            $query->andWhere([
                '>=',
                'ar.first_release_time',
                TimeHelper::dayToBeginTime($params['firstReleaseTimeStart']),
            ]);
        }
        if ($params['firstReleaseTimeEnd']) {
            $query->andWhere([
                '<=',
                'ar.first_release_time',
                TimeHelper::dayToEndTime($params['firstReleaseTimeEnd']),
            ]);
        }

        // 刷新时间
        if ($params['realRefreshTimeStart']) {
            $query->andWhere([
                '>=',
                'ar.real_refresh_time',
                TimeHelper::dayToBeginTime($params['realRefreshTimeStart']),
            ]);
        }
        if ($params['realRefreshTimeEnd']) {
            $query->andWhere([
                '<=',
                'ar.real_refresh_time',
                TimeHelper::dayToEndTime($params['realRefreshTimeStart']),
            ]);
        }

        // 更新时间
        if ($params['updateTimeStart']) {
            $query->andWhere([
                '>=',
                'a.update_time',
                TimeHelper::dayToBeginTime($params['updateTimeStart']),
            ]);
        }
        if ($params['updateTimeEnd']) {
            $query->andWhere([
                '<=',
                'a.update_time',
                TimeHelper::dayToEndTime($params['updateTimeEnd']),
            ]);
        }

        if ($params['notOverseasAttribute'] || $params['overseasAttribute']) {
            // 合并属性

            $query->innerJoin(['e' => ArticleAttribute::tableName()], 'e.article_id = ar.id');
            if ($params['notOverseasAttribute']) {
                $query->andFilterCompare('e.type', $params['notOverseasAttribute']);
            }
            if ($params['overseasAttribute']) {
                $query->innerJoin(['h' => ArticleAttribute::tableName()], 'h.article_id = ar.id');
                $query->andFilterCompare('h.type', $params['overseasAttribute']);
            }
        }

        $orderBy = 'a.add_time desc';
        // 平台投递
        if ($params['sortPlatformNum'] == self::ORDER_BY_DESC) {
            $orderBy = 'platform_num desc';
        } elseif ($params['sortPlatformNum'] == self::ORDER_BY_ASC) {
            $orderBy = 'platform_num asc';
        }
        // 邮件投递
        if ($params['sortEmailNum'] == self::ORDER_BY_DESC) {
            $orderBy = 'email_num desc';
        } elseif ($params['sortEmailNum'] == self::ORDER_BY_ASC) {
            $orderBy = 'email_num asc';
        }
        // 网址投递
        if ($params['sortLinkNum'] == self::ORDER_BY_DESC) {
            $orderBy = 'link_num desc';
        } elseif ($params['sortLinkNum'] == self::ORDER_BY_ASC) {
            $orderBy = 'link_num asc';
        }
        // 排序数
        if ($params['sortNumberCount'] == self::ORDER_BY_DESC) {
            $orderBy = 'ar.sort desc';
        } elseif ($params['sortNumberCount'] == self::ORDER_BY_ASC) {
            $orderBy = 'ar.sort asc';
        }
        // 点击量
        if ($params['sortClickCount'] == self::ORDER_BY_DESC) {
            $orderBy = 'ar.click desc';
        } elseif ($params['sortClickCount'] == self::ORDER_BY_ASC) {
            $orderBy = 'ar.click asc';
        }
        // 最近一次发布时间
        if ($params['sortReleaseTime'] == self::ORDER_BY_DESC) {
            $orderBy = 'ar.release_time desc';
        } elseif ($params['sortReleaseTime'] == self::ORDER_BY_ASC) {
            $orderBy = 'ar.release_time asc';
        }

        // 发布时间
        if ($params['sortRealRefreshTime'] == self::ORDER_BY_DESC) {
            $orderBy = 'ar.real_refresh_time desc';
        } elseif ($params['sortRealRefreshTime'] == self::ORDER_BY_ASC) {
            $orderBy = 'ar.real_refresh_time asc';
        }
        // 发布时间
        if ($params['sortRefreshTime'] == self::ORDER_BY_DESC) {
            $orderBy = 'ar.refresh_time desc';
        } elseif ($params['sortRefreshTime'] == self::ORDER_BY_ASC) {
            $orderBy = 'ar.refresh_time asc';
        }

        // 发布时间
        if ($params['sortFirstReleaseTime'] == self::ORDER_BY_DESC) {
            $orderBy = 'ar.first_release_time desc';
        } elseif ($params['sortFirstReleaseTime'] == self::ORDER_BY_ASC) {
            $orderBy = 'ar.first_release_time asc';
        }

        // 下线时间
        if ($params['sortOfflineTime'] == self::ORDER_BY_DESC) {
            $orderBy = 'a.period_date desc';
        } elseif ($params['sortOfflineTime'] == self::ORDER_BY_ASC) {
            $orderBy = 'a.period_date asc';
        }

        if ($params['announcementTitleNum']) {
            // 这里因为查询比较慢,所以考虑拆分出来处理
            // 首先找公告的标题
            $announcementIds = self::find()
                ->select(['id'])
                ->where([
                    'like',
                    'title',
                    $params['announcementTitleNum'],
                ])
                ->asArray()
                ->column();

            // 再找公告的编号
            if (strlen($params['announcementTitleNum']) == 8) {
                $number = UUIDHelper::decryption($params['announcementTitleNum']);
                if ($number) {
                    $announcementIds[] = $number;
                }
            }
            $query->andWhere([
                'in',
                'a.id',
                $announcementIds,
            ]);
        }

        // 添加编制相关
        // if ($params['establishmentType']) {
        //     $query->innerJoin(['j' => BaseJob::tableName()], 'j.announcement_id = a.id');
        //     // 这里会有几种可能性,-1和其他值(-1的情况下是没有选编制的类型,其他值是选了编制类型里面某个并且find_in_set
        //     $establishmentType = explode(',', $params['establishmentType']);
        //     $orWhere           = [
        //         'or',
        //     ];
        //     foreach ($establishmentType as $itemKey => $establishmentItem) {
        //         if ($establishmentItem == -1) {
        //             $orWhere[] = ['j.is_establishment' => BaseJob::IS_ESTABLISHMENT_NO];
        //         } else {
        //             $establishmentItemKey = 'establishmentItem' . $itemKey;
        //             $orWhere[]            = new Expression("FIND_IN_SET(:" . $establishmentItemKey . ", j.establishment_type)",
        //                 [$establishmentItemKey => $establishmentItem]);
        //         }
        //     }
        //     $query->andWhere($orWhere);
        // }

        // 审核状态
        $query->andFilterCompare('a.is_miniapp', $params['isMiniapp']);

        $count    = $query->count();
        $pageSize = $params['pageSize'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $params['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->select($select)
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        DebugHelper::writeLog($query->createCommand()
            ->getRawSql(), 'announcementList');

        foreach ($list as &$item) {
            // 公告编号
            $item['announcementUid'] = UUIDHelper::encrypt(UUIDHelper::TYPE_ANNOUNCEMENT, $item['aid']);
            // 操作人
            $item['auditStatusTxt']   = self::STATUS_AUDIT_LIST[$item['audit_status']] ?: '';
            $item['recruitStatusTxt'] = Article::STATUS_LIST[$item['status']] ?: '';
            $item['announcementUid']  = UUIDHelper::encrypt(UUIDHelper::TYPE_ANNOUNCEMENT, $item['aid']);
            $item['fullName']         = $item['full_name'] . '(' . UUIDHelper::encrypt(UUIDHelper::TYPE_COMPANY,
                    $item['company_id']) . ')';
            if ($item['create_type'] == self::CREATE_TYPE_ADMIN) {
                $item['creatorName'] = Admin::findOneVal(['id' => $item['creator_id']], 'name');
            } else {
                $item['creatorName'] = Company::findOneVal(['member_id' => $item['creator_id']], 'full_name');
            }
            $handle_log_info = BaseAnnouncementHandleLog::find()
                ->where(['announcement_id' => $item['aid']])
                ->orderBy('add_time desc')
                ->one();
            if ($handle_log_info) {
                $item['creatorName']    = $handle_log_info['handler_name'];
                $item['applyAuditTime'] = $handle_log_info['add_time'];
            }

            $item['attribute'] = BaseArticleAttribute::getArticleAttribute($item['article_id']);
            // 文档属性
            $attribute = explode(',', $item['attribute']);
            if ($attribute > 1) {
                $tip = [];
                foreach ($attribute as $val) {
                    $tip[] = ArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_LIST[$val];
                }
                $item['tip'] = implode(' ', $tip);
            } else {
                $item['tip'] = ArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_LIST[$item['attribute']];
            }

            $item['opinion']          = $item['opinion'] ?: '';
            $item['auditStatusTxt']   = self::STATUS_AUDIT_LIST[$item['audit_status']] ?: '';
            $item['recruitStatusTxt'] = Article::STATUS_LIST[$item['status']] ?: '';
            $item['isShowTxt']        = Article::IS_SHOW_LIST[$item['is_show']] ?: '';
            $item['columnName']       = HomeColumn::findOneVal(['id' => $item['home_column_id']], 'name');
            $item['announcementUid']  = UUIDHelper::encrypt(UUIDHelper::TYPE_ANNOUNCEMENT, $item['aid']);
            $item['fullName']         = $item['full_name'] . '(' . UUIDHelper::encrypt(UUIDHelper::TYPE_COMPANY,
                    $item['company_id']) . ')';

            $item['isMiniappTxt'] = self::IS_MINIAPP_LIST[$item['is_miniapp']];
        }

        return [
            'list'  => $list,
            'pages' => [
                'size'  => (int)$pageSize,
                'total' => (int)$count,
            ],
        ];
    }

    /**
     * 校验搜索字段并返回查询列表
     * @param $params
     * @return array
     * @throws Exception
     */
    public static function validateSearchKeywords($params): array
    {
        $select      = [
            'a.id as aid',
            'a.title',
            'a.company_id',
            'a.audit_status',
            'a.article_id',
            'a.creator_id',
            'a.create_type',
            'a.period_date',
            'a.relation_company_ids',
            'a.add_time',
            'a.offline_time',
            'a.home_sort',
            'ar.is_show',
            'ar.update_time',
            'ar.refresh_time',
            'ar.id as arid',
            'ar.status',
            'ar.home_column_id',
            'ar.home_sub_column_ids',
            'ar.sort',
            'ar.click',
            'ar.release_time',
            'ar.real_refresh_time',
            'ar.first_release_time',
            'a.is_miniapp',
            'c.id as cid',
            'c.full_name',
            'c.is_cooperation',
            'sum(if(jar.delivery_way=1,1,0)) as platform_num',
            'sum(if(jar.delivery_way=2,1,0)) as email_num',
            'sum(if(jar.delivery_way=3,1,0)) as link_num',
            // 硕士投递次数
            'sum(if(r.top_education_code=3,1,0)) as master_num',
            // 博士投递次数
            'sum(if(r.top_education_code=4,1,0)) as doctor_num',
            // 职位阅读量
            'ifnull(tmp.job_click_num, 0) as job_click_num',
            // 招聘职位
            'ifnull(tmp.job_num, 0) as job_num',
            // 在线职位
            'ifnull(tmp.online_count, 0) as online_count',
            // 下线职位
            'ifnull(tmp.offline_count, 0) as offline_count',
            // 站内投递次数
            'ifnull(tmp2.job_apply_num, 0) as job_apply_num',
            // 邀约次数
            'ifnull(tmp2.job_interview_num, 0) as job_interview_num',
            // 站外投递次数
            'ifnull(tmp3.job_off_apply_num, 0) as job_off_apply_num',
        ];
        $auditStatus = [
            self::STATUS_AUDIT_PASS,
            self::STATUS_AUDIT_REFUSE,
            self::STATUS_AUDIT_STAGING,
        ];

        // 职位状态
        $jobStatusOnline  = BaseJob::STATUS_ONLINE;
        $jobStatusOffline = BaseJob::STATUS_OFFLINE;
        $jobIsShowYes     = BaseJob::IS_SHOW_YES;

        // 公告标题or编号检索
        if ($params['announcementTitleNum']) {
            // 这里因为查询比较慢,所以考虑拆分出来处理
            // 首先找公告的标题
            $announcementIds = self::find()
                ->select(['id'])
                ->where([
                    'like',
                    'title',
                    $params['announcementTitleNum'],
                ])
                ->asArray()
                ->column();

            // 再找公告的编号
            if (strlen($params['announcementTitleNum']) == 8) {
                $number = UUIDHelper::decryption($params['announcementTitleNum']);
                if ($number) {
                    $announcementIds[] = $number;
                }
            }
        }

        // 临时表（职位信息）
        $tmpQuery = self::find()
            ->select([
                'a.id',
                'sum(j.click) as job_click_num',
                "sum(if(j.is_show = {$jobIsShowYes} and (j.status = {$jobStatusOnline} or j.status = {$jobStatusOffline}), 1, 0)) as job_num",
                "sum(if(j.status = {$jobStatusOnline}, 1, 0)) as online_count",
                "sum(if(j.status = {$jobStatusOffline}, 1, 0)) as offline_count",
            ])
            ->alias('a')
            ->andFilterCompare('a.audit_status', $auditStatus, 'in')
            ->leftJoin(['j' => BaseJob::tableName()], 'a.id = j.announcement_id')
            ->groupBy('a.id');

        // 公告状态
        $isArticle = BaseJob::IS_ARTICLE_YES;

        // 临时表（站内投递/邀约信息）
        $tmpQuery2 = self::find()
            ->select([
                'a.id',
                'count(ja.job_id) as job_apply_num',
                'count(if(ja.is_invitation > 0, ja.job_id, null)) as job_interview_num',
            ])
            ->alias('a')
            ->where(['j.is_article' => $isArticle])
            ->andWhere([
                'in',
                'j.status',
                [
                    $jobStatusOnline,
                    $jobStatusOffline,
                ],
            ])
            ->leftJoin(['j' => BaseJob::tableName()], 'a.id = j.announcement_id')
            ->leftJoin(['ja' => BaseJobApply::tableName()], 'ja.job_id = j.id')
            ->groupBy('a.id');

        // 临时表（站外投递）
        $tmpQuery3 = self::find()
            ->select([
                'a.id',
                'count(osja.job_id) as job_off_apply_num',
            ])
            ->alias('a')
            ->where(['j.is_article' => $isArticle])
            ->andWhere([
                'in',
                'j.status',
                [
                    $jobStatusOnline,
                    $jobStatusOffline,
                ],
            ])
            ->leftJoin(['j' => BaseJob::tableName()], 'a.id = j.announcement_id')
            ->leftJoin(['osja' => OffSiteJobApply::tableName()], 'osja.job_id = j.id')
            ->groupBy('a.id');

        $query = self::find()
            ->alias('a')
            ->leftJoin(['ar' => Article::tableName()], 'ar.id = a.article_id')
            ->leftJoin(['c' => Company::tableName()], 'c.id = a.company_id')
            ->where([
                'ar.is_delete' => self::STATUS_DELETE,
            ])
            ->leftJoin(['jar' => BaseJobApplyRecord::tableName()], 'a.id = jar.announcement_id')
            ->leftJoin(['r' => BaseResume::tableName()], 'r.id = jar.resume_id')
            ->leftJoin(['tmp' => $tmpQuery], 'tmp.id = a.id')
            ->leftJoin(['tmp2' => $tmpQuery2], 'tmp2.id = a.id')
            ->leftJoin(['tmp3' => $tmpQuery3], 'tmp3.id = a.id')
            ->andFilterCompare('a.audit_status', $auditStatus, 'in')
            ->groupBy('a.id');
        // 合作单位公展示
        if ($params['isCooperation'] == Company::COOPERATIVE_UNIT_YES) {
            $query->orWhere([
                'and',
                [
                    '<>',
                    'ar.status',
                    BaseArticle::STATUS_STAGING,
                ],
                [
                    '=',
                    'a.create_type',
                    self::CREATE_TYPE_COMPANY,
                ],
            ]);
        }

        // 发布人名称
        if ($params['creator']) {
            // 发布人
            $createId = Admin::findOneVal([
                'name' => $params['creator'],
            ], 'id');
            if (!$createId) {
                $createId = Company::findOneVal([
                    'full_name' => $params['creator'],
                ], 'member_id');
            }

            $query->andFilterCompare('a.creator_id', $createId);
        }

        // 公告标题or编号检索
        // if ($params['announcementTitleNum']) {
        //     $query = JobApply::uidJudgeWhere($params['announcementTitleNum'], 'a.id', 'a.title', $query);
        // }
        // 单位名称or编号检索
        if ($params['companyName']) {
            $query = JobApply::uidJudgeWhere($params['companyName'], 'c.id', 'c.full_name', $query);
        }

        // 是否合作单位的公告
        $query->andFilterCompare('c.is_cooperation', $params['isCooperation']);
        // 审核状态
        $query->andFilterCompare('a.audit_status', $params['auditStatus']);
        // 所属栏目
        $query->andFilterCompare('ar.home_column_id', $params['homeColumnId']);
        // 招聘状态
        $query->andFilterCompare('ar.status', $params['status']);
        // 显示状态
        $query->andFilterCompare('ar.is_show', $params['isShow']);
        // 文档属性
        // if ($params['notOverseasAttribute']) {
        //     $query->innerJoin(['e' => ArticleAttribute::tableName()], 'e.article_id = ar.id');
        //     $query->andFilterCompare('e.type', $params['notOverseasAttribute'], 'in');
        // }
        // if ($params['overseasAttribute']) {
        //     $query->innerJoin(['e' => ArticleAttribute::tableName()], 'e.article_id = ar.id');
        //     $query->andFilterCompare('e.type', $params['overseasAttribute'], 'in');
        // }

        if ($params['notOverseasAttribute'] || $params['overseasAttribute']) {
            // 合并属性

            $query->innerJoin(['e' => ArticleAttribute::tableName()], 'e.article_id = ar.id');
            if ($params['notOverseasAttribute']) {
                $query->andFilterCompare('e.type', $params['notOverseasAttribute']);
            }
            if ($params['overseasAttribute']) {
                $query->innerJoin(['h' => ArticleAttribute::tableName()], 'h.article_id = ar.id');
                $query->andFilterCompare('h.type', $params['overseasAttribute']);
            }
        }

        // 发布时间（审核通过后的发布时间）
        if ($params['releaseTimeStart']) {
            $query->andWhere([
                '>=',
                'ar.release_time',
                TimeHelper::dayToBeginTime($params['releaseTimeStart']),
            ]);
        }
        if ($params['releaseTimeEnd']) {
            $query->andWhere([
                '<=',
                'ar.release_time',
                TimeHelper::dayToEndTime($params['releaseTimeEnd']),
            ]);
        }

        // 发布时间
        if ($params['refreshTimeStart']) {
            $query->andWhere([
                '>=',
                'ar.refresh_time',
                TimeHelper::dayToBeginTime($params['refreshTimeStart']),
            ]);
        }
        if ($params['refreshTimeEnd']) {
            $query->andWhere([
                '<=',
                'ar.refresh_time',
                TimeHelper::dayToEndTime($params['refreshTimeEnd']),
            ]);
        }

        // 首次发布时间
        if ($params['firstReleaseTimeStart']) {
            $query->andWhere([
                '>=',
                'ar.first_release_time',
                TimeHelper::dayToBeginTime($params['firstReleaseTimeStart']),
            ]);
        }
        if ($params['firstReleaseTimeEnd']) {
            $query->andWhere([
                '<=',
                'ar.first_release_time',
                TimeHelper::dayToEndTime($params['firstReleaseTimeEnd']),
            ]);
        }

        // 刷新时间
        if ($params['realRefreshTimeStart']) {
            $query->andWhere([
                '>=',
                'ar.real_refresh_time',
                TimeHelper::dayToBeginTime($params['realRefreshTimeStart']),
            ]);
        }
        if ($params['realRefreshTimeEnd']) {
            $query->andWhere([
                '<=',
                'ar.real_refresh_time',
                TimeHelper::dayToEndTime($params['realRefreshTimeStart']),
            ]);
        }

        // 更新时间
        if ($params['updateTimeStart']) {
            $query->andWhere([
                '>=',
                'a.update_time',
                TimeHelper::dayToBeginTime($params['updateTimeStart']),
            ]);
        }
        if ($params['updateTimeEnd']) {
            $query->andWhere([
                '<=',
                'a.update_time',
                TimeHelper::dayToEndTime($params['updateTimeEnd']),
            ]);
        }
        $orderBy = 'a.add_time desc';
        // 平台投递
        if ($params['sortPlatformNum'] == self::ORDER_BY_DESC) {
            $orderBy = 'platform_num desc';
        } elseif ($params['sortPlatformNum'] == self::ORDER_BY_ASC) {
            $orderBy = 'platform_num asc';
        }
        // 邮件投递
        if ($params['sortEmailNum'] == self::ORDER_BY_DESC) {
            $orderBy = 'email_num desc';
        } elseif ($params['sortEmailNum'] == self::ORDER_BY_ASC) {
            $orderBy = 'email_num asc';
        }
        // 网址投递
        if ($params['sortLinkNum'] == self::ORDER_BY_DESC) {
            $orderBy = 'link_num desc';
        } elseif ($params['sortLinkNum'] == self::ORDER_BY_ASC) {
            $orderBy = 'link_num asc';
        }
        // 排序数
        if ($params['sortNumberCount'] == self::ORDER_BY_DESC) {
            $orderBy = 'ar.sort desc';
        } elseif ($params['sortNumberCount'] == self::ORDER_BY_ASC) {
            $orderBy = 'ar.sort asc';
        }
        // 点击量
        if ($params['sortClickCount'] == self::ORDER_BY_DESC) {
            $orderBy = 'ar.click desc';
        } elseif ($params['sortClickCount'] == self::ORDER_BY_ASC) {
            $orderBy = 'ar.click asc';
        }
        // 最近一次发布时间
        if ($params['sortReleaseTime'] == self::ORDER_BY_DESC) {
            $orderBy = 'ar.release_time desc';
        } elseif ($params['sortReleaseTime'] == self::ORDER_BY_ASC) {
            $orderBy = 'ar.release_time asc';
        }

        // 发布时间
        if ($params['sortRealRefreshTime'] == self::ORDER_BY_DESC) {
            $orderBy = 'ar.real_refresh_time desc';
        } elseif ($params['sortRealRefreshTime'] == self::ORDER_BY_ASC) {
            $orderBy = 'ar.real_refresh_time asc';
        }
        // 发布时间
        if ($params['sortRefreshTime'] == self::ORDER_BY_DESC) {
            $orderBy = 'ar.refresh_time desc';
        } elseif ($params['sortRefreshTime'] == self::ORDER_BY_ASC) {
            $orderBy = 'ar.refresh_time asc';
        }

        // 发布时间
        if ($params['sortFirstReleaseTime'] == self::ORDER_BY_DESC) {
            $orderBy = 'ar.first_release_time desc';
        } elseif ($params['sortFirstReleaseTime'] == self::ORDER_BY_ASC) {
            $orderBy = 'ar.first_release_time asc';
        }

        // 下线时间
        if ($params['sortOfflineTime'] == self::ORDER_BY_DESC) {
            $orderBy = 'a.period_date desc';
        } elseif ($params['sortOfflineTime'] == self::ORDER_BY_ASC) {
            $orderBy = 'a.period_date asc';
        }

        if ($params['sortHomeSort'] == self::ORDER_BY_DESC) {
            $orderBy = 'a.home_sort desc';
        } elseif ($params['sortHomeSort'] == self::ORDER_BY_ASC) {
            $orderBy = 'a.home_sort asc';
        }

        // 招聘职位数
        if ($params['sortRecruitJobCount'] == self::ORDER_BY_DESC) {
            $orderBy = 'tmp.job_num desc';
        } elseif ($params['sortRecruitJobCount'] == self::ORDER_BY_ASC) {
            $orderBy = 'tmp.job_num asc';
        }

        // 在线职位
        if ($params['sortOnlineJobCount'] == self::ORDER_BY_DESC) {
            $orderBy = 'tmp.online_count desc';
        } elseif ($params['sortOnlineJobCount'] == self::ORDER_BY_ASC) {
            $orderBy = 'tmp.online_count asc';
        }

        // 下限职位
        if ($params['sortOfflineJobCount'] == self::ORDER_BY_DESC) {
            $orderBy = 'tmp.offline_count desc';
        } elseif ($params['sortOfflineJobCount'] == self::ORDER_BY_ASC) {
            $orderBy = 'tmp.offline_count asc';
        }

        // 站内投递次数
        if ($params['sortApplyCount'] == self::ORDER_BY_DESC) {
            $orderBy = 'tmp2.job_apply_num desc';
        } elseif ($params['sortApplyCount'] == self::ORDER_BY_ASC) {
            $orderBy = 'tmp2.job_apply_num asc';
        }

        // 面试邀约次数
        if ($params['sortJobInterviewCount'] == self::ORDER_BY_DESC) {
            $orderBy = 'tmp2.job_interview_num desc';
        } elseif ($params['sortJobInterviewCount'] == self::ORDER_BY_ASC) {
            $orderBy = 'tmp2.job_interview_num asc';
        }

        // 站外投递次数
        if ($params['sortOffApplyCount'] == self::ORDER_BY_DESC) {
            $orderBy = 'tmp3.job_off_apply_num desc';
        } elseif ($params['sortOffApplyCount'] == self::ORDER_BY_ASC) {
            $orderBy = 'tmp3.job_off_apply_num asc';
        }

        if ($params['name'] || $params['natureType'] || $params['abroadType'] || $params['experienceType'] || $params['titleType'] || $params['ageType'] || $params['genderType'] || $params['politicalType'] || $params['city'] || $params['department'] || $params['jobCategoryId'] || $params['educationType'] || $params['majorId'] || $params['establishmentType']) {
            $jobSelect = [
                'j.id as job_id',
                'j.is_article',
                'j.amount',
                'j.address',
                'j.city_id',
                'j.name as job_name',
                'j.job_category_id',
                'j.education_type',
                'j.nature_type',
                'j.abroad_type',
                'j.experience_type',
                'j.title_type',
                'j.age_type',
                'j.min_age',
                'j.max_age',
                'j.gender_type',
                'j.political_type',
                'j.major_id',
            ];
            $select    = array_merge($select, $jobSelect);
            // 职位相关
            $query->leftJoin(['j' => Job::tableName()], 'j.announcement_id = a.id');

            // 工作性质
            $query->andFilterCompare('j.nature_type', $params['natureType']);
            // 海外经历
            $query->andFilterCompare('j.abroad_type', $params['abroadType']);
            // 工作经验
            $query->andFilterCompare('j.experience_type', $params['experienceType']);
            // 职称要求
            $query->andFilterCompare('j.title_type', $params['titleType'], 'in');
            // 年龄要求
            $query->andFilterCompare('j.age_type', $params['ageType'], 'like');
            // 性别要求
            $query->andFilterCompare('j.gender_type', $params['genderType']);
            // 政治面貌
            $query->andFilterCompare('j.political_type', $params['politicalType']);
            // 工作城市
            $query->andFilterCompare('j.province_id', $params['city'][0]);
            $query->andFilterCompare('j.city_id', $params['city'][1]);
            // 用人部门
            $query->andFilterCompare('j.department', $params['department'], 'like');
            // 职位类型
            $query->andFilterCompare('j.job_category_id', $params['jobCategoryId'], 'in');
            // 学历要求
            $query->andFilterCompare('j.education_type', $params['educationType'], 'in');
            // 需求学科
            if ($params['majorId']) {
                $query->andWhere("find_in_set(" . $params['majorId'] . ",j.major_id)");
            }

            if ($params['establishmentType']) {
                // 这里会有几种可能性,-1和其他值(-1的情况下是没有选编制的类型,其他值是选了编制类型里面某个并且find_in_set
                $establishmentType = explode(',', $params['establishmentType']);
                $orWhere           = [
                    'or',
                ];
                foreach ($establishmentType as $itemKey => $establishmentItem) {
                    if ($establishmentItem == -1) {
                        $orWhere[] = ['j.is_establishment' => BaseJob::IS_ESTABLISHMENT_NO];
                    } else {
                        $establishmentItemKey = 'establishmentItem' . $itemKey;
                        $orWhere[]            = new Expression("FIND_IN_SET(:" . $establishmentItemKey . ", j.establishment_type)",
                            [$establishmentItemKey => $establishmentItem]);
                    }
                }
                $query->andWhere($orWhere);
            }
        }

        // 这里拼接
        if ($announcementIds) {
            //announcementIds是一个数组
            $query->andWhere(['a.id' => $announcementIds]);
        }

        // 小程序
        $query->andFilterCompare('a.is_miniapp', $params['isMiniapp']);

        if ($params['export']) {
            // 不限制内存和时间
            ini_set('memory_limit', '-1');
            ini_set('max_execution_time', '0');
            set_time_limit(0);

            return [
                'list' => $query->select($select)
                    ->orderBy($orderBy)
                    ->asArray()
                    ->all(),
            ];
        }

        $count    = $query->count();
        $pageSize = $params['pageSize'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $params['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->select($select)
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        return [
            'list'  => $list,
            'pages' => [
                'size'  => (int)$pageSize,
                'total' => (int)$count,
            ],
        ];
    }

    /**
     * 获取公告审核列表（合作单位）
     * @param $params
     * @return array
     * @throws Exception
     */
    public static function getAuditList($params)
    {
        // 不限制内存和时间
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', '0');
        set_time_limit(0);

        $select = [
            'a.id as aid',
            'a.title',
            'a.company_id',
            'a.creator_id',
            'a.create_type',
            'a.audit_status',
            'ar.status',
            'ar.first_release_time',
            'ar.apply_audit_time',
            'c.full_name',
            'COUNT(j.announcement_id) as jobNum',
            'COUNT(CASE WHEN j.status=' . Job::STATUS_ONLINE . ' THEN 1 END) as onlineJobCount',
            'COUNT(CASE WHEN j.audit_status=' . Job::STATUS_WAIT_AUDIT . ' THEN 1 END) as waitAuditJobCount',
        ];

        $where = [
            'and',
            [
                'a.audit_status'   => self::STATUS_AUDIT_AWAIT,
                //'ar.is_delete'     => self::STATUS_DELETE,
                'c.is_cooperation' => Company::COOPERATIVE_UNIT_YES,
            ],
            [
                'ar.status' => [
                    BaseAnnouncement::STATUS_STAGING,
                    BaseAnnouncement::STATUS_ONLINE,
                ],
            ],
            [
                '<>',
                'j.status',
                Job::STATUS_DELETE,
            ],
        ];

        $query = self::find()
            ->alias('a')
            ->innerJoin(['ar' => Article::tableName()], 'ar.id = a.article_id')
            ->innerJoin(['c' => Company::tableName()], 'c.id = a.company_id')
            ->innerJoin(['j' => Job::tableName()], 'j.announcement_id = a.id')
            ->select($select)
            ->where($where)
            ->groupBy(['aid']);

        JobApply::uidJudgeWhere($params['announcementTitleNum'], 'a.id', 'a.title', $query);
        JobApply::uidJudgeWhere($params['companyNameNum'], 'c.id', 'c.full_name', $query);
        JobApply::uidJudgeWhere($params['jobNameNum'], 'j.id', 'j.name', $query);

        if ($params['username']) {
            $adminWhere = [
                'or',
                [
                    'username' => $params['username'],
                ],
                [
                    'name' => $params['username'],
                ],
            ];
            // 创建人帐号或姓名
            $createId = Admin::findOneVal($adminWhere, 'id');
            if (!$createId) {
                $memberWhere = [
                    'or',
                    [
                        'm.username' => $params['username'],
                    ],
                    [
                        'c.full_name' => $params['username'],
                    ],
                ];
                $createId    = Member::find()
                    ->alias('m')
                    ->leftJoin(['c' => Company::tableName()], 'c.member_id = m.id')
                    ->select('m.id')
                    ->where($memberWhere)
                    ->asArray()
                    ->one();
                $createId    = $createId['id'];
            }
            $query->andFilterCompare('a.creator_id', $createId);
        }

        // 首次发布时间
        if ($params['firstReleaseTimeStart']) {
            $query->andWhere([
                '>=',
                'ar.first_release_time',
                TimeHelper::dayToBeginTime($params['firstReleaseTimeStart']),
            ]);
        }
        if ($params['firstReleaseTimeEnd']) {
            $query->andWhere([
                '<=',
                'ar.first_release_time',
                TimeHelper::dayToEndTime($params['firstReleaseTimeEnd']),
            ]);
        }

        // 申请时间
        if ($params['applyAuditTimeStart']) {
            $query->andWhere([
                '>=',
                'ar.apply_audit_time',
                TimeHelper::dayToBeginTime($params['applyAuditTimeStart']),
            ]);
        }
        if ($params['applyAuditTimeEnd']) {
            $query->andWhere([
                '<=',
                'ar.apply_audit_time',
                TimeHelper::dayToEndTime($params['applyAuditTimeEnd']),
            ]);
        }

        $orderBy = 'ar.apply_audit_time desc';
        // 首次发布时间排序
        if ($params['sortFirstReleaseTime'] == self::ORDER_BY_DESC) {
            $orderBy = 'ar.first_release_time desc';
        } else {
            if ($params['sortFirstReleaseTime'] == self::ORDER_BY_ASC) {
                $orderBy = 'ar.first_release_time asc';
            }
        }
        // 申请审核时间排序
        if ($params['sortApplyAuditTime'] == self::ORDER_BY_DESC) {
            $orderBy = 'ar.apply_audit_time desc';
        } else {
            if ($params['sortApplyAuditTime'] == self::ORDER_BY_ASC) {
                $orderBy = 'ar.apply_audit_time asc';
            }
        }
        // 招聘职位数排序
        if ($params['sortRecruitJobCount'] == self::ORDER_BY_DESC) {
            $orderBy = 'jobNum desc';
        } else {
            if ($params['sortRecruitJobCount'] == self::ORDER_BY_ASC) {
                $orderBy = 'jobNum asc';
            }
        }
        // 在线职位数排序
        if ($params['sortOnlineJobCount'] == self::ORDER_BY_DESC) {
            $orderBy = 'onlineJobCount desc';
        } else {
            if ($params['sortOnlineJobCount'] == self::ORDER_BY_ASC) {
                $orderBy = 'onlineJobCount asc';
            }
        }

        $count    = $query->count();
        $pageSize = $params['pageSize'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $params['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->select($select)
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['auditStatusTxt']   = self::STATUS_AUDIT_LIST[$item['audit_status']] ?: '';
            $item['recruitStatusTxt'] = Article::STATUS_LIST[$item['status']] ?: '';
            $item['announcementUid']  = UUIDHelper::encrypt(UUIDHelper::TYPE_ANNOUNCEMENT, $item['aid']);
            $item['fullName']         = $item['full_name'] . '(' . UUIDHelper::encrypt(UUIDHelper::TYPE_COMPANY,
                    $item['company_id']) . ')';
            if ($item['create_type'] == self::CREATE_TYPE_ADMIN) {
                $item['creatorName'] = Admin::findOneVal(['id' => $item['creator_id']], 'name');
            } else {
                $item['creatorName'] = Company::findOneVal(['member_id' => $item['creator_id']], 'full_name');
            }
            $handle_log_info = BaseAnnouncementHandleLog::find()
                ->where(['announcement_id' => $item['aid']])
                ->orderBy('add_time desc')
                ->one();
            if ($handle_log_info) {
                $item['creatorName']    = $handle_log_info['handler_name'];
                $item['applyAuditTime'] = $handle_log_info['add_time'];
            }

            $item['attribute'] = BaseArticleAttribute::getArticleAttribute($item['article_id']);
            // 文档属性
            $attribute = explode(',', $item['attribute']);
            if ($attribute > 1) {
                $tip = [];
                foreach ($attribute as $val) {
                    $tip[] = ArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_LIST[$val];
                }
                $item['tip'] = implode(' ', $tip);
            } else {
                $item['tip'] = ArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_LIST[$item['attribute']];
            }
        }

        // 审核统计
        $queryAuditAmount = self::find()
            ->alias('a')
            ->innerJoin(['ar' => Article::tableName()], 'ar.id = a.article_id')
            ->leftJoin(['c' => Company::tableName()], 'c.id = a.company_id')
            ->leftJoin(['j' => Job::tableName()], 'j.announcement_id = a.id')
            ->select($select)
            ->where($where)
            ->groupBy(['aid']);

        $auditAmountCount            = $queryAuditAmount->count();
        $adminAuditStatusWaitCount   = 0;
        $companyAuditStatusWaitCount = 0;
        foreach ($queryAuditAmount->all() as $val) {
            if ($val['create_type'] == self::CREATE_TYPE_ADMIN) {
                // 运营代发待审核
                $adminAuditStatusWaitCount++;
            } elseif ($val['create_type'] == self::CREATE_TYPE_COMPANY) {
                // 单位发待审核
                $companyAuditStatusWaitCount++;
            }
        }

        if ($params['export']) {
            $realKey  = Cache::PC_ADMIN_TABLE_STAGING_FIELD_KEY . ':' . 'announcementAuditQuery' . ':' . Yii::$app->user->id;
            $keyValue = Cache::get($realKey) ?: '';
            if (sizeof($keyValue) > 0) {
                $keyValueArray = json_decode($keyValue, true);
                $keyHeaders    = [];
                $headers       = [];
                foreach ($keyValueArray as $keyList) {
                    if ($keyList['select'] && $keyList['name'] != 'operation') {
                        $keyHeaders[] = $keyList['name'];
                        $headers[]    = $keyList['v'];
                    }
                }
                if (!$keyHeaders || !$headers || sizeof($keyHeaders) <> sizeof($headers)) {
                    throw new Exception('当前不可下载');
                }

                $data = [];
                foreach ($list as $val) {
                    $temp = [];
                    foreach ($keyHeaders as $keys) {
                        $temp [] = $val[$keys];
                    }
                    $data[] = $temp;
                }

                $excel    = new Excel();
                $fileName = $excel->export($data, $headers);

                return [
                    'excelUrl' => $fileName,
                ];
            } else {
                throw new Exception('未知下载列');
            }
        } else {
            return [
                'list'   => $list,
                'pages'  => [
                    'size'  => (int)$pageSize,
                    'total' => (int)$count,
                ],
                'amount' => [
                    'auditAmountCount'            => (int)$auditAmountCount,
                    'adminAuditStatusWaitCount'   => $adminAuditStatusWaitCount,
                    'companyAuditStatusWaitCount' => $companyAuditStatusWaitCount,
                ],
            ];
        }
    }

    /**
     * 获取公告审核列表（非合作单位）
     * @param $params
     * @return array
     * @throws Exception
     */
    public static function getNoncooperationAuditList($params)
    {
        $select = [
            'a.id as aid',
            'a.title',
            'a.company_id',
            'a.creator_id',
            'a.create_type',
            'a.audit_status',
            'a.article_id',
            'ar.status',
            'ar.apply_audit_time',
            'ar.home_column_id',
            'ar.first_release_time',
            'c.full_name',
            'COUNT(j.announcement_id) as jobNum',
            'COUNT(CASE WHEN j.status=' . Job::STATUS_ONLINE . ' THEN 1 END) as onlineJobCount',
            'COUNT(CASE WHEN j.audit_status=' . Job::STATUS_WAIT_AUDIT . ' THEN 1 END) as waitAuditJobCount',
        ];

        $where = [
            'and',
            [
                'a.audit_status'   => self::STATUS_AUDIT_AWAIT,
                'ar.is_delete'     => self::STATUS_DELETE,
                'c.is_cooperation' => Company::COOPERATIVE_UNIT_NO,
            ],
            [
                'ar.status' => [
                    BaseAnnouncement::STATUS_STAGING,
                    BaseAnnouncement::STATUS_ONLINE,
                ],
            ],
            [
                '<>',
                'j.status',
                Job::STATUS_DELETE,
            ],
        ];

        $query = self::find()
            ->alias('a')
            ->innerJoin(['ar' => Article::tableName()], 'ar.id = a.article_id')
            ->leftJoin(['c' => Company::tableName()], 'c.id = a.company_id')
            ->leftJoin(['j' => Job::tableName()], 'j.announcement_id = a.id')
            ->select($select)
            ->where($where)
            ->groupBy(['aid']);

        // 所属栏目
        $query->andFilterCompare('ar.home_column_id', $params['homeColumnId']);
        // 文档属性
        // if ($params['attribute']) {
        //     $query->innerJoin(['e' => ArticleAttribute::tableName()], 'e.article_id = ar.id');
        //     $query->andFilterCompare('e.type', $params['attribute'], 'in');
        // }
        if ($params['attribute']) {
            $query->innerJoin(['e' => ArticleAttribute::tableName()], 'e.article_id = ar.id');
            $query->andFilterCompare('e.type', $params['attribute'], 'in');
        }
        if ($params['overseasAttribute']) {
            $query->innerJoin(['e' => ArticleAttribute::tableName()], 'e.article_id = ar.id');
            $query->andFilterCompare('e.type', $params['overseasAttribute'], 'in');
        }
        JobApply::uidJudgeWhere($params['announcementTitleNum'], 'a.id', 'a.title', $query);
        JobApply::uidJudgeWhere($params['companyNameNum'], 'c.id', 'c.full_name', $query);
        JobApply::uidJudgeWhere($params['jobNameNum'], 'j.id', 'j.name', $query);

        if ($params['username']) {
            $userWhere = [
                'or',
                [
                    'username' => $params['username'],
                ],
                [
                    'name' => $params['username'],
                ],
            ];
            // 创建人帐号
            $createId = Admin::findOneVal($userWhere, 'id');
            $query->andFilterCompare('a.creator_id', $createId);
        }

        // 申请审核时间
        if ($params['applyAuditTimeStart']) {
            $query->andWhere([
                '>=',
                'ar.apply_audit_time',
                TimeHelper::dayToBeginTime($params['applyAuditTimeStart']),
            ]);
        }
        if ($params['applyAuditTimeEnd']) {
            $query->andWhere([
                '<=',
                'ar.apply_audit_time',
                TimeHelper::dayToEndTime($params['applyAuditTimeEnd']),
            ]);
        }

        // 首次发布时间
        if ($params['firstReleaseTimeStart']) {
            $query->andWhere([
                '>=',
                'ar.first_release_time',
                TimeHelper::dayToBeginTime($params['firstReleaseTimeStart']),
            ]);
        }
        if ($params['firstReleaseTimeEnd']) {
            $query->andWhere([
                '<=',
                'ar.first_release_time',
                TimeHelper::dayToEndTime($params['firstReleaseTimeEnd']),
            ]);
        }

        $orderBy = 'ar.apply_audit_time desc';
        // 首次发布时间排序
        if ($params['sortFirstReleaseTime'] == self::ORDER_BY_DESC) {
            $orderBy = 'ar.first_release_time desc';
        } else {
            if ($params['sortReleaseTime'] == self::ORDER_BY_ASC) {
                $orderBy = 'ar.first_release_time asc';
            }
        }
        // 申请审核时间排序
        if ($params['sortApplyAuditTime'] == self::ORDER_BY_DESC) {
            $orderBy = 'ar.apply_audit_time desc';
        } else {
            if ($params['sortApplyAuditTime'] == self::ORDER_BY_ASC) {
                $orderBy = 'ar.apply_audit_time asc';
            }
        }
        // 招聘职位数排序
        if ($params['sortRecruitJobCount'] == self::ORDER_BY_DESC) {
            $orderBy = 'jobNum desc';
        } else {
            if ($params['sortRecruitJobCount'] == self::ORDER_BY_ASC) {
                $orderBy = 'jobNum asc';
            }
        }
        // 待审核职位数排序
        if ($params['sortWaitAuditJobCount'] == self::ORDER_BY_DESC) {
            $orderBy = 'waitAuditJobCount desc';
        } else {
            if ($params['sortWaitAuditJobCount'] == self::ORDER_BY_ASC) {
                $orderBy = 'waitAuditJobCount asc';
            }
        }
        // 在线职位数排序
        if ($params['sortOnlineJobCount'] == self::ORDER_BY_DESC) {
            $orderBy = 'onlineJobCount desc';
        } else {
            if ($params['sortOnlineJobCount'] == self::ORDER_BY_ASC) {
                $orderBy = 'onlineJobCount asc';
            }
        }

        $count    = $query->count();
        $pageSize = $params['pageSize'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $params['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->select($select)
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        // 待审核总数统计
        $queryAuditAmount = self::find()
            ->alias('a')
            ->innerJoin(['ar' => Article::tableName()], 'ar.id = a.article_id')
            ->leftJoin(['c' => Company::tableName()], 'c.id = a.company_id')
            ->leftJoin(['j' => Job::tableName()], 'j.announcement_id = a.id')
            ->select($select)
            ->where($where)
            ->groupBy(['aid']);
        $auditAmountCount = $queryAuditAmount->count();

        foreach ($list as &$item) {
            $item['attribute']        = BaseArticleAttribute::getArticleAttribute($item['article_id']);
            $item['auditStatusTxt']   = self::STATUS_AUDIT_LIST[$item['audit_status']] ?: '';
            $item['recruitStatusTxt'] = Article::STATUS_LIST[$item['status']] ?: '';
            $item['announcementUid']  = UUIDHelper::encrypt(UUIDHelper::TYPE_ANNOUNCEMENT, $item['aid']);
            $item['fullName']         = $item['full_name'] . '(' . UUIDHelper::encrypt(UUIDHelper::TYPE_COMPANY,
                    $item['company_id']) . ')';
            $item['creatorName']      = Admin::findOneVal(['id' => $item['creator_id']], 'name');
            // 文档属性
            $attribute = explode(',', $item['attribute']);
            if ($attribute > 1) {
                $tip = [];
                foreach ($attribute as $val) {
                    $tip[] = ArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_LIST[$val];
                }
                $item['tip'] = implode(' ', $tip);
            } else {
                $item['tip'] = ArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_LIST[$item['attribute']];
            }
        }

        if ($params['export']) {
            $realKey  = Cache::PC_ADMIN_TABLE_STAGING_FIELD_KEY . ':' . 'noncooperationAuditQuery' . ':' . Yii::$app->user->id;
            $keyValue = Cache::get($realKey) ?: '';
            if (sizeof($keyValue) > 0) {
                $keyValueArray = json_decode($keyValue, true);
                $keyHeaders    = [];
                $headers       = [];
                foreach ($keyValueArray as $keyList) {
                    if ($keyList['select'] && $keyList['name'] != 'operation') {
                        $keyHeaders[] = $keyList['name'];
                        $headers[]    = $keyList['v'];
                    }
                }
                if (!$keyHeaders || !$headers || sizeof($keyHeaders) <> sizeof($headers)) {
                    throw new Exception('当前不可下载');
                }

                $data = [];
                foreach ($list as $val) {
                    $temp = [];
                    foreach ($keyHeaders as $keys) {
                        $temp [] = $val[$keys];
                    }
                    $data[] = $temp;
                }

                $excel    = new Excel();
                $fileName = $excel->export($data, $headers);

                return [
                    'excelUrl' => $fileName,
                ];
            } else {
                throw new Exception('未知下载列');
            }
        } else {
            return [
                'list'             => $list,
                'pages'            => [
                    'size'  => (int)$pageSize,
                    'total' => (int)$count,
                ],
                'auditAmountCount' => (int)$auditAmountCount,
            ];
        }
    }

    /**
     * 公告和职位学科整合
     * @param $jobMajor
     * @param $announcementMajor
     * @return string
     * @throws \Exception
     */
    private static function integrationMajor($jobMajor, $announcementMajor)
    {
        $jobMajor    = implode(',', $jobMajor);
        $jobMajorArr = array_unique(explode(',', $jobMajor));

        if (count($jobMajorArr) > 1) {
            $key = array_search('-', $jobMajorArr);
            unset($jobMajorArr[$key]);
        }
        $announcementInfoMajor    = Major::getAllMajorName(explode(',', $announcementMajor)) ?: '';
        $announcementInfoMajorArr = explode(',', $announcementInfoMajor);

        return implode(' ', array_merge($jobMajorArr, $announcementInfoMajorArr));
    }

    /**
     * 获取公告详情
     * @param $params
     * @return array|\yii\db\ActiveRecord|null
     * @throws Exception
     * @throws \Exception
     */
    public static function getDetail($params)
    {
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', '0');
        $select = [
            'a.id',
            'a.company_id as cid',
            'a.period_date',
            'a.major_ids',
            'a.apply_type',
            'a.apply_address',
            'a.extra_notify_address',
            'a.delivery_type',
            'a.title',
            'a.audit_status',
            'a.article_id',
            'a.file_ids',
            'a.is_attachment_notice',
            'ar.home_column_id',
            'ar.home_sub_column_ids',
            'ar.release_time',
            'ar.add_time',
            'ar.content',
            'ar.status',
            'ar.first_release_time',
            'a.address_hide_status',
            'a.sub_title as subTitle',
            'a.highlights_describe as highlightsDescribe',
        ];

        if ($params['type'] == Article::STATUS_STAGING) {
            $where = [
                'ar.status' => [
                    Article::STATUS_STAGING,
                ],
            ];
        } else {
            $where = [
                'ar.status' => [
                    Article::STATUS_ONLINE,
                    Article::STATUS_OFFLINE,
                ],
            ];
        }

        $info = self::find()
            ->alias('a')
            ->leftJoin(['ar' => Article::tableName()], 'ar.id = a.article_id')
            ->leftJoin(['j' => Job::tableName()], 'j.announcement_id = a.id')
            ->select($select)
            ->where([
                'a.id' => $params['id'],
            ])
            ->andWhere($where)
            ->asArray()
            ->one();
        if (!$info['id']) {
            throw new Exception('当前公告不存在');
        }

        // 栏目名称
        $info['homeColumnTxt'] = HomeColumn::findOneVal(['id' => $info['home_column_id']], 'name') ?: '-';
        $homeSubColumnIdsArr   = explode(',', $info['home_sub_column_ids']);
        $info['companyTitle']  = Company::findOneVal(['id' => $info['cid']], 'full_name') ?: '';
        // 获取副栏目
        $homeSubColumnArr = HomeColumn::find()
            ->select('name')
            ->where([
                'id' => $homeSubColumnIdsArr,
            ])
            ->asArray()
            ->all();
        foreach ($homeSubColumnArr as $v) {
            $info['homeSubColumn'][] = $v['name'];
        }
        $info['company_delivery_type'] = Company::findOneVal(['id' => $info['cid']], 'delivery_type');
        $info['apply_address']         = $info['apply_address'] ?: '-';
        $info['extra_notify_address']  = $info['extra_notify_address'] ?: '-';
        $info['homeSubColumnTxt']      = !empty($info['homeSubColumn']) ? implode(',', $info['homeSubColumn']) : '-';
        $info['is_cooperation']        = Company::findOneVal(['id' => $info['cid']], 'is_cooperation');
        //获取公告在招职位数量
        $info['jobNum'] = Job::getAnnouncementJobAmount($params['id'], $params['type']);
        //获取公告招聘人
        $info['amountNum'] = Job::getAnnouncementJobRecruitAmount($params['id'], $params['type']);
        //获取公告下所有省份
        $info['areaProvinceTxt'] = self::getAllProvinceName($params['id'], $params['type']) ?: '-';
        //获取公告下的所有城市
        $info['areaCityTxt'] = self::getAllCityName($params['id'], $params['type']) ?: '-';
        //获取公告下的最低学历要求
        $minEducation         = self::getMinEducationName($params['id'], $params['type']);
        $info['educationTxt'] = !empty($minEducation) ? $minEducation : '-';
        //获取公告下的专业
        $info['majorTxt'] = self::getAllMajorName($params['id'], 'text', $params['type']);
        //获取报名方式
        if ($info['delivery_type'] == BaseAnnouncement::DELIVERY_TYPE_OUTSIDE) {
            if ($info['apply_type']) {
                $applyTypeList   = explode(',', $info['apply_type']);
                $applyTypeTxtArr = [];
                foreach ($applyTypeList as $item) {
                    $item_name = Dictionary::getSignUpName($item);
                    if (!empty($item_name)) {
                        array_push($applyTypeTxtArr, $item_name);
                    }
                }
                $info['applyTypeTxt'] = count($applyTypeTxtArr) > 0 ? implode(',', $applyTypeTxtArr) : '-';
            } else {
                $info['applyTypeTxt'] = '-';
            }
        } elseif ($info['delivery_type'] == BaseAnnouncement::DELIVERY_TYPE_INSIDE) {
            $info['applyTypeTxt'] = '站内投递';
        } else {
            $info['applyTypeTxt'] = '-';
        }
        // 有审核历史显示发布时间，无审核历史显示创建时间
        if ($info['first_release_time'] == TimeHelper::ZERO_TIME) {
            $info['release_time'] = $info['add_time'];
        }
        // 获取公告详情职位列表
        $jobList = Job::getAnnouncementDetailJobList($params['id'], $params['type']);
        foreach ($jobList['list'] as &$item) {
            if ($item['amount'] == '若干') {
                $info['amountNum'] = '若干';
            }
        }
        if ($info['period_date'] == TimeHelper::ZERO_TIME) {
            $info['period_date'] = '详见正文';
        } else {
            $info['period_date'] = substr($info['period_date'], 0, 10);
        }
        // 获取调用栏目
        $info['columnTxt'] = BaseArticleColumn::getArticleColumn($info['article_id']);

        //公告职位附件
        $fileList = [];
        if (!empty($info['file_ids'])) {
            $fileList = self::getAppendixList($info['file_ids']);
        }

        $info['is_attachment_notice_txt'] = BaseAnnouncement::IS_ATTACHMENT_NOTICE_LIST[$info['is_attachment_notice']] ?? '-';

        //隐藏邮箱文本
        $info['addressHideStatusText'] = self::ADDRESS_HIDE_STATUS_TEXT[$info['address_hide_status']];

        return [
            'baseInfo' => $info,
            'jobList'  => $jobList['list'] ?: [],
            'fileList' => $fileList ?: [],
        ];
    }

    /**
     * 获取审核详情
     * @param $params
     */
    public static function getAuditDetail($params)
    {
        $select = [
            'a.id',
            'a.company_id',
            'a.add_time',
            'a.period_date',
            'a.article_id',
            'a.work_area_id',
            'a.major_ids',
            'a.apply_type',
            'a.apply_address',
            'a.audit_status',
            'a.template_id',
            'a.file_ids',
            'a.delivery_type',
            'a.extra_notify_address',
            'a.is_attachment_notice',
            'ar.status',
            'ar.home_column_id',
            'ar.home_sub_column_ids',
            'ar.content',
            'ar.first_release_time',
            'ar.title',
            'ar.apply_audit_time',
            'COUNT(j.announcement_id) as jobNum',
            'SUM(j.amount) as amountCount',
            'a.address_hide_status',
            'a.sub_title as subTitle',
            'a.highlights_describe as highlightsDescribe',
            'a.background_img_file_id as backgroundImgFileId',
            'a.background_img_file_id_2 as backgroundImgFileId2',
            'a.background_img_file_id_3 as backgroundImgFileId3',
            'a.background_img_file_type as backgroundImgFileType',
            'a.activity_job_content',
        ];

        $announcementInfo = self::find()
            ->alias('a')
            ->innerJoin(['ar' => Article::tableName()], 'ar.id = a.article_id')
            ->leftJoin(['j' => Job::tableName()], 'j.announcement_id = a.id')
            ->select($select)
            ->where(['a.id' => $params['id']])
            ->asArray()
            ->one();
        if (!$announcementInfo) {
            throw new Exception('当前公告不存在');
        }
        if ($announcementInfo['audit_status'] != Announcement::STATUS_AUDIT_AWAIT) {
            throw new Exception('审核状态不是待审核');
        }

        if ($announcementInfo['period_date'] == TimeHelper::ZERO_TIME) {
            $announcementInfo['period_date'] = '详见正文';
        } else {
            $announcementInfo['period_date'] = substr($announcementInfo['period_date'], 0, 10);
        }

        $announcementInfo['delivery_type_txt'] = BaseAnnouncement::DELIVERY_TYPE_NAME[$announcementInfo['delivery_type']] ?? '-';

        $announcementInfo['is_attachment_notice_txt'] = BaseAnnouncement::IS_ATTACHMENT_NOTICE_LIST[$announcementInfo['is_attachment_notice']] ?? '-';

        if (!$announcementInfo['template_id']) {
            $announcementInfo['template_id'] = '';
        }

        $companyInfo                                   = BaseCompany::findOne(['id' => $announcementInfo['company_id']]);
        $isCooperation                                 = $companyInfo->is_cooperation;
        $announcementInfo['isCooperation']             = (string)$isCooperation;
        $announcementInfo['company_delivery_type']     = $companyInfo->delivery_type;
        $announcementInfo['company_delivery_type_txt'] = BaseCompany::DELIVERY_TYPE_NAME[$companyInfo->delivery_type];
        $announcementInfo['homeColumnTxt']             = HomeColumn::findOneVal(['id' => $announcementInfo['home_column_id']],
            'name');
        $announcementInfo['homeSubColumnIdsArr']       = explode(',', $announcementInfo['home_sub_column_ids']);

        // 获取副栏目
        $homeSubColumnArr = HomeColumn::find()
            ->select('name')
            ->where([
                'in',
                'id',
                $announcementInfo['homeSubColumnIdsArr'],
            ])
            ->asArray()
            ->all();

        foreach ($homeSubColumnArr as $v) {
            $announcementInfo['homeSubColumn'][] = $v['name'];
        }
        $announcementInfo['homeSubColumnTxt'] = implode(',', $announcementInfo['homeSubColumn']) ?: '-';

        $announcementInfo['applyTypeTxt'] = '-';
        if ($announcementInfo['apply_type']) {
            $applyTypeArr = explode(',', $announcementInfo['apply_type']);
            if (count($applyTypeArr) > 1) {
                $applyTypeTxtArr = [];
                foreach ($applyTypeArr as $v) {
                    $applyTypeTxtArr[] = Dictionary::getSignUpName($v);
                }
                $announcementInfo['applyTypeTxt'] = implode(',', $applyTypeTxtArr);
            } else {
                $announcementInfo['applyTypeTxt'] = Dictionary::getSignUpName($announcementInfo['apply_type']);
            }
        }

        //这里有审核通过记录，拿到最新的公告审核操作记录
        $announcementHandleLog = AnnouncementHandleLog::find()
            ->where([
                'announcement_id' => $announcementInfo['id'],
                'handle_type'     => AnnouncementHandleLog::HANDLE_TYPE_EDIT,
                'status'          => AnnouncementHandleLog::STATUS_ACTIVE,
            ])
            ->select([
                'id',
                'handle_before',
                'handle_after',
                'editor_type',
            ])
            ->limit(1)
            ->orderBy('id desc')
            ->asArray()
            ->one();

        $fileList                             = [];
        $announcementInfo['fileHandleBefore'] = [];
        $announcementInfo['fileHandleAfter']  = [];
        // 公告有审核通过历史
        if ($announcementInfo['first_release_time'] != TimeHelper::ZERO_TIME && $announcementHandleLog['editor_type']) {
            switch ($announcementHandleLog['editor_type']) {
                // ==仅修改公告==
                case Announcement::TYPE_EDITOR_ANNOUNCEMENT:
                    $announcementEditData                         = self::editAnnouncement($announcementHandleLog);
                    $announcementInfo['announcementHandleBefore'] = $announcementEditData['announcementHandleBefore'];
                    $announcementInfo['announcementHandleAfter']  = $announcementEditData['announcementHandleAfter'];
                    // 是否修改公告职位附件
                    if ($announcementEditData['fileHandleAfterIds']) {
                        $announcementInfo['fileHandleBefore'] = self::getJobFileAppendixData($announcementEditData['fileHandleBeforeIds'],
                            $announcementEditData['fileHandleAfterIds'])['fileHandleBefore'];
                        $announcementInfo['fileHandleAfter']  = self::getJobFileAppendixData($announcementEditData['fileHandleBeforeIds'],
                            $announcementEditData['fileHandleAfterIds'])['fileHandleAfter'];
                    }
                    //获取待审核职位
                    $announcementInfo['authJobList'] = self::getAuditJobList($params['id']);
                    break;
                // ==仅新增职位==
                case Announcement::TYPE_ADD_JOB:
                    $jobAddData                      = self::createJob($announcementInfo);
                    $announcementInfo['authJobList'] = $jobAddData['authJobList'];
                    break;
                // ==仅修改职位==
                case Announcement::TYPE_EDITOR_JOB:
                    //修改职位
                    $jobEditData = self::editJob($announcementInfo);

                    $announcementInfo['modifyBeforeList'] = $jobEditData['modifyBeforeList'];
                    //获取待审核职位
                    $announcementInfo['authJobList'] = self::getAuditJobList($params['id']);
                    break;
                // ==修改职位+新增职位==
                case Announcement::TYPE_EDITOR_ADD_JOB:
                    //修改职位
                    $jobEditData                          = self::editJob($announcementInfo);
                    $announcementInfo['modifyBeforeList'] = $jobEditData['modifyBeforeList'];
                    //新增职位
                    $jobAddData = self::createJob($announcementInfo);
                    //获取待审核职位
                    $announcementInfo['authJobList'] = $jobAddData['authJobList'];
                    break;
                // ==修改公告+修改职位==
                case Announcement::TYPE_EDITOR_ANNOUNCEMENT_JOB:
                    //修改过公告
                    $announcementEditData                         = self::editAnnouncement($announcementHandleLog);
                    $announcementInfo['announcementHandleBefore'] = $announcementEditData['announcementHandleBefore'];
                    $announcementInfo['announcementHandleAfter']  = $announcementEditData['announcementHandleAfter'];
                    // 是否修改公告职位附件
                    if ($announcementEditData['fileHandleAfterIds']) {
                        $announcementInfo['fileHandleBefore'] = self::getJobFileAppendixData($announcementEditData['fileHandleBeforeIds'],
                            $announcementEditData['fileHandleAfterIds'])['fileHandleBefore'];
                        $announcementInfo['fileHandleAfter']  = self::getJobFileAppendixData($announcementEditData['fileHandleBeforeIds'],
                            $announcementEditData['fileHandleAfterIds'])['fileHandleAfter'];
                    }
                    //修改职位
                    $jobEditData                          = self::editJob($announcementInfo);
                    $announcementInfo['modifyBeforeList'] = $jobEditData['modifyBeforeList'];
                    //获取待审核职位
                    $announcementInfo['authJobList'] = self::getAuditJobList($params['id']);
                    break;
                // ==修改公告+新增职位==
                case Announcement::TYPE_EDITOR_ANNOUNCEMENT_ADD_JOB:
                    //修改公告
                    $announcementEditData                         = self::editAnnouncement($announcementHandleLog);
                    $announcementInfo['announcementHandleBefore'] = $announcementEditData['announcementHandleBefore'];
                    $announcementInfo['announcementHandleAfter']  = $announcementEditData['announcementHandleAfter'];
                    // 是否修改公告职位附件
                    if ($announcementEditData['fileHandleAfterIds']) {
                        $announcementInfo['fileHandleBefore'] = self::getJobFileAppendixData($announcementEditData['fileHandleBeforeIds'],
                            $announcementEditData['fileHandleAfterIds'])['fileHandleBefore'];
                        $announcementInfo['fileHandleAfter']  = self::getJobFileAppendixData($announcementEditData['fileHandleBeforeIds'],
                            $announcementEditData['fileHandleAfterIds'])['fileHandleAfter'];
                    }
                    //新增职位
                    $jobAddData                      = self::createJob($announcementInfo);
                    $announcementInfo['authJobList'] = $jobAddData['authJobList'];
                    break;
                // ==修改公告+修改职位+新增职位==
                case Announcement::TYPE_EDITOR_ANNOUNCEMENT_JOB_ADD_JOB:
                    //修改公告
                    $announcementEditData                         = self::editAnnouncement($announcementHandleLog);
                    $announcementInfo['announcementHandleBefore'] = $announcementEditData['announcementHandleBefore'];
                    $announcementInfo['announcementHandleAfter']  = $announcementEditData['announcementHandleAfter'];
                    // 是否修改公告职位附件
                    if ($announcementEditData['fileHandleAfterIds']) {
                        $announcementInfo['fileHandleBefore'] = self::getJobFileAppendixData($announcementEditData['fileHandleBeforeIds'],
                            $announcementEditData['fileHandleAfterIds'])['fileHandleBefore'];
                        $announcementInfo['fileHandleAfter']  = self::getJobFileAppendixData($announcementEditData['fileHandleBeforeIds'],
                            $announcementEditData['fileHandleAfterIds'])['fileHandleAfter'];
                    }
                    //修改职位
                    $jobEditData                          = self::editJob($announcementInfo);
                    $announcementInfo['modifyBeforeList'] = $jobEditData['modifyBeforeList'];
                    //新增职位
                    $jobAddData                      = self::createJob($announcementInfo);
                    $announcementInfo['authJobList'] = $jobAddData['authJobList'];
                    break;
                case Announcement::TYPE_EDITOR_OTHER:

                    break;
            }
            $announcementInfo['editorType']    = $announcementHandleLog['editor_type'] ?: Announcement::TYPE_EDITOR_OTHER;
            $announcementInfo['editorTypeTxt'] = Announcement::TYPE_EDITOR_LIST[$announcementHandleLog['editor_type']] ?: '';
            $announcementInfo['add_time']      = $announcementInfo['release_time'] ?: '';
            $historyStatus                     = 1;
            $historyStatusTitle                = "有审核通过历史";
        } else {
            //这里没有审核记录，展示详情内容
            $jobList = self::getAuditJobList($params['id']);

            $jobMajor     = [];
            $educationArr = [];
            foreach ($jobList as &$item) {
                $jobMajor[]     = $item['majorTxt'];
                $educationArr[] = $item['education_type'];
                //省份地区
                if ($item['provinceTxt']) {
                    $provinceArr[] = $item['provinceTxt'];
                }
                if ($item['cityTxt']) {
                    $cityArr[] = $item['cityTxt'];
                }
                //职位招聘人数
                if ($item['amount'] == '若干') {
                    $announcementInfo['amountCount'] = '若干';
                }

                unset($item['major_id'], $item['education_type'], $item['province_id'], $item['city_id'], $item['audit_status'], $item['is_negotiable']);
            }
            $announcementInfo['editorType'] = Announcement::TYPE_EDITOR_OTHER;

            //公告职位附件
            if (!empty($announcementInfo['file_ids'])) {
                $fileList = self::getAppendixList($announcementInfo['file_ids']);
            }

            $historyStatus      = 2;
            $historyStatusTitle = "无审核通过历史";
            //隐藏邮箱文本
            $announcementInfo['addressHideStatusText'] = self::ADDRESS_HIDE_STATUS_TEXT[$announcementInfo['address_hide_status']];
        }
        // 公告学科与职位学科整合
        $announcementInfo['majorTxt'] = self::integrationMajor($jobMajor, $announcementInfo['major_ids']);
        // 省份
        if (count($provinceArr > 1)) {
            $announcementInfo['areaProvinceTxt'] = implode(',', array_unique($provinceArr)) ?: '';
        } else {
            $announcementInfo['areaProvinceTxt'] = $provinceArr[0] ?: '';
        }
        // 城市
        if (count($cityArr > 1)) {
            $announcementInfo['areaCityTxt'] = implode(',', array_unique($cityArr)) ?: '';
        } else {
            $announcementInfo['areaCityTxt'] = $cityArr[0] ?: '';
        }

        //学历要求
        if (count($educationArr) > 1) {
            $educationMin                     = min($educationArr);
            $education                        = Dictionary::getEducationName($educationMin);
            $announcementInfo['educationTxt'] = !empty($education) ? Dictionary::getEducationName($educationMin) . ($educationMin == BaseDictionary::EDUCATION_DOCTOR_ID ? '' : '及以上') : '-';
        } else {
            $announcementInfo['educationTxt'] = !empty($educationArr) ? Dictionary::getEducationName($educationArr[0]) . ($educationArr[0] == BaseDictionary::EDUCATION_DOCTOR_ID ? '' : '及以上') : '-';
        }

        unset($announcementInfo['company_id'], $announcementInfo['major_ids'], $announcementInfo['apply_type']);
        unset($announcementInfo['work_area_id'], $announcementInfo['homeSubColumn'], $announcementInfo['homeSubColumnIdsArr']);

        // 审核处理历史列表
        $handleLogList = AnnouncementHandleLog::announcementHandleLog($params);
        // 文档属性
        $comboAttributeInfo = BaseArticleAttribute::getComboAttributeInfo($announcementInfo['article_id']);
        $announcementInfo   = array_merge($announcementInfo, $comboAttributeInfo);

        //背景图
        $announcementInfo['backgroundImg'] = FileHelper::getFullUrl(BaseFile::findOneVal(['id' => $announcementInfo['backgroundImgFileId']],
            'path'));

        //背景图2
        $announcementInfo['backgroundImg2'] = FileHelper::getFullUrl(BaseFile::findOneVal(['id' => $announcementInfo['backgroundImgFileId2']],
            'path'));

        //背景图3
        $announcementInfo['backgroundImg3'] = FileHelper::getFullUrl(BaseFile::findOneVal(['id' => $announcementInfo['backgroundImgFileId3']],
            'path'));

        // 获取调用栏目
        $announcementInfo['columnTxt'] = BaseArticleColumn::getArticleColumn($announcementInfo['article_id']);

        // 查询公告关联的活动
        $activityAnnouncementList = BaseHwActivityAnnouncement::find()
            ->alias('aa')
            ->leftJoin(['a' => BaseHwActivity::tableName()], 'aa.activity_id = a.id')
            ->where(['aa.announcement_id' => $announcementInfo['id']])
            ->select(['a.name'])
            ->column();

        $announcementInfo['activityAnnouncementListText'] = implode("、", $activityAnnouncementList);

        return [
            'baseInfo'           => $announcementInfo,
            'jobList'            => $jobList ?: [],
            'fileList'           => $fileList ?: [],
            'handleLogList'      => $handleLogList,
            'historyStatus'      => $historyStatus,
            'historyStatusTitle' => $historyStatusTitle,
            'isCooperation'      => (string)$isCooperation,
        ];
    }

    private static function editAnnouncement($announcementHandleLog)
    {
        $contentBefore                    = json_decode($announcementHandleLog['handle_before'], true);
        $contentAfter                     = json_decode($announcementHandleLog['handle_after'], true);
        $data['announcementHandleBefore'] = $contentBefore['content'] ?: '';
        $data['announcementHandleAfter']  = $contentAfter['content'] ?: '';
        $data['fileHandleBeforeIds']      = $contentBefore['file_ids'] ?: '';
        $data['fileHandleAfterIds']       = $contentAfter['file_ids'] ?: '';

        return $data;
    }

    /**
     * 获取职位附件记录数据
     * @param $fileHandleBeforeIds
     * @param $fileHandleAfterIds
     * @return array
     */
    private static function getJobFileAppendixData($fileHandleBeforeIds, $fileHandleAfterIds)
    {
        $fileBeforeIds = explode(',', $fileHandleBeforeIds);
        $fileAfterIds  = explode(',', $fileHandleAfterIds);
        $select        = [
            'id',
            'name',
            'path',
            'suffix',
        ];
        // 修改前附件记录
        $data['fileHandleBefore'] = File::find()
            ->select($select)
            ->where(['id' => $fileBeforeIds])
            ->asArray()
            ->all();
        foreach ($data['fileHandleBefore'] as &$v) {
            $v['path'] = FileHelper::getFullUrl($v['path']);
        }

        // 修改后附件记录
        $data['fileHandleAfter'] = File::find()
            ->select($select)
            ->where(['id' => $fileAfterIds])
            ->asArray()
            ->all();
        foreach ($data['fileHandleAfter'] as &$vv) {
            $vv['path'] = FileHelper::getFullUrl($vv['path']);
        }

        return $data;
    }

    /**
     * 获取公告编辑数据
     * @param $id
     * @return array
     */
    public static function getEditInfo($id)
    {
        $announcement = self::findOne(['id' => $id]);
        if (!$announcement) {
            throw new Exception('公告不存在');
        }

        $announcementDel = Article::findOneVal(['id' => $announcement->article_id], 'is_delete');
        if ($announcementDel == self::STATUS_ACTIVE) {
            throw new Exception('公告已被删除');
        }

        $auditStatus = Announcement::findOneVal(['id' => $id], 'audit_status');
        $status      = Article::findOneVal(['id' => $announcement->article_id], 'status');
        if ($auditStatus == self::STATUS_AUDIT_AWAIT || $status == Article::STATUS_OFFLINE) {
            throw new Exception('待审核或下线状态不支持操作编辑');
        }

        $select = [
            'a.id',
            'a.article_id',
            'a.title',
            'a.company_id',
            'a.period_date',
            'a.apply_type',
            'a.apply_address',
            'a.template_id',
            'a.audit_status',
            'a.file_ids',
            'a.delivery_type',
            'a.delivery_way',
            'a.is_attachment_notice',
            'a.extra_notify_address',
            'ar.status',
            'ar.home_column_id',
            'ar.home_sub_column_ids',
            'ar.content',
            'ar.cover_thumb',
            'ar.seo_description',
            'ar.seo_keywords',
            'ar.recommend_ids',
            'ar.tag_ids',
            'a.address_hide_status as addressHideStatus',
            'a.sub_title as subTitle',
            'a.highlights_describe as highlightsDescribe',
            'a.background_img_file_id as backgroundImgFileId',
            'a.background_img_file_id_2 as backgroundImgFileId2',
            'a.background_img_file_id_3 as backgroundImgFileId3',
            'a.background_img_file_type as backgroundImgFileType',
            'a.activity_job_content',
        ];

        $announcementInfo = self::find()
            ->alias('a')
            ->leftJoin(['ar' => Article::tableName()], 'ar.id = a.article_id')
            ->select($select)
            ->where(['a.id' => $id])
            ->asArray()
            ->one();

        // 文档属性
        $comboAttributeInfo = BaseArticleAttribute::getComboAttributeInfo($announcementInfo['article_id']);
        $announcementInfo   = array_merge($announcementInfo, $comboAttributeInfo);

        // 获取调用栏目
        $announcementInfo['columnTxt']         = BaseArticleColumn::getArticleColumn($announcementInfo['article_id']);
        $announcementInfo['delivery_type_txt'] = BaseAnnouncement::DELIVERY_TYPE_NAME[$announcementInfo['delivery_type']];

        // 如果是审核拒绝，回显拒绝的数据
        if ($announcementInfo['audit_status'] == self::STATUS_AUDIT_REFUSE) {
            $editContent = AnnouncementEdit::find()
                ->select('edit_content')
                ->where(['announcement_id' => $id])
                ->orderBy('id desc')
                ->asArray()
                ->one();

            $announcementInfo['content'] = json_decode($editContent['edit_content'],
                true)['content'] ?: $announcementInfo['content'];
        }
        //投递方式处理
        if (in_array($announcementInfo['delivery_way'], BaseAnnouncement::DELIVERY_WAY_EMAIL_LINK_LIST)) {
            $announcementInfo['delivery_way'] = (string)BaseAnnouncement::DELIVERY_WAY_EMAIL_LINK;
        } elseif (empty($announcementInfo['delivery_way'])) {
            $announcementInfo['delivery_way'] = '';
        }
        if ($announcementInfo['period_date'] == TimeHelper::ZERO_TIME) {
            $announcementInfo['period_date'] = '';
        }
        if (!$announcementInfo['template_id']) {
            $announcementInfo['template_id'] = '';
        }
        if (empty($announcementInfo['delivery_type'])) {
            $announcementInfo['delivery_type'] = '';
        }
        $companyModel                               = Company::findOne(['id' => $announcementInfo['company_id']]);
        $announcementInfo['companyTxt']             = $companyModel->full_name;
        $announcementInfo['companyDeliveryType']    = $companyModel->delivery_type;
        $announcementInfo['companyDeliveryTypeTxt'] = BaseCompany::DELIVERY_TYPE_NAME[$companyModel->delivery_type];
        $announcementInfo['isCooperation']          = (string)$companyModel->is_cooperation;
        $announcementInfo['natureTxt']              = Dictionary::getCompanyNatureName($companyModel->nature);
        $announcementInfo['typeTxt']                = Dictionary::getCompanyTypeName($companyModel->type);

        //背景图
        $announcementInfo['backgroundImg'] = FileHelper::getFullUrl(BaseFile::findOneVal(['id' => $announcementInfo['backgroundImgFileId']],
            'path'));

        //背景图2
        $announcementInfo['backgroundImg2'] = FileHelper::getFullUrl(BaseFile::findOneVal(['id' => $announcementInfo['backgroundImgFileId2']],
            'path'));

        //背景图3
        $announcementInfo['backgroundImg3'] = FileHelper::getFullUrl(BaseFile::findOneVal(['id' => $announcementInfo['backgroundImgFileId3']],
            'path'));

        // 公告职位
        $jobSelect = [
            'id',
            'name',
            'code',
            'amount',
            'department',
            'major_id',
            'education_type',
            'province_id',
            'city_id',
            'audit_status',
            'status',
            'wage_type',
            'min_wage',
            'max_wage',
            'company_id',
            'first_release_time',
            'create_type',
            'delivery_type',
        ];

        $announcementJobList = Job::find()
            ->select($jobSelect)
            ->where([
                'announcement_id' => $announcementInfo['id'],
            ])
            ->andWhere([
                '<>',
                'status',
                BaseJob::STATUS_DELETE,
            ])
            ->orderBy('status desc,refresh_time desc')
            ->asArray()
            ->all();

        foreach ($announcementJobList as &$item) {
            $majorArr             = explode(',', $item['major_id']);
            $item['majorTxt']     = Major::getAllMajorName($majorArr) ?: '';
            $item['educationTxt'] = Dictionary::getEducationName($item['education_type']) ?: '';
            $item['provinceTxt']  = Area::getAreaName($item['province_id']) ?: '';
            $item['areaTxt']      = Area::getAreaName($item['city_id']) ?: '';
            $item['isTemp']       = BaseJobTemp::IS_TEMP_NO;
            if ($item['first_release_time'] != TimeHelper::ZERO_TIME) {
                $item['statusTxt']      = BaseJob::JOB_SEARCH_STATUS_NAME[$item['status']];
                $item['auditStatusTxt'] = BaseJob::JOB_AUDIT_STATUS_NAME[$item['audit_status']];
            } else {
                $item['statusTxt']      = '待发布';
                $item['auditStatusTxt'] = $item['audit_status'] == BaseJob::AUDIT_STATUS_WAIT ? '-' : BaseJob::JOB_AUDIT_STATUS_NAME[$item['audit_status']];
            }
            if (!$item['min_wage'] && !$item['max_wage']) {
                $item['wage'] = '面议';
            } else {
                $item['wage'] = BaseJob::formatWage($item['min_wage'], $item['max_wage'], $item['wage_type']) ?: '-';
            }
            if ($item['provinceTxt']) {
                $provinceArr[] = $item['provinceTxt'];
            }
            if ($item['areaTxt']) {
                $cityArr[] = $item['areaTxt'];
            }
            //学历要求
            if ($item['educationTxt']) {
                $educationArr[] = $item['educationTxt'];
            }

            //检查是否有投递数据
            $jobApply      = BaseOffSiteJobApply::findOne(['job_id' => $item['id']]);
            $isCooperation = Company::findOneVal(['id' => $item['company_id']], 'is_cooperation');
            // 有投递数据or待审核状态or合作单位&有审核通过历史or单位发布&没有审核通过历史&审核拒绝职位不可删除
            if ($jobApply) {
                $item['canDel'] = false;
            } elseif (!empty($item['first_release_time']) && $item['first_release_time'] != TimeHelper::ZERO_TIME && $isCooperation == Company::COOPERATIVE_UNIT_YES) {
                $item['canDel'] = false;
            } elseif ($item['audit_status'] == Job::AUDIT_STATUS_WAIT_AUDIT) {
                $item['canDel'] = false;
            } elseif (!empty($item['first_release_time']) && $item['first_release_time'] == TimeHelper::ZERO_TIME && $item['create_type'] == Job::CREATE_TYPE_SELF && $item['audit_status'] == Job::AUDIT_STATUS_REFUSE_AUDIT) {
                $item['canDel'] = false;
            } else {
                $item['canDel'] = true;
            }
            $item['job_contact']             = Job::getJobContact($item['id']);
            $item['job_contact_synergy']     = Job::getJobContactSynergy($item['id']);
            $item['job_contact_synergy_num'] = count($item['job_contact_synergy']);
            $information                     = [];
            if ($item['areaTxt']) {
                array_push($information, $item['areaTxt']);
            }
            if ($item['amount']) {
                array_push($information, "招{$item['amount']}人");
            }
            if ($item['educationTxt']) {
                array_push($information, $item['educationTxt']);
            }
            if ($item['wage']) {
                array_push($information, $item['wage']);
            }
            if ($item['majorTxt']) {
                array_push($information, $item['majorTxt']);
            }
            $item['information'] = implode(' | ', $information);
            unset($item['major_id'], $item['education_type'], $item['province_id'], $item['city_id'], $item['audit_status']);
        }

        //公告职位附件
        $fileList = [];
        if (!empty($announcementInfo['file_ids'])) {
            $fileList = self::getAppendixList($announcementInfo['file_ids']);
        }

        // 获取活动设置相关（关联的活动）
        if ($announcementInfo) {
            $activityAnnouncementList = BaseHwActivityAnnouncement::find()
                ->where(['announcement_id' => $announcementInfo['id']])
                ->select(['activity_id'])
                ->column();

            $announcementInfo['activity_announcement'] = implode(',', $activityAnnouncementList);
        }

        return compact('announcementInfo', 'announcementJobList', 'fileList');
    }

    private static function createJob($announcementInfo)
    {
        $authJobList = self::getAddJobList($announcementInfo['id']);
        foreach ($authJobList as $item) {
            $majorArr             = explode(',', $item['major_id']);
            $item['majorTxt']     = Major::getAllMajorName($majorArr) ?: '-';
            $item['educationTxt'] = Dictionary::getEducationName($item['education_type']) ?: '-';
            $item['provinceTxt']  = Area::getAreaName($item['province_id']) ?: '';
            $item['cityTxt']      = Area::getAreaName($item['city_id']) ?: '';
            //薪资wage_id回显
            if ($item['is_negotiable'] <> 1) {
                $item['wage_id'] = (string)BaseJob::getWageId($item['min_wage'], $item['max_wage']);
            }
            if (!$item['min_wage'] && !$item['max_wage']) {
                $item['wage'] = '面议';
            } else {
                $item['wage'] = BaseJob::formatWage($item['min_wage'], $item['max_wage'], $item['wage_type']) ?: '-';
            }

            if ($item['provinceTxt']) {
                $provinceArr[] = $item['provinceTxt'];
            }
            if ($item['cityTxt']) {
                $cityArr[] = $item['cityTxt'];
            }
            //学历要求
            if ($item['educationTxt']) {
                $educationArr[] = $item['educationTxt'];
            }
            if ($item['period_date'] == TimeHelper::ZERO_TIME) {
                $item['period_date'] = '详见正文';
            } else {
                $item['period_date'] = substr($item['period_date'], 0, 10);
            }

            $information = [];
            if ($item['cityTxt']) {
                array_push($information, $item['cityTxt']);
            }
            if ($item['amount']) {
                array_push($information, "招{$item['amount']}人");
            }
            if ($item['educationTxt']) {
                array_push($information, $item['educationTxt']);
            }
            if ($item['wage']) {
                array_push($information, $item['wage']);
            }
            if ($item['major_id']) {
                array_push($information, $item['majorTxt']);
            }
            $item['information'] = implode(' | ', $information);
            // 空转'-'
            $item['code']       = StringHelper::isEmpty($item['code']);
            $item['department'] = StringHelper::isEmpty($item['department']);
            //职位联系人
            $contact             = BaseJob::getJobContact($item['id']);
            $item['job_contact'] = $contact;
            //职位协同账号
            $contact_synergy                 = BaseJob::getJobContactSynergy($item['id']);
            $item['job_contact_synergy']     = $contact_synergy;
            $item['job_contact_synergy_num'] = count($contact_synergy);

            unset($item['major_id'], $item['education_type'], $item['province_id'], $item['city_id']);
            $data['authJobList'][] = $item;
        }

        return $data;
    }

    private static function editJob($announcementInfo)
    {
        // 获取职位数据
        $select  = [
            'file_ids',
            'duty',
            'requirement',
            'remark',
            'announcement_id',
            'status',
            'name',
            'id',
        ];
        $jobInfo = Job::selectInfos(['announcement_id' => $announcementInfo['id']], $select);

        //获取编辑的数据
        $jobEditInfo = JobEdit::find()
            ->where([
                'announcement_id' => $announcementInfo['id'],
                'status'          => JobEdit::STATUS_ONLINE,
            ])
            ->select('job_id,edit_content')
            ->asArray()
            ->all();

        $modifyBeforeList = [];
        foreach ($jobInfo as $jobVal) {
            // 修改后的数据
            foreach ($jobEditInfo as $k => $editVal) {
                if ($jobVal['id'] == $editVal['job_id']) {
                    $editContent = json_decode($editVal['edit_content'], true);
                    $jobUid      = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $editVal['job_id']);
                    $editKey     = array_keys($editContent);

                    if (!in_array('duty', $editKey)) {
                        unset($jobVal['duty']);
                    }
                    if (!in_array('requirement', $editKey)) {
                        unset($jobVal['requirement']);
                    }
                    if (!in_array('remark', $editKey)) {
                        unset($jobVal['remark']);
                    }
                    if (!in_array('file_ids', $editKey)) {
                        unset($jobVal['file_ids']);
                    }
                    if ($jobVal['file_ids']) {
                        $jobVal['file_list'] = self::getAppendixList($jobVal['file_ids']);
                    } else {
                        $jobVal['file_list'] = [];
                    }
                    if ($editContent['file_ids']) {
                        $editContent['file_list'] = self::getAppendixList($editContent['file_ids']);
                    } else {
                        $editContent['file_list'] = [];
                    }
                    $modifyBeforeList['jobName']         = $jobVal['name'] . '(' . $jobUid . ')';
                    $modifyBeforeList['jobHandleBefore'] = $jobVal;
                    $modifyBeforeList['jobHandleAfter']  = $editContent;
                    $data['modifyBeforeList'][$k]        = $modifyBeforeList;
                }
            }
        }

        return $data;
    }

    /**
     * 获取审核的职位列表
     * @param $announcementId
     * @return array|\yii\db\ActiveRecord[]
     */
    private static function getAuditJobList($announcementId)
    {
        $select = [
            'id',
            'name',
            'code',
            'major_id',
            'education_type',
            'amount',
            'department',
            'province_id',
            'city_id',
            'audit_status',
            'is_negotiable',
            'min_wage',
            'max_wage',
            'wage_type',
            'period_date',
            'status',
        ];

        $list = Job::find()
            ->where([
                'announcement_id' => $announcementId,
                'is_show'         => Job::IS_SHOW_YES,
            ])
            ->andWhere([
                'status' => [
                    Job::STATUS_WAIT,
                    Job::STATUS_ONLINE,
                ],
            ])
            ->andWhere([
                'in',
                'audit_status',
                [
                    Job::AUDIT_STATUS_WAIT_AUDIT,
                    Job::AUDIT_STATUS_REFUSE_AUDIT,
                    Job::AUDIT_STATUS_WAIT,
                ],
            ])
            ->select($select)
            ->orderBy('add_time desc')
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $majorArr             = explode(',', $item['major_id']);
            $item['majorTxt']     = Major::getAllMajorName($majorArr) ?: '-';
            $item['educationTxt'] = Dictionary::getEducationName($item['education_type']) ?: '-';
            $item['provinceTxt']  = Area::getAreaName($item['province_id']) ?: '';
            $item['cityTxt']      = Area::getAreaName($item['city_id']) ?: '';
            //薪资wage_id回显
            if ($item['is_negotiable'] <> 1) {
                $item['wage_id'] = (string)BaseJob::getWageId($item['min_wage'], $item['max_wage']);
            }
            if (!$item['min_wage'] && !$item['max_wage']) {
                $item['wage'] = '面议';
            } else {
                $item['wage'] = BaseJob::formatWage($item['min_wage'], $item['max_wage'], $item['wage_type']) ?: '-';
            }

            if ($item['provinceTxt']) {
                $provinceArr[] = $item['provinceTxt'];
            }
            if ($item['cityTxt']) {
                $cityArr[] = $item['cityTxt'];
            }
            //学历要求
            if ($item['educationTxt']) {
                $educationArr[] = $item['educationTxt'];
            }
            if ($item['period_date'] == TimeHelper::ZERO_TIME) {
                $item['period_date'] = '详见正文';
            } else {
                $item['period_date'] = substr($item['period_date'], 0, 10);
            }

            $information = [];
            if ($item['cityTxt']) {
                array_push($information, $item['cityTxt']);
            }
            if ($item['amount']) {
                array_push($information, "招{$item['amount']}人");
            }
            if ($item['educationTxt']) {
                array_push($information, $item['educationTxt']);
            }
            if ($item['wage']) {
                array_push($information, $item['wage']);
            }
            if ($item['major_id']) {
                array_push($information, $item['majorTxt']);
            }
            $item['information'] = implode(' | ', $information);
            // 空转'-'
            $item['code']       = StringHelper::isEmpty($item['code']);
            $item['department'] = StringHelper::isEmpty($item['department']);
            //职位联系人
            $item['job_contact'] = BaseJob::getJobContact($item['id']);
            //职位协同账号
            $item['job_contact_synergy']     = BaseJob::getJobContactSynergy($item['id']);
            $item['job_contact_synergy_num'] = count($item['job_contact_synergy']);
        }

        return $list;
    }

    /**
     * 获取新增的职位列表
     * @param $announcementId
     * @return array|\yii\db\ActiveRecord[]
     */
    private static function getAddJobList($announcementId)
    {
        $select = [
            'id',
            'name',
            'code',
            'major_id',
            'education_type',
            'amount',
            'department',
            'province_id',
            'city_id',
            'audit_status',
            'is_negotiable',
            'min_wage',
            'max_wage',
            'wage_type',
            'period_date',
            'status',
        ];

        $list = Job::find()
            ->where([
                'announcement_id' => $announcementId,
                'is_show'         => Job::IS_SHOW_YES,
            ])
            ->andWhere([
                'status' => Job::STATUS_WAIT,
            ])
            ->andWhere([
                'in',
                'audit_status',
                [
                    Job::AUDIT_STATUS_WAIT_AUDIT,
                    Job::AUDIT_STATUS_REFUSE_AUDIT,
                    Job::AUDIT_STATUS_WAIT,
                ],
            ])
            ->select($select)
            ->orderBy('add_time desc')
            ->asArray()
            ->all();

        return $list;
    }

    /**
     * 统计模型对应数量
     * @throws Exception
     */
    public static function getTemplateAmount($Ids, $homeColumnId): array
    {
        $oldHome = BaseAnnouncement::find()
            ->alias('a')
            ->leftJoin(['j' => BaseJob::tableName()], 'j.announcement_id = a.id')
            ->leftJoin(['art' => BaseArticle::tableName()], 'art.id=a.article_id')
            ->leftJoin(['h' => BaseHomeColumn::tableName()], 'h.id=art.home_column_id')
            ->select([
                'h.template_type',
                'a.id',
            ])
            ->where([
                'in',
                'a.id',
                $Ids,
            ])
            ->asArray()
            ->all();

        $checkTemplateType = BaseHomeColumn::findOneVal(['id' => $homeColumnId], 'template_type');

        $adoptIds   = [];
        $excludeIds = [];
        foreach ($oldHome as $item) {
            if ($item['template_type'] == $checkTemplateType) {
                $adoptIds[] = UUIDHelper::encrypt(UUIDHelper::TYPE_ANNOUNCEMENT, $item['id']);
            } else {
                $excludeIds[] = UUIDHelper::encrypt(UUIDHelper::TYPE_ANNOUNCEMENT, $item['id']);
            }
        }
        if (sizeof($adoptIds) == sizeof($Ids)) {
            $list = [
                'type'   => 1,
                'amount' => sizeof($Ids),
            ];
        } else {
            $exclude = '未成功操作的公告编号：';

            foreach ($excludeIds as $eid) {
                $exclude .= $eid . ',';
            }
            $exclude = substr($exclude, 0, -1);
            $list    = [
                'type'   => 2,
                'amount' => sizeof($adoptIds),
                'fail'   => $exclude,
            ];
        }

        return $list;
    }

    /**
     * 公告操作日志
     * @param $keywords
     * @return array
     * @throws \Exception
     */
    public static function getAnnouncementHandleLog($keywords): array
    {
        $select = [
            'add_time',
            'ip',
            'handle_type',
            'editor_type',
            'handler_name',
            'handle_before',
            'handle_after',
        ];

        $query = BaseAnnouncementHandleLog::find()
            ->select($select)
            ->where(['announcement_id' => $keywords['id']])
            ->andWhere(['status' => 1]);

        $query->andFilterCompare('handler_name', $keywords['handlerName'], 'like');
        $query->andFilterCompare('handle_type', $keywords['handleType']);

        if ($keywords['addTimeStart']) {
            $query->andWhere([
                '>=',
                'add_time',
                TimeHelper::dayToBeginTime($keywords['addTimeStart']),
            ]);
        }
        if ($keywords['addTimeEnd']) {
            $query->andWhere([
                '<=',
                'add_time',
                TimeHelper::dayToEndTime($keywords['addTimeEnd']),
            ]);
        }

        $count    = $query->count();
        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);

        $announcementHandleLog = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('add_time desc')
            ->asArray()
            ->all();

        $handleTypeName = BaseAnnouncementHandleLog::HANDLE_TYPE_NAME;
        foreach ($announcementHandleLog as $k => $log) {
            $announcementHandleLog[$k]['ip']                = long2ip($log['ip']);
            $announcementHandleLog[$k]['handle_type_title'] = $handleTypeName[$log['handle_type']];
            $announcementHandleLog[$k]['handle_before']     = json_decode('[' . $log['handle_before'] . ']', true);
            $announcementHandleLog[$k]['handle_after']      = json_decode('[' . $log['handle_after'] . ']', true);
        }

        return [
            'list'   => $announcementHandleLog,
            'amount' => $count,
            'page'   => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
        ];
    }

    /**
     * 业务-获取站内投递列表（原公告收到的简历）
     * @param $keywords
     * @return array
     * @throws \Exception
     */
    public static function getAnnouncementJobApplyList($keywords): array
    {
        $jobIds = BaseAnnouncement::getAnnouncementJobIds($keywords['id']);

        $select = [
            'a.add_time',
            'a.id',
            'a.status',
            'a.resume_name',
            'a.resume_id',
            'a.job_id',
            'a.source',
            'a.is_check',
            'a.resume_attachment_id',
            'a.job_name',
            'a.is_invitation',
            'jar.delivery_way',
        ];
        $query  = JobApply::find()
            ->alias('a')
            ->select($select)
            ->leftJoin(['jar' => BaseJobApplyRecord::tableName()], 'a.id = jar.apply_id')
            ->where([
                'a.job_id'          => $jobIds,
                'jar.delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_INSIDE,
            ]);

        //这里走个分流
        if ($keywords['resume_name']) {
            if (is_numeric($keywords['resume_name'])) {
                if (strlen($keywords['resume_name']) == 8) {
                    $nameChangeUid = UUIDHelper::decryption($keywords['resume_name']);
                    $query->andFilterCompare('a.resume_id', (int)$nameChangeUid);
                } else {
                    $query->andFilterCompare('a.resume_id', (int)$keywords['resume_name']);
                }
            } else {
                $query->andFilterCompare('a.resume_name', $keywords['resume_name'], 'like');
            }
        }
        //投递方式
        $query->andFilterCompare('jar.delivery_way', $keywords['delivery_way']);
        if ($keywords['add_time_start']) {
            $query->andWhere([
                '>=',
                'a.add_time',
                TimeHelper::dayToBeginTime($keywords['add_time_start']),
            ]);
        }
        if ($keywords['add_time_end']) {
            $query->andWhere([
                '<=',
                'a.add_time',
                TimeHelper::dayToEndTime($keywords['add_time_end']),
            ]);
        }
        $orderBy = ' a.add_time desc';

        $count   = $query->count();
        $allList = $query->asArray()
            ->all();

        //这里统计,确保已经有投递过的数据
        $tempIds = [];
        foreach ($allList as $item) {
            if (!in_array($item['job_id'], $tempIds)) {
                $tempIds[] = $item['job_id'];
            }
        }
        $jobAmount = sizeof($tempIds);

        $pageSize     = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages        = self::setPage($count, $keywords['page'], $pageSize);
        $jobApplyList = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        DebugHelper::writeLog($query->createCommand()
            ->getRawSql(), '站内投递');

        $statusList = BaseJobApply::STATUS_LIST;
        foreach ($jobApplyList as $k => $list) {
            $jobApplyList[$k]['job_uid']                 = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $list['job_id']);
            $jobApplyList[$k]['resume_uid']              = UUIDHelper::encrypt(UUIDHelper::TYPE_PERSON,
                $list['resume_id']);
            $jobApplyList[$k]['source_title']            = BaseJobApply::SOURCE_LIST[$list['source']];
            $jobApplyList[$k]['status_title']            = $statusList[$list['status']];
            $jobApplyList[$k]['delivery_way_txt']        = BaseJobApplyRecord::DELIVERY_WAY_NAME[$list['delivery_way']];
            $jobApplyList[$k]['resume_attachment_title'] = "";
            if (intval($list['resume_attachment_id']) > 0) {
                $fileName = BaseResumeAttachment::findOneVal(['id' => $list['resume_attachment_id']], 'file_name');
                if ($fileName) {
                    $jobApplyList[$k]['resume_attachment_title'] = $fileName;
                }
            }
        }

        return [
            'list'       => $jobApplyList,
            'amount'     => $count,
            'page'       => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
            'statistics' => [
                'allJobApplyAmount' => (int)$count,
                'allJobAmount'      => $jobAmount,
            ],
        ];
    }

    /**
     * 业务-获取站外投递列表
     * @param $keywords
     * @return array
     * @throws \Exception
     */
    public static function getOutsideApplyList($keywords)
    {
        //获取公告下的职位ID数组
        $jobIds = BaseAnnouncement::getAnnouncementJobIds($keywords['id']);
        $query  = BaseOffSiteJobApply::find()
            ->alias('osja')
            ->select([
                'j.id as job_id',
                'j.name as job_name',
                'osja.resume_id',
                'osja.add_time',
                'osja.apply_status',
                'osja.email',
                'r.name as resume_name',
                'ra.file_name',
                'jar.delivery_way',
            ])
            ->leftJoin(['j' => BaseJob::tableName()], 'j.id=osja.job_id')
            ->leftJoin(['jar' => BaseJobApplyRecord::tableName()], 'osja.id = jar.apply_site_id')
            ->leftJoin(['ra' => BaseResumeAttachment::tableName()], 'osja.resume_attachment_id=ra.id')
            ->leftJoin(['r' => BaseResume::tableName()], 'osja.resume_id=r.id')
            ->andWhere([
                'osja.source'       => BaseOffSiteJobApply::SOURCE_WEBSITE,
                'jar.delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE,
            ])
            ->andWhere([
                '>',
                'osja.announcement_id',
                0,
            ])
            ->andWhere([
                'in',
                'osja.job_id',
                $jobIds,
            ]);

        //投递时间筛选
        if ($keywords['add_time_start'] && $keywords['add_time_end']) {
            $query->andWhere([
                'and',
                [
                    '>=',
                    'osja.add_time',
                    $keywords['add_time_start'],
                ],
                [
                    '<=',
                    'osja.add_time',
                    $keywords['add_time_end'],
                ],
            ]);
        }
        //搜索框 --人才姓名/人才编号
        if ($keywords['resume_name']) {
            if (is_numeric($keywords['resume_name'])) {
                if (strlen($keywords['resume_name']) == 8) {
                    $nameId = UUIDHelper::decryption($keywords['resume_name']);
                    $query->andWhere(['osja.resume_id' => $nameId]);
                }
            } else {
                $query->andFilterCompare('r.name', $keywords['resume_name']);
            }
        }
        //投递方式
        $query->andFilterWhere(['jar.delivery_way' => $keywords['delivery_way']]);
        $count         = $query->count();
        $queryJobTotal = clone $query;
        $jobTotal      = $queryJobTotal->groupBy('osja.job_id')
            ->count();
        $pageSize      = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages         = self::setPage($count, $keywords['page'], $pageSize);
        $list          = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('osja.id desc')
            ->asArray()
            ->all();
        DebugHelper::writeLog($query->createCommand()
            ->getRawSql(), '非合作站外投递-混合');

        foreach ($list as &$v) {
            $v['job_id']    = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $v['job_id']);
            $v['resume_id'] = UUIDHelper::encrypt(UUIDHelper::TYPE_PERSON, $v['resume_id']);
            if (!empty($v['email'])) {
                $v['applytxt'] = '邮件';
            } else {
                $v['applytxt'] = '网址';
            }
            $v['delivery_way_txt'] = BaseJobApplyRecord::DELIVERY_WAY_NAME[$v['delivery_way']];
            $v['applyProgramTxt']  = BaseOffSiteJobApply::APPLY_STATUS_LIST[$v['apply_status']] ?: '-';
            $v['file_name']        = $v['file_name'] ?: '-';
            unset($v['email'], $v['apply_status']);
        }

        return [
            'list'     => $list,
            'jobTotal' => $jobTotal,
            'page'     => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
        ];
    }

    /**
     * 业务-公告面试邀约
     * @param $keywords
     * @return array
     * @throws \Exception
     */
    public static function getAnnouncementInterviewList($keywords): array
    {
        $jobIds = BaseAnnouncement::getAnnouncementJobIds($keywords['id']);

        $select = [
            'j.resume_id',
            'j.job_id',
            'j.resume_name',
            'j.add_time as apply_time',
            'c.id',
            'c.add_time',
            'c.interview_time',
            'c.job_name',
            'c.contact',
            'c.telephone',
            'c.address',
            'GROUP_CONCAT(c.id SEPARATOR "|") as ids',
        ];
        $query  = JobApply::find()
            ->alias('j')
            ->rightJoin(['c' => BaseCompanyInterview::tableName()], 'c.job_apply_id = j.id')
            ->select($select)
            ->where([
                'j.job_id' => $jobIds,
            ])
            ->groupBy('c.job_apply_id');

        $query->andFilterCompare('j.is_invitation', BaseJobApply::IS_INVITATION_YES);

        if ($keywords['resume_name']) {
            if (is_numeric($keywords['resume_name'])) {
                if (strlen($keywords['resume_name']) == 8) {
                    $nameChangeUid = UUIDHelper::decryption($keywords['resume_name']);
                    $query->andFilterCompare('j.resume_id', (int)$nameChangeUid);
                } else {
                    $query->andFilterCompare('j.resume_id', (int)$keywords['resume_name']);
                }
            } else {
                $query->andFilterCompare('j.resume_name', $keywords['resume_name'], 'like');
            }
        }

        if ($keywords['interview_time_start']) {
            $query->andWhere([
                '>=',
                'c.interview_time',
                TimeHelper::dayToBeginTime($keywords['interview_time_start']),
            ]);
        }
        if ($keywords['interview_time_end']) {
            $query->andWhere([
                '<=',
                'c.interview_time',
                TimeHelper::dayToEndTime($keywords['interview_time_end']),
            ]);
        }

        if ($keywords['add_time_start']) {
            $query->andWhere([
                '>=',
                'c.add_time',
                TimeHelper::dayToBeginTime($keywords['add_time_start']),
            ]);
        }
        if ($keywords['add_time_end']) {
            $query->andWhere([
                '<=',
                'c.add_time',
                TimeHelper::dayToEndTime($keywords['add_time_end']),
            ]);
        }

        $orderBy = ' c.add_time desc';

        $count = $query->count();

        $allList = $query->asArray()
            ->all();

        //这里统计,确保已经有面试邀约
        $tempIds = [];
        foreach ($allList as $item) {
            if (!in_array($item['job_id'], $tempIds)) {
                $tempIds[] = $item['job_id'];
            }
        }
        $jobAmount = sizeof($tempIds);

        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);

        $companyInterviewList = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        foreach ($companyInterviewList as $k => $list) {
            $companyInterviewList[$k]['job_uid']    = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $list['job_id']);
            $companyInterviewList[$k]['resume_uid'] = UUIDHelper::encrypt(UUIDHelper::TYPE_PERSON, $list['resume_id']);
            $interviewAmount                        = BaseAnnouncement::number2chinese(sizeof(explode('|',
                $list['ids'])));

            $companyInterviewList[$k]['interview_amount'] = $interviewAmount . '面';
        }

        return [
            'list'       => $companyInterviewList,
            'amount'     => $count,
            'page'       => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
            'statistics' => [
                'allInterviewAmount' => (int)$count,
                'allJobAmount'       => $jobAmount,
            ],
        ];
    }

    /**
     * 业务-下载的简历
     * @param $keywords
     * @return array
     * @throws \Exception
     */
    public static function getResumeDownloadLog($keywords): array
    {
        $jobIds  = BaseAnnouncement::getAnnouncementJobIds($keywords['id']);
        $select  = [
            'add_time',
            'id',
            'job_id',
            'resume_name',
            'resume_id',
            'resume_attachment_id',
        ];
        $where   = ['and'];
        $where[] = ['handler_type' => BaseResumeDownloadLog::HANDLE_TYPE_ADMIN];

        $query = ResumeDownloadLog::find()
            ->select($select)
            ->where($where)
            ->andWhere([
                'in',
                'job_id',
                $jobIds,
            ]);

        $query->andFilterCompare('resume_name', $keywords['resume_name'], 'like');
        if ($keywords['download_time_start']) {
            $query->andWhere([
                '>=',
                'add_time',
                TimeHelper::dayToBeginTime($keywords['download_time_start']),
            ]);
        }
        if ($keywords['download_time_end']) {
            $query->andWhere([
                '<=',
                'add_time',
                TimeHelper::dayToEndTime($keywords['download_time_end']),
            ]);
        }
        $orderBy = ' add_time desc';

        $count = $query->count();

        $allList = $query->asArray()
            ->all();

        //这里统计,确保已经有面试邀约
        $allResumeIds = array_column($allList, 'resume_id');
        $resumeIds    = [];
        foreach ($allResumeIds as $Id) {
            if (!in_array($Id, $resumeIds)) {
                $resumeIds[] = $Id;
            }
        }
        $resumeAmount = sizeof($resumeIds);

        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        //职位-下载简历列表
        $jobResumeDownloadList = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->groupBy('id')
            ->asArray()
            ->all();

        foreach ($jobResumeDownloadList as $k => $list) {
            $jobResumeDownloadList[$k]['id']                    = intval($list['id']);
            $jobResumeDownloadList[$k]['downloadTime']          = $list['add_time'];
            $jobResumeDownloadList[$k]['resumeAttachmentTitle'] = "";
            if ($list['resumeId'] > 0) {
                $jobResumeDownloadList[$k]['resume_type_title']       = "在线简历";
                $jobResumeDownloadList[$k]['resume_attachment_title'] = "简历" . $list['resumeId'];
            } else {
                $jobResumeDownloadList[$k]['resume_type_title'] = "附件简历";

                $resumeAdditionalInfo = BaseResumeAdditionalInfo::find()
                    ->where(['id' => intval($list['resumeAttachmentId'])])
                    ->select(['theme_name'])
                    ->asArray()
                    ->one();
                if ($resumeAdditionalInfo) {
                    $jobResumeDownloadList[$k]['resume_attachment_title'] = "(" . $resumeAdditionalInfo['theme_name'] . ") " . intval($list['resumeAttachmentId']);
                }
            }
        }

        return [
            'list'       => $jobResumeDownloadList,
            'amount'     => $count,
            'page'       => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
            'statistics' => [
                'allDownloadAmount' => (int)$count,
                'allResumeAmount'   => $resumeAmount,
            ],
        ];
    }

    /**
     * 业务-公告操作日志下操作类型
     * @return array
     * @throws \Exception
     */
    public static function getHandleTypeList(): array
    {
        $handleTypeList = BaseAnnouncementHandleLog::HANDLE_TYPE_NAME;

        return ArrayHelper::obj2Arr($handleTypeList);
    }

    /**
     * 获取回收站列表
     * @param $params
     * @return array
     * @throws Exception
     */
    public static function getRecycleList($params): array
    {
        $select = [
            'a.id as aid',
            'a.uuid as announcementUid',
            'a.title',
            'a.company_id',
            'a.article_id',
            'a.creator_id',
            'ar.home_column_id',
            'ar.home_sub_column_ids',
            'ar.click',
            'ar.update_time',
            'ar.is_delete',
            'ar.status',
            'ar.delete_time',
            'c.full_name',
            'c.uuid as companyUid',
        ];

        $query = self::find()
            ->alias('a')
            ->innerJoin(['ar' => Article::tableName()], 'ar.id = a.article_id')
            ->leftJoin(['c' => Company::tableName()], 'c.id = a.company_id')
            ->select($select)
            ->where([
                'ar.is_delete'     => self::STATUS_ACTIVE,
                'c.is_cooperation' => self::IS_COOPERATION_NO,
            ])
            ->groupBy(['aid']);

        JobApply::uidJudgeWhere($params['titleNum'], 'a.id', 'a.title', $query);

        // 所属栏目
        $query->andFilterCompare('ar.home_column_id', $params['homeColumnId']);
        // 所属单位
        if ($params['companyName']) {
            $companyId = Company::findOneVal([
                '=',
                'full_name',
                $params['companyName'],
            ], 'id');
            $query->andFilterCompare('a.company_id', $companyId);
        }
        // 发布人名称
        if ($params['creator']) {
            $createId = Admin::findOneVal([
                '=',
                'name',
                $params['creator'],
            ], 'id');
            $query->andFilterCompare('a.creator_id', $createId);
        }

        // 删除时间
        if ($params['deleteTimeStart']) {
            $query->andWhere([
                '>=',
                'ar.delete_time',
                TimeHelper::dayToBeginTime($params['deleteTimeStart']),
            ]);
        }
        if ($params['deleteTimeEnd']) {
            $query->andWhere([
                '<=',
                'ar.delete_time',
                TimeHelper::dayToEndTime($params['deleteTimeEnd']),
            ]);
        }

        $count    = $query->count();
        $pageSize = $params['pageSize'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $params['page'], $pageSize);

        $orderBy = 'ar.delete_time desc';
        if ($params['sortDeleteTime']) {
            $sort    = $params['sortDeleteTime'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = 'ar.delete_time' . $sort;
        }
        if ($params['sortUpdateTime']) {
            $sort    = $params['sortUpdateTime'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = 'ar.update_time' . $sort;
        }

        $list = $query->offset($pages['offset'])
            ->select($select)
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $columnName = HomeColumn::findOneVal(['id' => $item['home_column_id']], 'name');
            if (!empty($item['home_sub_column_ids'])) {
                $columnIdsArr = explode(',', $item['home_sub_column_ids']);
                $columnArr    = HomeColumn::getSubColumnName($columnIdsArr);
                array_push($columnArr, $columnName);
                $item['columnName'] = implode(';', array_unique($columnArr));
            } else {
                $item['columnName'] = $columnName;
            }
            $item['creatorName'] = Admin::findOneVal(['id' => $item['creator_id']], 'name');
            $item['companyName'] = $item['full_name'] . '(' . $item['companyUid'] . ')';
            unset($item['full_name'], $item['article_id'], $item['creator_id'], $item['home_column_id'], $item['home_sub_column_ids']);
        }

        return [
            'list'  => $list,
            'pages' => [
                'size'  => (int)$pageSize,
                'total' => (int)$count,
            ],
        ];
    }

    public static function majorMessageNotice($announcementId)
    {
        $majorId = BaseMajor::HIDE_MAJOR_ID;
        if (BaseJobMajorRelation::find()
            ->where([
                'announcement_id' => $announcementId,
                'major_id'        => $majorId,
            ])
            ->exists()) {
            $jobIds   = BaseJobMajorRelation::find()
                ->where([
                    'announcement_id' => $announcementId,
                    'major_id'        => $majorId,
                ])
                ->select('job_id')
                ->column();
            $jobUuids = BaseJob::find()
                ->where([
                    'id' => $jobIds,
                ])
                ->select('uuid')
                ->column();

            return [
                'msg' => '该公告下职位关联学科【电子信息】已不存在,职位ID:' . implode('、', $jobUuids),
                'res' => false,
            ];
        }

        return [
            'msg' => '再发布成功',
            'res' => true,
        ];
    }
}
