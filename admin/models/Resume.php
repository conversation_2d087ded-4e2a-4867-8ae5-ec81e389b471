<?php

namespace admin\models;

use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEquityAdminSetting;
use common\base\models\BaseResumeEquityPackage;
use common\base\models\BaseResumeEquityPackageCategorySetting;
use common\base\models\BaseResumeEquityPackageDetail;
use common\components\MessageException;
use common\helpers\UUIDHelper;
use yii\base\Exception;
use Yii;

class Resume extends BaseResume
{
    /**
     * 获取会员信息
     * @param $resumeId
     * @return array|\yii\db\ActiveRecord|null
     * @throws Exception
     */
    public static function getVipInfo($resumeId)
    {
        //验证参数合法性
        if (empty($resumeId)) {
            throw new Exception('参数错误');
        }
        //获取简历信息
        $resumeInfo = BaseResume::find()
            ->alias('r')
            ->select([
                'r.id',
                'r.name',
                'r.vip_type',
                'r.vip_level',
                'r.vip_begin_time',
                'r.vip_expire_time',
                'm.username',
            ])
            ->leftJoin(['m' => BaseMember::tableName()], 'm.id = r.member_id')
            ->andWhere([
                'r.id' => $resumeId,
                //                'r.status' => BaseResume::STATUS_ACTIVE,
            ])
            ->asArray()
            ->one();
        if (empty($resumeInfo)) {
            throw new Exception('简历不存在');
        }
        //简历ID加密
        $resumeInfo['encrypt_id'] = UUIDHelper::encrypt(UUIDHelper::TYPE_PERSON, $resumeInfo['id']);
        //会员类型文本
        if ($resumeInfo['vip_type'] == BaseResume::VIP_TYPE_ACTIVE) {
            $resumeInfo['vip_type_text'] = BaseResume::VIP_LEVEL_LIST[$resumeInfo['vip_level']];
        } else {
            $resumeInfo['vip_type_text'] = BaseResume::VIP_TYPE_LIST[$resumeInfo['vip_type']];
        }
        $resumeInfo['equity_detail'] = [];
        $insightNumber               = BaseResumeEquityPackageDetail::find()
            ->where([
                'resume_id'           => $resumeId,
                'package_category_id' => BaseResumeEquityPackageCategorySetting::ID_INSIGHT,
            ])
            ->groupBy('order_id')
            ->count();
        if ($insightNumber > 0) {
            //获取是否有生效的求职快套餐包
            $insightInfo = BaseResumeEquityPackage::findOne([
                'resume_id'           => $resumeId,
                'package_category_id' => BaseResumeEquityPackageCategorySetting::ID_INSIGHT,
                'expire_status'       => BaseResumeEquityPackage::STATUS_EXPIRE,
            ]);
            $insightItem = [
                'id'           => BaseResumeEquityPackageCategorySetting::ID_INSIGHT,
                'name'         => BaseResumeEquityPackageCategorySetting::ID_INSIGHT_TEXT,
                'categoryName' => BaseResumeEquityPackageCategorySetting::ID_INSIGHT_TEXT,
                'expireText'   => $insightInfo ? implode(' ~ ', [
                    $insightInfo->begin_time,
                    $insightInfo->expire_time,
                ]) : '已过期',
                'number'       => $insightNumber,
            ];

            //强行将求职快塞进去
            array_push($resumeInfo['equity_detail'], $insightItem);
        }

        //有没有求职快记录
        $jobFastNumber = BaseResumeEquityPackageDetail::find()
            ->where([
                'resume_id'           => $resumeId,
                'package_category_id' => BaseResumeEquityPackageCategorySetting::ID_JOB_FAST,
            ])
            ->groupBy('order_id')
            ->count();
        if ($jobFastNumber > 0) {
            //获取是否有生效的求职快套餐包
            $jobFastInfo = BaseResumeEquityPackage::findOne([
                'resume_id'           => $resumeId,
                'package_category_id' => BaseResumeEquityPackageCategorySetting::ID_JOB_FAST,
                'expire_status'       => BaseResumeEquityPackage::STATUS_EXPIRE,
            ]);
            $jobFastItem = [
                'id'           => BaseResumeEquityPackageCategorySetting::ID_JOB_FAST,
                'name'         => BaseResumeEquityPackageCategorySetting::ID_JOB_FAST_TEXT,
                'categoryName' => BaseResumeEquityPackageCategorySetting::ID_JOB_FAST_TEXT,
                'expireText'   => $jobFastInfo ? implode(' ~ ', [
                    $jobFastInfo->begin_time,
                    $jobFastInfo->expire_time,
                ]) : '已过期',
                'number'       => $jobFastNumber,
            ];

            //强行将求职快塞进去
            array_push($resumeInfo['equity_detail'], $jobFastItem);
        }

        //是否有人才套餐配置
        $resumeInfo['isResumePackageAdminSetting'] = BaseResumeEquityAdminSetting::find()
            ->where([
                'resume_id' => $resumeId,
                'status'    => [
                    BaseResumeEquityAdminSetting::STATUS_PASS,
                    BaseResumeEquityAdminSetting::STATUS_REFUND_REJECT,
                ],
            ])
            ->exists() ? '1' : '2';

        return $resumeInfo;
    }

    /**
     * 编辑求职者标签关联配置
     * @param $resumeId      int    单位id
     * @param $tagIds        标签id，字符串多个使用,隔开/数组
     * @return void
     * @throws Exception
     */
    public static function editTag($resumeId, $tagIds)
    {
        $checkHasResume = self::find()
            ->filterWhere(['id' => $resumeId])
            ->limit(1)
            ->one();
        if (!$checkHasResume) {
            throw new Exception('求职者不存在，非法请求');
        }

        if (!is_array($tagIds)) {
            $tagIds = array_filter(explode(',', $tagIds));
        }
        $tagIds = array_unique($tagIds);

        // 查询这个标签id
        foreach ($tagIds as $tagId) {
            if (!ResumeTag::findOne(['id' => $tagId])) {
                throw new Exception('标签不存在');
            }
        }

        // 删除原有的标签
        ResumeTagRelation::deleteAll(['resume_id' => $resumeId]);

        // 写入新的标签
        foreach ($tagIds as $tagId) {
            $model                = new ResumeTagRelation();
            $model->resume_id     = $resumeId;
            $model->resume_tag_id = $tagId;
            $model->save();
        }
    }

    /**
     * 验证数据并写入标签
     * @param $name
     * @return void
     * @throws Exception
     */
    public static function addTag($name, $adminId)
    {
        $name = trim($name);
        if (empty($name)) {
            throw new Exception('标签名称不能为空');
        }
        // 首先去检查是否存在
        $checkTag = ResumeTag::findOne(['tag' => $name]);

        if ($checkTag) {
            throw new MessageException('该标签名称己存在！');
        }

        $model           = new ResumeTag();
        $model->tag      = $name;
        $model->admin_id = $adminId;
        $model->save();
    }

    /**
     * 批量给求职者加上标签
     * @param array|string $resumeIds 求职者ID数组或逗号分隔的字符串
     * @param array|string $tagIds    标签ID数组或逗号分隔的字符串
     * @return array 返回操作结果统计
     * @throws Exception
     */
    public static function addResumeTag($resumeIds, $tagIds)
    {
        // 参数处理：转换为数组格式
        if (!is_array($resumeIds)) {
            $resumeIds = array_filter(explode(',', $resumeIds));
        }
        if (!is_array($tagIds)) {
            $tagIds = array_filter(explode(',', $tagIds));
        }

        // 去重和过滤空值
        $resumeIds = array_unique(array_filter($resumeIds));
        $tagIds    = array_unique(array_filter($tagIds));

        if (empty($resumeIds)) {
            throw new Exception('求职者ID不能为空');
        }
        if (empty($tagIds)) {
            throw new Exception('标签ID不能为空');
        }

        // 验证求职者是否存在
        $validResumeIds = self::find()
            ->select('id')
            ->where([
                'in',
                'id',
                $resumeIds,
            ])
            ->column();

        if (count($validResumeIds) != count($resumeIds)) {
            $invalidIds = array_diff($resumeIds, $validResumeIds);
            throw new Exception('以下求职者不存在：' . implode(',', $invalidIds));
        }

        // 验证标签是否存在
        $validTagIds = ResumeTag::find()
            ->select('id')
            ->where([
                'in',
                'id',
                $tagIds,
            ])
            ->column();

        if (count($validTagIds) != count($tagIds)) {
            $invalidIds = array_diff($tagIds, $validTagIds);
            throw new Exception('以下标签不存在：' . implode(',', $invalidIds));
        }

        // 查询已存在的标签关联关系
        $existingRelations = ResumeTagRelation::find()
            ->select([
                'resume_id',
                'resume_tag_id',
            ])
            ->where([
                'and',
                [
                    'in',
                    'resume_id',
                    $resumeIds,
                ],
                [
                    'in',
                    'resume_tag_id',
                    $tagIds,
                ],
            ])
            ->asArray()
            ->all();

        // 构建已存在关系的映射表，格式：resume_id_tag_id => true
        $existingMap = [];
        foreach ($existingRelations as $relation) {
            $key               = $relation['resume_id'] . '_' . $relation['resume_tag_id'];
            $existingMap[$key] = true;
        }

        // 准备批量插入的数据
        $insertData   = [];
        $skippedCount = 0; // 跳过的数量（已存在的关系）

        foreach ($resumeIds as $resumeId) {
            foreach ($tagIds as $tagId) {
                $key = $resumeId . '_' . $tagId;

                // 如果关系已存在，跳过
                if (isset($existingMap[$key])) {
                    $skippedCount++;
                    continue;
                }

                // 添加到待插入数据中
                $insertData[] = [
                    'resume_id'     => $resumeId,
                    'resume_tag_id' => $tagId,
                ];
            }
        }

        // 批量插入新的标签关联关系
        $insertedCount = 0;
        if (!empty($insertData)) {
            $insertedCount = Yii::$app->db->createCommand()
                ->batchInsert(ResumeTagRelation::tableName(), [
                        'resume_id',
                        'resume_tag_id',
                    ], array_map(function ($item) {
                        return [
                            $item['resume_id'],
                            $item['resume_tag_id'],
                        ];
                    }, $insertData))
                ->execute();
        }

        // 返回操作结果统计
        return [
            'totalResumeCount'   => count($resumeIds),
            // 总求职者数量
            'totalTagCount'      => count($tagIds),
            // 总标签数量
            'insertedCount'      => $insertedCount,
            // 新增关联数量
            'skippedCount'       => $skippedCount,
            // 跳过数量（已存在）
            'totalRelationCount' => count($resumeIds) * count($tagIds),
            // 总关系数量
        ];
    }

}
