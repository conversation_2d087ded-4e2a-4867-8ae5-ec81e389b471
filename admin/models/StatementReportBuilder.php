<?php
/**
 * create user：gaocai
 * create time：2023/12/27 09:10
 */
namespace admin\models;

use common\base\models\BaseAdmin;
use common\base\models\BaseAdminDownloadTask;
use common\base\models\BaseAdminStatementBuilderTitleConfig;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementCollect;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleClickLog;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyClickLog;
use common\base\models\BaseCompanyCollect;
use common\base\models\BaseDictionary;
use common\base\models\BaseHomePosition;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobApplyRecordExtra;
use common\base\models\BaseJobClickLog;
use common\base\models\BaseJobCollect;
use common\base\models\BaseMajor;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeResearchDirection;
use common\base\models\BaseResumeWork;
use common\base\models\BaseShowcase;
use common\helpers\FormatConverter;
use common\helpers\TimeHelper;
use common\libs\Cache;
use common\libs\Excel;
use common\service\downloadTask\DownLoadTaskApplication;
use Yii;
use yii\base\Exception;

/**
 * Class StatementReportBuilder
 * @package admin\models
 * 报表生成器
 */
class StatementReportBuilder
{
    /**
     * 获取维度列表
     * @return array
     */
    public static function reportBuilderConfig(): array
    {
        //做一个缓存
        $cacheKey = Cache::PC_ALL_REPORT_BUILDER_TITLE_CONFIG_TABLE_KEY;
        $data     = Cache::get($cacheKey);
        if ($data) {
            return json_decode($data, true);
        }
        //构造数据结构
        $data = [];
        foreach (BaseAdminStatementBuilderTitleConfig::TABS_LIST as $tabs) {
            $data[$tabs] = [];
            foreach (BaseAdminStatementBuilderTitleConfig::TYPE_LIST as $type) {
                $data[$tabs][$type] = BaseAdminStatementBuilderTitleConfig::find()
                    ->where([
                        'status' => BaseAdminStatementBuilderTitleConfig::STATUS_ENABLE,
                        'tabs'   => $tabs,
                        'type'   => $type,
                    ])
                    ->asArray()
                    ->all();
            }
        }

        Cache::set($cacheKey, json_encode($data));

        return $data;
    }

    /**
     * 获取筛选项
     * @return array
     */
    public static function filter(): array
    {
        // 职位ID =1
        // 职位名称=2
        // 单位ID =3
        // 单位名称=4
        // 公告ID =5
        // 公告名称=6
        // 人才ID =7
        // 人才姓名=8
        return [
            //常见搜索项
            'normalList' => [
                BaseAdminStatementBuilderTitleConfig::TABS_PERSON       => [
                    [
                        'value' => 7,
                        'label' => '人才ID',
                    ],
                    [
                        'value' => 8,
                        'label' => '人才姓名',
                    ],
                ],
                BaseAdminStatementBuilderTitleConfig::TABS_COMPANY      => [
                    [
                        'value' => 3,
                        'label' => '单位ID',
                    ],
                    [
                        'value' => 4,
                        'label' => '单位名称',
                    ],
                ],
                BaseAdminStatementBuilderTitleConfig::TABS_JOB          => [
                    [
                        'value' => 1,
                        'label' => '职位ID',
                    ],
                    [
                        'value' => 2,
                        'label' => '职位名称',
                    ],
                    [
                        'value' => 3,
                        'label' => '单位ID',
                    ],
                    [
                        'value' => 4,
                        'label' => '单位名称',
                    ],
                    [
                        'value' => 5,
                        'label' => '公告ID',
                    ],
                    [
                        'value' => 6,
                        'label' => '公告名称',
                    ],
                ],
                BaseAdminStatementBuilderTitleConfig::TABS_ANNOUNCEMENT => [
                    [
                        'value' => 5,
                        'label' => '公告ID',
                    ],
                    [
                        'value' => 6,
                        'label' => '公告名称',
                    ],
                ],
                BaseAdminStatementBuilderTitleConfig::TABS_APPLY        => [
                    [
                        'value' => 1,
                        'label' => '职位ID',
                    ],
                    [
                        'value' => 2,
                        'label' => '职位名称',
                    ],
                    [
                        'value' => 3,
                        'label' => '单位ID',
                    ],
                    [
                        'value' => 4,
                        'label' => '单位名称',
                    ],
                    [
                        'value' => 5,
                        'label' => '公告ID',
                    ],
                    [
                        'value' => 6,
                        'label' => '公告名称',
                    ],
                    [
                        'value' => 7,
                        'label' => '人才ID',
                    ],
                    [
                        'value' => 8,
                        'label' => '人才姓名',
                    ],
                ],
                BaseAdminStatementBuilderTitleConfig::TABS_SHOWCASE     => [
                    [
                        'value' => 3,
                        'label' => '单位ID',
                    ],
                ],
            ],
            //时间筛选项
            'timeList'   => [
                BaseAdminStatementBuilderTitleConfig::TABS_PERSON       => [
                    [
                        'value' => 1,
                        'label' => '注册时间',
                    ],
                ],
                BaseAdminStatementBuilderTitleConfig::TABS_COMPANY      => [
                    [
                        'value' => 1,
                        'label' => '注册时间',
                    ],
                    [
                        'value' => 2,
                        'label' => '导出时间',
                    ],
                ],
                BaseAdminStatementBuilderTitleConfig::TABS_JOB          => [
                    [
                        'value' => 1,
                        'label' => '添加时间',
                    ],
                    [
                        'value' => 2,
                        'label' => '导出时间',
                    ],
                ],
                BaseAdminStatementBuilderTitleConfig::TABS_ANNOUNCEMENT => [
                    [
                        'value' => 1,
                        'label' => '添加时间',
                    ],
                ],
                BaseAdminStatementBuilderTitleConfig::TABS_APPLY        => [
                    [
                        'value' => 1,
                        'label' => '投递时间',
                    ],
                ],
                BaseAdminStatementBuilderTitleConfig::TABS_SHOWCASE     => [
                    [
                        'value' => 1,
                        'label' => '添加时间',
                    ],
                    [
                        'value' => 601,
                        'label' => '生效时间',
                    ],
                    [
                        'value' => 606,
                        'label' => '失效时间',
                    ],
                ],
            ],
        ];
    }

    /**
     * 创建报表
     * @param $params
     */
    public static function create($params)
    {
        //将参数格式化成下划线
        $params = FormatConverter::convertHump($params);
        //检验一下
        $check_res = self::beforeCheck($params);
        if (!$check_res) {
            return [
                'code' => -1,
                'msg'  => '该报表暂时不支持使用，请联系管理员！',
            ];
        }
        //验证一下参数
        if (empty($params['title_select'])) {
            return [
                'code' => -1,
                'msg'  => '请选择表头',
            ];
        }
        //这里很重要--- 这个一定要转成数组
        $params['title_select'] = explode(',', $params['title_select']);
        if (isset($params['is_import_file']) && $params['is_import_file'] == 1) {
            //导入文件了 读取文件数据
            $filePath = Yii::getAlias('@frontendPc') . '/web/' . $params['file_path'];
            $excel    = new Excel();
            $data     = $excel->import($filePath, [], 3, [
                1 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
                2 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
            ]);
            $count    = count($data);
            if ($count <= 0 || $count > 100) {
                return [
                    'code' => -1,
                    'msg'  => '您导入空文件或者数据条数超过100,请核查！',
                ];
            }
            unset($params['file_path'], $params['filter_time_key'], $params['add_time_start'], $params['add_time_end'], $params['filter_value'], $params['filter_key']);
            $file_data = [];
            foreach ($data as $key => $value) {
                $initTime = strtotime('1974-01-01 00:00:00');
                if (empty($value[0]) || empty($value[1]) || empty($value[2]) || $value[1] <= $initTime || $value[2] <= $initTime) {
                    return [
                        'code' => -1,
                        'msg'  => '请检查' . ($key + 2) . '行出错，数据是否为空或者时间格式是否正确！',
                    ];
                }
                if ($value[1] > $value[2]) {
                    return [
                        'code' => -1,
                        'msg'  => ($key + 2) . '行出错，开始时间大于结束时间！',
                    ];
                }
                if (!BaseCompany::find()
                    ->where(['full_name' => $value[0]])
                    ->exists()) {
                    return [
                        'code' => -1,
                        'msg'  => ($key + 2) . '行出错，单位名称查询的单位信息不存在！单位名称获取内容为：' . $value[0],
                    ];
                }
                $value[1]    = date('Y-m-d', $value[1]);
                $value[2]    = date('Y-m-d', $value[2]);
                $file_data[] = [
                    'filter_time_key' => 2,
                    'add_time_start'  => TimeHelper::dayToBeginTime($value[1]),
                    'add_time_end'    => TimeHelper::dayToEndTime($value[2]),
                    'filter_value'    => $value[0],
                    'filter_key'      => 4,
                ];
            }
            //检查完成数据
            $params['file_data'] = $file_data;
        } else {
            if (((!empty($params['filter_value']) && empty($params['filter_key'])) || (empty($params['filter_value']) && !empty($params['filter_key']))) && $params['tabs'] != BaseAdminStatementBuilderTitleConfig::TABS_SHOWCASE) {
                return [
                    'code' => -1,
                    'msg'  => '第一个条件没有同时填写，筛选项与筛选值必须同时填写！',
                ];
            }
            if (($params['filter_time_key'] && !$params['add_time_start'] && !$params['add_time_end']) || (!$params['filter_time_key'] && ($params['add_time_start'] || $params['add_time_end']))) {
                return [
                    'code' => -1,
                    'msg'  => '第二个条件没有同时填写，筛选项与筛选值必须同时填写！',
                ];
            }
            //        if (empty($params['filter_value']) && $params['filter_time_key']) {
            //            return [
            //                'code' => -1,
            //                'msg'  => '必须填写筛选项才能导出数据！导出全部性能消耗太大！如需导出全部数据请联系管理员！',
            //            ];
            //        }
            if ($params['filter_time_key'] && $params['add_time_start'] && $params['add_time_end']) {
                if (empty($params['filter_key']) && empty($params['filter_value']) && $params['filter_time_key'] > 1 && $params['tabs'] != BaseAdminStatementBuilderTitleConfig::TABS_SHOWCASE) {
                    //报错
                    return [
                        'code' => -1,
                        'msg'  => '只选择导出时间，将会导致数据过大，无法生成报表，请增加筛选项',
                    ];
                }
                $params['add_time_start'] = TimeHelper::dayToBeginTime($params['add_time_start']);
                $params['add_time_end']   = TimeHelper::dayToEndTime($params['add_time_end']);
            }

            $isToast = false;
            if ($params['is_tips'] === 'true' && $params['filter_key'] && $params['filter_value'] && $params['add_time_start'] && $params['add_time_end']) {
                //$data = self::handleOutputData($params);
                $isToast = true;

                return [
                    'is_toast' => $isToast,
                ];
            }
        }
        //创建下载任务-去到下载中心
        $adminId = Yii::$app->user->id;
        $app     = DownLoadTaskApplication::getInstance();
        $app->createAdmin($adminId, BaseAdminDownloadTask::TYPE_STATEMENT_REPORT_BUILDER, $params);

        return [
            'is_toast' => $isToast,
        ];
    }

    /**
     * 检查一下是否支持该报表
     * @param $params
     * @throws Exception
     */
    public static function beforeCheck($params)
    {
        //后缀
        $tabs     = $params['tabs'];
        $tabsType = $params['tabs_type'];

        if (!method_exists(self::class, 'getQuery' . $tabs . $tabsType) || !method_exists(self::class,
                'where' . $tabs . $tabsType) || !method_exists(self::class, 'handleData' . $tabs . $tabsType)) {
            return false;
        }

        return true;
    }

    /**
     * 处理输出数据的逻辑
     */
    public static function handleOutputData($params)
    {
        //参数处理
        $tabs        = $params['tabs'];
        $tabsType    = $params['tabs_type'];
        $titleSelect = $params['title_select'];
        //首先根据表头参数获取数据
        $heardData = BaseAdminStatementBuilderTitleConfig::getTabsByCodeList($tabs, $titleSelect);
        //根据表头的tabs、code编写数据query
        $suffix           = $tabs . $tabsType;
        $queryMethod      = 'getQuery' . $suffix;
        $whereMethod      = 'where' . $suffix;
        $handleDataMethod = 'handleData' . $suffix;
        //构建query句柄
        $query = self::$queryMethod();
        //构建where条件与select
        $query = self::$whereMethod($query, $heardData, $params);
        //获取数据
        $data = $query->asArray()
            ->all();
        $data = self::$handleDataMethod($data, $params);

        return [
            'heardData' => $heardData,
            'data'      => $data,
        ];
    }

    /**
     * 公共筛选条件处理
     * @param $query
     * @param $params
     * @param $alias
     * @return mixed
     */
    public static function whereCommon($query, $params, $alias)
    {
        // 职位ID =1
        // 职位名称=2
        // 单位ID =3
        // 单位名称=4
        // 公告ID =5
        // 公告名称=6
        // 人才ID =7
        // 人才姓名=8
        if ($params['filter_key'] && $params['filter_value']) {
            //改写成switch
            switch ($params['filter_key']) {
                case 1:
                    $query->andWhere(['j.uuid' => $params['filter_value']]);
                    break;
                case 2:
                    $query->andWhere(['j.name' => $params['filter_value']]);
                    break;
                case 3:
                    $query->andWhere(['c.uuid' => $params['filter_value']]);
                    break;
                case 4:
                    $query->andWhere(['c.full_name' => $params['filter_value']]);
                    break;
                case 5:
                    $query->andWhere(['a.uuid' => $params['filter_value']]);
                    break;
                case 6:
                    $query->andWhere(['a.title' => $params['filter_value']]);
                    break;
                case 7:
                    $query->andWhere(['r.uuid' => $params['filter_value']]);
                    break;
                case 8:
                    $query->andWhere(['r.name' => $params['filter_value']]);
                    break;
            }
        }
        if ($params['filter_time_key'] && $params['add_time_start'] && $params['add_time_end']) {
            switch ($params['filter_time_key']) {
                case 1:
                    $query->andWhere([
                        'between',
                        $alias . '.add_time',
                        $params['add_time_start'],
                        $params['add_time_end'],
                    ]);
                    break;
                case 2:
                    //导出时间--做数据上的统计
                    break;
                case 601:
                    $query->andWhere([
                        'between',
                        $alias . '.online_time',
                        $params['add_time_start'],
                        $params['add_time_end'],
                    ]);
                    break;
                case 602:
                    $query->andWhere([
                        'between',
                        $alias . '.offline_time',
                        $params['add_time_start'],
                        $params['add_time_end'],
                    ]);
                    break;
            }
        }

        return $query;
    }

    /**
     * tabs=2 tabs_type=1 的获取Query处理方法
     * @return mixed
     */
    public static function getQuery21()
    {
        $alias = 'c';
        $query = BaseCompany::find()
            ->alias($alias)
            ->leftJoin(['j' => BaseJob::tableName()], 'c.id=j.company_id')
            ->andWhere([
                'c.status' => BaseCompany::STATUS_ACTIVE,
                //                'c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
            ])
            ->groupBy('c.id');

        return $query;
    }

    /**
     * tabs=2 tabs_type=1 的where处理方法
     * @param $query
     * @param $data
     * @param $params
     * @return mixed
     */
    public static function where21($query, $data, $params)
    {
        //处理公共筛选条件
        $query = self::whereCommon($query, $params, 'c');
        //单位业务员
        if (in_array(210024, $params['title_select'])) {
            $query->leftJoin(['ad' => BaseAdmin::tableName()], 'ad.id=c.admin_id');
        }
        //        if (isset($params['filter_time_key']) && $params['filter_time_key'] != 2) {
        //            $query->leftJoin(['jare' => BaseJobApplyRecordExtra::tableName()], 'j.id = jare.job_id');
        //        }

        //处理特殊筛选条件
        $select = [
            'c.id as company_id',
        ];
        foreach ($data as $item) {
            // 210001	单位ID
            // 210002	单位名称
            // 210003	创建时间
            // 210004	发布公告数量
            // 210005	发布职位数量
            // 210006	收到简历数量
            // 210007	博士简历数量
            // 210008	硕士简历数量
            // 210009	本科简历数量
            // 210010	公告点击量总量
            // 210011	职位点击总量
            // 210012	单位点击总量
            // 210013	单位关系
            // 210014	单位所在省
            // 210015	单位所在市
            // 210016	邀面数量
            // 210017	站内投递数量
            // 210018	站外投递数量
            // 210019	平台投递数量
            // 210020	邮件投递数量
            // 210021	链接投递数量
            // 210022	单位类型
            // 210023	单位性质
            // 210024	单位业务员

            // 单位ID
            if ($item['code'] == 210001) {
                array_push($select, 'c.uuid as company_uuid');
                continue;
            }
            // 单位名称
            if ($item['code'] == 210002) {
                array_push($select, 'c.full_name as company_name');
                continue;
            }
            // 创建时间
            if ($item['code'] == 210003) {
                array_push($select, 'c.add_time as company_add_time');
                continue;
            }
            // 发布职位数量
            if ($item['code'] == 210005 && $params['filter_time_key'] != 2) {
                array_push($select, 'count(j.id) as job_release_num');
                continue;
            }
            //            // 收到简历数量
            //            if ($item['code'] == 210006 && $params['filter_time_key'] != 2) {
            //                array_push($select, 'sum(jare.total) as resume_receive_num');
            //                continue;
            //            }
            //            // 博士简历数量
            //            if ($item['code'] == 210007 && $params['filter_time_key'] != 2) {
            //                array_push($select, 'sum(jare.education_doctor) as resume_doctoral_num');
            //                continue;
            //            }
            //            // 硕士简历数量
            //            if ($item['code'] == 210008 && $params['filter_time_key'] != 2) {
            //                array_push($select, 'sum(jare.education_master) as resume_master_num');
            //                continue;
            //            }
            //            // 本科简历数量
            //            if ($item['code'] == 210009 && $params['filter_time_key'] != 2) {
            //                array_push($select, 'sum(jare.education_undergraduate) as resume_undergraduate_num');
            //                continue;
            //            }

            // 职位点击总量
            if ($item['code'] == 210011 && $params['filter_time_key'] != 2) {
                array_push($select, 'sum(j.click) as job_click_num');
                continue;
            }
            // 单位点击总量
            if ($item['code'] == 210012 && $params['filter_time_key'] != 2) {
                array_push($select, 'c.click as company_click_num');
                continue;
            }
            // 单位关系
            if ($item['code'] == 210013) {
                array_push($select, 'c.is_cooperation as company_is_cooperation');
                continue;
            }
            // 单位所在省
            if ($item['code'] == 210014) {
                array_push($select, 'c.province_id as company_province_id');
                continue;
            }
            // 单位所在市
            if ($item['code'] == 210015) {
                array_push($select, 'c.city_id as company_city_id');
                continue;
            }
            //            // 邀面数量
            //            if ($item['code'] == 210016 && $params['filter_time_key'] != 2) {
            //                array_push($select, 'sum(jare.interview) as interview_num');
            //                continue;
            //            }
            //            // 站内投递数量
            //            if ($item['code'] == 210017 && $params['filter_time_key'] != 2) {
            //                array_push($select, 'sum(jare.delivery_type_outside) as apply_outside_num');
            //                continue;
            //            }
            //            // 站外投递数量
            //            if ($item['code'] == 210018 && $params['filter_time_key'] != 2) {
            //                array_push($select, 'sum(jare.delivery_type_outer) as apply_outer_num');
            //                continue;
            //            }
            //            // 平台投递数量
            //            if ($item['code'] == 210019 && $params['filter_time_key'] != 2) {
            //                array_push($select, 'sum(jare.delivery_way_platform) as apply_platform_num');
            //                continue;
            //            }
            //            // 邮件投递数量
            //            if ($item['code'] == 210020 && $params['filter_time_key'] != 2) {
            //                array_push($select, 'sum(jare.delivery_way_email) as apply_email_num');
            //                continue;
            //            }
            //            // 链接投递数量
            //            if ($item['code'] == 210021 && $params['filter_time_key'] != 2) {
            //                array_push($select, 'sum(jare.delivery_way_link) as apply_link_num');
            //                continue;
            //            }
            // 单位类型
            if ($item['code'] == 210022) {
                array_push($select, 'c.type as company_type');
                continue;
            }
            // 单位性质
            if ($item['code'] == 210023) {
                array_push($select, 'c.nature as company_nature');
                continue;
            }
            // 单位业务员
            if ($item['code'] == 210024) {
                array_push($select, 'ad.name as company_admin_name');
                continue;
            }
        }
        $query->select($select);

        return $query;
    }

    /**
     * tabs=2 tabs_type=1 的数据处理方法
     * @param $data
     * @param $params
     * @return mixed
     */
    public static function handleData21($data, $params)
    {
        $titleSelect = $params['title_select'];
        foreach ($data as &$item) {
            $diff = array_intersect([
                210006,
                210007,
                210008,
                210009,
                210016,
                210017,
                210018,
                210019,
                210020,
                210021,
                210025,
                210026,
                210027,
                210028,
                210029,
                210030,
                210031,
                210032,
                210033,
                210034,
                210035,
                210036,
                210037,
                210038,
                210039,
                210040,
                210041,
                210042,
                210043,
                210044,
                210045,
                210046,
            ], $titleSelect);
            if (count($diff) > 0) {
                $totalData = self::getCompanyApplyRecordTotal($item['company_id'], $params);
                //                'apply_num'                         => $apply_num,
                //                'apply_person_num'                  => $apply_person_num,
                //                'apply_doctoral_num'                => $apply_doctoral_num,
                //                'apply_doctoral_person_num'         => $apply_doctoral_person_num,
                //                'apply_master_num'                  => $apply_master_num,
                //                'apply_master_person_num'           => $apply_master_person_num,
                //                'apply_undergraduate_num'           => $apply_undergraduate_num,
                //                'apply_undergraduate_person_num'    => $apply_undergraduate_person_num,
                //                'apply_doctoral_abroad_num'         => $apply_doctoral_abroad_num,
                //                'apply_doctoral_abroad_person_num'  => $apply_doctoral_abroad_person_num,
                //                'apply_doctoral_985_211_num'        => $apply_doctoral_985_211_num,
                //                'apply_doctoral_985_211_person_num' => $apply_doctoral_985_211_person_num,
                //                'apply_doctoral_work_year_2'        => $apply_doctoral_work_year_2,
                //                'apply_doctoral_work_year_person_2' => $apply_doctoral_work_year_person_2,
                //                'apply_doctoral_worker'             => $apply_doctoral_worker,
                //                'apply_doctoral_worker_person'      => $apply_doctoral_worker_person,
                //                'apply_doctoral_graduate'           => $apply_doctoral_graduate,
                //                'apply_doctoral_graduate_person'    => $apply_doctoral_graduate_person,
                //                'apply_title_g'                     => $apply_title_g,
                //                'apply_title_g_person'              => $apply_title_g_person,
                //                'apply_title_z'                     => $apply_title_z,
                //                'apply_title_z_person'              => $apply_title_z_person,
                //                'apply_title_fg'                    => $apply_title_fg,
                //                'apply_title_fg_person'             => $apply_title_fg_person,
                //                'apply_delivery_outer'              => $apply_delivery_outer,
                //                'apply_delivery_outside'            => $apply_delivery_outside,
                //                'apply_delivery_platform'           => $apply_delivery_platform,
                //                'apply_delivery_email'              => $apply_delivery_email,
                //                'apply_delivery_link'               => $apply_delivery_link,
            }

            // 发布公告数量
            if (in_array(210004, $titleSelect)) {
                $item['announcement_release_num'] = self::getCompanyAnnouncementCount($item['company_id'], $params);
            }
            // 发布职位数量
            if (in_array(210005, $titleSelect) && !isset($item['job_release_num'])) {
                $item['job_release_num'] = self::getCompanyJobCount($item['company_id'], $params);
            }
            // 收到简历数量
            if (in_array(210006, $titleSelect)) {
                $item['resume_receive_num'] = $totalData['apply_num'];
            }
            // 博士简历数量
            if (in_array(210007, $titleSelect)) {
                $item['resume_doctoral_num'] = $totalData['apply_doctoral_num'];
            }
            // 硕士简历数量
            if (in_array(210008, $titleSelect)) {
                $item['resume_master_num'] = $totalData['apply_master_num'];
            }
            // 本科简历数量
            if (in_array(210009, $titleSelect)) {
                $item['resume_undergraduate_num'] = $totalData['apply_undergraduate_num'];
            }
            // 公告点击量总量
            if (in_array(210010, $titleSelect)) {
                $item['announcement_click_num'] = self::getCompanyAnnouncementClickCount($item['company_id'], $params);
            }

            // 职位点击总量
            if (in_array(210011, $titleSelect) && !isset($item['job_click_num'])) {
                $item['job_click_num'] = self::getCompanyJobClickCount($item['company_id'], $params);
            }
            // 单位点击总量
            if (in_array(210012, $titleSelect) && !isset($item['company_click_num'])) {
                $item['company_click_num'] = self::getCompanyClickTotal($item['company_id'], $params['add_time_start'],
                    $params['add_time_end']);
            }
            // 单位关系文案
            if (in_array(210013, $titleSelect)) {
                $item['company_is_cooperation_text'] = BaseCompany::COOPERATIVE_UNIT_LIST[$item['company_is_cooperation']];
            }
            // 邀面数量
            if (in_array(210016, $titleSelect)) {
                $item['interview_num'] = self::getCompanyInterviewCount($item['company_id'], $params);
            }
            // 站内投递数量
            if (in_array(210017, $titleSelect)) {
                $item['apply_outside_num'] = $totalData['apply_delivery_outside'];
            }
            // 站外投递数量
            if (in_array(210018, $titleSelect)) {
                $item['apply_outer_num'] = $totalData['apply_delivery_outer'];
            }
            // 平台投递数量
            if (in_array(210019, $titleSelect)) {
                $item['apply_platform_num'] = $totalData['apply_delivery_platform'];
            }
            // 邮件投递数量
            if (in_array(210020, $titleSelect)) {
                $item['apply_email_num'] = $totalData['apply_delivery_email'];
            }
            // 链接投递数量
            if (in_array(210021, $titleSelect)) {
                $item['apply_link_num'] = $totalData['apply_delivery_link'];
            }
            // 单位类型文案
            if (in_array(210022, $titleSelect)) {
                $item['company_type_text'] = BaseDictionary::getCompanyTypeName($item['company_type']);
            }
            // 单位性质文案
            if (in_array(210023, $titleSelect)) {
                $item['company_nature_text'] = BaseDictionary::getCompanyNatureName($item['company_nature']);
            }
            // 单位所在省文案
            if (in_array(210014, $titleSelect)) {
                $item['company_province_text'] = BaseArea::getAreaName($item['company_province_id']);
            }
            // 单位所在市文案
            if (in_array(210015, $titleSelect)) {
                $item['company_city_text'] = BaseArea::getAreaName($item['company_city_id']);
            }
            // 210025   收到简历人数
            if (in_array(210025, $titleSelect)) {
                $item['apply_person_num'] = $totalData['apply_person_num'];
            }
            // 210026   博士投递人数
            if (in_array(210026, $titleSelect)) {
                $item['apply_doctoral_person_num'] = $totalData['apply_doctoral_person_num'];
            }
            // 210027   硕士投递人数
            if (in_array(210027, $titleSelect)) {
                $item['apply_master_person_num'] = $totalData['apply_master_person_num'];
            }
            // 210028   本科投递人数
            if (in_array(210028, $titleSelect)) {
                $item['apply_undergraduate_person_num'] = $totalData['apply_undergraduate_person_num'];
            }
            // 210029   站内投递人数
            if (in_array(210029, $titleSelect)) {
                $item['apply_delivery_outside'] = $totalData['apply_delivery_outside_person'];
            }
            // 210030   站外投递人数
            if (in_array(210030, $titleSelect)) {
                $item['apply_delivery_outer'] = $totalData['apply_delivery_outer_person'];
            }
            // 210031	博士海外经历数量
            if (in_array(210031, $titleSelect)) {
                $item['apply_doctoral_abroad_num'] = $totalData['apply_doctoral_abroad_num'];
            }
            // 210032	博士985/211数量
            if (in_array(210032, $titleSelect)) {
                $item['apply_doctoral_985_211_num'] = $totalData['apply_doctoral_985_211_num'];
            }
            // 210033	博士且工作2年及以上数量
            if (in_array(210033, $titleSelect)) {
                $item['apply_doctoral_work_year_2'] = $totalData['apply_doctoral_work_year_2'];
            }
            // 210034	博士为职场数量
            if (in_array(210034, $titleSelect)) {
                $item['apply_doctoral_worker'] = $totalData['apply_doctoral_worker'];
            }
            // 210035	博士为应届生/在校生数量
            if (in_array(210035, $titleSelect)) {
                $item['apply_doctoral_graduate'] = $totalData['apply_doctoral_graduate'];
            }
            // 210036	高级职称数量
            if (in_array(210036, $titleSelect)) {
                $item['apply_title_g'] = $totalData['apply_title_g'];
            }
            // 210037	副高级职称数量
            if (in_array(210037, $titleSelect)) {
                $item['apply_title_fg'] = $totalData['apply_title_fg'];
            }
            // 210038	中级职称数量
            if (in_array(210038, $titleSelect)) {
                $item['apply_title_z'] = $totalData['apply_title_z'];
            }
            // 210039	博士海外经历人数
            if (in_array(210039, $titleSelect)) {
                $item['apply_doctoral_abroad_person_num'] = $totalData['apply_doctoral_abroad_person_num'];
            }
            // 210040	博士985/211人数
            if (in_array(210040, $titleSelect)) {
                $item['apply_doctoral_985_211_person_num'] = $totalData['apply_doctoral_985_211_person_num'];
            }
            // 210041	博士且工作2年及以上人数
            if (in_array(210041, $titleSelect)) {
                $item['apply_doctoral_work_year_person_2'] = $totalData['apply_doctoral_work_year_person_2'];
            }
            // 210042	博士为职场人人数
            if (in_array(210042, $titleSelect)) {
                $item['apply_doctoral_worker_person'] = $totalData['apply_doctoral_worker_person'];
            }
            // 210043	博士为应届生/在校生人数
            if (in_array(210043, $titleSelect)) {
                $item['apply_doctoral_graduate_person'] = $totalData['apply_doctoral_graduate_person'];
            }
            // 210044	高级职称人数
            if (in_array(210044, $titleSelect)) {
                $item['apply_title_g_person'] = $totalData['apply_title_g_person'];
            }
            // 210045	副高级职称人数
            if (in_array(210045, $titleSelect)) {
                $item['apply_title_fg_person'] = $totalData['apply_title_fg_person'];
            }
            // 210046	中级职称人数
            if (in_array(210046, $titleSelect)) {
                $item['apply_title_z_person'] = $totalData['apply_title_z_person'];
            }
        }

        return $data;
    }

    /**
     * tabs=3 tabs_type=1 的获取Query处理方法
     * @return mixed
     */
    public static function getQuery31()
    {
        $alias = 'j';
        $query = BaseJob::find()
            ->alias($alias)
            ->innerJoin(['c' => BaseCompany::tableName()], 'c.id = j.company_id')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id = j.announcement_id')
            ->leftJoin(['ar' => BaseArticle::tableName()], 'a.article_id = ar.id');

        return $query;
    }

    /**
     * tabs=3 tabs_type=1 的where处理方法
     * @param $query
     * @param $data
     * @param $params
     * @return mixed
     */
    public static function where31($query, $data, $params)
    {
        //处理公共筛选条件
        $query = self::whereCommon($query, $params, 'j');

        //所属销售
        if (in_array(310007, $params['title_select'])) {
            $query->leftJoin(['ad' => BaseAdmin::tableName()], 'ad.id = c.admin_id');
        }
        //投递量
        if (in_array(310011, $params['title_select']) || in_array(310013, $params['title_select']) || in_array(310013,
                $params['title_select'])) {
            $query->leftJoin(['jare' => BaseJobApplyRecordExtra::tableName()], 'jare.job_id = j.id');
        }

        //处理特殊筛选条件
        $select = [
            'c.id as company_id',
            'j.id as job_id',
            'a.id as announcement_id',
        ];
        foreach ($data as $item) {
            //310001	职位ID
            //310002	职位名称
            //310003	公告ID
            //310004	公告名称
            //310005	单位ID
            //310006	单位名称
            //310007	所属销售
            //310008	招聘状态
            //310009	职位阅读量
            //310010	职位收藏量

            // 职位ID
            if ($item['code'] == 310001) {
                array_push($select, 'j.uuid as job_uuid');
                continue;
            }
            // 职位名称
            if ($item['code'] == 310002) {
                array_push($select, 'j.name as job_name');
                continue;
            }
            // 公告ID
            if ($item['code'] == 310003) {
                array_push($select, 'a.uuid as announcement_uuid');
                continue;
            }
            // 公告名称
            if ($item['code'] == 310004) {
                array_push($select, 'a.title as announcement_name');
                continue;
            }
            // 单位ID
            if ($item['code'] == 310005) {
                array_push($select, 'c.uuid as company_uuid');
                continue;
            }
            // 单位名称
            if ($item['code'] == 310006) {
                array_push($select, 'c.full_name as company_name');
                continue;
            }
            // 所属销售
            if ($item['code'] == 310007) {
                array_push($select, 'ad.name as salesman_name');
                continue;
            }
            // 招聘状态
            if ($item['code'] == 310008) {
                array_push($select, 'j.status as job_status');
                continue;
            }
            // 职位阅读量
            if ($item['code'] == 310009 && $params['filter_time_key'] != 2) {
                array_push($select, 'j.click as job_click');
                continue;
            }
            //            // 投递量
            //            if ($item['code'] == 310011) {
            //                array_push($select, 'jare.total as job_apply_num');
            //                continue;
            //            }
            //            // 博士投递量
            //            if ($item['code'] == 310013) {
            //                array_push($select, 'jare.education_doctor as job_apply_doctoral_num');
            //                continue;
            //            }
            //            // 硕士投递量
            //            if ($item['code'] == 310015) {
            //                array_push($select, 'jare.education_master as job_apply_master_num');
            //                continue;
            //            }
            // 公告阅读量
            if ($item['code'] == 310025 && $params['filter_time_key'] != 2) {
                array_push($select, 'ar.click as announcement_click');
                continue;
            }
            // 单位阅读量
            if ($item['code'] == 310026 && $params['filter_time_key'] != 2) {
                array_push($select, 'c.click as company_click');
                continue;
            }
        }
        $query->select($select);

        return $query;
    }

    /**
     * tabs=3 tabs_type=1 的数据处理方法
     * @param $data
     * @param $params
     * @return mixed
     */
    public static function handleData31($data, $params)
    {
        $titleSelect = $params['title_select'];
        foreach ($data as &$item) {
            // 招聘状态
            $item['job_status_name'] = BaseJob::JOB_STATUS_NAME[$item['job_status']];

            //特殊处理
            //310010	职位收藏量
            //310011	投递量
            //310012	投递人数
            //310013	博士投递量
            //310014	博士投递人数
            //310015	硕士投递量
            //310016	硕士投递人数
            //310017	博士海外经历
            //310018	博士985/211
            //310019	博士且工作2年及以上
            //310020	博士为职场人
            //310021	博士为应届生/在校生
            //310022	高级职称
            //310023	中级职称
            //310024	副高级职称
            //310025	公告阅读量
            //310026	单位阅读量
            //310027	公告收藏量
            //310028	单位收藏量

            //310010	职位收藏量
            if (in_array(310010, $titleSelect)) {
                $item['job_collect'] = self::getJobCollectCount($item['job_id'], $params);
            }
            //310027	公告收藏量
            if (in_array(310027, $titleSelect)) {
                $item['announcement_collect'] = self::getAnnouncementCollectCount($item['announcement_id'], $params);
            }
            //310028	单位收藏量
            if (in_array(310028, $titleSelect)) {
                $item['company_collect'] = self::getCompanyCollectCount($item['company_id'], $params);
            }
            $diff = array_intersect([
                310011,
                310012,
                310013,
                310014,
                310015,
                310016,
                310017,
                310018,
                310019,
                310020,
                310021,
                310022,
                310023,
                310024,
            ], $titleSelect);
            if (count($diff) > 0) {
                //调用统计方法
                $total_data_item = self::getJobApplyRecordTotal($item['job_id'], $params);
                //            'apply_num'                  => $apply_num,
                //            'apply_person_num'           => $apply_person_num,
                //            'apply_doctoral_person_num'  => $apply_doctoral_person_num,
                //            'apply_doctoral_num'         => $apply_doctoral_num,
                //            'apply_master_person_num'    => $apply_master_person_num,
                //            'apply_master_num'           => $apply_master_num,
                //            'apply_doctoral_abroad_num'  => $apply_doctoral_abroad_num,
                //            'apply_doctoral_985_211_num' => $apply_doctoral_985_211_num,
                //            'apply_doctoral_work_year_2' => $apply_doctoral_work_year_2,
                //            'apply_doctoral_worker'      => $apply_doctoral_worker,
                //            'apply_doctoral_graduate'    => $apply_doctoral_graduate,
                //            'apply_title_g'              => $apply_title_g,
                //            'apply_title_z'              => $apply_title_z,
                //            'apply_title_fg'             => $apply_title_fg,
                //310011	投递数量
                $item['apply_num'] = $total_data_item['apply_num'];
                //310012	投递人数
                $item['apply_person_num'] = $total_data_item['apply_person_num'];
                //310013	博士投递数量
                $item['apply_doctoral_num'] = $total_data_item['apply_doctoral_num'];
                //310014	博士投递人数
                $item['apply_doctoral_person_num'] = $total_data_item['apply_doctoral_person_num'];
                //310015	硕士投递数量
                $item['apply_master_num'] = $total_data_item['apply_master_num'];
                //310016	硕士投递人数
                $item['apply_master_person_num'] = $total_data_item['apply_master_person_num'];
                //310017	博士海外经历
                $item['apply_doctoral_abroad_num'] = $total_data_item['apply_doctoral_abroad_num'];
                //310018	博士985/211
                $item['apply_doctoral_985_211_num'] = $total_data_item['apply_doctoral_985_211_num'];
                //310019	博士且工作2年及以上
                $item['apply_doctoral_work_year_2'] = $total_data_item['apply_doctoral_work_year_2'];
                //310020	博士为职场人
                $item['apply_doctoral_worker'] = $total_data_item['apply_doctoral_worker'];
                //310021	博士为应届生/在校生
                $item['apply_doctoral_graduate'] = $total_data_item['apply_doctoral_graduate'];
                //310022	高级职称
                $item['apply_title_g'] = $total_data_item['apply_title_g'];
                //310023	中级职称
                $item['apply_title_z'] = $total_data_item['apply_title_z'];
                //310024	副高级职称
                $item['apply_title_fg'] = $total_data_item['apply_title_fg'];
            }

            //特殊处理310009	职位阅读量
            if (in_array(310009, $titleSelect) && !isset($item['job_click'])) {
                $item['job_click'] = self::getJobClickTotal($item['job_id'], $params['add_time_start'],
                    $params['add_time_end']);
            }
            //特殊处理310025	公告阅读量
            if (in_array(310025, $titleSelect) && !isset($item['announcement_click'])) {
                $item['announcement_click'] = self::getAnnouncementClickTotal($item['announcement_id'],
                    $params['add_time_start'], $params['add_time_end']);
            }
            //特殊处理310026	单位阅读量
            if (in_array(310026, $titleSelect) && !isset($item['company_click'])) {
                $item['company_click'] = self::getCompanyClickTotal($item['company_id'], $params['add_time_start'],
                    $params['add_time_end']);
            }
        }

        return $data;
    }

    /**
     * tabs=5 tabs_type=1 的获取Query处理方法
     * @return mixed
     */
    public static function getQuery51()
    {
        $alias = 'jar';
        $query = BaseJobApplyRecord::find()
            ->alias($alias)
            ->innerJoin(['j' => BaseJob::tableName()], 'j.id = jar.job_id')
            ->innerJoin(['r' => BaseResume::tableName()], 'r.id = jar.resume_id')
            ->innerJoin(['c' => BaseCompany::tableName()], 'c.id = jar.company_id')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id = jar.announcement_id');

        return $query;
    }

    /**
     * tabs=5 tabs_type=1 的where处理方法
     * @param $query
     * @param $data
     * @param $params
     * @return mixed
     */
    public static function where51($query, $data, $params)
    {
        //处理公共筛选条件
        $query = self::whereCommon($query, $params, 'jar');

        if (in_array(510009, $params['title_select'])) {
            $query->leftJoin(['area' => BaseArea::tableName()], 'area.id = r.household_register_id');
        }
        if (in_array(510023, $params['title_select'])) {
            $query->leftJoin(['rrd' => BaseResumeResearchDirection::tableName()],
                'rrd.resume_id = r.id and rrd.status = 1');
        }
        //最高学历
        if (in_array(510018, $params['title_select'])) {
            $query->leftJoin(['d' => BaseDictionary::tableName()],
                'r.top_education_code = d.code and d.status = 1 and d.type = 1');
        }
        //510019	最高教育经历一级专业
        //510020	最高教育经历二级专业
        //510021	最高教育经历三级专业
        //510022	最高教育经历专业
        //510030	最高学历毕业时间
        //510031	最高学历院校类别
        if (in_array(510019, $params['title_select']) || in_array(510020, $params['title_select']) || in_array(510021,
                $params['title_select']) || in_array(510022, $params['title_select']) || in_array(510026,
                $params['title_select']) || in_array(510030, $params['title_select']) || in_array(510031,
                $params['title_select'])) {
            $query->leftJoin(['re' => BaseResumeEducation::tableName()],
                're.id = r.last_education_id and re.status = 1');
            if (in_array(510019, $params['title_select'])) {
                $query->leftJoin(['m1' => BaseMajor::tableName()], 'm1.id = re.major_id_level_1 and m1.status = 1');
            }
            if (in_array(510020, $params['title_select'])) {
                $query->leftJoin(['m2' => BaseMajor::tableName()], 'm2.id = re.major_id_level_2 and m2.status = 1');
            }
            if (in_array(510021, $params['title_select'])) {
                $query->leftJoin(['m3' => BaseMajor::tableName()], 'm3.id = re.major_id_level_3 and m3.status = 1');
            }
            if (in_array(510022, $params['title_select'])) {
                $query->leftJoin(['m' => BaseMajor::tableName()], 'm.id = re.major_id and m.status = 1');
            }
        }

        //处理特殊筛选条件
        $select = [
            'c.id as company_id',
            'j.id as job_id',
            'a.id as announcement_id',
            'r.id as resume_id',
            'r.member_id as resume_member_id',
            'jar.id as job_apply_record_id',
            'jar.apply_id as job_apply_record_apply_id',
        ];
        foreach ($data as $item) {
            //510001	单位ID
            //510002	单位名称
            //510003	职位ID
            //510004	职位名称
            //510005	公告ID
            //510006	公告名称
            //510007	人才ID
            //510008	人才姓名
            //510009	户籍/国籍
            //510010	性别
            //510011	职称
            //510012	出生年月
            //510013	身份类型
            //510018	最高学历
            //510019	最高教育经历一级专业
            //510020	最高教育经历二级专业
            //510021	最高教育经历三级专业
            //510022	最高教育经历专业
            //510023	研究方向
            //510027	投递时间
            //510028	职位代码
            //510029	婚姻状况
            //510030	最高学历毕业时间
            //510031	最高学历院校类别
            //510033	政治面貌

            //特殊处理
            //510014	是否博士后经历
            //510015	最晚毕业的博士的院校
            //510016	最晚毕业的硕士的院校
            //510017	最晚毕业的本科的院校
            //510024	博士毕业时间（年月）
            //510025	是否海外经历
            //510026	最高教育经历是否985/211
            //510032	现工作单位
            //510034	投递状态

            // 单位ID
            if ($item['code'] == 510001) {
                array_push($select, 'c.uuid as company_uuid');
                continue;
            }
            // 单位名称
            if ($item['code'] == 510002) {
                array_push($select, 'c.full_name as company_name');
                continue;
            }
            // 职位ID
            if ($item['code'] == 510003) {
                array_push($select, 'j.uuid as job_uuid');
                continue;
            }
            // 职位名称
            if ($item['code'] == 510004) {
                array_push($select, 'j.name as job_name');
                continue;
            }
            // 公告ID
            if ($item['code'] == 510005) {
                $select[] = 'a.uuid as announcement_uuid';
                continue;
            }
            // 公告名称
            if ($item['code'] == 510006) {
                $select[] = 'a.title as announcement_name';
                continue;
            }
            // 人才ID
            if ($item['code'] == 510007) {
                $select[] = 'r.uuid as resume_uuid';
                continue;
            }
            // 人才姓名
            if ($item['code'] == 510008) {
                array_push($select, 'r.name as resume_name');
                continue;
            }
            // 户籍/国籍
            if ($item['code'] == 510009) {
                array_push($select, 'area.name as household_register_name');
                continue;
            }
            // 性别
            if ($item['code'] == 510010) {
                array_push($select, "IF(r.gender=1,'男','女') as gender_name");
                continue;
            }
            // 职称
            if ($item['code'] == 510011) {
                array_push($select, 'r.title_id');
                continue;
            }
            // 出生年月
            if ($item['code'] == 510012) {
                array_push($select, "date_format(r.birthday, '%Y-%m') as birthday");
                continue;
            }
            // 身份类型
            if ($item['code'] == 510013) {
                array_push($select, 'r.identity_type');
                continue;
            }
            // 最高学历
            if ($item['code'] == 510018) {
                array_push($select, 'd.name as top_education_name');
                continue;
            }
            // 最高教育经历一级专业
            if ($item['code'] == 510019) {
                array_push($select, 'm1.name as major_name_level_1');
                continue;
            }
            // 最高教育经历二级专业
            if ($item['code'] == 510020) {
                array_push($select, 'm2.name as major_name_level_2');
                continue;
            }
            // 最高教育经历三级专业
            if ($item['code'] == 510021) {
                array_push($select, 'm3.name as major_name_level_3');
                continue;
            }
            // 最高教育经历专业
            if ($item['code'] == 510022) {
                array_push($select, 'm.name as major_name');
                continue;
            }
            // 研究方向
            if ($item['code'] == 510023) {
                array_push($select, 'rrd.content as research_direction');
                continue;
            }
            // 最高教育经历是否985/211 is_project_school
            if ($item['code'] == 510026) {
                array_push($select, 're.is_project_school as is_top_education_985_211');
                continue;
            }
            // 投递时间
            if ($item['code'] == 510027) {
                array_push($select, 'jar.add_time as apply_time');
                continue;
            }
            // 职位代码
            if ($item['code'] == 510028) {
                $select[] = 'j.code as job_code';
                continue;
            }
            // 婚姻状况
            if ($item['code'] == 510029) {
                $select[] = 'r.marriage';
                continue;
            }
            //510030	最高学历毕业时间
            if ($item['code'] == 510030) {
                $select[] = 're.end_date as top_education_end_date';
                continue;
            }
            //510031	最高学历院校类别
            if ($item['code'] == 510031) {
                $select[] = 're.is_abroad as top_education_is_abroad';
                $select[] = 're.is_project_school as top_education_is_project_school';
                continue;
            }
            // 政治面貌
            if ($item['code'] == 510033) {
                $select[] = 'r.political_status_id';
                continue;
            }
        }
        $query->select($select);

        return $query;
    }

    /**
     * tabs=5 tabs_type=1 的数据处理方法
     * @param $data
     * @param $params
     * @return mixed
     */
    public static function handleData51($data, $params)
    {
        $titleSelect = $params['title_select'];
        foreach ($data as &$item) {
            //处理一下职称
            if (in_array(510011, $titleSelect)) {
                $item['title_name'] = BaseResume::getTitleName($item['title_id']);
            }
            //身份类型
            if (in_array(510013, $titleSelect)) {
                $item['identity_type_name'] = BaseResume::IDENTITY_TEXT_LIST[$item['identity_type']] ?: '';
            }
            //最高学历是否985/211
            if (in_array(510026, $titleSelect)) {
                $item['is_top_education_985_211_name'] = $item['is_top_education_985_211'] == 1 ? '是' : '否';
            }
            //婚姻状况文案
            if (in_array(510029, $titleSelect)) {
                $item['marriage_name'] = $item['marriage'] > 0 ? BaseResume::MARRIAGE_LIST[$item['marriage']] : '';
            }
            //最高学历院校类别
            if (in_array(510031, $titleSelect)) {
                $top_education_school_type = [];
                if ($item['top_education_is_project_school'] == 1) {
                    $top_education_school_type[] = '985/211';
                }
                if ($item['top_education_is_abroad'] == 1) {
                    $top_education_school_type[] = '留学';
                }
                $item['top_education_school_type'] = implode(',', $top_education_school_type);
            }
            //政治面貌文案
            if (in_array(510033, $titleSelect)) {
                $item['political_status_name'] = $item['political_status_id'] > 0 ? BaseDictionary::getPoliticalStatusName($item['political_status_id']) : '';
            }

            //特殊处理
            //510014	是否博士后经历
            //510015	最晚毕业的博士的院校
            //510016	最晚毕业的硕士的院校
            //510017	最晚毕业的本科的院校
            //510024	博士毕业时间（年月）
            //510025	是否海外经历
            //510026	最高学历是否985/211
            //510032	现工作单位
            //510034	投递状态
            //510035	硕士三级专业
            //510036	本科三级专业

            // 是否博士后经历
            if (in_array(510014, $titleSelect)) {
                //工作经历中勾选了博士后经历
                $item['is_postdoctoral']      = BaseResumeWork::checkPostdocStatus($item['resume_id']);
                $item['is_postdoctoral_name'] = $item['is_postdoctoral'] ? '是' : '否';
            }
            // 最晚毕业的博士的院校
            if (in_array(510015, $titleSelect)) {
                //获取最晚毕业的博士的院校
                $item['last_doctoral_school'] = BaseResumeEducation::getLastSchool($item['resume_id'],
                    BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE);
            }
            // 最晚毕业的硕士的院校
            if (in_array(510016, $titleSelect)) {
                //获取最晚毕业的硕士的院校
                $item['last_master_school'] = BaseResumeEducation::getLastSchool($item['resume_id'],
                    BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE);
            }
            // 最晚毕业的本科的院校
            if (in_array(510017, $titleSelect)) {
                //获取最晚毕业的本科的院校
                $item['last_undergraduate_school'] = BaseResumeEducation::getLastSchool($item['resume_id'],
                    BaseResumeEducation::EDUCATION_TYPE_UNDERGRADUATE_CODE);
            }
            // 博士毕业时间（年月）
            if (in_array(510024, $titleSelect)) {
                //获取博士毕业时间（年月）
                $item['doctoral_graduation_time'] = BaseResumeEducation::getLastlTime($item['resume_id'],
                    BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE);
            }
            // 是否海外经历
            if (in_array(510025, $titleSelect)) {
                //获取是否海外经历
                $item['is_abroad']      = BaseResume::checkResumeAbroad($item['resume_id']);
                $item['is_abroad_name'] = $item['is_abroad'] ? '是' : '否';
            }
            // 现工作单位
            if (in_array(510032, $titleSelect)) {
                //获取现工作单位
                $workLastInfo                   = BaseResumeWork::getLastWorkInfo($item['resume_member_id']);
                $item['work_unit_company_name'] = $workLastInfo ? ($workLastInfo['company'] ?: '') : '';
            }
            // 投递状态
            if (in_array(510034, $titleSelect)) {
                //获取投递状态
                if ($item['job_apply_record_apply_id'] > 0) {
                    $applyInfo = BaseJobApply::findOne($item['job_apply_record_apply_id']);
                    if ($applyInfo) {
                        $item['apply_status_name'] = BaseJobApply::STATUS_LIST[$applyInfo->status];
                    } else {
                        $item['apply_status_name'] = '已投递';
                    }
                } else {
                    $item['apply_status_name'] = '已投递';
                }
            }
            // 最晚毕业的硕士的院校
            if (in_array(510035, $titleSelect)) {
                //获取最晚毕业的硕士的院校
                $item['last_master_major_name'] = BaseResumeEducation::getEducationLastMajor($item['resume_id'],
                    BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE);
            }
            // 最晚毕业的本科的院校
            if (in_array(510036, $titleSelect)) {
                //获取最晚毕业的本科的院校
                $item['last_undergraduate_major_name'] = BaseResumeEducation::getEducationLastMajor($item['resume_id'],
                    BaseResumeEducation::EDUCATION_TYPE_UNDERGRADUATE_CODE);
            }
        }

        return $data;
    }

    /**
     * tabs=5 tabs_type=2 的获取Query处理方法
     * @return mixed
     */
    public static function getQuery52()
    {
        $alias = 'jar';
        $query = BaseJobApplyRecord::find()
            ->alias($alias)
            ->leftJoin(['j' => BaseJob::tableName()], 'j.id = jar.job_id')
            ->leftJoin(['r' => BaseResume::tableName()], 'r.id = jar.resume_id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = jar.company_id')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id = jar.announcement_id')
            ->leftJoin(['ar' => BaseArticle::tableName()], 'ar.id = a.article_id')
            ->groupBy('c.id');

        return $query;
    }

    /**
     * tabs=5 tabs_type=2 的where处理方法
     * @param $query
     * @param $data
     * @param $params
     * @return mixed
     */
    public static function where52($query, $data, $params)
    {
        //处理公共筛选条件
        $query = self::whereCommon($query, $params, 'jar');

        //处理特殊筛选条件
        $select = [
            'c.id as company_id',
        ];
        foreach ($data as $item) {
            //  520001	单位ID
            //  520002	单位名称
            //  520003	投递量
            //  520004	公告投递量
            //  520005	职位投递量
            //  520006	博士简历数量
            //  520007	硕士简历数量
            //  520008	本科简历数量
            //  520009	站内投递数量
            //  520010	站外投递数量
            //  520011	平台投递数量
            //  520012	邮件投递数量
            //  520013	链接投递数量

            //  520001 单位ID
            if ($item['code'] == 520001) {
                array_push($select, 'c.uuid as company_uuid');
                continue;
            }
            //  520002 单位名称
            if ($item['code'] == 520002) {
                array_push($select, 'c.full_name as company_name');
                continue;
            }
            //  520003 投递量
            if ($item['code'] == 520003) {
                array_push($select, 'count(jar.id) as apply_total');
                continue;
            }
            //  520004 公告投递量
            if ($item['code'] == 520004) {
                array_push($select, 'sum(IF(jar.announcement_id>0,1,0)) as apply_announcement_total');
                continue;
            }
            //  520005 职位投递量
            if ($item['code'] == 520005) {
                array_push($select, 'count(jar.job_id) as apply_job_total');
                continue;
            }
            //  520006 博士简历数量
            if ($item['code'] == 520006) {
                array_push($select, 'sum(IF(r.top_education_code=4,1,0)) as doctoral_resume_total');
                continue;
            }
            //  520007 硕士简历数量
            if ($item['code'] == 520007) {
                array_push($select, 'sum(IF(r.top_education_code=3,1,0)) as master_resume_total');
                continue;
            }
            //  520008 本科简历数量
            if ($item['code'] == 520008) {
                array_push($select, 'sum(IF(r.top_education_code=2,1,0)) as undergraduate_resume_total');
                continue;
            }
            //  520009 站内投递数量
            if ($item['code'] == 520009) {
                array_push($select, 'sum(IF(jar.delivery_type=2,1,0)) as apply_inside_total');
                continue;
            }
            //  520010 站外投递数量
            if ($item['code'] == 520010) {
                array_push($select, 'sum(IF(jar.delivery_type=1,1,0)) as apply_outside_total');
                continue;
            }
            //  520011 平台投递数量
            if ($item['code'] == 520011) {
                array_push($select, 'sum(IF(jar.delivery_way=1,1,0)) as apply_platform_total');
                continue;
            }
            //  520012 邮件投递数量
            if ($item['code'] == 520012) {
                array_push($select, 'sum(IF(jar.delivery_way=2,1,0)) as apply_email_total');
                continue;
            }
            //  520013 链接投递数量
            if ($item['code'] == 520013) {
                array_push($select, 'sum(IF(jar.delivery_way=3,1,0)) as apply_link_total');
                continue;
            }
        }
        $query->select($select);

        return $query;
    }

    /**
     * tabs=5 tabs_type=2 的数据处理方法
     * @param $data
     * @param $titleSelect
     * @return mixed
     */
    public static function handleData52($data, $titleSelect)
    {
        //        foreach ($data as &$item) {
        //
        //        }

        return $data;
    }

    /**
     * tabs=6 tabs_type=1的获取Query处理方法
     * @return mixed
     */
    public static function getQuery61()
    {
        $alias = 's';
        $query = BaseShowcase::find()
            ->alias($alias)
            ->leftJoin(['h' => BaseHomePosition::tableName()], 'h.id = s.home_position_id')
            ->leftJoin(['a' => BaseAdmin::tableName()], 'a.id = s.creator_id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = s.company_id')
            ->andWhere('s.status!=' . BaseShowcase::STATUS_DELETE);

        return $query;
    }

    /**
     * tabs=6 tabs_type=1 的where处理方法
     * @param $query
     * @param $data
     * @param $params
     * @return mixed
     */
    public static function where61($query, $data, $params)
    {
        //处理公共筛选条件
        $query = self::whereCommon($query, $params, 's');

        //处理特殊筛选条件
        $select = [
            's.id',
            'c.id as company_id',
        ];
        foreach ($data as $item) {
            // 610001	广告位ID
            if ($item['code'] == 610001) {
                array_push($select, 's.id as showcase_id');
                continue;
            }
            //  610003	是否打包
            if ($item['code'] == 610003) {
                array_push($select, 's.is_packing');
                continue;
            }
            //  610004	广告位类型
            if ($item['code'] == 610004) {
                array_push($select, 's.type');
                continue;
            }
            //  610005	广告标题
            if ($item['code'] == 610005) {
                array_push($select, 's.title');
                continue;
            }
            //  610006	广告副标题
            if ($item['code'] == 610006) {
                array_push($select, 's.sub_title');
                continue;
            }
            //  610007	广告跳转地址
            if ($item['code'] == 610007) {
                array_push($select, 's.target_link');
                continue;
            }
            //  610008	所属平台
            if ($item['code'] == 610008) {
                array_push($select, 'h.platform_type');
                continue;
            }
            //  610009	广告位置
            if ($item['code'] == 610009) {
                array_push($select, 'h.name');
                continue;
            }
            //  610010	显示状态
            if ($item['code'] == 610010) {
                array_push($select, 's.is_show');
                continue;
            }
            // 610011	创建人
            if ($item['code'] == 610011) {
                array_push($select, 'a.name as creator');
                continue;
            }
            // 610012	创建时间
            if ($item['code'] == 610012) {
                array_push($select, 's.add_time');
                continue;
            }
            // 610013	广告状态
            if ($item['code'] == 610013) {
                array_push($select, 's.status');
                continue;
            }
            // 610014	关联单位ID
            if ($item['code'] == 610014) {
                array_push($select, 'c.uuid as company_uuid');
                continue;
            }
            // 610015	关联单位名称
            if ($item['code'] == 610015) {
                array_push($select, 'c.full_name as company_name');
                continue;
            }
            // 610016	生效时间
            if ($item['code'] == 610016) {
                array_push($select, 's.online_time');
                continue;
            }
            // 610017	失效时间
            if ($item['code'] == 610017) {
                array_push($select, 's.offline_time');
                continue;
            }
            // 610018	次标题
            if ($item['code'] == 610018) {
                array_push($select, 's.second_title');
                continue;
            }
            // 610019	图片地址
            if ($item['code'] == 610019) {
                array_push($select, 's.image_url');
                continue;
            }
            // 610020	图片链接地址
            if ($item['code'] == 610020) {
                array_push($select, 's.image_link');
                continue;
            }
            // 610021	跳转类型
            if ($item['code'] == 610021) {
                array_push($select, 's.target_link_type');
                continue;
            }
            // 610022	页面类型
            if ($item['code'] == 610022) {
                array_push($select, 's.page_link_type');
                continue;
            }
            // 610023	其他图片地址
            if ($item['code'] == 610023) {
                array_push($select, 's.other_image_url');
                continue;
            }
        }
        $query->select($select);

        return $query;
    }

    /**
     * tabs=6 tabs_type=1的数据处理方法
     * @param $data
     * @param $titleSelect
     * @return mixed
     */
    public static function handleData61($data, $titleSelect)
    {
        foreach ($data as &$item) {
            // 610002	打包ID
            if (in_array(610002, $titleSelect)) {
                $item['packing_id'] = self::getPackageId($item['id']);
            }
            $item['is_packing_txt']       = $item['is_packing'] == BaseShowcase::IS_PACKAGE_YES ? '是' : '否';
            $item['platform_type_title']  = BaseHomePosition::PLATFORM_TYPE_LIST[$item['platform_type']];
            $item['statusTxt']            = BaseShowcase::STATUS_LIST[$item['status']];
            $item['target_link_type_txt'] = BaseShowcase::TARGET_LINK_TYPE_LIST[$item['target_link_type']];
            $item['page_link_type_txt']   = BaseShowcase::PAGE_LINK_TYPE_LIST[$item['page_link_type']];
            $item['platform_type_title']  = BaseHomePosition::PLATFORM_TYPE_LIST[$item['platform_type']];
            $item['status_txt']           = BaseShowcase::STATUS_LIST[$item['status']];
            $item['is_show_txt']          = $item['is_show'] == BaseShowcase::IS_SHOW_YES ? '显示' : '隐藏';
            $item['type_name']            = '';
            if ($v['type']) {
                $type_name_arr = [];
                foreach (explode(',', $v['type']) as $item) {
                    $item_name       = BaseShowcase::TYPE_NAME[$item];
                    $type_name_arr[] = $item_name;
                }
                $item['type_name'] = implode(',', $type_name_arr);
            }
        }

        return $data;
    }

    // +=配合处理数据的函数====开始======================================================================================================

    /**
     * 获取一个单位某一段时间内的投递数量
     * 是否去重根据第三个参数区分
     */
    public static function getCompanyApplyTotal($companyId, $params, $isDistinct = true)
    {
        $query = BaseJobApplyRecord::find()
            ->where([
                'company_id' => $companyId,
            ]);
        if ($params['filter_time_key'] == 2) {
            $query->andWhere([
                'between',
                'add_time',
                $params['add_time_start'],
                $params['add_time_end'],
            ]);
        }
        if ($isDistinct) {
            $query->groupBy('resume_id');
        }

        return $query->count();
    }

    /**
     * 获取单位某一时间段内的投递数据统计
     */
    public static function getCompanyApplyRecordTotal($companyId, $params)
    {
        $query = BaseJobApplyRecord::find()
            ->select([
                'job_id',
                'resume_id',
                'company_id',
                'announcement_id',
                'delivery_type',
                'delivery_way',
            ])
            ->where([
                'company_id' => $companyId,
            ]);
        if ($params['filter_time_key'] == 2) {
            $query->andWhere([
                'between',
                'add_time',
                $params['add_time_start'],
                $params['add_time_end'],
            ]);
        }
        $apply_data = $query->asArray()
            ->all();
        //定义上述变量
        $apply_num                             = count($apply_data);
        $apply_person_arr                      = [];
        $apply_person_num                      = 0;
        $apply_doctoral_num                    = 0;
        $apply_doctoral_person_arr             = [];
        $apply_doctoral_person_num             = 0;
        $apply_master_num                      = 0;
        $apply_master_person_arr               = [];
        $apply_master_person_num               = 0;
        $apply_undergraduate_num               = 0;
        $apply_undergraduate_person_arr        = [];
        $apply_undergraduate_person_num        = 0;
        $apply_doctoral_abroad_person_arr      = [];
        $apply_doctoral_abroad_person_num      = 0;
        $apply_doctoral_abroad_num             = 0;
        $apply_doctoral_985_211_person_arr     = [];
        $apply_doctoral_985_211_person_num     = 0;
        $apply_doctoral_985_211_num            = 0;
        $apply_doctoral_work_year_2_person_arr = [];
        $apply_doctoral_work_year_person_2     = 0;
        $apply_doctoral_work_year_2            = 0;
        $apply_doctoral_worker_person_arr      = [];
        $apply_doctoral_worker_person          = 0;
        $apply_doctoral_worker                 = 0;
        $apply_doctoral_graduate_person_arr    = [];
        $apply_doctoral_graduate_person        = 0;
        $apply_doctoral_graduate               = 0;
        $apply_title_g_person_arr              = [];
        $apply_title_g_person                  = 0;
        $apply_title_g                         = 0;
        $apply_title_z_person_arr              = [];
        $apply_title_z_person                  = 0;
        $apply_title_z                         = 0;
        $apply_title_fg_person_arr             = [];
        $apply_title_fg_person                 = 0;
        $apply_title_fg                        = 0;
        $apply_delivery_outer                  = 0;
        $apply_delivery_outer_person_arr       = [];
        $apply_delivery_outer_person           = 0;
        $apply_delivery_outside                = 0;
        $apply_delivery_outside_person_arr     = [];
        $apply_delivery_outside_person         = 0;
        $apply_delivery_platform               = 0;
        $apply_delivery_email                  = 0;
        $apply_delivery_link                   = 0;

        foreach ($apply_data as $item) {
            //获取简历信息
            $resume_info = BaseResume::findOne($item['resume_id']);
            //投递人数
            if (!in_array($item['resume_id'], $apply_person_arr)) {
                $apply_person_arr[] = $item['resume_id'];
                $apply_person_num++;
            }
            //博士投递量与投递人数
            if ($resume_info->top_education_code == BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE) {
                $apply_doctoral_num++;
                if (!in_array($item['resume_id'], $apply_doctoral_person_arr)) {
                    $apply_doctoral_person_arr[] = $item['resume_id'];
                    $apply_doctoral_person_num++;
                }
            }
            //硕士投递量与投递人数
            if ($resume_info->top_education_code == BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE) {
                $apply_master_num++;
                if (!in_array($item['resume_id'], $apply_master_person_arr)) {
                    $apply_master_person_arr[] = $item['resume_id'];
                    $apply_master_person_num++;
                }
            }
            //本科投递量与投递人数
            if ($resume_info->top_education_code == BaseResumeEducation::EDUCATION_TYPE_UNDERGRADUATE_CODE) {
                $apply_undergraduate_num++;
                if (!in_array($item['resume_id'], $apply_undergraduate_person_arr)) {
                    $apply_undergraduate_person_arr[] = $item['resume_id'];
                    $apply_undergraduate_person_num++;
                }
            }
            //博士海外经历
            if ($resume_info->top_education_code == BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE && BaseResume::checkResumeAbroad($item['resume_id'])) {
                $apply_doctoral_abroad_num++;
                if (!in_array($item['resume_id'], $apply_doctoral_abroad_person_arr)) {
                    $apply_doctoral_abroad_person_arr[] = $item['resume_id'];
                    $apply_doctoral_abroad_person_num++;
                }
            }
            //博士985/211
            if (BaseResumeEducation::find()
                ->where([
                    'resume_id'         => $item['resume_id'],
                    'education_id'      => BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                    'is_project_school' => BaseResumeEducation::IS_PROJECT_SCHOOL_YES,
                ])
                ->exists()) {
                $apply_doctoral_985_211_num++;
                if (!in_array($item['resume_id'], $apply_doctoral_985_211_person_arr)) {
                    $apply_doctoral_985_211_person_arr[] = $item['resume_id'];
                    $apply_doctoral_985_211_person_num++;
                }
            }
            //博士且工作2年及以上
            if ($resume_info->top_education_code == BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE && $resume_info->work_experience >= 2) {
                $apply_doctoral_work_year_2++;
                if (!in_array($item['resume_id'], $apply_doctoral_work_year_2_person_arr)) {
                    $apply_doctoral_work_year_2_person_arr[] = $item['resume_id'];
                    $apply_doctoral_work_year_person_2++;
                }
            }
            //博士为职场人
            if ($resume_info->top_education_code == BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE && $resume_info->identity_type == BaseResume::IDENTITY_TYPE_WORKER) {
                $apply_doctoral_worker++;
                if (!in_array($item['resume_id'], $apply_doctoral_worker_person_arr)) {
                    $apply_doctoral_worker_person_arr[] = $item['resume_id'];
                    $apply_doctoral_worker_person++;
                }
            }
            //博士为应届生/在校生
            if ($resume_info->top_education_code == BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE && $resume_info->identity_type == BaseResume::IDENTITY_TYPE_GRADUATE) {
                $apply_doctoral_graduate++;
                if (!in_array($item['resume_id'], $apply_doctoral_graduate_person_arr)) {
                    $apply_doctoral_graduate_person_arr[] = $item['resume_id'];
                    $apply_doctoral_graduate_person++;
                }
            }
            //职称
            if ($resume_info->title_id) {
                $parent_code = BaseDictionary::find()
                    ->alias('dd')
                    ->select('d.code')
                    ->where([
                        'dd.code'   => explode(',', $resume_info->title_id),
                        'dd.status' => 1,
                        'dd.type'   => BaseDictionary::TYPE_TITLE,
                    ])
                    ->leftJoin(['d' => BaseDictionary::tableName()], 'd.id = dd.parent_id')
                    ->column();

                //高级职称
                if (in_array(BaseDictionary::TITLE_HIGH_ID, $parent_code)) {
                    $apply_title_g++;
                    if (!in_array($item['resume_id'], $apply_title_g_person_arr)) {
                        $apply_title_g_person_arr[] = $item['resume_id'];
                        $apply_title_g_person++;
                    }
                }
                //中级职称
                if (in_array(BaseDictionary::TITLE_MIDDLE_ID, $parent_code)) {
                    $apply_title_z++;
                    if (!in_array($item['resume_id'], $apply_title_z_person_arr)) {
                        $apply_title_z_person_arr[] = $item['resume_id'];
                        $apply_title_z_person++;
                    }
                }
                //副高级职称
                if (in_array(BaseDictionary::TITLE_VICE_HIGH_ID, $parent_code)) {
                    $apply_title_fg++;
                    if (!in_array($item['resume_id'], $apply_title_fg_person_arr)) {
                        $apply_title_fg_person_arr[] = $item['resume_id'];
                        $apply_title_fg_person++;
                    }
                }
            }
            //投递方式
            if ($item['delivery_type'] == BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE) {
                $apply_delivery_outer++;
                if (!in_array($item['resume_id'], $apply_delivery_outer_person_arr)) {
                    $apply_delivery_outer_person_arr[] = $item['resume_id'];
                    $apply_delivery_outer_person++;
                }
            }
            if ($item['delivery_type'] == BaseJobApplyRecord::DELIVERY_TYPE_INSIDE) {
                $apply_delivery_outside++;
                if (!in_array($item['resume_id'], $apply_delivery_outside_person_arr)) {
                    $apply_delivery_outside_person_arr[] = $item['resume_id'];
                    $apply_delivery_outside_person++;
                }
            }
            //投递渠道
            if ($item['delivery_way'] == BaseJobApplyRecord::DELIVERY_WAY_PLATFORM) {
                $apply_delivery_platform++;
            }
            if ($item['delivery_way'] == BaseJobApplyRecord::DELIVERY_WAY_EMAIL) {
                $apply_delivery_email++;
            }
            if ($item['delivery_way'] == BaseJobApplyRecord::DELIVERY_WAY_LINK) {
                $apply_delivery_link++;
            }
        }

        return [
            'apply_num'                         => $apply_num,
            'apply_person_num'                  => $apply_person_num,
            'apply_doctoral_num'                => $apply_doctoral_num,
            'apply_doctoral_person_num'         => $apply_doctoral_person_num,
            'apply_master_num'                  => $apply_master_num,
            'apply_master_person_num'           => $apply_master_person_num,
            'apply_undergraduate_num'           => $apply_undergraduate_num,
            'apply_undergraduate_person_num'    => $apply_undergraduate_person_num,
            'apply_doctoral_abroad_num'         => $apply_doctoral_abroad_num,
            'apply_doctoral_abroad_person_num'  => $apply_doctoral_abroad_person_num,
            'apply_doctoral_985_211_num'        => $apply_doctoral_985_211_num,
            'apply_doctoral_985_211_person_num' => $apply_doctoral_985_211_person_num,
            'apply_doctoral_work_year_2'        => $apply_doctoral_work_year_2,
            'apply_doctoral_work_year_person_2' => $apply_doctoral_work_year_person_2,
            'apply_doctoral_worker'             => $apply_doctoral_worker,
            'apply_doctoral_worker_person'      => $apply_doctoral_worker_person,
            'apply_doctoral_graduate'           => $apply_doctoral_graduate,
            'apply_doctoral_graduate_person'    => $apply_doctoral_graduate_person,
            'apply_title_g'                     => $apply_title_g,
            'apply_title_g_person'              => $apply_title_g_person,
            'apply_title_z'                     => $apply_title_z,
            'apply_title_z_person'              => $apply_title_z_person,
            'apply_title_fg'                    => $apply_title_fg,
            'apply_title_fg_person'             => $apply_title_fg_person,
            'apply_delivery_outer'              => $apply_delivery_outer,
            'apply_delivery_outer_person'       => $apply_delivery_outer_person,
            'apply_delivery_outside'            => $apply_delivery_outside,
            'apply_delivery_outside_person'     => $apply_delivery_outside_person,
            'apply_delivery_platform'           => $apply_delivery_platform,
            'apply_delivery_email'              => $apply_delivery_email,
            'apply_delivery_link'               => $apply_delivery_link,
        ];
    }

    /**
     * 获取一个职位的全部投递数据统计
     * 注意这里都是对人去重处理的
     */
    public static function getJobApplyRecordTotal($jobId, $params)
    {
        $query = BaseJobApplyRecord::find()
            ->select([
                'job_id',
                'resume_id',
                'company_id',
                'announcement_id',
            ])
            ->where([
                'job_id' => $jobId,
            ]);
        if ($params['filter_time_key'] == 2) {
            $query->andWhere([
                'between',
                'add_time',
                $params['add_time_start'],
                $params['add_time_end'],
            ]);
        }
        $apply_data = $query->asArray()
            ->all();
        //定义上述变量
        $apply_num                  = count($apply_data);
        $apply_person_arr           = [];
        $apply_person_num           = 0;
        $apply_doctoral_num         = 0;
        $apply_doctoral_person_arr  = [];
        $apply_doctoral_person_num  = 0;
        $apply_master_num           = 0;
        $apply_master_person_arr    = [];
        $apply_master_person_num    = 0;
        $apply_doctoral_abroad_num  = 0;
        $apply_doctoral_985_211_num = 0;
        $apply_doctoral_work_year_2 = 0;
        $apply_doctoral_worker      = 0;
        $apply_doctoral_graduate    = 0;
        $apply_title_g              = 0;
        $apply_title_z              = 0;
        $apply_title_fg             = 0;

        foreach ($apply_data as $item) {
            //获取简历信息
            $resume_info = BaseResume::findOne($item['resume_id']);
            //投递人数
            if (!in_array($item['resume_id'], $apply_person_arr)) {
                $apply_person_arr[] = $item['resume_id'];
                $apply_person_num++;
            }
            //博士投递量与投递人数
            if ($resume_info->top_education_code == BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE) {
                $apply_doctoral_num++;
                if (!in_array($item['resume_id'], $apply_doctoral_person_arr)) {
                    $apply_doctoral_person_arr[] = $item['resume_id'];
                    $apply_doctoral_person_num++;
                }
            }
            //硕士投递量与投递人数
            if ($resume_info->top_education_code == BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE) {
                $apply_master_num++;
                if (!in_array($item['resume_id'], $apply_master_person_arr)) {
                    $apply_master_person_arr[] = $item['resume_id'];
                    $apply_master_person_num++;
                }
            }
            //博士海外经历
            if ($resume_info->top_education_code == BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE && BaseResume::checkResumeAbroad($item['resume_id'])) {
                $apply_doctoral_abroad_num++;
            }
            //博士985/211
            if (BaseResumeEducation::find()
                ->where([
                    'resume_id'         => $item['resume_id'],
                    'education_id'      => BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                    'is_project_school' => BaseResumeEducation::IS_PROJECT_SCHOOL_YES,
                ])
                ->exists()) {
                $apply_doctoral_985_211_num++;
            }
            //博士且工作2年及以上
            if ($resume_info->top_education_code == BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE && $resume_info->work_experience >= 2) {
                $apply_doctoral_work_year_2++;
            }
            //博士为职场人
            if ($resume_info->top_education_code == BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE && $resume_info->identity_type == BaseResume::IDENTITY_TYPE_WORKER) {
                $apply_doctoral_worker++;
            }
            //博士为应届生/在校生
            if ($resume_info->top_education_code == BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE && $resume_info->identity_type == BaseResume::IDENTITY_TYPE_GRADUATE) {
                $apply_doctoral_graduate++;
            }
            //职称
            if ($resume_info->title_id) {
                $parent_code = BaseDictionary::find()
                    ->alias('dd')
                    ->select('d.code')
                    ->where([
                        'dd.code'   => explode(',', $resume_info->title_id),
                        'dd.status' => 1,
                        'dd.type'   => BaseDictionary::TYPE_TITLE,
                    ])
                    ->leftJoin(['d' => BaseDictionary::tableName()], 'd.id = dd.parent_id')
                    ->column();

                //高级职称
                if (in_array(BaseDictionary::TITLE_HIGH_ID, $parent_code)) {
                    $apply_title_g++;
                }
                //中级职称
                if (in_array(BaseDictionary::TITLE_MIDDLE_ID, $parent_code)) {
                    $apply_title_z++;
                }
                //副高级职称
                if (in_array(BaseDictionary::TITLE_VICE_HIGH_ID, $parent_code)) {
                    $apply_title_fg++;
                }
            }
        }

        return [
            'apply_num'                  => $apply_num,
            'apply_person_num'           => $apply_person_num,
            'apply_doctoral_num'         => $apply_doctoral_num,
            'apply_doctoral_person_num'  => $apply_doctoral_person_num,
            'apply_master_num'           => $apply_master_num,
            'apply_master_person_num'    => $apply_master_person_num,
            'apply_doctoral_abroad_num'  => $apply_doctoral_abroad_num,
            'apply_doctoral_985_211_num' => $apply_doctoral_985_211_num,
            'apply_doctoral_work_year_2' => $apply_doctoral_work_year_2,
            'apply_doctoral_worker'      => $apply_doctoral_worker,
            'apply_doctoral_graduate'    => $apply_doctoral_graduate,
            'apply_title_g'              => $apply_title_g,
            'apply_title_z'              => $apply_title_z,
            'apply_title_fg'             => $apply_title_fg,
        ];
    }

    /**
     * 获取一个职位的一段时间内的点击数量数据统计
     */
    private static function getJobClickTotal($jobId, $startTime, $endTime)
    {
        return BaseJobClickLog::find()
            ->where([
                'job_id' => $jobId,
            ])
            ->andWhere([
                'between',
                'add_time',
                $startTime,
                $endTime,
            ])
            ->count();
    }

    /**
     * 获取一个职位的一段时间内的公告数量数据统计
     */
    private static function getAnnouncementClickTotal($announcementId, $startTime = '', $endTime = '')
    {
        $articleId = BaseAnnouncement::find()
            ->select('article_id')
            ->where(['id' => $announcementId])
            ->column();
        $query     = BaseArticleClickLog::find()
            ->where([
                'article_id' => $articleId,
            ]);
        if (!empty($startTime) && !empty($endTime)) {
            $query->andWhere([
                'between',
                'add_time',
                $startTime,
                $endTime,
            ]);
        }

        return $query->count();
    }

    /**
     * 获取一个单位主页的一段时间内的点击数量数据统计
     */
    private static function getCompanyClickTotal($companyId, $startTime, $endTime)
    {
        return BaseCompanyClickLog::find()
            ->where([
                'company_id' => $companyId,
            ])
            ->andWhere([
                'between',
                'add_time',
                $startTime,
                $endTime,
            ])
            ->count();
    }

    /**
     * 获取职位的收藏量
     */
    private static function getJobCollectCount($jobId, $params)
    {
        $query = BaseJobCollect::find()
            ->where([
                'status' => BaseJobCollect::STATUS_ACTIVE,
                'job_id' => $jobId,
            ]);
        if ($params['filter_time_key'] == 2) {
            $query->andWhere([
                'between',
                'add_time',
                $params['add_time_start'],
                $params['add_time_end'],
            ]);
        }

        return $query->count();
    }

    /**
     * 获取公告的收藏量
     */
    private static function getAnnouncementCollectCount($announcementId, $params)
    {
        $query = BaseAnnouncementCollect::find()
            ->where([
                'status'          => BaseAnnouncementCollect::STATUS_ACTIVE,
                'announcement_id' => $announcementId,
            ]);
        if ($params['filter_time_key'] == 2) {
            $query->andWhere([
                'between',
                'add_time',
                $params['add_time_start'],
                $params['add_time_end'],
            ]);
        }

        return $query->count();
    }

    /**
     * 获取单位主页的收藏量
     */
    private static function getCompanyCollectCount($companyId, $params)
    {
        $query = BaseCompanyCollect::find()
            ->where([
                'status'     => BaseCompanyCollect::STATUS_ACTIVE,
                'company_id' => $companyId,
            ]);
        if ($params['filter_time_key'] == 2) {
            $query->andWhere([
                'between',
                'add_time',
                $params['add_time_start'],
                $params['add_time_end'],
            ]);
        }

        return $query->count();
    }

    /**
     * 获取单位在某一段时间内的发布职位数量
     */
    private static function getCompanyJobCount($companyId, $params)
    {
        $query = BaseJob::find()
            ->where([
                'status'     => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
                'company_id' => $companyId,
            ]);
        if ($params['filter_time_key'] == 2) {
            $query->andWhere([
                'between',
                'first_release_time',
                $params['add_time_start'],
                $params['add_time_end'],
            ]);
        }

        return $query->count();
    }

    /**
     * 获取单位在某一段时间内的发布公告数量
     */
    private static function getCompanyAnnouncementCount($companyId, $params)
    {
        $query = BaseAnnouncement::find()
            ->alias('an')
            ->leftJoin(['ar' => BaseArticle::tableName()], 'an.article_id = ar.id')
            ->where([
                'an.company_id' => $companyId,
            ])
            ->andWhere([
                '<>',
                'ar.status',
                BaseArticle::STATUS_STAGING,
            ]);
        if ($params['filter_time_key'] == 2) {
            $query->andWhere([
                'between',
                'ar.first_release_time',
                $params['add_time_start'],
                $params['add_time_end'],
            ]);
        }

        return $query->count();
    }

    /**
     * 获取单位在某一段时间的职位点击量
     */
    private static function getCompanyJobClickCount($companyId, $params)
    {
        //先获取单位的所有职位ID
        $jobIds = BaseJob::find()
            ->select('id')
            ->where([
                'status'     => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
                'company_id' => $companyId,
            ])
            ->column();

        //调用统计职位点击量的方法
        return self::getJobClickTotal($jobIds, $params['add_time_start'], $params['add_time_end']);
    }

    /**
     * 获取单位在某一段时间的公告点击量
     */
    private static function getCompanyAnnouncementClickCount($companyId, $params)
    {
        //先获取单位的所有公告ID
        $announcementIds = BaseAnnouncement::find()
            ->alias('an')
            ->leftJoin(['ar' => BaseArticle::tableName()], 'an.article_id = ar.id')
            ->where([
                'an.company_id' => $companyId,
            ])
            ->andWhere([
                '<>',
                'ar.status',
                BaseArticle::STATUS_STAGING,
            ])
            ->column();

        //调用统计公告点击量的方法
        return self::getAnnouncementClickTotal($announcementIds, $params['add_time_start'], $params['add_time_end']);
    }

    /**
     * 获取某一段时间内的单位的邀面试数量
     */
    private static function getCompanyInterviewCount($companyId, $params)
    {
        $query = BaseJobApply::find()
            ->where([
                'company_id' => $companyId,
            ]);
        if ($params['filter_time_key'] == 2) {
            $query->andWhere([
                'between',
                'add_time',
                $params['add_time_start'],
                $params['add_time_end'],
            ]);
        }

        return $query->count();
    }

    /**
     * 获取打包ID
     */
    private static function getPackageId($showcaseId)
    {
        return BaseShowcasePackingRelationship::find()
                   ->select('showcase_packing_id')
                   ->where(['showcase_id' => $showcaseId])
                   ->asArray()
                   ->one()['showcase_packing_id'];
    }
    // +=配合处理数据的函数====结束======================================================================================================

}