<?php

namespace common\base\models;

use common\helpers\StringHelper;
use common\helpers\UUIDHelper;
use common\libs\CompanyAuthority\BaseRule;
use common\libs\CompanyAuthority\CompanyAuthorityClassify;
use common\models\JobApply;
use Yii;
use yii\db\ActiveRecord;
use yii\helpers\Url;

class BaseJobApply extends JobApply
{
    const ORDER_BY_DESC = 1;
    const ORDER_BY_ASC  = 2;

    const EQUITY_STATUS_EXPIRE = 0;//无权益
    const EQUITY_STATUS_EFFECT = 1;//有权益

    const STATUS_IS_CHECK        = -1;//已查看（特殊处理，不存入数据库，只是在这做标记状态）
    const STATUS_HANDLE_WAIT     = 1;//已投递
    const STATUS_THROUGH_FIRST   = 2;//通过初筛
    const STATUS_SEND_INVITATION = 3;//邀请面试
    const STATUS_INAPPROPRIATE   = 4;//不合适
    const STATUS_EMPLOYED        = 5;//已录用

    //求职者列表状态

    const PERSON_STATUS_LIST = [
        self::STATUS_HANDLE_WAIT     => '已投递',
        self::STATUS_IS_CHECK        => '已查看',
        self::STATUS_THROUGH_FIRST   => '通过初筛',
        self::STATUS_SEND_INVITATION => '邀请面试',
        self::STATUS_INAPPROPRIATE   => '不合适',
        self::STATUS_EMPLOYED        => '已录用',
    ];

    const  COMPANY_MARK_STATUS_ORIGINAL        = 0;//原始状态
    const  COMPANY_MARK_STATUS_NOT_CONNECT     = 1;//未接通
    const  COMPANY_MARK_STATUS_NOT_INTENTION   = 2;//无意向
    const  COMPANY_MARK_STATUS_NOT_FACE        = 3;//未到面
    const  COMPANY_MARK_STATUS_WAIT_EMPLOYMENT = 4;//待录用
    const  COMPANY_MARK_STATUS_ENTRY           = 5;//已入职
    const  COMPANY_MARK_STATUS_NOT_ENTRY       = 6;//未入职

    ////////////////////////
    // const STATUS_INAPPROPRIATE_REVOCATION = 99;//撤销不合适
    // const STATUS_ENTRY                    = 99;//已入职
    // const STATUS_ENTRY_NO                 = 99;//未入职
    // const STATUS_ENTRY_REVOCATION         = 99;//撤销已入职
    // const STATUS_ENTRY_NO_REVOCATION      = 99;//撤销未入职
    // const STATUS_EMPLOYED_WAIT            = 99;//待录用
    // const STATUS_EMPLOYED_REVOCATION      = 99;//撤销录用
    // const STATUS_BLOCK_CALL               = 99;//未接通
    // const STATUS_INTENTION_NO             = 99;//无意向
    // const STATUS_INTERVIEW_NO             = 99;//未到面

    //获取投递状态
    // const APPLY_STATUS_DELIVERY = 1;
    // const APPLY_STATUS_VIEW     = 2;
    // const APPLY_STATUS_PASS     = 3;
    // const APPLY_STATUS_INVITER  = 4;
    //
    // const APPLY_STATUS_LIST = [
    //     self::APPLY_STATUS_DELIVERY => '已投递',
    //     self::APPLY_STATUS_VIEW     => '被查看',
    //     self::APPLY_STATUS_PASS     => '通过初筛',
    //     self::APPLY_STATUS_INVITER  => '邀请面试',
    // ];

    const STATUS_INTERVIEW_TOBAGO = 9;
    const STATUS_INTERVIEW_ENDED  = 10;
    const STATUS_INTERVIEW_SEARCH = [
        self::STATUS_INTERVIEW_TOBAGO => '未进行',
        self::STATUS_INTERVIEW_ENDED  => '已结束',
    ];

    //限制多少天不能再次投递--禅道需求929修改
    // const LIMIT_APPLY_DAYS_ONE = 30;
    // const LIMIT_APPLY_DAYS_TWO = 180;
    ////////////////////////

    const IS_CHECK_YES = 1;//以查看
    const IS_CHECK_NO  = 0; //未查看

    const SOURCE_NO     = 1;  //自主 非代投
    const SOURCE_YES    = 2; //代投
    const SOURCE_INVITE = 3; //公司邀请投递

    const SOURCE_LIST = [
        self::SOURCE_NO     => '主动投递',
        self::SOURCE_YES    => '代投简历',
        self::SOURCE_INVITE => '单位邀请',
    ];

    //获取列表是否需要分页信息
    const NEED_PAGE_INFO_YES = true;
    const NEED_PAGE_INFO_NO  = false;

    const STATUS_LIST = [
        self::STATUS_HANDLE_WAIT     => '已投递',
        self::STATUS_THROUGH_FIRST   => '通过初筛',
        self::STATUS_SEND_INVITATION => '邀请面试',
        self::STATUS_INAPPROPRIATE   => '不合适',
        self::STATUS_EMPLOYED        => '已录用',
    ];

    const MARK_STATUS_LIST = [
        self::COMPANY_MARK_STATUS_NOT_CONNECT     => '未接通',
        self::COMPANY_MARK_STATUS_NOT_INTENTION   => '无意向',
        self::COMPANY_MARK_STATUS_NOT_FACE        => '未到面',
        self::COMPANY_MARK_STATUS_WAIT_EMPLOYMENT => '待录用',
        self::COMPANY_MARK_STATUS_ENTRY           => '已入职',
        self::COMPANY_MARK_STATUS_NOT_ENTRY       => '未入职',
    ];

    // company_h5页面按钮权限
    const COMPANY_H5_HANDLE_BUTTON_LIST = [
        // 已投递（待处理）
        self::STATUS_HANDLE_WAIT     => [
            self::COMPANY_MARK_STATUS_ORIGINAL        => [
                [
                    'button'            => 'inappropriate',
                    'Chinese'           => '不合适',
                    'status'            => self::STATUS_INAPPROPRIATE,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE,
                    'tips'              => '确定要将该求职者处理为“不合适”吗',
                    'toast'             => '已将该求职者移动至“不合适”阶段',
                ],
                [
                    'button'            => 'throughFirst',
                    'Chinese'           => '通过初筛',
                    'status'            => self::STATUS_THROUGH_FIRST,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_THROUGH_FIRST,
                    'tips'              => '确定要将该求职者处理为“通过初筛”吗',
                    'toast'             => '已将该求职者移动至“通过初筛”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_NOT_CONNECT     => [
                [
                    'button'            => 'inappropriate',
                    'Chinese'           => '不合适',
                    'status'            => self::STATUS_INAPPROPRIATE,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE,
                    'tips'              => '确定要将该求职者处理为“不合适”吗',
                    'toast'             => '已将该求职者移动至“不合适”阶段',
                ],
                [
                    'button'            => 'throughFirst',
                    'Chinese'           => '通过初筛',
                    'status'            => self::STATUS_THROUGH_FIRST,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_THROUGH_FIRST,
                    'tips'              => '确定要将该求职者处理为“通过初筛”吗',
                    'toast'             => '已将该求职者移动至“通过初筛”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_NOT_INTENTION   => [
                [
                    'button'            => 'inappropriate',
                    'Chinese'           => '不合适',
                    'status'            => self::STATUS_INAPPROPRIATE,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE,
                    'tips'              => '确定要将该求职者处理为“不合适”吗',
                    'toast'             => '已将该求职者移动至“不合适”阶段',
                ],
                [
                    'button'            => 'throughFirst',
                    'Chinese'           => '通过初筛',
                    'status'            => self::STATUS_THROUGH_FIRST,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_THROUGH_FIRST,
                    'tips'              => '确定要将该求职者处理为“通过初筛”吗',
                    'toast'             => '已将该求职者移动至“通过初筛”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_NOT_FACE        => [
                [
                    'button'            => 'inappropriate',
                    'Chinese'           => '不合适',
                    'status'            => self::STATUS_INAPPROPRIATE,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE,
                    'tips'              => '确定要将该求职者处理为“不合适”吗',
                    'toast'             => '已将该求职者移动至“不合适”阶段',
                ],
                [
                    'button'            => 'throughFirst',
                    'Chinese'           => '通过初筛',
                    'status'            => self::STATUS_THROUGH_FIRST,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_THROUGH_FIRST,
                    'tips'              => '确定要将该求职者处理为“通过初筛”吗',
                    'toast'             => '已将该求职者移动至“通过初筛”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_WAIT_EMPLOYMENT => [
                [
                    'button'            => 'inappropriate',
                    'Chinese'           => '不合适',
                    'status'            => self::STATUS_INAPPROPRIATE,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE,
                    'tips'              => '确定要将该求职者处理为“不合适”吗',
                    'toast'             => '已将该求职者移动至“不合适”阶段',
                ],
                [
                    'button'            => 'throughFirst',
                    'Chinese'           => '通过初筛',
                    'status'            => self::STATUS_THROUGH_FIRST,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_THROUGH_FIRST,
                    'tips'              => '确定要将该求职者处理为“通过初筛”吗',
                    'toast'             => '已将该求职者移动至“通过初筛”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_ENTRY           => [
                [
                    'button'            => 'inappropriate',
                    'Chinese'           => '不合适',
                    'status'            => self::STATUS_INAPPROPRIATE,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE,
                    'tips'              => '确定要将该求职者处理为“不合适”吗',
                    'toast'             => '已将该求职者移动至“不合适”阶段',
                ],
                [
                    'button'            => 'throughFirst',
                    'Chinese'           => '通过初筛',
                    'status'            => self::STATUS_THROUGH_FIRST,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_THROUGH_FIRST,
                    'tips'              => '确定要将该求职者处理为“通过初筛”吗',
                    'toast'             => '已将该求职者移动至“通过初筛”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_NOT_ENTRY       => [
                [
                    'button'            => 'inappropriate',
                    'Chinese'           => '不合适',
                    'status'            => self::STATUS_INAPPROPRIATE,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE,
                    'tips'              => '确定要将该求职者处理为“不合适”吗',
                    'toast'             => '已将该求职者移动至“不合适”阶段',
                ],
                [
                    'button'            => 'throughFirst',
                    'Chinese'           => '通过初筛',
                    'status'            => self::STATUS_THROUGH_FIRST,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_THROUGH_FIRST,
                    'tips'              => '确定要将该求职者处理为“通过初筛”吗',
                    'toast'             => '已将该求职者移动至“通过初筛”阶段',
                ],
            ],
        ],
        // 通过初筛
        self::STATUS_THROUGH_FIRST   => [
            self::COMPANY_MARK_STATUS_ORIGINAL        => [
                [
                    'button'            => 'inappropriate',
                    'Chinese'           => '不合适',
                    'status'            => self::STATUS_INAPPROPRIATE,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE,
                    'tips'              => '确定要将该求职者处理为“不合适”吗',
                    'toast'             => '已将该求职者移动至“不合适”阶段',
                ],
                [
                    'button'            => 'invitation',
                    'Chinese'           => '面试邀请',
                    'status'            => self::STATUS_SEND_INVITATION,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_SEND_INVITATION,
                    'toast'             => '邀请发送成功！已将该求职者移动至“已邀面”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_NOT_CONNECT     => [
                [
                    'button'            => 'inappropriate',
                    'Chinese'           => '不合适',
                    'status'            => self::STATUS_INAPPROPRIATE,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE,
                    'tips'              => '确定要将该求职者处理为“不合适”吗',
                    'toast'             => '已将该求职者移动至“不合适”阶段',
                ],
                [
                    'button'            => 'invitation',
                    'Chinese'           => '面试邀请',
                    'status'            => self::STATUS_SEND_INVITATION,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_SEND_INVITATION,
                    'toast'             => '邀请发送成功！已将该求职者移动至“已邀面”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_NOT_INTENTION   => [
                [
                    'button'            => 'inappropriate',
                    'Chinese'           => '不合适',
                    'status'            => self::STATUS_INAPPROPRIATE,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE,
                    'tips'              => '确定要将该求职者处理为“不合适”吗',
                    'toast'             => '已将该求职者移动至“不合适”阶段',
                ],
                [
                    'button'            => 'invitation',
                    'Chinese'           => '面试邀请',
                    'status'            => self::STATUS_SEND_INVITATION,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_SEND_INVITATION,
                    'toast'             => '邀请发送成功！已将该求职者移动至“已邀面”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_NOT_FACE        => [
                [
                    'button'            => 'inappropriate',
                    'Chinese'           => '不合适',
                    'status'            => self::STATUS_INAPPROPRIATE,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE,
                    'tips'              => '确定要将该求职者处理为“不合适”吗',
                    'toast'             => '已将该求职者移动至“不合适”阶段',
                ],
                [
                    'button'            => 'invitation',
                    'Chinese'           => '面试邀请',
                    'status'            => self::STATUS_SEND_INVITATION,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_SEND_INVITATION,
                    'toast'             => '邀请发送成功！已将该求职者移动至“已邀面”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_WAIT_EMPLOYMENT => [
                [
                    'button'            => 'inappropriate',
                    'Chinese'           => '不合适',
                    'status'            => self::STATUS_INAPPROPRIATE,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE,
                    'tips'              => '确定要将该求职者处理为“不合适”吗',
                    'toast'             => '已将该求职者移动至“不合适”阶段',
                ],
                [
                    'button'            => 'invitation',
                    'Chinese'           => '面试邀请',
                    'status'            => self::STATUS_SEND_INVITATION,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_SEND_INVITATION,
                    'toast'             => '邀请发送成功！已将该求职者移动至“已邀面”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_ENTRY           => [
                [
                    'button'            => 'inappropriate',
                    'Chinese'           => '不合适',
                    'status'            => self::STATUS_INAPPROPRIATE,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE,
                    'tips'              => '确定要将该求职者处理为“不合适”吗',
                    'toast'             => '已将该求职者移动至“不合适”阶段',
                ],
                [
                    'button'            => 'invitation',
                    'Chinese'           => '面试邀请',
                    'status'            => self::STATUS_SEND_INVITATION,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_SEND_INVITATION,
                    'toast'             => '邀请发送成功！已将该求职者移动至“已邀面”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_NOT_ENTRY       => [
                [
                    'button'            => 'inappropriate',
                    'Chinese'           => '不合适',
                    'status'            => self::STATUS_INAPPROPRIATE,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE,
                    'tips'              => '确定要将该求职者处理为“不合适”吗',
                    'toast'             => '已将该求职者移动至“不合适”阶段',
                ],
                [
                    'button'            => 'invitation',
                    'Chinese'           => '面试邀请',
                    'status'            => self::STATUS_SEND_INVITATION,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_SEND_INVITATION,
                    'toast'             => '邀请发送成功！已将该求职者移动至“已邀面”阶段',
                ],
            ],
        ],
        // 邀请面试（已面试）
        self::STATUS_SEND_INVITATION => [
            self::COMPANY_MARK_STATUS_ORIGINAL        => [
                [
                    'button'            => 'inappropriate',
                    'Chinese'           => '不合适',
                    'status'            => self::STATUS_INAPPROPRIATE,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE,
                    'tips'              => '确定要将该求职者处理为“不合适”吗',
                    'toast'             => '已将该求职者移动至“不合适”阶段',
                ],
                [
                    'button'            => 'invitationAgain',
                    'Chinese'           => '再次邀请',
                    'status'            => self::STATUS_SEND_INVITATION,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_SEND_INVITATION,
                    'toast'             => '邀请发送成功！已将该求职者移动至“已邀面”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_NOT_CONNECT     => [
                [
                    'button'            => 'inappropriate',
                    'Chinese'           => '不合适',
                    'status'            => self::STATUS_INAPPROPRIATE,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE,
                    'tips'              => '确定要将该求职者处理为“不合适”吗',
                    'toast'             => '已将该求职者移动至“不合适”阶段',
                ],
                [
                    'button'            => 'invitationAgain',
                    'Chinese'           => '再次邀请',
                    'status'            => self::STATUS_SEND_INVITATION,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_SEND_INVITATION,
                    'toast'             => '邀请发送成功！已将该求职者移动至“已邀面”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_NOT_INTENTION   => [
                [
                    'button'            => 'inappropriate',
                    'Chinese'           => '不合适',
                    'status'            => self::STATUS_INAPPROPRIATE,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE,
                    'tips'              => '确定要将该求职者处理为“不合适”吗',
                    'toast'             => '已将该求职者移动至“不合适”阶段',
                ],
                [
                    'button'            => 'invitationAgain',
                    'Chinese'           => '再次邀请',
                    'status'            => self::STATUS_SEND_INVITATION,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_SEND_INVITATION,
                    'toast'             => '邀请发送成功！已将该求职者移动至“已邀面”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_NOT_FACE        => [
                [
                    'button'            => 'inappropriate',
                    'Chinese'           => '不合适',
                    'status'            => self::STATUS_INAPPROPRIATE,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE,
                    'tips'              => '确定要将该求职者处理为“不合适”吗',
                    'toast'             => '已将该求职者移动至“不合适”阶段',
                ],
                [
                    'button'            => 'invitationAgain',
                    'Chinese'           => '再次邀请',
                    'status'            => self::STATUS_SEND_INVITATION,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_SEND_INVITATION,
                    'toast'             => '邀请发送成功！已将该求职者移动至“已邀面”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_WAIT_EMPLOYMENT => [
                [
                    'button'            => 'employment',
                    'Chinese'           => '已录用',
                    'status'            => self::STATUS_EMPLOYED,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_EMPLOYED,
                ],
                [
                    'button'            => 'statusNotEntry',
                    'Chinese'           => '未入职',
                    'status'            => self::STATUS_SEND_INVITATION,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_NOT_ENTRY,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_ENTRY_NO,
                ],
            ],
            self::COMPANY_MARK_STATUS_ENTRY           => [
                [
                    'button'            => 'entryRevocation',
                    'Chinese'           => '撤销已入职',
                    'status'            => self::STATUS_HANDLE_WAIT,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_ENTRY_REVOCATION,
                    'tips'              => '是否撤销求职者已入职',
                    'toast'             => '已将该求职者移动至“待处理”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_NOT_ENTRY       => [
                [
                    'button'            => 'entryNoRevocation',
                    'Chinese'           => '撤销未入职',
                    'status'            => self::STATUS_HANDLE_WAIT,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_ENTRY_NO_REVOCATION,
                    'tips'              => '是否撤销该求职者未入职',
                    'toast'             => '已将该求职者移动至“待处理”阶段',
                ],
            ],
        ],
        //不合适
        self::STATUS_INAPPROPRIATE   => [
            self::COMPANY_MARK_STATUS_ORIGINAL        => [
                [
                    'button'            => 'inappropriateRevocation',
                    'Chinese'           => '撤销不合适',
                    'status'            => self::STATUS_HANDLE_WAIT,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE_REVOCATION,
                    'tips'              => '是否撤销求职者不合适',
                    'toast'             => '已将该求职者移动至“待处理”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_NOT_CONNECT     => [
                [
                    'button'            => 'inappropriateRevocation',
                    'Chinese'           => '撤销不合适',
                    'status'            => self::STATUS_HANDLE_WAIT,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE_REVOCATION,
                    'tips'              => '是否撤销求职者不合适',
                    'toast'             => '已将该求职者移动至“待处理”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_NOT_INTENTION   => [
                [
                    'button'            => 'inappropriateRevocation',
                    'Chinese'           => '撤销不合适',
                    'status'            => self::STATUS_HANDLE_WAIT,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE_REVOCATION,
                    'tips'              => '是否撤销求职者不合适',
                    'toast'             => '已将该求职者移动至“待处理”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_NOT_FACE        => [
                [
                    'button'            => 'inappropriateRevocation',
                    'Chinese'           => '撤销不合适',
                    'status'            => self::STATUS_HANDLE_WAIT,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE_REVOCATION,
                    'tips'              => '是否撤销求职者不合适',
                    'toast'             => '已将该求职者移动至“待处理”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_WAIT_EMPLOYMENT => [
                [
                    'button'            => 'inappropriateRevocation',
                    'Chinese'           => '撤销不合适',
                    'status'            => self::STATUS_HANDLE_WAIT,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE_REVOCATION,
                    'tips'              => '是否撤销求职者不合适',
                    'toast'             => '已将该求职者移动至“待处理”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_ENTRY           => [
                [
                    'button'            => 'inappropriateRevocation',
                    'Chinese'           => '撤销不合适',
                    'status'            => self::STATUS_HANDLE_WAIT,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE_REVOCATION,
                    'tips'              => '是否撤销求职者不合适',
                    'toast'             => '已将该求职者移动至“待处理”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_NOT_ENTRY       => [
                [
                    'button'            => 'inappropriateRevocation',
                    'Chinese'           => '撤销不合适',
                    'status'            => self::STATUS_HANDLE_WAIT,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_INAPPROPRIATE_REVOCATION,
                    'tips'              => '是否撤销求职者不合适',
                    'toast'             => '已将该求职者移动至“待处理”阶段',
                ],
            ],
        ],
        //已录用
        self::STATUS_EMPLOYED        => [
            self::COMPANY_MARK_STATUS_ORIGINAL        => [
                [
                    'button'            => 'entryRevocation',
                    'Chinese'           => '撤销已入职',
                    'status'            => self::STATUS_HANDLE_WAIT,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_ENTRY_REVOCATION,
                    'tips'              => '是否撤销求职者已入职',
                    'toast'             => '已将该求职者移动至“待处理”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_NOT_CONNECT     => [
                [
                    'button'            => 'entryRevocation',
                    'Chinese'           => '撤销已入职',
                    'status'            => self::STATUS_HANDLE_WAIT,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_ENTRY_REVOCATION,
                    'tips'              => '是否撤销求职者已入职',
                    'toast'             => '已将该求职者移动至“待处理”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_NOT_INTENTION   => [
                [
                    'button'            => 'entryRevocation',
                    'Chinese'           => '撤销已入职',
                    'status'            => self::STATUS_HANDLE_WAIT,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_ENTRY_REVOCATION,
                    'tips'              => '是否撤销求职者已入职',
                    'toast'             => '已将该求职者移动至“待处理”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_NOT_FACE        => [
                [
                    'button'            => 'entryRevocation',
                    'Chinese'           => '撤销已入职',
                    'status'            => self::STATUS_HANDLE_WAIT,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_ENTRY_REVOCATION,
                    'tips'              => '是否撤销求职者已入职',
                    'toast'             => '已将该求职者移动至“待处理”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_WAIT_EMPLOYMENT => [
                [
                    'button'            => 'entryRevocation',
                    'Chinese'           => '撤销已入职',
                    'status'            => self::STATUS_HANDLE_WAIT,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_ENTRY_REVOCATION,
                    'tips'              => '是否撤销求职者已入职',
                    'toast'             => '已将该求职者移动至“待处理”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_ENTRY           => [
                [
                    'button'            => 'entryRevocation',
                    'Chinese'           => '撤销已入职',
                    'status'            => self::STATUS_HANDLE_WAIT,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_ENTRY_REVOCATION,
                    'tips'              => '是否撤销求职者已入职',
                    'toast'             => '已将该求职者移动至“待处理”阶段',
                ],
            ],
            self::COMPANY_MARK_STATUS_NOT_ENTRY       => [
                [
                    'button'            => 'entryRevocation',
                    'Chinese'           => '撤销已入职',
                    'status'            => self::STATUS_HANDLE_WAIT,
                    'companyMarkStatus' => self::COMPANY_MARK_STATUS_ORIGINAL,
                    'handleType'        => BaseJobApplyHandleLog::TYPE_ENTRY_REVOCATION,
                    'tips'              => '是否撤销求职者已入职',
                    'toast'             => '已将该求职者移动至“待处理”阶段',
                ],
            ],
        ],
    ];

    // 已经邀请过面试的两种状态
    const IS_INVITATION_YES = 1;
    const IS_INVITATION_NO  = 0;

    //个人中心列表限制条数
    const PERSONAL_SHOW_LIST = 5;

    //限制上传的应聘材料数量
    const STUFF_FILE_LIMIT_NUM = 5;

    //存在记录，即是投递了
    const IS_APPLY_YES = 1;
    const IS_APPLY_NO  = 2;

    // 1代表已经被查看了提醒,2代表未被查看
    const IS_RESUME_OPERATION_REMIND_CHECK_YES = 1;
    const IS_RESUME_OPERATION_REMIND_CHECK_NO  = 2;

    // 1代表已经被查看了提醒,2代表未被查看
    const IS_RESUME_CHECK_REMIND_CHECK_YES = 1;
    const IS_RESUME_CHECK_REMIND_CHECK_NO  = 2;

    /**
     * 简历可操作状态数组(已废弃)
     */
    // const STATUS_OPERATION = [
    //     // self::STATUS_HANDLE_WAIT              => [
    //     //     self::STATUS_THROUGH_FIRST,
    //     //     self::STATUS_SEND_INVITATION,
    //     //     self::STATUS_INAPPROPRIATE,
    //     //     self::STATUS_EMPLOYED,
    //     // ],
    //     // self::STATUS_THROUGH_FIRST            => [
    //     //     self::STATUS_SEND_INVITATION,
    //     //     self::STATUS_INAPPROPRIATE,
    //     //     self::STATUS_EMPLOYED,
    //     //     self::STATUS_BLOCK_CALL,
    //     //     self::STATUS_INTENTION_NO,
    //     // ],
    //     // self::STATUS_SEND_INVITATION          => [
    //     //     self::STATUS_SEND_INVITATION,
    //     //     self::STATUS_INAPPROPRIATE,
    //     //     self::STATUS_EMPLOYED,
    //     //     self::STATUS_INTERVIEW_NO,
    //     //     self::STATUS_EMPLOYED_WAIT,
    //     // ],
    //     // self::STATUS_INAPPROPRIATE            => [
    //     //     self::STATUS_INAPPROPRIATE_REVOCATION,
    //     // ],
    //     // self::STATUS_INAPPROPRIATE_REVOCATION => [
    //     //     self::STATUS_THROUGH_FIRST,
    //     //     self::STATUS_SEND_INVITATION,
    //     //     self::STATUS_INAPPROPRIATE,
    //     //     self::STATUS_EMPLOYED,
    //     // ],
    //     // self::STATUS_ENTRY                    => [
    //     //     self::STATUS_ENTRY_REVOCATION,
    //     //     self::STATUS_HANDLE_WAIT,
    //     // ],
    //     // self::STATUS_ENTRY_NO                 => [
    //     //     self::STATUS_ENTRY_NO_REVOCATION,
    //     // ],
    //     // self::STATUS_ENTRY_REVOCATION         => [
    //     //     self::STATUS_THROUGH_FIRST,
    //     //     self::STATUS_SEND_INVITATION,
    //     //     self::STATUS_INAPPROPRIATE,
    //     //     self::STATUS_EMPLOYED,
    //     // ],
    //     // self::STATUS_ENTRY_NO_REVOCATION      => [
    //     //     self::STATUS_THROUGH_FIRST,
    //     //     self::STATUS_SEND_INVITATION,
    //     //     self::STATUS_INAPPROPRIATE,
    //     //     self::STATUS_EMPLOYED,
    //     // ],
    //     // self::STATUS_EMPLOYED                 => [
    //     //     self::STATUS_EMPLOYED_REVOCATION,
    //     //     self::STATUS_ENTRY_NO,
    //     //     self::STATUS_ENTRY,
    //     //     self::STATUS_HANDLE_WAIT,
    //     // ],
    //     // self::STATUS_EMPLOYED_WAIT            => [
    //     //     self::STATUS_EMPLOYED,
    //     //     self::STATUS_ENTRY,
    //     //     self::STATUS_ENTRY_NO,
    //     // ],
    //     // self::STATUS_EMPLOYED_REVOCATION      => [
    //     //     self::STATUS_THROUGH_FIRST,
    //     //     self::STATUS_SEND_INVITATION,
    //     //     self::STATUS_INAPPROPRIATE,
    //     //     self::STATUS_EMPLOYED,
    //     // ],
    //     // self::STATUS_BLOCK_CALL               => [
    //     //     self::STATUS_SEND_INVITATION,
    //     //     self::STATUS_INAPPROPRIATE,
    //     //     self::STATUS_EMPLOYED,
    //     //     self::STATUS_INTENTION_NO,
    //     // ],
    //     // self::STATUS_INTENTION_NO             => [
    //     //     self::STATUS_SEND_INVITATION,
    //     //     self::STATUS_INAPPROPRIATE,
    //     //     self::STATUS_EMPLOYED,
    //     // ],
    //     // self::STATUS_INTERVIEW_NO             => [
    //     //     self::STATUS_INAPPROPRIATE,
    //     //     self::STATUS_EMPLOYED,
    //     //     self::STATUS_SEND_INVITATION,
    //     // ],
    //
    // ];

    /**
     * 获取多条职位信息
     * @param array $where
     * @param array $select
     * @param array $andWhere
     * @return array
     */
    public static function selectInfos(array $where = [], array $select = [], array $andWhere = []): array
    {
        return self::find()
            ->where($where)
            ->andWhere($andWhere)
            ->select($select)
            ->asArray()
            ->all();
    }

    /**
     * 获取单条职位信息
     * @param array $where
     * @param array $select
     * @param array $andWhere
     * @return array|ActiveRecord|null
     */
    public static function selectInfo(array $where = [], array $select = [], array $andWhere = [])
    {
        return self::find()
            ->where($where)
            ->andWhere($andWhere)
            ->select($select)
            ->asArray()
            ->one();
    }

    /**
     * 关联多表返回列表
     * @param        $jobWhere
     * @param        $select
     * @param string $orderBy
     * @param string $limit
     * @param string $offset
     * @return array
     */
    public static function joinResumeAndResumeEducationList(
        $jobWhere,
        $select,
        string $orderBy = '',
        string $limit = '',
        string $offset = ''
    ): array {
        return self::find()
            ->alias('apply')
            ->select('apply.id,apply.job_id,apply.resume_id')
            ->leftJoin('member as m', 'apply.resume_member_id = m.id')
            ->leftJoin('resume as r', 'r.id = apply.resume_id')
            ->leftJoin('resume_education as education',
                'education.resume_id = r.id and education.id = (select id from resume_education where resume_id = r.id order by begin_date desc limit 1)')
            ->leftJoin('resume_intention as intention',
                'intention.resume_id = r.id and intention.id = (select id from resume_intention where resume_id = r.id order by id desc limit 1)')
            ->leftJoin('job as j', 'j.id = apply.job_id')
            ->leftJoin('resume_work as work',
                'work.resume_id = r.id and work.id = (select id from resume_work where resume_id = r.id order by begin_date desc limit 1)')
            ->where($jobWhere)
            ->limit($limit)
            ->offset($offset)
            ->select($select)
            ->orderBy($orderBy)
            ->asArray()
            ->all();
    }

    /**
     * 判断是否有记录
     * 投递后30天内或已达该职位投递次数上限（近180天投递3次），按钮置灰不可点击；
     * @param $memberId
     * @param $jobId
     * @return bool|int|string|null
     */
    public static function checkJobApplyStatus($memberId, $jobId)
    {
        $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id');
        return BaseJobApplyRecord::checkJobApplyStatus($resumeId, $jobId);
        //
        // //获取限制天数
        // $limitTime = date('Y-m-d', strtotime('-' . self::LIMIT_APPLY_DAYS_ONE . 'day'));
        //
        // //判断当前职位是站内职位还是站外职位
        // $cooperationStatus = BaseJob::checkCooperationStatus($jobId);
        //
        // if ($cooperationStatus == BaseCompany::COOPERATIVE_UNIT_YES) {
        //     //合作单位，查站内
        //     $count1 = self::find()
        //         ->where([
        //             'resume_member_id' => $memberId,
        //             'job_id'           => $jobId,
        //         ])
        //         ->andWhere([
        //             '>',
        //             'add_time',
        //             $limitTime,
        //         ])
        //         ->select('id')
        //         ->count();
        //
        //     $count2 = self::find()
        //         ->where([
        //             'resume_member_id' => $memberId,
        //             'job_id'           => $jobId,
        //         ])
        //         ->andWhere([
        //             '>',
        //             'add_time',
        //             date('Y-m-d', strtotime('-' . self::LIMIT_APPLY_DAYS_TWO . 'day')),
        //         ])
        //         ->select('id')
        //         ->count();
        //
        //     $count = $count1 + $count2;
        // } else {
        //     if ($cooperationStatus == BaseCompany::COOPERATIVE_UNIT_NO) {
        //         //非合作单位，查站外
        //         $count = BaseOffSiteJobApply::find()
        //             ->where([
        //                 'member_id' => $memberId,
        //                 'job_id'    => $jobId,
        //             ])
        //             ->andWhere([
        //                 '>',
        //                 'add_time',
        //                 $limitTime,
        //             ])
        //             ->select('id')
        //             ->count();
        //     }
        // }
        //
        // if (!empty($count)) {
        //     return BaseJob::JOB_APPLY_STATUS_YES;
        // } else {
        //     return BaseJob::JOB_APPLY_STATUS_NO;
        // }
    }

    /**
     * 获取用户站内投递次数
     * @param $memberId
     * @return bool|int|string|null
     */
    public static function getMemberApplyAmount($memberId)
    {
        return intval(self::find()
            ->where(['resume_member_id' => $memberId])
            ->count());
    }

    /**
     * 获取附件简历的投递次数
     */
    public static function getResumeAttachmentApplyAmount($resumeAttachmentId)
    {
        return self::find()
            ->where(['resume_attachment_id' => $resumeAttachmentId])
            ->count();
    }

    /**
     * 获取用户已邀面次数
     * @param $where
     * @return bool|int|string|null
     */
    public static function getInterviewAmount($where)
    {
        return intval(self::find()
            ->where($where)
            ->andWhere(['is_invitation' => self::IS_INVITATION_YES])
            ->asArray()
            ->count());
    }

    /**
     * 获取已邀请面试过的简历申请Id列表
     * @return array
     */
    public static function getInterviewJobApplyIdList(): array
    {
        return self::find()
            ->alias('apply')
            ->leftJoin('job_apply_handle_log as log', 'apply.id = log.job_apply_id')
            ->where(['log.handle_type' => BaseJobApplyHandleLog::TYPE_SEND_INVITATION])
            ->select('apply.id')
            ->asArray()
            ->column();
    }

    /**
     * 根据状态
     * @param $where
     * @param $select
     * @param $orWhere
     * @return bool|int|string|null
     */
    public static function getAmount($where, $select, $orWhere)
    {
        return self::find()
            ->where($where)
            ->orWhere($orWhere)
            ->select($select)
            ->asArray()
            ->count();
    }

    /**
     * 获取总条数
     * @param array $where
     * @param array $andWhere
     * @return bool|int|string|null
     */
    public static function countInfos(
        array $where = [],
        array $andWhere = []
    ) {
        return self::find()
            ->where($where)
            ->andWhere($andWhere)
            ->asArray()
            ->count();
    }

    /**
     * 判断UID编号与名称搜索
     * @param $params
     * @param $fieldUid
     * @param $fieldName
     * @param $query
     * @throws \yii\base\Exception
     */
    public static function uidJudgeWhere($params, $fieldUid, $fieldName, $query)
    {
        if (strlen($params) == 8 && UUIDHelper::decryption($params)) {
            $fullNameNum = UUIDHelper::decryption($params);
            if ($fullNameNum) {
                return $query->andFilterWhere([
                    '=',
                    $fieldUid,
                    $fullNameNum,

                ]);
            }
        } else {
            return $query->andFilterWhere([
                'like',
                $fieldName,
                $params,

            ]);
        }
    }

    /**
     * 计算单位简历查看率(这个已经放在一个字段里面去了,定期更新,所以不需要再计算),这个方法未来会弃用
     * company_stat_data中的resume_view_rate
     * @param $where
     */
    public static function statCompanyViewingRate($where)
    {
        //获取单位简历查看率
        $isCheckAccount = 0;
        $jobApplyQuery  = BaseJobApply::find()
            ->select([
                'status',
                'is_check',
                'add_time',
                'id',
                'job_id',
            ])
            ->where($where);
        $allAccount     = $jobApplyQuery->count();

        $jobApplyList = $jobApplyQuery->asArray()
            ->all();
        if ($allAccount > 0) {
            foreach ($jobApplyList as $list) {
                if ($list['is_check'] == 1) {
                    $isCheckAccount++;
                }
            }

            $viewingRate = round($isCheckAccount / $allAccount * 100, 2) . '%';
        } else {
            $viewingRate = '0%';
        }

        return $viewingRate;
    }

    /**
     * 单位简历处理用时
     * 这里查询最近一次简历处理
     * @param $memberId
     * @return int|string
     */
    public static function jobApplyHandleTime($memberId)
    {
        $handleTime   = "0天";
        $jobApplyLast = BaseJobApply::find()
            ->select([
                'add_time',
                'job_id',
                'id',
            ])
            ->where(['company_member_id' => $memberId])
            ->orderBy('add_time desc')
            ->asArray()
            ->one();

        if ($jobApplyLast) {
            $addTime = BaseJobApplyHandleLog::findOneVal([
                'job_apply_id' => $jobApplyLast['id'],
                'handler_type' => BaseJobApplyHandleLog::HANDLER_TYPE_COMPANY,
            ], 'add_time');

            if (strlen($addTime) > 0) {
                //$handleTime = TimeHelper::reduceDateTime($addTime, $jobApplyLast['add_time']);
                $time       = strtotime($addTime) - strtotime($jobApplyLast['add_time']);
                $handleTime = ceil($time / 86400) . '天';
            }
        }

        return $handleTime;
    }

    /**
     * 获取站内投递列表
     * @param       $memberId
     * @param array $searchData
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getApplyList($searchData, $needPageInfo = false)
    {
        $query = self::find()
            ->alias('ja')
            ->leftJoin(['j' => BaseJob::tableName()], 'j.id = ja.job_id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = ja.company_id')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id = j.announcement_id')
            ->where(['ja.resume_member_id' => $searchData['memberId']]);

        if ($searchData['status'] == self::STATUS_IS_CHECK) {
            //如果是已查看的状态，查询状态等于已投递，且状态等于已查看的数据
            // $query->andWhere(['ja.status' => self::STATUS_HANDLE_WAIT]);
            $query->andWhere(['ja.is_check' => self::IS_CHECK_YES]);
        } elseif ($searchData['status'] == self::STATUS_HANDLE_WAIT) {
            //查询已投递的，去除已查看的数据
            $query->andWhere(['ja.status' => self::STATUS_HANDLE_WAIT]);
            $query->andWhere(['ja.is_check' => self::IS_CHECK_NO]);
        } else {
            $query->andFilterWhere(['ja.status' => $searchData['status']]);
        }

        $count = $query->count();

        $query->select([
            'ja.id',
            'ja.source',
            'j.name as jobName',
            'j.id as jobId',
            'j.min_wage',
            'j.max_wage',
            'j.wage_type',
            'c.full_name as companyName',
            'c.id as companyId',
            'j.province_id',
            'j.status as jobStatus',
            'j.city_id',
            'a.title as announcementName',
            'a.id as announcementId',
            'j.release_time',
            'ja.add_time as applyDate',
            'ja.status',
            'ja.is_check',
            'ja.note',
            'j.education_type as educationType',
            'j.amount',
            'j.experience_type as experienceType',
            'c.nature as companyNature',
            'c.type as companyType',
            'is_resume_operation_remind_check',
            'is_resume_check_remind_check',
            // is_resume_operation_remind_check + is_resume_check_remind_check
            'sort' => '`is_resume_operation_remind_check` + `is_resume_check_remind_check`',
        ]);
        $pageSize = $searchData['pageSize'] ?: \Yii::$app->params['defaultPageSize'];

        $pages = self::setPage($count, $searchData['page'], $pageSize);

        // 这里的设计存在一定问题,所以用了伪造排序的方法来修正,如果是全部状态就先按照sort排序,如果是已查看,就按照is_resume_check_remind_check先倒序,已投递就直接时间倒序,其余就按照is_resume_check_remind_check先倒序

        switch ($searchData['status']) {
            case 1:
                // 已投递
                $query->orderBy('ja.add_time desc');
                break;
            case -1:
                // 已查看
                $query->orderBy('is_resume_check_remind_check desc,ja.add_time desc');
                break;
            default:
                if ($searchData['status']) {
                    $query->orderBy('is_resume_operation_remind_check desc,ja.add_time desc');
                } else {
                    // 这里需要增加一个字段帮助排序
                    // if ($v['status'] == self::STATUS_HANDLE_WAIT && $v['is_check'] == self::IS_CHECK_NO) {
                    $query->addSelect([
                        'second_sort' => 'IF(ja.status = ' . self::STATUS_HANDLE_WAIT . ' AND ja.is_check = ' . self::IS_CHECK_NO . ',1,0)',
                    ]);
                    $query->orderBy('sort desc,second_sort,ja.add_time desc');
                }
                break;
        }

        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->asArray()
            ->all();
        foreach ($list as $k => &$v) {
            $v['publishDate']       = date('Y-m-d', strtotime($v['release_time']));
            $v['wageInfo']          = BaseJob::formatWage($v['min_wage'], $v['max_wage'], $v['wage_type']);
            $v['educationText']     = BaseDictionary::getEducationName($v['educationType']);     //获取学历要求名称
            $v['experienceText']    = BaseDictionary::getExperienceName($v['experienceType']);   //获取经验要求名称
            $v['companyNatureText'] = BaseDictionary::getCompanyNatureName($v['companyNature']); //获取单位性质
            $v['companyTypeText']   = BaseDictionary::getCompanyTypeName($v['companyType']);     //获取单位类型
            $v['applyDate']         = date('Y-m-d', strtotime($v['applyDate']));
            if ($v['status'] == self::STATUS_HANDLE_WAIT && $v['is_check'] == self::IS_CHECK_YES) {
                //当前状态为已查看
                $v['applyStatusTxt'] = self::PERSON_STATUS_LIST[self::STATUS_IS_CHECK];       //状态名称
            } else {
                $v['applyStatusTxt'] = self::PERSON_STATUS_LIST[$v['status']];                    //状态名称
            }
            //获取投递来源
            $v['sourceText'] = self::SOURCE_LIST[$v['source']];

            $areaName      = BaseArea::getOneProvinceCityName($v['city_id']);
            $v['areaName'] = StringHelper::subtractString($areaName, '-');

            $v['url']        = Url::toRoute([
                'job/detail',
                'id' => $v['jobId'],
            ]);
            $v['companyUrl'] = Url::toRoute([
                'company/detail',
                'id' => $v['companyId'],
            ]);
            if (!empty($v['announcementId'])) {
                $v['announcementUrl'] = Url::toRoute([
                    'announcement/detail',
                    'id' => $v['announcementId'],
                ]);
            }

            // 获取简历操作提醒
            if ($searchData['status'] == '-1') {
                $v['isCheckRemind'] = $v['is_resume_check_remind_check'];
            } else {
                // 这里有一个特殊的情况,如果前端是查看已投递的列表,不应该有小红点
                if ($searchData['status'] != 1) {
                    if (!$searchData['status']) {
                        $v['isCheckRemind'] = $v['is_resume_check_remind_check'] == 2 || $v['is_resume_operation_remind_check'] == 2 ? '2' : '1';
                    } else {
                        $v['isCheckRemind'] = $v['is_resume_operation_remind_check'];
                    }
                }
            }

            // // 这里还有一个小红点策略,如果sort=4,就肯定有小红点
            // if ($v['sort'] == 4) {
            //     $v['isCheckRemind'] = '2';
            // }

            // 这里还有一个保底的操作,如果是已投递状态但是未查看,也是没有小红点的
            if ($v['status'] == self::STATUS_HANDLE_WAIT && $v['is_check'] == self::IS_CHECK_NO) {
                $v['isCheckRemind'] = '1';
            }

            if ($v['status'] == self::STATUS_SEND_INVITATION) {
                //邀请面试状态，获取面试信息
                $v['interviewInfo'] = BaseCompanyInterview::find()
                    ->where(['job_apply_id' => $v['id']])
                    ->select([
                        'job_name',
                        'contact',
                        'telephone',
                        'interview_time',
                        'content',
                        'address',
                    ])
                    ->asArray()
                    ->one();
            }

            /**
             * 已下线的职位，显示“停止招聘”，卡片置灰；
             *
             * 已删除的职位，显示“已删除”，卡片置灰；
             */
            if ($v['jobStatus'] == BaseJob::STATUS_OFFLINE) {
                $v['jobNotice'] = '停止招聘';
            }

            if ($v['jobStatus'] == BaseJob::STATUS_DELETE) {
                $v['jobNotice'] = '已删除';
            }
        }

        if ($needPageInfo) {
            return [
                'list' => $list,
                'page' => [
                    'count' => intval($count),
                    'limit' => intval($pages['limit']),
                    'page'  => intval($pages['page']),
                ],
            ];
        } else {
            return $list;
        }
    }

    /**
     * 累计总数、职位未读简历、待面试统计
     * 这里查询最近一次简历处理
     * @param $jobId
     * @return ActiveRecord[]|array
     */
    public static function jobApplyStatistics($jobId): array
    {
        $select = [
            'j.id',
            'j.status',
            'c.interview_time',
        ];

        $list = BaseJobApply::find()
            ->alias('j')
            ->leftJoin(['c' => BaseCompanyInterview::tableName()], 'c.job_apply_id=j.id')
            ->select($select)
            ->where([
                'j.job_id' => $jobId,
            ])
            ->asArray()
            ->all();

        $allJobApplyNum  = 0;
        $unreadApplyNum  = 0;
        $jobInterviewNum = 0;
        foreach ($list as $item) {
            $allJobApplyNum++;
            if ($item['status'] == 1) {
                $unreadApplyNum++;
            }

            if ($item['interview_time'] > CUR_DATETIME) {
                $jobInterviewNum++;
            }
        }

        return [
            'allJobApplyNum'  => $allJobApplyNum,
            'unreadApplyNum'  => $unreadApplyNum,
            'jobInterviewNum' => $jobInterviewNum,
        ];
    }

    /**
     * 公告下的职位累计总数、职位未读简历、待面试统计
     * 这里查询最近一次简历处理
     * @param $announcementId
     * @return ActiveRecord[]|array
     * @throws \yii\base\Exception
     */
    public static function announcementJobApplyStatistics($announcementId): array
    {
        $query         = BaseJob::find()
            ->alias('j')
            ->where(['j.announcement_id' => $announcementId])
            ->andWhere([
                'j.status' => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ]);
        $authorityList = (new CompanyAuthorityClassify())->run([
            'associatedField' => 'j.company_id',
            'memberId'        => Yii::$app->user->id,
            'query'           => $query,
            'returnType'      => BaseRule::DATA_JOB_COOPERATE,
        ]);

        if ($authorityList) {
            $query = $authorityList['query'];
        }

        $jobIds = $query->select('j.id')
            ->asArray()
            ->column();

        //站内-待处理简历
        $unreadApplyNum = BaseJobApply::find()
            ->where([
                'job_id' => $jobIds,
                'status' => self::STATUS_HANDLE_WAIT,
            ])
            ->count();;
        $jobInterviewNum = 0;
        //站内累计简历数量
        $allJobApplyNum = BaseJobApply::find()
            ->where(['job_id' => $jobIds])
            ->count();
        //站外网址投递简历数量---这里利用一下当前逻辑特殊性:合作单位的网址投递才会去到站外投递表
        $linkApplyNum = BaseOffSiteJobApply::find()
            ->where([
                'job_id' => $jobIds,
                'source' => BaseOffSiteJobApply::SOURCE_WEBSITE,
            ])
            ->count();
        // 职位里面是否含有平台投递或者邮件投递的职位
        $isPlatformOrEmailApply = BaseJob::find()
            ->alias('j')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'j.announcement_id=a.id')
            ->where([
                'j.id' => $jobIds,
            ])
            ->andWhere([
                'or',
                [
                    'j.delivery_way' => [
                        BaseJob::DELIVERY_WAY_PLATFORM,
                        BaseJob::DELIVERY_WAY_EMAIL,
                    ],
                ],
                [
                    'and',
                    ['j.delivery_type' => 0],
                    [
                        'a.delivery_way' => [
                            BaseAnnouncement::DELIVERY_WAY_PLATFORM,
                            BaseAnnouncement::DELIVERY_WAY_EMAIL,
                        ],
                    ],
                ],
            ])
            ->exists();
        // 职位里面是否含有网址投递职位
        $isLinkApply = BaseJob::find()
            ->alias('j')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'j.announcement_id=a.id')
            ->where([
                'j.id' => $jobIds,
            ])
            ->andWhere([
                'or',
                [
                    'j.delivery_way' => BaseJob::DELIVERY_WAY_LINK,
                ],
                [
                    'and',
                    ['j.delivery_type' => 0],
                    ['a.delivery_way' => BaseAnnouncement::DELIVERY_WAY_LINK],
                ],
            ])
            ->exists();

        //
        //        foreach ($list as $item) {
        //这里应该是bug 职位对应投递是一对多  所以重写一下
        //            $applyId         = BaseJobApply::findOneVal(['job_id' => $item['id']], 'id');
        //            $unreadApplyNum  += BaseCompanyInterview::find()
        //                ->where(['job_apply_id' => $applyId])
        //                ->andWhere([
        //                    '>',
        //                    'status',
        //                    1,
        //                ])
        //                ->count();
        //            $jobInterviewNum += BaseCompanyInterview::find()
        //                ->where(['job_apply_id' => $applyId])
        //                ->andWhere([
        //                    '>',
        //                    'interview_time',
        //                    CUR_DATETIME,
        //                ])
        //                ->count();
        //            $allJobApplyNum  += ((BaseJobApply::find()
        //                ->where(['job_id' => $item['id']])
        //                ->count()));//+(BaseOffSiteJobApply::find()->where(['job_id' => $item['id'],'source'=>BaseOffSiteJobApply::SOURCE_WEBSITE])->count())
        //        }

        return [
            'allJobApplyNum'         => $allJobApplyNum,
            'jobInterviewNum'        => $jobInterviewNum,
            'unreadApplyNum'         => $unreadApplyNum,
            'linkApplyNum'           => $linkApplyNum,
            'isPlatformOrEmailApply' => $isPlatformOrEmailApply,
            'isLinkApply'            => $isLinkApply,
        ];
    }

    /**
     * 判断是否存在投递记录
     * @param $resumeId
     * @param $companyId
     * @return int
     */
    public static function checkApplyRecord($resumeId, $companyId)
    {
        $hasApplyRecord = BaseJobApply::findOne([
            'resume_id'  => $resumeId,
            'company_id' => $companyId,
        ]);
        if ($hasApplyRecord) {
            return self::IS_APPLY_YES;
        } else {
            return self::IS_APPLY_NO;
        }
    }

    /**
     * 失效职位投递权益
     */
    public static function expireEquity($applyId)
    {
        $model = self::findOne($applyId);
        if ($model) {
            $model->equity_status = self::EQUITY_STATUS_EXPIRE;
            $model->save();
        }
    }
}