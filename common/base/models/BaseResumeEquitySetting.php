<?php

namespace common\base\models;

use common\models\ResumeEquitySetting;
use yii\db\Expression;

class BaseResumeEquitySetting extends ResumeEquitySetting
{
    // 待审核
    const STATUS_AUDIT = 0;
    // 已上线
    const STATUS_ONLINE = 1;
    // 已下线
    const STATUS_OFFLINE = -1;

    const STATUS_LIST = [
        self::STATUS_AUDIT   => '待审核',
        self::STATUS_ONLINE  => '已上线',
        self::STATUS_OFFLINE => '已下线',
    ];

    // 竞争力分析
    const ID_COMPETITION_ANALYSIS = 1;
    // 公告热度
    const ID_ANNOUNCEMENT_POPULARITY = 2;
    // 编制查询
    const ID_ESTABLISHMENT_QUERY = 3;
    // 简历模版
    const ID_RESUME_TEMPLATE = 4;
    // 浏览足迹
    const ID_FOOTPRINT = 5;
    // 收藏查看数量（职位收藏/公告收藏）
    const ID_COLLECT_VIEW = 6;
    // 求职资源
    const ID_JOB_RESOURCES = 7;
    // 专属标识(vip)
    const ID_VIP_LOGO = 8;
    // 简历刷新
    const ID_RESUME_REFRESH = 9;
    // 简历置顶
    const ID_RESUME_TOP = 10;
    // 投递置顶
    const ID_DELIVERY_TOP = 11;

    //特殊权益---高才优课
    const ID_GAOCAIYOUKE      = 8888;
    const ID_GAOCAIYOUKE_TEXT = '加赠“高才优课”求职课程包';
    const ID_GAOCAIYOUKE_ITEM = [
        'id'   => self::ID_GAOCAIYOUKE,
        'name' => self::ID_GAOCAIYOUKE_TEXT,
    ];

    /** 增值服务 */
    const ID_VALUE_ADDED_SERVICES = [
        self::ID_COMPETITION_ANALYSIS,
        self::ID_ANNOUNCEMENT_POPULARITY,
    ];

    /** 求职快服务 */
    const ID_JOB_FAST_SERVICES = [
        self::ID_RESUME_TOP,
        self::ID_DELIVERY_TOP,
        self::ID_RESUME_REFRESH,
    ];

    /** 黄金VIP */
    const ID_GOLD_VIP_SERVICES = [
        self::ID_COMPETITION_ANALYSIS,
        self::ID_ANNOUNCEMENT_POPULARITY,
        self::ID_ESTABLISHMENT_QUERY,
        self::ID_RESUME_TEMPLATE,
        self::ID_FOOTPRINT,
        self::ID_COLLECT_VIEW,
        self::ID_JOB_RESOURCES,
        self::ID_VIP_LOGO,
    ];

    /** 钻石VIP */
    const ID_DIAMOND_VIP_SERVICES = [
        self::ID_RESUME_TOP,
        self::ID_DELIVERY_TOP,
        self::ID_RESUME_REFRESH,
        self::ID_COMPETITION_ANALYSIS,
        self::ID_ANNOUNCEMENT_POPULARITY,
        self::ID_ESTABLISHMENT_QUERY,
        self::ID_RESUME_TEMPLATE,
        self::ID_FOOTPRINT,
        self::ID_COLLECT_VIEW,
        self::ID_JOB_RESOURCES,
        self::ID_VIP_LOGO,
    ];

    /** 需要配置资源权益  */
    const ID_NEED_CONFIG_RESOURCES = [
        self::ID_RESUME_TOP,
        self::ID_DELIVERY_TOP,
    ];
    /** 需要配置资源权益对应数量  */
    const ID_NEED_CONFIG_RESOURCES_NUM = [
        self::ID_RESUME_TOP   => [
            15 => 6,
            30 => 12,
            60 => 24,
            90 => 40,
        ],
        self::ID_DELIVERY_TOP => [
            15 => 7,
            30 => 16,
            60 => 34,
            90 => 52,
        ],
    ];

    // 一些套餐里面的单天价格
    const ID_DAY_PRICE = [
        self::ID_RESUME_REFRESH => 10,
        self::ID_RESUME_TOP     => 15,
        self::ID_DELIVERY_TOP   => 1,
    ];

    // 我的服务配置
    const MY_SERVICES_CONFIG = [
        self::ID_COMPETITION_ANALYSIS    => [
            'imgSrc'                => 'https://img.gaoxiaojob.com/uploads/static/image/resume/competition-analysis.png',
            'otherImgSrc'           => 'https://img.gaoxiaojob.com/uploads/static/image/resume/competition-analysis-virtual.png',
            'tips'                  => '正在使用',
            'tips_not'              => '多维度解析简历，知己知彼',
            'isCompetitionAnalysis' => 1,
            'jumpUrl'               => BaseResume::BUY_URL_COMPETITIVE_POWER,
        ],
        self::ID_ANNOUNCEMENT_POPULARITY => [
            'imgSrc'                   => 'https://img.gaoxiaojob.com/uploads/static/image/resume/announcement-popularity.png',
            'otherImgSrc'              => 'https://img.gaoxiaojob.com/uploads/static/image/resume/announcement-popularity-virtual-sky.png',
            'tips'                     => '正在使用',
            'tips_not'                 => '公告热度一眼洞悉',
            'isAnnouncementPopularity' => 1,
            'jumpUrl'                  => BaseResume::BUY_URL_COMPETITIVE_POWER,
        ],
        self::ID_ESTABLISHMENT_QUERY     => [
            'imgSrc' => 'https://img.gaoxiaojob.com/uploads/static/image/resume/establishment-query.png',
            'tips'   => '正在使用',
        ],
        self::ID_RESUME_TEMPLATE         => [
            'imgSrc' => 'https://img.gaoxiaojob.com/uploads/static/image/resume/resume-template.png',
            'tips'   => '正在使用',
        ],
        self::ID_FOOTPRINT               => [
            'imgSrc' => 'https://img.gaoxiaojob.com/uploads/static/image/resume/footprint.png',
            'tips'   => '正在使用',
        ],
        self::ID_COLLECT_VIEW            => [
            'imgSrc' => 'https://img.gaoxiaojob.com/uploads/static/image/resume/collect-view.png',
            'tips'   => '不限量',
        ],
        self::ID_JOB_RESOURCES           => [
            'imgSrc'         => 'https://img.gaoxiaojob.com/uploads/static/image/resume/job-resources.png',
            'tips'           => '扫码领取',
            'isJobResources' => 1,
        ],
        self::ID_VIP_LOGO                => [
            'imgSrc' => 'https://img.gaoxiaojob.com/uploads/static/image/resume/vip-logo.png',
            'tips'   => '正在使用',
        ],
        self::ID_RESUME_REFRESH          => [
            'imgSrc'      => 'https://img.gaoxiaojob.com/uploads/static/image/resume/refresh.png',
            'otherImgSrc' => 'https://img.gaoxiaojob.com/uploads/static/image/resume/refresh-virtual.png',
            'tips'        => '每天9:00自动刷新',
            'tips_not'    => '简历翻倍曝光',
            'jumpUrl'     => BaseResume::BUY_URL_JOB_FAST,
        ],
        self::ID_RESUME_TOP              => [
            'imgSrc'      => 'https://img.gaoxiaojob.com/uploads/static/image/resume/resume-top.png',
            'otherImgSrc' => 'https://img.gaoxiaojob.com/uploads/static/image/resume/resume-top-virtual.png',
            'tips'        => '置顶设置',
            'tips_not'    => '招聘方第一眼看到你',
            'jumpUrl'     => BaseResume::BUY_URL_JOB_FAST,
        ],
        self::ID_DELIVERY_TOP            => [
            'imgSrc'      => 'https://img.gaoxiaojob.com/uploads/static/image/resume/delivery-top.png',
            'otherImgSrc' => 'https://img.gaoxiaojob.com/uploads/static/image/resume/delivery-top-virtual.png',
            'tips'        => '正在使用',
            'tips_not'    => '投递的简历先被看到',
            'jumpUrl'     => BaseResume::BUY_URL_JOB_FAST,
        ],
    ];

    // 求职工具
    const JOB_TOOLS_CONFIG = [
        [
            'imgSrc'      => 'https://img.gaoxiaojob.com/uploads/static/image/resume/job-fast.png',
            'name'        => '求职快',
            'description' => '简历无限刷新+简历置顶+投递置顶，求职效率翻倍，找工作快人一步!',
            'jumpLink'    => BaseResume::BUY_URL_JOB_FAST,
        ],
        [
            'imgSrc'      => 'https://img.gaoxiaojob.com/uploads/static/image/resume/job-tools-insight.png',
            'name'        => '竞争力洞察',
            'description' => '职位竞争力分析+公告热度查询，知己知彼，求职更清晰!',
            'jumpLink'    => BaseResume::BUY_URL_COMPETITIVE_POWER,
        ],
        [
            'imgSrc'      => 'https://img.gaoxiaojob.com/uploads/static/image/resume/job-tools-resume-optimize.png',
            'name'        => '求职咨询',
            'description' => '服务专家根据求职意向，为你筛选匹配岗位，1v1规划投递流程。',
            'jumpLink'    => 'https://www.gaoxiaojob.com/zhaopin/zt/ycc_gaocaiyouke_v2/index.html',
        ],
        [
            'imgSrc'      => 'https://img.gaoxiaojob.com/uploads/static/image/resume/job-tools-resume-apply.png',
            'name'        => '简历代投',
            'description' => '专业顾问代投简历，平台官方账号背书，让你省心又省时。',
            'jumpLink'    => 'https://www.gaoxiaojob.com/zhaopin/zt/ycc_gaocaiyouke_v2/index.html',
        ],
    ];

    /**
     * 获取权益筛项列表
     */
    public static function getEquityFilterList()
    {
        return self::find()
            ->select([
                'id',
                'name',
            ])
            ->where([
                'status' => self::STATUS_ONLINE,
                'id'     => [
                    BaseResumeEquitySetting::ID_COMPETITION_ANALYSIS,
                    BaseResumeEquitySetting::ID_ANNOUNCEMENT_POPULARITY,
                    BaseResumeEquitySetting::ID_RESUME_TOP,
                    BaseResumeEquitySetting::ID_DELIVERY_TOP,
                ],
            ])
            ->asArray()
            ->all();
    }

    /**
     * 获取权益明细列表
     */
    public static function getEquityDetailList(array $equityIds)
    {
        //获取对应的权益数据
        $list = self::find()
            ->select([
                'id',
                'name',
            ])
            ->where([
                'status' => self::STATUS_ONLINE,
                'id'     => $equityIds,
            ])
            ->orderBy([new Expression('FIELD (id,' . implode(',', $equityIds) . ')')])
            ->asArray()
            ->all();

        return $list;
    }

    /**
     * 获取权益描述
     *
     */
    public static function getEquityDescription($equityId, $days)
    {
        switch ($equityId) {
            case self::ID_COMPETITION_ANALYSIS:
            case self::ID_ANNOUNCEMENT_POPULARITY:
                $description = $days . '天，10次/天';
                break;
            case self::ID_RESUME_REFRESH:
                $description = '无限次';
                break;
            case self::ID_RESUME_TOP:
                $description = self::ID_NEED_CONFIG_RESOURCES_NUM[$equityId][$days] . '天';
                break;
            case self::ID_DELIVERY_TOP:
                $description = self::ID_NEED_CONFIG_RESOURCES_NUM[$equityId][$days] . '次';
                break;
            case self::ID_RESUME_TEMPLATE:
                $description = '免费使用';
                break;
            case self::ID_COLLECT_VIEW:
                $description = '不限';
                break;
            case self::ID_ESTABLISHMENT_QUERY:
                $description = '<span class="primary">权益升级</span>';
                break;
            case self::ID_FOOTPRINT:
            case self::ID_JOB_RESOURCES:
            case self::ID_VIP_LOGO:
            default:
                $description = '';
                break;
        }

        return $description;
    }

    public static function getSinglePrice($equityId, $days)
    {
        switch ($equityId) {
            case self::ID_RESUME_REFRESH:
            case self::ID_RESUME_TOP:
            case self::ID_DELIVERY_TOP:
                if ($days == 15) {
                    if ($equityId == self::ID_RESUME_REFRESH) {
                        return 15;
                    }
                    if ($equityId == self::ID_RESUME_TOP) {
                        return 60;
                    }
                    if ($equityId == self::ID_DELIVERY_TOP) {
                        return 140;
                    }
                }
                if ($days == 30) {
                    if ($equityId == self::ID_RESUME_REFRESH) {
                        return 30;
                    }
                    if ($equityId == self::ID_RESUME_TOP) {
                        return 120;
                    }
                    if ($equityId == self::ID_DELIVERY_TOP) {
                        return 320;
                    }
                }

                if ($days == 60) {
                    if ($equityId == self::ID_RESUME_REFRESH) {
                        return 60;
                    }
                    if ($equityId == self::ID_RESUME_TOP) {
                        return 240;
                    }
                    if ($equityId == self::ID_DELIVERY_TOP) {
                        return 680;
                    }
                }
                // $price       = self::ID_DAY_PRICE[$equityId];
                // $singlePrice = $price * $days;
                // switch ($days) {
                //     case 15:
                //
                // }
                break;
            default:
                $singlePrice = '';
                break;
        }

        return $singlePrice;
    }

    public static function getIcon($equityId)
    {
        switch ($equityId) {
            case self::ID_ESTABLISHMENT_QUERY:
            case self::ID_FOOTPRINT:
            case self::ID_JOB_RESOURCES:
            case self::ID_VIP_LOGO:
            case self::ID_GAOCAIYOUKE:
                return '1';
            default:
                return '2';
        }
    }

    /**
     * 获取权益配置列表
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getList()
    {
        return self::find()
            ->where(['status' => self::STATUS_ONLINE])
            ->asArray()
            ->all();
    }
}
