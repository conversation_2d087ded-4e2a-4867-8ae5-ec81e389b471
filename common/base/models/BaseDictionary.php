<?php

namespace common\base\models;

use admin\models\RuleAnnouncement;
use admin\models\RuleCompany;
use admin\models\RuleJob;
use common\helpers\ArrayHelper;
use common\libs\Cache;
use common\models\Dictionary;
use common\models\HomeColumn;
use Exception;
use miniApp\models\Area;

/**
 * （1：学历水平 2：意向职能，3：工作性质 4：求职状态 5：到岗时间 6：期望月薪 7：项目类别 8：所属行业 9：资质证书 10：技能语言
 *  11：技能掌握程度 12：附件信息主题：13 职称  14：政治面貌  15：工作经验  16：是否海外经历  17：年龄要求  18：是否985/211
 *  20:单位性质  21:岗位类型  22:单位规模  23:职位发布时间范围  24:工资范围  25:单位类型  26：单位标签  27:报名方式 31:编制类别）
 */
class BaseDictionary extends Dictionary
{
    const TYPE_EDUCATION        = 1;
    const TYPE_JOB_CATEGORY     = 2;
    const TYPE_NATURE           = 3;
    const TYPE_JOB_STATUS       = 4;
    const TYPE_ARRIVE_DATE      = 5;
    const TYPE_WAGE             = 6;
    const TYPE_PROJECT_CATEGORY = 7;
    const TYPE_TRADE            = 8;
    const TYPE_CERTIFICATE      = 9;
    const TYPE_SKILL            = 10;
    const TYPE_DEGREE_TYPE      = 11;
    const TYPE_THEME            = 12;
    const TYPE_TITLE            = 13;
    const TYPE_POLITICAL        = 14;
    const TYPE_EXPERIENCE       = 15;
    const TYPE_ABROAD           = 16;
    const TYPE_AGE              = 17;
    const TYPE_SCHOOL           = 18;
    const TYPE_COMPANY_NATURE   = 20;
    const TYPE_JOB_TYPE         = 21;
    const TYPE_COMPANY_SCALE    = 22;
    const TYPE_JOB_RELEASE_TIME = 23;
    const TYPE_WAGE_RANGE       = 24;
    const TYPE_COMPANY          = 25;
    const TYPE_COMPANY_LABEL    = 26;
    const TYPE_SIGN_UP          = 27;
    const TYPE_PAPER_RANK       = 28;
    const TYPE_NATION           = 29;
    const TYPE_PATENT_RANK      = 30;
    const TYPE_ESTABLISHMENT    = 31;

    // 这些应该是要有单独表的 又或者是特殊处理的
    const TYPE_GROUP             = 100;
    const TYPE_AREA              = 101;
    const TYPE_MAJOR             = 102;
    const TYPE_ANNOUNCEMENT_TYPE = 103;

    const ADD_UNLIMITED_YES = 1;
    const ADD_UNLIMITED_NO  = 0;

    const EDUCATION_JUNIOR_PLUS_KEY        = 6;
    const EDUCATION_UNDERGRADUATE_PLUS_KEY = 7;
    const EDUCATION_MASTER_PLUS_KEY        = 8;
    const EDUCATION_DR_PLUS_KEY            = 9;

    const EDUCATION_DR_PLUS_NAME            = '博士及以上';
    const EDUCATION_MASTER_PLUS_NAME        = '硕士及以上';
    const EDUCATION_UNDERGRADUATE_PLUS_NAME = '本科及以上';
    const EDUCATION_JUNIOR_PLUS_NAME        = '大专及以上';

    const STATUS_ALL       = 0; //全部
    const STATUS_UNLIMITED = -1; //不限

    //工作年限字典，单位（年）
    const WORK_YEARS_LIST = [
        1 => [
            'min' => 0,
            'max' => 0,
        ],
        2 => [
            'min' => 1,
            'max' => 3,
        ],
        3 => [
            'min' => 3,
            'max' => 5,
        ],
        4 => [
            'min' => 5,
            'max' => 10,
        ],
        5 => [
            'min' => 10,
            'max' => 99,
        ],
    ];

    /**
     * @param int $type 本科学历
     */
    const EDUCATION_POACEAE_ID = 2;
    // 硕士学历
    const EDUCATION_MASTER_ID = 3;
    // 博士学历
    const EDUCATION_DOCTOR_ID = 4;
    //大专
    const EDUCATION_COLLEGE_ID = 1;
    //其他
    const EDUCATION_RESTS_ID = 5;

    // 应届生经验的id
    const EXPERIENCE_FRESH_Id = 1;

    // 硕博学历
    const EDUCATION_DOCTORN_AND_MASTER_ID = [
        //self::EDUCATION_MASTER_ID,
        self::EDUCATION_DOCTOR_ID,
    ];

    // 中共党员
    const POLITICAL_STATUS_PARTY_MEMBER_ID = 1;
    // 民主党派
    const POLITICAL_STATUS_DEMOCRATIC_PARTY_ID = 2;
    // 无民主党派
    const POLITICAL_STATUS_NO_DEMOCRATIC_PARTY_ID = 3;
    // 共青团员
    const POLITICAL_STATUS_LEAGUE_MEMBER_ID = 4;
    // 群众
    const POLITICAL_STATUS_MASSES_ID = 5;
    // 中共预备党员
    const POLITICAL_STATUS_PROBATIONARY_PARTY_MEMBER_ID = 6;

    // 正高级职称
    const TITLE_HIGH_ID = 1;
    // 副高级职称
    const TITLE_VICE_HIGH_ID = 2;
    // 中级职称
    const TITLE_MIDDLE_ID = 3;
    // 初级职称
    const TITLE_PRIMARY_ID = 4;

    /**
     * 获取学历水平列表
     */
    public static function getEducationList($addUnlimited = self::ADD_UNLIMITED_NO)
    {
        $list = self::getDataList(self::TYPE_EDUCATION);
        //做兼容处理
        $educationListNew = [];
        if ($addUnlimited == self::ADD_UNLIMITED_YES) {
            $educationListNew[0] = '不限';
        }
        if (isset($list[4])) {
            $educationListNew[4] = $list[4];
        }
        if (isset($list[3])) {
            $educationListNew[3] = $list[3];
        }
        if (isset($list[2])) {
            $educationListNew[2] = $list[2];
        }
        if (isset($list[1])) {
            $educationListNew[1] = $list[1];
        }
        if (isset($list[5])) {
            $educationListNew[5] = $list[5];
        }

        return $educationListNew;
    }

    public static function getSearchEducationList()
    {
        $list = self::getDataList(self::TYPE_EDUCATION);
    }

    /**
     * 获取学历水平筛选列表
     */
    public static function getEducationSearchList()
    {
        // $list = self::getDataList(self::TYPE_EDUCATION);

        // $list = [];
        //
        // // $list[self::EDUCATION_DR_PLUS_KEY]            = self::EDUCATION_DR_PLUS_NAME;
        // $list[self::EDUCATION_MASTER_PLUS_KEY]        = self::EDUCATION_MASTER_PLUS_NAME;
        // $list[self::EDUCATION_UNDERGRADUATE_PLUS_KEY] = self::EDUCATION_UNDERGRADUATE_PLUS_NAME;
        // $list[self::EDUCATION_JUNIOR_PLUS_KEY]        = self::EDUCATION_JUNIOR_PLUS_NAME;
        //
        // // 这里专门去找非其他的学历
        // $educationList = self::find()
        //     ->select('code,name')
        //     ->where([
        //         'type'   => self::TYPE_EDUCATION,
        //         'status' => self::STATUS_ACTIVE,
        //     ])
        //     ->andWhere([
        //         '<>',
        //         'code',
        //         5,
        //     ])
        //     ->orderBy('code desc')
        //     ->asArray()
        //     ->all();
        //
        // foreach ($educationList as $k => $education) {
        //     $list[$education['code']] = $education['name'];
        // }
        //
        // // 最后再把其他塞进去
        // $list[5] = '其他';

        // 最后排序
        //     // 博士研究生
        //         // 硕士及以上
        //         // 硕士研究生
        //         // 本科及以上
        //         // 本科
        //         // 大专及以上
        //         // 大专
        //         // 其他

        $list = [
            4                                      => '博士研究生',
            self::EDUCATION_MASTER_PLUS_KEY        => self::EDUCATION_MASTER_PLUS_NAME,
            3                                      => '硕士研究生',
            self::EDUCATION_UNDERGRADUATE_PLUS_KEY => self::EDUCATION_UNDERGRADUATE_PLUS_NAME,
            2                                      => '本科',
            self::EDUCATION_JUNIOR_PLUS_KEY        => self::EDUCATION_JUNIOR_PLUS_NAME,
            1                                      => '大专',
            5                                      => '其他',
        ];

        return $list;
    }

    /**
     * 用于获取真正的教育id
     * @param $key
     */
    public static function getEducationValueByKey($key)
    {
        if ($key == self::EDUCATION_MASTER_PLUS_KEY) {
            // 硕士研究生及以上,要拿硕士,博士
            return [
                3,
                4,
            ];
        }

        if ($key == self::EDUCATION_DR_PLUS_KEY) {
            // 博士研究生及以上,要拿博士
            return 4;
        }

        if ($key == self::EDUCATION_UNDERGRADUATE_PLUS_KEY) {
            // 本科及以上,要拿本科,硕士,博士
            return [
                2,
                3,
                4,
            ];
        }

        if ($key == self::EDUCATION_JUNIOR_PLUS_KEY) {
            // 大专及以上,要拿大专,本科,硕士,博士
            return [
                1,
                2,
                3,
                4,
            ];
        }

        return $key;
    }

    /**
     * 获取学历水平名称
     * @throws Exception
     */
    public static function getEducationName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_EDUCATION, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '学历不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取意向职能列表
     * @return array
     */
    public static function getJobCategoryList()
    {
        return self::getDataList(self::TYPE_JOB_CATEGORY);
    }

    /**
     * 获取意向职能名称
     * @throws Exception
     */
    public static function getJobCategoryName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_JOB_CATEGORY, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '职能不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取工作性质列表
     * @return array
     */
    public static function getNatureList($addUnlimited = self::ADD_UNLIMITED_NO)
    {
        return self::getDataList(self::TYPE_NATURE, $addUnlimited);
    }

    /**
     * 获取工作性质名称
     * @throws Exception
     */
    public static function getNatureName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_NATURE, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '工作性质不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取求职状态列表
     * @return array
     */
    public static function getJobStatusList()
    {
        return self::getDataList(self::TYPE_JOB_STATUS);
    }

    /**
     * 获取求职状态名称
     * @throws Exception
     */
    public static function getJobStatusName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_JOB_STATUS, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '求职状态不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取到岗时间列表
     * @return array
     */
    public static function getArriveDateList()
    {
        return self::getDataList(self::TYPE_ARRIVE_DATE);
    }

    /**
     * 获取到岗时间名称
     * @throws Exception
     */
    public static function getArriveDateName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_ARRIVE_DATE, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '到岗时间不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取项目类别列表
     * @return array
     */
    public static function getProjectCateList()
    {
        return self::getDataList(self::TYPE_PROJECT_CATEGORY);
    }

    /**
     * 获取项目类别名称
     * @throws Exception
     */
    public static function getProjectCateName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_PROJECT_CATEGORY, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '项目类别不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取行业列表
     * @return array
     */
    public static function getTradeList($addUnlimited = self::ADD_UNLIMITED_NO)
    {
        return BaseTrade::getAllList();
    }

    /**
     * 获取行业名称
     * @throws Exception
     */
    public static function getTradeName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_TRADE, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '行业不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    public static function getTradeNameByCode($codes)
    {
        $name = self::find()
            ->select('name')
            ->where([
                'code'   => $codes,
                'type'   => self::TYPE_TRADE,
                'status' => self::STATUS_ACTIVE,
            ])
            ->asArray()
            ->column();

        // 去重返回
        return array_unique($name);
    }

    /**
     * 获取资质证书列表
     * @return array
     */
    public static function getCertificateList()
    {
        return self::getDataList(self::TYPE_CERTIFICATE);
    }

    /**
     * 获取资质证书名称
     * @throws Exception
     */
    public static function getCertificateName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_CERTIFICATE, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '资质证书不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取技能语言列表
     * @return array
     */
    public static function getSkillList()
    {
        return self::getDataList(self::TYPE_SKILL);
    }

    /**
     * 获取技能语言名称
     * @throws Exception
     */
    public static function getSkillName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_SKILL, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '技能语言不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取技能语言掌握程度列表
     * @return array
     */
    public static function getDegreeTypeList()
    {
        return self::getDataList(self::TYPE_DEGREE_TYPE);
    }

    /**
     * 获取技能语言掌握程度名称
     * @throws Exception
     */
    public static function getDegreeTypeName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_DEGREE_TYPE, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '技能语言不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取附加信息主题列表
     * @return array
     */
    public static function getThemeList()
    {
        return self::getDataList(self::TYPE_THEME);
    }

    /**
     * 获取附加信息主题名称
     * @throws Exception
     */
    public static function getThemeName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_THEME, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '附加信息不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    public static function getFirstTitleList()
    {
        $list = self::find()
            ->where([
                'type'      => self::TYPE_TITLE,
                'status'    => self::STATUS_ACTIVE,
                'parent_id' => 0,
            ])
            ->select('name')
            ->asArray()
            ->orderBy('code asc')
            ->indexBy('code')
            ->column();

        return $list;
    }

    /**
     * 获取职称列表
     * @return array
     */
    public static function getTitleList()
    {
        // 这个有两个等级
        $list = self::find()
            ->where([
                'type'   => self::TYPE_TITLE,
                'status' => self::STATUS_ACTIVE,
            ])
            ->select('id as k,code,name as v,parent_id as parentId')
            ->asArray()
            ->all();
        foreach ($list as &$item) {
            if ($item['parentId']) {
                $item['level'] = '2';
            } else {
                $item['level'] = '1';
            }
        }

        $list = ArrayHelper::objMoreArr($list);
        // 最后需要转换一下,把code换成k
        foreach ($list as &$item) {
            $item['k'] = $item['code'];
            foreach ($item['children'] as &$child) {
                $child['k']           = $child['code'];
                $child['topParentId'] = $item['k'];
            }
        }

        return $list;
    }

    /**
     * 职称名称
     * @param $code
     * @return mixed
     * @throws Exception
     */
    public static function getTitleName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_TITLE, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '职称不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取政治面貌列表
     * @return array
     */
    public static function getPoliticalStatusList()
    {
        return self::getDataList(self::TYPE_POLITICAL);
    }

    /**
     * 经验要求列表
     * @return array
     */
    public static function getExperienceList($addUnlimited = self::ADD_UNLIMITED_NO): array
    {
        return self::getDataList(self::TYPE_EXPERIENCE, $addUnlimited);
    }

    /**
     * 获取经验要求名称
     * @param $code
     * @return string
     * @throws Exception
     */
    public static function getExperienceName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_EXPERIENCE, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '经验不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取海外经历列表getAgeList
     * @return array
     */
    public static function getAbroadList(): array
    {
        return self::getDataList(self::TYPE_ABROAD);
    }

    /**
     * 获取海外经历名称
     * @param $code
     * @return mixed
     * @throws Exception
     */
    public static function getAbroadName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_ABROAD, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '海外经历不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 编制列表
     * @return array
     */
    public static function getSystematicList(): array
    {
        return self::getDataList(self::TYPE_ESTABLISHMENT);
    }

    /**
     * 获取年龄列表
     * @return array
     */
    public static function getAgeList(): array
    {
        return self::getDataList(self::TYPE_AGE);
    }

    /**
     * 获取年龄名称
     * @throws Exception
     */
    public static function getAgeName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_AGE, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '年龄不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 政治面貌名称
     * @param $code
     * @return mixed
     * @throws Exception
     */
    public static function getPoliticalStatusName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_POLITICAL, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取单位性质列表
     * @return array
     */
    public static function getCompanyNatureList($addUnlimited = self::ADD_UNLIMITED_NO)
    {
        return self::getDataList(self::TYPE_COMPANY_NATURE, $addUnlimited);
    }

    /**
     * 获取单位性质名称
     * @param $code
     * @return mixed
     * @throws Exception
     */
    public static function getCompanyNatureName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_COMPANY_NATURE, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '单位性质不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取岗位类型列表
     * @return array
     */
    public static function getJobTypeList()
    {
        return self::getDataList(self::TYPE_JOB_TYPE);
    }

    /**
     * 获取岗位类型名称
     * @param $code
     * @return mixed
     * @throws Exception
     */
    public static function getJobTypeName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_JOB_TYPE, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '岗位类型不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取单位规模列表
     * @return array
     */
    public static function getCompanyScaleList($addUnlimited = self::ADD_UNLIMITED_NO)
    {
        return self::getDataList(self::TYPE_COMPANY_SCALE, $addUnlimited);
    }

    /**
     * 获取单位规模名称
     * @param $code
     * @return mixed
     * @throws Exception
     */
    public static function getCompanyScaleName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_COMPANY_SCALE, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '规模不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取是否985/211
     * @return array
     */
    public static function getSchoolTypeList(): array
    {
        return self::getDataList(self::TYPE_SCHOOL);
    }

    /**
     * 获取单位类型列表
     * @return array
     */
    public static function getCompanyTypeList($addUnlimited = self::ADD_UNLIMITED_NO): array
    {
        return self::getDataList(self::TYPE_COMPANY, $addUnlimited);
    }

    /**
     * 获取单位类型名称
     * @param $code
     * @return mixed|string
     * @throws Exception
     */
    public static function getCompanyTypeName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_COMPANY, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '单位类型不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取职位发布时间列表
     * @return array
     */
    public static function getReleaseTimeList($addUnlimited = self::ADD_UNLIMITED_NO)
    {
        return self::getDataList(self::TYPE_JOB_RELEASE_TIME, $addUnlimited);
    }

    /**
     * 获取职位发布时间
     * @param $code
     * @return mixed|string
     * @throws Exception
     */
    public static function getReleaseTimeName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_JOB_RELEASE_TIME, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取薪资范围列表
     * @return array
     */
    public static function getWageRangeList($addUnlimited = self::ADD_UNLIMITED_NO)
    {
        return self::getDataList(self::TYPE_WAGE_RANGE, $addUnlimited);
    }

    /**
     * 获取薪资范围名称
     * @param $code
     * @return mixed|string
     * @throws Exception
     */
    public static function getWageRangeName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_WAGE_RANGE, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '薪资范围不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取单位标签列表
     * @param int $addUnlimited
     * @return array
     */
    public static function getCompanyLabelList($addUnlimited = self::ADD_UNLIMITED_NO)
    {
        return self::getDataList(self::TYPE_COMPANY_LABEL, $addUnlimited);
    }

    /**
     * 获取单位标签名称
     * @param $code
     * @return mixed|string
     * @throws Exception
     */
    public static function getCompanyLabelName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_COMPANY_LABEL, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取编制文案
     */
    public static function getEstablishmentName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_ESTABLISHMENT, $code);

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取多个编制文案
     */
    public static function getAllEstablishmentName($codeArr)
    {
        $cache_data = Cache::hMGet(Cache::ALL_TABLE_DICTIONARY_TYPE_KEY . ':' . self::TYPE_ESTABLISHMENT, $codeArr);
        $name_arr   = $cache_data ? array_column($cache_data, 'name') : [];

        return implode(',', $name_arr);
    }

    /**
     * @param $where
     * @param $select
     * @return array
     */
    public static function findList($where, $select): array
    {
        return self::find()
            ->where($where)
            ->select($select)
            ->asArray()
            ->all();
    }

    /**
     * 获取报名方式列表
     * @param int $addUnlimited
     * @return array
     */
    public static function getSignUpList($addUnlimited = self::ADD_UNLIMITED_NO)
    {
        return self::getDataList(self::TYPE_SIGN_UP, $addUnlimited);
    }

    /**
     * 获取论文位次列表
     * @param int $addUnlimited
     * @return array
     */
    public static function getPaperRankList()
    {
        return self::getDataList(self::TYPE_PAPER_RANK);
    }

    /**
     * 获取民族列表
     * @param int $addUnlimited
     * @return array
     */
    public static function getNationList()
    {
        return self::getDataList(self::TYPE_NATION);
    }

    /**
     * 获取专利位次列表
     * @return array
     */
    public static function getPatentRankList()
    {
        return self::getDataList(self::TYPE_PATENT_RANK);
    }

    /**
     * 多个id，获取名称字符串
     * @param $ids
     * @return mixed
     * @throws \Exception
     */
    public static function getAllSignUpName($ids)
    {
        $info = self::find()
            ->where([
                'in',
                'code',
                $ids,
            ])
            ->andWhere(['type' => self::TYPE_SIGN_UP])
            ->select('name')
            ->asArray()
            ->all();

        $signUpName = '';
        foreach ($info as $key => $item) {
            $signUpName .= $item['name'] . ',';
        }

        return substr($signUpName, 0, -1);
    }

    /**
     * 获取报名方式名称
     * @param $code
     * @return mixed|string
     * @throws Exception
     */
    public static function getSignUpName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_SIGN_UP, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '报名方式不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取论文名次名称
     * @param $code
     * @return mixed|string
     * @throws Exception
     */
    public static function getPaperRankName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_PAPER_RANK, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '论文名次不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取民族名称
     * @param $code
     * @return mixed|string
     * @throws Exception
     */
    public static function getNationName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_NATION, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '民族不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    /**
     * 获取专利位次名称
     * @param $code
     * @return mixed|string
     * @throws Exception
     */
    public static function getPatentRankName($code)
    {
        if (!empty($code)) {
            $name = self::getDataName(self::TYPE_PATENT_RANK, $code);
            if (empty($name) && $code == self::STATUS_UNLIMITED) {
                $name = '专利位次不限';
            }

            return $name ?: '';
        } else {
            return '';
        }
    }

    public static function setCache($type)
    {
        $list = self::find()
            ->where([
                'type'   => $type,
                'status' => self::STATUS_ACTIVE,
            ])
            ->select('id,name,code,type,parent_id')
            ->orderBy('code asc')
            ->asArray()
            ->all();

        return Cache::hMSet(Cache::ALL_TABLE_DICTIONARY_TYPE_KEY . ':' . $type, array_column($list, null, 'code'));
    }

    /**
     * 获取数据列表
     * @param $type
     * @return array
     */
    protected static function getDataList($type, $addUnlimited = self::ADD_UNLIMITED_NO)
    {
        $data = Cache::hvals(Cache::ALL_TABLE_DICTIONARY_TYPE_KEY . ':' . $type);
        if (!$data) {
            self::setCache($type);
            $data = Cache::hvals(Cache::ALL_TABLE_DICTIONARY_TYPE_KEY . ':' . $type);
        }
        //        //（1学历水平，2意向职能，3工作性质，4求职状态，5到岗时间，6期望月薪，7项目类别）
        //        $list = self::find()
        //            ->where([
        //                'type'   => $type,
        //                'status' => self::STATUS_ACTIVE,
        //            ])
        //            ->select('name')
        //            ->asArray()
        //            ->orderBy('code asc')
        //            ->indexBy('code')
        //            ->column();
        $list = array_column($data, 'name', 'code');

        if ($addUnlimited == self::ADD_UNLIMITED_YES) {
            array_unshift($list, '经验不限');
        }

        return $list;
    }

    /**
     * 获取数据名称
     * @param $type
     * @param $code
     * @return mixed
     * @throws Exception
     */
    public static function getDataName($type, $code)
    {
        if ($code <= 0) {
            return '';
        }

        //先获取  没有则查询数据库set进redis
        $data = Cache::hGet(Cache::ALL_TABLE_DICTIONARY_TYPE_KEY . ':' . $type, $code);
        if (!$data) {
            self::setCache($type);
            $data = Cache::hGet(Cache::ALL_TABLE_DICTIONARY_TYPE_KEY . ':' . $type, $code);
        }
        $data = json_decode($data, true);

        return $data['name'];

        //               return self::findOneVal([
        //                   'type'   => $type,
        //                   'code'   => $code,
        //                   'status' => self::STATUS_ACTIVE,
        //               ], 'name');
    }

    public static function getMinAndMaxWage($type)
    {
        // 找到对应的内容
        $name = self::findOneVal([
            'code' => $type,
            'type' => BaseDictionary::TYPE_WAGE_RANGE,
        ], 'name');

        if (empty($name)) {
            //            throw new Exception('期望月薪选择错误');
        }

        if ($name == '面议') {
            $min = 0;
            $max = 0;
        } elseif ($name == '3k以下') {
            $min = 0;
            $max = 3000;
        } else {
            // 其余的都是有最大和最小的了
            $wageArr = explode('-', str_replace('k', '', $name));
            $min     = $wageArr[0] * 1000;
            $max     = $wageArr[1] * 1000;
        }

        return [
            'min' => $min,
            'max' => $max,
        ];
    }

    /**
     * 根据类型和名字获取code
     * @param $type
     * @param $name
     */
    public static function getCodeByName($type, $name)
    {
        return self::findOneVal([
            'status' => self::STATUS_ACTIVE,
            'type'   => $type,
            'name'   => $name,
        ], 'code');
    }

    /**
     * 字典获取真正的值
     * @param $releaseTimeType
     * @return mixed
     */
    public static function getReleaseTimeListInfo($releaseTimeType)
    {
        $releaseTimeList = [
            '1' => date('Y-m-d H:i:s', strtotime('-1 day')),
            //24小时内
            '2' => date('Y-m-d H:i:s', mktime(0, 0, 0, date('m'), date('d') - 3, date('Y'))),
            //近三天
            '3' => date('Y-m-d H:i:s', mktime(0, 0, 0, date('m'), date('d') - 7, date('Y'))),
            //近一周
            '4' => date('Y-m-d H:i:s', mktime(0, 0, 0, date('m') - 1, date('d'), date('Y'))),
            //近一个月
        ];

        return $releaseTimeList[$releaseTimeType];
    }

    public static function getBirthday($type = 0)
    {
        $birthdayList = [
            1 => [
                'min' => date('Y-m-d', strtotime("-20year")),
                'max' => date('Y-m-d', strtotime("-25year")),
            ],
            2 => [
                'min' => date('Y-m-d', strtotime("-25year")),
                'max' => date('Y-m-d', strtotime("-30year")),
            ],
            3 => [
                'min' => date('Y-m-d', strtotime("-30year")),
                'max' => date('Y-m-d', strtotime("-35year")),
            ],
            4 => [
                'min' => date('Y-m-d', strtotime("-35year")),
                'max' => date('Y-m-d', strtotime("-40year")),
            ],
            5 => [
                'min' => date('Y-m-d', strtotime("-40year")),
                'max' => date('Y-m-d', strtotime("-99year")),
            ],
        ];

        if (!$type) {
            return $birthdayList;
        }

        return $birthdayList[$type];
    }

    public static function getAllChildTitleArray($titleCode)
    {
        $list = [];
        foreach ($titleCode as $item) {
            $list = array_merge(self::getAllChildTitle($item), $list);
        }

        return $list;
    }

    /**
     * 获取子类职称列表code（包含自身）
     * @param $titleCode
     * @return int[]|string[]
     */
    public static function getAllChildTitle($titleCode)
    {
        $parentTitleId = self::findOneVal([
            'type' => self::TYPE_TITLE,
            'code' => $titleCode,
        ], 'id');

        $childList = self::find()
            ->where([
                'parent_id' => $parentTitleId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->select('code')
            ->indexBy('code')
            ->asArray()
            ->all();

        $childIdarr = array_keys($childList);

        if (is_array($titleCode)) {
            $childIdarr = array_merge($childIdarr, $titleCode);
        } else {
            array_push($childIdarr, $titleCode);
        }

        return $childIdarr;
    }

    public static function getValueByCodeArray($type, $codeArray)
    {
        $data = self::find()
            ->select([
                'name',
            ])
            ->andWhere([
                'status' => self::STATUS_ACTIVE,
                'type'   => $type,
            ])
            ->andWhere(['code' => $codeArray])
            ->asArray()
            ->column();

        return implode(',', $data);
    }

    /**
     *
     * 学历排序
     */
    public static function getEducationDesc($codeArray)
    {
        $list = self::find()
            ->select([
                'code',
                'name',
            ])
            ->andWhere([
                'status' => self::STATUS_ACTIVE,
                'type'   => self::TYPE_EDUCATION,
                'code'   => $codeArray,
            ])
            ->orderBy('code desc')
            ->groupBy('code')
            ->asArray()
            ->all();

        // 这里如果有code是5的,就把5放到最后
        $code5 = [];
        foreach ($list as $key => $value) {
            if ($value['code'] == 5) {
                $code5 = $value;
                unset($list[$key]);
            }
        }
        if ($code5) {
            $list[] = $code5;
        }

        // 只要name
        $list = array_column($list, 'name');

        return $list;
    }

    /**
     * 获取拼接的学历字符串，通过id数组来获取
     * @param $idArr
     * @return false|string
     * @throws Exception
     */
    public static function getEducationTextByCodeList($idArr)
    {
        if (!empty($idArr)) {
            $text = '';
            foreach ($idArr as $id) {
                $text .= self::getEducationName($id) . ',';
            }

            return substr($text, 0, -1);
        } else {
            return '';
        }
    }

    /**
     * 小程序地区页面的选择
     *
     * "k": "0",
     * "v": "热门城市",
     * "parentId": "-1",
     * "level": "0",
     * "children": [
     */
    public static function miniAreaPageSelect()
    {
        $areaPageSelect = \Yii::$app->params['areaPageSelect'];

        $hotList = $areaPageSelect['hotAreaColumn'];
        $list    = [];

        try {
            $res = Area::getAreaCurrent();
            // 转k,v
            $res['k'] = $res['id'];
            $res['v'] = $res['name'];
        } catch (\Exception $e) {
            $res = [
                'k' => '1964',
                'v' => '广东',
            ];
        }

        $list[0]['k']        = '0';
        $list[0]['v']        = '当前定位';
        $list[0]['parentId'] = '-1';
        $list[0]['level']    = '0';
        $list[0]['children'] = [$res];

        $list[1]['k']        = '0';
        $list[1]['v']        = '热门城市';
        $list[1]['parentId'] = '-1';
        $list[1]['level']    = '0';
        $list[1]['children'] = [];
        foreach ($hotList as $item) {
            $name = $item['name'];
            $id   = BaseArea::findOneVal([
                'name'  => $name,
                'level' => [
                    1,
                    2,
                ],
            ], 'id');
            if (in_array($id, BaseArea::HIGH_CROWN_ID_LIST)) {
                // 直辖市,找上一级
                $id = BaseArea::findOneVal([
                    'name'  => $item['name'],
                    'level' => 2,
                ], 'id');
            }
            $list[1]['children'][] = [
                'k'        => (string)$id,
                'v'        => $item['name'],
                'parentId' => '0',
                'level'    => '1',
            ];
        }

        $province            = $areaPageSelect['provinceColumn'];
        $city                = $areaPageSelect['cityColumn'];
        $list[2]['k']        = '0';
        $list[2]['v']        = '省份';
        $list[2]['parentId'] = '-1';
        $list[2]['level']    = '0';
        $list[2]['children'] = [];
        foreach ($province as $item) {
            $id = BaseArea::findOneVal([
                'name'  => $item['name'],
                'level' => 1,
            ], 'id');

            if (in_array($id, BaseArea::HIGH_CROWN_ID_LIST)) {
                // 直辖市,找上一级
                $id = BaseArea::findOneVal([
                    'name'  => $item['name'],
                    'level' => 2,
                ], 'id');
            }

            $list[2]['children'][] = [
                'k'        => (string)$id,
                'v'        => $item['name'],
                'parentId' => '0',
                'level'    => '1',
            ];
        }

        // $list[3]['k']        = '0';
        // $list[3]['v']        = '城市';
        // $list[3]['parentId'] = '-1';
        // $list[3]['level']    = '0';
        // $list[3]['children'] = [];
        //
        // foreach ($city as $k => $item) {
        //     foreach ($item as $i) {
        //         $id                    = BaseArea::findOneVal([
        //             'name'  => $i['name'],
        //             'level' => 2,
        //         ], 'id');
        //         $list[3]['children'][] = [
        //             'k'        => (string)$id,
        //             'v'        => $i['name'],
        //             'parentId' => '0',
        //             'level'    => '1',
        //         ];
        //     }
        // }

        $list[3]['k']        = '0';
        $list[3]['v']        = '城市';
        $list[3]['parentId'] = '-1';
        $list[3]['level']    = '0';
        $list[3]['children'] = [];

        foreach ($city as $k => $item) {
            $children = [];
            foreach ($item as $i) {
                // 这里需要兼容一下吉林市,把吉林市换成吉林
                if ($i['name'] == '吉林市') {
                    $id = BaseArea::findOneVal([
                        'name'  => '吉林',
                        'level' => 2,
                    ], 'id');
                } else {
                    $id = BaseArea::findOneVal([
                        'name'  => $i['name'],
                        'level' => 2,
                    ], 'id');
                }

                $children[] = [
                    'k'        => (string)$id,
                    'v'        => $i['name'],
                    'parentId' => '0',
                    'level'    => '1',
                ];
            }
            $list[] = [
                'k'        => '0',
                'v'        => $k,
                'parentId' => '-1',
                'level'    => '0',
                'children' => $children,
            ];
        }

        return $list;
    }

    public static function changeIsMiniapp($data)
    {
        $type  = $data['type'];
        $id    = $data['id'];
        $value = $data['value'];

        // 上面三个值都检查一下
        if (!in_array($type, [
            'job',
            'announcement',
            'company',
        ])) {
            throw new \Exception('type错误');
        }

        if (!in_array($value, [
            0,
            1,
            2,
        ])) {
            throw new \Exception('value错误');
        }

        if (!is_numeric($id)) {
            throw new \Exception('id错误');
        }

        switch ($type) {
            case 'job':
                $model = BaseJob::findOne($id);
                $exec  = new RuleJob();
                break;
            case 'announcement':
                $model = BaseAnnouncement::findOne($id);
                $exec  = new RuleAnnouncement();
                break;
            case 'company':
                $model = BaseCompany::findOne($id);
                $exec  = new RuleCompany();
                break;
        }

        if (empty($model)) {
            throw new \Exception('数据不存在');
        }

        if ($value == 0) {
            $is_miniapp           = $exec->exec($id);
            $model->is_miniapp    = $is_miniapp ? 1 : 2;
            $model->is_manual_tag = 0;
        } else {
            $model->is_miniapp    = $value;
            $model->is_manual_tag = $value;
        }
        $model->save();

        return true;
    }

    public static function changeIsAbroad($data)
    {
        $type  = $data['type'];
        $id    = $data['id'];
        $value = $data['value'];

        // 上面三个值都检查一下
        if (!in_array($type, [
            'job',
            'announcement',
            'company',
        ])) {
            throw new \Exception('type错误');
        }

        if (!in_array($value, [
            0,
            1,
            2,
        ])) {
            throw new \Exception('value错误');
        }

        if (!is_numeric($id)) {
            throw new \Exception('id错误');
        }

        switch ($type) {
            case 'job':
                // 预留

                break;
            case 'announcement':
                // 预留
                break;
            case 'company':
                $model            = BaseCompany::findOne($id);
                $model->is_abroad = $value;
                $model->save();
                break;
        }

        return true;
    }

    public static function getEmoteList()
    {
        return [
            '😀',
            '😁',
            '😆',
            '😊',
            '😅',
            '🤣',
            '😂',
            '🙂',
            '🙃',
            '😉',
            '😎',
            '😌',
            '😔',
            '😇',
            '😟',
            '😱',
            '😭',
            '😣',
            '😓',
            '😥',
            '😲',
            '😳',
            '😧',
            '😰',
            '😩',
            '🥱',
            '😤',
            '😡',
            '😠',
            '👍',
            '👎',
            '✊',
            '👊',
            '🤝',
            '👌',
            '✌',
            '👆',
            '👇',
            '👉',
            '👈',
            '👋',
            '✋',
            '👏',
            '🙌',
            '💪',
            '🙏',
            '👻',
            '🐵',
            '🙈',
            '🙉',
            '🙊',
            '💌',
            '💔',
            '🧡',
            '💛',
            '💋',
            '💯',
            '💢',
            '💦',
            '💤',
            '🥰',
            '😍',
            '🤩',
            '😘',
            '😚',
            '😜',
            '🤪',
            '😝',
            '🤗',
            '🤭',
            '🤔',
            '🤐',
            '😏',
            '😒',
            '🙄',
            '😬',
            '😴',
            '😷',
            '🥵',
            '😵‍',
            '🥳',
            '🚄',
            '⏰',
            '⌚',
            '🧳',
            '🎉',
            '🌹',
            '⭐',
            '🌈',
            '⚡',
        ];
    }

    public static function commonGetText($type, $value)
    {
        // 根据不同的字典类型到不同的表里面去取文本
        $codeArray = explode(',', $value);
        switch ($type) {
            case self::TYPE_EDUCATION:
            case self::TYPE_NATURE:
            case self::TYPE_JOB_STATUS:
            case self::TYPE_ARRIVE_DATE:
            case self::TYPE_WAGE:
            case self::TYPE_PROJECT_CATEGORY:
            case self::TYPE_TRADE:
            case self::TYPE_CERTIFICATE:
            case self::TYPE_SKILL:
            case self::TYPE_DEGREE_TYPE:
            case self::TYPE_THEME:
            case self::TYPE_TITLE:
            case self::TYPE_POLITICAL:
            case self::TYPE_EXPERIENCE:
            case self::TYPE_ABROAD:
            case self::TYPE_AGE:
            case self::TYPE_SCHOOL:
            case self::TYPE_COMPANY_NATURE:
            case self::TYPE_COMPANY_SCALE:
            case self::TYPE_JOB_RELEASE_TIME:
            case self::TYPE_WAGE_RANGE:
            case self::TYPE_COMPANY:
            case self::TYPE_COMPANY_LABEL:
            case self::TYPE_SIGN_UP:
            case self::TYPE_PAPER_RANK:
            case self::TYPE_NATION:
            case self::TYPE_PATENT_RANK:
            case self::TYPE_ESTABLISHMENT:
                $text = self::getValueByCodeArray($type, $codeArray);
                // 转数组
                break;
            case self::TYPE_ANNOUNCEMENT_TYPE:
                // 公告类型,其实是栏目名称
                $text = HomeColumn::find()
                    ->select([
                        'name',
                    ])
                    ->andWhere(['id' => $codeArray])
                    ->asArray()
                    ->column();
                break;
            case self::TYPE_JOB_TYPE:
            case self::TYPE_JOB_CATEGORY:
                $text = BaseCategoryJob::find()
                    ->select([
                        'name',
                    ])
                    ->andWhere(['id' => $codeArray])
                    ->asArray()
                    ->column();
                // 转数组
                break;

            case self::TYPE_AREA:
                $text = BaseArea::find()
                    ->select([
                        'name',
                    ])
                    ->andWhere(['id' => $codeArray])
                    ->asArray()
                    ->column();
                break;
            case self::TYPE_MAJOR:
                $text = BaseMajor::find()
                    ->select([
                        'name',
                    ])
                    ->andWhere(['id' => $codeArray])
                    ->asArray()
                    ->column();
                break;
            case self::TYPE_GROUP:
                // 这里是一个比较特殊的，是专门给职位的复合搜索用的，存在 redis 里
                // $data = self::getjob
                $data = BaseJob::getMiniJobSearchParams();
                // 是一个二维数组
                foreach ($data as $item) {
                    $children = $item['children'];
                    // 循环里面取取值，找到 k 对于的 v
                    foreach ($children as $child) {
                        if (in_array($child['k'], $codeArray)) {
                            $text[] = $child['v'];
                        }
                    }
                }
                break;
        }

        if (!is_array($text)) {
            $text = explode(',', $text);
        }

        return $text;
    }

}