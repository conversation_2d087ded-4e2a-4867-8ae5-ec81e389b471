<?php

namespace common\base\models;

use common\base\BaseActiveRecord;
use common\helpers\FileHelper;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\libs\Cache;
use common\libs\WxMiniApp;
use common\libs\WxWork;
use common\models\HwActivity;
use common\service\zhaoPinHuiColumn\BaseService;
use Yii;
use yii\base\Exception;

class BaseHwActivity extends HwActivity
{
    //活动系列
    const SERIES_COME_HOME            = 1;
    const SERIES_OVERSEAS_RECRUITMENT = 2;
    const SERIES_MORE_ACTIVITY        = 3;
    const SERIES_BOSHIHOU             = 4;
    const SERIES_ZHAOPINHUI_OFFLINE   = 5;
    const SERIES_ZHAOPINHUI_ONLINE    = 6;

    const SERIES_TEXT_LIST = [
        self::SERIES_COME_HOME            => '归国活动',
        self::SERIES_OVERSEAS_RECRUITMENT => '出海引才',
        self::SERIES_MORE_ACTIVITY        => '更多活动（出海）',
        self::SERIES_BOSHIHOU             => '高才博士后',
        self::SERIES_ZHAOPINHUI_OFFLINE   => '国内线下招聘会',
        self::SERIES_ZHAOPINHUI_ONLINE    => '线上引才活动',
    ];

    //非招聘会类型
    const ZHAOPINHUI_UN_TYPE = [
        self::SERIES_COME_HOME,
        self::SERIES_OVERSEAS_RECRUITMENT,
        self::SERIES_MORE_ACTIVITY,
        self::SERIES_BOSHIHOU,
    ];
    //招聘会类型
    const ZHAOPINHUI_TYPE = [
        self::SERIES_ZHAOPINHUI_OFFLINE,
        self::SERIES_ZHAOPINHUI_ONLINE,
    ];

    //活动系列、活动类型对应关系
    const SERIES_TYPE_RELATION_LIST = [
        self::SERIES_OVERSEAS_RECRUITMENT => [
            self::TYPE_CHUHAI_OVERSEAS_SESSION,
            self::TYPE_CHUHAI_GROUP_RECRUITMENT,
            self::TYPE_CHUHAI_OVERSEAS_ACTIVITY,
            self::TYPE_CHUHAI_OTHER_ACTIVITY,
        ],
        self::SERIES_COME_HOME            => [
            self::TYPE_GUIGUO_SCHOLARS_FORUM,
            self::TYPE_GUIGUO_STUDENTS_RETURNED_HOME,
            self::TYPE_GUIGUO_VENTURE_CONTEST,
            self::TYPE_GUIGUO_TALENT_CONFERENCE,
            self::TYPE_GUIGUO_OTHER_ACTIVITY,
        ],
        self::SERIES_MORE_ACTIVITY        => [
            self::TYPE_MORE_THIRD_PARTY,
        ],
        self::SERIES_BOSHIHOU             => [
            self::TYPE_BOSHIHOU_ACTIVITY,
        ],
        self::SERIES_ZHAOPINHUI_OFFLINE   => [
            self::TYPE_ZHAOPINHUI_OFFLINE_NATIONAL_TOUR,
            self::TYPE_ZHAOPINHUI_OFFLINE_RPO_PROJECT,
            self::TYPE_ZHAOPINHUI_OFFLINE_GROUP_SPECIAL,
        ],
        self::SERIES_ZHAOPINHUI_ONLINE    => [
            self::TYPE_ZHAOPINHUI_ONLINE_RPO,
            self::TYPE_ZHAOPINHUI_ONLINE_MERITOCRACY,
            self::TYPE_ZHAOPINHUI_ONLINE_ZPH,
        ],
    ];

    const TYPE_CHUHAI_OVERSEAS_SESSION          = 1;
    const TYPE_CHUHAI_GROUP_RECRUITMENT         = 2;
    const TYPE_CHUHAI_OVERSEAS_ACTIVITY         = 3;
    const TYPE_CHUHAI_OTHER_ACTIVITY            = 4;
    const TYPE_GUIGUO_SCHOLARS_FORUM            = 5;
    const TYPE_GUIGUO_STUDENTS_RETURNED_HOME    = 6;
    const TYPE_GUIGUO_VENTURE_CONTEST           = 7;
    const TYPE_GUIGUO_TALENT_CONFERENCE         = 8;
    const TYPE_GUIGUO_OTHER_ACTIVITY            = 9;
    const TYPE_MORE_THIRD_PARTY                 = 10;
    const TYPE_BOSHIHOU_ACTIVITY                = 11;
    const TYPE_ZHAOPINHUI_OFFLINE_NATIONAL_TOUR = 12;//全国巡回现场招聘会
    const TYPE_ZHAOPINHUI_OFFLINE_RPO_PROJECT   = 13;//RPO项目人才交流会
    const TYPE_ZHAOPINHUI_OFFLINE_GROUP_SPECIAL = 14;//组团&特色专场招聘会
    const TYPE_ZHAOPINHUI_ONLINE_RPO            = 15;//RPO线上面试会
    const TYPE_ZHAOPINHUI_ONLINE_MERITOCRACY    = 16;//英才职通车（全球引才直播交流会）
    const TYPE_ZHAOPINHUI_ONLINE_ZPH            = 17;//线上招聘会

    const TYPE_CHUHAI_OVERSEAS_SESSION_NAME          = '海外专场';
    const TYPE_CHUHAI_GROUP_RECRUITMENT_NAME         = '组团招聘';
    const TYPE_CHUHAI_OVERSEAS_ACTIVITY_NAME         = '海外活动';
    const TYPE_CHUHAI_OTHER_ACTIVITY_NAME            = '其他活动';
    const TYPE_GUIGUO_SCHOLARS_FORUM_NAME            = '学者论坛';
    const TYPE_GUIGUO_STUDENTS_RETURNED_HOME_NAME    = '学子归国行';
    const TYPE_GUIGUO_VENTURE_CONTEST_NAME           = '创业大赛';
    const TYPE_GUIGUO_TALENT_CONFERENCE_NAME         = '人才大会';
    const TYPE_GUIGUO_OTHER_ACTIVITY_NAME            = '其他活动';
    const TYPE_MORE_THIRD_PARTY_NAME                 = '第三方活动';
    const TYPE_BOSHIHOU_ACTIVITY_NAME                = '博后活动';
    const TYPE_ZHAOPINHUI_OFFLINE_NATIONAL_TOUR_NAME = '全国巡回现场招聘会';
    const TYPE_ZHAOPINHUI_OFFLINE_RPO_PROJECT_NAME   = 'RPO项目人才交流会';
    const TYPE_ZHAOPINHUI_OFFLINE_GROUP_SPECIAL_NAME = '组团&特色专场招聘会';
    const TYPE_ZHAOPINHUI_ONLINE_RPO_NAME            = 'RPO线上面试会';
    const TYPE_ZHAOPINHUI_ONLINE_MERITOCRACY_NAME    = '英才职通车（全球引才直播交流会）';
    const TYPE_ZHAOPINHUI_ONLINE_ZPH_NAME            = '线上职聘会';

    const TYPE_TEXT_LIST = [
        self::TYPE_CHUHAI_OVERSEAS_SESSION          => self::TYPE_CHUHAI_OVERSEAS_SESSION_NAME,
        self::TYPE_CHUHAI_GROUP_RECRUITMENT         => self::TYPE_CHUHAI_GROUP_RECRUITMENT_NAME,
        self::TYPE_CHUHAI_OVERSEAS_ACTIVITY         => self::TYPE_CHUHAI_OVERSEAS_ACTIVITY_NAME,
        self::TYPE_CHUHAI_OTHER_ACTIVITY            => self::TYPE_CHUHAI_OTHER_ACTIVITY_NAME,
        self::TYPE_GUIGUO_SCHOLARS_FORUM            => self::TYPE_GUIGUO_SCHOLARS_FORUM_NAME,
        self::TYPE_GUIGUO_STUDENTS_RETURNED_HOME    => self::TYPE_GUIGUO_STUDENTS_RETURNED_HOME_NAME,
        self::TYPE_GUIGUO_VENTURE_CONTEST           => self::TYPE_GUIGUO_VENTURE_CONTEST_NAME,
        self::TYPE_GUIGUO_TALENT_CONFERENCE         => self::TYPE_GUIGUO_TALENT_CONFERENCE_NAME,
        self::TYPE_GUIGUO_OTHER_ACTIVITY            => self::TYPE_GUIGUO_OTHER_ACTIVITY_NAME,
        self::TYPE_MORE_THIRD_PARTY                 => self::TYPE_MORE_THIRD_PARTY_NAME,
        self::TYPE_BOSHIHOU_ACTIVITY                => self::TYPE_BOSHIHOU_ACTIVITY_NAME,
        self::TYPE_ZHAOPINHUI_OFFLINE_NATIONAL_TOUR => self::TYPE_ZHAOPINHUI_OFFLINE_NATIONAL_TOUR_NAME,
        self::TYPE_ZHAOPINHUI_OFFLINE_RPO_PROJECT   => self::TYPE_ZHAOPINHUI_OFFLINE_RPO_PROJECT_NAME,
        self::TYPE_ZHAOPINHUI_OFFLINE_GROUP_SPECIAL => self::TYPE_ZHAOPINHUI_OFFLINE_GROUP_SPECIAL_NAME,
        self::TYPE_ZHAOPINHUI_ONLINE_RPO            => self::TYPE_ZHAOPINHUI_ONLINE_RPO_NAME,
        self::TYPE_ZHAOPINHUI_ONLINE_MERITOCRACY    => self::TYPE_ZHAOPINHUI_ONLINE_MERITOCRACY_NAME,
        self::TYPE_ZHAOPINHUI_ONLINE_ZPH            => self::TYPE_ZHAOPINHUI_ONLINE_ZPH_NAME,
    ];

    //调用活动类型
    const SUB_TYPE_TEXT_LIST = [
        self::TYPE_GUIGUO_SCHOLARS_FORUM         => self::TYPE_GUIGUO_SCHOLARS_FORUM_NAME,
        self::TYPE_GUIGUO_STUDENTS_RETURNED_HOME => self::TYPE_GUIGUO_STUDENTS_RETURNED_HOME_NAME,
        self::TYPE_GUIGUO_VENTURE_CONTEST        => self::TYPE_GUIGUO_VENTURE_CONTEST_NAME,
        self::TYPE_GUIGUO_TALENT_CONFERENCE      => self::TYPE_GUIGUO_TALENT_CONFERENCE_NAME,
        self::TYPE_GUIGUO_OTHER_ACTIVITY         => self::TYPE_GUIGUO_OTHER_ACTIVITY_NAME,
    ];

    const SUB_TYPE_PARENT_LIST = [
        self::TYPE_GUIGUO_SCHOLARS_FORUM         => self::SERIES_COME_HOME,
        self::TYPE_GUIGUO_STUDENTS_RETURNED_HOME => self::SERIES_COME_HOME,
        self::TYPE_GUIGUO_VENTURE_CONTEST        => self::SERIES_COME_HOME,
        self::TYPE_GUIGUO_TALENT_CONFERENCE      => self::SERIES_COME_HOME,
        self::TYPE_GUIGUO_OTHER_ACTIVITY         => self::SERIES_COME_HOME,
    ];

    // 活动招聘会页面链接的前缀
    const ACTIVITY_LINK_LIST = [
        self::SERIES_ZHAOPINHUI_OFFLINE => 'https://zhaopinhui.gaoxiaojob.com/xianxia/',
        self::SERIES_ZHAOPINHUI_ONLINE  => 'https://zhaopinhui.gaoxiaojob.com/xianshang/',
    ];

    //上架状态
    const GROUNDING_STATUS_ON        = 1;
    const GROUNDING_STATUS_OFF       = 2;
    const GROUNDING_STATUS_TEXT_LIST = [
        self::GROUNDING_STATUS_ON  => '已上架',
        self::GROUNDING_STATUS_OFF => '下架',
    ];
    //活动大状态
    const ACTIVITY_STATUS_PROGRESS = 1;
    const ACTIVITY_STATUS_END      = 2;

    const ACTIVITY_STATUS_TEXT_LIST = [
        self::ACTIVITY_STATUS_PROGRESS => '进行中',
        self::ACTIVITY_STATUS_END      => '已结束',
    ];

    //活动子状态
    //    const ACTIVITY_CHILD_STATUS_SIGN_UP   = 1;//报名中
    //    const ACTIVITY_CHILD_STATUS_NOT_START = 2;//待举办
    //    const ACTIVITY_CHILD_STATUS_PROGRESS  = 3;//正在进行
    //    const ACTIVITY_CHILD_STATUS_END       = 4;//结束
    // 活动子状态-重构新版
    const ACTIVITY_CHILD_STATUS_SIGN_UP           = -1;//报名中
    const ACTIVITY_CHILD_STATUS_NOT_START         = 1;//待举办
    const ACTIVITY_CHILD_STATUS_BE_ABOUT_TO_START = 2;//即将开始
    const ACTIVITY_CHILD_STATUS_PROGRESS          = 3;//正在进行
    const ACTIVITY_CHILD_STATUS_END               = 4;//结束

    const ACTIVITY_CHILD_TEXT_LIST            = [
        self::ACTIVITY_CHILD_STATUS_SIGN_UP           => '报名中',
        self::ACTIVITY_CHILD_STATUS_NOT_START         => '待举办',
        self::ACTIVITY_CHILD_STATUS_BE_ABOUT_TO_START => '即将开始',
        ///产品-佳莲说文案改成一致 正在进行 改为 进行中
        self::ACTIVITY_CHILD_STATUS_PROGRESS          => '进行中',
        self::ACTIVITY_CHILD_STATUS_END               => '已结束',
    ];
    const ACTIVITY_ZHAOPINHUI_CHILD_TEXT_LIST = [
        self::ACTIVITY_CHILD_STATUS_SIGN_UP           => '报名中',
        self::ACTIVITY_CHILD_STATUS_NOT_START         => '待举办',
        self::ACTIVITY_CHILD_STATUS_BE_ABOUT_TO_START => '即将开始',
        self::ACTIVITY_CHILD_STATUS_PROGRESS          => '进行中',
        self::ACTIVITY_CHILD_STATUS_END               => '已结束',
    ];

    //根据招聘会的类型获取子状态文案
    const ACTIVITY_CHILD_STATUS_TEXT_LIST = [
        self::SERIES_COME_HOME            => self::ACTIVITY_CHILD_TEXT_LIST,
        self::SERIES_OVERSEAS_RECRUITMENT => self::ACTIVITY_CHILD_TEXT_LIST,
        self::SERIES_MORE_ACTIVITY        => self::ACTIVITY_CHILD_TEXT_LIST,
        self::SERIES_BOSHIHOU             => self::ACTIVITY_CHILD_TEXT_LIST,
        self::SERIES_ZHAOPINHUI_OFFLINE   => self::ACTIVITY_ZHAOPINHUI_CHILD_TEXT_LIST,
        self::SERIES_ZHAOPINHUI_ONLINE    => self::ACTIVITY_ZHAOPINHUI_CHILD_TEXT_LIST,
    ];

    //活动报名按钮文案
    const ACTIVITY_BTN_TEXT_LIST = [
        self::ACTIVITY_CHILD_STATUS_SIGN_UP           => '立即报名',
        self::ACTIVITY_CHILD_STATUS_NOT_START         => '待举办',
        self::ACTIVITY_CHILD_STATUS_BE_ABOUT_TO_START => '立即报名',
        self::ACTIVITY_CHILD_STATUS_PROGRESS          => '正在进行',
        self::ACTIVITY_CHILD_STATUS_END               => '已结束',
    ];
    //活动状态对应列表
    const ACTIVITY_CHILD_STATUS_RELATION_LIST = [
        self::ACTIVITY_STATUS_PROGRESS => [
            self::ACTIVITY_CHILD_STATUS_SIGN_UP,
            self::ACTIVITY_CHILD_STATUS_NOT_START,
            self::ACTIVITY_CHILD_STATUS_PROGRESS,
        ],
        self::ACTIVITY_STATUS_END      => [self::ACTIVITY_CHILD_STATUS_END],
    ];

    const IS_OUTSIDE_URL_YES = 1;
    const IS_OUTSIDE_URL_NO  = 2;
    //活动标签:1=付费,2=项目推广,3=内部推广,4=额外推广,9=其他
    const TAGS_FENQI_PAID        = 1;
    const TAGS_PROJECT_PROMOTION = 2;
    const TAGS_INNER_PROMOTION   = 3;
    const TAGS_EXTRA_PROMOTION   = 4;
    const TAGS_OTHER             = 9;

    const TAGS_TEXT_LIST = [
        self::TAGS_FENQI_PAID        => '付费',
        self::TAGS_PROJECT_PROMOTION => '项目推广',
        self::TAGS_INNER_PROMOTION   => '内部推广',
        self::TAGS_EXTRA_PROMOTION   => '额外推广',
        self::TAGS_OTHER             => '其他',
    ];

    //举办类型
    const TO_HOLD_TYPE_ONLINE                  = 1;
    const TO_HOLD_TYPE_OFFLINE                 = 2;
    const TO_HOLD_TYPE_ONLINE_AND_OFFLINE      = 3;
    const TO_HOLD_TYPE_ONLINE_NAME             = '线上';
    const TO_HOLD_TYPE_OFFLINE_NAME            = '线下';
    const TO_HOLD_TYPE_ONLINE_AND_OFFLINE_NAME = '线上+线下';
    const TO_HOLD_TYPE_LIST                    = [
        self::TO_HOLD_TYPE_ONLINE  => self::TO_HOLD_TYPE_ONLINE_NAME,
        self::TO_HOLD_TYPE_OFFLINE => self::TO_HOLD_TYPE_OFFLINE_NAME,
    ];
    const TO_HOLD_TYPE_TEXT                    = [
        self::TO_HOLD_TYPE_ONLINE             => self::TO_HOLD_TYPE_ONLINE_NAME,
        self::TO_HOLD_TYPE_OFFLINE            => self::TO_HOLD_TYPE_OFFLINE_NAME,
        self::TO_HOLD_TYPE_ONLINE_AND_OFFLINE => self::TO_HOLD_TYPE_ONLINE_AND_OFFLINE_NAME,
    ];

    // 0=空 1=表单报名链接 2=其他链接 3=报名表选项ID
    const APPLY_LINK_PERSON_TYPE_NULL        = 0;
    const APPLY_LINK_PERSON_TYPE_FORM        = 1;
    const APPLY_LINK_PERSON_TYPE_OTHER       = 2;
    const APPLY_LINK_PERSON_TYPE_FORM_OPTION = 3;

    const APPLY_LINK_PERSON_TYPE_TEXT_LIST = [
        self::APPLY_LINK_PERSON_TYPE_FORM        => '表单报名链接',
        self::APPLY_LINK_PERSON_TYPE_OTHER       => '其他链接',
        self::APPLY_LINK_PERSON_TYPE_FORM_OPTION => '报名表选项ID',
    ];

    //活动点击量平台
    const CLICK_PLATFORM_PC   = 1;
    const CLICK_PLATFORM_MINI = 2;

    //活动模版
    const TEMPLATE_TYPE_DEFAULT = 1;
    const TEMPLATE_TYPE_GENERAL = 2;

    const TEMPLATE_TYPE_LIST = [
        self::TEMPLATE_TYPE_DEFAULT => '普通模板',
        self::TEMPLATE_TYPE_GENERAL => '通用模板',
    ];

    public function rules()
    {
        return [
            [
                [
                    'add_time',
                    'update_time',
                    'sign_end_date',
                    'activity_start_date',
                    'activity_end_date',
                    'series_type',
                    'type',
                    'introduce',
                    'sign_custom_end_date',
                ],
                'safe',
            ],
            [
                [
                    'status',
                    'series_type',
                    'type',
                    'grounding_status',
                    'sort',
                    'company_id',
                    'activity_status',
                    'is_outside_url',
                ],
                'integer',
            ],
            [
                ['introduce'],
                'string',
            ],
            [
                [
                    'name',
                    'sign_custom_end_date',
                    'detail_url',
                    'sign_up_url',
                    'main_img_file_id',
                    'logo_file_id',
                    'review_img_file_ids',
                    'other_img_one_file_id',
                    'other_img_two_file_id',
                    'other_img_three_file_id',
                    'other_description_one',
                    'other_description_two',
                    'other_description_three',
                ],
                'string',
                'max' => 255,
            ],
        ];
    }

    /**
     * 获取活动详情链接
     */
    public static function getActivityLinkUrl($seriesType, $activityLink)
    {
        $url = self::ACTIVITY_LINK_LIST[$seriesType];
        if (Yii::$app->params['environment'] != 'prod') {
            $urlLocal = 'https://zhaopinhui.' . Yii::$app->params['pcHost'];

            // 去掉www.
            $urlLocal = str_replace('www.', '', $urlLocal);
            $url      = str_replace('https://zhaopinhui.gaoxiaojob.com', $urlLocal, $url);
        }

        return $url . $activityLink . '.html';
    }

    /**
     * 获取人才报名链接
     */
    public static function getPersonApplyLinkUrl($activityId)
    {
        $activityInfo = BaseHwActivity::findOne($activityId);
        // 0=空 1=表单报名链接 2=其他链接 3=报名表选项ID
        if ($activityInfo->apply_link_person_type == BaseHwActivity::APPLY_LINK_PERSON_TYPE_NULL || ($activityInfo->apply_person_time != TimeHelper::ZERO_DATE && strtotime(TimeHelper::dayToEndTime($activityInfo->apply_person_time)) < time())) {
            $link = '';
        } elseif ($activityInfo->apply_link_person_type == BaseHwActivity::APPLY_LINK_PERSON_TYPE_FORM || $activityInfo->apply_link_person_type == BaseHwActivity::APPLY_LINK_PERSON_TYPE_FORM_OPTION) {
            $link = BaseActivityForm::findOne(['id' => $activityInfo->apply_link_person_form_id])->link;
        } else {
            $link = $activityInfo->apply_link_person;
        }

        $linkInfo = UrlHelper::formatLinkInfo($link);

        return [
            'link'     => $linkInfo['newLink'],
            'linkType' => $linkInfo['linkType'],
        ];
    }

    /**
     * 根据场次，更新活动状态、时间
     * @param $id
     * @throws Exception
     * @throws \yii\db\Exception
     *
     * 按该活动下，场次的“举办时间”自动获取对应的“活动时间”展示：
     * 1、所有场次的“举办时间”均为自定义文本时：
     * 取排序最前的场次，显示其“举办时间”录入的文本（完整展示）；
     * 2、所有场次的“举办时间”均为具体时间时：
     * 在所有场次的举办时间合集中，取最早的日期为“活动开始日期（天）”，取最晚的日期为“活动结束日期（天）”，显示时段范围：｛活动开始日期｝～｛活动结束日期｝；
     * 个—
     * 2.1示例：
     * 某论坛活动下，有场次A（举办时间：2024.05.10 10:00）、场次B（举办时间：2024.05.12~2024.05.13），则列表获取显示的“活动日期”为：2024.05.10～2024.05.13。
     * 2.2 日期显示格式为 年.月.日；
     * 2.3若获取的“活动开始日期=活动结束日期”，则显示为时间点：
     * ｛活动开始日期｝
     * 3、若活动下既有自定义时间的场次，又有具体时间的场次时：
     * 按第2点处理，不是示自定义时间文本内容。
     */
    public static function updateActivityBySession($id)
    {
        //根据场次内容，更新活动的字段
        $model = self::findOne($id);
        if (!$model) {
            throw new Exception('活动不存在');
        }
        // 报名截止时间
        $signEndDate = $model->sign_end_date;
        if ($signEndDate == TimeHelper::ZERO_DATE) {
            $signEndDate = '';
        }
        //活动存在则看一下是否是招聘会系列
        if (in_array($model->series_type, BaseHwActivity::ZHAOPINHUI_TYPE)) {
            //招聘会系列
            $sessionInfo = BaseHwActivitySession::findOne([
                'activity_id' => $id,
                'status'      => BaseActiveRecord::STATUS_ACTIVE,
            ]);
            //计算活动状态
            $activityChildStatus = self::getZhaoPinHuiActivityChildStatus($sessionInfo->start_date,
                $sessionInfo->end_date, $sessionInfo->start_time, $sessionInfo->end_time);
            $activityStatus      = self::getActivityStatusByChildStatus($activityChildStatus);
            if ($activityChildStatus == BaseHwActivity::ACTIVITY_CHILD_STATUS_END) {
                $model->sort = 0;
            }
            $model->activity_child_status = $activityChildStatus ?: 0;
            $model->activity_status       = $activityStatus ?: 0;
            $model->activity_start_date   = $sessionInfo->start_date ?: TimeHelper::ZERO_DATE;
            $model->activity_end_date     = $sessionInfo->end_date ?: TimeHelper::ZERO_DATE;
            $model->save();

            return true;
        }

        $sessionList   = BaseHwActivitySession::find()
            ->where([
                'activity_id' => $id,
                'status'      => BaseActiveRecord::STATUS_ACTIVE,
            ])
            ->select([
                'end_date as endDate',
                'start_date as startDate',
            ])
            ->asArray()
            ->all();
        $startDateList = [];
        $endDateList   = [];
        foreach ($sessionList as $session) {
            if ($session['startDate'] && $session['startDate'] != TimeHelper::ZERO_DATE) {
                $startDateList[] = $session['startDate'];
            }
            if ($session['endDate'] && $session['endDate'] != TimeHelper::ZERO_DATE) {
                $endDateList[] = $session['endDate'];
            }
        }

        // 最小的开始时间
        $minStartDate = min($startDateList) ?: '';
        // 最大的结束时间
        $maxEndDate = max($endDateList) ?: '';

        //计算活动状态
        //        if ($model->grounding_status == self::GROUNDING_STATUS_ON) {
        $activityChildStatus = self::getActivityChildStatus($minStartDate, $maxEndDate, $signEndDate);
        $activityStatus      = self::getActivityStatusByChildStatus($activityChildStatus);
        //        }
        if ($activityChildStatus == BaseHwActivity::ACTIVITY_CHILD_STATUS_END) {
            $model->sort = 0;
        }
        $model->activity_child_status = $activityChildStatus ?: 0;
        $model->activity_status       = $activityStatus ?: 0;
        $model->activity_start_date   = $minStartDate ?: TimeHelper::ZERO_DATE;
        $model->activity_end_date     = $maxEndDate ?: TimeHelper::ZERO_DATE;
        $model->save();
    }

    /**
     * 获取活动子状态
     * @param $startDate
     * @param $endDate
     * @param $signEndDate
     * @return int|void
     */
    public static function getActivityChildStatus($startDate, $endDate, $signEndDate)
    {
        if (!$startDate && !$endDate && !$signEndDate) {
            //活动开始日期～结束日期=自定义文本，且 报名截止日期 = 空/自定义文本 时，状态为报名中
            return self::ACTIVITY_CHILD_STATUS_SIGN_UP;
        }

        $currentTime = time();
        //活动开始时间、活动结束时间
        $startTime = $startDate ? strtotime(TimeHelper::dayToBeginTime($startDate)) : '';
        $endTime   = $endDate ? strtotime(TimeHelper::dayToEndTime($endDate)) : '';
        //真实结束时间
        $realEndTime = $endTime ? $endTime + 12 * 3600 : '';
        //截止报名时间
        $signEndTime = $signEndDate ? strtotime(TimeHelper::dayToEndTime($signEndDate)) : '';

        //活动开始日期～结束日期=自定义文本，但有具体的报名截止日期 时
        if (!$startTime && !$realEndTime && $signEndTime) {
            return $currentTime > $signEndTime ? self::ACTIVITY_CHILD_STATUS_NOT_START : self::ACTIVITY_CHILD_STATUS_SIGN_UP;
        }

        //有具体的活动开始日期～结束日期，但 报名截止日期 = 空/自定义文本 时，
        //当 报名截止日期 = 活动结束日期+12h 时
        if (($startTime && $realEndTime && !$signEndTime) || ($signEndTime >= $realEndTime)) {
            return $currentTime > $realEndTime ? self::ACTIVITY_CHILD_STATUS_END : self::ACTIVITY_CHILD_STATUS_SIGN_UP;
        }

        //当 活动开始日期 ≤ 报名截止日期 < 活动结束日期+12h 时，
        if ($startTime <= $signEndTime && $signEndTime < $realEndTime) {
            if ($currentTime < $signEndTime) {
                return self::ACTIVITY_CHILD_STATUS_SIGN_UP;
            }
            if ($currentTime > $signEndTime && $currentTime < $realEndTime) {
                return self::ACTIVITY_CHILD_STATUS_PROGRESS;
            }

            return self::ACTIVITY_CHILD_STATUS_END;
        }

        //当 报名截止日期 < 活动开始日期 时，
        if ($signEndTime < $startTime) {
            if ($currentTime < $signEndTime) {
                return self::ACTIVITY_CHILD_STATUS_SIGN_UP;
            }
            if ($currentTime > $signEndTime && $currentTime < $startTime) {
                return self::ACTIVITY_CHILD_STATUS_NOT_START;
            }
            if ($currentTime > $startTime && $currentTime < $realEndTime) {
                return self::ACTIVITY_CHILD_STATUS_PROGRESS;
            }

            return self::ACTIVITY_CHILD_STATUS_END;
        }
    }

    /**
     * 获取招聘会系列活动子状态
     * 本版本新增的招聘会活动系列的“活动状态”及展示规则如下：
     *
     * 3.1 活动状态：即将开始/待举办/进行中/已结束
     * 3.2 状态说明：
     * （1）即将开始：
     * 活动“开始时间”之前的7*24小时内。
     * （2）待举办：
     * ① “活动时间”为自定义录入；
     * ② 当前时间点距离活动开始时间＞7*24小时。
     * （3）进行中：
     * 当前时间正处于所设定的“活动时间”段内；
     * （4）已结束：
     * 当前时间点已经超过了所设定的“结束时间”。
     * @param $startDate
     * @param $endDate
     * @return int
     */
    public static function getZhaoPinHuiActivityChildStatus($startDate, $endDate, $startTime, $endTime)
    {
        $currentTime = time();
        //活动开始时间、活动结束时间
        if ($startDate != TimeHelper::ZERO_DATE) {
            $startDateTime  = $startTime ? $startDate . ' ' . substr_replace($startTime, ':', 2,
                    0) . ':00' : TimeHelper::dayToBeginTime($startDate);
            $startTimestamp = strtotime($startDateTime);
        } else {
            $startTimestamp = 0;
        }
        if ($endDate != TimeHelper::ZERO_DATE) {
            $endDateTime  = $endTime ? $endDate . ' ' . substr_replace($endTime, ':', 2,
                    0) . ':00' : TimeHelper::dayToEndTime($endDate);
            $endTimestamp = strtotime($endDateTime);
        } else {
            $endTimestamp = 0;
        }
        // 待举办-即将开始-进行中/正在进行-已结束
        // 1、待举办：① “活动时间”为自定义录入；② 当前时间点距离活动开始时间＞7*24小时。
        if ((!$startTimestamp && !$endTimestamp) || ($currentTime < ($startTimestamp - 7 * 24 * 3600))) {
            return self::ACTIVITY_CHILD_STATUS_NOT_START;
        }

        // 2、即将开始：活动“开始时间”之前的7*24小时内
        if ($currentTime > ($startTimestamp - 7 * 24 * 3600) && $currentTime < $startTimestamp) {
            return self::ACTIVITY_CHILD_STATUS_BE_ABOUT_TO_START;
        }
        // 3、进行中： 当前时间正处于所设定的“活动时间”段内；
        if ($currentTime >= $startTimestamp && $currentTime <= $endTimestamp) {
            return self::ACTIVITY_CHILD_STATUS_PROGRESS;
        }
        // 4、已结束：当前时间点已经超过了所设定的“结束时间”。
        if ($currentTime > $endTimestamp) {
            return self::ACTIVITY_CHILD_STATUS_END;
        }

        return self::ACTIVITY_CHILD_STATUS_NOT_START;
    }

    public static function getActivityStatusByChildStatus($childStatus)
    {
        if (!$childStatus) {
            return '';
        }
        if ($childStatus == self::ACTIVITY_CHILD_STATUS_END) {
            return self::ACTIVITY_STATUS_END;
        }

        return self::ACTIVITY_STATUS_PROGRESS;
    }

    /**
     * 获取活动时间
     * @param $id
     *           按该活动下，场次的“举办时间”自动获取对应的“活动时间”展示：
     *           1、所有场次的“举办时间”均为自定义文本时：
     *           取排序最前的场次，显示其“举办时间”录入的文本（完整展示）；
     *           2、所有场次的“举办时间”均为具体时间时：
     *           在所有场次的举办时间合集中，取最早的日期为“活动开始日期（天）”，取最晚的日期为“活动结束日期（天）”，显示时段范围：｛活动开始日期｝～｛活动结束日期｝；
     *           个—
     *           2.1示例：
     *           某论坛活动下，有场次A（举办时间：2024.05.10
     *           10:00）、场次B（举办时间：2024.05.12~2024.05.13），则列表获取显示的“活动日期”为：2024.05.10～2024.05.13。
     *           2.2 日期显示格式为 年.月.日；
     *           2.3若获取的“活动开始日期=活动结束日期”，则显示为时间点：
     *           ｛活动开始日期｝
     *           3、若活动下既有自定义时间的场次，又有具体时间的场次时：
     *           按第2点处理，不是示自定义时间文本内容。
     * @return mixed|string
     */
    public static function getActivityDate($id, $dataType = 2, $isWeek = true)
    {
        $info = self::findOne($id);
        if (in_array($info->series_type, self::ZHAOPINHUI_UN_TYPE)) {
            //有非自定义时间的
            if ($info['activity_start_date'] != TimeHelper::ZERO_DATE || $info['activity_end_date'] != TimeHelper::ZERO_DATE) {
                if ($info['activity_start_date'] == $info['activity_end_date']) {
                    return date('Y.m.d', strtotime($info['activity_start_date']));
                } else {
                    return date('Y.m.d', strtotime($info['activity_start_date'])) . '~' . date('Y.m.d',
                            strtotime($info['activity_end_date']));
                }
            }
            //如果都是自定义时间的
            $firstSession = BaseHwActivitySession::find()
                ->select(['custom_time as customTime'])
                ->where(['activity_id' => $id])
                ->orderBy('sort asc')
                ->asArray()
                ->one();

            return $firstSession['customTime'] ?: '';
        } else {
            $sessionInfo = BaseHwActivitySession::findOne(['activity_id' => $id]);
            if ($sessionInfo->custom_time) {
                return $sessionInfo->custom_time;
            } else {
                return BaseService::formatActivityDateText($dataType, $sessionInfo->start_date, $sessionInfo->end_date,
                    $sessionInfo->start_time, $sessionInfo->end_time, $isWeek);
            }
        }
    }

    /**
     * 设置排序
     * @param $id
     * @param $sort
     * @return bool
     * @throws \yii\db\Exception
     */
    public static function setSort($id, $sort)
    {
        $model       = self::findOne($id);
        $model->sort = $sort;
        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        return true;
    }

    /**
     * 修改上架状态
     * @param $id
     * @return bool
     * @throws Exception
     * @throws \yii\db\Exception
     */
    public static function changeGroundingStatus($id)
    {
        $model         = self::findOne($id);
        $isGroundingOn = $model->grounding_status != self::GROUNDING_STATUS_ON && $model->first_grounding_time == TimeHelper::ZERO_TIME ? 1 : 0;
        if ($isGroundingOn) {
            $model->first_grounding_time = date('Y-m-d H:i:s');
        }
        $model->grounding_status = $model->grounding_status == self::GROUNDING_STATUS_ON ? self::GROUNDING_STATUS_OFF : self::GROUNDING_STATUS_ON;
        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        if (BaseAdminPositionMenu::getAuth('sortAndGrounding') && $isGroundingOn) {
            //发送上架消息
            (new WxWork())->message($model->admin_id,
                '您创建的活动：' . $model->name . '（{' . BaseHwActivity::SERIES_TEXT_LIST[$model->series_type] . '-' . BaseHwActivity::TYPE_TEXT_LIST[$model->type] . '}）已上架。');
        }

        return true;
    }

    /**
     * 删除活动
     * @param $id
     * @return bool
     * @throws \yii\db\Exception
     */
    public static function delActivity($id)
    {
        $model         = self::findOne($id);
        $model->status = self::STATUS_DELETE;
        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        return true;
    }

    public static function getAddressBySeries($id)
    {
        $info   = self::findOne($id);
        $series = $info->series_type;
        //        if ($series != self::SERIES_OVERSEAS_RECRUITMENT && $series != self::SERIES_COME_HOME) {
        //            return '';
        //        }
        $sessionListQuery = BaseHwActivitySession::find()
            ->where([
                'activity_id' => $id,
                'status'      => BaseActiveRecord::STATUS_ACTIVE,
            ])
            ->orderBy('sort desc');
        if ($series == self::SERIES_OVERSEAS_RECRUITMENT || $series == self::SERIES_MORE_ACTIVITY) {
            //“出海引才”活动系列的活动，此位置读取“活动场次”字段展示：
            //即该活动下所有场次的“场次名称”，按场次排序，拼接展示；多个场次，则“,”隔开，超1行…；
            $list = $sessionListQuery->select([
                'name',
            ])
                ->column();
            $text = implode(',', $list);
        } else {
            //“归国活动”活动系列的活动，此位置读取“活动地点”字段展示：
            //即该活动下所有场次的“举办地点”的城市&自定义录入文本，按场次排序，用“,”拼接展示，超1行…；
            return self::getAllAddressBySeries($id);
        }

        return $text;
    }

    public static function getAllAddressBySeries($id)
    {
        $sessionListQuery = BaseHwActivitySession::find()
            ->where([
                'activity_id' => $id,
                'status'      => BaseActiveRecord::STATUS_ACTIVE,
            ]);

        $list        = $sessionListQuery->select([
            'custom_address',
            'id as sessionId',
        ])
            ->orderBy('sort desc')
            ->asArray()
            ->all();
        $addressList = [];
        foreach ($list as $item) {
            if ($item['custom_address']) {
                $addressList[] = $item['custom_address'];
                continue;
            }
            $areaList = BaseHwActivitySessionArea::find()
                ->where([
                    'activity_id' => $id,
                    'session_id'  => $item['sessionId'],
                ])
                ->select(['area_id'])
                ->column();
            foreach ($areaList as $areaId) {
                $addressList[] = BaseArea::getAreaName($areaId);
            }
        }

        $addressList = array_unique($addressList);
        $text        = implode(',', $addressList);

        return $text;
    }

    /**
     * 获取活动系列-》类型数组
     * @return array
     */
    public static function getSeriesAndTypeList()
    {
        $relationList = self::SERIES_TYPE_RELATION_LIST;
        $list         = [];
        foreach ($relationList as $k => $item) {
            $childList = [];
            foreach ($item as $childValue) {
                $childList[] = [
                    'k' => $childValue,
                    'v' => self::TYPE_TEXT_LIST[$childValue],
                ];
            }
            $list[] = [
                // 考虑到前端无法直接这样选择，所以这里的k需要处理一下
                'k'        => $k,
                'v'        => self::SERIES_TEXT_LIST[$k],
                'children' => $childList,
            ];
        }

        return $list;
    }

    public static function getSeriesAdminAndTypeList()
    {
        $relationList = self::SERIES_TYPE_RELATION_LIST;
        $list         = [];
        foreach ($relationList as $k => $item) {
            $childList = [];
            foreach ($item as $childValue) {
                $childList[] = [
                    'k' => $childValue . '',
                    'v' => self::TYPE_TEXT_LIST[$childValue],
                ];
            }
            $list[] = [
                // 考虑到前端无法直接这样选择，所以这里的k需要处理一下
                'k'        => $k . '00',
                'v'        => self::SERIES_TEXT_LIST[$k],
                'children' => $childList,
            ];
        }

        return $list;
    }

    /**
     * @param $id
     * @return mixed|string|void
     * @throws Exception
     */
    public static function getSignEndDate($id)
    {
        //1、录入方式=选择时间录入：
        //直接显示录入的时间点，格式为：{年.月.日}
        //2、录入方式=自定义录入：
        //直接显示录入的文本（超1行…）
        //3、若未填写，则不展示该字段；
        $model = self::find()
            ->where(['id' => $id])
            ->select([
                'sign_end_date',
                'sign_custom_end_date',
            ])
            ->asArray()
            ->one();
        if (!$model) {
            throw new Exception('活动不存在');
        }
        if (!$model['sign_end_date'] && !$model['sign_custom_end_date']) {
            return '';
        }

        return $model['sign_end_date'] != TimeHelper::ZERO_DATE ? date('Y.m.d',
            strtotime($model['sign_end_date'])) : $model['sign_custom_end_date'];
    }

    /**
     * 获取活动时间类型文本
     * @param $id
     * @return string
     */
    public static function getTimeTypeText($id)
    {
        //3、时区文案：当地时间/北京时间
        //在该活动下的所有场次的举办时间合集中，取最早开始的场次的时区文案展示；若无，则不展示；
        $session = BaseHwActivitySession::find()
            ->where([
                'activity_id' => $id,
                'status'      => BaseHwActivitySession::STATUS_ACTIVE,
            ])
            ->andWhere([
                '<>',
                'start_date',
                TimeHelper::ZERO_DATE,
            ])
            ->select([
                'time_type',
                'start_date',
            ])
            ->orderBy('start_date asc')
            ->asArray()
            ->one();

        if (!$session['start_date']) {
            return '';
        }

        return BaseHwActivitySession::TIME_TYPE_TEXT_LIST[$session['time_type']] ?: '';
    }

    /**
     * 获取活动logo
     * @param $id
     * @return array|bool|mixed|string
     */
    public static function getLogo($id)
    {
        //        $logoFileId = self::findOneVal(['id' => $id], 'logo_file_id');
        $info = self::findOne($id);
        if ($info->logo_file_id > 0) {
            return FileHelper::getFullPathById($info->logo_file_id);
        }
        if ($info->company_id > 0) {
            $company = BaseCompany::findOne($info->company_id);
            if ($company->logo_url) {
                return $company->logo_url;
            }
        }

        return \Yii::$app->params['defaultHwActivityLogo'];
    }

    /**
     * 获取表单列表，不分页
     * @param array  $params
     * @param array  $selectField
     * @param string $order
     * @param int    $limit
     * @return array
     */
    public static function getActivityList(
        array  $params = [],
        array  $selectField = [
            'id',
            'name',
        ],
        string $order = 'id desc',
        int    $limit = 10
    ): array {
        $orWhere = BaseActiveRecord::getSearchCondition($params, [
            [
                'keyword',
                'like',
                'name',
            ],
            [
                'keyword',
                'like',
                'id',
            ],
        ], 'or');

        $where = BaseActiveRecord::getSearchCondition($params, [
            [
                'removeIds',
                'not in',
                'id',
            ],
            [
                'seriesTypes',
                'in',
                'series_type',
            ],
        ]);

        return self::find()
            ->select($selectField)
            ->orWhere($orWhere)
            ->andWhere($where)
            ->orderBy($order)
            ->limit($limit)
            ->asArray()
            ->all();
    }

    /**
     * 根据code[$activityLink]获取活动ID
     * @param $activityLink
     * @return false|int|string|null
     */
    public static function getActivityIdByActivityLinkOne($activityLink)
    {
        return self::find()
            ->select('id')
            ->where(['activity_link' => $activityLink])
            ->scalar();
    }

    /**
     * 获取活动分享二维码
     * @param $activityId
     * @return mixed|string|null
     * @throws \Exception
     */
    public static function getMiniShareLinkUrl($activityId)
    {
        if (!$activityId) {
            return '';
        }

        $key  = Cache::MINI_CODE_KEY . ':' . WxMiniApp::QRCODE_PATH_TYPE_ACTIVITY . ':' . $activityId;
        $link = Cache::get($key);

        if (!$link) {
            $link = WxMiniApp::getInstance()
                ->createQrCodeByType(WxMiniApp::QRCODE_PATH_TYPE_ACTIVITY, $activityId);
            if ($link) {
                Cache::set($key, $link);
            }
        }

        return $link;
    }
}