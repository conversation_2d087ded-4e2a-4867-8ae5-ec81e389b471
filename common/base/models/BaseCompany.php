<?php

namespace common\base\models;

use common\base\BaseActiveRecord;
use common\components\MessageException;
use common\helpers\ArrayHelper;
use common\helpers\FileHelper;
use common\helpers\IpHelper;
use common\helpers\MiniHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\libs\BaiduTimeFactor;
use common\libs\Cache;
use common\libs\JobBatchImport;
use common\libs\ToutiaoTimeFactor;
use common\libs\WxMiniApp;
use common\models\Company;
use common\service\meilisearch\company\SearchService;
use common\service\search\CommonSearchApplication;
use common\service\search\CompanyListService;
use queue\ClickJob;
use queue\Producer;
use Yii;
use yii\base\Exception;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\helpers\Url;

class BaseCompany extends Company
{
    const CACHE_TIME = 300;

    const STATUS_ACTIVE = 1;
    // 审核未提交
    const AUDIT_STATUS_NO = 0;
    // 等待初审
    const STATUS_WAIT_FIRST_AUDIT = 9;
    // 等待复审
    const STATUS_WAIT_SECOND_AUDIT = 8;
    // 审核拒绝
    const STATUS_REJECT = -1;

    // 初审拒绝
    const STATUS_FIRST_REJECT = -8;
    // 复审拒绝
    const STATUS_SECOND_REJECT = -9;

    const STATUS_CAN_EDIT_LIST = [
        self::AUDIT_STATUS_NO,
        self::STATUS_FIRST_REJECT,
        self::STATUS_SECOND_REJECT,
        self::STATUS_REJECT,
    ];

    // 已合作单位
    const COOPERATIVE_UNIT_YES = 1;
    // 非合作单位
    const COOPERATIVE_UNIT_NO = 2;

    const RECOMMEND_NUM = 2;

    const COOPERATIVE_UNIT_LIST = [
        self::COOPERATIVE_UNIT_YES => '已合作单位',
        self::COOPERATIVE_UNIT_NO  => '非合作单位',
    ];

    // 自主申请
    const TYPE_SOURCE_APPLY = 1;
    // 运营添加
    const TYPE_SOURCE_ADD = 2;

    const TYPE_SOURCE_LIST = [
        self::TYPE_SOURCE_APPLY => '自主申请',
        self::TYPE_SOURCE_ADD   => '运营添加',
    ];

    const ORDER_BY_DESC = 1;
    const ORDER_BY_ASC  = 2;

    const LIST_PAGE_SIZE = 20;

    // 同步联系方式
    const COMPANY_SYN_CONTACT_YES = 1;

    //公司排序算法占比
    const SORT_COUNT_JOB_APPLY_AMOUNT_RATE = 0.65;    //最近3个月人才投递次数
    const SORT_COUNT_COLLECT_AMOUNT_RATE   = 0.05;    //单位主页累计收藏量
    const SORT_COUNT_JOB_AMOUNT_RATE       = 0.3;    //当前在线职位数

    //联系人是否公开
    const NAME_PUBLIC_YES = 1;
    const NAME_PUBLIC_NO  = 2;
    //联系邮箱是否公开
    const EMAIL_PUBLIC_YES = 1;
    const EMAIL_PUBLIC_NO  = 2;

    //联系电话是否公开
    const MOBILE_PUBLIC_YES = 1;
    const MOBILE_PUBLIC_NO  = 2;

    const NEED_PAGE_INFO_YES = 1;
    const NEED_PAGE_INFO_NO  = 0;

    //投递类型
    const DELIVERY_TYPE_OUTER       = 1;//站外投递
    const DELIVERY_TYPE_INNER       = 2;//站内投递
    const DELIVERY_TYPE_OUTER_INNER = 3;//站内投递&站外投递
    //做投递类型替换
    const DELIVERY_TYPE_REPLACE_OUTER       = 201;//站外投递
    const DELIVERY_TYPE_REPLACE_INNER       = 202;//站内投递
    const DELIVERY_TYPE_REPLACE_OUTER_INNER = 203;//站内投递&站外投递
    const DELIVERY_TYPE                     = [
        //self::DELIVERY_TYPE_INSIDE,
        self::DELIVERY_TYPE_INNER,
        self::DELIVERY_TYPE_OUTER_INNER,
    ];
    const DELIVERY_TYPE_NAME                = [
        //self::DELIVERY_TYPE_INSIDE         => '站外投递',
        self::DELIVERY_TYPE_INNER       => '专业版',
        self::DELIVERY_TYPE_OUTER_INNER => '标准版',
    ];

    //账号性质
    const ACCOUNT_NATURE_UNKNOWN = 0;//不详
    const ACCOUNT_NATURE_AGREE   = 1;//愿开通账号
    const ACCOUNT_NATURE_REFUSE  = 2;//不愿开通账号
    const ACCOUNT_NATURE         = [
        self::ACCOUNT_NATURE_UNKNOWN,
        self::ACCOUNT_NATURE_AGREE,
        self::ACCOUNT_NATURE_REFUSE,
    ];
    const ACCOUNT_NATURE_NAME    = [
        self::ACCOUNT_NATURE_UNKNOWN => '-',
        self::ACCOUNT_NATURE_AGREE   => '愿意开通账号',
        self::ACCOUNT_NATURE_REFUSE  => '不愿意开通账号',
    ];
    const PACKAGE_TYPE_FREE      = 1; //单位套餐类型：免费会员
    const PACKAGE_TYPE_SENIOR    = 2; //单位套餐类型：高级会员
    const PACKAGE_TYPE_OVER      = 3; //单位套餐类型：过期会员
    const PACKAGE_TYPE_TRIAL     = 4; //单位套餐类型：试用会员

    const PACKAGE_TYPE_LIST = [
        self::PACKAGE_TYPE_FREE   => '免费会员',
        self::PACKAGE_TYPE_SENIOR => '高级会员',
        self::PACKAGE_TYPE_OVER   => '过期会员',
        self::PACKAGE_TYPE_TRIAL  => '试用会员',
    ];

    const PC_PACKAGE_TYPE_LIST = [
        self::PACKAGE_TYPE_FREE   => '免费会员',
        self::PACKAGE_TYPE_SENIOR => '高级会员',
        self::PACKAGE_TYPE_OVER   => '会员已过期',
        self::PACKAGE_TYPE_TRIAL  => '试用会员',
    ];

    //是否隐藏
    const IS_HIDE_YES = 1;
    const IS_HIDE_NO  = 2;

    const ADMIN_HIDE_STATUS_WAIT = 0;
    const ADMIN_HIDE_STATUS_YES  = 1;
    const ADMIN_HIDE_STATUS_NO   = 2;

    const HIDE_STATUS_LIST = [
        self::IS_HIDE_YES => '隐藏',
        self::IS_HIDE_NO  => '展示',
    ];

    //单位排序
    const SORT_SENIOR  = 1;
    const SORT_DEFAULT = 0;

    const PACKAGE_TYPE_POINT_LIST = [
        self::PACKAGE_TYPE_SENIOR => 150,
        self::PACKAGE_TYPE_TRIAL  => 100,
        self::PACKAGE_TYPE_FREE   => 50,
        self::PACKAGE_TYPE_OVER   => 50,
    ];
    //是否被小程序调用
    const IS_MINIAPP_YES    = 1;
    const IS_MINIAPP_NO     = 2;
    const IS_MINIAPP_NO_SET = 0;
    //是否被运营手动标记
    const IS_MANUAL_TAG_NONE = 0;
    const IS_MANUAL_TAG_YES  = 1;
    const IS_MANUAL_TAG_NO   = 2;

    //是否海外栏目调用
    const IS_ABROAD_YES    = 1;
    const IS_ABROAD_NO     = 2;
    const IS_ABROAD_NO_SET = 0;

    const FOR_ADMIN_PACKAGE_TYPE_LIST = [
        self::PACKAGE_TYPE_FREE   => '免费会员',
        self::PACKAGE_TYPE_SENIOR => '高级会员',
        self::PACKAGE_TYPE_OVER   => '过期会员',
        self::PACKAGE_TYPE_TRIAL  => '试用会员',
    ];

    const IS_MINIAPP_LIST = [
        self::IS_MINIAPP_YES    => '是',
        self::IS_MINIAPP_NO     => '否',
        self::IS_MINIAPP_NO_SET => '未设置',
    ];

    // 是否海外
    const IS_ABROAD_LIST = [
        self::IS_ABROAD_YES    => '是',
        self::IS_ABROAD_NO     => '否',
        self::IS_ABROAD_NO_SET => '未设置',
    ];

    /**
     * 获取单位投递类型类别
     * @param $delivery_type
     * @return int
     * @throws Exception
     */
    public static function getDeliveryTypeCate($delivery_type)
    {
        //单位端不区分了 全部走203
        return self::DELIVERY_TYPE_REPLACE_OUTER_INNER;
        switch ($delivery_type) {
            case self::DELIVERY_TYPE_OUTER:
                return self::DELIVERY_TYPE_REPLACE_OUTER;
            case self::DELIVERY_TYPE_INNER:
                return self::DELIVERY_TYPE_REPLACE_INNER;
            case self::DELIVERY_TYPE_OUTER_INNER:
                return self::DELIVERY_TYPE_REPLACE_OUTER_INNER;
            default:
                throw new Exception('投递类型出错');
        }
    }

    /**
     * 解析来源
     * @param $type
     * @return int
     * @throws Exception
     */
    public static function setDeliveryTypeCate($type)
    {
        switch ($type) {
            case self::DELIVERY_TYPE_REPLACE_OUTER:
            case self::DELIVERY_TYPE_REPLACE_INNER:
            case self::DELIVERY_TYPE_REPLACE_OUTER_INNER:
                return JobBatchImport::PLATFORM_COMPANY;
            default:
                return JobBatchImport::PLATFORM_ADMIN;
        }
    }

    public static function getCompanyInfo($member_id)
    {
        $companyId = BaseCompanyMemberInfo::findOneVal(['member_id' => $member_id], 'company_id');
        $company   = self::find()
            ->where(['id' => $companyId])
            ->select([
                'id',
                'full_name',
                'contact',
                'telephone',
                'area_id',
                'address',
                'is_pay',
                'nature',
                'type',
                'province_id',
                'city_id',
                'district_id',
                'is_cooperation',
                'delivery_type',
            ])
            ->asArray()
            ->one();
        if (!$company) {
            return false;
        }

        return $company;
    }

    /**
     * 获取推荐的公司列表
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getRecommendList()
    {
        $list = self::find()
            ->where(['status' => self::STATUS_ACTIVE])
            ->select([
                'id',
                'full_name',
                'logo_url',
                'province_id',
                'city_id',
            ])
            ->asArray()
            ->limit(self::RECOMMEND_NUM)
            ->all();

        foreach ($list as $k => &$record) {
            $record['logo_full_url'] = self::getLogoFullUrl($record['logo_url']);
            $record['jobAmount']     = BaseJob::getCompanyJobAmount($record['companyId']);
            $areaName                = BaseArea::getAreaName($record['province_id']) . '-' . BaseArea::getAreaName($record['city_id']);
            $record['areaName']      = StringHelper::subtractString($areaName, '-');
        }

        return $list;
    }

    /**
     * 单位性质
     * @return array
     */
    public static function getNatureList()
    {
        return BaseDictionary::getCompanyNatureList();
    }

    /**
     * 单位类型
     * @return array
     */
    public static function getTypeList()
    {
        return BaseDictionary::getCompanyTypeList();
    }

    /**
     * 单位规模
     * @return array
     */
    public static function getScaleList()
    {
        return BaseDictionary::getCompanyScaleList();
    }

    /**
     * 福利
     * @return array
     */
    public static function getWelfareLabelList()
    {
        return [
            '1' => '测试福利1',
            '2' => '测试福利2',
            '3' => '测试福利3',
        ];
    }

    /**
     * 标签
     * @return array
     */
    //    public static function getTagList()
    //    {
    //        return [
    //            '1' => '测试标签1',
    //            '2' => '测试标签2',
    //            '3' => '测试标签3',
    //        ];
    //    }

    /**
     * 获取所属行业列表.
     */
    public static function getIndustryList(): array
    {
        return [
            '1' => '测试行业1',
            '2' => '测试行业2',
        ];
    }

    /**
     * 获取地区列表.
     */
    public static function getAreaList(): array
    {
        return [
            '1' => '测试地区1',
            '2' => '测试地区2',
        ];
    }

    /**
     * 获取单条信息
     * @param $where
     * @param $select
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function getCompanyOne($where, $select)
    {
        return self::find()
            ->where($where)
            ->select($select)
            ->asArray()
            ->one();
    }

    /**
     * 获取多条信息
     * @param array  $where
     * @param array  $select
     * @param array  $andWhere
     * @param string $limit
     * @param string $offset
     * @param string $orderBy
     * @param string $count
     * @return array|ActiveQuery|\yii\db\ActiveRecord[]
     */
    public static function getCompanyAll(
        array  $where = [],
        array  $select = [],
        array  $andWhere = [],
        string $limit = '',
        string $offset = '',
        string $orderBy = ''
    ) {
        return self::find()
            ->where($where)
            ->andWhere($andWhere)
            ->select($select)
            ->limit($limit)
            ->offset($offset)
            ->orderBy($orderBy)
            ->asArray()
            ->all();
    }

    /**
     * 获取总条数
     * @param array $where
     * @param array $andWhere
     * @return bool|int|string|null
     */
    public static function countAll(
        array $where = [],
        array $andWhere = []
    ) {
        return self::find()
            ->where($where)
            ->andWhere($andWhere)
            ->asArray()
            ->count();
    }

    /**
     * 根据id寻找名称
     * @param $id
     * @return mixed
     * @throws \Exception
     */
    public static function getNameById($id)
    {
        $info = self::find()
            ->where(['id' => $id])
            ->select(['full_name'])
            ->asArray()
            ->one();

        return ArrayHelper::getValue($info, 'full_name');
    }

    public static function search($data)
    {
        $searchModel = self::find()
            ->alias('c')
            ->innerJoin([
                't' => BaseCompanyStatData::tableName(),
            ], 'c.id=t.company_id')
            ->where([
                'status'  => self::STATUS_ACTIVE,
                'is_hide' => self::IS_HIDE_NO,
            ]);

        $searchModel->andFilterWhere([
            'like',
            'c.full_name',
            $data['keyword'],
        ]);//关键词查询

        if ($data['areaId']) {
            // 城市的查询
            if ($data['areaId']) {
                $area = $data['areaId'];
                if (count(explode('_', $area)) > 1) {
                    $areaIds = explode('_', $area);
                } else {
                    $areaIds = [$area];
                }

                $areaIds = BaseArea::getCityIds($areaIds);

                $searchModel->andWhere(['c.city_id' => $areaIds]);
            }
        }

        $searchModel->andFilterWhere(['c.scale' => $data['companyScaleType']]);                       //单位规模查询
        $searchModel->andFilterWhere(['c.industry_id' => $data['industryId']]);            //行业类别查询
        //单位性质筛选
        if (!empty($data['companyNature'])) {
            $companyNatureArr = explode('_', $data['companyNature']);
            $searchModel->andFilterWhere([
                'in',
                'c.nature',
                $companyNatureArr,
            ]);
        }
        //单位类型筛选
        if (!empty($data['companyType'])) {
            $companyTypeArr = explode('_', $data['companyType']);
            $searchModel->andFilterWhere([
                'in',
                'c.type',
                $companyTypeArr,
            ]);
        }

        //单位福利查询
        // if (!empty($data['welfareLabelId'])) {
        //     $welfareLabelArr = explode('_', $data['welfareLabelId']);
        //     foreach ($welfareLabelArr as $k => $v) {
        //         $searchModel->andWhere(new Expression("FIND_IN_SET(:tags_{$v}, welfare_label_ids)",
        //             [":tags_{$v}" => $v]));
        //     }
        // }

        //获取总数量
        $count = $searchModel->count();

        $pageSize = $data['pageSize'] ?: self::LIST_PAGE_SIZE;

        $pages = self::setPage($count, $data['page'], $pageSize);

        $select = [
            'c.id as companyId',
            "c.package_type as companyRole",
            'c.full_name as name',
            'c.logo_url',
            'c.scale',
            'c.nature',
            'c.industry_id',
            'c.member_id',
            'c.city_id as cityId',
            'c.type',
            'c.nature',
            'c.job_last_release_time',
            'c.is_cooperation as isCooperation',
            'c.welfare_label_ids',
            't.resume_view_rate',
        ];

        //子查询参数，获取排序数据
        $beginDate     = date('Y-m-d', strtotime('-3 month'));
        $collectStatus = BaseCompanyCollect::COLLECT_STATUS_YES;
        $jobStatus     = BaseJob::STATUS_ACTIVE;
        //获取排序计算比例
        $jobApplyAmountRate = self::SORT_COUNT_JOB_APPLY_AMOUNT_RATE;
        $collectAmountRate  = self::SORT_COUNT_COLLECT_AMOUNT_RATE;
        $jobAmountRate      = self::SORT_COUNT_JOB_AMOUNT_RATE;

        switch ($data['sort']) {
            case 'resume':
                // 简历查看率
                $sort = "t.resume_view_rate desc,sort desc,t.heat desc";
                break;
            case 'update':
                $sort = 'c.job_last_release_time desc,sort desc';
                break;
            case 'default':
                //推荐排序:单位热度=最近3个月人才投递次数*65%+单位主页累计收藏量*5%+当前在线职位数*30%；
                $sort = "sort desc,t.heat desc";
                break;
            default:
                // 简历查看率
                $sort = "t.resume_view_rate desc,sort desc,t.heat desc";
                break;
        }

        $list = $searchModel->select($select)
            ->orderBy($sort)
            ->asArray()
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->all();
        foreach ($list as $k => &$company) {
            // if ($company['isCooperation'] == self::COOPERATIVE_UNIT_YES) {
            //     //获取单位简历查看率
            //     $company['viewingRate'] = BaseJobApply::statCompanyViewingRate(['company_member_id' => $company['member_id']]);
            // } else {
            //     $company['viewingRate'] = '-';
            // }
            // 这里格式化一下简历查看率
            if ($company['resume_view_rate'] < 0) {
                $company['viewingRate'] = '-';
            } elseif ($company['resume_view_rate'] >= '0.00') {
                $company['viewingRate'] = $company['resume_view_rate'] . '%';
            } else {
                $company['viewingRate'] = '-';
            }

            //获取单位在招职位数量
            $company['jobAmount'] = BaseJob::getCompanyJobAmount($company['companyId']);
            //获取单位登录时间
            $company['lastLoginTime'] = BaseMemberActionLog::getCompanyLastLoginDateText($company['member_id']);
            //获取单位行业
            $company['industry'] = BaseTrade::getIndustryName($company['industry_id']);
            //获取单位规模
            $company['scale'] = BaseDictionary::getCompanyScaleName($company['scale']);
            //获取单位类型
            $company['type'] = BaseDictionary::getCompanyTypeName($company['type']);
            //获取单位性质
            $company['nature'] = BaseDictionary::getCompanyNatureName($company['nature']);

            //获取单位logo
            $company['logoUrl'] = self::getLogoFullUrl($company['logo_url']);

            //判断该单位是否被收藏了
            if (!empty($data['memberId'])) {
                $isCollect = BaseCompanyCollect::checkIsCollect($data['memberId'], $company['companyId']);
                if ($isCollect) {
                    $company['isCollect'] = BaseCompanyCollect::COLLECT_STATUS_YES;
                } else {
                    $company['isCollect'] = BaseCompanyCollect::COLLECT_STATUS_NO;
                }
            } else {
                $company['isCollect'] = BaseCompanyCollect::COLLECT_STATUS_NO;
            }
            $company['areaName'] = BaseArea::getAreaName($company['cityId']);

            //获取单位发布公告数量
            $company['announcementAmount'] = BaseAnnouncement::getCompanyOnLineAnnouncementAmount($company['companyId']);
            //获取单位详情url
            $company['url'] = Url::toRoute([
                'company/detail',
                'id' => $company['companyId'],
            ]);
        }

        return [
            'list'        => $list,
            'pageSize'    => $pageSize,
            'currentPage' => $data['page'],
            'totalNum'    => $count,

        ];
    }

    public static function searchForList($data)
    {
        // 整体迁移到服务层
        $searchService = new CompanyListService();

        //
        return $searchService->run($data, \common\service\search\BaseService::TYPE_PC_COMPANY_LIST);

        if (Yii::$app->params['systemSetting']['closeCompanyIndex']) {
            return [];
        }

        unset($data['memberId']);
        unset($data['/company']);

        self::check();

        if (count($data) == 0) {
            $stringKey = '0';
        } else {
            // 把搜索条件转换成字符串
            $stringKey = ArrayHelper::arrayToStringKey($data);
        }

        $cacheKey = Cache::ALL_COMPANY_LIST_PARAMS_LIST_KEY . '_' . $stringKey;

        $cacheData = Cache::get($cacheKey);

        if ($cacheData && ($data['page'] < 2 || !$data['page'])) {
            // 分页就不去拿缓存了
            return json_decode($cacheData, true);
        }
        self::openDb2();

        $searchModel = self::find()
            ->alias('c')
            ->innerJoin([
                't' => BaseCompanyStatData::tableName(),
            ], 'c.id=t.company_id')
            ->where([
                'status'  => self::STATUS_ACTIVE,
                'is_hide' => self::IS_HIDE_NO,
            ]);

        if ($data['keyword']) {
            $searchModel->andFilterWhere([
                'like',
                'c.full_name',
                $data['keyword'],
            ]);
        }

        if ($data['areaId']) {
            // 城市的查询
            if ($data['areaId']) {
                $area = $data['areaId'];
                if (count(explode('_', $area)) > 1) {
                    $areaIds = explode('_', $area);
                } else {
                    $areaIds = [$area];
                }

                $areaIds = BaseArea::getCityIds($areaIds);

                $searchModel->andWhere(['c.city_id' => $areaIds]);
            }
        }

        $searchModel->andFilterWhere(['c.scale' => $data['companyScaleType']]);                       //单位规模查询
        $searchModel->andFilterWhere(['c.industry_id' => $data['industryId']]);            //行业类别查询
        //单位性质筛选
        if (!empty($data['companyNature'])) {
            $companyNatureArr = explode('_', $data['companyNature']);
            $searchModel->andFilterWhere([
                'in',
                'c.nature',
                $companyNatureArr,
            ]);
        }
        //单位类型筛选
        if (!empty($data['companyType'])) {
            $companyTypeArr = explode('_', $data['companyType']);
            $searchModel->andFilterWhere([
                'in',
                'c.type',
                $companyTypeArr,
            ]);
        }

        //获取总数量
        $count = $searchModel->count();

        $pageSize = $data['pageSize'] ?: self::LIST_PAGE_SIZE;

        $pages = self::setPage($count, $data['page'], $pageSize);

        $select = [
            'c.id as companyId',
            "c.package_type as companyRole",
            'c.full_name as name',
            'c.logo_url',
            'c.scale',
            'c.nature',
            'c.industry_id',
            'c.member_id',
            'c.city_id as cityId',
            'c.type',
            'c.nature',
            'c.job_last_release_time',
            'c.is_cooperation as isCooperation',
            'c.welfare_label_ids',
            't.resume_view_rate',
        ];

        switch ($data['sort']) {
            case 'resume':
                // 简历查看率
                $sort = "t.resume_view_rate desc,sort desc,heat desc";
                break;
            case 'update':
                $sort = 'c.job_last_release_time desc,sort desc';
                break;
            case 'default':
                //推荐排序:单位热度=最近3个月人才投递次数*65%+单位主页累计收藏量*5%+当前在线职位数*30%；
                $sort = "sort desc,heat desc";
                break;
            default:
                // 简历查看率
                $sort = "t.resume_view_rate desc,sort desc,,heat desc";
                break;
        }

        $list = $searchModel->select($select)
            ->orderBy($sort)
            ->asArray()
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->all();
        foreach ($list as $k => &$company) {
            // 这里格式化一下简历查看率
            if ($company['resume_view_rate'] < 0) {
                $company['viewingRate'] = '-';
            } elseif ($company['resume_view_rate'] >= '0.00') {
                $company['viewingRate'] = $company['resume_view_rate'] . '%';
            } else {
                $company['viewingRate'] = '-';
            }

            //获取单位在招职位数量
            $company['jobAmount'] = BaseJob::getCompanyJobAmount($company['companyId']);
            //获取单位登录时间
            $company['lastLoginTime'] = BaseMemberActionLog::getCompanyLastLoginDateText($company['member_id']);
            //获取单位行业
            $company['industry'] = BaseTrade::getIndustryName($company['industry_id']);
            //获取单位规模
            $company['scale'] = BaseDictionary::getCompanyScaleName($company['scale']);
            //获取单位类型
            $company['type'] = BaseDictionary::getCompanyTypeName($company['type']);
            //获取单位性质
            $company['nature'] = BaseDictionary::getCompanyNatureName($company['nature']);

            //获取单位logo
            $company['logoUrl'] = self::getLogoFullUrl($company['logo_url']);

            //判断该单位是否被收藏了
            if (!empty($data['memberId'])) {
                $isCollect = BaseCompanyCollect::checkIsCollect($data['memberId'], $company['companyId']);
                if ($isCollect) {
                    $company['isCollect'] = BaseCompanyCollect::COLLECT_STATUS_YES;
                } else {
                    $company['isCollect'] = BaseCompanyCollect::COLLECT_STATUS_NO;
                }
            } else {
                $company['isCollect'] = BaseCompanyCollect::COLLECT_STATUS_NO;
            }
            $company['areaName'] = BaseArea::getAreaName($company['cityId']);

            //获取单位发布公告数量
            $company['announcementAmount'] = BaseAnnouncement::getCompanyAnnouncementAmount($company['companyId']);
            //获取单位详情url
            $company['url'] = Url::toRoute([
                'company/detail',
                'id' => $company['companyId'],
            ]);
        }

        $data = [
            'list'        => $list,
            'pageSize'    => $pageSize,
            'currentPage' => $data['page'],
            'totalNum'    => $count,

        ];

        Cache::set($cacheKey, json_encode($data), 30 * 60);

        return $data;
    }

    public static function searchKeyword($keyword)
    {
        $service = new SearchService();
        $ids     = $service->setKeyword($keyword)
            ->run();

        return $ids;
    }

    private static function check()
    {
        // 这里主要是对于ip做一个限制,避免恶意刷,一分钟内,同一个ip,只能查询10次,超过就不允许了
        // $ip = $this->ip;

        try {
            $ip = IpHelper::getIp();
        } catch (\Exception $e) {
            $ip = '';
        }

        $paramIpList = \Yii::$app->params['ipWhiteList'];

        // 非正式环境就直接返回true就可以了
        if (Yii::$app->params['environment'] != 'prod') {
            return true;
        }

        if (in_array($ip, $paramIpList)) {
            return true;
        }

        $key       = Cache::ALL_IP_WHITE_LIST_COMPANY_SEARCH_KEY . ':' . $ip;
        $minute    = date('YmdHi');
        $countData = json_decode(Cache::get($key), true);
        // 同一分钟不允许超过10次

        $value = $countData[$minute] + 1;

        if ($value > 30) {
            throw new MessageException('您搜索的太频繁了');
        }
        $countData[$minute] = $value;
        Cache::set($key, json_encode($countData), 3600);

        return true;
    }

    public static function getNewDetail($id, $memberId = '')
    {
        $companyDetail = self::find()
            ->alias('c')
            ->leftJoin(['cc' => BaseCompanyContact::tableName()], 'cc.company_id = c.id')
            ->where(['c.id' => $id])
            ->select([
                'c.id as companyId',
                'c.full_name as companyName',
                'c.scale',
                'c.status',
                'c.industry_id as industryId',
                'c.nature',
                'c.member_id as companyMemberId',
                'c.logo_url as logoUrl',
                'c.id as companyId',
                'c.introduce',
                'c.type',
                'c.website',
                'c.address',
                'c.province_id',
                'c.city_id',
                'c.district_id',
                'c.english_name as englishName',
                'c.industry_id as industryId',
                'c.label_ids',
                'c.is_cooperation as isCooperation',
                'c.head_banner_url as headBannerUrl',
                'c.style_atlas',
                'c.welfare_label_ids',
            ])
            ->asArray()
            ->one();

        if (empty($companyDetail)) {
            return false;
        }

        $contact = BaseCompanyContact::find()
            ->select([
                'name as contact',
                'telephone',
                'fax',
                'name_is_public as nameIsPublic',
                'email_is_public as emailIsPublic',
                'mobile_is_public as mobileIsPublic',
            ])
            ->where(['company_id' => $id])
            ->asArray()
            ->one();

        $companyDetail = array_merge($companyDetail, $contact);

        //判断职位联系方式是否显示
        if ($companyDetail['nameIsPublic'] == self::NAME_PUBLIC_NO) {
            $companyDetail['contact']   = '';
            $companyDetail['email']     = '';
            $companyDetail['telephone'] = '';
        }

        //拼接单位地址
        $companyDetail['address'] = BaseArea::getAreaName($companyDetail['province_id']) . BaseArea::getAreaName($companyDetail['city_id']) . BaseArea::getAreaName($companyDetail['district_id']) . $companyDetail['address'];

        //判断是否是合作单位
        if ($companyDetail['isCooperation'] == self::COOPERATIVE_UNIT_YES) {
            //获取单位简历查看率
            $companyDetail['viewingRate'] = self::statCompanyViewingRate($companyDetail['companyId']);
            //获取简历处理用时
            $companyDetail['handleTime'] = BaseJobApply::jobApplyHandleTime($companyDetail['companyMemberId']);
        } else {
            $companyDetail['viewingRate'] = '-';
            $companyDetail['handleTime']  = '-';
        }

        //获取单位类型
        $companyDetail['companyType'] = BaseDictionary::getCompanyTypeName($companyDetail['type']);
        //单位规模
        $companyDetail['scale'] = BaseDictionary::getCompanyScaleName($companyDetail['scale']);
        //单位行业类型
        $companyDetail['industry'] = BaseTrade::getIndustryName($companyDetail['industryId']);
        //获取单位标签
        $companyDetail['labelArr'] = [];
        if (!empty($companyDetail['label_ids'])) {
            $labelIdsArr = explode(',', $companyDetail['label_ids']);
            foreach ($labelIdsArr as $k => $v) {
                array_push($companyDetail['labelArr'], BaseDictionary::getCompanyLabelName($v));
            }
        }
        //获取单位福利
        $companyDetail['welfareArr'] = [];
        if (!empty($companyDetail['welfare_label_ids'])) {
            $welfareIdsArr = explode(',', $companyDetail['welfare_label_ids']);
            $welfareList   = BaseWelfareLabel::find()
                ->select([
                    'name',
                ])
                ->where([
                    'id'     => $welfareIdsArr,
                    'status' => BaseCompany::STATUS_ACTIVE,
                ])
                ->asArray()
                ->all();

            $companyDetail['welfareArr'] = array_column($welfareList, 'name');
        }

        //融合单位福利、标签
        $companyDetail['fuseArr'] = array_merge($companyDetail['labelArr'], $companyDetail['welfareArr']);

        //获取单位二级院校
        $companyDetail['childUnitList'] = BaseCompanyChildUnit::getList($companyDetail['companyId']);
        if (count($companyDetail['childUnitList']) > 0) {
            $companyDetail['hasChildUnitList'] = true;
        } else {
            $companyDetail['hasChildUnitList'] = false;
        }
        //获取招聘公告
        $jobSearchDate = [
            'companyId' => $companyDetail['companyId'],
        ];

        /**
         * [id] => 29770
         * [title] => 清华大学苏世民书院2022年招聘公告
         * [status] => 1
         * [is_show] => 1
         * [is_delete] => 2
         * [releaseTime] => 2022-07-22 17:37:17
         * [refreshTime] => 07-22
         * [jobAmount] => 1
         * [recruitAmount] => 若干
         * [areaName] => 北京
         * [fiveAreaName] => 北京
         * [allAreaName] => 北京
         * [isAllArea] => 0
         * [url] => /announcement/detail/29770.html
         * [invalid] => 2
         * // 这里有点奇怪的要求
         * 需要找到全部合法公告数量和在线公告数量
         */

        $allAnnouncementCount = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin([
                'ar' => BaseArticle::tableName(),
            ], 'ar.id = a.article_id')
            ->where([
                'company_id'   => $companyDetail['companyId'],
                'ar.is_show'   => BaseArticle::IS_SHOW_YES,
                'ar.is_delete' => BaseArticle::IS_DELETE_NO,
                'ar.status'    => [
                    BaseArticle::STATUS_OFFLINE,
                    BaseArticle::STATUS_ONLINE,
                ],
            ])
            ->count();

        $allOnlineAnnouncementCount = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin([
                'ar' => BaseArticle::tableName(),
            ], 'ar.id = a.article_id')
            ->where([
                'company_id'   => $companyDetail['companyId'],
                'ar.is_show'   => BaseArticle::IS_SHOW_YES,
                'ar.is_delete' => BaseArticle::IS_DELETE_NO,
                'ar.status'    => BaseArticle::STATUS_ONLINE,

            ])
            ->count();

        // 找全部公告列表?

        $announcementListInfo = BaseAnnouncement::getCompanyDetailList($jobSearchDate, self::NEED_PAGE_INFO_YES);

        bb($announcementListInfo);

        bb($companyDetail);
    }

    /**
     * 获取单位详情页面信息
     * @param $id
     * @param $memberId
     * @return array|ActiveRecord|null|bool
     * @throws \Exception
     */
    public static function getDetail($id, $memberId)
    {
        // 先找缓存里面是否有
        $cacheKey  = Cache::ALL_COMPANY_DETAIL_KEY . ':' . $id;
        $cacheData = Cache::get($cacheKey);
        if ($cacheData) {
            $companyDetail = json_decode($cacheData, true);
        } else {
            $companyDetail = self::find()
                ->alias('c')
                ->leftJoin(['cc' => BaseCompanyContact::tableName()], 'cc.company_id = c.id')
                ->where(['c.id' => $id])
                ->select([
                    'c.id as companyId',
                    'c.full_name as companyName',
                    'c.scale',
                    'c.status',
                    'c.industry_id as industryId',
                    'c.nature',
                    'c.member_id as companyMemberId',
                    'c.logo_url as logoUrl',
                    'c.id as companyId',
                    'c.introduce',
                    'c.type',
                    'c.website',
                    'cc.name as contact',
                    'cc.telephone',
                    'cc.fax',
                    'c.address',
                    'c.province_id',
                    'c.city_id',
                    'c.district_id',
                    'c.english_name as englishName',
                    'c.industry_id as industryId',
                    'c.label_ids',
                    'c.is_cooperation as isCooperation',
                    'cc.name_is_public as nameIsPublic',
                    'cc.email_is_public as emailIsPublic',
                    'cc.mobile_is_public as mobileIsPublic',
                    'c.head_banner_url as headBannerUrl',
                    'c.style_atlas',
                    'c.welfare_label_ids',
                    'c.add_time',
                ])
                ->asArray()
                ->one();

            if (empty($companyDetail)) {
                return false;
            }

            //判断职位联系方式是否显示
            if ($companyDetail['nameIsPublic'] == self::NAME_PUBLIC_NO) {
                $companyDetail['contact'] = '';
            }
            if ($companyDetail['emailIsPublic'] == self::EMAIL_PUBLIC_NO) {
                $companyDetail['email'] = '';
            }
            if ($companyDetail['mobileIsPublic'] == self::MOBILE_PUBLIC_NO) {
                $companyDetail['telephone'] = '';
            }
            //拼接单位地址
            $provinceName = BaseArea::getAreaName($companyDetail['province_id']);
            $cityName     = BaseArea::getAreaName($companyDetail['city_id']);
            if ($provinceName == $cityName) {
                $companyDetail['address'] = BaseArea::getAreaName($companyDetail['city_id']) . BaseArea::getAreaName($companyDetail['district_id']) . $companyDetail['address'];
            } else {
                $companyDetail['address'] = BaseArea::getAreaName($companyDetail['province_id']) . BaseArea::getAreaName($companyDetail['city_id']) . BaseArea::getAreaName($companyDetail['district_id']) . $companyDetail['address'];
            }
            //判断是否是合作单位
            if ($companyDetail['isCooperation'] == self::COOPERATIVE_UNIT_YES) {
                //获取单位简历查看率
                $companyDetail['viewingRate'] = BaseJobApply::statCompanyViewingRate(['company_member_id' => $companyDetail['companyMemberId']]);
                //获取简历处理用时
                $companyDetail['handleTime'] = BaseJobApply::jobApplyHandleTime($companyDetail['companyMemberId']);
            } else {
                $companyDetail['viewingRate'] = '-';
                $companyDetail['handleTime']  = '-';
            }

            //获取单位类型
            $companyDetail['companyType'] = BaseDictionary::getCompanyTypeName($companyDetail['type']);
            //单位规模
            $companyDetail['scale'] = BaseDictionary::getCompanyScaleName($companyDetail['scale']);
            //单位行业类型
            $companyDetail['industry'] = BaseTrade::getIndustryName($companyDetail['industryId']);
            //获取单位标签
            $companyDetail['labelArr'] = [];
            if (!empty($companyDetail['label_ids'])) {
                $labelIdsArr = explode(',', $companyDetail['label_ids']);
                foreach ($labelIdsArr as $k => $v) {
                    array_push($companyDetail['labelArr'], BaseDictionary::getCompanyLabelName($v));
                }
            }
            //获取单位福利
            $companyDetail['welfareArr'] = [];
            if (!empty($companyDetail['welfare_label_ids'])) {
                $welfareIdsArr = explode(',', $companyDetail['welfare_label_ids']);
                $welfareList   = BaseWelfareLabel::find()
                    ->select([
                        'name',
                    ])
                    ->where([
                        'id'     => $welfareIdsArr,
                        'status' => BaseCompany::STATUS_ACTIVE,
                    ])
                    ->asArray()
                    ->all();

                $companyDetail['welfareArr'] = array_column($welfareList, 'name');
            }

            //融合单位福利、标签
            $companyDetail['fuseArr'] = array_merge($companyDetail['labelArr'], $companyDetail['welfareArr']);

            //获取单位二级院校
            $companyDetail['childUnitList'] = BaseCompanyChildUnit::getList($companyDetail['companyId']);
            if (count($companyDetail['childUnitList']) > 0) {
                $companyDetail['hasChildUnitList'] = true;
            } else {
                $companyDetail['hasChildUnitList'] = false;
            }

            //获取招聘公告
            $jobSearchData = [
                'companyId' => $companyDetail['companyId'],
            ];
            if (!empty($memberId)) {
                $jobSearchData['memberId'] = $memberId;
            }
            $announcementListInfo = BaseAnnouncement::getCompanyDetailList($jobSearchData, self::NEED_PAGE_INFO_YES);

            $companyDetail['announcementList'] = $announcementListInfo['list'];

            //获取单位公告总数量
            $companyDetail['announcementAmount']      = $announcementListInfo['totalNum'];
            $companyDetail['onlineAnnouncementCount'] = $announcementListInfo['onlineTotalNum'];
            //获取在招职位
            $commonSearchApp = new CommonSearchApplication();

            $jobListInfo = $commonSearchApp->companyJobListSearch($jobSearchData);
            //        $jobListInfo              = BaseJob::getCompanyJobList($jobSearchData);
            $companyDetail['jobList'] = $jobListInfo['list'];
            //获取单位在招职位数量
            $companyDetail['jobAmount']      = $jobListInfo['totalNum'];
            $companyDetail['onlineJobCount'] = $jobListInfo['onlineTotalNum'];
            //获取单位下公告的职能筛选列表
            $companyDetail['announceCityList'] = self::getJobAreaList($companyDetail['companyId']);
            //获取单位下职位的职能筛选列表
            $companyDetail['jobCityList'] = self::getJobAreaList($companyDetail['companyId']);
            //获取单位下公告的职能筛选列表
            $companyDetail['announceJobCategoryList'] = self::getJobCategoryList($companyDetail['companyId'], true);
            //获取单位下职位的职能筛选列表
            $companyDetail['jobCategoryList'] = self::getJobCategoryList($companyDetail['companyId']);
            //获取单位下的薪资筛选列表
            $companyDetail['wageList'] = ArrayHelper::obj2Arr(BaseDictionary::getWageRangeList());
            //获取单位下的需求专业筛选列表
            $companyDetail['majorList'] = self::getJobMajorList($companyDetail['companyId']);
            //获取单位logo
            $companyDetail['logo'] = self::getLogoFullUrl($companyDetail['logoUrl']);
            //获取单位性质
            $companyDetail['nature'] = BaseDictionary::getCompanyNatureName($companyDetail['nature']);
            //获取单位类型
            $companyDetail['type'] = BaseDictionary::getCompanyTypeName($companyDetail['type']);
            //获取单位经纬度
            $companyDetail['lat'] = BaseMemberAddress::findOneVal(['member_id' => $companyDetail['companyMemberId']],
                'lat');
            $companyDetail['lng'] = BaseMemberAddress::findOneVal(['member_id' => $companyDetail['companyMemberId']],
                'lng');
            //单位介绍换行格式
            $companyDetail['introduce'] = StringHelper::changeLineFeed($companyDetail['introduce']);
            //单位背景图
            $companyDetail['headBannerUrl'] = FileHelper::getFullUrl($companyDetail['headBannerUrl']);
            if ($companyDetail['headBannerUrl']) {
                $companyDetail['headBannerUrl'] = $companyDetail['headBannerUrl'] . '?imageView2/1/w/1920/h/370/q/75';
            }

            //单位风采图集
            $styleAtlas     = array_filter(explode(',', $companyDetail['style_atlas']));
            $styleAtlasList = [];

            foreach ($styleAtlas as $key => $item) {
                $file                        = BaseFile::findOne(['id' => $item]);
                $styleAtlasList[$key]['url'] = FileHelper::getFullUrl($file['path'], $file['platform']);
                $styleAtlasList[$key]['id']  = $item;
            }
            $companyDetail['styleAtlasList'] = $styleAtlasList;

            //热招公告
            $announcementHotDate = [
                'companyId' => $companyDetail['companyId'],
                'pageSize'  => 8,
            ];

            $announcementHotList                  = BaseAnnouncement::getCompanyDetailList($announcementHotDate,
                self::NEED_PAGE_INFO_NO);
            $companyDetail['announcementHotList'] = $announcementHotList;

            //最新职位
            $jobHotDate                  = [
                'companyId' => $companyDetail['companyId'],
                'pageSize'  => 9,
            ];
            $jobHotList                  = BaseJob::getCompanyHotJobList($jobHotDate);
            $companyDetail['jobHotList'] = $jobHotList;
            Cache::set($cacheKey, json_encode($companyDetail), self::CACHE_TIME);
        }

        if (!empty($memberId)) {
            $isCollect = BaseCompanyCollect::checkIsCollect($memberId, $companyDetail['companyId']);
            if ($isCollect) {
                $companyDetail['isCollect'] = BaseCompanyCollect::COLLECT_STATUS_YES;
            } else {
                $companyDetail['isCollect'] = BaseCompanyCollect::COLLECT_STATUS_NO;
            }
            $companyDetail['isLogin'] = BaseMember::IS_LOGIN_YES;
            //获取简历信息
            $resumeInfo = BaseResume::findOne(['member_id' => $memberId]);
            foreach ($companyDetail['jobList'] as &$item) {
                if ($item['is_cooperation'] == BaseCompany::COOPERATIVE_UNIT_NO) {
                    $item['userEmail'] = BaseMember::findOneVal(['id' => $memberId], 'email');
                }
                $item['applyStatus'] = BaseJobApplyRecord::checkJobApplyStatus($resumeInfo['id'], $item['jobId']);
            }
        } else {
            $companyDetail['isCollect'] = BaseCompanyCollect::COLLECT_STATUS_NO;
            $companyDetail['isLogin']   = BaseMember::IS_LOGIN_NO;
        }

        return $companyDetail;
    }

    /**
     * 获取单位下的职位地区筛选列表
     * @param $companyId
     * @return array
     */
    public static function getJobAreaList($companyId, $hasAnnouncement = false)
    {
        $query = BaseJob::find()
            ->andWhere(['company_id' => $companyId])
            ->andWhere(['is_show' => BaseJob::IS_SHOW_YES])
            ->andWhere([
                'in',
                'status',
                [
                    BaseJob::STATUS_OFFLINE,
                    BaseJob::STATUS_ONLINE,
                ],
            ]);
        //如果需要查有公告的，需要判断不为空
        if ($hasAnnouncement) {
            $query->andWhere([
                '<>',
                'announcement_id',
                0,
            ]);
        }
        $jobCityList = $query->select('city_id')
            ->groupBy('city_id')
            ->asArray()
            ->all();
        $cityList    = [];
        foreach ($jobCityList as $k => $job) {
            if (empty($job['city_id'])) {
                continue;
            }
            $cityList[$k]['k'] = $job['city_id'];
            $cityList[$k]['v'] = BaseArea::getAreaName($job['city_id']);
        }

        if (PLATFORM == 'MINI') {
            //头部插入一个全部
            array_unshift($cityList, [
                'k' => '',
                'v' => '全部',
            ]);
        }

        return $cityList;
    }

    public static function getJobMajorList($companyId, $hasAnnouncement = false)
    {
        $query = BaseJob::find()
            ->alias('j')
            ->select('m.id as k,m.name as v')
            ->innerJoin(['r' => BaseJobMajorRelation::tableName()], 'j.id=r.job_id')
            ->innerJoin(['m' => BaseMajor::tableName()], 'r.major_id=m.id')
            ->andWhere([
                'j.company_id' => $companyId,
                'm.level'      => 2,
            ])
            ->andWhere([
                'in',
                'j.status',
                [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->andWhere(['j.is_show' => BaseJob::IS_SHOW_YES])
            ->andWhere(['m.status' => BaseMajor::STATUS_ACTIVE]);
        //如果需要查有公告的，需要判断不为空
        if ($hasAnnouncement) {
            $query->andWhere([
                '<>',
                'j.announcement_id',
                0,
            ]);

            $query->leftJoin(['an' => BaseAnnouncement::tableName()], 'r.announcement_id=an.id')
                ->leftJoin(['ar' => BaseArticle::tableName()], 'an.article_id = ar.id')
                ->andWhere(['ar.is_delete' => BaseArticle::IS_DELETE_NO])
                ->andWhere(['ar.is_show' => BaseArticle::IS_SHOW_YES]);
        }

        $majorList = $query->asArray()
            ->groupBy('m.id')
            ->all();

        if (PLATFORM == 'MINI') {
            //头部插入一个全部
            array_unshift($majorList, [
                'k' => '',
                'v' => '全部',
            ]);
        }

        return $majorList;
    }

    /**
     * 获取单位下职位的专业筛选列表
     * @param $companyId
     * @return array
     */
    public static function getJobMajorListOld($companyId, $hasAnnouncement = false)
    {
        $query = BaseJob::find()
            ->andWhere(['company_id' => $companyId])
            ->andWhere([
                'in',
                'status',
                [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->andWhere(['is_show' => BaseJob::IS_SHOW_YES])
            ->select('major_id');
        //如果需要查有公告的，需要判断不为空
        if ($hasAnnouncement) {
            $query->andWhere([
                '<>',
                'announcement_id',
                0,
            ]);
        }
        $jobList = $query->asArray()
            ->all();

        $majorIdList = [];
        $majorList   = [];
        foreach ($jobList as $k => $job) {
            if (empty($job['major_id'])) {
                continue;
            }
            $majorIds = explode(',', $job['major_id']);
            if (!empty($majorIds)) {
                foreach ($majorIds as $majorId) {
                    //专业必须是状态=1
                    // if ((BaseMajor::find()
                    //         ->where([
                    //             'id'     => $majorId,
                    //             'status' => 1,
                    //         ])
                    //         ->count()) == 0) {
                    //     continue;
                    // }
                    array_push($majorIdList, $majorId);
                }
            }
        }

        $majorIdList = array_unique($majorIdList);

        foreach ($majorIdList as $k => $majorId) {
            $item = [
                'k' => $majorId,
                'v' => BaseMajor::getMajorName($majorId),
            ];
            array_push($majorList, $item);
        }
        if (PLATFORM == 'MINI') {
            //头部插入一个全部
            array_unshift($majorList, [
                'k' => '',
                'v' => '全部',
            ]);
        }

        return $majorList;
    }

    /**
     * 获取单位下的职位职能筛选列表
     * @param $companyId
     * @param $announcementId
     * @return array
     * @throws \Exception
     */
    public static function getJobCategoryList($companyId, $hasAnnouncement = false)
    {
        $query = BaseJob::find()
            ->andWhere([
                'in',
                'status',
                [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->andWhere(['is_show' => BaseJob::IS_SHOW_YES])
            ->andWhere(['company_id' => $companyId]);
        //如果需要查有公告的，需要判断不为空
        if ($hasAnnouncement) {
            $query->andWhere([
                '<>',
                'announcement_id',
                0,
            ]);
        }
        $jobCategoryList = $query->select('job_category_id')
            ->groupBy('job_category_id')
            ->asArray()
            ->all();

        $categoryList = [];
        foreach ($jobCategoryList as $k => $job) {
            if (empty($job['job_category_id'])) {
                continue;
            }
            $categoryList[$k]['k'] = $job['job_category_id'];
            $categoryList[$k]['v'] = BaseCategoryJob::getName($job['job_category_id']);
        }
        if (PLATFORM == 'MINI') {
            //头部插入一个全部
            array_unshift($categoryList, [
                'k' => '',
                'v' => '全部',
            ]);
        }

        return $categoryList;
    }

    /**
     * 检查名字唯一性
     * @param $companyId
     * @param $fullName
     */
    public static function checkNameOnly($companyId, $fullName)
    {
        // 首先找这个名字并且是合法的企业
        $count = self::find()
            ->where([
                'full_name' => $fullName,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->andWhere([
                '<>',
                'id',
                $companyId,
            ])
            ->count();

        return $count > 0;
    }

    /**
     * 拼接单位logo，如果为空，设置默认的
     * @param $avatar
     * @return string
     */
    public static function getLogoFullUrl($avatar): string
    {
        if (!empty($avatar)) {
            return FileHelper::getFullUrl($avatar);
        } else {
            return \Yii::$app->params['defaultCompanyLogo'];
        }
    }

    public static function statCompanyViewingRateOld($companyId)
    {
        //获取单位简历查看率
        $isCheckAccount = 0;
        $jobApplyQuery  = BaseJobApply::find()
            ->select([
                'status',
                'is_check',
                'add_time',
                'id',
                'job_id',
            ])
            ->where(['company_id' => $companyId]);
        $allAccount     = $jobApplyQuery->count();

        if ($allAccount == 0) {
            // 压根没简历,何来查看率一说
            return -1;
        }

        $jobApplyList = $jobApplyQuery->asArray()
            ->all();
        if ($allAccount > 0) {
            foreach ($jobApplyList as $list) {
                if ($list['is_check'] == 1) {
                    $isCheckAccount++;
                }
            }

            $viewingRate = round($isCheckAccount / $allAccount * 100, 2);
        }

        return $viewingRate;
    }

    public static function statCompanyViewingRate($companyId)
    {
        //获取单位简历查看率
        $resumeViewRate = BaseCompanyStatData::findOneVal(['company_id' => $companyId], 'resume_view_rate');
        if ($resumeViewRate < 0) {
            return '-';
        }
        if ($resumeViewRate > 0) {
            return $resumeViewRate . '%';
        }

        return '0%';
    }

    /**
     * 获取单位热度
     * 单位热度=1.近90天人才投递次数*50%+2.单位活跃度得分*30% +3.简历査看率*100*10% +4.指定单位类型得分*10%
     */
    public static function getHeat($companyId)
    {
        $company = BaseCompany::find()
            ->alias('c')
            ->select([
                'c.member_id',
                'c.id',
                't.resume_view_rate',
                'c.type',
            ])
            ->innerJoin([
                't' => BaseCompanyStatData::tableName(),
            ], 'c.id=t.company_id')
            ->where(['c.id' => $companyId])
            ->asArray()
            ->one();
        $heat    = 0;

        // 1.最近3个月人才投递次数
        $applyCount = BaseJobApplyRecord::find()
            ->where(['company_id' => $companyId])
            ->andWhere([
                '>=',
                'add_time',
                date('Y-m-d', time() - (24 * 60 * 60 * 90)),
            ])
            ->count();
        $heat       += $applyCount * 0.5;

        // 2.单位最后一次登陆时间
        $lastLoginInfo = BaseMember::find()
            ->where(['id' => $company['member_id']])
            ->select(['last_active_time'])
            ->asArray()
            ->one();
        //计算相差的天数
        $differDays = TimeHelper::reduceDates(date('Y-m-d H:i:s'), $lastLoginInfo['last_active_time']);
        // 小到大排序，否则会出错
        $heatValues = [
            15 => 100,
            30 => 70,
            90 => 20,
        ];
        // 遍历数组，找到适合的热度
        foreach ($heatValues as $maxDays => $heatValue) {
            if ($differDays <= $maxDays) {
                $heat += $heatValue * 0.3;
                break; // 找到符合条件的值后退出循环
            }
        }

        // 3.简历查看率
        if ($company['resume_view_rate'] < 0) {
            $company['viewingRate'] = 0;
        } elseif ($company['resume_view_rate'] >= '0.00') {
            $company['viewingRate'] = $company['resume_view_rate'];
        } else {
            $company['viewingRate'] = 0;
        }
        $heat += $company['viewingRate'] * 0.1;

        // 4.指定单位类型得分
        $companyTypeHeat = [
            '100' => [
                1,
                2,
                16,
                17,
                18,
            ],
            '70'  => [
                3,
                4,
                19,
                21,
            ],
            '50'  => [13],
            '20'  => [
                7,
                8,
                9,
                10,
                11,
                14,
            ],
        ];
        foreach ($companyTypeHeat as $baseScore => $typeMap) {
            if (in_array($company['type'], $typeMap)) {
                $heat += $baseScore * 0.1;
                break;
            }
        }

        return intval($heat * 1000);
    }

    /**
     * 兼容站内站外
     * @param $id
     */
    public static function getJobApplyCount($id)
    {
        $onSiteCount = BaseJobApply::find()
            ->where(['company_id' => $id])
            ->count();

        $offSiteCount = BaseOffSiteJobApply::find()
            ->innerJoin(['job' => BaseJob::tableName()], 'job.id = off_site_job_apply.job_id')
            ->where(['company_id' => $id])
            ->count();

        return $onSiteCount + $offSiteCount;
    }

    /**
     * 获取单位详情url
     * @param $id
     * @return string
     */
    public static function getDetailUrl($id)
    {
        return Url::toRoute([
            'company/detail',
            'id' => $id,
        ]);
    }

    /**
     * 单位对应用户 1对1
     * @return ActiveQuery
     */
    public function getMember()
    {
        return $this->hasOne(BaseMember::class, ['id' => 'member_id'])
            ->select([
                "id",
                "update_time",
                "status",
                "type",
                "username",
                "email",
                "mobile_code",
                "mobile",
                "avatar",
                "last_login_time",
                "last_login_ip",
                "email_register_status",
                "source_type",
            ]);
    }

    public static function click($id)
    {
        if (!$_COOKIE[\Yii::$app->params['userCookiesKey']]) {
            // 过滤爬虫
            return false;
        }
        $data = [
            'type'        => ClickJob::TYPE_COMPANY,
            'mainId'      => $id,
            'ip'          => IpHelper::getIpInt(),
            'source'      => BaseMemberLoginForm::getSourceType(),
            'useragent'   => Yii::$app->request->headers['user-agent'] ?? '',
            'userCookies' => $_COOKIE[Yii::$app->params['userCookiesKey']] ?? '',
            'memberId'    => Yii::$app->user->id ?? 0,
        ];
        Producer::click($data);
    }

    /**
     * @param $id
     *           剩余简历下载点数    查看简历数（去重，查看同一用户简历记为一次）    收藏人才数    简历分享次数    邀约投递数    简历扣点下载数
     */
    public static function resumeLibraryData($id)
    {
        $package = BaseCompanyPackageConfig::findOne([
            'company_id' => $id,
            'status'     => 1,
        ]);

        // 查看简历数
        $resumeView = BaseJobApply::find()
            ->where([
                'company_id' => $id,
                'is_check'   => BaseJobApply::IS_CHECK_YES,
            ])
            ->groupBy('resume_id')
            ->count();

        $data['remainResumeDownloadPoint'] = $package->resume_download_amount ?? 0;
        $data['resumeView']                = $resumeView;
        $data['resumeCollect']             = BaseResumeLibraryCollect::find()
            ->where(['company_id' => $id])
            ->count();
        $data['resumeShare']               = BaseResumeShare::find()
            ->where(['company_id' => $id])
            ->count();
        $data['resumeInvite']              = BaseResumeLibraryInviteLog::find()
            ->where(['company_id' => $id])
            ->count();

        $data['consumeResumeDownloadPoint'] = BaseCompanyPackageChangeLog::find()
            ->where([
                'company_id'  => $id,
                'handle_type' => BaseCompanyPackageChangeLog::HANDLE_TYPE_RESUME_DOWNLOAD,
            ])
            ->sum('change_amount') ?: '0';

        return $data;

        // 收藏人才数

        // 简历分享次数

        // 邀约投递数

        // 简历扣点下载数

    }

    /**
     * 预留方法处理单位的排序字段，目前只有高级会员排第一，其余会员都是默认0
     * @param $companyId
     */
    public static function updateSort($companyId)
    {
        $company = self::findOne(['id' => $companyId]);

        $newSort = self::PACKAGE_TYPE_POINT_LIST[$company->package_type] ?: 0;

        if ($newSort == $company->sort) {
            return true;
        }

        $company->sort = $newSort;

        if (!$company->save()) {
            throw new Exception($company->getFirstErrorsMessage());
        }

        // 顺便去更新这个单位下面全部职位的排序
        BaseJob::updateCompanyJobSort($companyId);
    }

    public static function getDetailUpdateTime($companyId)
    {
        // 找到所有上线下线和非隐藏的职位
        $job = BaseJob::find()
            ->select('refresh_time')
            ->where([
                'company_id' => $companyId,
                'status'     => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->orderBy('refresh_time desc')
            ->limit(1)
            ->asArray()
            ->one();

        return $job['refresh_time'];
    }

    public static function getLabelNameList($id)
    {
        $company = self::findOne($id);
        if (!$company) {
            return [];
        }
        $label_ids         = $company->label_ids;
        $welfare_label_ids = $company->welfare_label_ids;

        $labelArr = [];
        if (!empty($label_ids)) {
            $labelIdsArr = explode(',', $label_ids);
            foreach ($labelIdsArr as $k => $v) {
                array_push($labelArr, BaseDictionary::getCompanyLabelName($v));
            }
        }
        //获取单位福利
        $welfareArr = [];
        if (!empty($welfare_label_ids)) {
            $welfareIdsArr = explode(',', $welfare_label_ids);
            $welfareList   = BaseWelfareLabel::find()
                ->select([
                    'name',
                ])
                ->where([
                    'id'     => $welfareIdsArr,
                    'status' => BaseCompany::STATUS_ACTIVE,
                ])
                ->asArray()
                ->all();

            $welfareArr = array_column($welfareList, 'name');
        }

        //融合单位福利、标签
        $fuseArr = array_merge($labelArr, $welfareArr);

        return $fuseArr;
    }

    /**
     * @param $id
     * 获取单位logo
     */
    public static function getLogo($id)
    {
        return self::findOneVal([
            'id' => $id,
        ], 'logo_url') ?: Yii::$app->params['defaultCompanyLogo'];
    }

    /**
     * 获取单位投递数据
     * @param $companyId
     * @return array
     */
    public static function getCompanyApply($companyId)
    {
        //        //网址投递次数
        //        $linkApplyAmount = BaseJobApplyRecord::find()
        //            ->andWhere([
        //                'company_id'   => $companyId,
        //                'delivery_way' => BaseJobApplyRecord::DELIVERY_WAY_LINK,
        //            ])
        //            ->count();
        //        //硕士网址投递次数
        //        $linkMasterApplyAmount = BaseJobApplyRecord::find()
        //            ->alias('jar')
        //            ->andWhere([
        //                'jar.company_id'   => $companyId,
        //                'jar.delivery_way' => BaseJobApplyRecord::DELIVERY_WAY_LINK,
        //            ])
        //            ->innerJoin(['r' => BaseResume::tableName()], 'r.id = jar.resume_id')
        //            ->andWhere(['r.top_education_code' => BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE])
        //            ->count();
        //        //博士网址投递次数
        //        $linkDoctorApplyAmount = BaseJobApplyRecord::find()
        //            ->alias('jar')
        //            ->andWhere([
        //                'jar.company_id'   => $companyId,
        //                'jar.delivery_way' => BaseJobApplyRecord::DELIVERY_WAY_LINK,
        //            ])
        //            ->innerJoin(['r' => BaseResume::tableName()], 'r.id = jar.resume_id')
        //            ->andWhere(['r.top_education_code' => BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE])
        //            ->count();
        //        //邮件/平台投递次数
        //        $emailPlatApplyAmount = BaseJobApplyRecord::find()
        //            ->andWhere([
        //                'company_id'   => $companyId,
        //                'delivery_way' => [
        //                    BaseJobApplyRecord::DELIVERY_WAY_PLATFORM,
        //                    BaseJobApplyRecord::DELIVERY_WAY_EMAIL,
        //                ],
        //            ])
        //            ->count();
        //        //硕士邮件/平台投递次数
        //        $emailPlatMasterApplyAmount = BaseJobApplyRecord::find()
        //            ->alias('jar')
        //            ->andWhere([
        //                'jar.company_id'   => $companyId,
        //                'jar.delivery_way' => [
        //                    BaseJobApplyRecord::DELIVERY_WAY_PLATFORM,
        //                    BaseJobApplyRecord::DELIVERY_WAY_EMAIL,
        //                ],
        //            ])
        //            ->innerJoin(['r' => BaseResume::tableName()], 'r.id = jar.resume_id')
        //            ->andWhere(['r.top_education_code' => BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE])
        //            ->count();
        //        //博士邮件/平台投递次数
        //        $emailPlatDoctorApplyAmount = BaseJobApplyRecord::find()
        //            ->alias('jar')
        //            ->andWhere([
        //                'jar.company_id'   => $companyId,
        //                'jar.delivery_way' => [
        //                    BaseJobApplyRecord::DELIVERY_WAY_PLATFORM,
        //                    BaseJobApplyRecord::DELIVERY_WAY_EMAIL,
        //                ],
        //            ])
        //            ->innerJoin(['r' => BaseResume::tableName()], 'r.id = jar.resume_id')
        //            ->andWhere(['r.top_education_code' => BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE])
        //            ->count();
        //        // 硕士投递次数
        //        $masterApplyAmount = JobApplyRecord::find()
        //            ->where(['company_id' => $companyId])
        //            ->innerJoin('resume', 'resume.id = job_apply_record.resume_id')
        //            ->andWhere(['resume.top_education_code' => BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE])
        //            ->count();
        //
        //        // 博士投递次数
        //        $doctorApplyAmount = JobApplyRecord::find()
        //            ->where(['company_id' => $companyId])
        //            ->innerJoin('resume', 'resume.id = job_apply_record.resume_id')
        //            ->andWhere(['resume.top_education_code' => BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE])
        //            ->count();
        //
        //        //投递总量
        //        $applyAmount = BaseCompany::getJobApplyCount($companyId);
        $data = BaseJobApplyRecord::find()
            ->alias('jar')
            ->select([
                'count(jar.id) as applyAmount',
                'sum(IF(r.top_education_code=3,1,0)) as masterApplyAmount',
                'sum(IF(r.top_education_code=4,1,0)) as doctorApplyAmount',
                'sum(IF(jar.delivery_way=3,1,0)) as linkApplyAmount',
                'sum(IF(jar.delivery_way=3 && r.top_education_code=3,1,0)) as linkMasterApplyAmount',
                'sum(IF(jar.delivery_way=3 && r.top_education_code=4,1,0)) as linkDoctorApplyAmount',
                'sum(IF(jar.delivery_way=1 || jar.delivery_way=2,1,0)) as emailPlatApplyAmount',
                'sum(IF((jar.delivery_way=1 || jar.delivery_way=2) && r.top_education_code=3,1,0)) as emailPlatMasterApplyAmount',
                'sum(IF((jar.delivery_way=1 || jar.delivery_way=2) && r.top_education_code=4,1,0)) as emailPlatDoctorApplyAmount',
            ])
            ->innerJoin(['r' => BaseResume::tableName()], 'r.id = jar.resume_id')
            ->andWhere([
                'jar.company_id'   => $companyId,
                'jar.delivery_way' => [
                    BaseJobApplyRecord::DELIVERY_WAY_PLATFORM,
                    BaseJobApplyRecord::DELIVERY_WAY_EMAIL,
                    BaseJobApplyRecord::DELIVERY_WAY_LINK,
                ],
            ])
            ->asArray()
            ->one();

        return [
            'applyAmount'                => $data['applyAmount'] ?: 0,
            'masterApplyAmount'          => $data['masterApplyAmount'] ?: 0,
            'doctorApplyAmount'          => $data['doctorApplyAmount'] ?: 0,
            'emailPlatApplyAmount'       => $data['emailPlatApplyAmount'] ?: 0,
            'emailPlatMasterApplyAmount' => $data['emailPlatMasterApplyAmount'] ?: 0,
            'emailPlatDoctorApplyAmount' => $data['emailPlatDoctorApplyAmount'] ?: 0,
            'linkApplyAmount'            => $data['linkApplyAmount'] ?: 0,
            'linkMasterApplyAmount'      => $data['linkMasterApplyAmount'] ?: 0,
            'linkDoctorApplyAmount'      => $data['linkDoctorApplyAmount'] ?: 0,
        ];
    }

    /**
     * 获取小程序搜索单位列表
     * @return array
     * @throws \Exception
     */
    // public static function searchForMiniAppList($keywords): array
    // {
    //     $threeMonth = date('Y-m-d H:i:s', strtotime('-90 day'));
    //     $select     = [
    //         'c.id',
    //         'c.full_name',
    //         'c.city_id',
    //         'c.type',
    //         'c.nature',
    //         'c.logo_url',
    //         'count(jar.id) as applyAmount',
    //     ];
    //
    //     $query = BaseCompany::find()
    //         ->alias('c')
    //         ->leftJoin(['j' => BaseJob::tableName()], 'j.company_id = c.id')
    //         ->leftJoin(['jar' => BaseJobApplyRecord::tableName()], 'jar.company_id = c.id')
    //         ->select($select)
    //         ->andWhere([
    //             'c.status' => self::STATUS_ACTIVE,
    //         ]);
    //
    //     $query->andFilterCompare('jar.add_time', $threeMonth, '>');
    //     $query->andFilterCompare('c.type', $keywords['type']);
    //     $query->andFilterCompare('c.nature', $keywords['nature']);
    //     $query->andFilterCompare('c.full_name', $keywords['fullName'], 'like');
    //
    //     $count = $query->count();
    //
    //     $pageSize = $keywords['pageSize'] ?: \Yii::$app->params['jobListDefaultPageSize'];
    //
    //     $pages = self::setPage($count, $keywords['page'], $pageSize);
    //
    //     $list = $query->groupBy('c.id')
    //         ->orderBy('c.sort desc,applyAmount desc,c.id desc')
    //         ->offset($pages['offset'])
    //         ->limit($pages['limit'])
    //         ->asArray()
    //         ->all();
    //
    //     foreach ($list as &$item) {
    //         $item['natureTxt'] = BaseDictionary::getCompanyNatureName($item['nature']);
    //         $item['typeTxt']   = BaseDictionary::getCompanyTypeName($item['type']);
    //         $item['logo']      = self::getLogoFullUrl($item['logoUrl']);
    //         $item['city']      = BaseArea::getAreaName($item['city_id']);
    //     }
    //
    //     return $list;
    // }
    /**
     * 小程序单位列表
     */
    public static function searchForMiniAppList($data): array
    {
        unset($data['memberId']);
        unset($data['/company']);

        self::check();

        if (count($data) == 0) {
            $stringKey = '0';
        } else {
            // 把搜索条件转换成字符串
            $stringKey = ArrayHelper::arrayToStringKey($data);
        }

        $cacheKey = Cache::MINI_COMPANY_LIST_PARAMS_LIST_KEY . '_' . $stringKey;

        $cacheData = Cache::get($cacheKey);

        if ($cacheData && ($data['page'] < 2 || !$data['page'])) {
            // 分页就不去拿缓存了
            return json_decode($cacheData, true);
        }

        $searchModel = self::find()
            ->alias('c')
            ->innerJoin([
                't' => BaseCompanyStatData::tableName(),
            ], 'c.id=t.company_id')
            ->where([
                'c.status' => self::STATUS_ACTIVE,
                'is_hide'  => self::IS_HIDE_NO,
            ]);

        if ($data['keyword']) {
            $searchModel->andFilterWhere([
                'like',
                'c.full_name',
                $data['keyword'],
            ]);//关键词查询
        }

        // 城市的查询
        if ($data['areaId']) {
            $area = $data['areaId'];
            if (count(explode(',', $area)) > 1) {
                $areaIds = explode(',', $area);
            } else {
                $areaIds = [$area];
            }

            $areaIds = BaseArea::getCityIds($areaIds);

            $searchModel->andWhere(['c.city_id' => $areaIds]);
        }

        $searchModel->andFilterWhere(['c.scale' => $data['companyScaleType']]);                       //单位规模查询
        $searchModel->andFilterWhere(['c.industry_id' => $data['industryId']]);            //行业类别查询
        $searchModel->andFilterWhere(['c.is_miniapp' => 1]);            //行业类别查询
        //单位性质筛选
        if (!empty($data['companyNature'])) {
            // 这里要兼容一下,有可能是逗号,先把逗号换成_
            $data['companyNature'] = str_replace(',', '_', $data['companyNature']);
            $companyNatureArr      = explode('_', $data['companyNature']);
            $searchModel->andFilterWhere([
                'in',
                'c.nature',
                $companyNatureArr,
            ]);
        }
        //单位类型筛选
        if (!empty($data['companyType'])) {
            $data['companyType'] = str_replace(',', '_', $data['companyType']);
            $companyTypeArr      = explode('_', $data['companyType']);
            $searchModel->andFilterWhere([
                'in',
                'c.type',
                $companyTypeArr,
            ]);
        }

        $page     = $data['page'] ?: 1;
        $pageSize = 20;
        $offset   = ($page - 1) * $pageSize;

        $select = [
            'c.id as companyId',
            "c.package_type as companyRole",
            'c.full_name as name',
            'c.logo_url',
            'c.scale',
            'c.nature',
            'c.industry_id',
            'c.member_id',
            'c.city_id as cityId',
            'c.type',
            'c.nature',
            'c.job_last_release_time',
            'c.is_cooperation as isCooperation',
            'c.welfare_label_ids',
            't.resume_view_rate',
        ];

        $sort = "c.sort desc,heat desc";

        $list = $searchModel->select($select)
            ->orderBy($sort)
            ->asArray()
            ->offset($offset)
            ->limit($pageSize)
            ->all();
        foreach ($list as $k => &$company) {
            if ($company['resume_view_rate'] < 0) {
                $company['viewingRate'] = '-';
            } elseif ($company['resume_view_rate'] >= '0.00') {
                $company['viewingRate'] = $company['resume_view_rate'] . '%';
            } else {
                $company['viewingRate'] = '-';
            }

            //获取单位在招职位数量
            $company['jobAmount'] = BaseJob::getCompanyJobAmount($company['companyId']);
            //获取单位登录时间
            $company['lastLoginTime'] = BaseMemberActionLog::getCompanyLastLoginDateText($company['member_id']);
            //获取单位行业
            $company['industry'] = BaseTrade::getIndustryName($company['industry_id']);
            //获取单位规模
            $company['scale'] = BaseDictionary::getCompanyScaleName($company['scale']);
            //获取单位类型
            $company['type'] = BaseDictionary::getCompanyTypeName($company['type']);
            //获取单位性质
            $company['nature'] = BaseDictionary::getCompanyNatureName($company['nature']);

            //获取单位logo
            $company['logoUrl'] = self::getLogoFullUrl($company['logo_url']);

            $company['areaName'] = BaseArea::getAreaName($company['cityId']) ?: '';

            //获取单位发布公告数量
            $company['announcementAmount'] = BaseAnnouncement::getCompanyOnLineAnnouncementAmount($company['companyId']);
            $company['id']                 = $company['companyId'];
        }

        Cache::set($cacheKey, json_encode($list), 30 * 60);

        return $list;
    }

    /**
     *
     * 需求说明
     * 单位搜索结果（无结果）：
     * 1、若无匹配单位，则默认推荐20条单位信息：
     * 按近3个月单位收到的职位投递量排序倒序；
     */
    public static function miniRecommendList($page = 1)
    {
        $searchModel = self::find()
            ->alias('c')
            ->innerJoin([
                't' => BaseCompanyStatData::tableName(),
            ], 'c.id=t.company_id')
            ->where([
                'status'  => self::STATUS_ACTIVE,
                'is_hide' => self::IS_HIDE_NO,
            ]);

        $select = [
            'c.id as companyId',
            "c.package_type as companyRole",
            'c.full_name as name',
            'c.logo_url',
            'c.scale',
            'c.nature',
            'c.industry_id',
            'c.member_id',
            'c.city_id as cityId',
            'c.type',
            'c.nature',
            'c.job_last_release_time',
            'c.is_cooperation as isCooperation',
            'c.welfare_label_ids',
            't.resume_view_rate',
        ];

        $sort = "heat desc";

        $offset = ($page - 1) * 20;
        $list   = $searchModel->select($select)
            ->orderBy($sort)
            ->asArray()
            ->offset($offset)
            ->limit(20)
            ->all();

        foreach ($list as &$company) {
            //获取单位在招职位数量
            $company['jobAmount'] = BaseJob::getCompanyJobAmount($company['companyId']);
            //获取单位登录时间
            $company['lastLoginTime'] = BaseMemberActionLog::getCompanyLastLoginDateText($company['member_id']);
            //获取单位行业
            $company['industry'] = BaseTrade::getIndustryName($company['industry_id']);
            //获取单位规模
            $company['scale'] = BaseDictionary::getCompanyScaleName($company['scale']);
            //获取单位类型
            $company['type'] = BaseDictionary::getCompanyTypeName($company['type']);
            //获取单位性质
            $company['nature'] = BaseDictionary::getCompanyNatureName($company['nature']);

            //获取单位logo
            $company['logoUrl'] = self::getLogoFullUrl($company['logo_url']);

            $company['areaName'] = BaseArea::getAreaName($company['cityId']);

            //获取单位发布公告数量
            $company['announcementAmount'] = BaseAnnouncement::getCompanyAnnouncementAmount($company['companyId']);
            $company['id']                 = $company['companyId'];
        }

        return $list;
    }

    /**
     * 获取单位详情
     * @param     $companyId
     * @param     $platformType
     * @param int $memberId
     * @return array
     * @throws Exception
     */
    public static function getDetailService($companyId, $platformType, $memberId = 0)
    {
        //验证参数合法性
        if (empty($companyId) || empty($platformType)) {
            throw new Exception('参数错误');
        }
        //获取单位信息
        $company_info = BaseCompany::findOne($companyId);
        if (empty($company_info)) {
            throw new Exception('单位不存在');
        }
        //获取单位账号信息
        $member_info = BaseMember::findOne($company_info->member_id);
        //获取单位联系方式基本信息
        $company_contact_info = BaseCompanyContact::findOne(['company_id' => $companyId]);
        //定义返回结果
        $return_result = [];
        //返回关键ID
        $return_result['company_id']           = $company_info->id;
        $return_result['member_id']            = $company_info->member_id;
        $return_result['company_name']         = $company_info->full_name;
        $return_result['company_package_type'] = $company_info->package_type;
        $return_result['contact_name']         = $company_contact_info->name_is_public == BaseCompany::NAME_PUBLIC_NO ? '' : $company_contact_info->name;//联系人姓名
        $return_result['contact_mobile']       = $company_contact_info->mobile_is_public == BaseCompany::MOBILE_PUBLIC_NO ? '' : $company_contact_info->mobile;//联系人手机
        $return_result['contact_email']        = $company_contact_info->email_is_public == BaseCompany::EMAIL_PUBLIC_NO ? '' : $company_contact_info->email;//联系人邮箱
        $return_result['contact_telephone']    = $company_contact_info->telephone;
        $return_result['contact_fax']          = $company_contact_info->fax;
        $return_result['is_collect']           = BaseCompanyCollect::COLLECT_STATUS_NO;
        $return_result['is_login']             = BaseMember::IS_LOGIN_NO;
        $return_result['website']              = $company_info->website;
        if (!empty($memberId)) {
            $isCollect = BaseCompanyCollect::checkIsCollect($memberId, $companyId);
            if ($isCollect) {
                $return_result['is_collect'] = BaseCompanyCollect::COLLECT_STATUS_YES;
            } else {
                $return_result['is_collect'] = BaseCompanyCollect::COLLECT_STATUS_NO;
            }
            $return_result['is_login'] = BaseMember::IS_LOGIN_YES;
        }
        //单位规模
        $return_result['company_scale_name'] = BaseDictionary::getCompanyScaleName($company_info->scale);
        //获取单位二级院校
        $return_result['child_unit_list'] = BaseCompanyChildUnit::getList($companyId);
        if (count($return_result['child_unit_list']) > 0) {
            $return_result['has_child_unit_list'] = true;
        } else {
            $return_result['has_child_unit_list'] = false;
        }
        $announcement_count_data = BaseCompany::getAnnouncementCountService($companyId, $platformType, $memberId);
        //获取单位在线公告数量
        if ($announcement_count_data['online_announcement_num'] > 99) {
            $return_result['announcement_amount'] = '99+';
        } else {
            $return_result['announcement_amount'] = strval($announcement_count_data['online_announcement_num']);
        }
        //获取单位在招职位数量
        $job_count_data = BaseCompany::getJobCountService($companyId, $platformType, $memberId);
        if ($job_count_data['online_job_num'] > 99) {
            $return_result['job_amount'] = '99+';
        } else {
            $return_result['job_amount'] = strval($job_count_data['online_job_num']);
        }
        $activityData = BaseCompany::getActivityList(['companyId' => $companyId]);
        if ($activityData['page']['count'] > 99) {
            $return_result['activity_amount'] = '99+';
        } else {
            $return_result['activity_amount'] = strval($activityData['page']['count']);
        }
        //获取单位logo
        $return_result['logo_url'] = BaseCompany::getLogoFullUrl($company_info->logo_url);
        //获取单位性质
        $return_result['nature_name'] = BaseDictionary::getCompanyNatureName($company_info->nature);
        //获取单位类型
        $return_result['type_name'] = BaseDictionary::getCompanyTypeName($company_info->type);
        //获取单位经纬度
        $return_result['lat'] = BaseMemberAddress::findOneVal(['member_id' => $company_info->member_id], 'lat');
        $return_result['lng'] = BaseMemberAddress::findOneVal(['member_id' => $company_info->member_id], 'lng');
        //单位介绍换行格式
        $return_result['introduce'] = StringHelper::changeLineFeed($company_info->introduce);
        //单位背景图
        $return_result['head_banner_url'] = FileHelper::getFullUrl($company_info->head_banner_url);
        //单位福利
        $return_result['welfare_label'] = [];
        if (!empty($company_info->welfare_label_ids)) {
            foreach (explode(',', $company_info->welfare_label_ids) as $welfare_label_id) {
                $return_result['welfare_label'][] = BaseWelfareLabel::getWelfareLabelName($welfare_label_id);
            }
        }
        //获取单位标签
        $return_result['company_label'] = [];
        if (!empty($company_info->label_ids)) {
            $label_arr = explode(',', $company_info->label_ids);
            foreach ($label_arr as $key => $v) {
                array_push($return_result['company_label'], BaseDictionary::getCompanyLabelName($v));
            }
        }
        $return_result['fuse_arr'] = array_merge($return_result['company_label'], $return_result['welfare_label']);
        //单位风采图集
        $style_atlas      = array_filter(explode(',', $company_info->style_atlas));
        $style_atlas_list = [];
        foreach ($style_atlas as $key => $item) {
            $file               = BaseFile::findOne(['id' => $item]);
            $style_atlas_list[] = FileHelper::getFullUrl($file['path'], $file['platform']);
        }
        $return_result['style_atlas_list']       = $style_atlas_list;
        $return_result['mobile_head_banner_url'] = FileHelper::getFullUrl($company_info->mobile_head_banner_url) ?: Yii::$app->params['companyDefaultBanner'];
        //拼接单位地址
        $province_name = BaseArea::getAreaName($company_info->province_id);
        $city_name     = BaseArea::getAreaName($company_info->city_id);
        $district_name = BaseArea::getAreaName($company_info->district_id);
        if ($province_name == $city_name) {
            $return_result['address'] = $city_name . $district_name . $company_info->address;
        } else {
            $return_result['address'] = $province_name . $city_name . $district_name . $company_info->address;
        }

        return $return_result;
    }

    /**
     * 单位详情下公告与职位列表的筛选项
     * @param $companyId
     * @param $type
     * @param $platformType
     * @return array
     * @throws \Exception
     */
    public static function getDetailFilterService($companyId, $type, $platformType)
    {
        $has_bool = false;
        if ($type == 1) {
            $has_bool = true;
        }
        //获取职位地区筛选项
        $area_list = BaseCompany::getJobAreaList($companyId, $has_bool);
        //获取职位职位类型筛选项
        $job_type_list = BaseCompany::getJobCategoryList($companyId, $has_bool);
        //获取职位专业筛选项
        $major_list = BaseCompany::getJobMajorList($companyId, $has_bool);
        // 获取学历
        $education_type_list = BaseCompany::getJobEducationList($companyId, $has_bool);

        //返回结果
        return [
            'area_list'           => $area_list,
            'job_type_list'       => $job_type_list,
            'major_list'          => $major_list,
            'education_type_list' => $education_type_list,
        ];
    }

    public static function getJobEducationList($companyId, $hasAnnouncement = false)
    {
        $query = BaseJob::find()
            ->andWhere([
                'in',
                'status',
                [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->andWhere(['is_show' => BaseJob::IS_SHOW_YES])
            ->andWhere(['company_id' => $companyId]);
        //如果需要查有公告的，需要判断不为空
        if ($hasAnnouncement) {
            $query->andWhere([
                '<>',
                'announcement_id',
                0,
            ]);
        }
        $educationTypeList = $query->select('education_type')
            ->groupBy('education_type')
            ->asArray()
            ->all();

        $educationList = [];
        foreach ($educationTypeList as $k => $job) {
            if (empty($job['education_type'])) {
                continue;
            }
            $educationList[$k]['k'] = $job['education_type'];
            $educationList[$k]['v'] = BaseResumeEducation::EDUCATION_TYPE_LIST[$job['education_type']];
        }
        if (PLATFORM == 'MINI') {
            //头部插入一个全部
            array_unshift($educationList, [
                'k' => '',
                'v' => '全部',
            ]);
        }

        return $educationList;
    }

    /**
     * 单位下职位数据统计
     * @param     $companyId
     * @param     $platformType
     * @param int $memberId
     * @return array
     */
    public static function getJobCountService($companyId, $platformType, $memberId = 0)
    {
        $job_data = BaseJob::find()
            ->select([
                'id',
                'status',
            ])
            ->andWhere(['company_id' => $companyId])
            ->andWhere([
                'status' => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->andWhere(['is_show' => BaseJob::IS_SHOW_YES])
            ->asArray()
            ->all();
        //获取到单位下的所有显示的在线或者下线的职位进行统计
        $online_job_num  = 0;
        $offline_job_num = 0;
        $all_job_num     = count($job_data);
        foreach ($job_data as $key => $value) {
            if ($value['status'] == BaseJob::STATUS_ONLINE) {
                $online_job_num++;
            } else {
                $offline_job_num++;
            }
        }

        return [
            'all_job_num'     => $all_job_num,
            'online_job_num'  => $online_job_num,
            'offline_job_num' => $offline_job_num,
        ];
    }

    /**
     * 单位下职位列表
     * @param       $companyId
     * @param       $platformType
     * @param int   $memberId
     * @param array $searchParams
     * @return array
     * @throws Exception
     */
    public static function getJobListService($companyId, $platformType, $memberId = 0, $searchParams = [])
    {
        // 2.4 小程序追加逻辑，如果没登陆只拿前20条
        if (PLATFORM == 'MINI' && empty($memberId)) {
            $searchParams['page']     = 1;
            $searchParams['pageSize'] = 20;
        }

        //判断单位ID
        if (empty($companyId)) {
            throw new Exception('参数错误');
        }
        //获取单位下职位数据
        $query = BaseJob::find()
            ->alias('j')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id = j.announcement_id')
            ->leftJoin(['jmr' => BaseJobMajorRelation::tableName()], 'j.id = jmr.job_id')
            ->andWhere(['j.company_id' => $companyId])
            ->andWhere(['j.is_show' => BaseJob::IS_SHOW_YES])
            ->andWhere([
                'j.status' => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->groupBy('j.id');

        $query->select([
            'j.id',
            'j.name',
            'j.refresh_time',
            'j.status',
            'j.education_type',
            'j.amount',
            'j.province_id',
            'j.city_id',
            'j.announcement_id',
            'j.min_wage',
            'j.max_wage',
            'j.wage_type',
            'a.title',
        ]);

        //根据条件筛选
        //地区
        $query->andFilterCompare('j.city_id', $searchParams['cityId']);
        //职位类型
        $query->andFilterCompare('j.job_category_id', $searchParams['jobCategoryId']);
        //专业
        $query->andFilterCompare('jmr.major_id', $searchParams['majorId']);
        // 学历
        $query->andFilterCompare('j.education_type', $searchParams['educationId']);

        $count    = $query->count();
        $pageSize = $searchParams['pageSize'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $searchParams['page'], $pageSize);
        //排序规则按在线，发布时间倒序，id倒序排，由于目前只有在线、下线两种状态被取出来
        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('j.status desc,j.refresh_time desc,j.id desc')
            ->asArray()
            ->all();
        foreach ($list as &$item) {
            //发布时间
            $item['refresh_time'] = TimeHelper::formatDateByYear($item['refresh_time']);
            //学历要求
            $item['education_type_name'] = BaseDictionary::getEducationName($item['education_type']);
            //地区
            $item['city_name'] = BaseArea::getAreaName($item['city_id']);
            //拼接工资
            if ($item['min_wage'] == 0 && $item['max_wage'] == 0) {
                $item['wage_name'] = '面议';
            } else {
                $item['wage_name'] = BaseJob::formatWage($item['min_wage'], $item['max_wage'], $item['wage_type']);
            }
        }

        return [
            'list'     => $list,
            'page'     => $pages['page'],
            'pageSize' => $pages['limit'],
            'total'    => intval($count),
        ];
    }

    /**
     * 单位下引才活动列表
     * @param       $companyId
     * @param       $platformType
     * @param int   $memberId
     * @param array $searchParams
     * @return array
     * @throws Exception
     */
    public static function getActivityListService($companyId, $platformType, $memberId = 0, $searchParams = [])
    {
        $data = BaseCompany::getActivityList([
            'companyId'    => $companyId,
            'platformType' => $platformType,
            'page'         => $searchParams['page'] ?? 1,
            'pageSize'     => $searchParams['pageSize'] ?? 20,
        ]);

        return [
            'list'     => $data['list'],
            'page'     => $data['page']['page'],
            'pageSize' => $data['page']['limit'],
            'total'    => intval($data['page']['count']),
        ];
    }

    /**
     * 单位下公告数据统计
     * @param     $companyId
     * @param     $platformType
     * @param int $memberId
     * @return array
     */
    public static function getAnnouncementCountService($companyId, $platformType, $memberId = 0)
    {
        $announcement_data = BaseAnnouncement::find()
            ->alias('an')
            ->select([
                'an.id',
                'ar.status',
            ])
            ->leftJoin(['ar' => BaseArticle::tableName()], 'an.article_id = ar.id')
            ->andWhere(['an.company_id' => $companyId])
            ->andWhere(['ar.is_delete' => BaseArticle::IS_DELETE_NO])
            ->andWhere([
                'ar.status' => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
            ]) //在线或者下线状态
            ->andWhere(['ar.is_show' => BaseArticle::IS_SHOW_YES])
            ->asArray()
            ->all();
        //统计数据
        $online_announcement_num  = 0;
        $offline_announcement_num = 0;
        $all_announcement_num     = count($announcement_data);
        foreach ($announcement_data as $key => $value) {
            if ($value['status'] == BaseArticle::STATUS_ONLINE) {
                $online_announcement_num++;
            } else {
                $offline_announcement_num++;
            }
        }

        return [
            'all_announcement_num'     => $all_announcement_num,
            'online_announcement_num'  => $online_announcement_num,
            'offline_announcement_num' => $offline_announcement_num,
        ];
    }

    /**
     * 单位下公告列表
     * @param     $companyId
     * @param     $platformType
     * @param int $memberId
     * @param     $searchParams
     * @return array
     * @throws Exception
     */
    public static function getAnnouncementListService($companyId, $platformType, $memberId = 0, $searchParams = [])
    {
        // 2.4 小程序追加逻辑，如果没登陆只拿前20条
        if (PLATFORM == 'MINI' && empty($memberId)) {
            $searchParams['page']     = 1;
            $searchParams['pageSize'] = 20;
        }

        //判断单位ID
        if (empty($companyId)) {
            throw new Exception('参数错误');
        }
        //获取公告列表
        $query = BaseAnnouncement::find()
            ->alias('an')
            ->innerJoin(['ar' => BaseArticle::tableName()], 'an.article_id = ar.id')
            ->leftJoin(['j' => BaseJob::tableName()], 'an.id = j.announcement_id')
            ->andWhere(['an.company_id' => $companyId])
            ->andWhere(['ar.is_delete' => BaseArticle::IS_DELETE_NO])
            ->andWhere(['ar.is_show' => BaseArticle::IS_SHOW_YES])
            ->andWhere([
                'ar.status' => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
            ])
            ->groupBy('an.id');

        $query->select([
            'an.id',
            'an.title',
            'ar.status',
            'ar.is_show',
            'ar.is_delete',
            'ar.refresh_time',
        ]);

        //根据条件筛选
        //地区
        $query->andFilterCompare('j.city_id', $searchParams['cityId']);
        //职位类型
        $query->andFilterCompare('j.job_category_id', $searchParams['jobCategoryId']);
        //专业
        if ($searchParams['majorId']) {
            $query->leftJoin(['jmr' => BaseJobMajorRelation::tableName()], 'an.id = jmr.announcement_id');
            $query->andFilterCompare('jmr.major_id', $searchParams['majorId']);
        }
        // 学历
        $query->andFilterCompare('j.education_type', $searchParams['educationId']);

        $count    = $query->count();
        $pageSize = $searchParams['pageSize'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $searchParams['page'], $pageSize);
        //排序规则按在线，发布时间倒序，id倒序排，由于目前只有在线、下线两种状态被取出来
        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('ar.status asc,an.home_sort desc,ar.refresh_time desc,an.id desc')
            ->asArray()
            ->all();
        foreach ($list as &$announcement) {
            $announcement_data = BaseAnnouncement::getJobCountService($announcement['id'], $platformType, $memberId);
            //学历
            $announcement['education_name'] = $announcement_data['education_name'];
            //最低学历文案
            $announcement['min_education_name'] = $announcement_data['min_education_name'];
            //在线及下线的职位数
            $announcement['all_job_number'] = $announcement_data['all_job_number'];
            //所有在线及下线职位总招聘人数
            $announcement['announcement_recruit_amount'] = $announcement_data['announcement_recruit_amount'];
            //发布时间
            $announcement['refresh_time'] = TimeHelper::formatDateByYear($announcement['refresh_time']);
        }

        return [
            'list'     => $list,
            'page'     => $pages['page'],
            'pageSize' => $pages['limit'],
            'total'    => intval($count),
        ];
    }

    /**
     * 获取单位列表筛选器
     * @param $platformType
     * @return array
     */
    public static function getListFilter($platformType)
    {
        $result = [];
        //获取单位类型
        $result['companyTypeList'] = BaseDictionary::getCompanyTypeList();
        //获取单位性质
        $result['companyNatureList'] = BaseDictionary::getCompanyNatureList();
        //获取工作地点
        $result['cityList'] = BaseArea::getAllHierarchyCityList();

        //引入其他平台时候注意公共与平台独有的区别从而根据平台加上判断及返回

        return $result;
    }

    public static function getDetailMiniCode($id)
    {
        // 先去缓存里面取
        $cacheKey = Cache::MINI_CODE_KEY . ':' . WxMiniApp::QRCODE_PATH_TYPE_COMPANY . ':' . $id;
        if ($url = Cache::get($cacheKey)) {
            return $url;
        }

        $mini = WxMiniApp::getInstance();
        $url  = $mini->createQrCodeByType(WxMiniApp::QRCODE_PATH_TYPE_COMPANY, $id);

        Cache::set($cacheKey, $url);

        return $url;
    }

    /**
     * 判断是否合作
     * @param $id
     * @return bool
     */
    public static function checkIsCooperation($id)
    {
        $isCooperation = self::findOneVal(['id' => $id], 'is_cooperation');
        if ($isCooperation == self::COOPERATIVE_UNIT_YES) {
            return true;
        }

        return false;
    }

    /**
     * 获取单位地址
     * @param $id
     * @return string
     */
    public static function getAddress($id): string
    {
        $info = self::find()
            ->where(['id' => $id])
            ->select([
                'address',
                'province_id as provinceId',
                'city_id as cityId',
                'district_id as districtId',
            ])
            ->asArray()
            ->one();
        //拼接单位地址
        $provinceName = BaseArea::getAreaName($info['provinceId']);
        $cityName     = BaseArea::getAreaName($info['cityId']);
        if ($provinceName == $cityName) {
            $address = BaseArea::getAreaName($info['cityId']) . BaseArea::getAreaName($info['districtId']) . $info['address'];
        } else {
            $address = BaseArea::getAreaName($info['provinceId']) . BaseArea::getAreaName($info['cityId']) . BaseArea::getAreaName($info['districtId']) . $info['address'];
        }

        return $address;
    }

    public static function editGroup($companyId, $groupIds)
    {
        //先获取单位信息
        $company = self::findOne($companyId);
        if (!$company) {
            throw new Exception('单位不存在');
        }
        //再插入新的分组关系
        $groupIdsArr = explode(',', $groupIds);
        //排序
        sort($groupIdsArr);
        $groupIds = implode(',', $groupIdsArr);
        //先看原先分组是否有变化
        $oldGroupIdsArr = BaseCompanyGroupRelation::find()
            ->where(['company_id' => $companyId])
            ->select('group_id')
            ->orderBy('group_id asc')
            ->column();
        $oldGroupIds    = implode(',', $oldGroupIdsArr);
        if ($oldGroupIds != $groupIds) {
            //这是后需要更新
            //先删除原先的分组关系
            BaseCompanyGroupRelation::deleteAll(['company_id' => $companyId]);

            $insert = [];
            foreach ($groupIdsArr as $groupId) {
                $insert[] = [
                    'company_id' => $companyId,
                    'group_id'   => $groupId,
                ];
            }
            if (count($insert) > 0) {
                $res = BaseCompanyGroupRelation::getDb()
                    ->createCommand()
                    ->batchInsert(BaseCompanyGroupRelation::tableName(), [
                        'company_id',
                        'group_id',
                    ], $insert)
                    ->execute();
                if ($res) {
                    //写入成功后 看看分值系统
                    $groupScoreSystemId = BaseCompanyGroupScoreSystem::getSystemScoreId($groupIds);
                    //更新单位自身
                    if ($groupScoreSystemId != $company->group_score_system_id) {
                        $company->group_score_system_id = $groupScoreSystemId;
                        $company->save();
                    }
                    //成功后对单位进行数量更新
                    $reduceArr   = array_diff($oldGroupIdsArr, $groupIdsArr);
                    $increaseArr = array_diff($groupIdsArr, $oldGroupIdsArr);
                    BaseCompanyGroup::companyGroupNumber($reduceArr, BaseCompanyGroup::OPERATION_TYPE_REDUCE);
                    BaseCompanyGroup::companyGroupNumber($increaseArr, BaseCompanyGroup::OPERATION_TYPE_INCREASE);

                    return true;
                }
            }
            //            throw new Exception('分组更新失败');
        }

        return true;
    }

    /**
     * @param $id
     * 获取单位logo
     */
    public static function getCompanyLogo($logoUrl)
    {
        return $logoUrl ?: Yii::$app->params['defaultCompanyLogo'];
    }

    /**
     * 单位标签
     * @param $ids
     * @return array
     * @throws \Exception
     */
    public static function getCompanyLabelName($ids)
    {
        if (!$ids) {
            return [];
        }
        if (is_string($ids)) {
            $ids = explode(',', $ids);
        }
        $names = [];
        foreach ($ids as $v) {
            if ($v) {
                $names[] = BaseDictionary::getCompanyLabelName($v);
            }
        }

        return $names;
    }

    // 获取单位群组名字
    public static function getGroupName($companyId, $seg = ',')
    {
        $groupIds = BaseCompanyGroupRelation::find()
            ->where(['company_id' => $companyId])
            ->select('group_id')
            ->column();
        if (count($groupIds) > 0) {
            $groupNames = BaseCompanyGroup::find()
                ->where(['id' => $groupIds])
                ->select('group_name')
                ->column();
            if (count($groupNames) > 0) {
                return implode($seg, $groupNames);
            }
        }

        return '';
    }

    /**
     * 获取合作单位详情页面引才活动页面信息
     * @param $id
     * @return array|ActiveRecord|null
     * @throws \Exception
     */
    public static function getDetailActivityListInfo($id)
    {
        $companyInfo          = BaseCompany::findOne($id);
        $info                 = [];
        $info['companyId']    = $id;
        $info['activityList'] = BaseCompany::getActivityList(['companyId' => $id]);
        //获取单位在招职位数量
        $info['add_time']    = $companyInfo->add_time;
        $info['companyName'] = $companyInfo->full_name;

        return $info;
    }

    /**
     * 获取单位引才活动的活动列表
     * @param $companyId
     */
    public static function getActivityList($params)
    {
        if (!isset($params['companyId']) || $params['companyId'] < 0) {
            throw new Exception('单位ID为空');
        }
        //【单位详情页】新增“引才活动”Tab，展示该单位“上架”状态的相关活动内容。内容调用及展示规则：
        //【单位详情页】
        //（1）招聘会系列活动：展示该单位活动所关联的公告（隐藏状态的公告除外），并按公告ID去重处理后展示。
        $announcementList = BaseHwActivityAnnouncement::find()
            ->alias('haa')
            ->select([
                'a.id as announcementId',
                'a.title as announcementName',
            ])
            ->leftJoin(['ha' => BaseHwActivity::tableName()], 'ha.id = haa.activity_id')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id = haa.announcement_id')
            ->leftJoin(['art' => BaseArticle::tableName()], 'art.id = a.article_id')
            ->where([
                'haa.company_id'      => $params['companyId'],
                'ha.series_type'      => BaseHwActivity::ZHAOPINHUI_TYPE,
                'art.status'          => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
                'ha.grounding_status' => BaseHwActivity::GROUNDING_STATUS_ON,
                'art.is_show'         => BaseArticle::IS_SHOW_YES,
            ])
            ->groupBy('haa.announcement_id')
            ->asArray()
            ->all();
        $previewList      = [];
        $list1            = [];
        foreach ($announcementList as $item) {
            //获取所有活动ID
            $activityIds   = BaseHwActivityAnnouncement::find()
                ->select('activity_id')
                ->where(['announcement_id' => $item['announcementId']])
                ->orderBy('activity_id desc')
                ->column();
            $activityMaxId = $activityIds[0];
            //获取
            $maxEndDatetimeSessionInfo = BaseHwActivitySession::find()
                ->select([
                    'id',
                    'end_date',
                    'end_time',
                    'custom_time',
                ])
                ->where(['activity_id' => $activityIds])
                ->orderBy('end_date desc,activity_id desc')
                ->limit(1)
                ->asArray()
                ->one();
            if ($maxEndDatetimeSessionInfo['end_date'] != TimeHelper::ZERO_DATE) {
                $minStartDatetimeSessionInfo = BaseHwActivitySession::find()
                    ->select([
                        'id',
                        'start_date',
                        'start_time',
                    ])
                    ->andWhere(['activity_id' => $activityIds])
                    ->andWhere([
                        '!=',
                        'start_date',
                        TimeHelper::ZERO_DATE,
                    ])
                    ->orderBy('start_date asc,activity_id desc')
                    ->limit(1)
                    ->asArray()
                    ->one();
                $sortTime                    = $minStartDatetimeSessionInfo['start_date'];
                $sortType                    = 1;
                $time                        = $minStartDatetimeSessionInfo['start_date'] == $maxEndDatetimeSessionInfo['end_date'] ? $minStartDatetimeSessionInfo['start_date'] : $minStartDatetimeSessionInfo['start_date'] . '~' . $maxEndDatetimeSessionInfo['end_date'];
                $status                      = BaseHwActivity::getZhaoPinHuiActivityChildStatus($minStartDatetimeSessionInfo['start_date'],
                    $maxEndDatetimeSessionInfo['end_date'], $minStartDatetimeSessionInfo['start_time'],
                    $maxEndDatetimeSessionInfo['end_time']);
            } else {
                $sortTime = $maxEndDatetimeSessionInfo['custom_time'];
                $time     = $maxEndDatetimeSessionInfo['custom_time'];
                $sortType = 2;
                $status   = BaseHwActivity::ACTIVITY_CHILD_STATUS_NOT_START;
            }
            if (PLATFORM == 'PC') {
                $announcementUrl = UrlHelper::createPcAnnouncementDetailPath($item['announcementId']);
            } elseif (PLATFORM == 'H5') {
                $announcementUrl = UrlHelper::createH5AnnouncementDetailPath($item['announcementId']);
            } elseif (PLATFORM == 'MINI') {
                $announcementUrl = UrlHelper::createMiniAnnouncementDetailPath($item['announcementId']);
                $miniUrl         = [
                    'targetType' => BaseShowcase::TARGET_LINK_TYPE_STATION,
                    'url'        => BaseShowcase::urlToMiniRouter(BaseShowcase::PAGE_LINK_TYPE_ANNOUNCEMENT_DETAIL,
                        $item['announcementId']),
                ];
            } else {
                $announcementUrl = UrlHelper::createPcAnnouncementDetailPath($item['announcementId']);
            }
            $areaArr = [];
            foreach ($activityIds as $activityId) {
                $areaArr[] = BaseHwActivity::getAddressBySeries($activityId);
            }
            $activityMaxInfo = BaseHwActivity::findOne($activityMaxId);
            $activityItem    = [
                'name'       => $item['announcementName'],
                'url'        => $announcementUrl,
                'miniUrl'    => $miniUrl ?? [],
                'time'       => $time,
                'rel'        => '',
                'sortType'   => $sortType,
                'sortTime'   => $sortTime,
                'seriesType' => $activityMaxInfo->series_type,
                'type'       => $activityMaxInfo->type,
                'typeText'   => BaseHwActivity::TYPE_TEXT_LIST[$activityMaxInfo->type],
                //去重
                'area'       => implode(',', array_unique(explode(',', implode(',', $areaArr)))),
                'status'     => $status,
                'statusText' => BaseHwActivity::ACTIVITY_CHILD_STATUS_TEXT_LIST[$activityMaxInfo->series_type][$status],
            ];
            $list1[]         = $activityItem;
            if (in_array($status, [
                BaseHwActivity::ACTIVITY_CHILD_STATUS_NOT_START,
                BaseHwActivity::ACTIVITY_CHILD_STATUS_BE_ABOUT_TO_START,
            ])) {
                $previewList[] = $activityItem;
            }
        }
        //（2）非招聘会系列活动：展示“关联单位”=该单位的 活动；
        $activityList = BaseHwActivity::find()
            ->select([
                'id',
                'series_type',
                'type',
                'name',
                'activity_child_status',
                'detail_url',
                'activity_start_date',
                'activity_end_date',
                'is_outside_url',
            ])
            ->where([
                'grounding_status' => BaseHwActivity::GROUNDING_STATUS_ON,
                'company_id'       => $params['companyId'],
                'series_type'      => BaseHwActivity::ZHAOPINHUI_UN_TYPE,
            ])
            ->asArray()
            ->all();

        $list2 = [];
        foreach ($activityList as $item) {
            $time     = BaseHwActivity::getActivityDate($item['id']);
            $sortType = $item['activity_start_date'] != TimeHelper::ZERO_DATE ? 1 : 2;
            $sortTime = $item['activity_start_date'] != TimeHelper::ZERO_DATE ? $item['activity_start_date'] : $time;
            $timeType = BaseHwActivity::getTimeTypeText($item['id']);
            if ($timeType) {
                $time .= '(' . $timeType . ')';
            }
            $getUrlType = MiniHelper::getUrlType($item['detail_url']);
            if ($getUrlType['targetType'] == BaseShowcase::TARGET_LINK_TYPE_STATION) {
                $getUrlType['url'] = BaseShowcase::urlToMiniRouter($getUrlType['pageType'], $getUrlType['url']);
            }
            $activityItem2 = [
                'name'       => $item['name'],
                'url'        => $item['detail_url'],
                'miniUrl'    => $getUrlType,
                'sortType'   => $sortType,
                'sortTime'   => $sortTime,
                'time'       => $time,
                'rel'        => $item['is_outside_url'] == BaseHwActivity::IS_OUTSIDE_URL_YES ? 'nofollow' : '',
                'seriesType' => intval($item['series_type']),
                'type'       => intval($item['type']),
                'typeText'   => BaseHwActivity::TYPE_TEXT_LIST[$item['type']],
                'area'       => BaseHwActivity::getAddressBySeries($item['id']),
                'status'     => intval($item['activity_child_status']),
                'statusText' => BaseHwActivity::ACTIVITY_CHILD_STATUS_TEXT_LIST[$item['series_type']][$item['activity_child_status']],
            ];
            $list2[]       = $activityItem2;
            if (in_array($item['activity_child_status'], [
                BaseHwActivity::ACTIVITY_CHILD_STATUS_NOT_START,
                BaseHwActivity::ACTIVITY_CHILD_STATUS_BE_ABOUT_TO_START,
            ])) {
                $previewList[] = $activityItem2;
            }
        }
        $page     = $params['page'] ?: 1;
        $pageSize = $params['pageSize'] ?: 20;
        //合并起来
        $list = array_merge($list1, $list2);
        //再根据页码进行分页
        $count = count($list);
        if ($list) {
            usort($list, function ($a, $b) {
                // 1. 先按 sortType 降序（2 > 1）
                $compare = $b['sortType'] <=> $a['sortType'];
                if ($compare !== 0) {
                    return $compare;
                }

                // 2. sortType 相同时，按 sortTime（日期）降序
                $timeA = strtotime($a['sortTime']);
                $timeB = strtotime($b['sortTime']);

                return $timeB <=> $timeA;
            });
            $pages            = BaseActiveRecord::setPage($count, $page, $pageSize);
            $returnList       = array_slice($list, $pages['offset'], $pages['limit']);
            $previewListSlice = array_slice($previewList, 0, 2);
        }

        return [
            'previewList' => [
                'list'  => $previewListSlice ?? [],
                'count' => count($previewList),
            ],
            'list'        => $returnList ?? [],
            'page'        => [
                'limit' => intval($pageSize),
                'count' => $count,
                'page'  => intval($page),
            ],
        ];
    }

    /*
     * 设置单位时间因子
     * @param $addTime   单位入库时间
     * @param $companyId 单位id
     */
    public static function getCompanyDetailTimeJs($addTime, $companyId)
    {
        $latestJob = BaseJob::getOneJobByCompanyId($companyId, [
            'refresh_time',
        ]);
        if (empty($latestJob)) {
            BaiduTimeFactor::create($addTime, '');

            return true;
        }
        $refreshTime = strtotime($latestJob['refresh_time']);
        $currentTime = time();
        $diffTime    = floor((($currentTime - $refreshTime) / 86400));

        if ($diffTime >= 90) {
            $cacheKey = sprintf(Cache::COMPANY_TIME_FACTOR, $companyId);

            $cacheTime = Cache::get($cacheKey);
            if ($cacheTime) {
                $cacheDiffTime = floor((($currentTime - $cacheTime) / 86400));
                if ($cacheDiffTime >= 90) {
                    $refreshTime = $currentTime;
                    Cache::set($cacheKey, $refreshTime);
                } else {
                    $refreshTime = $cacheTime;
                }
            } else {
                $refreshTime = $currentTime;
                Cache::set($cacheKey, $refreshTime);
            }
        }

        $refreshDate = date('Y-m-d H:i:s', $refreshTime);

        BaiduTimeFactor::create($addTime, $refreshDate);
        ToutiaoTimeFactor::create('', $refreshDate);
    }
}