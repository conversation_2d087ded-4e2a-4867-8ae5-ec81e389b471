<?php

namespace common\base\models;

use common\helpers\FileHelper;
use common\helpers\ValidateHelper;
use common\models\CompanyMemberInfo;
use Yii;
use yii\db\conditions\AndCondition;
use yii\db\Exception;

class BaseCompanyMemberInfo extends CompanyMemberInfo
{
    /** @var int 保留上次操作按钮（通用） */
    const IS_REMEMBER_YES = 1;
    const IS_REMEMBER_NO  = 2;

    /** 单位主账号 */
    const COMPANY_MEMBER_TYPE_MAIN      = 0;
    const COMPANY_MEMBER_TYPE_MAIN_TEXT = '主账号';
    /** 单位子账号 */
    const COMPANY_MEMBER_TYPE_SUB      = 1;
    const COMPANY_MEMBER_TYPE_SUB_TEXT = '子账号';
    /** 单位账号文案列表 */
    const COMPANY_MEMBER_TYPE_LIST = [
        self::COMPANY_MEMBER_TYPE_MAIN => self::COMPANY_MEMBER_TYPE_MAIN_TEXT,
        self::COMPANY_MEMBER_TYPE_SUB  => self::COMPANY_MEMBER_TYPE_SUB_TEXT,
    ];

    /** 账号权限-普通权限 */
    const COMPANY_MEMBER_AUTH_NORMAL          = 1;
    const COMPANY_MEMBER_AUTH_NORMAL_TEXT     = '普通权限';
    const COMPANY_MEMBER_AUTH_NORMAL_TEXT_TAG = '普通子账号';
    /** 账号权限-VIP权限 */
    const COMPANY_MEMBER_AUTH_VIP          = 2;
    const COMPANY_MEMBER_AUTH_VIP_TEXT     = 'VIP权限';
    const COMPANY_MEMBER_AUTH_VIP_TEXT_TAG = 'VIP子账号';
    /** 账号权限-超级权限 */
    const COMPANY_MEMBER_AUTH_SUPER          = 9;
    const COMPANY_MEMBER_AUTH_SUPER_TEXT     = '超管权限';
    const COMPANY_MEMBER_AUTH_SUPER_TEXT_TAG = '超管账号';
    /** 账号标签列表 */
    const COMPANY_MEMBER_AUTH_LIST_TAG = [
        self::COMPANY_MEMBER_AUTH_NORMAL => self::COMPANY_MEMBER_AUTH_NORMAL_TEXT_TAG,
        self::COMPANY_MEMBER_AUTH_VIP    => self::COMPANY_MEMBER_AUTH_VIP_TEXT_TAG,
        self::COMPANY_MEMBER_AUTH_SUPER  => self::COMPANY_MEMBER_AUTH_SUPER_TEXT_TAG,
    ];
    /** 账号权限列表 */
    const COMPANY_MEMBER_AUTH_LIST = [
        self::COMPANY_MEMBER_AUTH_NORMAL => self::COMPANY_MEMBER_AUTH_NORMAL_TEXT,
        self::COMPANY_MEMBER_AUTH_VIP    => self::COMPANY_MEMBER_AUTH_VIP_TEXT,
        self::COMPANY_MEMBER_AUTH_SUPER  => self::COMPANY_MEMBER_AUTH_SUPER_TEXT,
    ];

    /** 创建来源-自主 */
    const SOURCE_TYPE_SELF      = 1;
    const SOURCE_TYPE_SELF_TEXT = '自主创建';
    /** 创建来源-运营 */
    const SOURCE_TYPE_OPERATION      = 2;
    const SOURCE_TYPE_OPERATION_TEXT = '运营创建';
    /** 创建来源列表 */
    const SOURCE_TYPE_LIST = [
        self::SOURCE_TYPE_SELF      => self::SOURCE_TYPE_SELF_TEXT,
        self::SOURCE_TYPE_OPERATION => self::SOURCE_TYPE_OPERATION_TEXT,
    ];

    /** 是否绑定微信-是 */
    const IS_WX_BIND_YES      = 1;
    const IS_WX_BIND_YES_TEXT = '已绑定';
    /** 是否绑定微信-否 */
    const IS_WX_BIND_NO      = 2;
    const IS_WX_BIND_NO_TEXT = '未绑定';
    /** 是否绑定微信列表 */
    const IS_WX_BIND_LIST = [
        self::IS_WX_BIND_YES => self::IS_WX_BIND_YES_TEXT,
        self::IS_WX_BIND_NO  => self::IS_WX_BIND_NO_TEXT,
    ];

    /**
     * 添加账号信息
     */
    public static function add($insert)
    {
        $model              = new BaseCompanyMemberInfo();
        $insert['add_time'] = CUR_DATETIME;
        $model->setAttributes($insert);
        $model->save();

        return $model->save();
    }

    /**
     * 验证邮箱的账号信息是否合法
     * @param       $companyId
     * @param       $email
     * @return false|mixed
     */
    public static function validateEmailMember($companyId, $email)
    {
        //验证当前邮箱是否有对应的子账号
        $query = BaseCompanyMemberInfo::find()
            ->alias('cmi')
            ->leftJoin(['m' => BaseMember::tableName()], 'm.id=cmi.member_id')
            ->andWhere([
                'm.email'                 => $email,
                'cmi.company_id'          => $companyId,
                'cmi.company_member_type' => self::COMPANY_MEMBER_TYPE_SUB,
            ]);

        $member = $query->select([
            'cmi.id',
        ])
            ->asArray()
            ->one();
        if ($member) {
            //存在
            return $member['id'];
        }

        return false;
    }

    /**
     * 验证账号记录ID是否是当前单位的账号
     * @param     $companyId
     * @param     $recordId
     * @param int $isAccount 1主账号 2子账号 3验证时该单位账号
     * @return bool
     */
    public static function validateMemberRecordId($companyId, $recordId, $isAccount = 1)
    {
        //验证当前邮箱是否有对应的子账号
        $query = BaseCompanyMemberInfo::find()
            ->andWhere([
                'id'         => $recordId,
                'company_id' => $companyId,
            ]);
        if ($isAccount == 1) {
            $query->andWhere(['company_member_type' => self::COMPANY_MEMBER_TYPE_MAIN]);
        } elseif ($isAccount == 2) {
            $query->andWhere(['company_member_type' => self::COMPANY_MEMBER_TYPE_SUB]);
        }
        $member = $query->select([
            'id',
            'company_member_type',
        ])
            ->asArray()
            ->one();
        if ($member['id'] > 0) {
            return true;
        }

        return false;
    }

    /**
     * 获取账号信息
     * @param $id
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function getInfoOne($id)
    {
        $data = BaseCompanyMemberInfo::find()
            ->alias('cmi')
            ->select([
                'cmi.id as company_member_info_id',
                'cmi.member_id',
                'cmi.company_member_type',
                'cmi.contact',
                'cmi.department',
                'm.email',
                'm.mobile',
            ])
            ->leftJoin(['m' => BaseMember::tableName()], 'm.id = cmi.member_id')
            ->where(['cmi.id' => $id])
            ->asArray()
            ->one();

        return $data ?: [];
    }

    /**
     * 获取多个账号信息
     * @param $id
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function getInfoMany($ids)
    {
        $data = BaseCompanyMemberInfo::find()
            ->alias('cmi')
            ->select([
                'cmi.id',
                'cmi.member_id',
                'cmi.company_member_type',
                'cmi.contact',
                'cmi.department',
                'm.email',
                'm.mobile',
            ])
            ->leftJoin(['m' => BaseMember::tableName()], 'm.id = cmi.member_id')
            ->where(['cmi.id' => $ids])
            ->asArray()
            ->all();

        return $data;
    }

    /**
     * 获取单位的配置信息
     */
    public static function getCompanyList($params)
    {
        $query = BaseCompany::find()
            ->select([
                'c.full_name as company_name',
                'c.package_type',
                'c.id as company_id',
                'cmc.vip_total',
                'cmc.vip_available',
                'cmc.vip_used',
                'cmc.total',
                'cmc.available',
                'cmc.used',
            ])
            ->alias('c')
            ->leftJoin(['cmc' => BaseCompanyMemberConfig::tableName()], 'cmc.company_id=c.id')
            ->andWhere(['c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES]);
        //搜索单位名称
        if (!empty($params['company_name'])) {
            $query->andFilterWhere([
                'like',
                'c.full_name',
                $params['company_name'],
            ]);
        }
        //limit
        if (!$params['limit']) {
            $params['limit'] = 10;
        }
        $query->limit($params['limit']);
        $data = $query->asArray()
            ->all();
        foreach ($data as &$item) {
            $item['package_type_text']  = BaseCompany::PACKAGE_TYPE_LIST[$item['package_type']];
            $expire_time                = BaseCompanyPackageConfig::findOneVal([
                'company_id' => $item['company_id'],
                'status'     => BaseCompanyPackageConfig::STATUS_ACTIVE,
            ], 'expire_time');
            $item['company_vip_expire'] = $expire_time ? date('Y.m.d', strtotime($expire_time)) : '';
        }

        return $data;
    }

    /**
     * 获取用户单位下所有账号信息
     * @param $companyId
     * @return array
     */
    public static function getAllCompanyMemberInfo($companyId): array
    {
        $list = BaseCompanyMemberInfo::find()
            ->alias('cmi')
            ->leftJoin(['m' => BaseMember::tableName()], 'm.id = cmi.member_id')
            ->select([
                'cmi.member_id',
                'cmi.contact',
                'cmi.department',
            ])
            ->where([
                'cmi.company_id' => $companyId,
                'm.status'       => BaseMember::STATUS_ACTIVE,
            ])
            ->asArray()
            ->all();

        $return = [];
        foreach ($list as $item) {
            $return[] = [
                'k' => $item['member_id'],
                'v' => $item['contact'] . '(' . $item['department'] . ')',
            ];
        }

        return $return;
    }

    /**
     * 获取用户单位账号信息
     * @param $memberId
     * @return array
     */
    public static function getCompanyMemberInfo($memberId): array
    {
        $info = BaseCompanyMemberInfo::find()
            ->alias('cmi')
            ->leftJoin(['m' => BaseMember::tableName()], 'm.id = cmi.member_id')
            ->select([
                'cmi.contact',
                'cmi.department',
                'm.email',
                'm.mobile',
                'm.username',
                'm.avatar',
            ])
            ->where([
                'cmi.member_id' => $memberId,
            ])
            ->asArray()
            ->one();

        $info['avatar'] = FileHelper::getFullUrl($info['avatar']);

        return $info;
    }

    /**
     * 获取用户单位账号信息
     * @param $keywords
     * @return array
     */
    public static function getCompanyMemberInfoList($keywords): array
    {
        $memberId  = Yii::$app->user->id;
        $companyId = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'company_id');
        $select    = [
            'cmi.add_time',
            'cmi.contact',
            'cmi.department',
            'cmi.member_rule',
            'cmi.company_member_type',
            'cmi.member_id',
            'm.status',
            'm.email',
            'm.mobile',
        ];

        $query = BaseCompanyMemberInfo::find()
            ->alias('cmi')
            ->leftJoin(['m' => BaseMember::tableName()], 'm.id = cmi.member_id')
            ->select($select,)
            ->where([
                'cmi.company_id' => $companyId,
            ]);

        $count                 = $query->count();
        $pageSize              = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages                 = self::setPage($count, $keywords['page'], $pageSize);
        $companyMemberInfoList = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('member_rule desc,add_time desc')
            ->asArray()
            ->all();

        foreach ($companyMemberInfoList as &$list) {
            $list['last_login_time']          = BaseMemberActionLog::findOneVal([
                'member_id' => $list['member_id'],
                'is_login'  => 1,
            ], 'add_time');
            $list['member_rule_text']         = BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_LIST[$list['member_rule']];
            $list['company_member_type_text'] = BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_LIST[$list['company_member_type']];
            $list['member_status_text']       = BaseMember::COMPANY_STATUS_LIST[$list['status']];
        }

        /** 子账号资源*/
        $companyMemberConfig  = BaseCompanyMemberConfig::findOne(['company_id' => $companyId]);
        $companyPackageConfig = BaseCompanyPackageConfig::getCompanyRoleMess(['company_id' => $companyId]);

        return [
            'page'      => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
            'resources' => [
                'vip_total'         => $companyMemberConfig->vip_total ?: 0,
                'vip_used'          => $companyMemberConfig->vip_used ?: 0,
                'total'             => $companyMemberConfig->total ?: 0,
                'used'              => $companyMemberConfig->used ?: 0,
                'expire_time'       => $companyPackageConfig['expire_time'] ?: '',
                'package_type'      => $companyPackageConfig['package_type'],
                'package_type_name' => $companyPackageConfig['package_type_name'],
            ],
            'list'      => $companyMemberInfoList,
        ];
    }

    /**
     * 获取用户单位账号信息
     * @param $keywords
     * @return array
     * @throws \yii\db\Exception
     */
    public static function memberAccountFilter($keywords, $needPrimary = 1): array
    {
        //获取单位ID
        $companyId = $keywords['companyId'];
        $contact   = $keywords['contact'];
        $limit     = $keywords['limit'];
        //获取单位下的所有账号
        $query = BaseCompanyMemberInfo::find()
            ->alias('cmi')
            ->leftJoin(['m' => BaseMember::tableName()], 'm.id=cmi.member_id')
            ->andWhere(['cmi.company_id' => $companyId]);

        if ($needPrimary == 2) {
            $query->andWhere([
                '<>',
                'cmi.company_member_type',
                BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN,
            ]);
        }

        if ($contact) {
            $query->andWhere([
                'or',
                [
                    'like',
                    'm.email',
                    $contact,
                ],
                [
                    'like',
                    'cmi.contact',
                    $contact,
                ],
                [
                    'like',
                    'cmi.department',
                    $contact,
                ],
            ]);
        }
        if ($limit) {
            $query->limit($limit);
        }

        $list = $query->select([
            'cmi.id',
            'm.email',
            'm.mobile',
            'cmi.contact',
            'cmi.department',
            'cmi.company_member_type',
        ])
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['contact_text'] = $item['contact'];
            if ($item['department']) {
                $item['contact_text'] .= ' /' . $item['department'];
            }
            $item['job_contact_id'] = $item['id'];
        }

        return $list;
    }

    /**
     * 确认绑定
     */
    public static function confirmBind($memberId)
    {
        $model = BaseCompanyMemberInfo::findOne([
            'member_id' => $memberId,
        ]);
        if (!$model) {
            throw new Exception('参数错误');
        }
        $model->setAttributes([
            'is_wx_bind' => self::IS_WX_BIND_YES,
        ]);
        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        return true;
    }

    /**
     * 取消绑定
     */
    public static function cancelBind($memberId)
    {
        $model = BaseCompanyMemberInfo::findOne([
            'member_id' => $memberId,
        ]);
        if (!$model) {
            throw new Exception('参数错误');
        }
        $model->setAttributes([
            'is_wx_bind' => self::IS_WX_BIND_NO,
        ]);
        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        // 这里还需要把微信的绑定关系给重置了
        $companyBindModel = BaseCompanyMemberWxBind::findOne(['company_member_id' => $memberId]);
        if (!$companyBindModel) {
            return true;
        }

        $companyBindModel->company_member_id = 0;
        $companyBindModel->company_id        = 0;

        if (!$companyBindModel->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        return true;
    }

    /**
     * 用户单位账号信息
     * @throws \yii\base\Exception
     */
    public static function getCompanyMemberInfoByAccount($memberId): array
    {
        $companyMemberInfo = BaseCompanyMemberInfo::find()
            ->select([
                'member_rule',
                'company_id',
            ])
            ->where([
                'member_id' => $memberId,
            ])
            ->asArray()
            ->one();

        $companyPackageConfig = BaseCompanyPackageConfig::getCompanyPackageConfig($companyMemberInfo['company_id']);

        return [
            'memberRule'      => $companyMemberInfo['member_rule'],
            'companyRole'     => $companyPackageConfig['companyRole'],
            'companyRoleName' => $companyPackageConfig['companyRoleName'],
        ];
    }

    public static function setIsRememberData($companyId, $memberId, $field, $isRemember = 1)
    {
        $where = [
            [
                '=',
                'company_id',
                $companyId,
            ],
            [
                '=',
                'member_id',
                $memberId,
            ],
        ];
        $model = BaseCompanyMemberInfo::find()
            ->where(new AndCondition($where))
            ->one();

        $model->$field = $isRemember;

        return $model->save();
    }
}