<?php

namespace common\base\models;

use admin\models\Major;
use common\helpers\ArrayHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\helpers\UUIDHelper;
use common\libs\Cache;
use common\models\Area;
use common\models\Resume;
use common\models\ResumeStatData;
use common\service\CommonService;
use common\service\resume\GetEditInfoService;
use common\service\resume\ResumeService;
use frontendPc\models\CategoryJob;
use frontendPc\models\Dictionary;
use frontendPc\models\Member;
use frontendPc\models\ResumeAcademicBook;
use frontendPc\models\ResumeAcademicPage;
use frontendPc\models\ResumeAcademicPatent;
use frontendPc\models\ResumeAcademicReward;
use frontendPc\models\ResumeAdditionalInfo;
use frontendPc\models\ResumeAttachment;
use frontendPc\models\ResumeCertificate;
use frontendPc\models\ResumeComplete;
use frontendPc\models\ResumeEducation;
use frontendPc\models\ResumeIntention;
use frontendPc\models\ResumeOtherReward;
use frontendPc\models\ResumeOtherSkill;
use frontendPc\models\ResumeResearchDirection;
use frontendPc\models\ResumeResearchProject;
use frontendPc\models\ResumeSetting;
use frontendPc\models\ResumeSkill;
use frontendPc\models\ResumeWork;
use SebastianBergmann\Timer\Timer;
use yii\base\Exception;
use Yii;
use yii\db\conditions\AndCondition;

class BaseResume extends Resume
{
    const STATUS_ACTIVE = 1;
    // 等待审核
    const STATUS_WAIT_AUDIT = 9;
    // 审核拒绝
    const STATUS_REJECT = -1;

    //简历是否完成前三步
    const STATUS_UN_COMPLETE_BASE_INFO = 1;
    const STATUS_COMPLETE_BASE_INFO    = 2;

    //自定义到岗时间类型
    const CUSTOM_ARRIVE_DATE_TYPE = '-1';

    //性别
    const GENDER_MAN   = 1;
    const GENDER_WOMAN = 2;

    const TYPE_ADD       = 1;
    const CACHE_KEY_LIST = [
        self::TYPE_ADD => 'add_member_resume_info',

    ];

    //获取简历类型的名称
    const RESUME_TYPE_ELITE        = 1;
    const RESUME_TYPE_HIGH_QUALITY = 2;
    const RESUME_TYPE_ORDINARY     = 3;

    const RESUME_TYPE_LIST = [
        self:: RESUME_TYPE_ELITE       => '精英简历',
        self::RESUME_TYPE_HIGH_QUALITY => '优质简历',
        self::RESUME_TYPE_ORDINARY     => '普通简历',
    ];

    const MARRIAGE_YES     = 1;
    const MARRIAGE_NO      = 2;
    const MARRIAGE_SECRECY = 3;

    const MARRIAGE_LIST = [
        self::MARRIAGE_YES     => '已婚',
        self::MARRIAGE_NO      => '未婚',
        self::MARRIAGE_SECRECY => '保密',
    ];

    const GENDER_LIST = [
        self::GENDER_MAN   => '男',
        self::GENDER_WOMAN => '女',
    ];

    const IS_ABROAD_YES = 1;
    const IS_ABROAD_NO  = 2;

    const IS_POSTDOC_YES = 1;
    const IS_POSTDOC_NO  = 2;

    const SORT_UPDATE_TIME_DESC = 1;
    const SORT_UPDATE_TIME_ASC  = 2;

    //求职者身份
    const IDENTITY_TYPE_DELETION      = -1;  //缺失
    const IDENTITY_TYPE_WORKER        = 1;  //职场人
    const IDENTITY_TYPE_WORKER_NAME   = '职场人';  //职场人
    const IDENTITY_TYPE_GRADUATE      = 2;  //应届生
    const IDENTITY_TYPE_GRADUATE_NAME = '应届生/在校生';  //应届生

    //身份文案
    const IDENTITY_TEXT_LIST = [
        self::IDENTITY_TYPE_WORKER   => self::IDENTITY_TYPE_WORKER_NAME,
        self::IDENTITY_TYPE_GRADUATE => self::IDENTITY_TYPE_GRADUATE_NAME,
    ];
    // 普通用户
    const VIP_TYPE_NORMAL = 0;
    // 生效会员
    const VIP_TYPE_ACTIVE = 1;
    // 过期会员
    const VIP_TYPE_EXPIRE = -1;

    /** 会员类型列表 */
    const VIP_TYPE_LIST = [
        self::VIP_TYPE_EXPIRE => '过期会员',
        self::VIP_TYPE_NORMAL => '普通用户',
        self::VIP_TYPE_ACTIVE => 'VIP会员',
    ];

    //普通会员
    const VIP_LEVEL_NORMAL = 0;
    //黄金VIP
    const VIP_LEVEL_GOLD = 1;
    //钻石VIP
    const VIP_LEVEL_DIAMOND = 2;
    //普通会员文案
    const VIP_LEVEL_NORMAL_TEXT = '普通会员';
    //黄金VIP文案
    const VIP_LEVEL_GOLD_TEXT = '黄金VIP';
    //钻石VIP文案
    const VIP_LEVEL_DIAMOND_TEXT = '钻石VIP';
    //会员等级列表
    const VIP_LEVEL_LIST = [
        self::VIP_LEVEL_NORMAL  => self::VIP_LEVEL_NORMAL_TEXT,
        self::VIP_LEVEL_GOLD    => self::VIP_LEVEL_GOLD_TEXT,
        self::VIP_LEVEL_DIAMOND => self::VIP_LEVEL_DIAMOND_TEXT,
    ];

    const BUY_URL_VIP               = '/vip.html';
    const BUY_URL_JOB_FAST          = '/job-fast.html';
    const BUY_URL_COMPETITIVE_POWER = '/competitive-power.html';

    const BUY_URL = [
        'vip'              => self::BUY_URL_VIP,
        'jobFast'          => self::BUY_URL_JOB_FAST,
        'competitivePower' => self::BUY_URL_COMPETITIVE_POWER,
    ];

    //是否简历库
    const IS_RESUME_LIBRARY_YES = 1;
    const IS_RESUME_LIBRARY_NO  = 2;

    /**
     * 获取求职者简历完整度
     * @param $memberId int 用户ID
     * @return int|mixed
     */
    public static function getComplete($memberId)
    {
        if ($memberId <= 0) {
            return 0;
        }

        return self::findOneVal(['member_id' => $memberId], 'complete');
    }

    /**
     * @throws \Exception
     */
    public static function getInfo($memberId): array
    {
        $userInfo = self::getUserInfo($memberId);

        //获取用户求职意向
        $intentionList = ResumeIntention::getInfoList($memberId);

        //获取用户教育经历
        $educationList = ResumeEducation::getInfoList($memberId);

        //获取用户工作经历
        $workList = ResumeWork::getInfoList($memberId);

        //获取用户项目经历
        $projectList = ResumeResearchProject::getInfoList($memberId);

        //获取用户学术论文
        $pageList = ResumeAcademicPage::getPageList($memberId);

        //获取用户学术专利
        $patentList = ResumeAcademicPatent::getPatentList($memberId);

        //获取用户学术专著
        $bookList = ResumeAcademicBook::getBookList($memberId);

        //获取用户学术奖励
        $rewardList = ResumeAcademicReward::getRewardList($memberId);

        //获取用户其他荣誉
        $otherRewardList = ResumeOtherReward::getRewardList($memberId);

        //获取用户资质证书
        $certificateList = ResumeCertificate::getCertificateList($memberId);

        //获取用户技能语言
        $skillList = ResumeSkill::getSkillList($memberId);

        //获取用户其他技能
        $otherSkillList = ResumeOtherSkill::getInfoList($memberId);

        //获取用户研究方向
        $researchDirection = ResumeResearchDirection::getResearchDirectionInfo($memberId);

        //获取用户附加信息
        $addInfoList = ResumeAdditionalInfo::getInfoList($memberId);

        //计算简历完成度
        $resumePercent = BaseResume::getComplete($memberId);

        //获取屏蔽单位数量
        $resumeId            = BaseMember::getMainId($memberId);
        $shieldCompanyAmount = BaseShieldCompany::getAmount($resumeId);

        //获取身份信息提示语句
        $tipList = self::getIdentityTipList($resumeId);

        //拼接数据
        $data                        = [];
        $data['userInfo']            = $userInfo;
        $data['intentionList']       = $intentionList;
        $data['educationList']       = $educationList;
        $data['workList']            = $workList;
        $data['projectList']         = $projectList;
        $data['pageList']            = $pageList;
        $data['patentList']          = $patentList;
        $data['bookList']            = $bookList;
        $data['rewardList']          = $rewardList;
        $data['otherRewardList']     = $otherRewardList;
        $data['certificateList']     = $certificateList;
        $data['skillList']           = $skillList;
        $data['otherSkillList']      = $otherSkillList;
        $data['researchDirection']   = $researchDirection['researchDirection'];
        $data['addInfoList']         = $addInfoList;
        $data['resumePercent']       = $resumePercent;
        $data['shieldCompanyAmount'] = $shieldCompanyAmount;
        $data['completePercent']     = Yii::$app->params['completeResumePercent'];
        $data['resumeRate']          = BaseResumeComplete::getRateInfoList($memberId);
        $educationTotal              = count($educationList);//教育经历数量
        $academicTotal               = count($pageList) + count($bookList) + count($patentList);//学术成果数量
        $projectTotal                = count($projectList);//项目经历数量
        $data['isIntention']         = '0';//教育经历
        $data['isAcademic']          = '0';//学术成果
        $data['isProject']           = '0';//项目经历
        $data['isSenior']            = '0';//是否是硕博人才
        $intentionBool               = false;
        $education_id                = 0;
        foreach ($educationList as $val) {
            if ($val['educationId'] == BaseDictionary::EDUCATION_POACEAE_ID) {
                $intentionBool = true;
            }
            if (!in_array($education_id, BaseDictionary::EDUCATION_DOCTORN_AND_MASTER_ID)) {
                $education_id = $val['educationId'];
            }
        }
        if (in_array($education_id, BaseDictionary::EDUCATION_DOCTORN_AND_MASTER_ID)) {
            $data['isSenior'] = '1';
            if ($educationTotal > 0) {
                if ($intentionBool) {
                    $data['isIntention'] = "1";
                }
            }
            if ($academicTotal > 0) {
                $data['isAcademic'] = "1";
            }
            if ($projectTotal > 0) {
                $data['isProject'] = "1";
            }
        }
        $data['tipList'] = $tipList;

        //获取用户完成度提醒
        $data['completeTipsInfo'] = BaseResume::getCompleteTips($memberId);

        return $data;
    }

    /**
     * 更新用户简历完整度
     * @param $memberId int 求职者ID
     * @return int
     */
    public static function updateComplete($memberId)
    {
        // 去到服务让它走after
        $service = new ResumeService();

        return $service->setPlatform(CommonService::PLATFORM_WEB)
            ->setOparetion(ResumeService::OPERATION_TYPE_UPDATE_COMPLETE)
            ->init(['member_id' => $memberId])
            ->run();

        //        if (!$memberId) {
        //            return 0;
        //        }
        //        // todo:暂时写死模块，后期进入配置
        //        $complete_info = BaseResumeComplete::find()
        //            ->where([
        //                'member_id' => $memberId,
        //                'status'    => BaseResumeComplete::STATUS_ACTIVE,
        //            ])
        //            ->select([
        //                'basic',
        //                'education',
        //                'research_direction',
        //                'work',
        //                'intention',
        //                'academic_page',
        //                'academic_patent',
        //                'academic_book',
        //                'research_project',
        //                'academic_reward',
        //                'other_reward',
        //                'certificate',
        //                'skill',
        //                'other_skill',
        //                'advantage',
        //            ])
        //            ->asArray()
        //            ->one();
        //
        //        if (empty($complete_info)) {
        //            return 0;
        //        }
        //        //
        //        $total_num = 0;
        //
        //        $total = 0;
        //        //用户头像
        //        $memberInfo = Member::findone($memberId);
        //        //用户头像不为空，则计算简历完成度
        //        if (!empty($memberInfo) && !empty($memberInfo['avatar'])) {
        //            $total += BaseResumeComplete::RATE_ICON;
        //        }
        //        $hasAcademicInfo = 0;
        //        $hasRewardInfo   = 0;
        //        $hasSkillInfo    = 0;
        //        foreach ($complete_info as $k => $v) {
        //            switch ($k) {
        //                case BaseResumeComplete::MODULE_BASIC://简历基本信息
        //                    if ($v > 0) {
        //                        $total += BaseResumeComplete::RATE_BASIC;
        //                    }
        //                    break;
        //                case BaseResumeComplete::MODULE_EDUCATION://教育经历
        //                    if ($v > 0) {
        //                        $total += BaseResumeComplete::RATE_EDUCATION;
        //                    }
        //                    break;
        //                case BaseResumeComplete::MODULE_RESEARCH_DIRECTION://研究方向
        //                    if ($v > 0) {
        //                        $total += BaseResumeComplete::RATE_RESEARCH_DIRECTION;
        //                    }
        //                    break;
        //                case BaseResumeComplete::MODULE_WORK://工作经历
        //                    if ($v > 0) {
        //                        $total += BaseResumeComplete::RATE_WORK;
        //                    }
        //                    break;
        //                case BaseResumeComplete::MODULE_INTENTION://求职意向
        //                    if ($v > 0) {
        //                        $total += BaseResumeComplete::RATE_INTENTION;
        //                    }
        //                    break;
        //                case BaseResumeComplete::MODULE_ACADEMIC_PAGE://学术论文
        //                    if ($v > 0 && $hasAcademicInfo == 0) {
        //                        $hasAcademicInfo += 1;
        //                        $total           += BaseResumeComplete::RATE_ACADEMIC_PAGE;
        //                    }
        //                    break;
        //                case BaseResumeComplete::MODULE_ACADEMIC_PATENT://学术专利
        //                    if ($v > 0 && $hasAcademicInfo == 0) {
        //                        $hasAcademicInfo += 1;
        //                        $total           += BaseResumeComplete::RATE_ACADEMIC_PATENT;
        //                    }
        //                    break;
        //                case BaseResumeComplete::MODULE_ACADEMIC_BOOK://学术著作
        //                    if ($v > 0 && $hasAcademicInfo == 0) {
        //                        $hasAcademicInfo += 1;
        //                        $total           += BaseResumeComplete::RATE_ACADEMIC_BOOK;
        //                    }
        //                    break;
        //                case BaseResumeComplete::MODULE_RESEARCH_PROJECT:
        //                    if ($v > 0) {
        //                        $total += BaseResumeComplete::RATE_RESEARCH_PROJECT;
        //                    }
        //                    break;
        //                case BaseResumeComplete::MODULE_ACADEMIC_REWARD:
        //                    if ($v > 0 && $hasRewardInfo == 0) {
        //                        $hasRewardInfo += 1;
        //                        $total         += BaseResumeComplete::RATE_ACADEMIC_REWARD;
        //                    }
        //                    break;
        //                case BaseResumeComplete::MODULE_OTHER_REWARD:
        //                    if ($v > 0 && $hasRewardInfo == 0) {
        //                        $hasRewardInfo += 1;
        //                        $total         += BaseResumeComplete::RATE_OTHER_REWARD;
        //                    }
        //                    break;
        //                case BaseResumeComplete::MODULE_CERTIFICATE:
        //                    if ($v > 0 && $hasSkillInfo == 0) {
        //                        $hasSkillInfo += 1;
        //                        $total        += BaseResumeComplete::RATE_CERTIFICATE;
        //                    }
        //                    break;
        //                case BaseResumeComplete::MODULE_SKILL:
        //                    if ($v > 0 && $hasSkillInfo == 0) {
        //                        $hasSkillInfo += 1;
        //                        $total        += BaseResumeComplete::RATE_SKILL;
        //                    }
        //                    break;
        //                case BaseResumeComplete::MODULE_OTHER_SKILL:
        //                    if ($v > 0 && $hasSkillInfo == 0) {
        //                        $hasSkillInfo += 1;
        //                        $total        += BaseResumeComplete::RATE_OTHER_SKILL;
        //                    }
        //                    break;
        //                case BaseResumeComplete::MODULE_ADVANTAGE:
        //                    if ($v > 0) {
        //                        $total += BaseResumeComplete::RATE_ADVANTAGE;
        //                    }
        //                    break;
        //            }
        //        }
        //
        //        return $total;
    }

    /**
     * 获取用户基本信息
     * @param $memberId
     * @throws \Exception
     */
    public static function getUserInfo($memberId)
    {
        //获取用户基本信息
        $userInfo = self::find()
            ->alias('r')
            ->leftJoin(['m' => Member::tableName()], 'r.member_id = m.id')
            ->leftJoin(['e' => ResumeEducation::tableName()], 'e.id = r.last_education_id')
            ->leftJoin(['a' => BaseResumeAttachment::tableName()], 'a.resume_id=r.id')
            ->where([
                'r.member_id' => $memberId,
                'm.type'      => Member::TYPE_PERSON,
            ])
            ->select([
                'r.id as resumeId',
                'r.member_id as memberId',
                'r.name',
                'r.gender',
                'r.birthday',
                'r.status',
                'r.household_register_id as householdRegisterId',
                'r.marriage',
                'r.political_status_id as politicalStatusId',
                'r.english_name as englishName',
                'r.nation_id as nationId',
                'r.residence',
                'r.native_place_area_id as nativePlaceAreaId',
                'r.title_id as titleId',
                'r.advantage',
                'r.arrive_date_type as arriveDateType',
                'r.arrive_date as arriveDate',
                'r.work_status as workStatus',
                'm.mobile',
                'm.mobile_code as mobileCode',
                'm.email',
                'm.avatar',
                'm.username',
                'm.id',
                'm.last_login_time',
                'm.last_active_time',
                'm.email_register_status as emailRegisterStatus',
                'r.last_update_time as refreshTime',
                'r.age',
                'r.work_experience as workExperience',
                'e.school as schoolName',
                'r.last_education_id',
                'e.education_id',
                'a.token',
                'e.is_project_school',
                'e.is_recruitment',
                'major_id',
                'r.identity_type as identityType',
                'r.begin_work_date as beginWorkDate',
                'm.status as memberStatus'
            ])
            ->asArray()
            ->one();
        //        $userInfo['householdRegisterName'] = Dictionary::get();
        //        $userInfo['nativePlaceAreaName'] = Dictionary::get();
        $userInfo['advantage'] = $userInfo['advantage'] ?: '';
        if (empty($userInfo['politicalStatusId'])) {
            //设置默认值
            $userInfo['politicalStatusId'] = '';
        }
        $userInfo['politicalStatusName'] = Dictionary::getPoliticalStatusName($userInfo['politicalStatusId']) ?: '';
        if ($userInfo['titleId'] == 0) {
            $userInfo['titleId'] = '';
        }
        $userInfo['titleName'] = BaseResume::getTitleName($userInfo['titleId']);

        $userInfo['householdRegisterText'] = Area::findOneVal(['id' => $userInfo['householdRegisterId']], 'name') ?: '';
        //如果没有头像，用的是默认头像，加个参数外部判断
        if (empty($userInfo['avatar'])) {
            $userInfo['isDefaultAvatar'] = true;
        } else {
            $userInfo['isDefaultAvatar'] = false;
        }
        $userInfo['avatar']         = BaseMemberLoginForm::getAvatar($userInfo['avatar'], $userInfo['gender']);
        $userInfo['workStatusName'] = Dictionary::getJobStatusName($userInfo['workStatus']) ?: '';
        $userInfo['nationTxt']      = Dictionary::getNationName($userInfo['nationId']) ?: '';
        if ($userInfo['nationId'] == 0) {
            $userInfo['nationId'] = '';
        }
        if ($userInfo['marriage'] == 0) {
            $userInfo['marriage'] = '';
        }
        $userInfo['marriageTxt']        = self::MARRIAGE_LIST[$userInfo['marriage']] ?: '';
        $userInfo['identityText']       = self::IDENTITY_TEXT_LIST[$userInfo['identityType']];
        $userInfo['residenceTxt']       = Area::findOneVal(['id' => $userInfo['residence']], 'name') ?: '';
        $userInfo['nativePlaceAreaTxt'] = Area::findOneVal(['id' => $userInfo['nativePlaceAreaId']], 'name') ?: '';
        $userInfo['genderTxt']          = self::GENDER_LIST[$userInfo['gender']] ?: '';
        $userInfo['majorTxt']           = Major::getMajorName(BaseResumeEducation::findOneVal(['id' => $userInfo['last_education_id']],
            'major_id')) ?: BaseResumeEducation::findOneVal(['id' => $userInfo['last_education_id']], 'major_custom');
        //学历名称
        $userInfo['educationName'] = BaseDictionary::getEducationName($userInfo['education_id']);
        //如果不是自定义的，获取表里数据
        $userInfo['arriveDateTypeName'] = '';
        if ($userInfo['arriveDateType'] != self::CUSTOM_ARRIVE_DATE_TYPE) {
            $userInfo['arriveDateTypeName'] = Dictionary::getArriveDateName($userInfo['arriveDateType']);
        }
        //        $userInfo['workExperience'] = BaseResumeWork::getWorkExperience($memberId);
        //获取用户简历状态
        $userInfo['isHideResume'] = ResumeSetting::findOneVal(['resume_id' => $userInfo['resumeId']], 'is_hide_resume');
        //计算简历完成度
        $userInfo['resumePercent'] = BaseResume::getComplete($memberId);
        //判断用户的简历完成到了哪一步
        if ($userInfo['status'] == self::STATUS_WAIT_AUDIT) {
            $userInfo['resumeStep'] = ResumeComplete::getResumeStep($userInfo['resumeId']);
        }
        if (strtotime($userInfo['birthday']) == '0000-00-00') {
            $userInfo['birthday'] = '';
        }

        $userInfo['uid']              = UUIDHelper::encrypt(UUIDHelper::TYPE_PERSON, $userInfo['id']);
        $userInfo['updateResumeInfo'] = self::getUpdateInfo($userInfo['resumeId']);
        // 获取完整的手机号，海外号段包含区号
        $userInfo['fullMobile'] = BaseMember::getFullMobile($memberId);

        //拼接用户基本信息，根据身份类型，判断回显类型
        //        年龄 | 学历 | 专业 | 毕业时间
        //        年龄 | 学历 | 专业 | 工作经验
        $userInfo['baseInfo'] = [];
        //判断参数，拼接参数
        if (!empty($userInfo['age'])) {
            $userInfo['baseInfo'][] = $userInfo['age'] . '岁';
        }
        if (!empty($userInfo['educationName'])) {
            $userInfo['baseInfo'][] = $userInfo['educationName'];
        }

        if (!empty($userInfo['majorTxt'])) {
            $userInfo['baseInfo'][] = $userInfo['majorTxt'];
        }

        //拼接身份经验
        $identityExperienceText = self::getIdentityExperienceText($userInfo['resumeId']);
        if ($identityExperienceText) {
            $userInfo['baseInfo'][] = $identityExperienceText;
        }

        //        去掉尾部的|符号
        //        $userInfo['baseInfo'] = substr($userInfo['baseInfo'], 0, -3);
        //累赘个字段出来，单位、运营很多地方要用
        $userInfo['identityExperience'] = $identityExperienceText;
        //如果身份为应届生，清空日期，用于前端默认显示
        if ($userInfo['identityType'] == BaseResume::IDENTITY_TYPE_GRADUATE) {
            $userInfo['beginWorkDate'] = '';
        }

        return $userInfo;
    }

    /**
     * 更新简历的数据
     * @param $resumeId
     * @return array
     * @throws \Exception
     */
    public static function getUpdateInfo($resumeId)
    {
        $result = [];
        //获取简历信息
        $resumeInfo = self::find()
            ->where(['id' => $resumeId])
            ->asArray()
            ->one();

        $result['complete'] = $resumeInfo['complete'];
        //标签
        $result['tag'] = [];
        if (!empty($resumeInfo['title_id'])) {
            $titleIdArr = explode(',', $resumeInfo['title_id']);
            foreach ($titleIdArr as $item) {
                $titleName = BaseDictionary::getTitleName($item) ?: '';
                if (!empty($titleName)) {
                    $result['tag'][] = $titleName;
                }
            }
        }
        //工作经验
        $result['identityExperience'] = BaseResume::getIdentityExperienceText($resumeId);

        //教育经历
        $result['educationName'] = BaseDictionary::getEducationName(BaseResumeEducation::findOneVal(['id' => $resumeInfo['last_education_id']],
            'education_id'));

        $educationAbroad = BaseResumeEducation::findOne([
            'member_id' => $resumeInfo['member_id'],
            'is_abroad' => 1,
            'status'    => 1,
        ]);

        $resumeWorkAbroad = BaseResumeWork::findOne([
            'member_id' => $resumeInfo['member_id'],
            'is_abroad' => 1,
            'status'    => 1,
        ]);

        $resumeWorkPostdoc = BaseResumeWork::findOne([
            'member_id'  => $resumeInfo['member_id'],
            'is_postdoc' => 1,
            'status'     => 1,
        ]);

        if ($resumeWorkAbroad || $educationAbroad) {
            $result['tag'][] = '海外经历';
        }

        if ($resumeWorkPostdoc) {
            $result['tag'][] = '博士后经历';
        }

        return $result;
    }

    public static function getGenderName($genderId)
    {
        if ($genderId == self::GENDER_MAN) {
            return '男';
        }

        if ($genderId == self::GENDER_WOMAN) {
            return '女';
        }

        return '';
    }

    /**
     * 简历下载次数增加
     * @param     $resumeId
     * @param int $resumeAttachmentId
     */
    public static function downloadAdd($resumeId, $resumeAttachmentId = 0)
    {
        $model                         = ResumeStatData::findOne(['resume_id' => $resumeId]);
        $model->resume_download_amount = $model->resume_download_amount + 1;
        if (!$model->save()) {
            throw new \Exception('修改失败');
        }
        if ($resumeAttachmentId) {
            $resumeAttachment                  = BaseResumeAttachment::findOne($resumeAttachmentId);
            $resumeAttachment->download_amount = $resumeAttachment->download_amount + 1;
            if (!$resumeAttachment->save()) {
                throw new \Exception('修改失败');
            }
        }
    }

    /**
     * 新增约面次数
     * @param $resumeId
     * @throws \Exception
     */
    public static function updateInterviewAmount($resumeId)
    {
        //统计用户当前状态为约面的总条数
        //站内邀面条数
        $onSiteInterviewAmount = BaseJobApply::find()
            ->where([
                'status'    => BaseJobApply::STATUS_SEND_INVITATION,
                'resume_id' => $resumeId,
            ])
            ->count();
        //站外邀面条数
        $offSiteInterviewAmount           = BaseOffSiteJobApply::find()
            ->where([
                'apply_status' => BaseOffSiteJobApply::APPLY_STATUS_SEND_INVITATION,
                'resume_id'    => $resumeId,
            ])
            ->count();
        $resumeStatData                   = ResumeStatData::findOne(['resume_id' => $resumeId]);
        $resumeStatData->interview_amount = $offSiteInterviewAmount + $onSiteInterviewAmount;
        if (!$resumeStatData->save()) {
            throw new \Exception('修改失败');
        }
    }

    /**
     * 新增约面记录总条数
     * @param $resumeId
     * @throws \Exception
     */
    public static function updateInterviewRecordAmount($resumeId, $step = '1')
    {
        $count                                   = BaseCompanyInterview::find()
            ->alias('ci')
            ->leftJoin(['ja' => BaseJobApply::tableName()], 'ja.id=ci.job_apply_id')
            ->where(['resume_id' => $resumeId])
            ->count();
        $resumeStatData                          = ResumeStatData::findOne(['resume_id' => $resumeId]);
        $resumeStatData->interview_record_amount = $count;
        if (!$resumeStatData->save()) {
            throw new \Exception('修改失败');
        }
    }

    /**
     * 新增投递次数
     * @param $resumeId
     * @param $resumeAttachmentId
     * @throws \Exception
     */
    public static function OnSiteApplyAdd($resumeId, $resumeAttachmentId)
    {
        $model                       = ResumeStatData::findOne(['resume_id' => $resumeId]);
        $model->on_site_apply_amount = $model->on_site_apply_amount + 1;
        if (!$model->save()) {
            throw new \Exception('修改失败');
        }
        if ($resumeAttachmentId) {
            $resumeAttachment                       = BaseResumeAttachment::findOne($resumeAttachmentId);
            $resumeAttachment->on_site_apply_amount = $resumeAttachment->on_site_apply_amount + 1;
            if (!$resumeAttachment->save()) {
                throw new \Exception('修改失败');
            }
        }
    }

    /**
     * 新增站外投递次数
     * @param $resumeId
     * @throws \Exception
     */
    public static function offSiteApplyAdd($resumeId, $resumeAttachmentId)
    {
        $model                        = ResumeStatData::findOne(['resume_id' => $resumeId]);
        $model->off_site_apply_amount = $model->off_site_apply_amount + 1;
        if (!$model->save()) {
            throw new \Exception('修改失败');
        }
        if ($resumeAttachmentId) {
            $resumeAttachment                        = BaseResumeAttachment::findOne($resumeAttachmentId);
            $resumeAttachment->off_site_apply_amount = $resumeAttachment->off_site_apply_amount + 1;
            if (!$resumeAttachment->save()) {
                throw new \Exception('修改失败');
            }
        }
    }

    //    /**
    //     * 保存用户基本信息
    //     * @param $data
    //     */
    public static function saveUserBaseInfo($data)
    {
        $service = new ResumeService();
        $service->setPlatform(CommonService::PLATFORM_MINI)
            ->setOparetion(ResumeService::OPERATION_TYPE_SAVE_RESUME_STEP_ONE)
            ->init()
            ->run();
    }
    /**
     * 保存用户基本信息
     * @param $data
     */
    // public static function saveUserBaseInfo($data)
    // {
    //     $memberId = $data['memberId'];
    //     $model    = self::findOne(['member_id' => $memberId]);
    //     //对基本信息参数进行校验
    //     if (strlen($data['name']) < 1 || empty($data['gender']) || empty($data['birthday']) || empty($data['householdRegisterId']) || empty($data['mobile']) || empty($data['email']) || empty($data['politicalStatusId'])) {
    //         throw new Exception('基本信息缺失必填参数！');
    //     }
    //     if (!empty($data['mobile'])) {
    //         //如果有手机号，校验是否和用户进行绑定了
    //         $memberMobile = BaseMember::getMemberField($memberId, 'mobile');
    //         if ($memberMobile != $data['mobile']) {
    //             throw new Exception('手机号暂未通过绑定');
    //         }
    //     }
    //     $age          = TimeHelper::countYears($data['birthday']);
    //     $birthdayCode = date('md', strtotime($data['birthday']));
    //
    //     $model->name                  = $data['name'];
    //     $model->gender                = $data['gender'];
    //     $model->birthday              = TimeHelper::formatAddDay($data['birthday']);
    //     $model->age                   = $age;
    //     $model->birthday_code         = $birthdayCode;
    //     $model->household_register_id = $data['householdRegisterId'];
    //     $model->political_status_id   = $data['politicalStatusId'];
    //
    //     if (!$model->save()) {
    //         throw new Exception($model->getFirstErrorsMessage());
    //     }
    //
    //     //更新用户的邮箱
    //     if (!empty($data['email'])) {
    //         //判断邮箱是否被占用了
    //         //            $memberInfo = BaseMember::find()
    //         //                ->where(['email' => $data['email']])
    //         //                ->andWhere(['type' => BaseMember::TYPE_PERSON])
    //         //                ->select('id')
    //         //                ->asArray()
    //         //                ->one();
    //         //            if (!empty($memberInfo['id']) && $memberInfo['id'] != $memberId) {
    //         //                throw new Exception('邮箱地址已被占用！');
    //         //            }
    //         $memberModel = BaseMember::findOne($memberId);
    //
    //         $memberModel->email = $data['email'];
    //         $memberModel->save();
    //
    //         if (!$memberModel->save()) {
    //             throw new Exception($memberModel->getFirstErrorsMessage());
    //         }
    //     }
    //     //新增操作日志
    //     $data = [
    //         'content' => '保存简历用户基本信息，memberId：' . $memberId,
    //     ];
    //     // 写登录日志
    //     BaseMemberActionLog::log($data);
    //
    //     //更新用户简历完成度表
    //     BaseResumeComplete::updateResumeCompleteInfo($memberId);
    //     //更新简历最后更新时间
    //     BaseResume::updateLastUpdateTime($model->id);
    // }

    /**
     * 保存求职意向的到岗时间和求职状态
     * @param $data
     * @throws Exception
     */
    public static function saveIntentionInfo($data)
    {
        $memberId = \Yii::$app->user->id;
        $model    = self::findOne(['member_id' => $memberId]);
        if (empty($data['arriveDateType']) || empty($data['workStatus'])) {
            throw new Exception('缺失必填参数');
        }

        if ($data['arriveDateType'] == self::CUSTOM_ARRIVE_DATE_TYPE) {
            //自定义的情况
            if (!empty($data['arriveDate'])) {
                $model->arrive_date = TimeHelper::formatAddDay($data['arriveDate']);
            } else {
                throw new Exception('自定义到岗时间不能为空');
            }
        }
        $model->arrive_date_type = $data['arriveDateType'];
        $model->work_status      = $data['workStatus'];

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //新增操作日志
        $data = [
            'content' => '保存简历第三步信息，memberId：' . $memberId,
        ];
        // 写登录日志
        BaseMemberActionLog::log($data);
    }

    /**
     * 获取用户基本信息（简历第一步数据）
     * @return array|\yii\db\ActiveRecord|null
     */
    // public static function getUserBaseInfo()
    // {
    //     $service = new ResumeService();
    //
    //     return $service->setPlatform(CommonService::PLATFORM_MINI)
    //         ->setOparetion(ResumeService::OPERATION_TYPE_GET_RESUME_STEP_ONE)
    //         ->init()
    //         ->run();
    // }

    public static function getJobTrend($memberId)
    {
        $resumeId = BaseMember::getMainId($memberId);
        // 在这里多加一些信息,用户全部已投递(站内投递数量), 职位邀约全部数量,谁看过我全部数量,收藏关注全部数量
        $userInfo['jobApplyAmount'] = BaseJobApplyRecord::find()
                ->where(['resume_id' => $resumeId])
                ->count() * 1;

        $userInfo['jobInviteAmount'] = BaseResumeLibraryInviteLog::find()
                ->where(['resume_id' => $resumeId])
                ->count() * 1 + BaseAdminJobInvite::find()
                ->where(['resume_id' => $resumeId])
                ->count() * 1;

        $userInfo['resumeViewAmount'] = BaseCompanyViewResume::find()
                ->where(['resume_id' => $resumeId])
                ->count() * 1;

        $userInfo['collectAmount'] = BaseJobCollect::find()
                ->where([
                    'member_Id' => $memberId,
                    'status'    => BaseJobCollect::STATUS_ACTIVE,
                ])
                ->count() + BaseAnnouncementCollect::find()
                ->where([
                    'member_Id' => $memberId,
                    'status'    => BaseAnnouncementCollect::STATUS_ACTIVE,
                ])
                ->count() + BaseCompanyCollect::find()
                ->where([
                    'member_Id' => $memberId,
                    'status'    => BaseCompanyCollect::STATUS_ACTIVE,
                ])
                ->count() + BaseNewsCollect::find()
                ->where([
                    'member_Id' => $memberId,
                    'status'    => BaseNewsCollect::STATUS_ACTIVE,
                ])
                ->count();

        // 获取足迹相关数据(90天内的)       
        $ninetyDay                   = date('Y-m-d', strtotime('-90 days'));
        $userInfo['footprintAmount'] = BaseResumeAnnouncementFootprint::find()
                ->where([
                    'resume_id' => $resumeId,
                ])
                ->andWhere([
                    '>=',
                    'date',
                    $ninetyDay,
                ])
                ->count() + BaseResumeJobFootprint::find()
                ->where([
                    'resume_id' => $resumeId,
                ])
                ->andWhere([
                    '>=',
                    'date',
                    $ninetyDay,
                ])
                ->count();

        return $userInfo;
    }

    public static function checkResumeInfo($memberId)
    {
        //获取用户在线简历内容
        $data = self::getInfo($memberId);

        //获取用户最近应聘职位名称
        $data['lastApplyJobInfo'] = BaseJobApply::find()
            ->where(['resume_member_id' => $memberId])
            ->select([
                'job_name',
                'add_time',
            ])
            ->orderBy('add_time desc')
            ->asArray()
            ->one();
        $data['lastApplyJobName'] = ArrayHelper::getValue($data['lastApplyJobInfo'], 'job_name');
        //最近一次投递时间
        $data['lastApplyJobTime'] = ArrayHelper::getValue($data['lastApplyJobInfo'], 'add_time');
        //最近一次更新简历时间
        $resumeInfo                   = Resume::find()
            ->where(['member_id' => $memberId])
            ->select([
                'last_update_time as update_time',
                'id',
            ])
            ->asArray()
            ->one();
        $data['lastUpdateResumeTime'] = ArrayHelper::getValue($resumeInfo, 'update_time');
        //获取简历设置
        $data['resumeSettingInfo'] = ResumeSetting::getMemberSettingInfo($resumeInfo['id']);
        //获取附件简历信息
        $data['resumeAttachmentInfo'] = ResumeAttachment::getList($memberId);
        //获取日程列表
        $today                = date('Y-m-d', time());
        $scheduleSearchData   = [
            'date_start' => $today,
            'date_end'   => $today,
        ];
        $scheduleList         = BaseMemberSchedule::getMemberSchedule($scheduleSearchData, $memberId);
        $data['scheduleInfo'] = [
            'date' => $today,
            'list' => $scheduleList,
        ];

        return $data;
    }

    /**
     * 更新简历最后更新时间
     * @param $resumeId
     * @throws Exception
     */
    public static function updateLastUpdateTime($resumeId)
    {
        $service = new ResumeService();
        //平台设置没有用 随便给一个
        $service->setPlatform(CommonService::PLATFORM_WEB)
            ->setOparetion(ResumeService::OPERATION_TYPE_UPDATE_LAST_UPDATE_TIME)
            ->init(['resume_id' => $resumeId])
            ->run();
        //        $model                   = self::findOne($resumeId);
        //        $model->last_update_time = date('Y-m-d H:i:s', time());
        //
        //        if (!$model->save()) {
        //            throw new Exception($model->getFirstErrorsMessage());
        //        }
    }

    /**
     * 更新用户简历状态
     * @throws Exception
     */
    public static function updateUserRealStatus()
    {
        $memberId = \Yii::$app->user->id;
        if (empty($memberId)) {
            throw new Exception('用户登录状态错误');
        }

        $resumeId = BaseMember::getMainId($memberId);
        //判断用户当前是否已经完成前三步
        $resumeStepNum = BaseResumeComplete::getResumeStep($resumeId);
        if ($resumeStepNum < 3) {
            throw new Exception('当前用户简历未完善');
        }

        //更新用户状态
        $resumeModel = self::findOne(['member_id' => $memberId]);

        $resumeModel->status = self::STATUS_ACTIVE;
        $resumeModel->save();

        BaseMember::updateUserRealStatus(self::STATUS_ACTIVE, $memberId);
    }

    /**
     * 获取教育经历
     * @param $resumeId
     * @return mixed|string
     * @throws \Exception
     */
    public static function getEducation($resumeId)
    {
        $lastId = self::findOneVal(['id' => $resumeId], 'last_education_id');
        if (!$lastId) {
            return '';
        }

        $educationCode = ResumeEducation::findOneVal(['id' => $lastId], 'education_id');

        return BaseDictionary::getDataName(BaseDictionary::TYPE_EDUCATION, $educationCode);
    }

    /**
     * 获取多个职称
     * @param $titleId
     * @return string
     * @throws \Exception
     */
    public static function getTitleName($titleId): string
    {
        $ids = array_filter(explode(',', $titleId));
        if (!$ids) {
            return '';
        }

        $result = '';
        foreach ($ids as $id) {
            $result .= Dictionary::getTitleName($id) . ',';
        }

        return substr($result, 0, -1);
    }

    /**
     * 简历对应用户表一对一关系
     * resume对member
     * @return \yii\db\ActiveQuery
     */
    public function getMember()
    {
        return $this->hasOne(BaseMember::class, ['id' => 'member_id'])
            ->select([
                "id",
                "update_time",
                "status",
                "type",
                "username",
                "email",
                "mobile_code",
                "mobile",
                "avatar",
                "last_login_time",
                "last_login_ip",
                "email_register_status",
                "source_type",
            ]);
    }

    /**
     * * 简历对应求职意向一对多关系
     * resume对resume_intention
     * @return \yii\db\ActiveQuery
     */
    public function getResumeIntention()
    {
        return $this->hasMany(BaseResumeIntention::class, ['resume_id' => 'id'])
            ->select([
                'id',
                'resume_id',
                'nature_type as natureType',
                'job_category_id as jobCategoryId',
                'area_id as areaId',
                'wage_type as wageType',
            ])
            ->andWhere(['status' => BaseResumeIntention::STATUS_ACTIVE])
            ->orderBy('add_time desc');
    }

    /**
     * 简历对应教育经历一对多关系
     * resume对resume_education
     * @return \yii\db\ActiveQuery
     */
    public function getResumeEducation()
    {
        return $this->hasMany(BaseResumeEducation::class, ['resume_id' => 'id'])
            ->select([
                'id',
                'resume_id',
                'begin_date as studyBeginDate',
                'end_date as studyEndDate',
                'is_abroad as isOverseasStudy',
                'school',
                'college',
                'is_recruitment as isRecruitment',
                'education_id as educationId',
                'major_id as majorId',
                'is_project_school as isProjectSchool',
                'major_custom as majorCustom',
            ])
            ->andWhere(['status' => BaseResumeEducation::STATUS_ACTIVE])
            ->orderBy('end_date desc,id desc');
    }

    /**
     * 简历对应工作经历一对多关系
     * resume对resume_work
     * @return \yii\db\ActiveQuery
     */
    public function getResumeWork()
    {
        return $this->hasMany(BaseResumeWork::class, ['resume_id' => 'id'])
            ->select([
                'id',
                'resume_id',
                'begin_date as jobBeginDate',
                'end_date as jobEndDate',
                'is_abroad as isAbroad',
                'is_postdoc as isPostdoc',
                'company',
                'job_name as jobName',
                'department',
                'content as jobContent',
            ])
            ->orderBy('begin_date desc,id desc');
    }

    /**
     * 获取人才库关键词查询简历结果
     * @param $keyword
     * @return array|int[]|string[]
     */
    public static function getIdsByKeyword($keyword): array
    {
        $ids = [];
        //教育经历】-学校名称
        $educationIds = BaseResumeEducation::find()
            ->alias('e')
            ->innerJoin(['rl' => BaseResumeLibrary::tableName()], 'rl.resume_id=e.resume_id')
            ->where([
                'like',
                'school',
                $keyword,
            ])
            ->andWhere(['e.status' => BaseResumeEducation::STATUS_ACTIVE])
            ->select('e.resume_id')
            ->asArray()
            ->groupBy('e.resume_id')
            ->indexBy('e.resume_id')
            ->column();
        $ids          = array_merge($ids, array_keys($educationIds));

        //研究方向
        $researchDirectionIds = BaseResumeResearchDirection::find()
            ->alias('rd')
            ->innerJoin(['rl' => BaseResumeLibrary::tableName()], 'rl.resume_id=rd.resume_id')
            ->where([
                'like',
                'content',
                $keyword,
            ])
            ->andWhere(['rd.status' => BaseResumeResearchDirection::STATUS_ACTIVE])
            ->select('rd.resume_id')
            ->asArray()
            ->groupBy('rd.resume_id')
            ->indexBy('rd.resume_id')
            ->column();

        $ids = array_merge($ids, array_keys($researchDirectionIds));

        //工作经历，单位名称/职位名称/工作内容
        $workIds = BaseResumeWork::find()
            ->alias('w')
            ->innerJoin(['rl' => BaseResumeLibrary::tableName()], 'rl.resume_id=w.resume_id')
            ->where([
                'like',
                'w.company',
                $keyword,
            ])
            ->orWhere([
                'like',
                'w.content',
                $keyword,
            ])
            ->orWhere([
                'like',
                'w.job_name',
                $keyword,
            ])
            ->andWhere(['w.status' => BaseResumeWork::STATUS_ACTIVE])
            ->select('w.resume_id')
            ->asArray()
            ->groupBy('w.resume_id')
            ->indexBy('w.resume_id')
            ->column();

        $ids = array_merge($ids, array_keys($workIds));

        //科研项目，项目名称/所属单位/项目描述
        $researchProjectIds = BaseResumeResearchProject::find()
            ->alias('rp')
            ->innerJoin(['rl' => BaseResumeLibrary::tableName()], 'rl.resume_id=rp.resume_id')
            ->where([
                'like',
                'rp.name',
                $keyword,
            ])
            ->orWhere([
                'like',
                'rp.company',
                $keyword,
            ])
            ->orWhere([
                'like',
                'rp.description',
                $keyword,
            ])
            ->andWhere(['rp.status' => BaseResumeResearchProject::STATUS_ACTIVE])
            ->select('rp.resume_id')
            ->asArray()
            ->groupBy('rp.resume_id')
            ->indexBy('rp.resume_id')
            ->column();

        $ids = array_merge($ids, array_keys($researchProjectIds));

        //【学术论文】-论文题目/论文描述、
        $pageIds = BaseResumeAcademicPage::find()
            ->alias('ra')
            ->innerJoin(['rl' => BaseResumeLibrary::tableName()], 'rl.resume_id=ra.resume_id')
            ->where([
                'like',
                'ra.title',
                $keyword,
            ])
            ->orWhere([
                'like',
                'ra.description',
                $keyword,
            ])
            ->andWhere(['ra.status' => BaseResumeAcademicPage::STATUS_ACTIVE])
            ->select('ra.resume_id')
            ->asArray()
            ->groupBy('ra.resume_id')
            ->indexBy('ra.resume_id')
            ->column();

        $ids = array_merge($ids, array_keys($pageIds));

        //【学术专利】-专利名称/专利描述、
        $patentIds = BaseResumeAcademicPatent::find()
            ->alias('ap')
            ->innerJoin(['rl' => BaseResumeLibrary::tableName()], 'rl.resume_id=ap.resume_id')
            ->where([
                'like',
                'ap.name',
                $keyword,
            ])
            ->orWhere([
                'like',
                'ap.description',
                $keyword,
            ])
            ->andWhere(['ap.status' => BaseResumeAcademicPatent::STATUS_ACTIVE])
            ->select('ap.resume_id')
            ->asArray()
            ->indexBy('ap.resume_id')
            ->groupBy('ap.resume_id')
            ->column();

        $ids = array_merge($ids, array_keys($patentIds));

        //【学术专著】-著作名称
        $bookIds = BaseResumeAcademicBook::find()
            ->alias('ab')
            ->innerJoin(['rl' => BaseResumeLibrary::tableName()], 'rl.resume_id=ab.resume_id')
            ->where([
                'like',
                'name',
                $keyword,
            ])
            ->andWhere(['ab.status' => BaseResumeAcademicBook::STATUS_ACTIVE])
            ->select('ab.resume_id')
            ->asArray()
            ->indexBy('ab.resume_id')
            ->groupBy('ab.resume_id')
            ->column();

        $ids = array_merge($ids, array_keys($bookIds));

        //【资质证书】-证书名称、
        //先查下证书表有没有内容相似
        $certificateList = BaseCertificate::find()
            ->where([
                'like',
                'name',
                $keyword,
            ])
            ->andWhere(['status' => BaseCertificate::STATUS_ACTIVE])
            ->select('id')
            ->asArray()
            ->indexBy('id')
            ->all();
        $certificateIds  = array_keys($certificateList);
        $query           = BaseResumeCertificate::find()
            ->alias('rc')
            ->innerJoin(['rl' => BaseResumeLibrary::tableName()], 'rl.resume_id=rc.resume_id')
            ->where([
                'like',
                'rc.certificate_custom',
                $keyword,
            ]);
        if (!empty($certificateIds)) {
            $query->orWhere(['rc.certificate_id' => $certificateIds]);
        }
        $query->andWhere(['rc.status' => BaseCertificate::STATUS_ACTIVE]);

        $certificateResumeIds = $query->select('rc.resume_id')
            ->asArray()
            ->indexBy('rc.resume_id')
            ->groupBy('rc.resume_id')
            ->column();

        $ids = array_merge($ids, array_keys($certificateResumeIds));

        //【技能/语言】-技能/语言名称、技能包括2个表，其他技能和技能表
        //其他技能表的
        $otherSkillIds = BaseResumeOtherSkill::find()
            ->alias('os')
            ->innerJoin(['rl' => BaseResumeLibrary::tableName()], 'rl.resume_id=os.resume_id')
            ->where([
                'like',
                'os.name',
                $keyword,
            ])
            ->andWhere(['os.status' => BaseResumeOtherSkill::STATUS_ACTIVE])
            ->select('os.resume_id')
            ->asArray()
            ->indexBy('os.resume_id')
            ->groupBy('os.resume_id')
            ->column();

        $ids = array_merge($ids, array_keys($otherSkillIds));

        //技能表的
        //先查下技能表符合的id
        $skillIds = BaseSkill::find()
            ->where([
                'like',
                'name',
                $keyword,
            ])
            ->andWhere(['status' => BaseSkill::STATUS_ACTIVE])
            ->select('id')
            ->asArray()
            ->indexBy('id')
            ->all();
        if (!empty($skillIds)) {
            $skillResumeIds = BaseResumeSkill::find()
                ->alias('rs')
                ->innerJoin(['rl' => BaseResumeLibrary::tableName()], 'rl.resume_id=rs.resume_id')
                ->where(['rs.skill_id' => $skillIds])
                ->select('rs.resume_id')
                ->asArray()
                ->indexBy('rs.resume_id')
                ->groupBy('rs.resume_id')
                ->column();

            $ids = array_merge($ids, array_keys($skillResumeIds));
        }
        //【个人优势】
        $resumeIds = BaseResume::find()
            ->alias('r')
            ->innerJoin(['rl' => BaseResumeLibrary::tableName()], 'rl.resume_id=r.id')
            ->where([
                'like',
                'r.advantage',
                $keyword,
            ])
            ->andWhere(['r.status' => BaseResume::STATUS_ACTIVE])
            ->orWhere([
                'like',
                'r.name',
                $keyword,
            ])
            ->select('r.id')
            ->asArray()
            ->indexBy('r.id')
            ->column();

        $ids = array_merge($ids, array_keys($resumeIds));

        if (count($ids) > 0) {
            //合并
            $ids = array_keys(array_flip($ids));
        }

        return $ids;
    }

    /**
     * 获取单位简历库查询关键词简历结果
     * @param $keyword
     * @return array|int[]|string[]
     */
    public static function getIdsByKeywordCompanyLibrary($keyword): array
    {
        $ids = [];
        //教育经历】-学校名称
        $educationIds = BaseResumeEducation::find()
            ->alias('e')
            ->innerJoin(['crl' => BaseCompanyResumeLibrary::tableName()], 'crl.resume_id=e.resume_id')
            ->where([
                'like',
                'school',
                $keyword,
            ])
            ->andWhere(['e.status' => BaseResumeEducation::STATUS_ACTIVE])
            ->select('e.resume_id')
            ->asArray()
            ->groupBy('e.resume_id')
            ->indexBy('e.resume_id')
            ->column();
        $ids          = array_merge($ids, array_keys($educationIds));

        //研究方向
        $researchDirectionIds = BaseResumeResearchDirection::find()
            ->alias('rd')
            ->innerJoin(['crl' => BaseCompanyResumeLibrary::tableName()], 'crl.resume_id=rd.resume_id')
            ->where([
                'like',
                'content',
                $keyword,
            ])
            ->andWhere(['rd.status' => BaseResumeResearchDirection::STATUS_ACTIVE])
            ->select('rd.resume_id')
            ->asArray()
            ->groupBy('rd.resume_id')
            ->indexBy('rd.resume_id')
            ->column();

        $ids = array_merge($ids, array_keys($researchDirectionIds));

        //工作经历，单位名称/职位名称/工作内容
        $workIds = BaseResumeWork::find()
            ->alias('w')
            ->innerJoin(['crl' => BaseCompanyResumeLibrary::tableName()], 'crl.resume_id=w.resume_id')
            ->where([
                'like',
                'w.company',
                $keyword,
            ])
            ->orWhere([
                'like',
                'w.content',
                $keyword,
            ])
            ->orWhere([
                'like',
                'w.job_name',
                $keyword,
            ])
            ->andWhere(['w.status' => BaseResumeWork::STATUS_ACTIVE])
            ->select('w.resume_id')
            ->asArray()
            ->groupBy('w.resume_id')
            ->indexBy('w.resume_id')
            ->column();

        $ids = array_merge($ids, array_keys($workIds));

        //科研项目，项目名称/所属单位/项目描述
        $researchProjectIds = BaseResumeResearchProject::find()
            ->alias('rp')
            ->innerJoin(['crl' => BaseCompanyResumeLibrary::tableName()], 'crl.resume_id=rp.resume_id')
            ->where([
                'like',
                'rp.name',
                $keyword,
            ])
            ->orWhere([
                'like',
                'rp.company',
                $keyword,
            ])
            ->orWhere([
                'like',
                'rp.description',
                $keyword,
            ])
            ->andWhere(['rp.status' => BaseResumeResearchProject::STATUS_ACTIVE])
            ->select('rp.resume_id')
            ->asArray()
            ->groupBy('rp.resume_id')
            ->indexBy('rp.resume_id')
            ->column();

        $ids = array_merge($ids, array_keys($researchProjectIds));

        //【学术论文】-论文题目/论文描述、
        $pageIds = BaseResumeAcademicPage::find()
            ->alias('ra')
            ->innerJoin(['crl' => BaseCompanyResumeLibrary::tableName()], 'crl.resume_id=ra.resume_id')
            ->where([
                'like',
                'ra.title',
                $keyword,
            ])
            ->orWhere([
                'like',
                'ra.description',
                $keyword,
            ])
            ->andWhere(['ra.status' => BaseResumeAcademicPage::STATUS_ACTIVE])
            ->select('ra.resume_id')
            ->asArray()
            ->groupBy('ra.resume_id')
            ->indexBy('ra.resume_id')
            ->column();

        $ids = array_merge($ids, array_keys($pageIds));

        //【学术专利】-专利名称/专利描述、
        $patentIds = BaseResumeAcademicPatent::find()
            ->alias('ap')
            ->innerJoin(['crl' => BaseCompanyResumeLibrary::tableName()], 'crl.resume_id=ap.resume_id')
            ->where([
                'like',
                'ap.name',
                $keyword,
            ])
            ->orWhere([
                'like',
                'ap.description',
                $keyword,
            ])
            ->andWhere(['ap.status' => BaseResumeAcademicPatent::STATUS_ACTIVE])
            ->select('ap.resume_id')
            ->asArray()
            ->indexBy('ap.resume_id')
            ->groupBy('ap.resume_id')
            ->column();

        $ids = array_merge($ids, array_keys($patentIds));

        //【学术专著】-著作名称
        $bookIds = BaseResumeAcademicBook::find()
            ->alias('ab')
            ->innerJoin(['crl' => BaseCompanyResumeLibrary::tableName()], 'crl.resume_id=ab.resume_id')
            ->where([
                'like',
                'name',
                $keyword,
            ])
            ->andWhere(['ab.status' => BaseResumeAcademicBook::STATUS_ACTIVE])
            ->select('ab.resume_id')
            ->asArray()
            ->indexBy('ab.resume_id')
            ->groupBy('ab.resume_id')
            ->column();

        $ids = array_merge($ids, array_keys($bookIds));

        //【资质证书】-证书名称、
        //先查下证书表有没有内容相似
        $certificateList = BaseCertificate::find()
            ->where([
                'like',
                'name',
                $keyword,
            ])
            ->andWhere(['status' => BaseCertificate::STATUS_ACTIVE])
            ->select('id')
            ->asArray()
            ->indexBy('id')
            ->all();
        $certificateIds  = array_keys($certificateList);
        $query           = BaseResumeCertificate::find()
            ->alias('rc')
            ->innerJoin(['crl' => BaseCompanyResumeLibrary::tableName()], 'crl.resume_id=rc.resume_id')
            ->where([
                'like',
                'rc.certificate_custom',
                $keyword,
            ]);
        if (!empty($certificateIds)) {
            $query->orWhere(['rc.certificate_id' => $certificateIds]);
        }
        $query->andWhere(['rc.status' => BaseCertificate::STATUS_ACTIVE]);

        $certificateResumeIds = $query->select('rc.resume_id')
            ->asArray()
            ->indexBy('rc.resume_id')
            ->groupBy('rc.resume_id')
            ->column();

        $ids = array_merge($ids, array_keys($certificateResumeIds));

        //【技能/语言】-技能/语言名称、技能包括2个表，其他技能和技能表
        //其他技能表的
        $otherSkillIds = BaseResumeOtherSkill::find()
            ->alias('os')
            ->innerJoin(['crl' => BaseCompanyResumeLibrary::tableName()], 'crl.resume_id=os.resume_id')
            ->where([
                'like',
                'os.name',
                $keyword,
            ])
            ->andWhere(['os.status' => BaseResumeOtherSkill::STATUS_ACTIVE])
            ->select('os.resume_id')
            ->asArray()
            ->indexBy('os.resume_id')
            ->groupBy('os.resume_id')
            ->column();

        $ids = array_merge($ids, array_keys($otherSkillIds));

        //技能表的
        //先查下技能表符合的id
        $skillIds = BaseSkill::find()
            ->where([
                'like',
                'name',
                $keyword,
            ])
            ->andWhere(['status' => BaseSkill::STATUS_ACTIVE])
            ->select('id')
            ->asArray()
            ->indexBy('id')
            ->all();
        if (!empty($skillIds)) {
            $skillResumeIds = BaseResumeSkill::find()
                ->alias('rs')
                ->innerJoin(['crl' => BaseCompanyResumeLibrary::tableName()], 'crl.resume_id=rs.resume_id')
                ->where(['rs.skill_id' => $skillIds])
                ->select('rs.resume_id')
                ->asArray()
                ->indexBy('rs.resume_id')
                ->groupBy('rs.resume_id')
                ->column();

            $ids = array_merge($ids, array_keys($skillResumeIds));
        }
        //【个人优势】
        $resumeIds = BaseResume::find()
            ->alias('r')
            ->innerJoin(['crl' => BaseCompanyResumeLibrary::tableName()], 'crl.resume_id=r.id')
            ->where([
                'like',
                'r.advantage',
                $keyword,
            ])
            ->andWhere(['r.status' => BaseResume::STATUS_ACTIVE])
            ->orWhere([
                'like',
                'r.name',
                $keyword,
            ])
            ->select('r.id')
            ->asArray()
            ->indexBy('r.id')
            ->column();

        $ids = array_merge($ids, array_keys($resumeIds));

        if (count($ids) > 0) {
            //合并
            $ids = array_keys(array_flip($ids));
        }

        return $ids;
    }

    public static function getMixAndMaxAge($type)
    {
        // 找到对应的内容
        $name = BaseDictionary::findOneVal([
            'code' => $type,
            'type' => BaseDictionary::TYPE_AGE,
        ], 'name');

        if (empty($name)) {
            throw new Exception('年龄选择错误');
        }

        if ($name == '40岁以上') {
            $min = 41;
            $max = 0;
        } else {
            // 其余的都是有最大和最小的了
            $wageArr = explode('-', str_replace('岁', '', $name));
            $min     = $wageArr[0];
            $max     = $wageArr[1];
        }

        return [
            'min' => $min,
            'max' => $max,
        ];
    }

    /**
     * 获取单位简历库某个求职者的所有附件列表（投递到该单位职位下的材料）
     */
    public static function getCompanyResumeFileList($request): array
    {
        $applyList = BaseJobApply::find()
            ->select([
                'id',
                'stuff_file_id',
                'job_id',
            ])
            ->where([
                'resume_id'  => $request['resumeId'],
                'company_id' => $request['companyId'],
            ])
            ->asArray()
            ->all();
        $list      = [];
        foreach ($applyList as $k => &$item) {
            if (!empty($item['stuff_file_id'])) {
                $stuffFileIdArr = explode(',', $item['stuff_file_id']);
                $jobName        = BaseJob::findOneVal(['id' => $item['job_id']], 'name');
                foreach ($stuffFileIdArr as $key => $fileId) {
                    $data = [
                        'applyId'  => $item['id'],
                        'jobName'  => $jobName,
                        'fileName' => BaseFile::findOneVal(['id' => $fileId], 'name'),
                    ];
                    array_push($list, $data);
                }
            }
        }

        return $list;
    }

    /**
     * 获取单位简历库某个求职者的附件材料数量（投递到该单位职位下的材料）
     */
    public static function getCompanyResumeFileAmount($resumeId, $companyId): int
    {
        $applyList   = BaseJobApply::find()
            ->select([
                'id',
                'stuff_file_id',
                'job_id',
            ])
            ->where([
                'resume_id'  => $resumeId,
                'company_id' => $companyId,
            ])
            ->asArray()
            ->all();
        $totalAmount = 0;
        foreach ($applyList as $k => &$item) {
            if (!empty($item['stuff_file_id'])) {
                $stuffFileIdArr = explode(',', $item['stuff_file_id']);
                foreach ($stuffFileIdArr as $key => $fileId) {
                    $totalAmount += 1;
                }
            }
        }

        return $totalAmount;
    }

    /**
     * 获取单位简历库某个求职者的所有附件简历列表（投递到该单位职位下的附件简历）
     */
    public static function getCompanyResumeAttachmentList($request): array
    {
        $applyList = BaseJobApply::find()
            ->select([
                'id',
                'resume_attachment_id',
                'job_id',
            ])
            ->where([
                'resume_id'  => $request['resumeId'],
                'company_id' => $request['companyId'],
            ])
            ->asArray()
            ->all();

        $list = [];
        foreach ($applyList as $k => $item) {
            if (!empty($item['resume_attachment_id'])) {
                $data = [
                    'applyId'  => $item['id'],
                    'jobName'  => BaseJob::findOneVal(['id' => $item['job_id']], 'name'),
                    'fileName' => BaseFile::findOneVal(['id' => $item['resume_attachment_id']], 'name'),
                ];
                array_push($list, $data);
            } else {
                unset($applyList[$k]);
            }
        }

        return $list;
    }

    /**
     * 获取单位简历库某个求职者的附件简历数量（投递到该单位职位下的附件简历）
     */
    public static function getCompanyResumeAttachmentAmount($resumeId, $companyId): int
    {
        $applyList = BaseJobApply::find()
            ->select([
                'id',
                'resume_attachment_id',
                'job_id',
            ])
            ->where([
                'resume_id'  => $resumeId,
                'company_id' => $companyId,
            ])
            ->asArray()
            ->all();

        $totalAmount = 0;
        foreach ($applyList as $k => $item) {
            if (!empty($item['resume_attachment_id'])) {
                $totalAmount += 1;
            }
        }

        return $totalAmount;
    }

    /**
     * 更新简历的数据
     * @param $resumeId
     * @return array
     * @throws \Exception
     */
    public static function getUserSpecialInfo($resumeId): array
    {
        //        //获取简历信息
        //        $resumeInfo = self::find()
        //            ->where(['id' => $resumeId])
        //            ->with(['resumeWork'])
        //            ->asArray()
        //            ->one();
        //
        //        //标签
        //        $tag = [];
        //        //工作经历
        //        if (!empty($resumeInfo['resumeWork'])) {
        //            foreach ($resumeInfo['resumeWork'] as $value) {
        //                if ($value['isAbroad'] == 1) {
        //                    $tagAbroad = '海外经历';
        //                    array_push($tag, $tagAbroad);
        //                }
        //                if ($value['isPostdoc'] == 1) {
        //                    $tagWork = '博士后经历';
        //                    array_push($tag, $tagWork);
        //                }
        //            }
        //        }
        //标签
        $tag = [];
        if (BaseResume::find()
            ->andWhere([
                'id'        => $resumeId,
                'is_abroad' => 1,
            ])
            ->exists()) {
            $tag[] = '海外经历';
        }
        if (BaseResumeWork::find()
            ->andWhere([
                'resume_id'  => $resumeId,
                'is_postdoc' => 1,
                'status'     => BaseResumeWork::STATUS_ACTIVE,
            ])
            ->exists()) {
            $tag[] = '博士后经历';
        }

        return $tag;
        //        return array_unique($tag);
    }

    public static function editWorkStatus($memberId, $parameter)
    {
        $model              = BaseResume::findOne(['member_id' => $memberId]);
        $model->work_status = $parameter;
        if (!$model->save()) {
            throw new \Exception('修改失败');
        }

        $resumeId = BaseMember::getMainId($memberId);
        //更新简历最后更新时间
        BaseResume::updateLastUpdateTime($resumeId);
        //新增操作日志
        $log_data = [
            'content' => '修改求职状态，memberId：' . $memberId,
        ];
        // 写日志
        BaseMemberActionLog::log($log_data);
    }

    public static function editArriveDateType($memberId, $parameter)
    {
        $model                   = BaseResume::findOne(['member_id' => $memberId]);
        $model->arrive_date_type = $parameter;
        if (!$model->save()) {
            throw new \Exception('修改失败');
        }
        $resumeId = BaseMember::getMainId($memberId);
        //更新简历最后更新时间
        BaseResume::updateLastUpdateTime($resumeId);
        $log_data = [
            'content' => '修改到岗时间，memberId：' . $memberId,
        ];
        // 写日志
        BaseMemberActionLog::log($log_data);
    }

    /**
     * 判断是否已经有了基本信息
     * @param $memberId
     * @return bool
     */
    public static function checkHasBasicRecord($memberId)
    {
        $info = self::find()
            ->where(['member_id' => $memberId])
            ->select(['name'])
            ->asArray()
            ->one();
        //存在姓名，即是提交了基本信息
        if (!empty($info['name'])) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断是否已经有了个人优势
     * @param $memberId
     * @return bool
     */
    public static function checkHasAdvantage($memberId)
    {
        $info = self::find()
            ->where(['member_id' => $memberId])
            ->select(['advantage'])
            ->asArray()
            ->one();
        if (!empty($info['advantage'])) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @param $resumeId
     *    顶尖简历    开发预留，先不展示，1年后可以再使用。1年后也可能不启用。
     *
     *    精英简历    "满足以下条件之一：
     *    1、职称为副高或正高的简历（不限制学历）；
     *    2、学历为博士，且有海外经历的简历（包括工作经历或者教育经历(本科以上)）；
     *    3.紧缺专业博士
     *    （二级学科：心理学，统计学，电子科学与技术，信息与通信工程，控制科学与工程，计算机科学与技术，核科学与技术，软件工程，网络空间安全，电子信息，药学，图书情报与档案管理，理临床医学，生物医学工程，公共卫生与预防医学，护理学，集成电路科学与工程）
     *    （三级学科：情报学，图书馆学，口腔医学）
     *
     *    优质简历    "满足以下条件之一：
     *    1、学历为博士的简历；
     *    2、学历为硕士，且有中级职称的简历；
     *    3、学历为硕士，且有海外留学(本科以上)经历的简历；"
     *    普通简历    除精英简历和优质简历以外的简历
     */

    public static function getResumeLevel($resumeId)
    {
        // 找到职称,1.09版本后,职称是多个的了,逗号隔开
        $resume = BaseResume::findOne($resumeId);

        //开始判断属于哪种类型
        if ($resume->resume_type == BaseResume::RESUME_TYPE_ELITE) {
            //精英简历
            return [
                'name'  => BaseSystemConfig::RESUME_TYPE_TEXT_LIST[BaseSystemConfig::RESUME_TYPE_NAME_ELITE],
                'type'  => BaseSystemConfig::RESUME_TYPE_NAME_ELITE,
                'point' => BaseSystemConfig::getValue(BaseSystemConfig::RESUME_LIBRARY_DOWNLOAD_POINT_ELITE_KEY),
            ];
        } elseif ($resume->resume_type == BaseResume::RESUME_TYPE_HIGH_QUALITY) {
            //优质简历
            return [
                'name'  => BaseSystemConfig::RESUME_TYPE_TEXT_LIST[BaseSystemConfig::RESUME_TYPE_NAME_HIGH_QUALITY],
                'type'  => BaseSystemConfig::RESUME_TYPE_NAME_HIGH_QUALITY,
                'point' => BaseSystemConfig::getValue(BaseSystemConfig::RESUME_LIBRARY_DOWNLOAD_POINT_HIGH_QUALITY_KEY),
            ];
        }

        //普通简历
        return [
            'name'  => BaseSystemConfig::RESUME_TYPE_TEXT_LIST[BaseSystemConfig::RESUME_TYPE_NAME_ORDINARY],
            'type'  => BaseSystemConfig::RESUME_TYPE_NAME_ORDINARY,
            'point' => BaseSystemConfig::getValue(BaseSystemConfig::RESUME_LIBRARY_DOWNLOAD_POINT_ORDINARY_KEY),
        ];
    }

    public static function getResumeAttachmentList($request): array
    {
        $resumeAttachmentIds = BaseJobApply::findOneVal(['id' => $request['job_apply_id']], 'resume_attachment_id');
        $ids                 = explode(',', $resumeAttachmentIds);

        return BaseResumeAttachment::find()
            ->select([
                'token',
                'file_url',
                'file_name',
            ])
            ->where([
                'in',
                'id',
                $ids,
            ])
            ->asArray()
            ->all();
    }

    /**
     * 获取求职附件列表
     */
    public static function getResumeFileList($request): array
    {
        $file = BaseJobApply::findOneVal(['id' => $request['job_apply_id']], 'stuff_file_id');
        if ($file < 1) {
            return [];
        }
        $applyList = BaseJobApply::find()
            ->alias('a')
            ->select([
                'a.id',
                'a.stuff_file_id',
            ])
            ->where(['a.id' => $request['job_apply_id']])
            ->asArray()
            ->one();

        $temp = explode(',', $applyList['stuff_file_id']);
        $list = [];
        foreach ($temp as $k => $v) {
            $list[$k]['id']       = $v;
            $list[$k]['fileName'] = BaseFile::findOneVal(['id' => $v], 'name');
        }

        return $list;
    }

    /**
     *  获取用户活动表单回馈信息
     * @throws \Exception
     */
    public static function getActivityFormResumeInfo($memberId, $activityFormId = 0): array
    {
        $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id');
        //无在线简历时，若简历前三步已有填入的内容，则需要回显；若简历前三步全部内容为空，则模块字段直接显示缺省。
        $registrationForm = BaseActivityFormRegistrationForm::getResumeActivityFormList($resumeId, $activityFormId);
        $resumeStep       = BaseResumeComplete::getResumeStep($resumeId);

        if (sizeof($registrationForm) > 0) {
            $baseInfo = [
                'resume_id'             => $registrationForm['resume_id'],
                'name'                  => $registrationForm['name'],
                'gender'                => $registrationForm['gender'],
                'household_register_id' => $registrationForm['household_register_id'],
                'mobile'                => $registrationForm['mobile'],
                'political_status_id'   => $registrationForm['political_status_id'],
                'title_id'              => $registrationForm['title_id'],
                'email'                 => $registrationForm['email'],
                'birthday'              => $registrationForm['birthday'],
                'residence'             => $registrationForm['residence'] ?: '',
            ];

            $educationList = [
                'school'            => $registrationForm['school'],
                'college'           => $registrationForm['college'],
                'education_id'      => $registrationForm['education_id'],
                'major_id'          => $registrationForm['major_id'],
                'major_custom'      => $registrationForm['major_custom'],
                'is_recruitment'    => $registrationForm['is_recruitment'],
                'is_project_school' => $registrationForm['is_project_school'],
                'is_abroad'         => $registrationForm['is_abroad'],
                'begin_date'        => $registrationForm['begin_date'],
                'end_date'          => $registrationForm['end_date'],
            ];

            $intentionList = [
                'work_status'      => $registrationForm['work_status'],
                'arrive_date_type' => $registrationForm['arrive_date_type'],
                'job_category_id'  => $registrationForm['job_category_id'],
                'nature_type'      => $registrationForm['nature_type'],
                'area_id'          => $registrationForm['area_id'],
                'wage_type'        => $registrationForm['wage_type'],
            ];
        } else {
            //获取用户基本信息
            $baseInfo = self::getActivityFormResumeBaseInfo($resumeId);

            //获取用户求职意向
            $intentionList                     = BaseResumeIntention::getLastIntentionInfo($memberId);
            $intentionList['arrive_date_type'] = $baseInfo['arrive_date_type'];

            //获取用户教育经历
            $educationList = ResumeEducation::getActivityHighestEducationInfo($memberId);
        }

        //这里做默认值的处理
        $baseInfo = self::checkActivityParameter($baseInfo, [
            'household_register_id',
            'title_id',
            'political_status_id',
        ]);

        $educationList = self::checkActivityParameter($educationList, [
            'education_id',
            'major_id',
        ]);

        $intentionList = self::checkActivityParameter($intentionList, [
            'arrive_date_type',
            'area_id',
            'nature_type',
        ]);

        $intentionList['nature_type'] = $intentionList['nature_type'] > 0 ? $intentionList['nature_type'] : "1";
        $intentionList['wage_type']   = $intentionList['wage_type'] > 0 ? $intentionList['wage_type'] : "1";

        $baseInfo = array_merge($baseInfo, [
            'genderTxt'             => self::GENDER_LIST[$baseInfo['gender']] ?: '',
            'householdRegisterText' => Area::findOneVal(['id' => $baseInfo['household_register_id']], 'name') ?: '',
            'politicalStatusTxt'    => Dictionary::getPoliticalStatusName($baseInfo['political_status_id']) ?: '',
            'titleTxt'              => BaseResume::getTitleName($baseInfo['title_id']) ?: '',
            'birthday'              => TimeHelper::formatToYearMonth($baseInfo['birthday']),
            'residenceTxt'          => Area::findOneVal(['id' => $baseInfo['residence']], 'name') ?: '',
        ]);

        $educationList = array_merge($educationList, [
            'majorTxt'     => Major::getMajorName($educationList['major_id']) ?: $educationList['major_custom'],
            'educationTxt' => BaseDictionary::getEducationName($educationList['education_id']) ?: '',
            'begin_date'   => TimeHelper::formatToYearMonth($educationList['begin_date']),
            'end_date'     => TimeHelper::formatToYearMonth($educationList['end_date']),
        ]);

        $areaId   = explode(',', $intentionList['area_id']);
        $areaNum  = count($areaId);
        $areaName = '';
        foreach ($areaId as $key => $val) {
            if ($key < $areaNum - 1) {
                $areaName = $areaName . BaseArea::getAreaName($val) . '｜';
            } else {
                $areaName = $areaName . BaseArea::getAreaName($val);
            }
        }
        $intentionList = array_merge($intentionList, [
            'arriveDateTypeTxt' => Dictionary::getArriveDateName($intentionList['arrive_date_type']),
            'workStatusTxt'     => BaseDictionary::getJobStatusName($intentionList['work_status']) ?: '',
            'jobCategoryTxt'    => CategoryJob::getName($intentionList['job_category_id']),
            'areaTxt'           => $areaName,
            'natureTxt'         => Dictionary::getNatureName($intentionList['nature_type']) ?: '',
            'wageTxt'           => Dictionary::getWageRangeName($intentionList['wage_type']),
        ]);

        return [
            'resumeStep'    => $resumeStep,
            'baseInfo'      => $baseInfo,
            'intentionList' => $intentionList,
            'educationList' => $educationList,
        ];
    }

    /**
     * 获取用户基础信息--活动表单
     */
    public static function getActivityFormResumeBaseInfo($resumeId)
    {
        return BaseResume::find()
            ->alias('r')
            ->leftJoin(['m' => BaseMember::tableName()], 'm.id=r.member_id')
            ->select([
                'r.id as resume_id',
                'r.name',
                'r.gender',
                'r.birthday',
                'r.household_register_id',
                'r.political_status_id',
                'm.mobile',
                'm.email',
                'r.title_id',
                'r.arrive_date_type',
                'r.residence',
            ])
            ->where([
                'r.id' => $resumeId,
            ])
            ->asArray()
            ->one();
    }

    /**
     * 获取求职者的所有附件列表
     */
    public static function getActivityResumeFileList($memberId): array
    {
        if (!$memberId) {
            return [];
        }
        $list = BaseResumeAttachment::find()
            ->where(['member_id' => $memberId])
            ->andWhere([
                'in',
                'status',
                [
                    BaseResumeAttachment::STATUS_ACTIVE,
                    BaseResumeAttachment::STATUS_DISABLE,
                ],
            ])
            ->select([
                'file_id',
                'token',
                'note',
                'file_name',
                'file_url',
                'note',
            ])
            ->asArray()
            ->all();
        foreach ($list as &$item) {
            $item['downloadUrl'] = self::getMineDownloadUrl($item['token']);
        }

        return $list;
    }

    /**
     * 保存用户基本信息
     * @param $data
     */
    public static function saveActivityFormUserBaseInfo($data)
    {
        $memberId = $data['memberId'];
        $model    = self::findOne(['member_id' => $memberId]);
        //对基本信息参数进行校验
        if (strlen($data['name']) < 1 || empty($data['gender']) || empty($data['birthday']) || empty($data['householdRegisterId']) || empty($data['email']) || empty($data['politicalStatusId'])) {
            throw new Exception('基本信息缺失必填参数！');
        }

        $age          = TimeHelper::countYears($data['birthday']);
        $birthdayCode = date('md', strtotime($data['birthday']));

        $model->name                  = $data['name'];
        $model->gender                = $data['gender'];
        $model->birthday              = TimeHelper::formatAddDay($data['birthday']);
        $model->age                   = $age;
        $model->birthday_code         = $birthdayCode;
        $model->household_register_id = $data['householdRegisterId'];
        $model->political_status_id   = $data['politicalStatusId'];
        $model->work_status           = $data['workStatus'];
        $model->arrive_date_type      = $data['arriveDateType'];
        $model->residence             = $data['residence'];
        if (strlen($data['titleId']) > 0) {
            $model->title_id = $data['titleId'];
        }

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        //更新用户的邮箱
        if (!empty($data['email'])) {
            $memberModel = BaseMember::findOne($memberId);

            $memberModel->email = $data['email'];
            $memberModel->save();

            if (!$memberModel->save()) {
                throw new Exception($memberModel->getFirstErrorsMessage());
            }
        }
        //新增操作日志
        $data = [
            'content' => '保存活动表单用户基本信息，memberId：' . $memberId,
        ];
        // 写登录日志
        BaseMemberActionLog::log($data);

        //更新用户简历完成度表
        BaseResumeComplete::updateResumeCompleteInfo($memberId);
    }

    public static function getMineDownloadUrl($token)
    {
        $url = UrlHelper::toRoute([
            '/api/person/resume/download-attachment',
            'token' => $token,
        ]);

        return $url;
    }

    /**
     * @throws Exception
     * @throws \Exception
     */
    public static function getAllActivityMessage($id): array
    {
        $memberId = Yii::$app->user->id;
        $info     = BaseActivityForm::getActivityFormInfo($id, $memberId);

        //简历前三步信息
        $activityFormResumeInfo = BaseResume::getActivityFormResumeInfo($memberId, $info['id']);

        return [
            'info'          => $info,
            'resumeStep'    => $activityFormResumeInfo['resumeStep'],
            'isLogin'       => (bool)$memberId,
            'baseInfo'      => $activityFormResumeInfo['baseInfo'],
            'intentionList' => $activityFormResumeInfo['intentionList'],
            'educationList' => $activityFormResumeInfo['educationList'],
            'parameter'     => [
                'allCityAreaList'         => BaseArea::getNativeCityAreaList(),
                'politicalList'           => ArrayHelper::obj2Arr(BaseDictionary::getPoliticalStatusList()),
                'titleList'               => BaseDictionary::getTitleList(),
                'educationList'           => ArrayHelper::obj2Arr(BaseDictionary::getEducationList()),
                'majorList'               => BaseMajor::getPersonMajorListByLevel3(),
                'jobStatusList'           => ArrayHelper::obj2Arr(BaseDictionary::getJobStatusList()),
                'categoryJobList'         => ArrayHelper::objMoreArr(BaseCategoryJob::getCompanyCategoryJobList()),
                'natureList'              => ArrayHelper::obj2Arr(BaseDictionary::getNatureList()),
                'areaList'                => BaseArea::getHierarchyCityList(),
                'wageList'                => ArrayHelper::obj2Arr(BaseDictionary::getWageRangeList()),
                'unitTypeList'            => ArrayHelper::obj2Arr(BaseActivityFormRegistrationForm::UNIT_TYPE_LIST),
                'channelList'             => ArrayHelper::obj2Arr(BaseActivityFormRegistrationForm::CHANNEL_LIST),
                'resumeFileList'          => BaseResume::getResumeFileList($memberId),
                'arriveDateList'          => ArrayHelper::obj2Arr(BaseDictionary::getArriveDateList()),
                'showMessageList'         => ArrayHelper::obj22Arr(BaseActivityFormIntentionOption::SHOW_MESSAGE_LIST),
                'overseasWorkingTimeList' => ArrayHelper::obj2Arr(BaseActivityFormRegistrationForm::OVERSEAS_WORKING_TIME_LIST),
                'employmentStatusList'    => ArrayHelper::obj2Arr(BaseActivityFormRegistrationForm::EMPLOYMENT_STATUS_LIST),
                ‘,
            ],
        ];
    }

    /**
     * @throws Exception
     * @throws \Exception
     */
    public static function checkActivityParameter($arr, $checkArr): array
    {
        foreach ($checkArr as $item) {
            if ($arr[$item] == 0) {
                $arr[$item] = '';
            }
        }

        return $arr;
    }

    /**
     * 获取用户到岗时间和工作状态
     * @param $resumeId
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function getArriveDateAndWorkStatus($resumeId)
    {
        $info               = BaseResume::find()
            ->where(['id' => $resumeId])
            ->select([
                'arrive_date_type as arriveDateType',
                'work_status as workStatus',
            ])
            ->asArray()
            ->one();
        $info['arriveDate'] = TimeHelper::formatToYearMonth($info['arriveDate']);
        if (empty($info['workStatus'])) {
            $info['workStatus']     = '';
            $info['workStatusText'] = '';
        } else {
            $info['workStatusText'] = BaseDictionary::getJobStatusName($info['workStatus']);
        }
        if (empty($info['arriveDateType'])) {
            $info['arriveDateType'] = '';
            $info['arriveDate']     = '';
        } else {
            $info['arriveDate'] = BaseDictionary::getArriveDateName($info['arriveDateType']);
        }

        return $info;
    }

    /**
     * 获取求职者最高教育经历的专业名称
     * @param $memberId
     * @return mixed|string
     * @throws \Exception
     */
    public static function getTopEducationMajorText($memberId)
    {
        //获取最高教育经历id
        $educationId = self::findOneVal(['member_id' => $memberId], 'last_education_id');
        //获取专业id
        $majorId = BaseResumeEducation::findOneVal(['id' => $educationId], 'major_id');
        if (!empty($majorId)) {
            //获取专业名称返回
            return BaseMajor::getMajorName($majorId);
        } else {
            return '';
        }
    }

    /**
     * 获取求职者最高教育经历的专业id
     * @param $memberId
     * @return mixed|string
     * @throws \Exception
     */
    public static function getTopEducationMajorId($memberId)
    {
        //获取最高教育经历id
        $educationId = self::findOneVal(['member_id' => $memberId], 'last_education_id');
        //获取专业id
        $majorId = BaseResumeEducation::findOneVal(['id' => $educationId], 'major_id');
        if (!empty($majorId)) {
            //获取专业名称返回
            return $majorId;
        } else {
            return '';
        }
    }

    public static function getTopEducationInfo($resumeId)
    {
        $educationId = self::findOneVal(['id' => $resumeId], 'last_education_id');
        $education   = BaseResumeEducation::findOne($educationId);

        return [
            'school'    => $education->school,
            'education' => BaseDictionary::getEducationName($education->education_id),
            'major'     => BaseMajor::findOneVal(['id' => $education->major_id], 'name'),
        ];
    }

    /**
     * @param $resumeId
     * 点击全局关闭弹窗
     */
    public static function clickGlobalPopover($resumeId)
    {
        $key = Cache::RESUME_TODAY_GLOBAL_POPOVER_CLICK_KEY . ':' . $resumeId;

        $time = Cache::get($key);

        if (!$time) {
            // 计算一下到今晚23:59:59还有多少秒
            $now      = time();
            $todayEnd = strtotime(date('Y-m-d 23:59:59'));
            $seconds  = $todayEnd - $now;
            Cache::set($key, time(), $seconds);
        }
    }

    /**
     * @param $resumeId
     * 今天是否点击过全局弹窗的关闭按钮
     */
    public static function isTodayClickGlobalPopover($resumeId)
    {
        $key  = Cache::RESUME_TODAY_GLOBAL_POPOVER_CLICK_KEY . ':' . $resumeId;
        $time = Cache::get($key);
        if (!$time) {
            return false;
        }

        return true;
    }

    /**
     * 检查是否vip
     */
    public static function checkVip($memberId)
    {
        if (self::find()
            ->where([
                'member_id' => $memberId,
                'vip_type'  => self::VIP_TYPE_ACTIVE,
            ])
            ->exists()) {
            $isVip = true;
        } else {
            $isVip = false;
        }

        return $isVip;
    }

    /**
     * 获取vip会员信息
     */
    public static function getVipInfo($memberId)
    {
        $vipTypeActive = self::VIP_TYPE_ACTIVE;
        $vipTypeNormal = self::VIP_TYPE_NORMAL;
        $vipInfo       = self::find()
            ->select([
                "id",
                "vip_type AS vipType",
                "vip_level as vipLevel",
                "vip_expire_time as vipExpireTime",
                "DATE_FORMAT(vip_begin_time, '%Y-%m-%d') AS vipBeginDate",
                "DATE_FORMAT(vip_expire_time, '%Y-%m-%d') AS vipExpireDate",
                "(CASE vip_type WHEN $vipTypeActive THEN $vipTypeActive ELSE $vipTypeNormal END) AS isVip",
            ])
            ->where([
                'member_id' => $memberId,
            ])
            ->asArray()
            ->one();

        return $vipInfo;
    }

    /**
     * 编辑简历页数据获取
     * @param $memberId
     * @return array|\yii\db\ActiveRecord|null
     * @throws Exception
     */
    public static function getMobileInfo($memberId): array
    {
        //获取用户基本信息
        $userInfo = self::find()
            ->alias('r')
            ->leftJoin(['m' => BaseMember::tableName()], 'r.member_id = m.id')
            ->where([
                'r.member_id' => $memberId,
                'm.type'      => BaseMember::TYPE_PERSON,
            ])
            ->select([
                'r.id as resumeId',
                'r.member_id as memberId',
                'r.name',
                'r.gender',
                'r.advantage',
                'm.mobile',
                'm.email',
                'm.avatar',
                'm.username',
                'r.age',
                'r.last_education_id',
                'r.complete',
                'r.work_status',
                'r.arrive_date_type',
                'r.arrive_date',
                'r.title_id',
                'r.is_project_school',
            ])
            ->asArray()
            ->one();
        //获取用户其余信息
        $topEducationInfo               = BaseResumeEducation::find()
            ->where(['id' => $userInfo['last_education_id']])
            ->select([
                'major_id',
                'education_id',
                'major_custom',
            ])
            ->asArray()
            ->one();
        $userInfo['mobile']             = StringHelper::strMobileDes($userInfo['mobile']);
        $userInfo['fullMobile']         = StringHelper::strMobileDes(BaseMember::getFullMobile($memberId));
        $userInfo['avatar']             = BaseMember::getAvatar($memberId);
        $userInfo['workExperience']     = BaseResumeWork::getWorkExperienceText($userInfo['resumeId']);
        $userInfo['identityExperience'] = self::getIdentityExperienceText($userInfo['resumeId']);
        $userInfo['topEducation']       = BaseDictionary::getEducationName($topEducationInfo['education_id']);
        if ($topEducationInfo['major_id']) {
            $userInfo['topEducationMajor'] = BaseMajor::getMajorName($topEducationInfo['major_id']);
        } elseif ($topEducationInfo['major_custom']) {
            $userInfo['topEducationMajor'] = $topEducationInfo['major_custom'];
        } else {
            $userInfo['topEducationMajor'] = '';
        }
        $resumeService = new ResumeService();
        $resumeService = $resumeService->setPlatform(CommonService::PLATFORM_H5);

        $titleArr = [];
        if (!empty($userInfo['title_id'])) {
            $titleIdArr = explode(',', $userInfo['title_id']);
            foreach ($titleIdArr as $item) {
                $titleName = Dictionary::getTitleName($item) ?: '';
                if (!empty($titleName)) {
                    array_push($titleArr, $titleName);
                }
            }
        }
        $userInfo['titleArr'] = $titleArr;
        //获取到岗时间和工作状态
        $workStatus = BaseDictionary::getJobStatusName($userInfo['work_status']);
        if ($userInfo['arrive_date_type'] != BaseResume::CUSTOM_ARRIVE_DATE_TYPE) {
            $arriveDate = BaseDictionary::getArriveDateName($userInfo['arrive_date_type']);
        } else {
            $arriveDate = $userInfo['arrive_date'];
        }
        $userInfo['intentionInfo'] = $workStatus . '-' . $arriveDate . '到岗';
        //判断是否是博士后
        $userInfo['isPostdoc'] = BaseResumeWork::checkPostdocStatus($userInfo['resumeId']);
        $userInfo['isAbroad']  = BaseResumeWork::checkHasAbroad($userInfo['resumeId']);

        //获取用户求职意向列表
        $userInfo['intentionList'] = $resumeService->setOparetion(ResumeService::OPERATION_TYPE_INTENTION_INDEX)
            ->init(['member_id' => $memberId])
            ->run();
        //获取教育经历
        $userInfo['educationList'] = $resumeService->setOparetion(ResumeService::OPERATION_TYPE_EDUCATION_INDEX)
            ->init(['member_id' => $memberId])
            ->run();
        foreach ($userInfo['educationList'] as &$item) {
            $item['begin_date'] = TimeHelper::formatToYearMonth($item['begin_date'], '.');
            $item['end_date']   = TimeHelper::formatToYearMonth($item['end_date'], '.');
        }
        //获取研究方向
        $service                       = new GetEditInfoService();
        $researchDirectionInfo         = $service->setOparetion(GetEditInfoService::INFO_RESEARCH_DIRECTION)
            ->init([
                'memberId' => $memberId,
            ])
            ->run();
        $userInfo['researchDirection'] = $researchDirectionInfo['content'];
        //获取科研项目
        $userInfo['projectList'] = $resumeService->setOparetion(ResumeService::OPERATION_TYPE_PROJECT_INDEX)
            ->init(['member_id' => $memberId])
            ->run();
        foreach ($userInfo['projectList'] as &$item) {
            $item['beginDate'] = TimeHelper::formatToYearMonth($item['beginDate'], '.');
            $item['endDate']   = BaseResumeResearchProject::formatEndDate($item['id']);
        }

        //工作经历
        $userInfo['workList'] = $resumeService->setOparetion(ResumeService::OPERATION_TYPE_WORK_INDEX)
            ->init(['member_id' => $memberId])
            ->run();
        foreach ($userInfo['workList'] as &$item) {
            $item['begin_date'] = TimeHelper::formatToYearMonth($item['begin_date'], '.');
            $item['end_date']   = BaseResumeWork::formatEndDate($item['id']);
        }
        //学术成果部分
        //学术论文
        $userInfo['paperList'] = $resumeService->setOparetion(ResumeService::OPERATION_TYPE_PAPER_INDEX)
            ->init(['member_id' => $memberId])
            ->run();
        foreach ($userInfo['paperList'] as &$item) {
            $item['publishDate'] = TimeHelper::formatToYearMonth($item['publishDate'], '.');
        }
        //学术著作
        $userInfo['bookList'] = $resumeService->setOparetion(ResumeService::OPERATION_TYPE_BOOK_INDEX)
            ->init(['member_id' => $memberId])
            ->run();
        foreach ($userInfo['bookList'] as &$item) {
            $item['publish_date'] = TimeHelper::formatToYearMonth($item['publish_date'], '.');
        }
        //学术专利
        $userInfo['patentList'] = $resumeService->setOparetion(ResumeService::OPERATION_TYPE_PATENT_INDEX)
            ->init(['member_id' => $memberId])
            ->run();
        foreach ($userInfo['patentList'] as &$item) {
            $item['authorization_date'] = TimeHelper::formatToYearMonth($item['authorization_date'], '.');
        }
        //学术奖励
        $userInfo['rewardList'] = $resumeService->setOparetion(ResumeService::OPERATION_TYPE_REWARD_INDEX)
            ->init(['member_id' => $memberId])
            ->run();
        foreach ($userInfo['rewardList'] as &$item) {
            $item['obtain_date'] = TimeHelper::formatToYearMonth($item['obtain_date'], '.');
        }
        //其他奖励
        $userInfo['otherRewardList'] = $resumeService->setOparetion(ResumeService::OPERATION_TYPE_OTHER_REWARD_INDEX)
            ->init(['member_id' => $memberId])
            ->run();
        foreach ($userInfo['otherRewardList'] as &$item) {
            $item['obtain_date'] = TimeHelper::formatToYearMonth($item['obtain_date'], '.');
        }
        //资质证书
        $userInfo['certificateList'] = $resumeService->setOparetion(ResumeService::OPERATION_TYPE_CERTIFICATE_INDEX)
            ->init(['member_id' => $memberId])
            ->run();
        foreach ($userInfo['certificateList'] as &$item) {
            $item['obtain_date'] = TimeHelper::formatToYearMonth($item['obtain_date'], '.');
        }
        //技能/语言
        $userInfo['skillList'] = $resumeService->setOparetion(ResumeService::OPERATION_TYPE_SKILL_INDEX)
            ->init(['member_id' => $memberId])
            ->run();
        //其他技能
        $userInfo['otherSkillList'] = $resumeService->setOparetion(ResumeService::OPERATION_TYPE_OTHER_SKILL_INDEX)
            ->init(['member_id' => $memberId])
            ->run();
        //附加信息
        $userInfo['additionalList'] = $resumeService->setOparetion(ResumeService::OPERATION_TYPE_ADDITIONAL_INDEX)
            ->init(['member_id' => $memberId])
            ->run();

        //获取用户完成度提醒
        $userInfo['completeTipsInfo'] = BaseResume::getCompleteTips($memberId);

        return $userInfo;
    }

    /**
     * 获取求职者身份信息提示语句
     * @param $resumeId
     * @return array
     */
    public static function getIdentityTipList($resumeId)
    {
        $resumeInfo = self::find()
            ->where(['id' => $resumeId])
            ->select([
                'identity_type as identityType',
                'last_education_id as lastEducationId',
            ])
            ->asArray()
            ->one();
        $tips       = [];
        if ($resumeInfo['identityType'] == self::IDENTITY_TYPE_DELETION) {
            //若求职者身份信息为空
            $tips[] = [
                'title'   => '身份信息缺失',
                'content' => '您的身份信息待完善，请及时设置个人身份获取更多机会',
                'btnText' => '立即完善',
                'h5Url'   => '/resume/base-info',
                'type'    => 'unComplete',
            ];
        }
        if ($resumeInfo['identityType'] == self::IDENTITY_TYPE_GRADUATE) {
            //若求职者最高学历毕业时间为历史年，且当前身份为“应届生/在校生”，则个人信息模块常显如下提示信息：
            //判断最高学历的毕业时间
            $topEducationEndDate = BaseResumeEducation::findOneVal(['id' => $resumeInfo['lastEducationId']],
                'end_date');
            //获取毕业时间年份
            $topEducationEndYear = date('Y', strtotime($topEducationEndDate));
            //获取当前年份
            $nowYear = date('Y', time());
            if ($nowYear > $topEducationEndYear) {
                $tips[] = [
                    'title'   => '身份信息未更新',
                    'content' => '系统校验到您已毕业，切换为“职场人”身份获取更多机会',
                    'btnText' => '立即切换',
                    'h5Url'   => '/resume/base-info?identityType=1',
                    'type'    => 'change',
                ];
            }
        }

        return $tips;
    }

    /**
     * 根据求职者身份，获取求职者工作经验或者毕业时间文案
     * @param $resumeId
     * @return string|void
     */
    public static function getIdentityExperienceText($resumeId)
    {
        $identityType = self::findOneVal(['id' => $resumeId], 'identity_type');

        if ($identityType == self::IDENTITY_TYPE_WORKER) {
            //如果是职场人士，同时是拥有工作经验,获取工作经验文案
            return BaseResumeWork::getWorkExperienceText($resumeId);
        }

        if ($identityType == self::IDENTITY_TYPE_GRADUATE) {
            //获取求职者毕业时间信息
            return BaseResumeEducation::getGraduateInfo($resumeId);
        }

        return '';
    }

    /**
     * 获取参加工作时间
     * @param $resumeId
     * @return false|string
     */
    public static function getBeginWorkDate($resumeId)
    {
        //获取身份类型
        $identityType = BaseResume::findOneVal(['id' => $resumeId], 'identity_type');
        //获取参加工作设计
        $beginWorkDate = BaseResume::findOneVal(['id' => $resumeId], 'begin_work_date');
        //如果是职场人，无论如何都返回格式化后的日期
        if ($identityType == BaseResume::IDENTITY_TYPE_WORKER) {
            if ($beginWorkDate == TimeHelper::ZERO_DATE) {
                return TimeHelper::ZERO_MONTH;
            } else {
                $time = strtotime($beginWorkDate);

                return date('Y' . '-' . 'm', $time);
            }
        } else {
            return '';
        }
    }

    /**
     * 根据参加工作时间，返回工作经验
     * @param $beginWorkDate
     * @return false|float|int
     */
    public static function countExperienceByBeginWork($beginWorkDate)
    {
        if ($beginWorkDate == TimeHelper::ZERO_MONTH || $beginWorkDate == TimeHelper::ZERO_DATE) {
            return 0;
        } else {
            $today = date('Y-m-d', time());

            return TimeHelper::countDifferYears($today, $beginWorkDate);
        }
    }

    // 查询某个简历 id 是否有编制查询权限（现在只要是 vip 就有权限），避免以后扩展
    public static function isEstablishment($resumeId)
    {
        if (BaseResume::find()
            ->where([
                'id'       => $resumeId,
                'vip_type' => BaseResume::VIP_TYPE_ACTIVE,
            ])
            ->exists()) {
            return true;
        }

        return false;
    }

    /**
     * 获取完善简历弹窗信息
     * @param $memberId
     * @return array
     */
    public static function getResumeCompletePopInfo($memberId)
    {
        $cacheKey = Cache::RESUME_PERFECT_POP_KEY . ':' . PLATFORM . ':' . $memberId;
        if ($day = Cache::get($cacheKey)) {
            if (TimeHelper::isThisWeek($day)) {
                return [];
            }
        }

        $completePercent = BaseResume::getComplete($memberId);
        $resumeId        = BaseMember::getMainId($memberId);
        //3、简历完善度≥65%，且有生效中的“钻石VIP”/“求职快”套餐时，不显示弹窗。
        $diamondVip = BaseResumeEquityPackage::isPackageEffect(BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP,
            $resumeId);
        $jobFast    = BaseResumeEquityPackage::isPackageEffect(BaseResumeEquityPackageCategorySetting::ID_JOB_FAST,
            $resumeId);
        if ($completePercent >= 65 && ($diamondVip || $jobFast)) {
            return [];
        }

        //获取用户头像
        $avatar = BaseMember::getAvatar($memberId);

        //2、简历完善度≥65%，且无生效中的“钻石VIP”/“求职快”套餐时，显示【弹窗2】样式；
        //（1）点击“完善简历”，则跳转【编辑简历】页面；
        //（2）点击“置顶简历”，则跳转【求职快 介绍页】；
        $data                 = [];
        $data['avatar']       = $avatar;
        $data['completeText'] = '简历完善度 ' . $completePercent . '%';
        if ($completePercent >= 60) {
            $data['title']    = '如何获得更多的简历曝光';
            $data['subTitle'] = '可通过以下方式提升';

            if (PLATFORM == 'MINI') {
                $data['content'] = '<ul class="writing">';
                $data['content'] .= '<li class="writing-item"><span class="writing-title">完善简历：</span><span class="writing-sub-title">简历内容越完善，越容易获取单位关注</span></li>';
                $data['content'] .= '<li class="writing-item"><span class="writing-title">刷新简历：</span><span class="writing-sub-title">提高活跃度，让排名更靠前！</span></li>';
                $data['content'] .= '<li class="writing-item"><span class="writing-title">置顶简历：</span><span class="writing-sub-title">让简历脱颖而出，单位优先看到您</span></li>';
                $data['content'] .= '</ul>';
                $data['btnList'] = [
                    [
                        'text' => '完善简历',
                        'url'  => '/packages/resume/index',
                        'type' => 'mini',
                    ],
                    [
                        'text' => '置顶简历',
                        'url'  => '/job-fast.html',
                        'type' => 'h5',
                    ],
                ];
            } else {
                $data['content'] = '<ul class="resume-list">';
                $data['content'] .= '<li class="resume-item"><span>完善简历：</span>简历内容越完善，越容易获取单位关注</li>';
                $data['content'] .= '<li class="resume-item"><span>刷新简历：</span>提高活跃度，让排名更靠前！</li>';
                $data['content'] .= '<li class="resume-item"><span>置顶简历：</span>让简历脱颖而出，单位优先看到您</li>';
                $data['content'] .= '</ul>';
                $data['btnList'] = [
                    '<a href="/resume/edit" class="weui-btn weui-btn_default close-popup">完善简历</a>',
                    ' <a href="/job-fast.html" class="weui-btn weui-btn_primary close-popup">置顶简历</a>',
                ];
            }
        } else {
            $data['title'] = '完善个人简历信息，获得更多职场机遇';
            $userStatus    = Member::getUserResumeStatus($memberId);
            //判断是否完成前三步
            if (PLATFORM == 'MINI') {
                $data['content']               = '<ul class="writing">';
                $data['content']               .= '<li class="writing-item"><span class="writing-sub-title">获取精准职位推荐，找工作快人一步！</span></li>';
                $data['content']               .= '<li class="writing-item"><span class="writing-sub-title">完善度达75%即可投递职位，精准出击！</span></li>';
                $data['content']               .= '<li class="writing-item"><span class="writing-sub-title">简历曝光加倍，更容易获得用人单位青睐！</span></li>';
                $data['content']               .= '</ul>';
                $data['btnList'][0]['text']    = '立即完善';
                $data['btnList'][0]['subText'] = '30w+用户已完善';
                if ($userStatus == BaseMember::USER_STATUS_COMPLETE_RESUME) {
                    $data['btnList'][0]['url']  = '/packages/resume/index';
                    $data['btnList'][0]['type'] = 'mini';
                } else {
                    $data['btnList'][0]['url']  = '/packages/resume/required';
                    $data['btnList'][0]['type'] = 'mini';
                }
            } else {
                if ($userStatus == BaseMember::USER_STATUS_COMPLETE_RESUME) {
                    $url = '/resume/edit';
                } else {
                    $url = '/resume/index';
                }
                $data['content'] = '<ul class="resume-list">';
                $data['content'] .= '<li class="resume-item">获取精准职位推荐，找工作快人一步！</li>';
                $data['content'] .= '<li class="resume-item">完善度达75%即可投递职位，精准出击！</li>';
                $data['content'] .= '<li class="resume-item">简历曝光加倍，更容易获得用人单位青睐！</li>';
                $data['content'] .= '</ul>';
                $data['btnList'] = [
                    " <a class='weui-btn weui-btn_primary close-popup' href='$url'>立即完善<span>30w+用户已完善</span></a>",
                ];
            }
        }

        return $data;
    }

    public static function setResumeCompletePopInfo($memberId)
    {
        // 日期
        $day      = date('Y-m-d', time());
        $cacheKey = Cache::RESUME_PERFECT_POP_KEY . ':' . PLATFORM . ':' . $memberId;

        Cache::set($cacheKey, $day);

        return true;
    }

    /**
     * 获取简历完善度提醒语句
     * @param $memberId
     * @return array
     */
    public static function getCompleteTips($memberId)
    {
        $resumeCompletePercent = self::getComplete($memberId);
        $info['isComplete']    = false;
        switch ($resumeCompletePercent) {
            case $resumeCompletePercent < 65:
                $info['text'] = '简历完整度低于65%，将无法被单位查看哦';
                break;
            case $resumeCompletePercent >= 65 && $resumeCompletePercent < 75:
                $info['text'] = '简历完整度达75%，可投全站职位';
                break;
            case $resumeCompletePercent >= 75:
                $info['text']       = '简历完整度越高，曝光度越高哦';
                $info['isComplete'] = true;
        }

        return $info;
    }

    /**
     * 检验是否有海外经历
     * @param $resumeId
     * @return bool
     */
    public static function checkResumeAbroad($resumeId)
    {
        return BaseResume::findOneVal(['id' => $resumeId], 'is_abroad') == self::IS_ABROAD_YES;
    }

    public static function getChinaMobileResume($resumeIds)
    {
        $where = [
            [
                'in',
                'r.id',
                $resumeIds,
            ],
            [
                '=',
                'm.mobile_code',
                '86',
            ],
        ];

        // 将可发的信息整理一下
        return BaseResume::find()
            ->alias('r')
            ->where(new AndCondition($where))
            ->innerJoin(['m' => BaseMember::tableName()], 'm.id = r.member_id')
            ->select([
                'r.id',
                'm.mobile',
            ])
            ->column();
    }

    /**
     * 检查简历是否有博士后经历（使用冗余字段）
     * @param int $resumeId 简历ID
     * @return bool
     */
    public static function checkHasPostdocExperience($resumeId)
    {
        $resume = self::findOne($resumeId);

        return $resume && $resume->has_postdoc_experience == self::IS_POSTDOC_YES;
    }

    /**
     * 更新简历的博士后经历状态
     * @param int $resumeId 简历ID
     * @return bool
     */
    public static function updatePostdocStatus($resumeId)
    {
        // 检查是否有博士后工作经历
        $hasPostdoc = BaseResumeWork::checkPostdocStatus($resumeId);

        // 更新简历表的冗余字段
        $resume = self::findOne($resumeId);
        if ($resume) {
            $resume->has_postdoc_experience = $hasPostdoc ? self::IS_POSTDOC_YES : self::IS_POSTDOC_NO;

            return $resume->save();
        }

        return false;
    }
}