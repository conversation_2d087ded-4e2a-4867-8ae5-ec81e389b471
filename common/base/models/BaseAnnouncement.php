<?php

namespace common\base\models;

use common\components\MessageException;
use common\helpers\DebugHelper;
use common\helpers\FileHelper;
use common\helpers\MaskHelper;
use common\helpers\StringHelper;
use common\helpers\UrlHelper;
use common\libs\CompanyAuthority\CompanyAuthorityClassify;
use common\libs\Qiniu;
use common\libs\WxMiniApp;
use common\models\JobApply;
use common\helpers\ArrayHelper;
use common\helpers\TimeHelper;
use common\libs\Cache;
use common\models\Announcement;
use common\models\Job;
use Faker\Provider\Base;
use h5\models\Article;
use Yii;
use yii\base\Exception;
use yii\db\conditions\AndCondition;
use yii\db\Expression;
use yii\helpers\Url;

class BaseAnnouncement extends Announcement
{
    const DETAIL_CACHE_TIME = 18000;
    // const DETAIL_CACHE_TIME = 300;

    const APPLY_ID_TYPE_COMPANY = 1;//单位
    const APPLY_ID_TYPE_ADMIN   = 2;//运营

    // 应聘方式
    const ATTRIBUTE_APPLY_EMAIL     = 1;
    const ATTRIBUTE_APPLY_ONLINE    = 2;
    const ATTRIBUTE_APPLY_SCENE     = 3;
    const ATTRIBUTE_APPLY_TELEPHONE = 4;
    const ATTRIBUTE_APPLY_FAX       = 5;
    const ATTRIBUTE_APPLY_MAIL      = 6;
    const ATTRIBUTE_APPLY_OTHER     = 7;

    // 特色标签
    const ATTRIBUTE_TAG_RETURNEES = 1;
    const ATTRIBUTE_TAG_YEAR      = 2;
    const ATTRIBUTE_TAG_LIST      = [
        self::ATTRIBUTE_TAG_RETURNEES => '高才海外',
        self::ATTRIBUTE_TAG_YEAR      => '年度招聘',
    ];

    const IS_FIRST_RELEASE_YES = 1;
    const IS_FIRST_RELEASE_NO  = 2;

    // 推荐位
    const ATTRIBUTE_RECOMMEND_INFO       = 1;
    const ATTRIBUTE_RECOMMEND_HOTSPOT    = 2;
    const ATTRIBUTE_RECOMMEND_MARVELLOUS = 3;
    const ATTRIBUTE_RECOMMEND_LIST       = [
        self::ATTRIBUTE_RECOMMEND_INFO       => '推荐信息',
        self::ATTRIBUTE_RECOMMEND_HOTSPOT    => '热点信息',
        self::ATTRIBUTE_RECOMMEND_MARVELLOUS => '精彩文章',
    ];

    //审核状态
    const STATUS_AUDIT_PASS    = 1; //审核通过--v2
    const STATUS_AUDIT_REFUSE  = -1; //审核拒绝--v2
    const STATUS_AUDIT_STAGING = 3; //编辑中--v2
    const STATUS_AUDIT_AWAIT   = 7; //待审核--v2
    const STATUS_AUDIT_LIST    = [
        self::STATUS_AUDIT_PASS    => '审核通过',
        self::STATUS_AUDIT_REFUSE  => '审核拒绝',
        self::STATUS_AUDIT_AWAIT   => '待审核',
        self::STATUS_AUDIT_STAGING => '编辑中',
    ];
    const STATUS_AUDIT_LISTS   = [
        self::STATUS_AUDIT_PASS    => '审核通过',
        self::STATUS_AUDIT_REFUSE  => '审核拒绝',
        self::STATUS_AUDIT_STAGING => '编辑中',
    ];

    // 修改类型
    const TYPE_EDITOR_ANNOUNCEMENT             = 1; //仅修改公告
    const TYPE_ADD_JOB                         = 2; //仅新增职位
    const TYPE_EDITOR_JOB                      = 3; //仅修改职位
    const TYPE_EDITOR_ADD_JOB                  = 4; //修改职位+新增职位
    const TYPE_EDITOR_ANNOUNCEMENT_JOB         = 5; //修改公告+修改职位
    const TYPE_EDITOR_ANNOUNCEMENT_ADD_JOB     = 6; //修改公告+新增职位
    const TYPE_EDITOR_ANNOUNCEMENT_JOB_ADD_JOB = 7; //修改公告+修改职位+新增职位
    const TYPE_EDITOR_OTHER                    = 9; //详情界面
    const TYPE_EDITOR_LIST                     = [
        self::TYPE_EDITOR_ANNOUNCEMENT             => '仅修改公告',
        self::TYPE_ADD_JOB                         => '仅新增职位',
        self::TYPE_EDITOR_JOB                      => '仅修改职位',
        self::TYPE_EDITOR_ADD_JOB                  => '修改职位+新增职位',
        self::TYPE_EDITOR_ANNOUNCEMENT_JOB         => '修改公告+修改职位',
        self::TYPE_EDITOR_ANNOUNCEMENT_ADD_JOB     => '修改公告+新增职位',
        self::TYPE_EDITOR_ANNOUNCEMENT_JOB_ADD_JOB => '修改公告+修改职位+新增职位',
        self::TYPE_EDITOR_OTHER                    => '详情界面',
    ];

    //招聘状态1=在线,2=已下线,3=待发布,9=删除
    const STATUS_ONLINE  = 1; //在线 --v2
    const STATUS_OFFLINE = 2; //已下线--v2
    const STATUS_STAGING = 3; //待发布--v2
    const STATUS_DELETE  = 9; //删除--v2

    const STATUS_LIST = [
        self::STATUS_ONLINE  => '在线',
        self::STATUS_OFFLINE => '已下线',
        self::STATUS_STAGING => '待发布',
        self::STATUS_DELETE  => '删除',
    ];

    const ATTENTION_PERIOD_DAY  = 7;    //公告到期时间提醒，7天内
    const ADD_RELEASE_AGAIN_DAY = 365;  //再发布新增天数，365天，基于当前时间
    const IS_COOPERATION_YES    = 1;
    const IS_COOPERATION_NO     = 2;

    const CREATE_TYPE_COMPANY = 1;     //企业发布
    const CREATE_TYPE_ADMIN   = 2;     //运营发布

    const SHOW_JOB_AREA_NAME_NUM = 3;

    const TEMPLATE_ORDINARY                = 1;
    const TEMPLATE_SENIOR_ONE              = 2;
    const TEMPLATE_DOUBLE_MEETING_ACTIVITY = 3;
    const TEMPLATE_SENIOR_TWO              = 4;
    const TEMPLATE_SENIOR_THREE            = 5;

    const TEMPLATE_LIST = [
        self::TEMPLATE_ORDINARY                => '普通模版',
        self::TEMPLATE_SENIOR_ONE              => '高级模版1（橙色）',
        self::TEMPLATE_SENIOR_TWO              => '高级模版2（紫色）',
        self::TEMPLATE_SENIOR_THREE            => '高级模版3（蓝色）',
        self::TEMPLATE_DOUBLE_MEETING_ACTIVITY => '2022年双会活动',
    ];

    const IS_CONSUME_RELEASE_YES = 1;
    const IS_CONSUME_RELEASE_NO  = 2;

    const OFFLINE_TYPE_DEFAULT   = 0;     // -
    const OFFLINE_TYPE_AUTO      = 1;     //下线类型（自动）
    const OFFLINE_TYPE_HAND      = 2;     //下线类型（手动）
    const OFFLINE_TYPE_VIOLATION = 3;     //下线类型（违规）
    const OFFLINE_TYPE_NAME      = [
        self::OFFLINE_TYPE_DEFAULT   => '-',
        self::OFFLINE_TYPE_AUTO      => '自动下线',
        self::OFFLINE_TYPE_HAND      => '手动下线',
        self::OFFLINE_TYPE_VIOLATION => '违规',
    ];

    //投递类型
    const DELIVERY_TYPE_UN      = 0;
    const DELIVERY_TYPE_OUTSIDE = 1;//站外投递
    const DELIVERY_TYPE_INSIDE  = 2;//站内投递
    const DELIVERY_TYPE         = [
        self::DELIVERY_TYPE_OUTSIDE,
        self::DELIVERY_TYPE_INSIDE,
    ];
    const DELIVERY_TYPE_NAME    = [
        self::DELIVERY_TYPE_OUTSIDE => '站外投递',
        self::DELIVERY_TYPE_INSIDE  => '站内投递',
    ];

    //投递方式
    const DELIVERY_WAY_UN              = 0;
    const DELIVERY_WAY_PLATFORM        = 1;
    const DELIVERY_WAY_EMAIL           = 2;
    const DELIVERY_WAY_LINK            = 3;
    const DELIVERY_WAY_EMAIL_LINK      = 66;
    const DELIVERY_WAY_PLATFORM_NAME   = '平台投递';
    const DELIVERY_WAY_EMAIL_NAME      = '邮件投递';
    const DELIVERY_WAY_LINK_NAME       = '网址投递';
    const DELIVERY_WAY_EMAIL_LINK_NAME = '邮件&网址投递';
    const DELIVERY_WAY                 = [
        self::DELIVERY_WAY_PLATFORM,
        self::DELIVERY_WAY_EMAIL,
        self::DELIVERY_WAY_LINK,
    ];
    const DELIVERY_WAY_EMAIL_LINK_LIST = [
        self::DELIVERY_WAY_EMAIL,
        self::DELIVERY_WAY_LINK,
    ];
    const DELIVERY_WAY_NAME            = [
        self::DELIVERY_WAY_PLATFORM => self::DELIVERY_WAY_PLATFORM_NAME,
        self::DELIVERY_WAY_EMAIL    => self::DELIVERY_WAY_EMAIL_NAME,
        self::DELIVERY_WAY_LINK     => self::DELIVERY_WAY_LINK_NAME,
    ];
    const DELIVERY_WAY_SELECT_NAME     = [
        self::DELIVERY_WAY_PLATFORM   => self::DELIVERY_WAY_PLATFORM_NAME,
        self::DELIVERY_WAY_EMAIL_LINK => self::DELIVERY_WAY_EMAIL_LINK_NAME,
    ];

    //是否被小程序调用
    const IS_MINIAPP_YES    = 1;
    const IS_MINIAPP_NO     = 2;
    const IS_MINIAPP_NO_SET = 0;
    //是否被运营手动标记
    const IS_MANUAL_TAG_NONE = 0;
    const IS_MANUAL_TAG_YES  = 1;
    const IS_MANUAL_TAG_NO   = 2;

    const IS_MINIAPP_LIST = [
        self::IS_MINIAPP_YES    => '是',
        self::IS_MINIAPP_NO     => '否',
        self::IS_MINIAPP_NO_SET => '未设置',
    ];

    // 是否附件提示
    const IS_ATTACHMENT_NOTICE_YES = 1;
    const IS_ATTACHMENT_NOTICE_NO  = 2;

    // 是否附件提示
    const IS_ATTACHMENT_NOTICE_LIST = [
        self::IS_ATTACHMENT_NOTICE_YES => '是',
        self::IS_ATTACHMENT_NOTICE_NO  => '否',
    ];

    const NOT_ESTABLISHMENT  = '0';
    const ALL_ESTABLISHMENT  = '1';
    const PART_ESTABLISHMENT = '2';

    //存在编制的类型列表
    const IS_ESTABLISHMENT_TYPE_LIST = [
        self::ALL_ESTABLISHMENT,
        self::PART_ESTABLISHMENT,
    ];
    const IS_ESTABLISHMENT_YES       = 1;
    const IS_ESTABLISHMENT_NO        = 2;

    const ESTABLISHMENT_TEXT_LIST = [
        self::PART_ESTABLISHMENT => '部分有编',
        self::ALL_ESTABLISHMENT  => '全部有编',
    ];

    //职位投递热度
    const ANNOUNCEMENT_HEAT_TYPE_PRIMARY   = 1;  //一般
    const ANNOUNCEMENT_HEAT_TYPE_HOT       = 2;  //较热
    const ANNOUNCEMENT_HEAT_TYPE_EXPLOSIVE = 3;  //火爆
    const ANNOUNCEMENT_HEAT_TYPE_TEXT_LIST = [
        self::ANNOUNCEMENT_HEAT_TYPE_PRIMARY   => '一般',
        self::ANNOUNCEMENT_HEAT_TYPE_HOT       => '较热',
        self::ANNOUNCEMENT_HEAT_TYPE_EXPLOSIVE => '火爆',
    ];

    //非合作单位邮箱投递地址隐藏状态
    const ADDRESS_HIDE_STATUS_YES = '1';
    const ADDRESS_HIDE_STATUS_NO  = '2';

    const ADDRESS_HIDE_STATUS_TEXT = [
        self::ADDRESS_HIDE_STATUS_YES => '隐藏',
        self::ADDRESS_HIDE_STATUS_NO  => '显示',
    ];

    // 背景图类型
    const BACKGROUND_IMG_FILE_TYPE_DEFAULT = 1; // 默认
    const BACKGROUND_IMG_FILE_TYPE_COMPANY = 2; // 单位主页
    const BACKGROUND_IMG_FILE_TYPE_CUSTOM  = 3; // 自定义

    const BACKGROUND_IMG_FILE_TYPE_LIST = [
        self::BACKGROUND_IMG_FILE_TYPE_DEFAULT => '模板默认背景',
        self::BACKGROUND_IMG_FILE_TYPE_COMPANY => '单位主页背景',
        self::BACKGROUND_IMG_FILE_TYPE_CUSTOM  => '自定义背景',
    ];

    //创建的类型(1企业自行发布，2运营人员代发布,3平台公告,不属于任何企业）
    const CREATE_TYPE_SELF  = 1;
    const CREATE_TYPE_AGENT = 2;

    const ANNOUNCEMENT_TEMPLATE_THREE_HEAD_IMG = 'https://img.gaoxiaojob.com/uploads/announcement/detail-header-3.png';

    public static function getInfoByJob($jobId)
    {
        $jobInfo        = Job::find()
            ->where(['id' => $jobId])
            ->select('announcement_id')
            ->asArray()
            ->one();
        $announcementId = ArrayHelper::getValue($jobInfo, 'announcement_id');
        if (!empty($announcementId)) {
            $announcementInfo = self::find()
                ->where(['id' => $announcementId])
                ->select([
                    'title',
                    'id',
                ])
                ->asArray()
                ->one();

            return $announcementInfo;
        } else {
            return '';
        }
    }

    /**
     * 获取公告url
     * @param $id
     * @return string
     */
    public static function getDetailUrl($id)
    {
        return UrlHelper::toRoute([
            '/announcement/detail',
            'id' => $id,
        ]);
    }

    /**
     * 获取公告下职位列表url
     * @param $id
     * @return string
     */
    public static function getJobListUrl($id)
    {
        return UrlHelper::toRoute([
            '/announcement/job-list',
            'id' => $id,
        ]);
    }

    /**
     * 获取单位发布的公告数量
     * @param $companyId
     * @return bool|int|string|null
     */
    public static function getCompanyAnnouncementAmount($companyId)
    {
        return self::find()
            ->alias('an')
            ->leftJoin(['ar' => BaseArticle::tableName()], 'an.article_id = ar.id')
            ->andWhere(['an.company_id' => $companyId])
            ->andWhere(['ar.is_delete' => BaseArticle::IS_DELETE_NO])
            ->andWhere([
                '<>',
                'ar.status',
                BaseArticle::STATUS_STAGING,
            ]) //在线或者下线状态
            ->andWhere(['ar.is_show' => BaseArticle::IS_SHOW_YES])
            ->count();
    }

    /**
     * 获取单位发布的公告数量
     * @param $companyId
     * @return bool|int|string|null
     */
    public static function getCompanyAnnouncementAmounts($companyId)
    {
        return self::find()
            ->alias('an')
            ->leftJoin(['ar' => BaseArticle::tableName()], 'an.article_id = ar.id')
            ->andWhere(['an.company_id' => $companyId])
            ->andWhere([
                '<>',
                'ar.status',
                BaseArticle::STATUS_STAGING,
            ]) //在线或者下线状态
            ->count();
    }

    /**
     * 获取单位发布的公告此点击数量
     * @param $companyId
     * @return bool|int|string|null
     */
    public static function getCompanyAnnouncementClickAmount($companyId)
    {
        return self::find()
            ->alias('an')
            ->leftJoin(['ar' => BaseArticle::tableName()], 'an.article_id = ar.id')
            ->andWhere(['an.company_id' => $companyId])
            ->andWhere(['ar.is_delete' => BaseArticle::IS_DELETE_NO])
            ->andWhere([
                '<>',
                'ar.status',
                BaseArticle::STATUS_STAGING,
            ]) //在线或者下线状态
            ->sum('ar.click');
    }

    /**
     * 获取单位在线公告数量
     * @param $companyId
     * @return bool|int|string|null
     */
    public static function getCompanyOnLineAnnouncementAmount($companyId)
    {
        return self::find()
            ->alias('an')
            ->leftJoin(['ar' => BaseArticle::tableName()], 'an.article_id = ar.id')
            ->andWhere(['an.company_id' => $companyId])
            ->andWhere(['ar.is_delete' => BaseArticle::IS_DELETE_NO])
            ->andWhere(['ar.status' => BaseAnnouncement::STATUS_ONLINE,]) //在线或者下线状态
            ->andWhere(['an.status' => BaseAnnouncement::STATUS_ONLINE,]) //在线或者下线状态
            ->andWhere(['ar.is_show' => BaseArticle::IS_SHOW_YES])
            ->count();
    }

    /**
     * 获取多个单位单位在线公告数量
     * @param $companyIds
     * @return array
     */
    public static function getCompanysOnLineAnnouncementAmount($companyIds): array
    {
        $result = self::find()
            ->alias('an')
            ->select([
                'count(*) AS an_count',
                'company_id',
            ])
            ->andWhere([
                'in',
                'an.company_id',
                $companyIds,
            ])
            ->leftJoin(['ar' => BaseArticle::tableName()], 'an.article_id = ar.id')
            ->andWhere(['ar.is_delete' => BaseArticle::IS_DELETE_NO])
            ->andWhere(['ar.status' => BaseAnnouncement::STATUS_ONLINE,]) //在线或者下线状态
            ->andWhere(['an.status' => BaseAnnouncement::STATUS_ONLINE,]) //在线或者下线状态
            ->andWhere(['ar.is_show' => BaseArticle::IS_SHOW_YES])
            ->groupBy('an.company_id')
            ->asArray()
            ->all();

        return $result ? array_column($result, 'an_count', 'company_id') : [];
    }

    /**
     * 获取单位详情页面公告
     * @param $searchData
     */
    public static function getCompanyDetailList($searchData, $needPageInfo = false)
    {
        //通过查询职位，去获取公告的id
        $announcementIdListQuery = BaseJob::find()
            ->alias('j')
            ->where(['company_id' => $searchData['companyId']])
            ->andWhere([
                'in',
                'j.status',
                [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->andFilterWhere(['j.city_id' => $searchData['areaId']])
            ->andFilterWhere(['j.job_category_id' => $searchData['jobCategoryId']]);

        // 2.4 追加专业和学历，都以一对多
        if ($searchData['majorId']) {
            $announcementIdListQuery->leftJoin(['mr' => BaseJobMajorRelation::tableName()], 'j.id = mr.job_id')
                ->andWhere(['mr.major_id' => $searchData['majorId']]);
        }
        $announcementIdListQuery->andFilterWhere(['j.education_type' => $searchData['educationId']]);

        //薪资范围查询
        if (!empty($searchData['wageId'])) {
            $wageInfo = BaseDictionary::getMinAndMaxWage($searchData['wageId']);
            //面议
            if ($wageInfo['min'] == 0 && $wageInfo['max'] == 0) {
                $announcementIdListQuery->andWhere([
                    'j.min_wage' => 0,
                ]);
                $announcementIdListQuery->andWhere([
                    'j.max_wage' => 0,
                ]);
            }
            //其他
            if ($wageInfo['min'] > 0) {
                $announcementIdListQuery->andWhere([
                    '>=',
                    'j.min_wage',
                    (int)$wageInfo['min'],
                ]);
            }

            if ($wageInfo['max'] > 0) {
                $announcementIdListQuery->andWhere([
                    '<=',
                    'j.max_wage',
                    (int)$wageInfo['max'],
                ]);
            }
        }
        $announcementIdListQuery->andWhere([
            '<>',
            'j.announcement_id',
            0,
        ]);
        $announcementIdList = $announcementIdListQuery->select('j.announcement_id')
            ->groupBy('j.announcement_id')
            ->asArray()
            ->all();

        $announcementIds = !$announcementIdList ? [] : array_column($announcementIdList, 'announcement_id');

        $query = self::find()
            ->alias('an')
            ->leftJoin(['ar' => BaseArticle::tableName()], 'an.article_id = ar.id')
            ->where(['an.id' => $announcementIds])
            ->andWhere(['an.company_id' => $searchData['companyId']])
            ->andWhere(['ar.is_delete' => BaseArticle::IS_DELETE_NO])
            ->andWhere(['ar.is_show' => BaseArticle::IS_SHOW_YES]);

        $query->select([
            'an.id',
            'an.title',
            'ar.status',
            'ar.is_show',
            'ar.is_delete',
            'ar.release_time as releaseTime',
            'ar.refresh_time as refreshTime',
        ]);
        //克隆$query句柄去到$clone_query从而实现不变原始逻辑
        $cloneQuery  = clone $query;
        $onlineCount = $cloneQuery->andWhere(['ar.status' => BaseArticle::STATUS_ONLINE])
            ->count();
        $query->andWhere([
            'in',
            'ar.status',
            [
                BaseArticle::STATUS_ONLINE,
                BaseArticle::STATUS_OFFLINE,
            ],
        ]); //在线或者下线状态
        $count    = $query->count();
        $pageSize = $searchData['pageSize'] ?: \Yii::$app->params['companyAnnouncementListDefaultPageSize'];

        $pages = self::setPage($count, $searchData['page'], $pageSize);

        //排序规则按在线，发布时间倒序，id倒序排，由于目前只有在线、下线两种状态被取出来
        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('ar.status asc,an.home_sort desc,ar.refresh_time desc,id desc')
            ->asArray()
            ->all();

        foreach ($list as $k => &$announcement) {
            //获取公告下的职位数量
            $announcement['jobAmount'] = BaseJob::getAnnouncementJobAmount($announcement['id']);
            //获取公告下职位招聘数量
            $announcement['recruitAmount'] = BaseJob::getAnnouncementJobRecruitAmount($announcement['id']);
            //获取职位的地区
            $announcement['areaName'] = BaseJob::getAnnouncementJobArea($announcement['id']);
            //获取职位的地区（5）
            $announcement['fiveAreaName'] = BaseJob::getAnnouncementJobAreaAccount($announcement['id'], 5);
            //获取职位的地区（all）
            $announcement['allAreaName'] = BaseJob::getAnnouncementJobAreaAccount($announcement['id'], 1000);
            //判断是否气泡
            $announcement['isAllArea'] = sizeof(explode(' ', $announcement['allAreaName'])) > 5 ? 1 : 0;
            //获取url
            $announcement['url'] = Url::toRoute([
                'announcement/detail',
                'id' => $announcement['id'],
            ]);

            //处理发布时间
            $thisYearTime = mktime(0, 0, 0, 1, 1, date('Y'));
            $refreshTime  = strtotime($announcement['refreshTime']);
            if ($refreshTime > $thisYearTime) {
                $announcement['refreshTime'] = date('m-d', $refreshTime);
            } else {
                $announcement['refreshTime'] = date('Y-m-d', $refreshTime);
            }

            //判断公告是否失效了（下线，删除）
            if ($announcement['is_delete'] == BaseArticle::IS_DELETE_YES || $announcement['status'] == BaseArticle::STATUS_OFFLINE || $announcement['is_show'] == BaseArticle::IS_SHOW_NO) {
                $announcement['invalid'] = 1;
            } else {
                $announcement['invalid'] = 2;
            }
        }
        if ($needPageInfo) {
            $data = [
                'list'           => $list,
                'pageSize'       => $pageSize,
                'currentPage'    => $searchData['page'],
                'totalNum'       => $count,
                'onlineTotalNum' => $onlineCount,
            ];

            return $data;
        } else {
            return $list;
        }
    }

    /**
     * 公告下职位id
     * @param $announcementId
     * @return array
     * @throws \Exception
     */
    public static function getAnnouncementJobIds($announcementId): array
    {
        $jobList = BaseJob::find()
            ->select(['id'])
            ->where(['announcement_id' => $announcementId])
            ->andFilterCompare('status', BaseJob::JOB_HISTORY_STATUS, 'in')
            ->groupBy('id')
            ->asArray()
            ->all();

        return array_column($jobList, 'id');
    }

    /**
     * 公告下有审核记录的职位id
     * @param $announcementId
     * @return array
     * @throws \Exception
     */
    public static function getHasAuditAnnouncementJobIds($announcementId): array
    {
        $jobList = BaseJob::find()
            ->select(['id'])
            ->where(['announcement_id' => $announcementId])
            ->andWhere([
                'in',
                'status',
                [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->groupBy('id')
            ->asArray()
            ->all();

        return array_column($jobList, 'id');
    }

    /**
     * 获取推荐公告列表
     * @param $searchData
     * @return array[]
     * @throws \Exception
     */
    public static function getRecommendList($searchData)
    {
        $query = self::find()
            ->alias('an')
            ->leftJoin(['ar' => BaseArticle::tableName()], 'an.article_id = ar.id')
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'ar.id = aa.article_id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = an.company_id')
            ->andWhere(['ar.is_delete' => BaseArticle::IS_DELETE_NO])
            ->andWhere([
                '<>',
                'ar.status',
                BaseArticle::STATUS_STAGING,
            ]) //在线或者下线状态
            ->andWhere(['ar.is_show' => BaseArticle::IS_SHOW_YES]);

        if (!empty($searchData['companyId'])) {
            $query->andWhere([
                'or',
                ['an.company_id' => $searchData['companyId']],
                ['c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES],
            ]);
        } else {
            $query->andWhere(['c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES]);
        }

        $query->select([
            'an.id',
            'an.title',
            'aa.sort_time as publishTime',
            'an.company_id',
        ]);

        $pageSize = $searchData['pageSize'] ?: \Yii::$app->params['recommendAnnouncementCount'];

        $count = $query->count();

        $pages = self::setPage($count, $searchData['page'], $pageSize);
        $list  = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('aa.sort_time desc,id desc')
            ->asArray()
            ->all();

        foreach ($list as $k => &$item) {
            $item['url'] = self::getDetailUrl($item['id']);
            //获取公告学历
            $item['education'] = trim(BaseJob::getAnnouncementJobEducationType($item['id']));
            //获取公告招聘人数
            $item['recruitAmount'] = BaseJob::getAnnouncementJobRecruitAmount($item['id']);
            //获取公告下职位数量
            $item['jobAmount'] = BaseJob::getAnnouncementJobAmount($item['id']);
            //获取公告
            //公告单位名称
            $item['companyName'] = BaseCompany::getNameById($item['company_id']);
            //获取公告下地点
            $item['areaName'] = self::getOnlineNewCityName($item['id']);
            $item['url']      = self::getDetailUrl($item['url']);
        }

        //        //todo:暂时写死
        //        $list = [
        //            [
        //                'workExperience' => '经验不限',
        //                'majorTxt'       => '哲学等',
        //            ],
        //        ];

        return $list;
    }

    /**
     * 数字转换为中文
     * @param integer $num 目标数字
     */
    public static function number2chinese(int $num)
    {
        if ($num < 100) {
            $char   = [
                '零',
                '一',
                '二',
                '三',
                '四',
                '五',
                '六',
                '七',
                '八',
                '九',
            ];
            $unit   = [
                '',
                '十',
                '百',
                '千',
                '万',
            ];
            $return = '';
            if ($num < 10) {
                $return = $char[$num];
            } elseif ($num % 10 == 0) {
                $firstNum = substr($num, 0, 1);
                if ($num != 10) {
                    $return .= $char[$firstNum];
                }
                $return .= $unit[strlen($num) - 1];
            } elseif ($num < 20) {
                $return = $unit[substr($num, 0, -1)] . $char[substr($num, -1)];
            } else {
                $numData   = str_split($num);
                $numLength = count($numData) - 1;
                foreach ($numData as $k => $v) {
                    if ($k == $numLength) {
                        continue;
                    }
                    $return .= $char[$v];
                    if ($v != 0) {
                        $return .= $unit[$numLength - $k];
                    }
                }
                $return .= $char[substr($num, -1)];
            }

            return $return;
        }
    }

    /**
     * 公告一级栏目下二级栏目列表
     */
    public static function getHomeSubColumnIdsList($keywords): array
    {
        $list = BaseHomeColumn::find()
            ->select([
                'name',
                'id',
            ])
            ->where([
                'parent_id' => $keywords['columnId'],
            ])
            ->orderBy(' sort desc ')
            ->asArray()
            ->all();

        //这里拿附加配置
        $level1LinkLevel2 = Yii::$app->params['level1LinkLevel2'][$keywords['columnId']];

        if ($level1LinkLevel2) {
            $list = array_merge($list, $level1LinkLevel2);
        }

        $level1NotLinkLevel2 = Yii::$app->params['level1NotLinkLevel2'][$keywords['columnId']];
        if ($level1NotLinkLevel2) {
            // 去掉不需要的栏目
            foreach ($list as $key => $item) {
                foreach ($level1NotLinkLevel2 as $level2) {
                    if ($item['id'] == $level2['id']) {
                        unset($list[$key]);
                    }
                }
            }
        }

        // 重置索引
        $list = array_values($list);

        foreach ($list as $k => $v) {
            $list[$k]['url'] = BaseHomeColumn::getDetailUrl($v['id']);
        }

        return $list;
    }

    /**
     * 栏目页公告数据（一级栏目）
     * @throws \Exception
     */
    public static function getColumnAnnouncementList($keywords): array
    {
        $select = [
            'a.id',
            'a.title',
            'a.major_ids',
            'art.cover_thumb',
            'art.status',
            'y.full_name',
            'j.city_id',
            'art.refresh_time',
        ];

        $query = BaseArticle::find()
            ->alias('art')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'art.id = a.article_id')
            ->leftJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id=art.id')
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'art.id = aa.article_id')
            ->leftJoin(['y' => BaseCompany::tableName()], 'a.company_id = y.id')
            ->leftJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'y.group_score_system_id = cgss.id')
            ->leftJoin(['j' => BaseJob::tableName()], 'a.id = j.announcement_id')
            ->select($select)
            ->where([
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.is_show'   => BaseArticle::IS_SHOW_YES,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'c.column_id'   => $keywords['columnId'],
                'art.status'    => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
            ])
            ->groupBy('a.id');

        // $orderBy = 'art.status asc,art.refresh_time desc,art.id desc';
        $orderBy = 'art.status asc,art.refresh_date desc,a.is_first_release asc,cgss.score desc,a.id desc';

        switch ($keywords['key']) {
            case 'headlines':
                $query->andFilterCompare('aa.type', BaseArticleAttribute::ATTRIBUTE_COLUMN_HEAD);
                break;
            case 'left_hot_announcement':
                $query->andWhere([
                    '>=',
                    'art.refresh_time',
                    date('Y-m-d', strtotime("-15 day")),
                ]);
                $orderBy = 'art.click desc,art.refresh_time desc';
                $query->andFilterCompare('art.status', BaseArticle::STATUS_ONLINE);
                break;
            case 'right_hot_announcement':
                // $query->andWhere([
                //     'in',
                //     'aa.type',
                //     [
                //         BaseArticleAttribute::ATTRIBUTE_ROLLING,
                //         BaseArticleAttribute::ATTRIBUTE_RECOMMEND,
                //         BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP,
                //         BaseArticleAttribute::ATTRIBUTE_HOME_TOP_ONE,
                //         BaseArticleAttribute::ATTRIBUTE_HOME_TOP_TWO,
                //         BaseArticleAttribute::ATTRIBUTE_FOCUS,
                //     ],
                // ]);
                $orderBy = 'art.status asc,art.refresh_time desc';
                break;
            case 'right_announcement_classification':
                $orderBy = 'art.status asc,art.refresh_time desc';
                break;
            case 'focus_announcement':
                $query->andFilterCompare('aa.type', BaseArticleAttribute::ATTRIBUTE_RECOMMEND);
                break;
        }

        if ($keywords['outIds']) {
            $query->andWhere([
                'NOT IN',
                'a.id',
                $keywords['outIds'],
            ]);
        }

        $count    = $query->count();
        $pageSize = $keywords['limit'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($keywords['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        foreach ($list as $key => $value) {
            //这里需要默认图片--待产品部门提供
            $value['cover_thumb']       = $value['cover_thumb'] ?: "http://test.static.gaoxiaojob.com/uploads/image/20220111/20220111150218_81088.jpeg";
            $cityName                   = BaseAnnouncement::getOnlineNewCityName($value['id']);
            $list[$key]['city']         = $cityName ? "【" . $cityName . "】" : '';
            $list[$key]['refresh_time'] = TimeHelper::short($value['refresh_time']);
            $list[$key]['cover_thumb']  = UrlHelper::fix($value['cover_thumb']);
            $list[$key]['url']          = BaseAnnouncement::getDetailUrl($value['id']);
            //$list[$key]['educationText'] = trim(BaseJob::getAnnouncementJobEducationType($value['id']));
        }

        return $list;
    }

    /**
     * 栏目页公告数据数据（二级栏目）
     * @throws \Exception
     */
    public static function getSecondColumnAnnouncementList($keywords): array
    {
        //实际这里取焦点资讯
        $query = BaseArticle::find()
            ->alias('art')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'art.id = a.article_id')
            ->leftJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id=art.id')
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'art.id = aa.article_id')
            ->select([
                'a.title',
                'a.id',
                "(CASE  WHEN aa.type in (" . BaseArticleAttribute::ATTRIBUTE_ROLLING . "," . BaseArticleAttribute::ATTRIBUTE_FOCUS . ") THEN 1 ElSE 0 END) as sortNum",
            ])
            ->where([
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'c.column_id'   => $keywords['columnId'],
                'art.status'    => BaseArticle::STATUS_ONLINE,
                'aa.type'       => [
                    BaseArticleAttribute::ATTRIBUTE_ROLLING,
                    BaseArticleAttribute::ATTRIBUTE_FOCUS,
                    BaseArticleAttribute::ATTRIBUTE_RECOMMEND,
                    BaseArticleAttribute::ATTRIBUTE_HOME,
                ],
            ]);

        // $articleAttributeList = BaseArticleAttribute::find()
        //     ->alias('a')
        //     ->select([
        //         'a.id',
        //         'a.article_id',
        //         '(CASE WHEN a.type in (' . BaseArticleAttribute::ATTRIBUTE_ROLLING . ',' . BaseArticleAttribute::ATTRIBUTE_FOCUS . ') THEN 1 ElSE 0 END) as sort',
        //     ])
        //     ->where([
        //         'type' => $type,
        //     ])
        //     ->orderBy('sort desc')
        //     ->asArray()
        //     ->all();
        //
        // $tempIds = [];
        // $temp    = [];
        // foreach ($articleAttributeList as $item) {
        //     if (!in_array($item['article_id'], $temp)) {
        //         $temp[]    = $item['article_id'];
        //         $tempIds[] = $item['id'];
        //     }
        // }
        // $query->andWhere(['aa.id' => $tempIds]);
        $orderBy = 'sortNum desc,art.refresh_time desc,art.id desc';

        $list = $query->limit($keywords['limit'])
            ->orderBy($orderBy)
            ->groupBy("a.id")
            ->asArray()
            ->all();

        foreach ($list as $key => $value) {
            $list[$key]['url'] = BaseAnnouncement::getDetailUrl($value['id']);
        }

        return $list;
    }

    // 找某个栏目下面拥有的可用搜索条件  专业  学历 城市 职位类型
    // 专业可以从job里面取
    public static function getSecondColumnSearchListOld($keywords): array
    {
    }

    /**
     * 栏目页公告导航数据（二级栏目）
     * @throws \Exception
     */
    public static function getSecondColumnSearchList($keywords): array
    {
        $majorIds      = [];
        $educationType = [];
        $cityIds       = [];
        $jobCategoryId = [];

        $allList         = BaseArticle::find()
            ->alias('art')
            ->innerJoin(['a' => BaseAnnouncement::tableName()], 'art.id = a.article_id')
            ->innerJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id=art.id')
            ->select(['a.id'])
            ->where([
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'c.column_id'   => $keywords['columnId'],
                'art.status'    => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
            ])
            ->asArray()
            ->all();
        $announcementIds = [];
        foreach ($allList as $item) {
            $announcementIds[] = $item['id'];
        }

        $jobList = BaseJob::find()
            ->select([
                'major_id',
                'education_type',
                'city_id',
                'job_category_id',
            ])
            ->where([
                'is_show'         => BaseJob::IS_SHOW_YES,
                'is_article'      => BaseJob::IS_ARTICLE_YES,
                'status'          => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
                'announcement_id' => $announcementIds,
            ])
            ->orderBy('education_type desc')
            ->asArray()
            ->all();

        $other = [];
        foreach ($jobList as $item) {
            if (!in_array($item['city_id'], $cityIds)) {
                $cityIds[] = $item['city_id'];
            }
            $majorTmpList = explode(',', $item['major_id']);
            foreach ($majorTmpList as $majorItem) {
                if (in_array($majorItem, $majorIds)) {
                    continue;
                }
                $majorIds[] = $majorItem;
            }
            if (!in_array($item['job_category_id'], $jobCategoryId)) {
                $jobCategoryId[] = $item['job_category_id'];
            }
            if ($item['education_type'] == 5) {
                if (sizeof($other) < 1) {
                    $other[] = $item['education_type'];
                }
            } else {
                if (!in_array($item['education_type'], $educationType)) {
                    $educationType[] = $item['education_type'];
                }
            }
        }

        $educationType = array_merge($educationType, $other);
        $majorIds      = BaseMajor::find()
            ->select([
                'id',
            ])
            ->where([
                'id'    => $majorIds,
                'level' => 2,
            ])
            ->asArray()
            ->column();

        $tapList = [
            'education_type'  => [
                '0' => [
                    'k' => '0',
                    'v' => '学历',
                ],
            ],
            'major_id'        => [
                '0' => [
                    'k' => '0',
                    'v' => '需求专业',
                ],
            ],
            'city_id'         => [
                '0' => [
                    'k' => '0',
                    'v' => '地区',
                ],
            ],
            'job_category_id' => [
                '0' => [
                    'k' => '0',
                    'v' => '职位类型',
                ],
            ],
        ];

        foreach ($tapList as $i => $item) {
            switch ($i) {
                case 'education_type':
                    $temp              = array_unique($educationType);
                    $educationTypeList = BaseDictionary::getEducationList();
                    foreach ($temp as $v) {
                        if ($v > 0) {
                            $name          = $educationTypeList[$v];
                            $tapList[$i][] = [
                                'k' => $v,
                                'v' => $name,
                            ];
                        }
                    }
                    break;
                case 'major_id':
                    $temp = array_unique($majorIds);
                    foreach ($temp as $v) {
                        if ($v > 0) {
                            $name = BaseMajor::getAllMajorName([$v]);
                            if ($name) {
                                $tapList[$i][] = [
                                    'k' => $v,
                                    'v' => $name,
                                ];
                            }
                        }
                    }
                    break;
                case 'city_id':
                    $temp      = array_unique($cityIds);
                    $areaCache = BaseArea::setAreaCache();
                    foreach ($temp as $v) {
                        if ($v > 0) {
                            $name          = $areaCache[$v]['name'];
                            $tapList[$i][] = [
                                'k' => $v,
                                'v' => $name,
                            ];
                        }
                    }
                    break;
                case 'job_category_id':
                    $temp = array_unique($jobCategoryId);
                    foreach ($temp as $v) {
                        if ($v > 0) {
                            $CompanyJobCategory = BaseCategoryJob::getCompanyJobCategory($v);
                            $name               = $CompanyJobCategory['name'];
                            $tapList[$i][]      = [
                                'k' => $v,
                                'v' => $name,
                            ];
                        }
                    }
                    break;
            }
        }

        return $tapList;
    }

    /**
     * 二级栏目模板下面职位筛选项
     * @param $columnId
     * @return array|\string[][][]
     * @throws \Exception
     */
    public static function getSecondColumnJobSearchList($columnId, $data)
    {
        $majorIds      = [];
        $educationType = [];
        $cityIds       = [];
        $jobCategoryId = [];

        if ($data['area']) {
            $citySelect = BaseArea::find()
                ->select('id as k, name as v')
                ->where(['id' => ArrayHelper::getColumn($data['area'], 'id')])
                ->asArray()
                ->all();
        }

        if ($data['major']) {
            $majorSelect = BaseMajor::find()
                ->select('id as k, name as v')
                ->where(['id' => ArrayHelper::getColumn($data['major'], 'id')])
                ->asArray()
                ->all();
        }

        // 找到所有的职位
        $jobList = BaseJob::find()
            ->select([
                'major_id',
                'education_type',
                'city_id',
                'job_category_id',
            ])
            ->innerJoin('job_column', 'job_column.job_id = job.id')
            ->where([
                'job.is_show' => BaseJob::IS_SHOW_YES,
                'job.status'  => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
                'column_id'   => $columnId,
            ])
            ->asArray()
            ->all();

        $other = [];
        foreach ($jobList as $item) {
            if (!in_array($item['city_id'], $cityIds)) {
                $cityIds[] = $item['city_id'];
            }
            $majorTmpList = explode(',', $item['major_id']);
            foreach ($majorTmpList as $majorItem) {
                if (in_array($majorItem, $majorIds)) {
                    continue;
                }
                $majorIds[] = $majorItem;
            }
            if (!in_array($item['job_category_id'], $jobCategoryId)) {
                $jobCategoryId[] = $item['job_category_id'];
            }
            if ($item['education_type'] == 5) {
                if (sizeof($other) < 1) {
                    $other[] = $item['education_type'];
                }
            } else {
                if (!in_array($item['education_type'], $educationType)) {
                    $educationType[] = $item['education_type'];
                }
            }
        }

        $educationType = array_merge($educationType, $other);

        // 学历要重新排序
        sort($educationType);

        $tapList = [
            'education_type'  => [
                '0' => [
                    'k' => '0',
                    'v' => '学历',
                ],
            ],
            'major_id'        => [
                '0' => [
                    'k' => '0',
                    'v' => '需求专业',
                ],
            ],
            'city_id'         => [
                '0' => [
                    'k' => '0',
                    'v' => '地区',
                ],
            ],
            'job_category_id' => [
                '0' => [
                    'k' => '0',
                    'v' => '职位类型',
                ],
            ],
        ];

        foreach ($tapList as $i => $item) {
            switch ($i) {
                case 'education_type':
                    $temp              = array_unique($educationType);
                    $educationTypeList = BaseDictionary::getEducationList();
                    foreach ($temp as $v) {
                        if ($v > 0) {
                            $name          = $educationTypeList[$v];
                            $tapList[$i][] = [
                                'k' => $v,
                                'v' => $name,
                            ];
                        }
                    }
                    break;
                case 'major_id':
                    if ($majorSelect) {
                        break;
                    }
                    $temp = array_unique($majorIds);
                    foreach ($temp as $v) {
                        if ($v > 0) {
                            $name = BaseMajor::getAllMajorName([$v]);
                            if ($name) {
                                $tapList[$i][] = [
                                    'k' => $v,
                                    'v' => $name,
                                ];
                            }
                        }
                    }
                    break;
                case 'city_id':
                    if ($citySelect) {
                        break;
                    }
                    $temp      = array_unique($cityIds);
                    $areaCache = BaseArea::setAreaCache();
                    foreach ($temp as $v) {
                        if ($v > 0) {
                            $name          = $areaCache[$v]['name'];
                            $tapList[$i][] = [
                                'k' => $v,
                                'v' => $name,
                            ];
                        }
                    }
                    break;
                case 'job_category_id':
                    $temp = array_unique($jobCategoryId);
                    foreach ($temp as $v) {
                        if ($v > 0) {
                            $CompanyJobCategory = BaseCategoryJob::getCompanyJobCategory($v);
                            $name               = $CompanyJobCategory['name'];
                            $tapList[$i][]      = [
                                'k' => $v,
                                'v' => $name,
                            ];
                        }
                    }
                    break;
            }
        }

        if ($citySelect) {
            $tapList['city_id'] = $citySelect;
        }

        if ($majorSelect) {
            $tapList['major_id'] = $majorSelect;
        }

        return $tapList;
    }

    /**
     * 栏目页职位数据导航数据（二级栏目）
     * @throws \Exception
     */
    public static function getSecondColumnJobList($keywords): array
    {
        $select = [
            'j.id',
        ];

        $query = BaseJob::find()
            ->alias('j')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id=j.announcement_id')
            ->leftJoin(['art' => BaseArticle::tableName()], 'art.id=a.article_id')
            ->leftJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id=a.article_id')
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'aa.article_id=art.id')
            ->select($select)
            ->where([
                'art.status'    => BaseArticle::STATUS_ACTIVE,
                'c.column_id'   => $keywords['columnId'],
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
            ])
            ->groupBy('j.id');
        $count = $query->count();

        return [
            'count' => $count,
        ];
    }

    /**
     * 栏目页公告数据（政府栏目）
     * @throws \Exception
     */
    public static function getGovernmentColumnAnnouncementList($keywords): array
    {
        $select = [
            'a.id',
            'a.title',
            'a.major_ids',
            'art.refresh_time',
            'count(distinct j.id) as jobAccount',
            'GROUP_CONCAT(j.amount SEPARATOR ",") as amountList',
            'count(j.amount) as amount',
            'j.political_type',
        ];

        $query = BaseArticle::find()
            ->alias('art')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'art.id = a.article_id')
            ->leftJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id=art.id')
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'art.id = aa.article_id')
            ->leftJoin(['j' => BaseJob::tableName()], 'a.id = j.announcement_id')
            ->select($select)
            ->where([
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'c.column_id'   => $keywords['columnId'],
            ])
            ->andWhere([
                'in',
                'art.status',
                [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
            ])
            ->groupBy('a.id');

        $orderBy = 'art.refresh_time desc,art.id desc';

        if ($keywords['major_id']) {
            $majorIds  = explode(',', $keywords['major_id']);
            $condition = ['or'];
            foreach ($majorIds as $item) {
                $condition[] = "find_in_set(" . $item . ",j.major_id)";
            }
            $query->andWhere($condition);
        }

        switch ($keywords['key']) {
            case 'headlines':
                $query->andFilterCompare('aa.type', BaseArticleAttribute::ATTRIBUTE_COLUMN_HEAD);
                break;
            case 'left_hot_announcement':
                $orderBy = 'art.click desc,art.refresh_time desc';
                $query->andWhere([
                    '>=',
                    'art.refresh_time',
                    date('Y-m-d', strtotime("-15 day")),
                ]);
                $query->andFilterCompare('art.status', BaseArticle::STATUS_ONLINE);
                break;
            case 'right_hot_announcement':
                $query->andWhere([
                    'in',
                    'aa.type',
                    [
                        BaseArticleAttribute::ATTRIBUTE_ROLLING,
                        BaseArticleAttribute::ATTRIBUTE_RECOMMEND,
                        BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP,
                        BaseArticleAttribute::ATTRIBUTE_HOME_TOP_ONE,
                        BaseArticleAttribute::ATTRIBUTE_HOME_TOP_TWO,
                        BaseArticleAttribute::ATTRIBUTE_FOCUS,
                    ],
                ]);
                break;
        }

        $count = $query->count();

        $pageSize = $keywords['limit'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($keywords['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        foreach ($list as $key => $value) {
            //这里需要默认图片--待产品部门提供
            $list[$key]['refresh_time'] = TimeHelper::short($value['refresh_time']);
            $list[$key]['cover_thumb']  = UrlHelper::fix($value['cover_thumb']);
            $list[$key]['url']          = BaseAnnouncement::getDetailUrl($value['id']);
        }

        if ($keywords['key'] == "government_latest_announcement") {
            foreach ($list as $key => $value) {
                $list[$key]['major'] = trim(BaseJob::getAnnouncementJobMajor($value['id']));
                $amountList          = explode(',', $value['amountList']);
                if (in_array("若干", $amountList) || in_array("若干人", $amountList)) {
                    $list[$key]['amount'] = "招若干人";
                } else {
                    $list[$key]['amount'] = "招" . $value['amount'] . "人";
                }
                $list[$key]['jobAccount']    = $value['jobAccount'] . "个职位";
                $list[$key]['political']     = BaseDictionary::getPoliticalStatusName($value['political_type']) ?: '';
                $list[$key]['educationText'] = trim(BaseJob::getAnnouncementJobEducationType($value['id']));
            }
        }

        return $list;
    }

    /**
     * 栏目页公告数据（省区栏目）
     * @throws \Exception
     */
    public static function getProvinceColumnAnnouncementList($keywords): array
    {
        $select = [
            'a.id',
            'a.title',
            'a.major_ids',
            'art.refresh_time',
            'art.cover_thumb',
            'y.full_name',
            'j.city_id',
        ];

        $query = BaseArticle::find()
            ->alias('art')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'art.id = a.article_id')
            ->leftJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id = a.article_id')
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'art.id = aa.article_id')
            ->leftJoin(['y' => BaseCompany::tableName()], 'a.company_id = y.id')
            ->leftJoin(['j' => BaseJob::tableName()], 'a.id = j.announcement_id')
            ->select($select)
            ->where([
                'art.status'    => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'c.column_id'   => $keywords['columnId'],
            ])
            ->groupBy('a.id');

        $orderBy = 'art.refresh_time desc,art.id desc';

        switch ($keywords['key']) {
            case 'headlines':
                $query->andFilterCompare('aa.type', BaseArticleAttribute::ATTRIBUTE_COLUMN_HEAD);
                break;
            case 'left_hot_announcement':
                $orderBy = 'art.click desc,art.refresh_time desc,art.id desc';
                $query->andWhere([
                    '>=',
                    'art.refresh_time',
                    date('Y-m-d', strtotime("-15 day")),
                ]);
                $query->andFilterCompare('art.status', BaseArticle::STATUS_ONLINE);
                break;
            case 'right_hot_announcement':
                $query->andWhere([
                    'aa.type' => [
                        BaseArticleAttribute::ATTRIBUTE_RECOMMEND,
                        BaseArticleAttribute::ATTRIBUTE_COLUMN_HEAD,
                        BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP,
                        BaseArticleAttribute::ATTRIBUTE_ROLLING,
                        BaseArticleAttribute::ATTRIBUTE_FOCUS,
                    ],
                ]);
                break;
            case 'right_announcement_classification':
                $orderBy = 'art.refresh_time desc';
                break;
        }

        $count = $query->count();

        $pageSize = $keywords['limit'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($keywords['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        foreach ($list as $key => $value) {
            //这里需要默认图片
            $value['cover_thumb']       = $value['cover_thumb'] ?: "http://test.static.gaoxiaojob.com/uploads/image/20220111/20220111150218_81088.jpeg";
            $cityName                   = BaseAnnouncement::getOnlineNewCityName($value['id']);
            $list[$key]['city']         = $cityName ? "【" . $cityName . "】" : '';
            $list[$key]['refresh_time'] = TimeHelper::short($value['refresh_time']);
            $list[$key]['cover_thumb']  = UrlHelper::fix($value['cover_thumb']);
            $list[$key]['url']          = BaseAnnouncement::getDetailUrl($value['id']);
            //$list[$key]['educationText'] = trim(BaseJob::getAnnouncementJobEducationType($value['id']));
        }

        return $list;
    }

    /**
     * 栏目页公告数据（博士后栏目）
     * @throws \Exception
     */
    public static function getPostDoctorColumnAnnouncementList($keywords): array
    {
        $select = [
            'a.id',
            'a.title',
            'a.major_ids',
            'art.refresh_time',
            'art.cover_thumb',
            'y.full_name',
        ];

        $query = BaseArticle::find()
            ->alias('art')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'art.id = a.article_id')
            ->leftJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id = a.article_id')
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'art.id = aa.article_id')
            ->leftJoin(['y' => BaseCompany::tableName()], 'a.company_id = y.id')
            ->select($select)
            ->where([
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'c.column_id'   => $keywords['columnId'],
                'art.status'    => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
            ])
            ->groupBy('a.id');

        $orderBy = 'art.refresh_time desc,art.id desc';

        switch ($keywords['key']) {
            case 'headlines':
                $query->andFilterCompare('aa.type', BaseArticleAttribute::ATTRIBUTE_COLUMN_HEAD);
                break;
            case 'left_hot_announcement':
                $orderBy = 'art.click desc,art.refresh_time desc,art.id desc';
                $query->andWhere([
                    '>=',
                    'art.refresh_time',
                    date('Y-m-d', strtotime("-15 day")),
                ]);
                $query->andFilterCompare('art.status', BaseArticle::STATUS_ONLINE);
                break;
            case 'right_hot_announcement':
                $query->andFilterCompare('aa.type', [
                    BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP,
                    BaseArticleAttribute::ATTRIBUTE_COLUMN_HEAD,
                    BaseArticleAttribute::ATTRIBUTE_FOCUS,
                    BaseArticleAttribute::ATTRIBUTE_ROLLING,
                ], 'in');
                break;
            case 'right_announcement_classification':
                $orderBy = 'art.refresh_time desc';
                break;
        }

        $count = $query->count();

        $pageSize = $keywords['limit'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($keywords['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        foreach ($list as $key => $value) {
            //这里需要默认图片
            $value['cover_thumb']       = $value['cover_thumb'] ?: "http://test.static.gaoxiaojob.com/uploads/image/20220111/20220111150218_81088.jpeg";
            $cityName                   = BaseAnnouncement::getOnlineNewCityName($value['id']);
            $list[$key]['city']         = $cityName ? "【" . $cityName . "】" : '';
            $list[$key]['refresh_time'] = TimeHelper::short($value['refresh_time']);
            $list[$key]['cover_thumb']  = UrlHelper::fix($value['cover_thumb']);
            $list[$key]['url']          = BaseAnnouncement::getDetailUrl($value['id']);
            //$list[$key]['educationText'] = trim(BaseJob::getAnnouncementJobEducationType($value['id']));
        }

        return $list;
    }

    /**
     * 栏目页精选职位
     * @throws \Exception
     */
    public static function getColumnJobList($keywords): array
    {
        $select = [
            'j.id',
            'j.name',
            'j.wage_type',
            'j.min_wage',
            'j.max_wage',
            'j.experience_type',
            'j.education_type',
            'j.amount',
            'j.city_id',
            'j.major_id',
            'j.period_date',
            'j.release_time',
            'j.refresh_time as time',
            'y.nature',
            'y.full_name',
            'y.type',
            'art.title',
            'art.refresh_time',
        ];

        $query = BaseJob::find()
            ->alias('j')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id=j.announcement_id')
            ->leftJoin(['art' => BaseArticle::tableName()], 'art.id=a.article_id')
            ->leftJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id=a.article_id')
            ->leftJoin(['y' => BaseCompany::tableName()], 'y.id=j.company_id')
            ->select($select)
            ->where([
                'art.status'    => [
                    BaseArticle::STATUS_OFFLINE,
                    BaseArticle::STATUS_ONLINE,
                ],
                'c.column_id'   => $keywords['columnId'],
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'art.is_show'   => BaseArticle::IS_SHOW_YES,
                'j.is_show'     => BaseJob::IS_SHOW_YES,
                'j.status'      => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->groupBy('j.id');

        $query->andFilterCompare('j.job_category_id', $keywords['job_category_id']);

        $orderBy = " j.refresh_time desc,j.id desc";

        if ($keywords['sort_hot']) {
            $query->leftJoin(['p' => JobApply::tableName()], 'j.id = p.job_id');
            $orderBy = " (COUNT(p.id)*0.9+j.click*0.1) desc ";
        }

        if ($keywords['type']) {
            $query->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'aa.article_id=art.id');
            $query->andWhere([
                'in',
                'aa.type',
                $keywords['type'],
            ]);
        }

        $count = $query->count();

        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($keywords['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        $educationTypeList = BaseDictionary::getEducationList(BaseDictionary::ADD_UNLIMITED_NO);
        $experienceList    = BaseDictionary::getExperienceList(BaseDictionary::ADD_UNLIMITED_NO);
        $companyTypeList   = BaseDictionary::getCompanyTypeList();
        $companyNatureList = BaseDictionary::getCompanyNatureList();
        foreach ($list as $key => $value) {
            $majorIds = explode(',', $value['major_id']);
            $length   = sizeof($majorIds);

            if ($length > 0) {
                $major = BaseMajor::getAllMajorName($majorIds[0]);
                if ($length > 1) {
                    $major .= "等";
                }
            } else {
                $major = "";
            }

            $list[$key]['wage']                = BaseJob::formatWage($value['min_wage'], $value['max_wage'],
                $value['wage_type']);
            $list[$key]['experienceTypeTitle'] = $experienceList[intval($value['experience_type'])] ?: '';
            $list[$key]['educationText']       = $educationTypeList[$value['education_type']] ?: '';
            $list[$key]['city']                = BaseArea::getAreaName($value['city_id']) ?: '';
            $list[$key]['companyTypeTitle']    = $companyTypeList[$value['type']] ?: '';
            $list[$key]['companyNatureTitle']  = $companyNatureList[$value['type']] ?: '';
            $list[$key]['major']               = trim($major);
            $list[$key]['amount']              = $value['amount'] . '人';
            $list[$key]['refresh_time']        = TimeHelper::short($value['refresh_time']);
            $list[$key]['url']                 = BaseJob::getDetailUrl($value['id']);
            $list[$key]['period_date']         = TimeHelper::short($value['period_date']);
            $list[$key]['release_time']        = TimeHelper::short($value['release_time']);
            $list[$key]['time']                = TimeHelper::short($value['time']);
        }

        return $list;
    }

    /**
     * 三日热门公告
     * @return array|mixed|\yii\db\ActiveRecord[]
     */
    public static function getThreeDayTop()
    {
        $key = Cache::ALL_ANNOUNCEMENT_HOT_THREE_DAY_KEY;

        $r = Cache::get($key);
        if ($r) {
            return json_decode($r, true);
        }

        return self::setThreeDayTop();
    }

    /**
     * 一周(7日热门公告)
     * @return array|mixed|\yii\db\ActiveRecord[]
     */
    public static function getWeekTop()
    {
        // 这个会有一个定时脚本每天晚上去更新
        $key = Cache::ALL_ANNOUNCEMENT_HOT_WEEK_KEY;

        $r = Cache::get($key);
        if ($r) {
            return json_decode($r, true);
        }

        return self::setWeekTop();
    }

    public static function getHalfMonthTop()
    {
        // 这个会有一个定时脚本每天晚上去更新
        $key = Cache::ALL_ANNOUNCEMENT_HOT_HALF_MONTH_KEY;

        $r = Cache::get($key);
        if ($r) {
            return json_decode($r, true);
        }

        return self::setWeekTop();
    }

    /**
     * 1月(30日热门公告)
     * @return array|mixed|\yii\db\ActiveRecord[]
     */
    public static function getMonthTop()
    {
        // 这个会有一个定时脚本每天晚上去更新
        $key = Cache::ALL_ANNOUNCEMENT_HOT_MONTH_KEY;

        $r = Cache::get($key);
        if ($r) {
            return json_decode($r, true);
        }

        return self::setMonthTop();
    }

    /**
     * 三日热门公告
     */
    public static function setThreeDayTop($day = CUR_DATE)
    {
        // 这个会有一个定时脚本每天晚上去更新
        $endDay    = date('Y-m-d', strtotime("$day -1 day"));
        $beginDay  = date('Y-m-d', strtotime("$day -3 day"));
        $beginTime = TimeHelper::dayToBeginTime($beginDay);
        $endTime   = TimeHelper::dayToEndTime($endDay);
        $list      = self::getHotListByTime($beginTime, $endTime);

        $key = Cache::ALL_ANNOUNCEMENT_HOT_THREE_DAY_KEY;

        Cache::set($key, json_encode($list));

        return $list;
    }

    /**
     * 一周(7日热门公告)
     */
    public static function setWeekTop($day = CUR_DATE)
    {
        // 这个会有一个定时脚本每天晚上去更新
        $endDay    = date('Y-m-d', strtotime("$day -1 day"));
        $beginDay  = date('Y-m-d', strtotime("$day -7 day"));
        $beginTime = TimeHelper::dayToBeginTime($beginDay);
        $endTime   = TimeHelper::dayToEndTime($endDay);
        $list      = self::getHotListByTime($beginTime, $endTime);

        $key = Cache::ALL_ANNOUNCEMENT_HOT_WEEK_KEY;

        Cache::set($key, json_encode($list));

        return $list;
    }

    /**
     * 半个月(15日热门公告)
     */
    public static function setHalfMonthTop($day = CUR_DATE)
    {
        // 这个会有一个定时脚本每天晚上去更新
        $endDay    = date('Y-m-d', strtotime("$day -1 day"));
        $beginDay  = date('Y-m-d', strtotime("$day -15 day"));
        $beginTime = TimeHelper::dayToBeginTime($beginDay);
        $endTime   = TimeHelper::dayToEndTime($endDay);
        $list      = self::getHotListByTime($beginTime, $endTime);

        $key = Cache::ALL_ANNOUNCEMENT_HOT_WEEK_KEY;

        Cache::set($key, json_encode($list));

        return $list;
    }

    /**
     * 1月(30日热门公告)
     */
    public static function setMonthTop($day = CUR_DATE)
    {
        // 这个会有一个定时脚本每天晚上去更新
        $endDay    = date('Y-m-d', strtotime("$day -1 day"));
        $beginDay  = date('Y-m-d', strtotime("$day -30 day"));
        $beginTime = TimeHelper::dayToBeginTime($beginDay);
        $endTime   = TimeHelper::dayToEndTime($endDay);
        $list      = self::getHotListByTime($beginTime, $endTime);

        $key = Cache::ALL_ANNOUNCEMENT_HOT_MONTH_KEY;

        Cache::set($key, json_encode($list));

        return $list;
    }

    public static function getHotListByTime($beginTime, $endTime)
    {
        $list = BaseArticle::find()
            ->alias('a')
            ->leftJoin(['b' => BaseAnnouncement::tableName()], 'a.id = b.article_id')
            ->select([
                'a.id as articleId',
                'b.id as announcementId',
                'click',
            ])
            ->where([
                'a.status'    => BaseArticle::STATUS_ACTIVE,
                'a.is_delete' => BaseArticle::IS_DELETE_NO,
                'a.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
            ])
            ->andFilterCompare('a.refresh_time', $beginTime, '>=')
            ->andFilterCompare('a.refresh_time', $endTime, '<=')
            ->groupBy('a.id')
            ->orderBy('a.click desc')
            ->limit(50)
            ->asArray()
            ->all();

        return $list;
    }

    /**
     * 获取公告下的最小学历
     * @param $announcementId
     * @param $type
     * @return mixed|string
     * @throws \Exception
     */
    public static function getMinEducationName($announcementId, $type = 0)
    {
        if ($type == BaseAnnouncement::STATUS_STAGING) {
            $where = [
                'status' => BaseJob::STATUS_WAIT,
            ];
        } elseif ($type == BaseAnnouncement::STATUS_OFFLINE) {
            $where = [
                'status' => BaseJob::STATUS_OFFLINE,
            ];
        } else {
            $where = [
                'status' => [
                    BaseJob::STATUS_ACTIVE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ];
        }

        $educationType = BaseJob::find()
            ->where([
                'is_show'         => BaseJob::IS_SHOW_YES,
                'announcement_id' => $announcementId,
            ])
            ->andWhere($where)
            ->min('education_type');

        return BaseDictionary::getEducationName($educationType);
    }

    /**
     * 获取公告下的随机一个城市名称
     * @param $announcementId
     * @return false|string
     */
    public static function getOneCityName($announcementId)
    {
        $cityList = BaseJob::find()
            ->where(['announcement_id' => $announcementId])
            ->andWhere([
                'in',
                'status',
                [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->select('city_id')
            ->asArray()
            ->indexBy('city_id')
            ->all();
        if ($cityList) {
            $item = array_rand($cityList);

            return BaseArea::getAreaName($item);
        }

        return '';
    }

    /**
     * 获取公告下职位在线的最新的一个城市名称
     * @param $announcementId
     * @return false|string
     */
    public static function getOnlineNewCityName($announcementId)
    {
        $cityId = BaseJob::find()
            ->andWhere([
                'announcement_id' => $announcementId,
                'status'          => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->select('city_id')
            ->orderBy('refresh_time desc')
            ->asArray()
            ->one();
        if ($cityId['city_id'] > 0) {
            return BaseArea::getAreaName($cityId['city_id']);
        }

        return '';
    }

    /**
     * 获取公告下职位在线的最新的一个城市名称(专供小程序)
     * @param $announcementId
     * @return false|string
     */
    public static function getOnlineNewCityNameForMini($announcementId)
    {
        $cityId = BaseJob::find()
            ->andWhere([
                'announcement_id' => $announcementId,
                'status'          => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->distinct('city_id')
            ->select('city_id')
            ->orderBy('refresh_time desc')
            ->limit(2)
            ->asArray()
            ->all();

        if (count($cityId) > 1) {
            return BaseArea::getAreaName($cityId[0]['city_id']) . '等';
        }

        if (count($cityId) > 0) {
            return BaseArea::getAreaName($cityId[0]['city_id']);
        }

        return '';
    }

    /**
     * 获取公告下的随机一个城市名称(省+市)
     * @param $announcementId
     */
    public static function getOneProvinceCityName($announcementId)
    {
        $cityList = BaseJob::find()
            ->where(['announcement_id' => $announcementId])
            ->andWhere([
                'in',
                'status',
                [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->select('city_id')
            ->asArray()
            ->indexBy('city_id')
            ->all();
        if ($cityList) {
            $cityId   = array_rand($cityList);
            $cityName = BaseArea::getAreaName($cityId);
            if (in_array($cityId, BaseArea::CROWN_ID_LIST)) {
                return [
                    'area'     => $cityName,
                    'city'     => $cityName,
                    'province' => $cityName,
                ];
            }

            if ($cityName == '吉林') {
                return [
                    'area'     => '吉林省吉林市',
                    'city'     => '吉林省',
                    'province' => '吉林市区',
                ];
            }

            // 找到省份的name
            $provinceId = BaseArea::findOneVal(['id' => $cityId], 'parent_id');
            $province   = BaseArea::getAreaName($provinceId);

            return [
                'area'     => $province . $cityName,
                'city'     => $cityName,
                'province' => $province,
            ];
        }

        return [];
    }

    /**
     * 获取公告下的所有城市名称
     * @param $announcementId
     * @param $type
     * @return false|string
     */
    public static function getAllCityName($announcementId, $type = 0)
    {
        if ($type == BaseAnnouncement::STATUS_STAGING) {
            $where = [
                'status' => BaseJob::STATUS_WAIT,
            ];
        } elseif ($type == BaseAnnouncement::STATUS_OFFLINE) {
            $where = [
                'status' => BaseJob::STATUS_OFFLINE,
            ];
        } else {
            $where = [
                'status' => [
                    BaseJob::STATUS_ACTIVE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ];
        }

        $cityList = BaseJob::find()
            ->where([
                'announcement_id' => $announcementId,
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->andWhere($where)
            ->select('city_id')
            ->orderBy('refresh_time desc')
            ->asArray()
            ->indexBy('city_id')
            ->all();
        //去重复，取id
        $cityIds  = array_unique(array_keys($cityList));
        $cityText = '';
        foreach ($cityIds as $cityId) {
            $cityText .= BaseArea::getAreaName($cityId) . ',';
        }

        return $cityText ? substr($cityText, '0', '-1') : '';
    }

    public static function getAllCityId($announcementId)
    {
        $cityList = BaseJob::find()
            ->where(['announcement_id' => $announcementId])
            ->andWhere(['status' => BaseJob::STATUS_ACTIVE])
            ->select('city_id')
            ->asArray()
            ->indexBy('city_id')
            ->all();
        //去重复，取id
        $cityIds = array_unique(array_keys($cityList));

        return $cityIds;
    }

    /**
     * 获取公告下的所有城市名称(栏目页取值专用)
     * @param $announcementId
     * @param $type
     * @return false|string
     */
    public static function getAnnouncementAllCityName($announcementId)
    {
        $where = [
            'status' => [
                BaseJob::STATUS_ONLINE,
                BaseJob::STATUS_OFFLINE,
            ],
        ];

        $cityList = BaseJob::find()
            ->where(['announcement_id' => $announcementId])
            ->andWhere($where)
            ->select('city_id')
            ->asArray()
            ->indexBy('city_id')
            ->all();
        //去重复，取id
        $cityIds  = array_unique(array_keys($cityList));
        $cityText = '';
        foreach ($cityIds as $cityId) {
            $cityText .= BaseArea::getAreaName($cityId) . ',';
        }

        return substr($cityText, '0', '-1');
    }

    /**
     * 获取公告下的所有省份名称
     * @param $announcementId
     * @param $type
     * @return false|string
     */
    public static function getAllProvinceName($announcementId, $type = 0)
    {
        if ($type == BaseAnnouncement::STATUS_STAGING) {
            $where = [
                'status' => BaseJob::STATUS_WAIT,
            ];
        } elseif ($type == BaseAnnouncement::STATUS_OFFLINE) {
            $where = [
                'status' => BaseJob::STATUS_OFFLINE,
            ];
        } else {
            $where = [
                'status' => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ];
        }

        $provinceList = BaseJob::find()
            ->where([
                'announcement_id' => $announcementId,
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->andWhere($where)
            ->select('province_id')
            ->asArray()
            ->indexBy('province_id')
            ->all();

        //去重复，取id
        $provinceIds  = array_unique(array_keys($provinceList));
        $provinceText = '';
        foreach ($provinceIds as $provinceId) {
            $provinceText .= BaseArea::getAreaName($provinceId) . ',';
        }

        return $provinceText ? substr($provinceText, '0', '-1') : '';
    }

    /**
     * 获取公告下所有专业
     * @param $announcementId
     * @throws \Exception
     */
    public static function getAllMajorName($announcementId, $format = 'text', $type = 0)
    {
        if ($type == BaseAnnouncement::STATUS_STAGING) {
            $where = [
                'job.status' => BaseJob::STATUS_WAIT,
            ];
        } elseif ($type == BaseAnnouncement::STATUS_OFFLINE) {
            $where = [
                'job.status' => BaseJob::STATUS_OFFLINE,
            ];
        } else {
            $where = [
                'job.status' => [
                    BaseJob::STATUS_ACTIVE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ];
        }

        $jobList         = BaseJob::find()
            ->innerJoin(['m' => BaseMajor::tableName()], 'job.major_id=m.id')
            ->where([
                'announcement_id' => $announcementId,
                'is_show'         => BaseJob::IS_SHOW_YES,
                'm.status'        => BaseMajor::STATUS_ACTIVE,
            ])
            ->andWhere($where)
            //major_id不为空的职位
            ->andWhere([
                '!=',
                'major_id',
                '',
            ])
            ->select('major_id')
            ->asArray()
            ->all();
        $allMajorInitArr = array_column($jobList, 'major_id');
        $allMajorText    = implode(',', $allMajorInitArr);
        $allMajorArr     = array_unique(explode(',', $allMajorText));
        if ($announcementId == 181404) {
            $targetStr = '70,58,61,49,55,47,57,34,36,19,23,52,59,82,35,38,40,41,43,44,48,50,51,54,56,60,62,63,64,65,67,72,74,75,77,78,79,80,81,84,86,87,98,118,119,120,121,124,127,16,17,18,20,21,25,26,27,28,29,32';
            $targetArr = explode(',', $targetStr);
            //把$allMajorArr在$targetArr中的值放在最前面按照$targetArr的顺序放置，剩下的直接放在后面
            $allMajorArr = array_merge(array_intersect($targetArr, $allMajorArr), array_diff($allMajorArr, $targetArr));
            $majorQuery  = BaseMajor::find()
                ->where(['id' => $allMajorArr]);
            if (count($allMajorArr) > 0) {
                $majorQuery->orderBy([new Expression('FIELD(id,' . implode(',', $allMajorArr) . ') asc')]);
            }

            $majorData = $majorQuery->select(['name'])
                ->asArray()
                ->all();
            if ($majorData) {
                return implode(',', array_column($majorData, 'name'));
            } else {
                return '';
            }
        }
        if ($format == 'text') {
            return BaseMajor::getAllMajorName($allMajorArr);
        } elseif ($format == 'array') {
            $list = [];
            foreach ($allMajorArr as $k => $v) {
                $list[$k]['major'] = BaseMajor::getMajorName($v);
                $list[$k]['url']   = BaseHomeColumnDictionaryRelationship::getUrl(BaseHomeColumnDictionaryRelationship::TYPE_MAJOR,
                    $v);
            }

            //重置数组key
            return array_values($list);
        }
    }

    /**
     * 获取公告下所有学历要求
     * @param $announcementId
     * @return false|string
     */
    public static function getAllEducationName($announcementId, $format = 'text')
    {
        $jobList          = BaseJob::find()
            ->where([
                'is_show'         => BaseJob::IS_SHOW_YES,
                'announcement_id' => $announcementId,
                'status'          => [
                    BaseJob::STATUS_ACTIVE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->andWhere([])
            ->select('education_type')
            ->asArray()
            ->all();
        $allEducationText = '';
        foreach ($jobList as $job) {
            if (empty($job['education_type'])) {
                continue;
            } else {
                $allEducationText .= $job['education_type'] . ',';
            }
        }
        $allEducationText = substr($allEducationText, '0', '-1');
        $allEducationArr  = array_unique(explode(',', $allEducationText));

        if ($format == 'text') {
            foreach ($allEducationArr as $k => $v) {
                $allEducation .= BaseDictionary::getEducationName($v) . ',';
            }
            $allEducation = substr($allEducation, '0', '-1');

            return $allEducation;
        } elseif ($format == 'array') {
            $list = [];
            foreach ($allEducationArr as $k => $v) {
                $list[$k] = BaseMajor::getEducationName($v);
            }

            return $list;
        }
    }

    /**
     * 获取所有福利标签
     * @param $announcementId
     * @return array
     * @throws \Exception
     */
    public static function getAllWelfareLabel($announcementId)
    {
        $welfareList = BaseJobWelfareRelation::find()
            ->alias('jwr')
            ->leftJoin(['j' => BaseJob::tableName()], 'jwr.job_id = j.id')
            ->leftJoin(['wl' => BaseWelfareLabel::tableName()], 'jwr.welfare_id = wl.id')
            ->where(new AndCondition([
                [
                    '=',
                    'j.is_show',
                    BaseJob::IS_SHOW_YES,
                ],
                [
                    '<>',
                    'j.status',
                    BaseJob::STATUS_DELETE,
                ],
                [
                    '=',
                    'j.audit_status',
                    BaseJob::AUDIT_STATUS_PASS_AUDIT,
                ],
                [
                    '=',
                    'jwr.announcement_id',
                    $announcementId,
                ],
                [
                    '=',
                    'wl.status',
                    BaseWelfareLabel::STATUS_ACTIVE,
                ],
            ]))
            ->select(['wl.name'])
            ->asArray()
            ->all();

        if (!$welfareList) {
            return [];
        }

        return array_unique(array_column($welfareList, 'name'));

        //
        //        $jobList        = BaseJob::find()
        //            ->where([
        //                'announcement_id' => $announcementId,
        //                'is_show'         => BaseJob::IS_SHOW_YES,
        //            ])
        //            ->andWhere(['status' => BaseJob::STATUS_ACTIVE])
        //            ->select('welfare_tag')
        //            ->asArray()
        //            ->all();
        //        $allWelfareText = '';
        //        if (count($jobList) > 0) {
        //            foreach ($jobList as $job) {
        //                if (empty($job['welfare_tag'])) {
        //                    continue;
        //                } else {
        //                    $allWelfareText .= $job['welfare_tag'] . ',';
        //                }
        //            }
        //            if (empty($allWelfareText)) {
        //                return [];
        //            }
        //            $allWelfareText    = substr($allWelfareText, '0', '-1');
        //            $allWelfareArr     = array_unique(explode(',', $allWelfareText));
        //            $allWelfareNameArr = [];
        //            foreach ($allWelfareArr as $item) {
        //                array_push($allWelfareNameArr, BaseWelfareLabel::getWelfareLabelName($item));
        //            }
        //
        //            return $allWelfareNameArr;
        //        } else {
        //            return [];
        //        }
    }

    /**
     * 公告下职位的职位类型（岗位类型、职能、职位类型）
     * @throws \Exception
     */
    public static function getHomeColumnJobIdsList($keywords): array
    {
        self::openDb2();
        $select = [
            'j.job_category_id',
            'count(distinct j.id) as jobAccount',
        ];

        $list = BaseArticle::find()
            ->alias('art')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'art.id = a.article_id')
            ->leftJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id = art.id')
            ->leftJoin(['j' => BaseJob::tableName()], 'j.announcement_id = a.id')
            ->select($select)
            ->where([
                'art.status'    => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.is_show'   => BaseArticle::IS_SHOW_YES,
                'j.is_show'     => BaseJob::IS_SHOW_YES,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'c.column_id'   => $keywords['columnId'],
                'j.status'      => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->groupBy('j.job_category_id')
            ->limit(5)
            ->orderBy('jobAccount desc')
            ->asArray()
            ->all();

        foreach ($list as $k => $item) {
            $list[$k]['name'] = BaseCategoryJob::getName($item['job_category_id']);
        }

        self::closeDb2();

        return $list;
    }

    /**
     * 栏目页公告数据（二级栏目切换）
     * @throws \Exception
     */
    public static function changeSecondColumnAnnouncementList($keywords): array
    {
        self::openDb2();
        // // 找到对应栏目下面所有合法的公告
        $announcementColumnQuery = BaseArticle::find()
            ->alias('art')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.article_id=art.id')
            ->leftJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id=art.id')
            ->select('a.id,a.article_id')
            ->where([
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.is_show'   => BaseArticle::IS_SHOW_YES,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'c.column_id'   => $keywords['columnId'],
            ])
            ->andWhere([
                'in',
                'art.status',
                [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
            ]);
        //是否编制查询
        if (!empty($keywords['isEstablishment'])) {
            $announcementColumnQuery->andWhere([
                'establishment_type' => [
                    BaseAnnouncement::ALL_ESTABLISHMENT,
                    BaseAnnouncement::PART_ESTABLISHMENT,
                ],
            ]);
        }
        //公告热度查询
        if (!empty($keywords['announcementHeat'])) {
            $announcementColumnQuery->andWhere([
                'announcement_heat_type' => $keywords['announcementHeat'],
            ]);
        }

        $announcementColumnList = $announcementColumnQuery->orderBy('art.status asc')
            ->asArray()
            ->all();

        $columnAnnouncementIds = array_column($announcementColumnList, 'id');
        $columnArticleIds      = array_column($announcementColumnList, 'article_id');
        $columnArticleIds      = implode(',', $columnArticleIds);

        $jobQuery = BaseJob::find()
            ->select(['announcement_id'])
            ->where([
                'is_show'    => BaseJob::IS_SHOW_YES,
                'is_article' => BaseJob::IS_ARTICLE_YES,
                'status'     => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->andWhere([
                'in',
                'announcement_id',
                $columnAnnouncementIds,
            ]);

        if ($keywords['major_id']) {
            $condition   = ['or'];
            $condition[] = "find_in_set(" . $keywords['major_id'] . ",major_id)";
            $jobQuery->andWhere($condition);
        }

        $jobQuery->andFilterCompare('education_type', $keywords['education_type']);
        $jobQuery->andFilterCompare('city_id', $keywords['city_id']);
        $jobQuery->andFilterCompare('job_category_id', $keywords['job_category_id']);

        $jobColumnList = $jobQuery->asArray()
            ->all();

        $jobAnnouncementIds = array_column($jobColumnList, 'announcement_id');

        if (!$keywords['major_id'] && !$keywords['education_type'] && !$keywords['city_id'] && !$keywords['job_category_id']) {
            $announcementIds = $columnAnnouncementIds;
        } else {
            $announcementIds = array_unique(array_intersect($jobAnnouncementIds, $columnAnnouncementIds));
        }

        $type   = BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP;
        $select = [
            'a.id',
            'a.title',
            'art.status',
            'a.period_date',
            'art.refresh_time',
            'aa.type',
            "(CASE  WHEN aa.type='" . $type . "' THEN 1 ElSE 0 END) as sortNum",
        ];

        if ($columnArticleIds) {
            $columnArticleViews = ' and at.article_id in ( ' . $columnArticleIds . ' ) ';
        } else {
            $columnArticleViews = '';
        }

        $query = BaseArticle::find()
            ->alias('art')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'art.id = a.article_id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'a.company_id = c.id')
            ->leftJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'c.group_score_system_id = cgss.id')
            ->leftJoin(['aa' => '( SELECT at.id,at.type,at.article_id,at.sort_time from `article_attribute` as at where at.type=' . $type . $columnArticleViews . ' order by at.sort_time desc  limit 6 )'],
                'art.id = aa.article_id')
            ->where([
                'in',
                'a.id',
                $announcementIds,
            ])
            ->groupBy('a.id');

        //        $orderBy = 'art.status asc,sortNum desc,aa.sort_time desc,art.refresh_time desc,art.id desc';
        $orderBy = 'art.status asc,sortNum desc,art.refresh_date desc,a.is_first_release asc,cgss.score desc,a.id desc';

        $query->select($select);
        $count = sizeof($announcementIds);

        $pageSize = $keywords['limit'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($keywords['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();
        foreach ($list as $key => $value) {
            $list[$key]['major']        = trim(BaseJob::getAnnouncementJobMajor($value['id']));
            $list[$key]['jobAccount']   = BaseJob::getAnnouncementJobAmount($value['id']) . '个';
            $list[$key]['amount']       = BaseJob::getAnnouncementJobRecruitAmount($value['id']) . '人';
            $list[$key]['refresh_time'] = TimeHelper::short($value['refresh_time']);
            $list[$key]['url']          = BaseAnnouncement::getDetailUrl($value['id']);
            $cityName                   = BaseAnnouncement::getOnlineNewCityName($value['id']);
            $list[$key]['city']         = $cityName ?: '';
            if (TimeHelper::checkIsZeroTimeOrDate($value['period_date'])) {
                $list[$key]['period_date'] = "详见正文";
            } else {
                $list[$key]['period_date'] = TimeHelper::getLong($value['period_date']);
            }
            $allCityName               = BaseAnnouncement::getAnnouncementAllCityName($value['id']);
            $list[$key]['allCityName'] = $allCityName ?: '';
        }

        self::closeDb2();

        return $list;
    }

    function reduce($array): array
    {
        $return = [];
        array_walk_recursive($array, function ($x, $index) use (&$return) {
            $return[] = $x;
        });

        return $return;
    }

    /**
     * 栏目页公告数据统计（二级栏目切换）
     * @throws \Exception
     */
    public static function changeSecondColumnAnnouncementListAccount($keywords): int
    {
        // 这一段走db2
        self::openDb2();
        $announcementColumnQuery = BaseArticle::find()
            ->alias('art')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.article_id=art.id')
            ->leftJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id=art.id')
            ->select('a.id')
            ->where([
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.is_show'   => BaseArticle::IS_SHOW_YES,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'c.column_id'   => $keywords['columnId'],
            ])
            ->andWhere([
                'in',
                'art.status',
                [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
            ]);

        //是否编制查询
        if (!empty($keywords['isEstablishment'])) {
            $announcementColumnQuery->andWhere([
                'establishment_type' => [
                    BaseAnnouncement::ALL_ESTABLISHMENT,
                    BaseAnnouncement::PART_ESTABLISHMENT,
                ],
            ]);
        }
        //公告热度查询
        if (!empty($keywords['announcementHeat'])) {
            $announcementColumnQuery->andWhere([
                'announcement_heat_type' => $keywords['announcementHeat'],
            ]);
        }

        $announcementColumnList = $announcementColumnQuery->asArray()
            ->all();

        $columnAnnouncementIds = array_column($announcementColumnList, 'id');

        if ($keywords['major_id'] || $keywords['education_type'] || $keywords['city_id'] || $keywords['job_category_id']) {
            $jobQuery = BaseJob::find()
                ->select(['GROUP_CONCAT(announcement_id SEPARATOR ",") as announcementIds'])
                ->where([
                    'is_show'    => BaseJob::IS_SHOW_YES,
                    'is_article' => BaseJob::IS_ARTICLE_YES,
                ])
                ->andWhere([
                    'in',
                    'status',
                    [
                        BaseJob::STATUS_ONLINE,
                        BaseJob::STATUS_OFFLINE,
                    ],
                ])
                ->andWhere([
                    'in',
                    'announcement_id',
                    $columnAnnouncementIds,
                ]);

            if ($keywords['major_id']) {
                $condition   = ['or'];
                $condition[] = "find_in_set(" . $keywords['major_id'] . ",major_id)";
                $jobQuery->andWhere($condition);
            }

            $jobQuery->andFilterCompare('education_type', $keywords['education_type']);
            $jobQuery->andFilterCompare('city_id', $keywords['city_id']);
            $jobQuery->andFilterCompare('job_category_id', $keywords['job_category_id']);

            $jobColumnList = $jobQuery->asArray()
                ->one();

            $jobAnnouncementIds = explode(",", $jobColumnList['announcementIds']);
            $announcementIds    = array_unique(array_intersect($jobAnnouncementIds, $columnAnnouncementIds));
        } else {
            $announcementIds = array_unique($columnAnnouncementIds);
        }

        self::closeDb2();

        return sizeof(array_filter($announcementIds));
    }

    /**
     * 栏目页职位数据（二级栏目切换）
     * @throws \Exception
     */ // public static function changeSecondColumnJobList($keywords): array
    // {
    //     //获取当前栏目下所有公告
    //     $allList = BaseArticle::find()
    //         ->alias('art')
    //         ->leftJoin(['a' => BaseAnnouncement::tableName()], 'art.id = a.article_id')
    //         ->leftJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id=art.id')
    //         ->select(['a.id'])
    //         ->where([
    //             'art.is_delete' => BaseArticle::IS_DELETE_NO,
    //             'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
    //             'c.column_id'   => $keywords['columnId'],
    //             'art.status'    => [
    //                 BaseArticle::STATUS_ONLINE,
    //                 BaseArticle::STATUS_OFFLINE,
    //             ],
    //         ])
    //         ->groupBy('a.id')
    //         ->asArray()
    //         ->all();
    //
    //     $announcementIds = [];
    //     foreach ($allList as $item) {
    //         if (!in_array($item['id'], $announcementIds)) {
    //             $announcementIds[] = $item['id'];
    //         }
    //     }
    //
    //     $select = [
    //         'j.id',
    //         'j.status',
    //         'y.full_name',
    //         'y.nature',
    //         'j.min_wage',
    //         'j.max_wage',
    //         'j.wage_type',
    //         'j.name',
    //         'j.major_id',
    //         'j.city_id',
    //         'j.amount',
    //         'j.experience_type',
    //         'j.education_type',
    //         'j.refresh_time as time',
    //     ];
    //
    //     $query = BaseJob::find()
    //         ->alias('j')
    //         ->leftJoin(['y' => BaseCompany::tableName()], 'y.id=j.company_id')
    //         ->select($select)
    //         ->where([
    //             'j.status'          => [
    //                 BaseJob::STATUS_ONLINE,
    //                 BaseJob::STATUS_OFFLINE,
    //             ],
    //             'j.announcement_id' => $announcementIds,
    //         ])
    //         ->groupBy('j.id');
    //
    //     $orderBy = 'j.status desc,j.refresh_time desc,j.id desc';
    //
    //     //todo 学历、需求专业、地区、职能 搜索，联表？与页面展示内容可能出入
    //     if ($keywords['major_id']) {
    //         $condition   = ['or'];
    //         $condition[] = "find_in_set(" . $keywords['major_id'] . ",j.major_id)";
    //         $query->andWhere($condition);
    //     }
    //
    //     $query->andFilterCompare('j.education_type', $keywords['education_type']);
    //     $query->andFilterCompare('j.city_id', $keywords['city_id']);
    //     $query->andFilterCompare('j.job_category_id', $keywords['job_category_id']);
    //
    //     $query->select($select);
    //     $count = $query->count();
    //
    //     $pageSize = $keywords['limit'];
    //     $pages    = self::setPage($count, $keywords['page'], $pageSize);
    //     $list     = $query->offset($pages['offset'])
    //         ->limit($keywords['limit'])
    //         ->orderBy($orderBy)
    //         ->asArray()
    //         ->all();
    //
    //     $experienceList    = BaseDictionary::getExperienceList(BaseDictionary::ADD_UNLIMITED_YES);
    //     $companyNatureList = BaseDictionary::getCompanyNatureList();
    //
    //     foreach ($list as $key => $value) {
    //         $amountList = [$value['amount']];
    //         if (in_array("若干", $amountList) || in_array("若干人", $amountList)) {
    //             $list[$key]['amount'] = "招若干人";
    //         } else {
    //             $list[$key]['amount'] = "招" . $value['amount'] . "人";
    //         }
    //         $majorIds = explode(',', $value['major_id']);
    //         $length   = sizeof($majorIds);
    //         if ($length > 0) {
    //             $major = BaseMajor::getAllMajorName($majorIds[0]) ?: '';
    //             if ($length > 1) {
    //                 $major = BaseMajor::getAllMajorName($majorIds[1]);
    //                 $major .= "等";
    //             }
    //         } else {
    //             $major = "专业不限";
    //         }
    //
    //         $list[$key]['educationText']      = BaseDictionary::getEducationName($value['education_type']) ?: '';
    //         $list[$key]['major']              = trim($major);
    //         $list[$key]['city']               = BaseArea::getAreaName($value['city_id']) ?: '';
    //         $list[$key]['refresh_time']       = TimeHelper::short($value['refresh_time']);
    //         $list[$key]['wage']               = BaseJob::formatWage($value['min_wage'], $value['max_wage'],
    //             $value['wage_type']);
    //         $list[$key]['experience']         = $experienceList[intval($value['experience_type'])] ?: '经验不限';
    //         $list[$key]['companyNatureTitle'] = $companyNatureList[$value['type']] ?: '';
    //         $list[$key]['url']                = BaseJob::getDetailUrl($value['id']);
    //         $list[$key]['time']               = TimeHelper::short($value['time']);
    //     }
    //
    //     // bb($list);
    //     return [
    //         'list'     => $list,
    //         'count'    => $count,
    //         'pageSize' => $pageSize,
    //     ];
    // }

    // 这个是上了自动栏目匹配规则以后的方法
    public static function changeSecondColumnJobList($keywords): array
    {
        self::openDb2();
        $select = [
            'j.id',
            'j.status',
            'y.full_name',
            'y.nature',
            'j.min_wage',
            'j.max_wage',
            'j.wage_type',
            'j.name',
            'j.major_id',
            'j.city_id',
            'j.amount',
            'j.experience_type',
            'j.education_type',
            'j.refresh_time as time',
        ];

        $query = BaseJob::find()
            ->alias('j')
            ->innerJoin(['y' => BaseCompany::tableName()], 'y.id=j.company_id')
            ->innerJoin(['c' => BaseJobColumn::tableName()], 'c.job_id=j.id')
            ->select($select)
            ->where([
                'c.column_id' => $keywords['columnId'],
            ]);

        if ($keywords['applyHeat']) {
            $query->andWhere([
                'j.status' => [
                    BaseJob::STATUS_ONLINE,
                ],
            ]);
        } else {
            $query->andWhere([
                'j.status' => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ]);
        }

        $orderBy = 'j.status desc,refresh_date desc,y.is_cooperation,j.id desc';

        //todo 学历、需求专业、地区、职能 搜索，联表？与页面展示内容可能出入
        if ($keywords['major_id']) {
            $condition   = ['or'];
            $condition[] = "find_in_set(" . $keywords['major_id'] . ",j.major_id)";
            $query->andWhere($condition);
        }

        if ($keywords['isEstablishment']) {
            $query->andWhere(['j.is_establishment' => 1]);
        }

        if ($keywords['applyHeat']) {
            $query->andWhere(['j.apply_heat_type' => $keywords['applyHeat']]);
        }

        $query->andFilterCompare('j.education_type', $keywords['education_type']);
        $query->andFilterCompare('j.city_id', $keywords['city_id']);
        $query->andFilterCompare('j.job_category_id', $keywords['job_category_id']);

        $query->select($select);
        $count = $query->count();

        $pageSize = $keywords['limit'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($keywords['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        $experienceList    = BaseDictionary::getExperienceList(BaseDictionary::ADD_UNLIMITED_YES);
        $companyNatureList = BaseDictionary::getCompanyNatureList();

        foreach ($list as $key => $value) {
            $amountList = [$value['amount']];
            if (in_array("若干", $amountList) || in_array("若干人", $amountList)) {
                $list[$key]['amount'] = "招若干人";
            } else {
                $list[$key]['amount'] = "招" . $value['amount'] . "人";
            }
            $majorIds = explode(',', $value['major_id']);
            $length   = sizeof($majorIds);
            if ($length > 0) {
                $major = BaseMajor::getAllMajorName($majorIds[0]) ?: '';
                if ($length > 1) {
                    $major = BaseMajor::getAllMajorName($majorIds[1]);
                    $major .= "等";
                }
            } else {
                $major = "专业不限";
            }

            $list[$key]['educationText']      = BaseDictionary::getEducationName($value['education_type']) ?: '';
            $list[$key]['major']              = trim($major);
            $list[$key]['city']               = BaseArea::getAreaName($value['city_id']) ?: '';
            $list[$key]['refresh_time']       = TimeHelper::short($value['refresh_time']);
            $list[$key]['wage']               = BaseJob::formatWage($value['min_wage'], $value['max_wage'],
                $value['wage_type']);
            $list[$key]['experience']         = $experienceList[intval($value['experience_type'])] ?: '经验不限';
            $list[$key]['companyNatureTitle'] = $companyNatureList[$value['type']] ?: '';
            $list[$key]['url']                = BaseJob::getDetailUrl($value['id']);
            $list[$key]['time']               = TimeHelper::short($value['time']);
        }

        self::closeDb2();

        return [
            'list'     => $list,
            'count'    => $count,
            'pageSize' => $pageSize,
        ];
    }

    public static function changeSecondColumnJobListOld($keywords): array
    {
        //获取当前栏目下所有公告
        $allList = BaseArticle::find()
            ->alias('art')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'art.id = a.article_id')
            ->leftJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id=art.id')
            ->select(['a.id'])
            ->where([
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'c.column_id'   => $keywords['columnId'],
                'art.status'    => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
            ])
            ->groupBy('a.id')
            ->asArray()
            ->all();

        $announcementIds = [];
        foreach ($allList as $item) {
            if (!in_array($item['id'], $announcementIds)) {
                $announcementIds[] = $item['id'];
            }
        }

        $select = [
            'j.id',
            'j.status',
            'y.full_name',
            'y.nature',
            'j.min_wage',
            'j.max_wage',
            'j.wage_type',
            'j.name',
            'j.major_id',
            'j.city_id',
            'j.amount',
            'j.experience_type',
            'j.education_type',
            'j.refresh_time as time',
        ];

        $query = BaseJob::find()
            ->alias('j')
            ->leftJoin(['y' => BaseCompany::tableName()], 'y.id=j.company_id')
            ->select($select)
            ->where([
                'j.status'          => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
                'j.announcement_id' => $announcementIds,
            ])
            ->groupBy('j.id');

        $orderBy = 'j.status desc,j.refresh_time desc,j.id desc';

        //todo 学历、需求专业、地区、职能 搜索，联表？与页面展示内容可能出入
        if ($keywords['major_id']) {
            $condition   = ['or'];
            $condition[] = "find_in_set(" . $keywords['major_id'] . ",j.major_id)";
            $query->andWhere($condition);
        }

        $query->andFilterCompare('j.education_type', $keywords['education_type']);
        $query->andFilterCompare('j.city_id', $keywords['city_id']);
        $query->andFilterCompare('j.job_category_id', $keywords['job_category_id']);

        $query->select($select);
        $count = $query->count();

        $pageSize = $keywords['limit'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($keywords['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        $experienceList    = BaseDictionary::getExperienceList(BaseDictionary::ADD_UNLIMITED_YES);
        $companyNatureList = BaseDictionary::getCompanyNatureList();

        foreach ($list as $key => $value) {
            $amountList = [$value['amount']];
            if (in_array("若干", $amountList) || in_array("若干人", $amountList)) {
                $list[$key]['amount'] = "招若干人";
            } else {
                $list[$key]['amount'] = "招" . $value['amount'] . "人";
            }
            $majorIds = explode(',', $value['major_id']);
            $length   = sizeof($majorIds);
            if ($length > 0) {
                $major = BaseMajor::getAllMajorName($majorIds[0]) ?: '';
                if ($length > 1) {
                    $major = BaseMajor::getAllMajorName($majorIds[1]);
                    $major .= "等";
                }
            } else {
                $major = "专业不限";
            }

            $list[$key]['educationText']      = BaseDictionary::getEducationName($value['education_type']) ?: '';
            $list[$key]['major']              = trim($major);
            $list[$key]['city']               = BaseArea::getAreaName($value['city_id']) ?: '';
            $list[$key]['refresh_time']       = TimeHelper::short($value['refresh_time']);
            $list[$key]['wage']               = BaseJob::formatWage($value['min_wage'], $value['max_wage'],
                $value['wage_type']);
            $list[$key]['experience']         = $experienceList[intval($value['experience_type'])] ?: '经验不限';
            $list[$key]['companyNatureTitle'] = $companyNatureList[$value['type']] ?: '';
            $list[$key]['url']                = BaseJob::getDetailUrl($value['id']);
            $list[$key]['time']               = TimeHelper::short($value['time']);
        }

        return [
            'list'     => $list,
            'count'    => $count,
            'pageSize' => $pageSize,
        ];
    }

    /**
     * 栏目页公告数据（专题栏目B）
     * @throws \Exception
     */
    public static function getSpecialBColumnAnnouncementList($keywords): array
    {
        self::openDb2();
        $select = [
            'a.id',
            'a.title',
            'art.status',
            'a.major_ids',
            'a.period_date',
            'art.refresh_time',
            'count(distinct j.id) as jobAccount',
            'GROUP_CONCAT(j.amount SEPARATOR ",") as amountList',
            'count(j.amount) as amount',
            'j.political_type',
        ];

        $query = BaseArticle::find()
            ->alias('art')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'art.id = a.article_id')
            ->leftJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id=art.id')
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'art.id = aa.article_id')
            ->leftJoin(['j' => BaseJob::tableName()], 'a.id = j.announcement_id')
            ->select($select)
            ->where([
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'c.column_id'   => $keywords['columnId'],
                'art.status'    => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
            ])
            ->groupBy('a.id');

        $orderBy = ' art.status asc,art.refresh_time desc,art.id desc';

        if ($keywords['major_id']) {
            $majorIds  = explode(',', $keywords['major_id']);
            $condition = ['or'];
            foreach ($majorIds as $item) {
                $condition[] = "find_in_set(" . $item . ",j.major_id)";
            }
            $query->andWhere($condition);
        }

        switch ($keywords['key']) {
            case 'headlines':
                $query->andFilterCompare('aa.type', BaseArticleAttribute::ATTRIBUTE_COLUMN_HEAD);
                break;
            case 'left_hot_announcement':
                $orderBy = 'art.click desc,art.refresh_time desc';
                $query->andWhere([
                    '>=',
                    'art.refresh_time',
                    date('Y-m-d', strtotime("-15 day")),
                ]);
                $query->andFilterCompare('art.status', BaseArticle::STATUS_ONLINE);
                break;
            case 'right_hot_announcement':
                $query->andWhere([
                    'in',
                    'aa.type',
                    [
                        BaseArticleAttribute::ATTRIBUTE_ROLLING,
                        BaseArticleAttribute::ATTRIBUTE_RECOMMEND,
                        BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP,
                        BaseArticleAttribute::ATTRIBUTE_HOME_TOP_ONE,
                        BaseArticleAttribute::ATTRIBUTE_HOME_TOP_TWO,
                        BaseArticleAttribute::ATTRIBUTE_FOCUS,
                    ],
                ]);
                break;
        }

        $count = $query->count();

        $pageSize = $keywords['limit'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($keywords['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        foreach ($list as $key => $value) {
            //这里需要默认图片--待产品部门提供
            $list[$key]['refresh_time'] = TimeHelper::short($value['refresh_time']);
            $list[$key]['cover_thumb']  = UrlHelper::fix($value['cover_thumb']);
            $list[$key]['url']          = BaseAnnouncement::getDetailUrl($value['id']);
        }

        self::closeDb2();

        return [
            'list'  => $list,
            'count' => $count,
        ];
    }

    /**
     * 栏目页公告导航数据（专题栏目B）
     * @throws \Exception
     */
    public static function getSpecialBColumnSearchList($keywords): array
    {
        self::openDb2();
        $allListQuery = BaseArticle::find()
            ->alias('art')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'art.id = a.article_id')
            ->leftJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id=art.id')
            ->select(['a.id'])
            ->where([
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'c.column_id'   => $keywords['columnId'],
                'art.status'    => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
            ]);
        //是否编制查询
        if (!empty($keywords['isEstablishment'])) {
            $allListQuery->andWhere([
                'a.establishment_type' => [
                    BaseAnnouncement::ALL_ESTABLISHMENT,
                    BaseAnnouncement::PART_ESTABLISHMENT,
                ],
            ]);
        }
        //公告热度查询
        if (!empty($keywords['announcementHeat'])) {
            $allListQuery->andWhere([
                'a.announcement_heat_type' => $keywords['announcementHeat'],
            ]);
        }

        $allList         = $allListQuery->asArray()
            ->all();
        $announcementIds = [];
        foreach ($allList as $item) {
            if (!in_array($item['id'], $announcementIds)) {
                $announcementIds[] = $item['id'];
            }
        }

        $jobList = BaseJob::find()
            ->select([
                'major_id',
                'education_type',
                'city_id',
                'job_category_id',
            ])
            ->where([
                'is_show'         => BaseJob::IS_SHOW_YES,
                'is_article'      => BaseJob::IS_ARTICLE_YES,
                'status'          => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
                'announcement_id' => $announcementIds,
            ])
            ->orderBy('education_type desc')
            ->asArray()
            ->all();

        $majorIds      = [];
        $educationType = [];
        $cityIds       = [];
        $jobCategoryId = [];

        $other = [];
        foreach ($jobList as $item) {
            if (!in_array($item['city_id'], $cityIds)) {
                $cityIds[] = $item['city_id'];
            }
            $majorTmpList = explode(',', $item['major_id']);
            foreach ($majorTmpList as $majorItem) {
                if (in_array($majorItem, $majorIds)) {
                    continue;
                }
                $majorIds[] = $majorItem;
            }
            if (!in_array($item['job_category_id'], $jobCategoryId)) {
                $jobCategoryId[] = $item['job_category_id'];
            }
            if ($item['education_type'] == 5) {
                if (sizeof($other) < 1) {
                    $other[] = $item['education_type'];
                }
            } else {
                if (!in_array($item['education_type'], $educationType)) {
                    $educationType[] = $item['education_type'];
                }
            }
        }

        $educationType = array_merge($educationType, $other);

        $tapList = [
            'education_type'  => [
                '0' => [
                    'k' => '0',
                    'v' => '学历',
                ],
            ],
            'major_id'        => [
                '0' => [
                    'k' => '0',
                    'v' => '需求专业',
                ],
            ],
            'city_id'         => [
                '0' => [
                    'k' => '0',
                    'v' => '地区',
                ],
            ],
            'job_category_id' => [
                '0' => [
                    'k' => '0',
                    'v' => '职位类型',
                ],
            ],
        ];

        foreach ($tapList as $i => $item) {
            switch ($i) {
                case 'education_type':
                    $temp              = array_unique($educationType);
                    $educationTypeList = BaseDictionary::getEducationList();
                    foreach ($temp as $v) {
                        if ($v > 0) {
                            $name          = $educationTypeList[$v];
                            $tapList[$i][] = [
                                'k' => $v,
                                'v' => $name,
                            ];
                        }
                    }
                    break;
                case 'major_id':
                    $temp = array_unique($majorIds);
                    foreach ($temp as $v) {
                        if ($v > 0) {
                            $name = BaseMajor::getAllMajorName([$v]);
                            if ($name) {
                                $tapList[$i][] = [
                                    'k' => $v,
                                    'v' => $name,
                                ];
                            }
                        }
                    }
                    break;
                case 'city_id':
                    $temp      = array_unique($cityIds);
                    $areaCache = BaseArea::setAreaCache();
                    foreach ($temp as $v) {
                        if ($v > 0) {
                            $name          = $areaCache[$v]['name'];
                            $tapList[$i][] = [
                                'k' => $v,
                                'v' => $name,
                            ];
                        }
                    }
                    break;
                case 'job_category_id':
                    $temp = array_unique($jobCategoryId);
                    foreach ($temp as $v) {
                        if ($v > 0) {
                            $CompanyJobCategory = BaseCategoryJob::getCompanyJobCategory($v);
                            $name               = $CompanyJobCategory['name'];
                            $tapList[$i][]      = [
                                'k' => $v,
                                'v' => $name,
                            ];
                        }
                    }
                    break;
            }
        }
        self::closeDb2();

        return $tapList;
    }

    /**
     * 栏目页公告数据（二级栏目切换）
     * @throws \Exception
     */
    public static function changeSpecialBColumnAnnouncementList($keywords): array
    {
        self::openDb2();
        //找到对应栏目下面所有合法的公告
        $announcementColumnQuery = BaseArticle::find()
            ->alias('art')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.article_id=art.id')
            ->leftJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id=art.id')
            ->select('a.id,a.article_id')
            ->where([
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.is_show'   => BaseArticle::IS_SHOW_YES,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'c.column_id'   => $keywords['columnId'],
            ])
            ->andWhere([
                'in',
                'art.status',
                [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
            ]);

        //是否编制查询
        if (!empty($keywords['isEstablishment'])) {
            $announcementColumnQuery->andWhere([
                'establishment_type' => [
                    BaseAnnouncement::ALL_ESTABLISHMENT,
                    BaseAnnouncement::PART_ESTABLISHMENT,
                ],
            ]);
        }
        //公告热度查询
        if (!empty($keywords['announcementHeat'])) {
            $announcementColumnQuery->andWhere([
                'announcement_heat_type' => $keywords['announcementHeat'],
            ]);
        }

        $announcementColumnList = $announcementColumnQuery->orderBy('art.status asc')
            ->asArray()
            ->all();

        $columnAnnouncementIds = array_column($announcementColumnList, 'id');
        $columnArticleIds      = array_column($announcementColumnList, 'article_id');
        $columnArticleIds      = implode(',', $columnArticleIds);

        $jobQuery = BaseJob::find()
            ->select(['announcement_id'])
            ->where([
                'is_show'    => BaseJob::IS_SHOW_YES,
                'is_article' => BaseJob::IS_ARTICLE_YES,
                'status'     => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->andWhere([
                'in',
                'announcement_id',
                $columnAnnouncementIds,
            ]);

        if ($keywords['major_id']) {
            $condition   = ['or'];
            $condition[] = "find_in_set(" . $keywords['major_id'] . ",major_id)";
            $jobQuery->andWhere($condition);
        }

        $jobQuery->andFilterCompare('education_type', $keywords['education_type']);
        $jobQuery->andFilterCompare('city_id', $keywords['city_id']);
        $jobQuery->andFilterCompare('job_category_id', $keywords['job_category_id']);

        $jobColumnList = $jobQuery->asArray()
            ->all();

        $jobAnnouncementIds = array_column($jobColumnList, 'announcement_id');

        if (!$keywords['major_id'] && !$keywords['education_type'] && !$keywords['city_id'] && !$keywords['job_category_id']) {
            $announcementIds = $columnAnnouncementIds;
        } else {
            $announcementIds = array_unique(array_intersect($jobAnnouncementIds, $columnAnnouncementIds));
        }

        $type   = BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP;
        $select = [
            'a.id',
            'a.title',
            'art.status',
            'a.period_date',
            'art.refresh_time',
            'aa.type',
            "(CASE  WHEN aa.type='" . $type . "' THEN 1 ElSE 0 END) as sortNum",
        ];

        if ($columnArticleIds) {
            $columnArticleViews = ' and at.article_id in ( ' . $columnArticleIds . ' ) ';
        } else {
            $columnArticleViews = '';
        }

        $query = BaseArticle::find()
            ->alias('art')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'art.id = a.article_id')
            ->leftJoin(['aa' => '( SELECT at.id,at.type,at.article_id,at.sort_time from `article_attribute` as at where at.type=' . $type . $columnArticleViews . ' order by at.sort_time desc  limit 6 )'],
                'art.id = aa.article_id')
            ->where([
                'in',
                'a.id',
                $announcementIds,
            ])
            ->groupBy('a.id');

        $orderBy = 'art.status asc,sortNum desc,aa.sort_time desc,art.refresh_time desc,art.id desc';

        $query->select($select);
        $count = sizeof($announcementIds);

        $pageSize = $keywords['limit'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($keywords['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        foreach ($list as $key => $value) {
            $list[$key]['major']        = trim(BaseJob::getAnnouncementJobMajor($value['id']));
            $list[$key]['jobAccount']   = BaseJob::getAnnouncementJobAmount($value['id']) . '个';
            $list[$key]['amount']       = BaseJob::getAnnouncementJobRecruitAmount($value['id']) . '人';
            $list[$key]['refresh_time'] = TimeHelper::short($value['refresh_time']);
            $list[$key]['url']          = BaseAnnouncement::getDetailUrl($value['id']);
            $cityName                   = BaseAnnouncement::getAllCityName($value['id']);
            $list[$key]['city']         = $cityName ?: '';
            if (TimeHelper::checkIsZeroTimeOrDate($value['period_date'])) {
                $list[$key]['period_date'] = "详见正文";
            } else {
                $list[$key]['period_date'] = TimeHelper::getLong($value['period_date']);
            }
            $allCityName               = BaseAnnouncement::getAnnouncementAllCityName($value['id']);
            $list[$key]['allCityName'] = $allCityName ?: '';
        }

        self::closeDb2();

        return [
            'list'  => $list,
            'count' => $count,
        ];
    }

    public static function changeSpecialBColumnAnnouncementListNew($keywords): array
    {
        self::openDb2();
        //找到对应栏目下面所有合法的公告
        $announcementColumnQuery = BaseArticle::find()
            ->alias('art')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.article_id=art.id')
            ->leftJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id=art.id')
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'aa.article_id=a.article_id')
            ->select('a.id,a.article_id')
            ->where([
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.is_show'   => BaseArticle::IS_SHOW_YES,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'c.column_id'   => $keywords['columnId'],
            ])
            ->andWhere([
                'in',
                'art.status',
                [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
            ])
            ->groupBy('a.id');

        //是否编制查询
        if (!empty($keywords['isEstablishment'])) {
            $announcementColumnQuery->andWhere([
                'establishment_type' => [
                    BaseAnnouncement::ALL_ESTABLISHMENT,
                    BaseAnnouncement::PART_ESTABLISHMENT,
                ],
            ]);
        }
        //公告热度查询
        if (!empty($keywords['announcementHeat'])) {
            $announcementColumnQuery->andWhere([
                'announcement_heat_type' => $keywords['announcementHeat'],
            ]);
        }
        //学历查询
        //$jobQuery->andFilterCompare('education_type', $keywords['education_type']);
        if (!empty($keywords['education_type'])) {
            $announcementColumnQuery->leftJoin(['aer' => BaseAnnouncementEducationRelation::tableName()],
                'a.id=aer.announcement_id')
                ->andWhere(['aer.education_code' => $keywords['education_type']]);
        }
        //专业
        if (!empty($keywords['major_id'])) {
            $announcementColumnQuery->leftJoin(['jmr' => BaseJobMajorRelation::tableName()], 'a.id=jmr.announcement_id')
                ->andWhere(['jmr.major_id' => $keywords['major_id']]);
        }
        //地区
        if (!empty($keywords['city_id'])) {
            $announcementColumnQuery->leftJoin(['aar' => BaseAnnouncementAreaRelation::tableName()],
                'a.id=aar.announcement_id and aar.level=2')
                ->andWhere(['aar.area_id' => $keywords['city_id']]);
        }
        //职位类型
        if (!empty($keywords['job_category_id'])) {
            $announcementJobCategoryIds = BaseJob::find()
                ->select('announcement_id')
                ->where([
                    'job_category_id' => $keywords['job_category_id'],
                    'is_show'         => BaseJob::IS_SHOW_YES,
                    'is_article'      => BaseJob::IS_ARTICLE_YES,
                    'status'          => [
                        BaseJob::STATUS_ONLINE,
                        BaseJob::STATUS_OFFLINE,
                    ],
                ])
                ->andWhere([
                    '>',
                    'announcement_id',
                    0,
                ])
                ->column();
            $announcementColumnQuery->andWhere(['a.id' => $announcementJobCategoryIds]);
        }
        //如果是第一页就先拿六条制定出来，如果不是则不用处理
        $keywords['page'] = !isset($keywords['page']) || $keywords['page'] <= 0 ? 1 : $keywords['page'];

        $topQuery = clone $announcementColumnQuery;
        $topList  = $topQuery->select([
            'a.id',
            'a.title',
            'art.status',
            'a.period_date',
            'art.refresh_time',
            'aa.type',
            "(CASE  WHEN aa.type='" . BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP . "' THEN 1 ElSE 0 END) as sortNum",
        ])
            ->andWhere(['aa.type' => BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP])
            ->groupBy('a.id')
            ->limit(6)
            ->orderBy('aa.sort_time desc')
            ->asArray()
            ->all();
        if ($topList) {
            //剔除掉已经在顶部的公告
            $announcementColumnQuery->andWhere([
                'not in',
                'a.id',
                array_column($topList, 'id'),
            ]);
        }

        if ($keywords['page'] == 1) {
            $keywords['limit'] = $keywords['limit'] - count($topList);
        }
        $count    = ($announcementColumnQuery->count()) + count($topList);
        $pageSize = $keywords['limit'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $list     = $announcementColumnQuery->select([
            'a.id',
            'a.title',
            'art.status',
            'a.period_date',
            'art.refresh_time',
            'aa.type',
            "(CASE  WHEN aa.type='" . BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP . "' THEN 1 ElSE 0 END) as sortNum",
        ])
            ->orderBy('art.status asc,sortNum desc,art.refresh_time desc,art.id desc')
            ->groupBy('a.id')
            ->offset($pages['offset'] - count($topList))
            ->limit($keywords['limit'])
            ->asArray()
            ->all();
        if ($keywords['page'] == 1) {
            $list = array_merge($topList, $list);
        }

        foreach ($list as $key => $value) {
            $list[$key]['major']        = trim(BaseJob::getAnnouncementJobMajor($value['id']));
            $list[$key]['jobAccount']   = BaseJob::getAnnouncementJobAmount($value['id']) . '个';
            $list[$key]['amount']       = BaseJob::getAnnouncementJobRecruitAmount($value['id']) . '人';
            $list[$key]['refresh_time'] = TimeHelper::short($value['refresh_time']);
            $list[$key]['url']          = BaseAnnouncement::getDetailUrl($value['id']);
            $cityName                   = BaseAnnouncement::getAllCityName($value['id']);
            $list[$key]['city']         = $cityName ?: '';
            if (TimeHelper::checkIsZeroTimeOrDate($value['period_date'])) {
                $list[$key]['period_date'] = "详见正文";
            } else {
                $list[$key]['period_date'] = TimeHelper::getLong($value['period_date']);
            }
            $allCityName               = BaseAnnouncement::getAnnouncementAllCityName($value['id']);
            $list[$key]['allCityName'] = $allCityName ?: '';
        }

        self::closeDb2();

        return [
            'list'  => $list,
            'count' => $count,
        ];
    }

    /**
     * 栏目页精选职位（政府与事业单位/专题栏目A）
     * @throws \Exception
     */
    public static function getGovernmentColumnJobList($keywords): array
    {
        $select = [
            'j.id',
            'j.name',
            'j.wage_type',
            'j.min_wage',
            'j.max_wage',
            'j.experience_type',
            'j.education_type',
            'j.amount',
            'j.city_id',
            'j.major_id',
            'j.period_date',
            'j.release_time',
            'j.refresh_time as time',
            'y.nature',
            'y.full_name',
            'y.type',
            'art.title',
            'art.refresh_time',
        ];

        $query = BaseJob::find()
            ->alias('j')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id=j.announcement_id')
            ->leftJoin(['art' => BaseArticle::tableName()], 'art.id=a.article_id')
            ->leftJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id=a.article_id')
            ->leftJoin(['y' => BaseCompany::tableName()], 'y.id=j.company_id')
            ->select($select)
            ->where([
                'art.status'    => [
                    BaseArticle::STATUS_OFFLINE,
                    BaseArticle::STATUS_ONLINE,
                ],
                'c.column_id'   => $keywords['columnId'],
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'art.is_show'   => BaseArticle::IS_SHOW_YES,
                'j.is_show'     => BaseJob::IS_SHOW_YES,
                'j.status'      => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->groupBy('j.id');

        $query->andFilterCompare('j.job_category_id', $keywords['job_category_id']);

        $orderBy = " j.refresh_time desc,j.id desc";

        if ($keywords['sort_hot']) {
            $query->leftJoin(['p' => JobApply::tableName()], 'j.id = p.job_id');
            $orderBy = " (COUNT(p.id)*0.9+j.click*0.1) desc ";
        }

        if ($keywords['type']) {
            $query->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'aa.article_id=art.id');
            $query->andWhere([
                'in',
                'aa.type',
                $keywords['type'],
            ]);
        }

        $count = $query->count();

        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($keywords['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        $educationTypeList = BaseDictionary::getEducationList(BaseDictionary::ADD_UNLIMITED_NO);
        $experienceList    = BaseDictionary::getExperienceList(BaseDictionary::ADD_UNLIMITED_NO);
        $companyTypeList   = BaseDictionary::getCompanyTypeList();
        $companyNatureList = BaseDictionary::getCompanyNatureList();
        foreach ($list as $key => $value) {
            $majorIds = explode(',', $value['major_id']);
            $length   = sizeof($majorIds);

            if ($length > 0) {
                $major = BaseMajor::getAllMajorName($majorIds[0]);
                if ($length > 1) {
                    $major .= "等";
                }
            } else {
                $major = "";
            }

            $list[$key]['wage']                = BaseJob::formatWage($value['min_wage'], $value['max_wage'],
                $value['wage_type']);
            $list[$key]['experienceTypeTitle'] = $experienceList[intval($value['experience_type'])] ?: '';
            $list[$key]['educationText']       = $educationTypeList[$value['education_type']] ?: '';
            $list[$key]['city']                = BaseArea::getAreaName($value['city_id']) ?: '';
            $list[$key]['companyTypeTitle']    = $companyTypeList[$value['type']] ?: '';
            $list[$key]['companyNatureTitle']  = $companyNatureList[$value['type']] ?: '';
            $list[$key]['major']               = trim($major);
            $list[$key]['amount']              = $value['amount'] . '人';
            $list[$key]['refresh_time']        = TimeHelper::short($value['refresh_time']);
            $list[$key]['url']                 = BaseJob::getDetailUrl($value['id']);
            $list[$key]['period_date']         = TimeHelper::short($value['period_date']);
            $list[$key]['release_time']        = TimeHelper::short($value['release_time']);
            if (strtotime($value['time']) < 1) {
                $list[$key]['time'] = TimeHelper::short($value['release_time']);
            } else {
                $list[$key]['time'] = TimeHelper::short($value['time']);
            }
        }

        return [
            'list'     => $list,
            'count'    => $count,
            'pageSize' => 10,
        ];
    }

    /**
     * 获取公告审核状态
     * @param $jobId
     * @return array|false
     */
    public static function getAuditStatus($jobId)
    {
        $job = BaseJob::findOne($jobId);
        if (!$job) {
            return false;
        }
        $announcement = BaseAnnouncement::findOne($job->announcement_id);
        if (!$announcement) {
            return false;
        }
        $article = BaseArticle::findOne($announcement->article_id);
        if (!$article) {
            return false;
        }

        $articleStatus           = $article->status;
        $announcementAuditStatus = $announcement->audit_status;
        $jobAuditStatus          = $job->audit_status;
        $jobStatus               = $job->status;
        $isArticle               = $job->is_article;

        return [
            'articleStatus'           => $articleStatus,
            'announcementAuditStatus' => $announcementAuditStatus,
            'jobAuditStatus'          => $jobAuditStatus,
            'jobStatus'               => $jobStatus,
            'isArticle'               => $isArticle,
        ];
    }

    /**
     * 获取公告下的所有省份名称带栏目链接
     * @param $announcementId
     * @param $type
     * @return false|string
     * @throws \Exception
     */
    public static function getAllProvinceList($announcementId, $type = 0)
    {
        if ($type == BaseAnnouncement::STATUS_STAGING) {
            $where = [
                'status' => BaseJob::STATUS_WAIT,
            ];
        } elseif ($type == BaseAnnouncement::STATUS_OFFLINE) {
            $where = [
                'status' => BaseJob::STATUS_OFFLINE,
            ];
        } else {
            $where = [
                'status' => [
                    BaseJob::STATUS_ACTIVE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ];
        }

        $provinceList = BaseJob::find()
            ->where([
                'announcement_id' => $announcementId,
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->andWhere($where)
            ->select('province_id')
            ->asArray()
            ->indexBy('province_id')
            ->all();

        if (!$provinceList) {
            return [];
        }

        //去重复，取id
        $provinceIds = array_unique(array_keys($provinceList));
        $count       = sizeof($provinceIds);

        $list = [];
        foreach ($provinceIds as $k => $provinceId) {
            if ($k + 1 < $count) {
                $list[$k]['provinceText'] = BaseArea::getAreaName($provinceId) . ',';
            } else {
                $list[$k]['provinceText'] = BaseArea::getAreaName($provinceId);
            }
            $list[$k]['url'] = BaseHomeColumnDictionaryRelationship::getUrl(BaseHomeColumnDictionaryRelationship::TYPE_AREA,
                $provinceId);
        }

        return $list;
    }

    /**
     * 获取公告下的所有城市名称带栏目链接
     * @param $announcementId
     * @param $type
     * @return false|string
     */
    public static function getAllCityList($announcementId, $type = 0)
    {
        if ($type == BaseAnnouncement::STATUS_STAGING) {
            $where = [
                'status' => BaseJob::STATUS_WAIT,
            ];
        } elseif ($type == BaseAnnouncement::STATUS_OFFLINE) {
            $where = [
                'status' => BaseJob::STATUS_OFFLINE,
            ];
        } else {
            $where = [
                'status' => [
                    BaseJob::STATUS_ACTIVE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ];
        }

        $cityList = BaseJob::find()
            ->where([
                'announcement_id' => $announcementId,
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->andWhere($where)
            ->select('city_id')
            ->asArray()
            ->indexBy('city_id')
            ->all();
        //去重复，取id
        $cityIds = array_unique(array_keys($cityList));
        $count   = sizeof($cityIds);
        $list    = [];

        foreach ($cityIds as $k => $provinceId) {
            if ($k + 1 < $count) {
                $list[$k]['cityText'] = BaseArea::getAreaName($provinceId) . ',';
            } else {
                $list[$k]['cityText'] = BaseArea::getAreaName($provinceId);
            }
            $list[$k]['url'] = BaseHomeColumnDictionaryRelationship::getUrl(BaseHomeColumnDictionaryRelationship::TYPE_AREA,
                $provinceId, BaseArea::getAreaName($provinceId));
        }

        return $list;
    }

    /**
     * 获取公告的部分统计信息
     * 职位个数、招聘总人数、工作地点、近七天简历、累计简历数量
     * 企业端专用，部分可能不适用
     * @throws Exception
     */
    public static function getAnnouncementInfoList($announcementId): array
    {
        $query = BaseJob::find()
            ->alias('j')
            ->select([
                'j.id',
                'j.city_id',
                'j.amount',
                'j.education_type',
            ])
            ->where([
                'j.status'          => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
                'j.announcement_id' => $announcementId,
            ]);

        $jobAllList = $query->asArray()
            ->all();

        $authorityList = (new CompanyAuthorityClassify())->run([
            'associatedField' => 'j.company_id',
            'memberId'        => Yii::$app->user->id,
            'query'           => $query,
            'returnType'      => CompanyAuthorityClassify::DATA_JOB_COOPERATE,
        ]);

        if ($authorityList) {
            $query = $authorityList['query'];
        }

        $jobList      = $query->asArray()
            ->all();
        $educationArr = [];

        foreach ($jobList as $job) {
            $educationArr[] = $job['education_type'];
        }
        // 学历要求
        if (count($educationArr) > 1) {
            $educationMin = min($educationArr);
            $education    = BaseDictionary::getEducationName($educationMin);

            $announcementEducationName = !empty($education) ? BaseDictionary::getEducationName($educationMin) . ($educationMin == BaseDictionary::EDUCATION_DOCTOR_ID ? '' : '及以上') : '未填';
        } else {
            $announcementEducationName = !empty($educationArr) ? BaseDictionary::getEducationName($educationArr[0]) . ($educationArr[0] == BaseDictionary::EDUCATION_DOCTOR_ID ? '' : '及以上') : '未填';
        }

        $jobIds      = array_column($jobList, 'id');
        $amountArray = array_column($jobAllList, 'amount');

        $jobAccount    = sizeof($jobAllList); //职位总个数
        $amountAccount = 0;

        if (in_array('若干', $amountArray) || in_array('若干人', $amountArray)) {
            $amountAccount = "若干";
        } else {
            foreach ($amountArray as $item) {
                $amountAccount += $item;
            }
        }
        $cityName     = self::getAllCityName($announcementId);
        $jobApplyList = BaseJobApply::find()
            ->select([
                'id',
                'add_time',
            ])
            ->where([
                'job_id' => $jobIds,
            ])
            ->asArray()
            ->all();

        $jobApplyAccount     = 0;
        $jobApplyWeekAccount = 0;
        foreach ($jobApplyList as $item) {
            $jobApplyAccount++;
            $day = count(TimeHelper::getDateList(date('Y-m-d', strtotime($item['add_time'])), CUR_DATE));
            if ($day < 8) {
                $jobApplyWeekAccount++;
            }
        }

        return [
            'jobAccount'                => $jobAccount,
            'amountAccount'             => $amountAccount,
            'cityName'                  => $cityName,
            'jobApplyWeekAccount'       => $jobApplyWeekAccount,
            'jobApplyAccount'           => $jobApplyAccount,
            'announcementEducationName' => $announcementEducationName,
        ];
    }

    /**
     * 获取栏目页文档属性"栏目置顶"最新公告四条
     */
    public static function getColumnAnnouncementTopList($announcementId): array
    {
        $list = BaseArticle::find()
            ->alias('art')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'art.id = a.article_id')
            ->leftJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id=art.id')
            ->innerJoin(['aa' => BaseArticleAttribute::tableName()], 'art.id = aa.article_id')
            ->select([
                'a.id',
                'a.title',
                'art.refresh_time',
                'art.status',
            ])
            ->where([
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.is_show'   => BaseArticle::IS_SHOW_YES,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'c.column_id'   => $announcementId,
                'art.status'    => [
                    BaseArticle::STATUS_ONLINE,
                ],
                'aa.type'       => BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP,
            ])
            ->orderBy('aa.sort_time desc')
            ->groupBy('a.id')
            ->limit(4)
            ->asArray()
            ->all();

        foreach ($list as $key => $value) {
            $list[$key]['refresh_time'] = TimeHelper::short($value['refresh_time']);
            $list[$key]['url']          = BaseAnnouncement::getDetailUrl($value['id']);
            $list[$key]['sortNum']      = 1;
        }

        return $list;
    }

    /**
     * 栏目页推荐公告&简章（新规则）
     * 这里做新得函数，旧的不修改
     * 一级栏目通用模板A/B、博士后栏目模板/B、专场通用模板A/B
     * @throws \Exception
     */
    public static function getRecommendAnnouncementList($keywords): array
    {
        $articleAttributeList = BaseArticleAttribute::find()
            ->alias('a')
            ->select([
                'a.id',
                'a.article_id',
                '(CASE a.type WHEN "3" THEN 1 ElSE 0 END) as sort',
            ])
            ->where([
                'type' => [
                    BaseArticleAttribute::ATTRIBUTE_HOME_TOP,
                    BaseArticleAttribute::ATTRIBUTE_ROLLING,
                    BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP,
                    BaseArticleAttribute::ATTRIBUTE_COLUMN_HEAD,
                ],
            ])
            ->orderBy('sort desc')
            ->asArray()
            ->all();

        $tempIds = [];
        $temp    = [];
        foreach ($articleAttributeList as $item) {
            if (!in_array($item['article_id'], $temp)) {
                $temp[]    = $item['article_id'];
                $tempIds[] = $item['id'];
            }
        }

        $select = [
            'a.id',
            'a.title',
            'art.refresh_time',
            'art.refresh_date as date',
            'aa.type',
            '(CASE aa.type WHEN "' . BaseArticleAttribute::ATTRIBUTE_HOME_TOP . '" THEN 1 ElSE 0 END) as sortNum',
        ];

        $query = BaseArticle::find()
            ->alias('art')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'art.id = a.article_id')
            ->leftJoin(['com' => BaseCompany::tableName()], 'a.company_id=com.id')
            ->leftJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'cgss.id=com.group_score_system_id')
            ->leftJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id=art.id')
            ->innerJoin(['aa' => BaseArticleAttribute::tableName()], 'art.id = aa.article_id')
            ->select($select)
            ->where([
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.is_show'   => BaseArticle::IS_SHOW_YES,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'c.column_id'   => $keywords['columnId'],
                'art.status'    => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
                'aa.id'         => $tempIds,
            ])
            ->groupBy('a.id');

        //        $orderBy = 'date desc,sortNum desc,art.refresh_time desc';
        $orderBy = 'date desc,sortNum desc,a.is_first_release asc,cgss.score desc,a.id desc';

        $count    = $query->count();
        $pageSize = $keywords['limit'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($keywords['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        foreach ($list as $key => $value) {
            $list[$key]['refresh_time'] = TimeHelper::short($value['refresh_time']);
            $list[$key]['url']          = BaseAnnouncement::getDetailUrl($value['id']);
        }

        return $list;
    }

    /**
     * 栏目页推荐公告&简章（新规则）
     * 省区模版
     * @throws \Exception
     */
    public static function getProvinceRecommendAnnouncementList($keywords): array
    {
        $articleAttributeList = BaseArticleAttribute::find()
            ->alias('a')
            ->select([
                'a.id',
                'a.article_id',
                '(CASE a.type WHEN "3" THEN 1 ElSE 0 END) as sort',
            ])
            ->where([
                'type' => [
                    BaseArticleAttribute::ATTRIBUTE_HOME_TOP,
                    BaseArticleAttribute::ATTRIBUTE_ROLLING,
                    BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP,
                    BaseArticleAttribute::ATTRIBUTE_COLUMN_HEAD,
                    BaseArticleAttribute::ATTRIBUTE_FOCUS,
                    BaseArticleAttribute::ATTRIBUTE_RECOMMEND,
                ],
            ])
            ->orderBy('sort desc')
            ->asArray()
            ->all();

        $tempIds = [];
        $temp    = [];
        foreach ($articleAttributeList as $item) {
            if (!in_array($item['article_id'], $temp)) {
                $temp[]    = $item['article_id'];
                $tempIds[] = $item['id'];
            }
        }

        $select = [
            'a.id',
            'a.title',
            'art.refresh_time',
            'DATE_FORMAT(art.refresh_time,"%Y-%m-%d") as date',
            'aa.type',
            '(CASE aa.type WHEN "' . BaseArticleAttribute::ATTRIBUTE_HOME_TOP . '" THEN 1 ElSE 0 END) as sortNum',
        ];

        $query = BaseArticle::find()
            ->alias('art')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'art.id = a.article_id')
            ->leftJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id=art.id')
            ->innerJoin(['aa' => BaseArticleAttribute::tableName()], 'art.id = aa.article_id')
            ->select($select)
            ->where([
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.is_show'   => BaseArticle::IS_SHOW_YES,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'c.column_id'   => $keywords['columnId'],
                'art.status'    => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
                'aa.id'         => $tempIds,
            ])
            ->groupBy('a.id');

        $orderBy = 'date desc,sortNum desc,art.refresh_time desc';

        $count    = $query->count();
        $pageSize = $keywords['limit'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($keywords['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        foreach ($list as $key => $value) {
            $list[$key]['refresh_time'] = TimeHelper::short($value['refresh_time']);
            $list[$key]['url']          = BaseAnnouncement::getDetailUrl($value['id']);
        }

        return $list;
    }

    /**
     * 获取公告职位附件
     * @param $fileIds
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getAppendixList($fileIds)
    {
        if (empty($fileIds)) {
            return [];
        }
        $ids = explode(',', $fileIds);

        $list        = BaseFile::find()
            ->select('id,name,path,suffix')
            ->where([
                'in',
                'id',
                $ids,
            ])
            ->asArray()
            ->all();
        $orderedList = [];
        foreach ($ids as $id) {
            foreach ($list as $item) {
                if ($item['id'] == $id) {
                    $orderedList[] = $item;
                    break;
                }
            }
        }

        $list = $orderedList;

        foreach ($list as &$item) {
            $item['path'] = FileHelper::getFullUrl($item['path']);
            // $item['path'] = Qiniu::supplementDownloadParameter($item['path'], $item['name']);
        }

        return $list;
    }

    /**
     * 公告对应文章
     * @return \yii\db\ActiveQuery
     */
    public function getArticle()
    {
        return $this->hasOne(BaseArticle::class, ['id' => 'article_id'])
            ->select([
                'id',
                'add_time',
                'update_time',
                'refresh_time',
                'status',
                'type',
                'is_show',
                'is_delete',
                'title',
                'content',
                'home_column_id',
                'home_sub_column_ids',
                'original_url',
                'author',
                'tag_ids',
                'link_url',
                'cover_thumb',
            ]);
    }

    /**
     * 推荐公告获取地点名称
     * @param $id
     * @return mixed|string|void
     */
    public static function getAnnouncementAreaName($id)
    {
        $where        = [
            'announcement_id' => $id,
            'is_show'         => BaseJob::IS_SHOW_YES,
            'status'          => [
                BaseJob::STATUS_ONLINE,
                BaseJob::STATUS_OFFLINE,
            ],
        ];
        $jobCityCount = BaseJob::find()
            ->where($where)
            ->select('city_id')
            ->groupBy('city_id')
            ->count();
        $jobInfo      = BaseJob::find()
            ->where($where)
            ->select('city_id')
            ->asArray()
            ->one();
        if ($jobCityCount == 0) {
            return '';
        } elseif ($jobCityCount == 1) {
            return BaseArea::getAreaName($jobInfo['city_id']);
        } elseif ($jobCityCount > 1) {
            return BaseArea::getAreaName($jobInfo['city_id']) . '等';
        }
    }

    /**
     * 获取公告分析报告url
     */
    public static function getReportUrl($resumeId, $id)
    {
        $token = BaseResumeAnnouncementReportRecord::findOneVal([
            'resume_id'       => $resumeId,
            'announcement_id' => $id,
        ], 'token');

        return UrlHelper::toRoute([
            '/announcement/report',
            'id'    => $id,
            'token' => $token,
        ]);
    }

    /**
     * 获取小程序公告列表公告--搜索页面
     * @throws \Exception
     */
    public static function searchForMiniAppList($keywords): array
    {
        $select = [
            'a.id',
            'a.title',
            'art.status',
            'GROUP_CONCAT(j.major_id SEPARATOR ",") as majorIds',
            'a.period_date',
            'art.refresh_time',
            'count(distinct j.id) as jobAccount',
            'GROUP_CONCAT(j.amount SEPARATOR ",") as amountList',
            'count(j.amount) as amount',
            'j.political_type',
            'CASE WHEN aa.type in (1,3,4,5,6,7,10) THEN 1 ELSE 0 END AS typeNumber',
            'GROUP_CONCAT(j.city_id SEPARATOR ",") as cityIds',
            'DATE_FORMAT(art.refresh_time,"%Y-%m-%d") as date',
        ];

        $query = BaseArticle::find()
            ->alias('art')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'art.id = a.article_id')
            ->leftJoin(['col' => BaseArticleColumn::tableName()], 'col.article_id = art.id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = a.company_id')
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'art.id = aa.article_id')
            ->leftJoin(['j' => BaseJob::tableName()], 'a.id = j.announcement_id')
            ->select($select)
            ->where([
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.is_show'   => BaseArticle::IS_SHOW_YES,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'art.status'    => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
                'a.is_miniapp'  => 1,
            ])
            ->groupBy('a.id');

        if ($keywords['majorIds']) {
            $majorIds = explode(',', $keywords['majorIds']);
            $query->andWhere([
                'jmr.major_id' => $majorIds,
            ]);
        }

        //这里默认是城市id群，如果前端带上一级要提前处理一下
        if ($keywords['cityIds']) {
            $cityIds = explode(',', $keywords['cityIds']);
            $query->andWhere([
                'j.city_id' => $cityIds,
            ]);
        }

        $query->andFilterCompare('c.nature', $keywords['nature']);

        if ($keywords['columnIds']) {
            $columnIds = explode(',', $keywords['columnIds']);
            $query->andWhere([
                'col.column_id' => $columnIds,
            ]);
        }

        $orderBy = 'date desc,c.sort desc,c.is_cooperation asc,c.id desc';
        $list    = $query->groupBy('a.id')
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        foreach ($list as &$value) {
            $amountList = $value['amountList'];
            if (in_array("若干", $amountList) || in_array("若干人", $amountList)) {
                $value['amount'] = "招若干人";
            } else {
                $value['amount'] = "招" . $value['amount'] . "人";
            }
            $majorIds = explode(',', $value['majorIds']);
            $length   = sizeof($majorIds);
            if ($length > 0) {
                $major = BaseMajor::getAllMajorName($majorIds[0]) ?: '';
                if ($length > 1) {
                    $major = BaseMajor::getAllMajorName($majorIds[1]);
                    $major .= "等";
                }
            } else {
                $major = "专业不限";
            }
            $value['major'] = $major;
            $cityIds        = explode(',', $value['cityIds']);
            $city           = BaseArea::getAreaName($cityIds[0]) ?: '';
            if (sizeof($cityIds) > 1) {
                $city .= "等";
            }
            $value['city']         = $city;
            $value['refresh_time'] = TimeHelper::short($value['refresh_time']);
        }

        return $list;
    }

    /**
     * 获取小程序公告列表公告
     * @throws \Exception
     */
    public static function searchForMiniAppAnnouncementList($keywords): array
    {
        return [];
        $select = [
            'a.id',
            'a.title',
            'art.status',
            'a.major_ids',
            'a.period_date',
            'art.refresh_time',
            'count(distinct j.id) as jobAccount',
            'GROUP_CONCAT(j.amount SEPARATOR ",") as amountList',
            'count(j.amount) as amount',
            'j.political_type',
            'CASE WHEN aa.type in (1,3,4,5,6,7,10) THEN 1 ELSE 0 END AS typeNumber',
            'GROUP_CONCAT(j.city_id SEPARATOR ",") as cityIds',
        ];

        $query = BaseArticle::find()
            ->alias('art')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'art.id = a.article_id')
            ->leftJoin(['col' => BaseArticleColumn::tableName()], 'col.article_id = art.id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = a.company_id')
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'art.id = aa.article_id')
            ->leftJoin(['j' => BaseJob::tableName()], 'a.id = j.announcement_id')
            ->select($select)
            ->where([
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'art.is_show'   => BaseArticle::IS_SHOW_YES,
                'art.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'art.status'    => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
                'a.is_miniapp'  => 1,
            ])
            ->groupBy('a.id');

        if ($keywords['majorIds']) {
            $majorIds  = explode(',', $keywords['majorIds']);
            $condition = ['or'];
            foreach ($majorIds as $item) {
                $condition[] = "find_in_set(" . $item . ",j.major_id)";
            }
            $query->andWhere($condition);
        }

        //这里默认是城市id群，如果前端带上一级要提前处理一下
        if ($keywords['cityIds']) {
            $cityIds   = explode(',', $keywords['cityIds']);
            $condition = ['or'];
            foreach ($cityIds as $item) {
                $condition[] = "find_in_set(" . $item . ",j.city_id)";
            }
            $query->andWhere($condition);
        }

        $query->andFilterCompare('c.nature', $keywords['nature']);

        if ($keywords['columnIds']) {
            $columnIds = explode(',', $keywords['columnIds']);
            $condition = ['or'];
            foreach ($columnIds as $item) {
                $condition[] = "find_in_set(" . $item . ",col.column_id)";
            }
            $query->andWhere($condition);
        }

        $orderBy = ' art.status asc,art.refresh_time desc,art.id desc,typeNumber desc,c.sort desc';
        $list    = $query->groupBy('a.id')
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        foreach ($list as &$value) {
            $amountList = explode(',', $value['amountList']);
            if (in_array("若干", $amountList) || in_array("若干人", $amountList)) {
                $value['amount'] = "招若干人";
            } else {
                $value['amount'] = "招" . $value['amount'] . "人";
            }
            $majorIds = explode(',', $value['major_ids']);
            $length   = sizeof($majorIds);
            if ($length > 0) {
                $major = BaseMajor::getAllMajorName($majorIds[0]) ?: '';
                if ($length > 1) {
                    $major = BaseMajor::getAllMajorName($majorIds[1]);
                    $major .= "等";
                }
            } else {
                $major = "专业不限";
            }
            $value['major'] = $major;
            $cityIds        = explode(',', $value['cityIds']);
            $city           = BaseArea::getAreaName($cityIds[0]) ?: '';
            if (sizeof($cityIds) > 1) {
                $city .= "等";
            }
            $value['city']         = $city;
            $value['refresh_time'] = TimeHelper::short($value['refresh_time']);
            unset($value['amountList'], $value['cityIds']);
        }

        return $list;
    }

    /**
     * 获取小程序公告详情
     */
    public static function getMiniAnnouncementDetail($keywords)
    {
        $memberId = \Yii::$app->user->id;
        $id       = $keywords['id'];

        $info = self::find()
            ->alias('an')
            ->leftJoin(['a' => BaseArticle::tableName()], 'a.id=an.article_id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'an.company_id = c.id')
            ->where(['an.id' => $id])
            ->select([
                'article_id',
                'is_show',
                'an.template_id',
                'an.id',
                'an.title',
                'an.file_ids',
                'an.add_time',
                'c.logo_url',
                'c.full_name as companyName',
                'c.id as companyId',
                'c.type as companyType',
                'c.nature as companyNature',
                'c.scale as companyScale',
                'c.is_cooperation as isCooperation',
                'a.refresh_time',
                'a.content',
                'a.status',
                'a.id as articleId',
                'an.period_date as periodDate',
                'a.refresh_time as refreshTime',
                'a.seo_keywords',
                'a.seo_description',
                'a.home_column_id as homeColumnId',
                'an.apply_type',
                'an.delivery_way',
                'an.delivery_type',
            ])
            ->asArray()
            ->one();
        if (empty($info)) {
            return false;
        }

        //获取公告在招职位数量
        $onLineJobNum         = BaseJob::getAnnouncementJobAmount($id, BaseAnnouncement::STATUS_ONLINE);
        $info['allJobAmount'] = BaseJob::getAnnouncementJobAmount($id);
        $info['jobAmount']    = $onLineJobNum;
        if ($onLineJobNum > 99) {
            $info['jobListAmount'] = '99+';
        } else {
            $info['jobListAmount'] = $onLineJobNum;
        }
        //获取公告招聘人
        $info['jobRecruitAmount'] = BaseJob::getAnnouncementJobRecruitAmount($id);
        //获取公告下职位列表(全部)
        $searchData           = [
            'announcementId' => $id,
        ];
        $info['jobList']      = Job::getAnnouncementJobList($searchData);
        $info['totalJobList'] = Job::getAnnouncementJobList($searchData, false);
        //获取可以进行投递的职位列表
        if (count($info['jobList']) > 0) {
            $info['applyJobList'] = Job::filterJobList($memberId, $info['totalJobList']);
        } else {
            $info['applyJobList'] = [];
        }

        //获取logo完整路径
        $info['logo'] = BaseCompany::getLogoFullUrl($info['logo_url']);
        //判断是否收藏单位了
        $info['isCollectCompany'] = BaseCompanyCollect::COLLECT_STATUS_NO;
        $info['isCollect']        = BaseAnnouncementCollect::COLLECT_STATUS_NO;
        if (!empty($memberId)) {
            //判断是否收藏单位了
            $isCollectCompany = BaseCompanyCollect::checkIsCollect($memberId, $info['companyId']);
            if ($isCollectCompany) {
                $info['isCollectCompany'] = BaseCompanyCollect::COLLECT_STATUS_YES;
            }
            //判断是否收藏公告
            $isCollect = BaseAnnouncementCollect::checkIsCollect($memberId, $id);
            if ($isCollect) {
                $info['isCollect'] = BaseAnnouncementCollect::COLLECT_STATUS_YES;
            }
        }
        //获取公告下的所有学历
        $info['educationText'] = self::getAllEducationName($id);
        $info['minEducation']  = trim(BaseJob::getAnnouncementJobEducationType($id));
        //获取公告下的专业列表
        $info['majorName'] = self::getAllMajorName($id, 'text');
        //获取公告下的所有城市
        $info['cityName'] = self::getAllCityName($id);
        $info['cityIds']  = self::getAllCityId($id);
        if ($info['periodDate'] == TimeHelper::ZERO_TIME) {
            $info['periodDate'] = '详见正文';
        } else {
            $info['periodDate'] = date('Y-m-d', strtotime($info['periodDate']));
        }
        if ($info['refreshTime'] == TimeHelper::ZERO_TIME) {
            $info['fullRefreshTime'] = $info['add_time'];
            $info['refreshTime']     = date('Y-m-d', strtotime($info['add_time']));;
        } else {
            $info['fullRefreshTime'] = $info['refreshTime'];
            $info['refreshTime']     = date('Y-m-d', strtotime($info['refreshTime']));
        }
        //单位性质、类型、规模
        $info['companyType']   = BaseDictionary::getCompanyTypeName($info['companyType']);
        $info['companyNature'] = BaseDictionary::getCompanyNatureName($info['companyNature']);
        $info['companyScale']  = BaseDictionary::getCompanyScaleName($info['companyScale']);
        //单位url
        $info['companyUrl'] = Url::toRoute([
            'company/detail',
            'id' => $info['companyId'],
        ]);

        //获取报名方式
        ////获取公告职位信息
        $job_list        = BaseJob::find()
            ->select('delivery_way,delivery_type,apply_type,apply_address')
            ->andWhere(['announcement_id' => $info['id']])
            ->andWhere(['is_show' => BaseJob::IS_SHOW_YES])
            ->andWhere([
                'status' => [
                    BaseJob::STATUS_OFFLINE,
                    BaseJob::STATUS_ONLINE,
                ],
            ])
            ->asArray()
            ->all();
        $jobApplyTypeArr = [];
        //处理一下没有跟公告就不拿
        $announcement_bool = false;
        foreach ($job_list as $item) {
            if (!empty($item['delivery_type'])) {
                if ($item['delivery_type'] == BaseJob::DELIVERY_TYPE_OUTSIDE) {
                    $applyTypeList = explode(',', $item['apply_type']);
                    foreach ($applyTypeList as $type_item) {
                        array_push($jobApplyTypeArr, BaseDictionary::getSignUpName($type_item));
                    }
                } else {
                    array_push($jobApplyTypeArr, '站内投递');
                }
            } else {
                if (!$announcement_bool) {
                    $announcement_bool = true;
                }
            }
        }
        $announcementApplyTypeArr = [];
        if ($info['delivery_type'] > 0 && $announcement_bool) {
            if ($info['delivery_type'] == BaseAnnouncement::DELIVERY_TYPE_OUTSIDE) {
                $announcementApplyTypeTxt = BaseJob::getApplyTypeName($info['apply_type']);
                $announcementApplyTypeArr = explode(',', $announcementApplyTypeTxt);
            } else {
                $announcementApplyTypeArr = ['站内投递'];
            }
        }
        $applyTypeArr          = array_merge($announcementApplyTypeArr, $jobApplyTypeArr);
        $applyTypeArr          = array_unique($applyTypeArr);
        $info['applyTypeText'] = implode(',', $applyTypeArr);
        //双会特殊处理
        if ($info['template_id'] == BaseAnnouncement::TEMPLATE_DOUBLE_MEETING_ACTIVITY) {
            $info['applyTypeText'] = '站内报名';
        }

        return $info;
    }

    /**
     * 获取公告详情
     * @param     $announcementId
     * @param     $platformType
     * @param int $memberId
     * @return array
     * @throws Exception
     */
    public static function getDetailService($announcementId, $platformType, $memberId = 0)
    {
        //判断参数合法性
        if (empty($announcementId) || empty($platformType)) {
            throw new Exception('参数错误');
        }
        //获取公告信息
        $announcement_info = BaseAnnouncement::findOne($announcementId);
        if (empty($announcement_info)) {
            throw new Exception('公告不存在');
        }
        //获取公告对应的文章信息
        $article_info = BaseArticle::findOne($announcement_info->article_id);
        //获取公告对应的公司信息
        $company_info = BaseCompany::findOne($announcement_info->company_id);
        //定义返回信息
        $return_info = [];
        //关键ID返回
        $return_info['announcement_id']      = $announcementId;
        $return_info['article_id']           = $announcement_info->article_id;
        $return_info['company_id']           = $announcement_info->company_id;
        $return_info['company_package_type'] = $company_info->package_type;
        $return_info['announcement_title']   = $announcement_info->title;
        $return_info['announcement_content'] = $article_info->content;
        $return_info['home_column_id']       = $article_info->home_column_id;
        //判断是否收藏单位了
        //        $return_info['is_collect_company']      = BaseCompanyCollect::COLLECT_STATUS_NO;
        //        $return_info['is_collect_announcement'] = BaseAnnouncementCollect::COLLECT_STATUS_NO;
        //        if (!empty($memberId)) {
        //            //判断是否收藏单位了
        //            $isCollectCompany = BaseCompanyCollect::checkIsCollect($memberId, $company_info->id);
        //            if ($isCollectCompany) {
        //                $return_info['is_collect_company'] = BaseCompanyCollect::COLLECT_STATUS_YES;
        //            }
        //            //判断是否收藏公告
        //            $isCollect = BaseAnnouncementCollect::checkIsCollect($memberId, $announcementId);
        //            if ($isCollect) {
        //                $return_info['is_collect_announcement'] = BaseAnnouncementCollect::COLLECT_STATUS_YES;
        //            }
        //        }
        if ($announcement_info->period_date == TimeHelper::ZERO_TIME) {
            $return_info['period_date'] = '详见正文';
        } else {
            $return_info['period_date'] = date('Y-m-d', strtotime($announcement_info->period_date));
        }
        if ($article_info->refresh_time == TimeHelper::ZERO_TIME) {
            $return_info['full_refresh_time'] = $announcement_info->add_time;
            $return_info['refresh_time']      = date('Y-m-d', strtotime($announcement_info->add_time));;
        } else {
            $return_info['full_refresh_time'] = $article_info->refresh_time;
            $return_info['refresh_time']      = date('Y-m-d', strtotime($article_info->refresh_time));
        }
        //单位名称、logo、单位性质、类型、规模
        $return_info['company_name']        = $company_info->full_name;
        $return_info['company_logo']        = BaseCompany::getLogoFullUrl($company_info->logo_url);
        $return_info['company_type_name']   = BaseDictionary::getCompanyTypeName($company_info->type);
        $return_info['company_nature_name'] = BaseDictionary::getCompanyNatureName($company_info->nature);
        $return_info['company_scale_name']  = BaseDictionary::getCompanyScaleName($company_info->scale);
        //单位基本信息拼接
        $company_basics_info = [];
        //单位性质
        if ($return_info['company_nature_name']) {
            array_push($company_basics_info, $return_info['company_nature_name']);
        }
        //单位类型
        if ($return_info['company_type_name']) {
            array_push($company_basics_info, $return_info['company_type_name']);
        }
        //单位规模
        if ($return_info['company_scale_name']) {
            array_push($company_basics_info, $return_info['company_scale_name']);
        }
        $return_info['company_basics_info'] = implode('·', $company_basics_info);
        //职位信息
        //todo：这个$memberId传进去，但是没有使用
        $job_data = BaseAnnouncement::getJobCountService($announcementId, $platformType, $memberId);
        //返回赋值的内容
        $return_info['online_job_number']           = $job_data['online_job_number'];
        $return_info['offline_job_umber']           = $job_data['offline_job_umber'];
        $return_info['all_job_number']              = $job_data['all_job_number'];
        $return_info['announcement_recruit_amount'] = $job_data['announcement_recruit_amount'];
        $return_info['education_name']              = $job_data['education_name'];
        $return_info['min_education_name']          = $job_data['min_education_name'];
        $return_info['city_name']                   = $job_data['city_name'];
        $return_info['city_ids']                    = $job_data['city_ids'];
        $return_info['major_name']                  = $job_data['major_name'];

        // 2.4 更多查询逻辑
        $cityName = implode(',', $job_data['cityNameList']);
        $cityId   = implode(',', $job_data['city_ids']);

        $articleColumn = BaseHomeColumn::findOne(['id' => $article_info->home_column_id]);

        $return_info['moreSearchParams'] = [
            'typeId'   => strval($article_info->home_column_id),
            'typeName' => $articleColumn->name,
            'cityId'   => strval($cityId),
            'city'     => strval($cityName),
        ];

        $announcement_bool           = $job_data['announcement_bool'];
        $job_apply_type_arr          = $job_data['job_apply_type_arr'];
        $announcement_apply_type_arr = [];
        if ($announcement_info->delivery_type > 0 && $announcement_bool) {
            if ($announcement_info->delivery_type == BaseAnnouncement::DELIVERY_TYPE_OUTSIDE) {
                $announcement_apply_type_txt = BaseJob::getApplyTypeName($announcement_info->apply_type);
                $announcement_apply_type_arr = explode(',', $announcement_apply_type_txt);
            } else {
                $announcement_apply_type_arr = ['站内投递'];
            }
        }
        $apply_type_arr                 = array_merge($announcement_apply_type_arr, $job_apply_type_arr);
        $apply_type_arr                 = array_unique($apply_type_arr);
        $return_info['apply_type_text'] = implode(',', $apply_type_arr);
        //双会特殊处理
        //        if ($announcement_info->template_id == BaseAnnouncement::TEMPLATE_DOUBLE_MEETING_ACTIVITY) {
        //            $return_info['apply_type_text'] = '站内报名';
        //        }
        //查看公告是否有附件需要显示
        $return_info['file_list'] = [];
        if (!empty($announcement_info->file_ids)) {
            $fileIdsArr = explode(',', $announcement_info->file_ids);
            $file_data  = BaseFile::getIdsList($fileIdsArr);
            $fileArr    = [];
            foreach ($file_data as &$value) {
                if (!empty($value['path'])) {
                    $file_item           = [];
                    $file_item['path']   = FileHelper::getFullUrl($value['path']);
                    $file_item['name']   = $value['name'];
                    $file_item['suffix'] = FileHelper::getFileSuffixClassName($value['suffix']);
                    $file_item['id']     = $value['id'];
                    array_push($fileArr, $file_item);
                }
            }
            $return_info['file_list'] = $fileArr;
        }
        //这里可根据来源去做特殊数据返回处理

        // 找编制相关内容，其实主要是要看职位的
        $return_info['establishmentType'] = BaseAnnouncement::getEstablishmentType($announcementId);

        //返回
        return $return_info;
    }

    /**
     * 公告下职位统计
     * @param     $announcementId
     * @param     $platformType
     * @param int $memberId
     * @return array
     * @throws \Exception
     */
    public static function getJobCountService($announcementId, $platformType, $memberId = 0)
    {
        ////获取公告职位信息
        $job_data = BaseJob::find()
            ->select([
                'city_id',
                'major_id',
                'education_type',
                'status',
                'delivery_way',
                'delivery_type',
                'apply_type',
                'apply_address',
                'amount',
            ])
            ->andWhere(['announcement_id' => $announcementId])
            ->andWhere(['is_show' => BaseJob::IS_SHOW_YES])
            ->andWhere([
                'status' => [
                    BaseJob::STATUS_OFFLINE,
                    BaseJob::STATUS_ONLINE,
                ],
            ])
            ->orderBy('refresh_time desc')
            ->asArray()
            ->all();
        //处理公告职位业务
        $online_job_number           = 0;//在线职位数量
        $offline_job_umber           = 0;//下线职位数量
        $all_job_number              = 0;//所有职位数量
        $announcement_recruit_amount = '';//公告招聘人数
        $job_apply_type_arr          = [];//报名方式
        //学历要求
        $min_education       = 0;
        $min_education_name  = '';
        $education_name_list = [];
        $educationType       = [];
        //工作地点
        $city_ids       = [];
        $city_name_list = [];
        //需求专业
        $major_ids       = [];
        $major_name_list = [];
        //处理一下没有跟公告就不拿
        $announcement_bool = false;
        foreach ($job_data as $item) {
            //处理公告的业务逻辑=================开始
            if ($item['status'] == BaseJob::STATUS_ONLINE) {
                $online_job_number++;
            } else {
                $offline_job_umber++;
            }
            $all_job_number++;
            //公告招聘人数不等于若干就累加
            if ($announcement_recruit_amount != '若干') {
                if ($item['amount'] == '若干') {
                    $announcement_recruit_amount = '若干';
                } else {
                    $announcement_recruit_amount += $item['amount'];
                }
            }

            if (!empty($item['delivery_type'])) {
                if ($item['delivery_type'] == BaseJob::DELIVERY_TYPE_OUTSIDE) {
                    $apply_type_list = explode(',', $item['apply_type']);
                    foreach ($apply_type_list as $type_item) {
                        array_push($job_apply_type_arr, BaseDictionary::getSignUpName($type_item));
                    }
                } else {
                    array_push($job_apply_type_arr, '站内投递');
                }
            } else {
                if (!$announcement_bool) {
                    $announcement_bool = true;
                }
            }
            //学历要求
            if (!empty($item['education_type'])) {
                $education_name = BaseDictionary::getEducationName($item['education_type']);
                if (($min_education > $item['education_type'] && $min_education != BaseResumeEducation::EDUCATION_TYPE_OTHER_CODE) || $min_education == 0) {
                    $min_education = $item['education_type'];
                    //$min_education_name = $education_name;
                } elseif ($item['education_type'] == BaseResumeEducation::EDUCATION_TYPE_OTHER_CODE) {
                    $min_education = $item['education_type'];
                    //$min_education_name = $education_name;
                }
                array_push($education_name_list, $education_name);
                $education_name_list = array_unique($education_name_list);
                array_push($educationType, $item['education_type']);
                $educationType = array_unique($educationType);
            }
            //工作地点
            if (!empty($item['city_id']) && !in_array($item['city_id'], $city_ids)) {
                $city_name = BaseArea::getAreaName($item['city_id']);
                array_push($city_name_list, $city_name);
                array_push($city_ids, $item['city_id']);
            }
            //需求专业
            if (!empty($item['major_id'])) {
                //炸成数组
                $major_id_arr = explode(',', $item['major_id']);
                //循环
                foreach ($major_id_arr as $major_id) {
                    if (!in_array($major_id, $major_ids)) {
                        $major_name = BaseMajor::getMajorName($major_id);
                        array_push($major_name_list, $major_name);
                        array_push($major_ids, $major_id);
                    }
                }
            }
            //处理公告的业务逻辑=================结束
        }
        //公告招聘人数转成字符串
        $announcement_recruit_amount = strval($announcement_recruit_amount);
        //返回赋值的内容
        $education_name = implode(',', array_unique($education_name_list));
        //$min_education_name = $min_education_name ? $min_education_name . ($min_education_name == $topEducationName ? '' : '及以上') : '';
        $min_education_name = trim(BaseJob::getAnnouncementJobEducationType($announcementId));
        $city_name          = implode(',', array_unique($city_name_list));
        $major_name         = implode(',', array_unique($major_name_list));
        unset($job_data);
        //特殊处理
        if ($announcementId == 181404) {
            $major_name = self::getAllMajorName($announcementId, 'text');
        }

        return [
            'online_job_number'           => $online_job_number,
            'offline_job_umber'           => $offline_job_umber,
            'all_job_number'              => $all_job_number,
            'announcement_recruit_amount' => $announcement_recruit_amount,
            'city_ids'                    => $city_ids,
            'city_name'                   => $city_name,
            'cityNameList'                => $city_name_list,
            'education_name'              => $education_name,
            'education_type'              => $educationType,
            'educationNameList'           => $education_name_list,
            'min_education_name'          => $min_education_name,
            'major_name'                  => $major_name,
            'job_apply_type_arr'          => $job_apply_type_arr,
            'announcement_bool'           => $announcement_bool,
        ];
    }

    /**
     * 获取公告下职位筛选条件
     * @param $announcementId int 公告id
     * @return array
     */
    public static function getJobListFilter($announcementId)
    {
        $data = [
            'jobCategoryFilter'      => self::jobCategoryFilter($announcementId),
            'jobEducationTypeFilter' => self::jobEducationTypeFilter($announcementId),
            'jobMajorFilter'         => self::getJobMajorFilter($announcementId),
        ];

        return $data;
    }

    /**
     * 获取公告里面的学科专业
     */
    public static function getJobMajorFilter($announcementId)
    {
        $where = [
            [
                '=',
                'r.announcement_id',
                $announcementId,
            ],
            [
                '=',
                'r.level',
                2,
            ],
        ];
        $field = [
            'k' => 'm.id',
            'v' => 'm.name',
        ];

        $data = BaseJobMajorRelation::find()
            ->alias('r')
            ->innerJoin(['m' => BaseMajor::tableName()], 'r.major_id=m.id')
            ->andWhere(new AndCondition($where))
            ->select($field)
            ->groupBy('m.id')
            ->asArray()
            ->all();

        if (PLATFORM == 'MINI') {
            //头部插入一个全部
            array_unshift($data, [
                'k' => '',
                'v' => '全部',
            ]);
        }

        return $data;
    }

    /**
     * 获取公告里面的学历列表
     */
    public static function jobEducationTypeFilter($announcementId)
    {
        $educationTypeList = BaseJob::find()
            ->andWhere(['announcement_id' => $announcementId])
            ->andWhere(['is_show' => BaseJob::IS_SHOW_YES])
            ->andWhere([
                'status' => [
                    BaseJob::STATUS_OFFLINE,
                    BaseJob::STATUS_ONLINE,
                ],
            ])
            ->select(['education_type'])
            ->groupBy('education_type')
            ->limit(5)
            ->asArray()
            ->all();

        $educationList = [];
        foreach ($educationTypeList as $k => $item) {
            if (empty($item['education_type'])) {
                continue;
            }
            $educationList[$k]['k'] = $item['education_type'];
            $educationList[$k]['v'] = BaseResumeEducation::EDUCATION_TYPE_LIST[$item['education_type']];
        }

        if (PLATFORM == 'MINI') {
            //头部插入一个全部
            array_unshift($educationList, [
                'k' => '',
                'v' => '全部',
            ]);
        }

        return $educationList;
    }

    /**
     * 获取公告里面的职位类型
     */
    public static function jobCategoryFilter($announcementId)
    {
        $where = [
            [
                '=',
                'cr.announcement_id',
                $announcementId,
            ],
            [
                '=',
                'c.is_delete',
                BaseCategoryJob::IS_DELETE_NO,
            ],
            [
                '=',
                'cr.level',
                2,
            ],
        ];
        $field = [
            'k' => 'c.id',
            'v' => 'c.name',
        ];

        $data = BaseJobCategoryRelation::find()
            ->alias('cr')
            ->leftJoin(['c' => BaseCategoryJob::tableName()], 'cr.category_id = c.id')
            ->andWhere(new AndCondition($where))
            ->groupBy('c.id')
            ->select($field)
            ->asArray()
            ->all();

        if (PLATFORM == 'MINI') {
            //头部插入一个全部
            array_unshift($data, [
                'k' => '',
                'v' => '全部',
            ]);
        }

        return $data;
    }

    /**
     * 获取公告职位数据
     * @param     $announcementId
     * @param     $platformType
     * @param int $memberId
     * @return array
     * @throws \Exception
     */
    public static function getJobListService($announcementId, $platformType, $searchParams = [], $memberId = 0)
    {
        ////获取公告职位信息
        $query = BaseJob::find()
            ->alias('j')
            ->select([
                'j.city_id',
                'j.major_id',
                'j.education_type',
                'j.status',
                'j.delivery_way',
                'j.delivery_type',
                'j.apply_type',
                'j.apply_address',
                'j.release_time',
                'j.refresh_time',
                'j.name',
                'j.id',
                'j.wage_type',
                'j.min_wage',
                'j.max_wage',
                'j.amount',
                'j.experience_type',
                'j.is_establishment',
            ])
            ->andWhere(['j.announcement_id' => $announcementId])
            ->andWhere(['j.is_show' => BaseJob::IS_SHOW_YES])
            ->andWhere([
                'j.status' => [
                    BaseJob::STATUS_OFFLINE,
                    BaseJob::STATUS_ONLINE,
                ],
            ])
            ->groupBy('j.id')
            ->orderBy('j.status desc,j.refresh_time desc,j.id asc');

        // 追加查询条件
        // 职位类型
        if (!empty($searchParams['categoryId'])) {
            $searchParams['categoryId'] = explode(',', $searchParams['categoryId']);
            $query->leftJoin(['cr' => BaseJobCategoryRelation::tableName()], 'cr.job_id=j.id');
            $query->andWhere([
                'in',
                'cr.category_id',
                $searchParams['categoryId'],
            ]);
        }
        // 职位学历
        if (!empty($searchParams['educationId'])) {
            $searchParams['educationType'] = explode(',', $searchParams['educationId']);
            $query->andWhere([
                'in',
                'j.education_type',
                $searchParams['educationType'],
            ]);
        }
        // 职位专业
        if (!empty($searchParams['majorId'])) {
            $searchParams['majorId'] = explode(',', $searchParams['majorId']);
            $query->leftJoin(['mr' => BaseJobMajorRelation::tableName()], 'mr.job_id=j.id');
            $query->andWhere([
                'in',
                'mr.major_id',
                $searchParams['majorId'],
            ]);
        }

        $count_query_item = clone $query;
        $job_query_item   = clone $query;
        $onlineCount      = (clone $query)->andWhere(['j.status' => BaseJob::STATUS_ONLINE])
            ->count();

        //分页
        $page      = $searchParams['page'] ?? 1;
        $count     = $count_query_item->count();
        $page_size = $searchParams['pageSize'] ?? Yii::$app->params['defaultPageSize'];
        $pages     = self::setPage($count, $page, $page_size);
        $job_list  = $job_query_item->offset($pages['offset'])
            ->alias('j')
            ->limit($pages['limit'])
            ->asArray()
            ->all();

        //获取公告信息
        $announcement_info = BaseAnnouncement::findOne($announcementId);
        foreach ($job_list as &$item) {
            //处理职位本身的业务=================开始
            $item['wage']              = BaseJob::formatWage($item['min_wage'], $item['max_wage'], $item['wage_type']);
            $item['education_name']    = BaseDictionary::getEducationName($item['education_type']);
            $item['experience_name']   = BaseDictionary::getExperienceName($item['experience_type']);
            $item['city_name']         = BaseArea::getAreaName($item['city_id']);
            $item['major_name']        = BaseMajor::getMajorName(explode(',', $item['major_id'])[0]);
            $item['release_time']      = TimeHelper::formatDateByYear($item['release_time']);
            $item['refresh_time']      = TimeHelper::formatDateByYear($item['refresh_time']);
            $item['announcement_name'] = $announcement_info->title;
            ////处理一下各端独有逻辑====开始
            /// 处理一下各端独有逻辑====结束
            ////删除冗余数据
            unset($item['city_id'], $item['major_id'], $item['education_type'], $item['experience_type'], $item['min_wage'], $item['max_wage'], $item['wage_type'], $item['apply_type'], $item['apply_address'], $item['delivery_type'], $item['delivery_way']);
            //处理职位本身的业务=================结束
        }

        return [
            'data'        => $job_list,
            'page'        => $pages['page'],
            'total'       => intval($count),
            'page_size'   => $pages['limit'],
            'onlineCount' => intval($onlineCount),
        ];
    }

    /**
     * 获取公告详情推荐公告列表
     * @param $whereData
     * @param $platformType
     * @return array
     * @throws Exception
     */
    public static function getRecommendListService($whereData, $platformType)
    {
        //判断参数合法性
        if (empty($whereData) || !in_array($platformType, BaseCommon::PLATFORM_TYPE_LIST)) {
            throw new Exception('参数错误');
        }
        //公告推荐句柄
        $query = BaseAnnouncement::find()
            ->alias('a')
            ->select([
                'a.id',
                'a.title',
                'ar.refresh_time',
                'c.full_name',
                'c.id as company_id',
            ])
            ->innerJoin(['ar' => BaseArticle::tableName()], 'ar.id = a.article_id')
            ->innerJoin(['c' => BaseCompany::tableName()], 'c.id = a.company_id')
            ->leftJoin(['j' => BaseJob::tableName()], 'a.id = j.announcement_id')
            ->andWhere(['ar.status' => BaseArticle::STATUS_ONLINE])
            ->groupBy('a.id');
        //各端有特殊处理的地方
        switch ($platformType) {
            case BaseCommon::PLATFORM_MINI:
                //定义规则
                //定义最大推荐数
                $max_recommend_number = 18;
                //1、优先展示该单位最新发布的在线公告，最多6条。
                //2、展示该公告所属栏目下，相同工作地点，会员类型为高级会员的合作单位，最新发布的在线公告，最多18条。
                //3、展示该公告所属栏目下，相同工作地点的最新发布的在线公告。
                $rule = [
                    'rule_1' => [
                        'where' => [
                            'a.company_id' => $whereData['company_id'],
                        ],
                        'order' => [
                            'ar.refresh_time' => SORT_DESC,
                        ],
                        'limit' => 6,
                    ],
                    'rule_2' => [
                        'where' => [
                            'ar.home_column_id' => $whereData['home_column_id'],
                            'j.city_id'         => $whereData['city_id'],
                            'c.is_cooperation'  => BaseCompany::COOPERATIVE_UNIT_YES,
                            'c.package_type'    => BaseCompany::PACKAGE_TYPE_SENIOR,
                        ],
                        'order' => [
                            'ar.refresh_time' => SORT_DESC,
                        ],
                        'limit' => 18,
                    ],
                    'rule_3' => [
                        'where' => [
                            'ar.home_column_id' => $whereData['home_column_id'],
                            'j.city_id'         => $whereData['city_id'],
                        ],
                        'order' => [
                            'ar.refresh_time' => SORT_DESC,
                        ],
                        'limit' => 18,
                    ],
                ];
                break;
            default:
                return [];
                break;
        }
        //如果规则空就直接返回空
        if (empty($rule)) {
            return [];
        }
        //定义一下获取公告ID数组及列表
        $announcement_id_arr = [];
        $list                = [];
        //循环规则
        //根据循环去获取符合规则的公告ID push到职位ID数组中去
        foreach ($rule as $value) {
            $limit      = $max_recommend_number - count($announcement_id_arr);
            $limit      = $limit < 0 ? $max_recommend_number : $limit;
            $limit      = $limit > $value['limit'] ? $value['limit'] : $limit;
            $query_item = clone $query;
            if (!empty($announcement_id_arr)) {
                $query_item->andWhere([
                    'not in',
                    'a.id',
                    $announcement_id_arr,
                ]);
            }
            $announcement_data_item = $query_item->andFilterWhere($value['where'])
                ->orderBy($value['order'])
                ->limit($limit)
                ->asArray()
                ->all();
            unset($query_item);
            $list                  = array_merge($list, $announcement_data_item);
            $announcement_ids_item = array_column($announcement_data_item, 'id');
            $announcement_id_arr   = array_merge($announcement_id_arr, $announcement_ids_item);
            if (count($announcement_id_arr) >= $max_recommend_number) {
                break;
            }
        }
        //处理列表
        foreach ($list as &$value) {
            //获取公告下的职位数量
            $value['jobAmount'] = BaseJob::getAnnouncementJobAmount($value['id']);
            //获取公告的职位招聘数量
            $value['recruitAmount']     = BaseJob::getAnnouncementJobRecruitAmount($value['id']);
            $value['establishmentType'] = BaseAnnouncement::getEstablishmentType($value['id']);
            //获取公告下的最低学历要求
            //$value['education'] = BaseAnnouncement::getMinEducationName($value['id']);
            $value['education'] = trim(BaseJob::getAnnouncementJobEducationType($value['id']));
            //获取公告地点
            $value['area']         = BaseAnnouncement::getOnlineNewCityName($value['id']);
            $value['refresh_time'] = date('Y-m-d', strtotime($value['refresh_time']));
            $value['time']         = TimeHelper::formatDateByYear($value['refresh_time']);
        }

        return $list;
    }

    public static function getEstablishmentType($id)
    {
        // 找到所有在线的职位的数量，和有编制的数量做比较

        $establishmentNum = BaseJob::find()
            ->where([
                'announcement_id'  => $id,
                'status'           => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
                'is_show'          => BaseJob::IS_SHOW_YES,
                'is_establishment' => BaseJob::IS_ESTABLISHMENT_YES,
            ])
            ->count();

        if ($establishmentNum <= 0) {
            return self::NOT_ESTABLISHMENT;
        }

        $onlineJobNum = BaseJob::find()
            ->where([
                'announcement_id' => $id,
                'status'          => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->count();

        if ($establishmentNum == $onlineJobNum) {
            return self::ALL_ESTABLISHMENT;
        } else {
            return self::PART_ESTABLISHMENT;
        }
    }

    public static function getDetailMiniCode($id)
    {
        // 先去缓存里面取
        $cacheKey = Cache::MINI_CODE_KEY . ':' . WxMiniApp::QRCODE_PATH_TYPE_ANNOUNCEMENT . ':' . $id;
        if ($url = Cache::get($cacheKey)) {
            return $url;
        }

        $mini = WxMiniApp::getInstance();
        $url  = $mini->createQrCodeByType(WxMiniApp::QRCODE_PATH_TYPE_ANNOUNCEMENT, $id);

        Cache::set($cacheKey, $url);

        return $url;
    }

    /**
     * 获取公告热度类型
     * @param $id
     * @return int
     */
    public static function getHeatType($id)
    {
        //公告热度查询：即该公告近90天的浏览热度情况。
        //公告热度 判定规则：获取在线公告近90天？的浏览量m，
        //“一般”：筛选 0 ≤ m ≤ 100 的在线公告；
        //“较热”：筛选 100 < m ≤ 500 的在线公告；
        //“火爆”：筛选 m > 500 的在线公告；
        $clickNum = self::getClickNumberByDays($id, 90);
        switch (true) {
            case $clickNum >= 0 && $clickNum <= 100:
                $heatType = self::ANNOUNCEMENT_HEAT_TYPE_PRIMARY;
                break;
            case $clickNum > 100 && $clickNum <= 500:
                $heatType = self::ANNOUNCEMENT_HEAT_TYPE_HOT;
                break;
            case $clickNum > 500:
                $heatType = self::ANNOUNCEMENT_HEAT_TYPE_EXPLOSIVE;
                break;
            default:
                $heatType = 0;
                break;
        }

        return $heatType;
    }

    public static function getClickNumberByDays($id, $days)
    {
        $startTime = date('Y-m-d', strtotime("- $days days"));
        $endTime   = date('Y-m-d', time());

        $clickNum = BaseAnnouncementClickTotalDaily::find()
            ->where([
                'announcement_id' => $id,
            ])
            ->andWhere([
                '>=',
                'add_date',
                $startTime,
            ])
            ->andWhere([
                '<',
                'add_date',
                $endTime,
            ])
            ->sum('total') ?: 0;

        return $clickNum;
    }

    /**
     * 设置公告内邮箱脱敏
     * @param $announcementId
     * @param $content
     * @return array|string|string[]
     */
    public static function setEmailMask($announcementId, $content)
    {
        //判断是否要隐藏
        $info = self::findOne($announcementId);
        if ($info->address_hide_status == self::ADDRESS_HIDE_STATUS_NO) {
            return $content;
        }
        //合作单位不做处理
        $isCooperation = BaseCompany::findOneVal(['id' => $info->company_id], 'is_cooperation');
        if ($isCooperation == BaseCompany::COOPERATIVE_UNIT_YES) {
            return $content;
        }
        $announcementApplyType = explode(',', $info->apply_type);
        if (!in_array(self::ATTRIBUTE_APPLY_EMAIL, $announcementApplyType)) {
            //如果没有电子邮箱投递方式的，返回
            return $content;
        }
        $announcementEmailList = explode(',', $info->apply_address);
        //获取职位的邮箱
        $jobList      = BaseJob::find()
            ->where(['announcement_id' => $announcementId])
            ->select([
                'apply_type',
                'apply_address',
            ])
            ->asArray()
            ->all();
        $jobEmailList = [];
        //获取职位邮箱列表
        if (!empty($jobList)) {
            foreach ($jobList as $job) {
                $jobApplyTypeList = explode(',', $job['apply_type']);
                if (!in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $jobApplyTypeList)) {
                    continue;
                }
                $jobApplyAddress = explode(',', $job['apply_address']);
                if ($jobApplyAddress) {
                    foreach ($jobApplyAddress as $item) {
                        $jobEmailList[] = $item;
                    }
                }
            }
        }
        $allEmailList = array_unique(array_merge($jobEmailList, $announcementEmailList));
        if (!empty($allEmailList)) {
            foreach ($allEmailList as $oldEmail) {
                //检查是否有href的邮箱'
                $hrefText = 'href="mailto:' . $oldEmail . '"';
                $content  = str_replace($hrefText, 'href="javascript:;"', $content);
                //隐藏邮箱
                $newEmail = '<span class="detail-hidden-email-address">（点击查看）</span>';
                $content  = str_replace($oldEmail, $newEmail, $content);
            }
        }

        return $content;
    }

    public static function getRealHeadImage($id)
    {
        // 先拿图片类型
        $backgroundImgFileType = self::findOneVal(['id' => $id], 'background_img_file_type');
        // 这种情况，就要看膜拜了，三套模板对应的图片连接是不一样的
        $templateId = self::findOneVal(['id' => $id], 'template_id');

        if ($backgroundImgFileType <= self::BACKGROUND_IMG_FILE_TYPE_DEFAULT) {
            if ($templateId == self::TEMPLATE_SENIOR_THREE) {
                return self::ANNOUNCEMENT_TEMPLATE_THREE_HEAD_IMG;
            }

            return '';
        }

        if ($backgroundImgFileType == self::BACKGROUND_IMG_FILE_TYPE_COMPANY) {
            // 拿单位的主页图
            $companyId     = self::findOneVal(['id' => $id], 'company_id');
            $headBannerUrl = BaseCompany::findOneVal(['id' => $companyId], 'head_banner_url');

            $url = FileHelper::getFullUrl($headBannerUrl);

            if (!$url && $templateId == self::TEMPLATE_SENIOR_THREE) {
                return self::ANNOUNCEMENT_TEMPLATE_THREE_HEAD_IMG;
            }

            return $url;
        }

        if ($backgroundImgFileType == self::BACKGROUND_IMG_FILE_TYPE_CUSTOM) {
            switch ($templateId) {
                case self::TEMPLATE_SENIOR_ONE:
                    $fileId = self::findOneVal(['id' => $id], 'background_img_file_id');

                    return FileHelper::getFullPathById($fileId);
                case self::TEMPLATE_SENIOR_TWO:
                    $fileId = self::findOneVal(['id' => $id], 'background_img_file_id_2');

                    return FileHelper::getFullPathById($fileId);
                case self::TEMPLATE_SENIOR_THREE:
                    $fileId = self::findOneVal(['id' => $id], 'background_img_file_id_3');

                    return FileHelper::getFullPathById($fileId);
                default:
                    return '';
            }
        }

        return '';

        // $backgroundImgFileId = self::findOneVal(['id' => $id], 'background_img_file_id');
        // if ($backgroundImgFileId) {
        //     return FileHelper::getFullPathById($backgroundImgFileId);
        // }
        //
        // return '';
        //
        // // 下面的逻辑暂时关闭
        //
        // // 单位图片
        // $companyId   = self::findOneVal(['id' => $id], 'company_id');
        // $companyLogo = BaseCompany::findOneVal(['id' => $companyId], 'head_banner_url');
        //
        // if ($companyLogo) {
        //     return FileHelper::getFullUrl($companyLogo);
        // }
    }

    /**
     * 把内容格式化成移动详情页面显示
     * @param $content
     */
    public static function formatContentMobile($content)
    {
        preg_match_all('/<h5[^>]*([\s\S]*?)<\/h5>/i', $content, $matches);
        foreach ($matches[0] as $match) {
            // 去掉html
            $txt     = strip_tags($match);
            $h1      = '<h2 style="font-size: 16px;font-weight: bold;"><span>' . $txt . '</span></h2>';
            $content = str_replace($match, $h1, $content);
        }

        return $content;
    }
}