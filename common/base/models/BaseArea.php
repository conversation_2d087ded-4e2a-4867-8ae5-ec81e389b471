<?php

namespace common\base\models;

use common\helpers\ArrayHelper;
use common\libs\Cache;
use common\models\Area;
use Yii;
use yii\db\ActiveRecord;

class BaseArea extends Area
{
    const AREA_CACHE     = 'area_list';
    const PROVINCE_LEVEL = 1;
    const CITY_LEVEL     = 2;

    const ADD_UNLIMITED_YES = 1;
    const ADD_UNLIMITED_NO  = 0;

    const IS_CHINA_YES = 1;
    const IS_CHINA_NO  = 2;

    // 直辖市的id
    const CROWN_ID_LIST = [
        2,
        20,
        802,
        2324,
    ];
    //给港澳定义一个名义上的上级ID
    const HONG_KONG_AND_MACAO_ID = 9999999;

    // 直辖市的id(省级)+港澳台
    const HIGH_CROWN_ID_LIST = [
        1,
        19,
        801,
        2323,
        3716,
        3738,
        3325,
    ];

    // 直辖市的id(省级)+港澳台对应的城市id
    const HIGH_CROWN_CITY_ID = [
        1    => 2,
        19   => 20,
        801  => 802,
        2323 => 2324,
        3716 => 2363,
        3738 => 3326,
        3325 => 3717,
    ];

    /**
     * 华东
     * 华中
     * 华北
     * 西南
     * 华南
     * 东北
     * 西北
     */
    const REGION_EAST       = 1;
    const REGION_CENTRAL    = 2;
    const REGION_NORTH      = 3;
    const REGION_SOUTH_WEST = 4;
    const REGION_SOUTH      = 5;
    const REGION_NORTH_EAST = 6;
    const REGION_NORTH_WEST = 7;

    const REGION_LIST = [
        self::REGION_EAST       => '华东',
        self::REGION_CENTRAL    => '华中',
        self::REGION_NORTH      => '华北',
        self::REGION_SOUTH_WEST => '西南',
        self::REGION_SOUTH      => '华南',
        self::REGION_NORTH_EAST => '东北',
        self::REGION_NORTH_WEST => '西北',
    ];

    /**
     * @return bool|mixed
     */
    public static function setAreaCache()
    {
        $areaList = Cache::get(Cache::PC_ALL_AREA_TABLE_KEY);
        if (!$areaList) {
            $areaList      = self::find()
                ->asArray()
                ->indexBy('id')
                ->select('id,parent_id,name,letter,code,level,full_name')
                ->all();
            $cacheAreaList = json_encode($areaList);

            Cache::set(Cache::PC_ALL_AREA_TABLE_KEY, $cacheAreaList, 3600 * 24);
        } else {
            $areaList = json_decode($areaList, true);
        }

        return $areaList;
    }

    /**
     * @return array|bool
     */
    public static function getFullAreaList()
    {
        $select = [
            'id as k',
            'name as v',
            'parent_id as parentId',
            'level',
        ];
        Cache::get(Cache::PC_ALL_AREA_LIST_KEY) ? $areaList = Cache::get(Cache::PC_ALL_AREA_LIST_KEY) : Cache::set(Cache::PC_ALL_AREA_LIST_KEY,
            $areaList = json_encode(ArrayHelper::objMoreArr(self::find()
                ->where(['status' => self::STATUS_ACTIVE])
                ->select($select)
                ->asArray()
                ->all())));

        return json_decode($areaList, true);
    }

    /**
     * @return array|bool
     */
    public static function getAreaList($where = [])
    {
        $select = [
            'id as k',
            'name as v',
            'parent_id as parentId',
            'level',
            'full_name',
            'spell',
        ];

        $baseWhere = array_merge([
            'level' => [
                1,
                2,
            ],
        ], $where);

        $list     = self::find()
            ->select($select)
            ->where(['status' => self::STATUS_ACTIVE])
            ->andWhere($baseWhere)
            ->asArray()
            ->all();
        $areaList = ArrayHelper::objMoreArr($list);

        return $areaList;
    }

    /**
     * @return array|bool
     */
    public static function getAreaInfo($id)
    {
        //读取缓存
        $areaInfo = Cache::hGet(Cache::ALL_TABLE_AREA_KEY, $id);
        if (!$areaInfo) {
            $data = self::find()
                ->select('id,parent_id,name,letter,code,level,full_name,region')
                ->andWhere(['status' => self::STATUS_ACTIVE])
                ->andWhere(['id' => $id])
                ->asArray()
                ->one();
            Cache::hSet(Cache::ALL_TABLE_AREA_KEY, $id, json_encode($data));
            $areaInfo = Cache::hGet(Cache::ALL_TABLE_AREA_KEY, $id);
        }
        $areaInfo = json_decode($areaInfo, true);

        return $areaInfo;
    }

    /**
     * 获取地区名称
     * @param $id
     * @return mixed
     */
    public static function getAreaName($id)
    {
        //读取缓存
        $areaInfo = Cache::hGet(Cache::ALL_TABLE_AREA_KEY, $id);
        if (!$areaInfo) {
            $data = self::find()
                ->select('id,parent_id,name,letter,code,level,full_name')
                ->andWhere(['status' => self::STATUS_ACTIVE])
                ->andWhere(['id' => $id])
                ->asArray()
                ->one();
            Cache::hSet(Cache::ALL_TABLE_AREA_KEY, $id, json_encode($data));
            $areaInfo = Cache::hGet(Cache::ALL_TABLE_AREA_KEY, $id);
        }
        $areaInfo = json_decode($areaInfo, true);

        return $areaInfo['name'];
        // //        // 这个方法导致很慢
        //        $rs = self::find()
        //            ->select('name')
        //            ->where(['id' => $id])
        //            ->asArray()
        //            ->one();
        //
        //        return $rs['name'];
    }

    /**
     * 获取模糊查找城市id
     */
    public static function getAreaIdByName($name): array
    {
        $findIds = self::find()
            ->select('id')
            ->where([
                'and',
                [
                    'like',
                    'full_name',
                    $name,
                ],
                ['level' => 2],
            ])
            ->asArray()
            ->all();

        $cityIds = [];
        foreach ($findIds as $id) {
            $cityIds[] = $id['id'];
        }

        return $cityIds;
    }

    /**
     * 获取户籍国籍列表
     * @return array|array[]|ActiveRecord[]
     */
    public static function getNativePlaceAreaList()
    {
        $list = self::find()
            ->select([
                'id as k',
                'name as v',
                'parent_id as parentId',
                'level',
            ])
            ->where(['is_china' => self::IS_CHINA_YES])
            ->andWhere(['status' => self::STATUS_ACTIVE])
            ->andWhere([
                'level' => [
                    1,
                    2,
                ],
            ])
            ->asArray()
            ->all();

        $list = ArrayHelper::objMoreArr($list, 0);
        //插入一个国内选项
        $chinaList = [
            [
                'k'        => 0,
                'v'        => '国内',
                'parentId' => -1,
                'level'    => 0,
                'children' => $list,
            ],
        ];

        $otherCountryList = self::find()
            ->select([
                'id as k',
                'name as v',
                'parent_id as parentId',
                'level',
            ])
            ->where([
                'is_china' => self::IS_CHINA_NO,
                'level'    => [
                    1,
                    2,
                    3,
                ],
            ])
            ->andWhere(['status' => self::STATUS_ACTIVE])
            ->asArray()
            ->all();
        $otherCountryList = ArrayHelper::objMoreArr($otherCountryList, 0);

        return array_merge($chinaList, $otherCountryList);
    }

    /**
     * 根据地区返回城市id列表
     * @param $areaArr
     */
    public static function getCityIds($areaArr)
    {
        $list = self::find()
            ->select('id,level')
            ->where([
                'status' => self::STATUS_ACTIVE,
                'id'     => $areaArr,
            ])
            ->asArray()
            ->all();

        $return = [];
        $leve1  = [];
        foreach ($list as $item) {
            if ($item['level'] == 2) {
                $return[] = $item['id'];
            }
            if ($item['level'] == 1) {
                $leve1[] = $item['id'];
            }
        }

        if ($leve1) {
            // 找到leve1下面的全部城市
            $cityList = self::find()
                ->select('id')
                ->where([
                    'status'    => self::STATUS_ACTIVE,
                    'parent_id' => $leve1,
                ])
                ->asArray()
                ->indexBy('id')
                ->all();
            $return   = array_merge($return, array_keys($cityList));

            return $return;
        } else {
            return $return;
        }
    }

    /**
     * 获取户籍国籍列表--同级 (这里在2.0版本修改过，所以限制成3级)
     * @return array|array[]|ActiveRecord[]
     */
    public static function getNativeAreaList(): array
    {
        $list = self::getAllCityAreaList();

        //这里取海外数据
        $overseasId = BaseArea::findOneVal([
            'name'   => '海外',
            'level'  => 1,
            'status' => 1,
        ], 'id') ?: '3870';

        $otherCountryList = self::find()
            ->select([
                'id as k',
                'name as v',
                'parent_id as parentId',
                'level',
            ])
            ->where([
                'is_china' => self::IS_CHINA_NO,
                'level'    => [
                    1,
                    2,
                    3,
                ],
            ])
            ->andWhere(['status' => self::STATUS_ACTIVE])
            ->asArray()
            ->all();
        $otherCountryList = ArrayHelper::objMoreArr($otherCountryList, 0);

        //这里加个热门国家数据
        $hotAreaCountyList = self::getHotAreaCountyList();
        foreach ($hotAreaCountyList as &$item) {
            $item['topParentId'] = $overseasId;
        }
        $hotCountryList = [
            [
                'k'        => '0',
                'v'        => '热门国家',
                'parentId' => '-1',
                'level'    => '0',
                'children' => $hotAreaCountyList,
            ],
        ];

        //分组处理
        $childLayeredList = array_chunk($otherCountryList[0]['children'], 4);
        $lastArr          = [];
        foreach ($childLayeredList as $it) {
            foreach ($it as &$value) {
                $children = $value['children'];
                foreach ($children as &$child) {
                    $child['topParentId'] = $overseasId;
                }
                $value['children'] = $children;
            }
            $template = [
                [
                    'k'        => -1,
                    'v'        => '分组数据',
                    'parentId' => -1,
                    'level'    => 0,
                    'children' => $it,
                ],
            ];
            $lastArr  = array_merge($lastArr, $template);
        }

        $temp = [
            [
                'k'        => '-1',
                'v'        => '大洲',
                'parentId' => '-1',
                'level'    => '0',
                'children' => $lastArr,
            ],
        ];

        //这里加个热门国家数据--非字典数据
        $otherCountryList = array_merge($hotCountryList, $temp);

        $overseasList = [
            [
                'k'        => $overseasId,
                'v'        => '海外',
                'parentId' => '0',
                'level'    => '1',
                'children' => $otherCountryList,
            ],
        ];

        return array_merge($list, $overseasList);
    }

    public static function getHwAreaList()
    {
        $list = self::find()
            ->select([
                'id as k',
                'name as v',
                'parent_id as parentId',
                'level',
            ])
            ->where(['is_china' => self::IS_CHINA_YES])
            ->andWhere(['status' => self::STATUS_ACTIVE])
            ->andWhere([
                'level' => [
                    1,
                    2,
                ],
            ])
            ->asArray()
            ->all();

        $list = ArrayHelper::objMoreArr($list, 0);
        //插入一个国内选项
        $chinaList = [
            [
                'k'        => 0,
                'v'        => '国内',
                'parentId' => -1,
                'level'    => 0,
                'children' => $list,
            ],
        ];

        // 先找第四级
        $level4IdsParentId = BaseArea::find()
            ->select('parent_id')
            ->where([
                'is_china' => self::IS_CHINA_NO,
                'status'   => self::STATUS_ACTIVE,
                'level'    => 4,
            ])
            ->asArray()
            ->column();

        $level3IdsParentId = BaseArea::find()
            ->select('parent_id')
            ->where([
                'is_china' => self::IS_CHINA_NO,
                'status'   => self::STATUS_ACTIVE,
                'level'    => 3,
                'id'       => $level4IdsParentId,
            ])
            ->asArray()
            ->column();

        $level2IdsParentId = BaseArea::find()
            ->select('parent_id')
            ->where([
                'is_china' => self::IS_CHINA_NO,
                'status'   => self::STATUS_ACTIVE,
                'level'    => 2,
                'id'       => $level3IdsParentId,
            ])
            ->asArray()
            ->column();

        $level4Ids = BaseArea::find()
            ->select('id')
            ->where([
                'is_china' => self::IS_CHINA_NO,
                'status'   => self::STATUS_ACTIVE,
                'level'    => 4,
            ])
            ->asArray()
            ->column();

        // 找到等级四地区的顶级地区id

        $level4 = BaseArea::find()
            ->select([
                'id as k',
                'name as v',
                'parent_id as parentId',
                'level',
            ])
            ->where([
                'is_china' => self::IS_CHINA_NO,
                'status'   => self::STATUS_ACTIVE,
                'id'       => array_merge($level4Ids, $level4IdsParentId, $level3IdsParentId, $level2IdsParentId),
            ])
            ->asArray()
            ->all();

        $otherCountryList = ArrayHelper::objMoreArr($level4, 0);

        $hasLevel4AreaList = [];

        foreach ($otherCountryList as &$level1) {
            foreach ($level1['children'] as &$level2) {
                foreach ($level2['children'] as &$level3) {
                    if ($level3['children']) {
                        $level3OriginalId = $level3['k'];
                        $level3['k']      = '0_' . $level3['k'];
                        foreach ($level3['children'] as &$item) {
                            $item['parentId'] = $level3['k'];
                        }

                        $children           = ArrayHelper::merge([
                            [
                                'k'        => $level3OriginalId,
                                'v'        => '全国',
                                'parentId' => $level3['k'],
                                'level'    => '4',
                            ],
                        ], $level3['children'],);
                        $level3['children'] = $children;

                        $hasLevel4AreaList[] = $level2;
                    }
                }
            }
        }

        $rs = ArrayHelper::merge($chinaList, $otherCountryList);

        return $rs;
    }

    /**
     * 获取户籍国籍列表--去除分组数据结构的
     * @return array|array[]|ActiveRecord[]
     */
    public static function getHouseholdRegisterList(): array
    {
        $list = self::getAllCityAreaList();

        //这里取海外数据
        $overseasId = BaseArea::findOneVal([
            'name'   => '海外',
            'level'  => 1,
            'status' => 1,
        ], 'id') ?: '3870';

        $otherCountryList = self::find()
            ->select([
                'id as k',
                'name as v',
                'parent_id as parentId',
                'level',
            ])
            ->where([
                'is_china' => self::IS_CHINA_NO,
                'level'    => [
                    1,
                    2,
                    3,
                ],
            ])
            ->andWhere(['status' => self::STATUS_ACTIVE])
            ->asArray()
            ->all();

        $otherCountryList = ArrayHelper::objMoreArr($otherCountryList, 0);

        //这里加个热门国家数据
        $hotAreaCountyList = self::getHotAreaCountyList();
        foreach ($hotAreaCountyList as &$item) {
            $item['topParentId'] = $overseasId;
        }
        $hotCountryList = [
            [
                'k'        => '0',
                'v'        => '热门国家',
                'parentId' => '-1',
                'level'    => '0',
                'children' => $hotAreaCountyList,
            ],
        ];

        //分组处理
        // $childLayeredList = array_chunk($otherCountryList[0]['children'], 4);
        //        print_r($otherCountryList[0]['children']);
        //        print_r("<br/><br/>");
        //        print_r($childLayeredList);die;
        //        $lastArr          = [];
        //        foreach ($childLayeredList as $it) {
        //            foreach ($it as &$value) {
        //                $children = $value['children'];
        //                foreach ($children as &$child) {
        //                    $child['topParentId'] = $overseasId;
        //                }
        //                $value['children'] = $children;
        //            }
        //            $template = [
        //                [
        //                    'k'        => -1,
        //                    'v'        => '分组数据',
        //                    'parentId' => -1,
        //                    'level'    => 0,
        //                    'children' => $it,
        //                ],
        //            ];
        //            $lastArr  = array_merge($lastArr, $template);
        //        }

        $temp = [
            [
                'k'        => '-1',
                'v'        => '大洲',
                'parentId' => '-1',
                'level'    => '0',
                'children' => $otherCountryList[0]['children'],
            ],
        ];

        //这里加个热门国家数据--非字典数据
        $otherCountryList = array_merge($hotCountryList, $temp);

        $overseasList = [
            [
                'k'        => $overseasId,
                'v'        => '海外',
                'parentId' => '0',
                'level'    => '1',
                'children' => $otherCountryList,
            ],
        ];

        return array_merge($list, $overseasList);
    }

    /**
     * 获取户籍国籍列表--热门城市
     * @return array|array[]|ActiveRecord[]
     */
    public static function getHotAreaCityList(): array
    {
        $nameList = [
            '北京',
            '上海',
            '天津',
            '重庆',
            '广州',
            '深圳',
            '南京',
            '西安',
            '武汉',
            '杭州',
            '成都',
            '合肥',
            '长沙',
            '郑州',
            '济南',
            '哈尔滨',
            '福州',
            '珠海',
        ];

        return BaseArea::find()
            ->select([
                'id as k',
                'name as v',
                'parent_id as parentId',
                'level',
                'spell',
            ])
            ->where([
                'level' => BaseArea::CITY_LEVEL,
                'name'  => $nameList,
            ])
            ->asArray()
            ->all();
    }

    /**
     * 获取户籍国籍列表--热门国家
     * @return array|array[]|ActiveRecord[]
     */
    public static function getHotAreaCountyList(): array
    {
        $nameList = [
            '日本',
            '韩国',
            '马来西亚',
            '新加坡',
            '泰国',
            '英国',
            '法国',
            '德国',
            '意大利',
            '西班牙',
            '俄罗斯',
            '瑞士',
            '丹麦',
            '比利时',
            '荷兰',
            '美国',
            '加拿大',
            '巴西',
            '埃及',
            '南非',
            '加纳',
            '澳大利亚',
            '新西兰',
        ];

        return BaseArea::find()
            ->select([
                'id as k',
                'name as v',
                'parent_id as parentId',
                'level',
            ])
            ->where([
                'level' => 3,
                'name'  => $nameList,
            ])
            ->asArray()
            ->all();
    }

    /**
     * 意向城市列表--二级分组
     * @return array|array[]|ActiveRecord[]
     */
    public static function getHierarchyCityList($isOnlyChina = 2): array
    {
        $hotNameList = BaseArea::getHotAreaCityList();
        //热门城市
        $hotCityList = [
            [
                'k'        => '0',
                'v'        => '热门城市',
                'parentId' => '-1',
                'level'    => '0',
                'children' => $hotNameList,
                'spell'    => '',
            ],
        ];

        $areaWhere = [];
        if ($isOnlyChina == 1) {
            $areaWhere = [
                'is_china' => BaseArea::IS_CHINA_YES,
            ];
        }
        $nativeAreaList = BaseArea::getAreaList($areaWhere);
        $list           = array_merge($hotCityList, $nativeAreaList);

        //这里做个数据层级处理
        foreach ($list as $k => $v) {
            if ($k > 0 && !in_array($v['k'], BaseArea::HIGH_CROWN_ID_LIST)) {
                $fullName     = explode(',', $v['full_name'])[0];
                $temp         = [
                    [
                        "k"        => $v['k'],
                        "v"        => $fullName,
                        "parentId" => $v['parentId'],
                        "level"    => $v['level'],
                        "spell"    => $v['spell'],
                    ],
                ];
                $childrenList = array_merge($temp, $v['children']);
                //省份顶部父级跟自己
                foreach ($childrenList as $h => &$item) {
                    if ($h == 0) {
                        $item['topParentId'] = $item['k'];
                    } else {
                        $item['topParentId'] = $item['parentId'];
                    }
                }
            } else {
                $childrenList = $v['children'];
                foreach ($childrenList as &$item) {
                    $item['topParentId'] = $v['k'];
                }
            }

            $list[$k]['children'] = $childrenList;
        }

        return $list;
    }

    /**
     * 获取户籍国籍列表--同级
     * @return array|array[]|ActiveRecord[]
     */
    public static function getNativeCityAreaList(): array
    {
        $hotNameList = BaseArea::getHotAreaCityList();

        //热门城市
        $hotCityList = [
            [
                'k'        => '0',
                'v'        => '热门城市',
                'parentId' => '-1',
                'level'    => '0',
                'children' => $hotNameList,
            ],
        ];

        $list = self::find()
            ->select([
                'id as k',
                'name as v',
                'parent_id as parentId',
                'level',
                'full_name',
            ])
            ->where(['is_china' => self::IS_CHINA_YES])
            ->andWhere(['status' => self::STATUS_ACTIVE])
            ->andWhere([
                'level' => [
                    1,
                    2,
                ],
            ])
            ->asArray()
            ->all();

        $list = ArrayHelper::objMoreArr($list, 0);

        $result = array_merge($hotCityList, $list);
        foreach ($result as $k => &$v) {
            //这里去掉四个直辖市
            if ($k > 0 && !in_array($v['k'], BaseArea::HIGH_CROWN_ID_LIST)) {
                $fullName = explode(',', $v['full_name'])[0];
                $temp     = [
                    [
                        "k"        => $v['k'],
                        "v"        => $fullName,
                        "parentId" => $v['parentId'],
                        "level"    => $v['level'],
                    ],
                ];
                if ($v['children']['level'] == 2) {
                    $v['children'] = array_merge($temp, $v['children']);
                }
            }

            $children = $v['children'];
            foreach ($children as &$child) {
                $child['topParentId'] = $v['k'];
            }
            $v['children'] = $children;
        }

        return $result;
    }

    /**
     * @param $id
     * 主要是用于过滤一些直辖市的重复
     */
    public static function getOneProvinceCityName($id)
    {
        $cityName = BaseArea::getAreaName($id);
        if (in_array($id, BaseArea::CROWN_ID_LIST)) {
            return $cityName;
        }

        // if ($cityName == '吉林') {
        //     return '吉林省吉林市';
        // }

        // 找到省份的name
        $provinceId = BaseArea::findOneVal(['id' => $id], 'parent_id');
        $province   = BaseArea::getAreaName($provinceId);

        return $province . BaseArea::getAreaName($id);
    }

    /**
     * 获取户籍国籍列表--同级不要省级
     * @return array|array[]|ActiveRecord[]
     */
    public static function getAllCityAreaList(): array
    {
        $hotNameList = BaseArea::getHotAreaCityList();

        //热门城市
        $hotCityList = [
            [
                'k'        => '0',
                'v'        => '热门城市',
                'parentId' => '-1',
                'level'    => '0',
                'children' => $hotNameList,
            ],
        ];

        $list = self::find()
            ->select([
                'id as k',
                'name as v',
                'parent_id as parentId',
                'level',
                'full_name',
            ])
            ->where(['is_china' => self::IS_CHINA_YES])
            ->andWhere(['status' => self::STATUS_ACTIVE])
            ->andWhere([
                'level' => [
                    1,
                    2,
                ],
            ])
            ->asArray()
            ->all();

        $list = ArrayHelper::objMoreArr($list, 0);

        $result = array_merge($hotCityList, $list);
        foreach ($result as $k => &$v) {
            $children = $v['children'];
            foreach ($children as &$child) {
                $child['topParentId'] = $v['k'];
            }
            $v['children'] = $children;
        }

        return $result;
    }

    /**
     * 城市列表--二级要涵盖父级
     * @return array|array[]|ActiveRecord[]
     */
    public static function getAllHierarchyCityList(): array
    {
        $list = BaseArea::getAreaList();

        //这里做个数据层级处理
        foreach ($list as $k => $v) {
            if ($k > 0 && !in_array($v['k'], BaseArea::HIGH_CROWN_ID_LIST)) {
                $fullName     = explode(',', $v['full_name'])[0];
                $temp         = [
                    [
                        "k"        => $v['k'],
                        "v"        => $fullName,
                        "parentId" => $v['parentId'],
                        "level"    => $v['level'],
                    ],
                ];
                $childrenList = array_merge($temp, $v['children']);
                //省份顶部父级跟自己
                foreach ($childrenList as $h => &$item) {
                    if ($h == 0) {
                        $item['topParentId'] = $item['k'];
                    } else {
                        $item['topParentId'] = $item['parentId'];
                    }
                }
            } else {
                $childrenList = $v['children'];
                foreach ($childrenList as &$item) {
                    $item['topParentId'] = $v['k'];
                }
            }

            $list[$k]['children'] = $childrenList;
        }

        return $list;
    }

    /**
     * 根据地区id列表，获取拼接字符串
     * @param $idArr
     * @return false|string
     */
    public static function getTextByIdList($idArr)
    {
        if (!empty($idArr)) {
            $text = '';
            foreach ($idArr as $id) {
                $text .= self::getAreaName($id) . ',';
            }

            return substr($text, 0, -1);
        } else {
            return '';
        }
    }

    // 根据城市获取省份+城市名称,当城市为直辖市时，返回直辖市名称
    public static function getProvinceCityName($cityId, $symbol = '')
    {
        $cityName = BaseArea::getAreaName($cityId);

        // 找到省份的name
        $provinceId = BaseArea::findOneVal(['id' => $cityId], 'parent_id');

        if (in_array($provinceId, BaseArea::HIGH_CROWN_ID_LIST)) {
            return $cityName;
        }
        $province = BaseArea::getAreaName($provinceId);

        return $province . $symbol . $cityName;
    }

    public static function getFullNameByCityId($cityId)
    {
        $cityName = BaseArea::getAreaName($cityId);

        // 找到省份的name
        $provinceId = BaseArea::findOneVal(['id' => $cityId], 'parent_id');

        if (in_array($provinceId, BaseArea::HIGH_CROWN_ID_LIST)) {
            return $cityName;
        }
        $province = BaseArea::getAreaName($provinceId);

        return $province . $cityName;
    }

}