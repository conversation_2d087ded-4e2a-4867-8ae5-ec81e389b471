<?php

namespace common\base\models;

use common\helpers\DebugHelper;
use common\helpers\FileHelper;
use common\models\ChatMessage;
use yii\db\Exception;

class BaseChatMessage extends ChatMessage
{

    const DEFAULT_PAGE_LIMIT = 20;
    //申请附件发送底部操作栏自定义消息id
    const REQUEST_FILE_MESSAGE_ID = '0';

    const STATUS_DELIVERY = '1';  //发送成功
    const STATUS_READ     = '2';  //已读
    const STATUS_FAIL     = '-1';  //发送失败

    const STATUS_TEXT_LIST = [
        self::STATUS_DELIVERY => '送达',
        self::STATUS_READ     => '已读',
        self::STATUS_FAIL     => '发送失败',
    ];

    const IS_READ_YES = 1;
    const IS_READ_NO  = 2;

    const IS_SHOW_TIME_YES = 1;
    const IS_SHOW_TIME_NO  = 2;

    const TYPE_TEXT                             = 1;
    const TYPE_JOB_CARD                         = 2;
    const TYPE_JOB_APPLY                        = 3;
    const TYPE_RESUME_CARD                      = 4;
    const TYPE_REQUEST_FILE                     = 5;
    const TYPE_AGREE_REQUEST_FILE               = 6;
    const TYPE_AGREE_REQUEST_FILE_CARD          = 7;
    const TYPE_FILE                             = 8;
    const TYPE_RESUME_COMPLETE_CARD             = 9;
    const TYPE_INVITE_JOB_REQUEST_CARD          = 10;
    const TYPE_INVITE_JOB_REQUEST_CARD_IGNORE   = 11;
    const TYPE_INVITE_JOB_REQUEST_CARD_INTEREST = 12;
    const TYPE_SYSTEM                           = 99;
    const TYPE_IGNORE_REQUEST_FILE              = 13;
    const TYPE_CHANGE_JOB                       = 14;

    const TYPE_EVENT_KEY                       = 1001;
    const TYPE_EVENT_KEY_SYSTEM_GREETING_EVENT = 'systemGreetingEvent';
    const TYPE_EVENT_KEY_TEXT                  = 'eventKey';

    // 操作的类型返回给到前端实际显示的类型
    const TYPE_TO_SHOW_TYPE_LIST = [
        self::TYPE_TEXT                             => 'text',
        self::TYPE_JOB_CARD                         => 'jobCard',
        self::TYPE_JOB_APPLY                        => 'system',
        // self::TYPE_JOB_APPLY                        => 'jobApply',
        self::TYPE_RESUME_CARD                      => 'resumeCard',
        // self::TYPE_REQUEST_FILE                     => 'requestFile',
        self::TYPE_REQUEST_FILE                     => 'system',
        // self::TYPE_AGREE_REQUEST_FILE               => 'agreeRequestFile',
        self::TYPE_AGREE_REQUEST_FILE               => 'system',
        self::TYPE_AGREE_REQUEST_FILE_CARD          => 'agreeRequestFileCard',
        self::TYPE_FILE                             => 'file',
        self::TYPE_RESUME_COMPLETE_CARD             => 'resumeCompleteCard',
        self::TYPE_INVITE_JOB_REQUEST_CARD          => 'inviteJobRequestCard',
        self::TYPE_INVITE_JOB_REQUEST_CARD_IGNORE   => 'inviteJobRequestIgnore',
        self::TYPE_INVITE_JOB_REQUEST_CARD_INTEREST => 'inviteJobRequestInterest',
        self::TYPE_SYSTEM                           => 'system',
        self::TYPE_IGNORE_REQUEST_FILE              => 'ignoreRequestFile',
        self::TYPE_CHANGE_JOB                       => 'system',
    ];

    //专属单位的消息类型
    const MESSAGE_TYPE_BELONG_COMPANY = [// self::TYPE_REQUEST_FILE,
    ];
    //专属求职者的消息类型
    const MESSAGE_TYPE_BELONG_PERSON = [
        // self::TYPE_REQUEST_FILE,
        self::TYPE_INVITE_JOB_REQUEST_CARD_IGNORE,
        self::TYPE_INVITE_JOB_REQUEST_CARD_INTEREST,
    ];

    public static function getResumeRealContent($resumeMemberId, $type, $role, $content, $messageId = '')
    {
        $data = self::getRealContent($type, $role, $content, $messageId);

        $memberModel = BaseMember::findOne($resumeMemberId);

        if ($memberModel->status == BaseMember::STATUS_RESUME_CANCELED) {
            if ($data['btnList']) {
                foreach ($data['btnList'] as &$item) {
                    $item['disabled'] = true;
                }
            }
        }

        return $data;
    }

    // 一些消息的文案获取,针对不同的人,获取不同的文案(暂时手动直接写)
    public static function getRealContent($type, $role, $content, $messageId = '')
    {
        if ($type == self::TYPE_TEXT) {
            return $content;
        }

        switch ($type) {
            case self::TYPE_JOB_APPLY:
                //发送投递
                $jobApplyId = $content['jobApplyId'];
                if (!$jobApplyId) {
                    return false;
                }
                $content['text'] = $role == BaseMember::TYPE_COMPANY ? '对方已投递该职位' : '您已投递该职位';
                break;
            case self::TYPE_RESUME_CARD:
                //发送简历卡片
                $jobApplyId = $content['jobApplyId'];

                if (!$jobApplyId) {
                    return false;
                }
                $resumeId = BaseJobApply::findOneVal(['id' => $jobApplyId], 'resume_id');
                $name     = BaseResume::findOneVal(['id' => $resumeId], 'name');

                $content['title']   = $name . '的在线简历';
                $content['content'] = '这是我的简历，如果觉得合适请随时与我联系，谢谢！';
                $content['btnText'] = '点击查看';
                // 对于求职者,需要显示url,并点击可点击的
                if ($role == BaseMember::TYPE_PERSON) {
                    $content['disabled'] = false;
                    $content['url']      = '/member/person/resume';
                }

                if ($role == BaseMember::TYPE_COMPANY) {
                    $jobId           = BaseJobApply::findOneVal(['id' => $jobApplyId], 'job_id');
                    $chatMessage     = BaseChatMessage::findOne($messageId);
                    $chatRoom        = BaseChatRoom::findOne(['id' => $chatMessage->chat_room_id]);
                    $companyMemberId = $chatRoom->company_member_id;
                    // 找这个人还是不是这个职位联系人或者联系人,如果是,就可以点击
                    if (BaseJobContactSynergy::isMyJob($companyMemberId, $jobId)) {
                        $content['url'] = '/member/company/resume/detail/' . $jobApplyId;
                    } else {
                        $content['url']       = '';
                        $content['disabled']  = true;
                        $content['toastText'] = '您已非该职位的协同者，不支持查看该投递';
                    }
                }

                // 对于单位端,如果
                break;
            case self::TYPE_JOB_CARD:
            case self::TYPE_CHANGE_JOB:
                //职位卡片
                $jobId = $content['jobId'];
                if (!$jobId) {
                    return false;
                }
                $cardInfo    = BaseJob::getJobCardInfo($jobId);
                $content     = array_merge($content, $cardInfo);
                $messageInfo = BaseChatMessage::findOne($messageId);
                $isMy        = $content['nowMemberId'] == $messageInfo['from_member_id'];
                // 找到最后一次沟通的职位信息
                $type = BaseChatHistoryJob::findOneVal(['chat_message_id' => $messageId], 'type');
                if (!$type || $type == BaseChatHistoryJob::TYPE_CREATE) {
                    $content['footerText'] = $isMy ? '由您发起沟通' : '由对方发起沟通';
                }
                if ($type == BaseChatHistoryJob::TYPE_CHANGE) {
                    $content['footerText'] = $isMy ? '您切换了沟通职位' : '对方切换了沟通职位';
                }

                break;
            case self::TYPE_RESUME_COMPLETE_CARD:
                //简历完善卡片
                $resumeId = $content['resumeId'];
                if (!$resumeId) {
                    return false;
                }
                $content['title']   = '简历完善提醒';
                $content['content'] = '您的简历完善度较低，建议完善！简历越完整，更容易受到单位青睐哦～';
                if ($role == BaseMember::TYPE_PERSON) {
                    $content['btnText'] = '点击查看';
                    $content['url']     = '/member/person/resume';
                }
                break;
            case self::TYPE_FILE:
                // 文件就直接返回就好了,连fileId都可以过滤掉
                // $fileId = $content['fileId'];
                // 拼接url
                $content['url']     = '/api/member/chat-download?messageId=' . $messageId;
                $content['miniUrl'] = '/member/chat-download?messageId=' . $messageId;
                if (!$content['fileType']) {
                    //如果是存量数据的，去查询获取文件类型
                    $fileId              = BaseChatMessageFile::findOneVal(['chat_message_id' => $messageId],
                        'file_id');
                    $fileName            = BaseFile::findOneVal(['id' => $fileId], 'name');
                    $content['fileType'] = substr(strrchr($fileName, '.'), 1);
                }
                break;
            case self::TYPE_REQUEST_FILE:
                //申请发送附件,这里只需要发送单位，求职者是系统消息
                //                $jobApplyId = $content['jobApplyId'];
                if ($role == BaseMember::TYPE_PERSON) {
                    $content['text'] = '您已向对方请求发送附件';
                } else {
                    // 这里会对应两个,如果messageId是空的,就需要给button
                    if ($messageId == self::REQUEST_FILE_MESSAGE_ID) {
                        $content['text']    = '对方正在申请向您发送附件材料，是否同意？';
                        $content['btnList'] = [
                            [
                                'type'     => 'ignore',
                                'text'     => '忽略',
                                'disabled' =>  false,
                            ],
                            [
                                'type'     => 'agree',
                                'text'     => '同意',
                                'disabled' => false,
                            ],
                        ];
                    } else {
                        $content['text'] = '对方正在向您请求发送附件';
                    }
                }
                break;
            case self::TYPE_AGREE_REQUEST_FILE:
                //同意发送附件
                // $jobApplyId = $content['jobApplyId'];
                // if (!$jobApplyId) {
                //     return false;
                // }
                $content['text'] = $role == BaseMember::TYPE_COMPANY ? '您已同意对方向您发送附件' : '对方已同意您向其发送附件';
                break;
            case self::TYPE_AGREE_REQUEST_FILE_CARD:
                //发送附件卡片
                // $jobApplyId = $content['jobApplyId'];
                // if (!$jobApplyId) {
                //     return false;
                // }
                $content['content']  = '您好，请按职位/公告要求补充相关附件';
                $content['btnText']  = $role == BaseMember::TYPE_COMPANY ? '您已同意' : '发附件';
                $content['disabled'] = $role == BaseMember::TYPE_COMPANY;
                break;
            case self::TYPE_INVITE_JOB_REQUEST_CARD:
            case self::TYPE_INVITE_JOB_REQUEST_CARD_IGNORE:
            case self::TYPE_INVITE_JOB_REQUEST_CARD_INTEREST:

                //发送投递邀约卡片
                $jobId       = $content['jobId'];
                $companyId   = BaseJob::findOneVal(['id' => $jobId], 'company_id');
                $companyName = BaseCompany::findOneVal(['id' => $companyId], 'full_name');
                if (!$jobId) {
                    return false;
                }
                $content['title']       = '邀请投递';
                $content['content']     = $companyName . '觉得您很适合这个职位，诚邀您投递，欢迎了解！';
                $content['jobCardInfo'] = BaseJob::getJobCardInfo($jobId);
                if ($role == BaseMember::TYPE_PERSON) {
                    $cardModel = BaseChatMessageCard::findOne(['chat_message_id' => $messageId]);
                    if ($cardModel->operation_status == BaseChatMessageCard::INVITE_OPERATION_STATUS_INTEREST) {
                        $content['btnList'] = [
                            [
                                'type'     => 'interest',
                                'text'     => '已查看',
                                'disabled' => true,
                            ],
                        ];
                    }

                    if ($cardModel->operation_status == BaseChatMessageCard::INVITE_OPERATION_STATUS_IGNORE) {
                        $content['btnList'] = [
                            [
                                'type'     => 'ignore',
                                'text'     => '已忽略',
                                'disabled' => true,
                            ],
                        ];
                    }

                    if ($cardModel->operation_status == BaseChatMessageCard::INVITE_OPERATION_STATUS_NO) {
                        $content['btnList'] = [
                            [
                                'type'     => 'ignore',
                                'text'     => '忽略',
                                'disabled' => false,
                            ],
                            [
                                'type'     => 'interest',
                                'text'     => '感兴趣',
                                'disabled' => false,
                            ],
                        ];
                    }
                }
                break;
        }

        return $content;
    }

    /**
     * 获取聊天列表信息
     * @param $messageId
     * @param $memberId
     * @return string
     */
    public static function getListContent($messageId, $memberId)
    {
        $memberType = BaseMember::findOneVal(['id' => $memberId], 'type');
        $message    = self::findOne($messageId);
        //        if (!$message) {
        //            throw new Exception('消息不存在');
        //        }
        $fromMemberId = $message->from_member_id;
        $type         = $message->type;
        if ($type == self::TYPE_SYSTEM && $message->to_member_id == 0) {
            $lastMessageId = BaseChatMessage::find()
                ->where(['chat_room_id' => $message->chat_room_id])
                ->andWhere([
                    '!=',
                    'to_member_id',
                    0,
                ])
                ->select([
                    'id',
                ])
                ->orderBy('add_time desc,id desc')
                ->scalar();
            $message       = self::findOne($lastMessageId);
            $fromMemberId  = $message->from_member_id;
            $type          = $message->type;
        }
        //设置默认值
        $messageStatus = '';
        if ($memberId == $message->from_member_id) {
            // 这个消息是从我发出去的,这个时候才需要拼接状态等文案,
            $messageStatus = '[' . self::STATUS_TEXT_LIST[$message['status']] . ']';
            $isMy          = true;
        } else {
            // 这里是别人发过来的消息,这个时候就不需要拼接了
            $isMy = false;
        }

        switch ($type) {
            case self::TYPE_TEXT:
                //文本
                $content = $messageStatus . json_decode($message->content, true)['text'];
                break;
            case self::TYPE_JOB_CARD:
                //职位卡片
                $type = BaseChatHistoryJob::findOneVal(['chat_message_id' => $messageId], 'type');
                if (!$type || $type == BaseChatHistoryJob::TYPE_CREATE) {
                    $content = $isMy ? '由您发起沟通' : '由对方发起沟通';
                }
                if ($type == BaseChatHistoryJob::TYPE_CHANGE) {
                    $content = $isMy ? '您切换了沟通职位' : '对方切换了沟通职位';
                }
                break;
            case self::TYPE_JOB_APPLY:
                //投递消息，直接显示对应系统提示文案
                $content = $fromMemberId == $memberId ? '您已投递该职位' : '对方已投递该职位';
                break;
            case self::TYPE_RESUME_CARD:
                //发送简历
                return $messageStatus . '<span style="color: #ffa000">[在线简历]</span>';
            case self::TYPE_REQUEST_FILE:
                //请求发送附件
                $content = $memberType == BaseMember::TYPE_PERSON ? '您已向对方请求发送附件' : '对方正在向您请求发送附件';
                break;
            case self::TYPE_AGREE_REQUEST_FILE:
                //同意发送附件
                $content = $memberType == BaseMember::TYPE_PERSON ? '对方已同意您向其发送附件' : '您已同意对方向您发送附件';
                break;
            case self::TYPE_AGREE_REQUEST_FILE_CARD:
                //同意发送附件卡片
                $content = $messageStatus . '<span style="color: #ffa000">[附件补充]</span>您好，请按职位/公告要求补充相关附件';
                break;
            case self::TYPE_FILE:
                //寻找文件信息
                $fileInfo = BaseChatMessageFile::findOne(['chat_message_id' => $message->id]);
                $content  = $messageStatus . '<span style="color: #ffa000">[文件]</span>' . $fileInfo->file_name;
                break;

            case self::TYPE_INVITE_JOB_REQUEST_CARD:
                //邀约投递卡片
                $jobId   = json_decode($message->content, true)['jobId'];
                $jobName = BaseJob::findOneVal(['id' => $jobId], 'name') ?: '';
                $content = $messageStatus . '<span style="color: #ffa000">[邀请投递]</span>' . $jobName;
                break;
            case self::TYPE_RESUME_COMPLETE_CARD:
                //简历完善卡片
                $content = $messageStatus . '<span style="color: #ffa000">[简历完善提醒]</span>';
                break;
        }

        return $content;
    }

    /**
     * @param $memberId
     * 拿全部未读总数
     */
    public static function getUnreadAmount($memberId)
    {
        // return self::find()
        //     ->where([
        //         'to_member_id' => $memberId,
        //         'status'       => self::STATUS_DELIVERY,
        //     ])
        //     ->count();

        return BaseChatRoomSession::find()
            ->where([
                'member_id' => $memberId,
                'is_delete' => BaseChatRoomSession::IS_DELETE_NO,
            ])
            ->sum('unread_amount');
    }

    /**
     * 获取消息头像
     * @param $memberId  获取头像用户的id
     * @param $companyId 如果是要获取求职者头像，传入companyId，判断是否简历库
     * @return string|void
     */
    public static function getMessageAvatar($memberId, $companyId = '')
    {
        //判断当前用户类型
        $memberType = BaseMember::findOneVal(['id' => $memberId], 'type');
        if ($memberType == BaseMember::TYPE_PERSON) {
            //如果是获取求职者头像，且获取用户是单位，需要检查是否在简历库
            $baseAvatar = BaseMember::findOneVal(['id' => $memberId], 'avatar');
            $avatar     = BaseMember::getAvatar($memberId);

            if ($companyId) {
                //判断是否简历库的求职者
                $resumeModel          = BaseResume::findOne(['member_id' => $memberId]);
                $companyResumeLibrary = BaseCompanyResumeLibrary::findOne([
                    'company_id' => $companyId,
                    'resume_id'  => $resumeModel->id,
                ]);

                ///如果不是简历库的，获取模糊的头像
                if (!$companyResumeLibrary) {
                    $avatar = BaseMember::getAvatarMask($baseAvatar, $resumeModel->gender);
                }
            }

            return $avatar;
        } elseif ($memberType == BaseMember::TYPE_COMPANY) {
            //获取对方头像,如果单位用户没有头像，获取单位logo
            $avatar = BaseMember::findOneVal(['id' => $memberId], 'avatar');
            //获取单位id
            $companyId = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'company_id');
            if (!$avatar) {
                $logo = BaseCompany::findOneVal(['id' => $companyId], 'logo_url');

                $avatar = BaseCompany::getLogoFullUrl($logo);
            }

            return FileHelper::getFullUrl($avatar);
        }
    }

}