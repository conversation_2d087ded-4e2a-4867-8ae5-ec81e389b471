<?php

namespace common\base\models;

use common\models\ResumeAnnouncementReportRecord;
use common\helpers\TimeHelper;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArticle;
use common\base\models\BaseJob;
use common\base\models\BaseArea;
use common\base\models\BaseResumeEquity;
use common\base\models\BaseResumeEquitySetting;
use common\base\models\BaseResumeEquityActionRecord;

class BaseResumeAnnouncementReportRecord extends ResumeAnnouncementReportRecord
{
    // 查看报告记录上限
    // 目前通过常量写死
    const REPORT_RECORD_LIMIT = 10;

    /**
     * 获取公告使用分析报告列表
     */
    public static function getReportRecordList($resumeId, $pageSize, $page)
    {
        $onlineStatus  = BaseArticle::STATUS_ONLINE;
        $offlineStatus = BaseArticle::STATUS_OFFLINE;
        $deleteStatus  = BaseArticle::IS_DELETE_YES;

        $query = self::find()
            ->alias('rarr')
            ->leftJoin(['an' => BaseAnnouncement::tableName()], 'an.id = rarr.announcement_id')
            ->leftJoin(['ar' => BaseArticle::tableName()], 'ar.id = an.article_id')
            ->leftJoin(['j' => BaseJob::tableName()], 'j.announcement_id = an.id')
            ->where([
                'rarr.resume_id' => $resumeId,
                'j.status'       => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
                'j.is_show'      => BaseJob::IS_SHOW_YES,
            ])
            ->groupBy('an.id');

        $query->select([
            'rarr.id',
            'an.id AS announcement_id',
            'an.title AS announcement_title',
            "(CASE WHEN ar.status = $onlineStatus THEN 1 WHEN ar.status = $offlineStatus THEN -1 WHEN ar.is_delete = $deleteStatus THEN -9 END) AS announcement_status",
            'COUNT(j.id) AS job_total',
            'GROUP_CONCAT(j.amount) AS job_amount',
        ]);

        $count = $query->count();
        $pages = self::setPage($count, $page, $pageSize);

        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('announcement_status desc, rarr.add_time desc')
            ->asArray()
            ->all();

        // 最新发布的职位
        $newJobRows = BaseJob::find()
            ->alias('j')
            ->select('j.announcement_id, j.city_id, j.refresh_time, area.name')
            ->where([
                'j.announcement_id' => array_column($list, 'announcement_id'),
                'j.status'          => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
                'j.is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->leftJoin(['area' => BaseArea::tableName()], 'area.id = j.city_id')
            // 正序，indexBy之后数组第一个为最新
            ->orderBy('j.refresh_time asc, j.id asc')
            ->indexBy('announcement_id')
            ->asArray()
            ->all();

        foreach ($list as &$v) {
            // 特殊处理招聘人数
            $v['job_amount'] = strstr($v['job_amount'], '若干') !== false ? '招若干人' : '招' . array_sum(explode(',',
                    $v['job_amount'])) . '人';
            // 工作城市名称
            $v['city_id']   = $newJobRows[$v['announcement_id']]['city_id'] ?? 0;
            $v['city_name'] = $newJobRows[$v['announcement_id']]['name'] ?? '';

            $v['announcement_url'] = BaseAnnouncement::getDetailUrl($v['announcement_id']);
            // 已删除特殊处理
            $v['report_url'] = $v['announcement_status'] == -9 ? '/home/<USER>' : BaseAnnouncement::getReportUrl($resumeId,
                $v['announcement_id']);
            $v['job_total']  = $v['job_total'] . '个职位';
        }

        $res = [
            'list'  => $list,
            'pages' => [
                'size'  => (int)$pageSize,
                'total' => (int)$count,
                'page'  => (int)$page,
            ],
        ];

        return $res;
    }

    /**
     * 用于生成报告前的检查
     */
    public static function checkGenerateReport($resumeId, $announcementId)
    {
        // 公告信息
        $announcementRow = BaseAnnouncement::find()
            ->alias('an')
            ->select('ar.status')
            ->where([
                'an.id' => $announcementId,
            ])
            ->leftJoin(['ar' => BaseArticle::tableName()], 'ar.id = an.article_id')
            ->one();
        if (empty($announcementRow)) {
            throw new \Exception('公告不存在');
        }
        // 返回结果
        $res       = [
            'title'           => '提示',
            // 1:有弹窗带跳转链接,2:无弹窗带跳转链接,-1:有弹窗无跳转链接
            'jump_type'       => -1,
            'jump_url'        => '',
            'tips1'           => '',
            'tips2'           => '',
            // 取消按钮
            'cancel_btn_txt'  => '',
            // 确认按钮
            'confirm_btn_txt' => '',
            // 是否需要二次确认
            'is_confirm'      => 0,
        ];
        $recordRow = self::checkReportRecord($resumeId, $announcementId);
        // 已查看过该公告
        if ($recordRow) {
            $jumpUrl          = BaseAnnouncement::getReportUrl($resumeId, $announcementId);
            $res['jump_type'] = 2;
            $res['jump_url']  = $jumpUrl;

            return $res;
        }
        // 未拥有公告热度
        if (BaseResumeEquity::checkEquity($resumeId, BaseResumeEquitySetting::ID_ANNOUNCEMENT_POPULARITY) === false) {
            // 竞争力洞察介绍页链接
            $jumpUrl          = BaseResume::BUY_URL_VIP;
            $res['jump_type'] = 2;
            $res['jump_url']  = $jumpUrl;

            return $res;
        }
        // 今日使用次数是否达到上限
        $isLimit = self::checkReportLimit($resumeId);
        if ($isLimit) {
            $res['tips1']          = '今日公告热度分析次数已消耗完毕，请明天再试！';
            $res['tips2']          = '（每天最多可使用' . self::REPORT_RECORD_LIMIT . '次）';
            $res['cancel_btn_txt'] = '关闭';

            return $res;
        }
        // 今日使用剩余次数
        $residueLimit = self::getReportResidueLimit($resumeId);
        // 公告是否下线
        if ($announcementRow['status'] == BaseArticle::STATUS_OFFLINE) {
            $res['jump_type']       = 1;
            $res['tips1']           = '确定对该公告（已下线）使用公告热度分析吗？';
            $res['tips2']           = "（今日剩余次数：{$residueLimit}）";
            $res['cancel_btn_txt']  = '取消';
            $res['confirm_btn_txt'] = '立即使用';

            return $res;
        }

        // 校验通过
        $res['jump_type']       = 1;
        $res['tips1']           = '确定对该公告使用公告热度分析吗？';
        $res['tips2']           = "（今日剩余次数：{$residueLimit}）";
        $res['cancel_btn_txt']  = '取消';
        $res['confirm_btn_txt'] = '立即使用';

        return $res;
    }

    /**
     * 判断是否查看过指定公告
     */
    public static function checkReportRecord($resumeId, $announcementId, $token = null)
    {
        $recordRow = self::find()
            ->where([
                'resume_id'       => $resumeId,
                'announcement_id' => $announcementId,
            ])
            ->andFilterCompare('token', $token)
            ->one();
        // 已查看过该公告
        if ($recordRow) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断查看次数是否达到上限
     */
    public static function checkReportLimit($resumeId)
    {
        $count = self::find()
            ->where(['resume_id' => $resumeId])
            ->andWhere([
                'between',
                'add_time',
                TimeHelper::dayToBeginTime(CUR_DATE),
                TimeHelper::dayToEndTime(CUR_DATE),
            ])
            ->count();
        // 限制使用次数
        $limit = 10;
        if ($count >= $limit) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 查看当日使用剩余次数
     */
    public static function getReportResidueLimit($resumeId)
    {
        $count = self::find()
            ->where(['resume_id' => $resumeId])
            ->andWhere([
                'between',
                'add_time',
                TimeHelper::dayToBeginTime(CUR_DATE),
                TimeHelper::dayToEndTime(CUR_DATE),
            ])
            ->count();
        // 剩余使用次数
        $limit = self::REPORT_RECORD_LIMIT;
        $count = $limit - $count;

        return $count;
    }

    /**
     * 保存查看记录写入
     */
    public static function saveReportRecord($resumeId, $announcementId)
    {
        $model                  = new self();
        $model->resume_id       = $resumeId;
        $model->announcement_id = $announcementId;
        $model->token           = md5($resumeId . CUR_TIMESTAMP);
        if (!$model->save()) {
            throw new \Exception($model->getFirstErrorsMessage());
        }

        return $model->id;
    }

    /**
     * 记录查看记录
     * 记录权益消耗
     */
    public static function saveReportAndEquityActionRecord($resumeId, $announcementId)
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            // 报告记录
            self::saveReportRecord($resumeId, $announcementId);
            // 权益消耗记录
            BaseResumeEquityActionRecord::saveActionRecord(CUR_DATETIME, $resumeId,
                [BaseResumeEquitySetting::ID_ANNOUNCEMENT_POPULARITY], BaseResumeEquityActionRecord::EQUITY_TYPE_USED,
                BaseResumeEquityActionRecord::ACTION_USED_ANNOUNCEMENT, $announcementId,
                BaseResumeEquityActionRecord::TYPE_RELATION_REMARK_ANNOUNCEMENT,
                BaseResumeEquityActionRecord::TYPE_OPERATION_RESUME, $resumeId);
            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw new \Exception($e->getMessage());
        }
    }
}