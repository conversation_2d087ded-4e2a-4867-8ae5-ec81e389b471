<?php

namespace common\base\models;

use common\helpers\ArrayHelper;
use common\helpers\FileHelper;
use common\helpers\MaskHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\libs\Cache;
use common\models\Member;
use Yii;
use yii\base\NotSupportedException;
use yii\db\Exception;
use yii\web\IdentityInterface;

class BaseMember extends Member implements IdentityInterface
{
    // 运营添加单位统一密码
    const COMPANY_USER_PASSWORD = 'Aa_65421';

    const ORDER_BY_DESC = 1;
    const ORDER_BY_ASC  = 2;

    const USER_LOGIN_INFO = 'user_login_info';

    const TYPE_PERSON  = 1;
    const TYPE_COMPANY = 2;

    //单位主账号
    const COMPANY_MEMBER_TYPE_MAIN = 0;
    //单位子账号
    const COMPANY_MEMBER_TYPE_SUB = 1;

    // member.status
    const STATUS_ACTIVE          = 1; // 正常已审核过
    const STATUS_DELETE          = 2; // 删除
    const STATUS_AUDIT           = 9;     // 等待审核
    const STATUS_ILLEGAL         = -1;
    const STATUS_RESUME_CANCELED = 10;     // 求职者已注销

    // member.cancel_status 0未申请注销，9注销中（冷静期），1注销成功（已完成）
    const STATUS_CANCEL_STATUS_NO        = 0;
    const STATUS_CANCEL_STATUS_CANCELING = 9;
    const STATUS_CANCEL_STATUS_CANCELED  = 1;

    // 注销状态常量（新增cancel_status字段使用）
    const CANCEL_STATUS_NORMAL    = 0;      // 未申请注销
    const CANCEL_STATUS_CANCELING = 9;   // 注销中（冷静期）
    const CANCEL_STATUS_CANCELED  = 1;    // 注销成功（已完成）

    const COMPANY_STATUS_LIST = [
        self::STATUS_ACTIVE          => '正常',
        self::STATUS_DELETE          => '删除',
        self::STATUS_AUDIT           => '等待审核',
        self::STATUS_ILLEGAL         => '已禁用',
        self::STATUS_RESUME_CANCELED => '已注销',
    ];

    const SELECT_STATUS_LIST = [
        1 => '正常',
        2 => '已注销',
        3 => '注销中',
        4 => '已禁用',
    ];

    // 注销状态名称映射
    const CANCEL_STATUS_LIST = [
        self::CANCEL_STATUS_NORMAL    => '正常',
        self::CANCEL_STATUS_CANCELING => '注销中',
        self::CANCEL_STATUS_CANCELED  => '已注销',
    ];

    //当前单位帐号状态
    const COMPANY_ACCOUNTS_STATUS_NORMAL  = 1;
    const COMPANY_ACCOUNTS_STATUS_DISABLE = -1;
    //非合作单位帐号状态
    const STATUS_NO_COOPERATION        = -2;
    const COMPANY_ACCOUNTS_STATUS_LIST = [
        self::COMPANY_ACCOUNTS_STATUS_NORMAL  => '正常',
        self::COMPANY_ACCOUNTS_STATUS_DISABLE => '禁用',
    ];

    //未完善简历的
    const STATUS_WAIT_PERFECT_RESUME = 9;

    const TYPE_REGISTER           = 1;            //注册
    const TYPE_REGISTER_SAVE_INFO = 2;            //注册暂存信息

    const EMAIL_REGISTER_STATUS_ILLEGAL = -1;
    const EMAIL_REGISTER_STATUS_NORMAL  = 1;

    //当前用户的状态（用于投递简历时使用）
    const USER_STATUS_UN_LOGIN           = 0;
    const USER_STATUS_UN_COMPLETE_RESUME = 1;
    const USER_STATUS_COMPLETE_RESUME    = 2;

    //排序
    const SORT_ASC  = 1;
    const SORT_DESC = 2;
    //创建时间排序
    const SORT_CREATE_TIME_ASC  = 1;
    const SORT_CREATE_TIME_DESC = 2;
    //最近登陆时间排序
    const SORT_LAST_LOGIN_TIME_ASC  = 1;
    const SORT_LAST_LOGIN_TIME_DESC = 2;
    //根据站内投递数量排序
    const SORT_ON_SITE_APPLY_AMOUNT_ASC  = 1;
    const SORT_ON_SITE_APPLY_AMOUNT_DESC = 2;
    //根据站外投递数量排序
    const SORT_OFF_SITE_APPLY_AMOUNT_ASC  = 1;
    const SORT_OFF_SITE_APPLY_AMOUNT_DESC = 2;
    //根据约面数量排序
    const SORT_INTERVIEW_AMOUNT_ASC  = 1;
    const SORT_INTERVIEW_AMOUNT_DESC = 2;
    //根据简历下载次数排序
    const SORT_RESUME_DOWNLOAD_AMOUNT_ASC  = 1;
    const SORT_RESUME_DOWNLOAD_AMOUNT_DESC = 2;

    //根据简历完整度排序
    const SORT_RESUME_COMPLETE_ASC  = 1;
    const SORT_RESUME_COMPLETE_DESC = 2;

    //根据站外投递（邮件）排序
    const SORT_EMAIL_COUNT_ASC  = 1;
    const SORT_EMAIL_COUNT_DESC = 2;

    //根据站外投递（链接）排序
    const SORT_LINK_COUNT_ASC  = 1;
    const SORT_LINK_COUNT_DESC = 2;

    //用户是否登陆
    const IS_LOGIN_YES = 1;
    const IS_LOGIN_NO  = 0;

    // 是否直聊
    const IS_CHAT_ALLOW = 1;
    const IS_CHAT_BAN   = 2;

    const IS_CHAT_LIST = [
        self::IS_CHAT_ALLOW => '开启',
        self::IS_CHAT_BAN   => '关闭',
    ];

    //打招呼相关常量
    // 是-打开
    const IS_CHAT_WINDOW_YES = 1;
    //否
    const IS_CHAT_WINDOW_NO = 2;
    //是
    const IS_GREETING_YES = 1;
    //否
    const IS_GREETING_NO = 2;
    //默认招呼语类型-系统
    const GREETING_TYPE_SYSTEM = 1;
    //默认招呼语类型-自定义
    const GREETING_TYPE_CUSTOM = 2;

    const ACTIVE_RULE_NEWLY_ACTIVE  = 1;//刚刚活跃
    const ACTIVE_RULE_YESTERDAY     = 2;//昨日活跃
    const ACTIVE_RULE_THREE_DAYS    = 3;//3天内活跃
    const ACTIVE_RULE_CURRENT_WEEK  = 4;//本周活跃
    const ACTIVE_RULE_TWO_WEEk      = 5;//两周内活跃
    const ACTIVE_RULE_CURRENT_MONTH = 6;//本月活跃
    const ACTIVE_RULE_NO            = 100;//不显示活跃
    const ACTIVE_RULE_LIST_FOR_CHAT = [
        self::ACTIVE_RULE_NEWLY_ACTIVE  => '刚刚活跃',
        self::ACTIVE_RULE_YESTERDAY     => '昨日活跃',
        self::ACTIVE_RULE_THREE_DAYS    => '3天内活跃',
        self::ACTIVE_RULE_CURRENT_WEEK  => '本周活跃',
        self::ACTIVE_RULE_TWO_WEEk      => '两周内活跃',
        self::ACTIVE_RULE_CURRENT_MONTH => '本月活跃',
        self::ACTIVE_RULE_NO            => '',
    ];

    const ACTIVE_RULE_LIST_FOR_COMPANY = [
        self::ACTIVE_RULE_NEWLY_ACTIVE  => '今天',
        self::ACTIVE_RULE_YESTERDAY     => '昨天',
        self::ACTIVE_RULE_THREE_DAYS    => '近3天',
        self::ACTIVE_RULE_CURRENT_WEEK  => '近7天',
        self::ACTIVE_RULE_TWO_WEEk      => '近15天',
        self::ACTIVE_RULE_CURRENT_MONTH => '近30天',
        self::ACTIVE_RULE_NO            => '-',
    ];

    public static function getActiveText($lastActiveDate, $typeMap = self::ACTIVE_RULE_LIST_FOR_CHAT)
    {
        $lastActiveDate = date('Y-m-d', strtotime($lastActiveDate));
        switch (true) {
            case $lastActiveDate == date('Y-m-d'):
                $activeType = self::ACTIVE_RULE_NEWLY_ACTIVE;
                $isOnline   = true;
                break;
            case $lastActiveDate == date('Y-m-d', strtotime('-1 day')):
                $activeType = self::ACTIVE_RULE_YESTERDAY;
                $isOnline   = true;
                break;
            case $lastActiveDate == date('Y-m-d', strtotime('-2 day')):
                $activeType = self::ACTIVE_RULE_THREE_DAYS;
                $isOnline   = true;
                break;
            case $lastActiveDate >= date('Y-m-d', strtotime('-6 day')) && $lastActiveDate < date('Y-m-d',
                    strtotime('-2 day')):
                $activeType = self::ACTIVE_RULE_CURRENT_WEEK;
                $isOnline   = true;
                break;
            case $lastActiveDate >= date('Y-m-d', strtotime('-13 day')) && $lastActiveDate < date('Y-m-d',
                    strtotime('-6 day')):
                $activeType = self::ACTIVE_RULE_TWO_WEEk;
                $isOnline   = false;
                break;
            case  $lastActiveDate >= date('Y-m-d', strtotime('-29 day')) && $lastActiveDate < date('Y-m-d',
                    strtotime('-13 day')):
                $activeType = self::ACTIVE_RULE_CURRENT_MONTH;
                $isOnline   = false;
                break;
            default:
                $activeType = self::ACTIVE_RULE_NO;
                $isOnline   = false;
        }

        return [
            $activeType,
            $typeMap[$activeType] ?? '',
            $isOnline,
        ];
    }

    public function beforeSave($insert)
    {
        if (parent::beforeSave($insert)) {
            if ($this->isNewRecord) {
                $this->add_time = CUR_DATETIME;
                if ($this->password) {
                    $this->password = Yii::$app->getSecurity()
                        ->generatePasswordHash($this->password);
                }
                if (!$this->status) {
                    $this->status = self::STATUS_ACTIVE;
                }
                $this->is_chat == self::IS_CHAT_ALLOW;
            } else {
                $this->update_time = CUR_DATETIME;
            }

            return true;
        } else {
            return false;
        }
    }

    /**
     * {@inheritdoc}
     */
    public static function findIdentity($id, $status = self::STATUS_ACTIVE)
    {
        return static::findOne([
            'id'     => $id,
            'status' => $status,
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public static function findIdentityByAccessToken($token, $type = null)
    {
        //        return static::findOne(['access_token' => $token]);
        throw new NotSupportedException('"findIdentityByAccessToken" is not implemented.');
    }

    /**
     * Finds user by username.
     *
     * @param string $username
     *
     * @return static|null
     */
    public static function findByUsername($username, $type)
    {
        return static::findOne([
            'username' => $username,
            'type'     => $type,
        ]);
    }

    /**
     * 暂时废弃掉mobileCode
     * @param $mobile
     * @param $type
     * @param $mobileCode
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function findByMobile($mobile, $type, $mobileCode = '')
    {
        if ($type == self::TYPE_PERSON) {
            $user = self::find()
                ->alias('m')
                ->select(['m.id'])
                ->asArray()
                ->innerJoin(['r' => BaseResume::tableName()], 'r.member_id = m.id')
                ->where([
                    'm.mobile' => $mobile,
                    'm.type'   => $type,
                ])
                ->orderBy('r.complete desc,m.add_time asc')
                ->one();
            if ($user) {
                $user = static::findOne($user['id']);
            }
        } else {
            $user = static::findOne([
                'mobile' => $mobile,
                'type'   => $type,
            ]);
        }

        return $user;
    }

    public static function findByEmail($email, $type)
    {
        return static::findOne([
            'email' => $email,
            'type'  => $type,
        ]);
    }

    /**
     * 查询邮箱是否绑定.
     */
    public static function findByEmailWithStatus($email, $type)
    {
        return static::findOne([
            'email'                 => $email,
            'type'                  => $type,
            'email_register_status' => self::EMAIL_REGISTER_STATUS_NORMAL,
        ]);
    }

    /**
     * 设置用户的realStatus.
     *
     * @param $info ,$memberId
     */
    public static function setLoginInfo($info, $memberId)
    {
        Cache::set(Cache::PC_MEMBER_LOGIN_INFO_KEY . ':' . $memberId, json_encode($info));
    }

    /**
     * 获取主表的id(company_id/resume_id).
     *
     * @return mixed
     */
    public static function getMainId($memberId = '')
    {
        if (!$memberId) {
            $memberId = Yii::$app->user->id;
        }

        if (!$memberId) {
            return '';
        }

        $info = json_decode(Cache::get(Cache::PC_MEMBER_LOGIN_INFO_KEY . ':' . $memberId), true);

        if (!$info['mainId']) {
            if ($info['type'] == BaseMember::TYPE_PERSON) {
                $person         = BaseResume::findOne(['member_id' => $memberId]);
                $info['mainId'] = $person['id'];
                BaseMember::setLoginInfo($info, $memberId);
            }

            if ($info['type'] == BaseMember::TYPE_COMPANY) {
                $companyId      = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId,], 'company_id');
                $company        = BaseCompany::findOne(['id' => $companyId]);
                $info['mainId'] = $company['id'];
                BaseMember::setLoginInfo($info, $memberId);
            }
        }

        if (!$info['mainId']) {
            return '';
        }

        return $info['mainId'];
    }

    /**
     * 获取用户的realStatus.
     */
    public static function getLoginInfo()
    {
        $memberId = Yii::$app->user->id;

        $info = json_decode(Cache::get(Cache::PC_MEMBER_LOGIN_INFO_KEY . ':' . $memberId), true);

        if (!$info) {
            Yii::$app->user->logout();

            return [];
        }
        $info['showName'] = $info['name'] ?: $info['mobile'] ?: $info['email'];

        return $info;
    }

    /**
     * 获取用户的realStatus.
     */
    public static function getLoginInfoByMemberId($memberId)
    {
        $info = json_decode(Cache::get(Cache::PC_MEMBER_LOGIN_INFO_KEY . ':' . $memberId), true);
        if (!$info) {
            return [];
        }
        $info['showName'] = $info['name'] ?: $info['mobile'] ?: $info['email'];

        return $info;
    }

    public static function updateUserRealStatus($status, $memberId)
    {
        $info = json_decode(Cache::get(Cache::PC_MEMBER_LOGIN_INFO_KEY . ':' . $memberId), true);

        $info['status'] = $status;

        self::setLoginInfo($info, $memberId);
    }

    /**
     * {@inheritdoc}
     */
    public function getId()
    {
        return $this->getPrimaryKey();
    }

    /**
     * {@inheritdoc}
     */
    public function getAuthKey()
    {
        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function validateAuthKey($authKey)
    {
        return $this->getAuthKey() === $authKey;
    }

    /**
     * Validates password.
     *
     * @param string $password password to validate
     *
     * @return bool if password provided is valid for current user
     */
    public function validatePassword($password)
    {
        return Yii::$app->security->validatePassword($password, $this->password);
    }

    /**
     * Generates password hash from password and sets it to the model.
     *
     * @param string $password
     */
    public function setPassword($password)
    {
        $this->password_hash = Yii::$app->security->generatePasswordHash($password);
    }

    /**
     * Generates "remember me" authentication key.
     */
    //    public function generateAuthKey()
    //    {
    //        $this->auth_key = Yii::$app->security->generateRandomString();
    //    }

    /**
     * Generates new password reset token.
     */
    public function generatePasswordResetToken()
    {
        $this->password_reset_token = Yii::$app->security->generateRandomString() . '_' . time();
    }

    /**
     * Removes password reset token.
     */
    public function removePasswordResetToken()
    {
        $this->password_reset_token = null;
    }

    /**
     * 查找邮箱是否已经存在.
     *
     * @param $email
     *
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function findEmail($email, $type)
    {
        return static::find()
            ->where([
                'email' => $email,
                'type'  => $type,
            ])
            ->one();
    }

    /**
     * 查找邮箱是否已经存在
     * 针对是否绑定做判断.
     *
     * @param $email
     *
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function findEmailWithStatus($email, $type)
    {
        $count = static::find()
            ->where([
                'email'                 => $email,
                'type'                  => $type,
                'email_register_status' => self::EMAIL_REGISTER_STATUS_NORMAL,
            ])
            ->count();

        if ($count > 0) {
            return false;
        }

        return true;
    }

    public static function getMemberField($memberId, $field, $type = self::TYPE_PERSON)
    {
        $info = self::find()
            ->where([
                'id'     => $memberId,
                'type'   => $type,
                'status' => self::STATUS_ACTIVE,
            ])
            ->select($field)
            ->asArray()
            ->one();

        return ArrayHelper::getValue($info, $field);
    }

    public static function getBaseInfo()
    {
        if (Yii::$app->user->isGuest) {
            return [];
        }
        $userId = Yii::$app->user->id;
        $type   = Yii::$app->user->identity->type;
        $email  = Yii::$app->user->identity->email;
        $mobile = Yii::$app->user->identity->mobile;

        if ($type == self::TYPE_PERSON) {
            $user = BaseResume::find()
                ->select('name')
                ->where(['member_id' => $userId])
                ->asArray()
                ->one();
        } else {
            $user = BaseCompany::find()
                ->select('short_name as name')
                ->where(['member_id' => $userId])
                ->asArray()
                ->one();
        }

        return [
            'user' => [
                'name'   => $user['name'] ?: $mobile ?: $email,
                'mobile' => $mobile,
                'email'  => $email,
            ],
        ];
    }

    /**
     * 检查手机号的唯一性(除了这个会员以外还有没有用这个手机号).
     *
     * @param $memberId
     * @param $mobile
     */
    public static function checkMobileOnly($memberId, $mobile, $mobileCode)
    {
        $member = self::findOne($memberId);

        $count = self::find()
            ->where([
                'mobile'      => $mobile,
                'type'        => $member->type,
                'mobile_code' => StringHelper::getMobileCodeNumber($mobileCode),
            ])
            ->andWhere([
                '!=',
                'id',
                $memberId,
            ])
            ->count();

        if ($count > 0) {
            return false;
        }

        return true;
    }

    /**
     * 检查邮箱的唯一性(除了这个会员以外还有没有用这个邮箱).
     *
     * @param $memberId
     * @param $email
     */
    public static function checkEmailOnly($memberId, $email)
    {
        $member = self::findOne($memberId);

        $count = self::find()
            ->where([
                'email'                 => $email,
                'type'                  => $member->type,
                'email_register_status' => self::EMAIL_REGISTER_STATUS_NORMAL,
            ])
            ->andWhere([
                '!=',
                'id',
                $memberId,
            ])
            ->count();

        if ($count > 0) {
            return false;
        }

        return true;
    }

    /**
     * 生成用户名.
     *
     * @param int $letterLength
     * @param int $numberLength
     */
    public static function createUserName($letterLength = 2, $numberLength = 6): string
    {
        $letter    = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $number    = '0123456789';
        $numberLen = strlen($number) - 1;
        $letterLen = strlen($letter) - 1;

        $str = '';

        for ($j = 0; $j < $letterLength; ++$j) {
            $str .= $letter[mt_rand(0, $letterLen)];
        }
        for ($i = 0; $i < $numberLength; ++$i) {
            $str .= $number[mt_rand(0, $numberLen)];
        }

        // 这里还需要检查一下,是否已经有这个用户名了
        $count = self::find()
            ->where(['username' => $str])
            ->count();

        if ($count > 0) {
            // 加个睡眠时间,防止时间戳导致的随机数一样的
            sleep(0.3);
            $str = self::createUserName();
        }

        return $str;
    }

    /**
     * 获取用户信息.
     *
     * @param int $memberId
     */
    public static function getMemberInfo($memberId): array
    {
        $memberInfo                    = self::find()
            ->alias('m')
            ->leftJoin(['r' => BaseResume::tableName()], 'r.member_id = m.id')
            ->leftJoin(['e' => BaseResumeEducation::tableName()], 'e.id = r.last_education_id')
            ->where(['m.id' => $memberId])
            ->select([
                'm.id',
                'm.username',
                'm.mobile',
                'm.avatar',
                'm.email',
                'r.gender',
                'r.age',
                'r.name',
                'r.id as resumeId',
                'r.work_experience',
                'e.education_id',
                'e.school',
                'e.major_id',
            ])
            ->asArray()
            ->one();
        $memberInfo['major']           = BaseMajor::getMajorName($memberInfo['major_id']);
        $memberInfo['education']       = BaseDictionary::getEducationName($memberInfo['education_id']);
        $memberInfo['avatar']          = FileHelper::getFullUrl($memberInfo['avatar']);
        $memberInfo['work_experience'] = BaseResumeWork::getWorkExperienceText($memberInfo['resumeId']);

        return $memberInfo;
    }

    /**
     * 获取用户简历状态
     *
     * @param $memberId
     *
     * @return int
     */
    public static function getUserResumeStatus($memberId)
    {
        $resumeId = BaseMember::getMainId();
        //用户步数
        $resumeStepNum = BaseResumeComplete::getResumeStep($resumeId);
        if ($resumeStepNum < 4) {
            //用户未完成前三步
            $status = self::USER_STATUS_UN_COMPLETE_RESUME;
        } elseif ($resumeStepNum >= 4) {
            $status = self::USER_STATUS_COMPLETE_RESUME;
        }

        return $status;
    }

    /**
     * 获取用户活跃时间.
     *
     * @param $memberId
     *
     * @return string
     */
    public static function getUserActiveTime($memberId)
    {
        $lastLoginTime = self::findOneVal(['id' => $memberId], 'last_active_time');
        $days          = ceil(TimeHelper::reduceDates(CUR_DATETIME, $lastLoginTime));

        if ($days == 0) {
            $activeTime = '今天活跃';
        } elseif ($days > 0 && $days < 3) {
            $activeTime = $days . '天前活跃';
        } elseif ($days >= 3 && $days < 7) {
            $activeTime = '近一周活跃';
        } elseif ($days >= 7 && $days < 30) {
            $activeTime = '近一个月活跃';
        } else {
            $activeTime = '';
        }

        return $activeTime;
    }

    /**
     * 获取用户收藏总数.
     *
     * @param $memberId
     *
     * @return bool|int|string|null
     */
    public static function getAllCollectAmount($memberId)
    {
        $where                     = [
            'member_id' => $memberId,
            'status'    => BaseJobCollect::STATUS_ACTIVE,
        ];
        $jobCollectAmount          = BaseJobCollect::find()
            ->where($where)
            ->count();
        $announcementCollectAmount = BaseAnnouncementCollect::find()
            ->where($where)
            ->count();
        $companyCollectAmount      = BaseCompanyCollect::find()
            ->where($where)
            ->count();
        $newsCollectAmount         = BaseNewsCollect::find()
            ->where($where)
            ->count();

        return $jobCollectAmount + $announcementCollectAmount + $companyCollectAmount + $newsCollectAmount;
    }

    /**
     * 获取头像实际链接.
     *
     * @return mixed|string
     */
    public static function getAvatar($memberId)
    {
        $avatar = self::findOneVal(['id' => $memberId], 'avatar');
        $gender = BaseResume::findOneVal(['member_id' => $memberId], 'gender');
        if ($avatar) {
            return FileHelper::getFullUrl($avatar);
        }

        switch ($gender) {
            case 1:
                return Yii::$app->params['defaultMemberAvatarMale'];
            case 2:
                return Yii::$app->params['defaultMemberAvatarFemale'];
            default:
                return Yii::$app->params['defaultMemberAvatar'];
        }
    }

    public static function getMiniMemberId()
    {
        $token = \Yii::$app->request->getHeaders()
            ->get('authorization-token');

        if (empty($token)) {
            return false;
        }

        $jwtAuth = new \common\libs\JwtAuth();
        $userId  = $jwtAuth->checkToken($token);

        return $userId;
    }

    /**
     * 获取模糊头像实际链接.
     *
     * @param $avatar
     * @param $gender
     *
     * @return mixed|string
     */
    public static function getAvatarMask($avatar, $gender)
    {
        if ($avatar) {
            return MaskHelper::getImage(FileHelper::getFullUrl($avatar));
        }

        switch ($gender) {
            case 1:
                return Yii::$app->params['defaultMemberAvatarMale'];
            case 2:
                return Yii::$app->params['defaultMemberAvatarFemale'];
            default:
                return Yii::$app->params['defaultMemberAvatar'];
        }
    }

    public static function changeAvatar($data)
    {
        $avatar = $data['avatar'];
        if (empty($avatar)) {
            throw new Exception('头像地址不能为空');
        }
        $memberId      = \Yii::$app->user->id ?: $data['memberId'];
        $model         = self::findOne($memberId);
        $model->avatar = $avatar;
        $model->save();
        $resumeModel              = BaseResume::findOne(['member_id' => $memberId]);
        $resumeModel->complete    = BaseResume::updateComplete($memberId);
        $resumeModel->update_time = date('Y-m-d H:i:s');
        $resumeModel->save();
    }

    /**
     * 获取完整的手机号，海外号段包含区号
     * @param $memberId
     * @return mixed|string
     */
    public static function getFullMobile($memberId)
    {
        $memberInfo = self::find()
            ->where(['id' => $memberId])
            ->select([
                'mobile',
                'mobile_code as mobileCode',
            ])
            ->asArray()
            ->one();
        if ($memberInfo['mobileCode'] == '86' || empty($memberInfo['mobileCode'])) {
            return $memberInfo['mobile'];
        } else {
            return '+' . $memberInfo['mobileCode'] . ' ' . $memberInfo['mobile'];
        }
    }

    /**
     * 通过传入区号、手机号，拼接完整手机号，脱敏可用
     * @param $mobile
     * @param $mobileCode
     * @return string
     */
    public static function getFormatFullMobile($mobile, $mobileCode)
    {
        if ($mobileCode == '86' || empty($mobileCode)) {
            return $mobile;
        } else {
            return '+' . $mobileCode . ' ' . $mobile;
        }
    }

    /**
     * @param $data
     * @return string
     * @throws \yii\base\Exception
     */
    public static function editIsChat($data): string
    {
        $model          = self::findOne($data['memberId']);
        $model->is_chat = $data['isChat'];
        if (!$model->save()) {
            throw new Exception('更改错误');
        }

        return true;
    }

    public static function checkMemberIsCanceled($memberId = 0, $resumeId = 0)
    {
        if (empty($memberId) && empty($resumeId)) {
            throw new \Exception('参数错误');
        }
        $where = ['and'];
        if (!empty($resumeId)) {
            $resumeModel = BaseResume::findOne($resumeId);
            $where[] = [
                '=',
                'id',
                $resumeModel->member_id,
            ];
        }
        if (!empty($memberId)) {
            $where[] = [
                '=',
                'id',
                $memberId,
            ];
        }
        $resumeMemberModel = BaseMember::find()->andWhere($where)->one();
        if ($resumeMemberModel->status == BaseMember::STATUS_RESUME_CANCELED) {
            throw new \Exception('该用户已注销');
        }
    }

}
