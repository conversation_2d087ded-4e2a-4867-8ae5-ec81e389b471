<?php

namespace common\libs;

use <PERSON><PERSON><PERSON><PERSON>\JWT\Encoding\ChainedFormatter;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Encoding\JoseEncoder;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Key\InMemory;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Hmac\Sha256;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Token\Builder;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Token\Parser;
use <PERSON><PERSON><PERSON><PERSON>\JWT\UnencryptedToken;
use Yii;
use yii\base\Exception;

class JwtAuth
{
    private $tokenId;
    /**
     * @var Parser
     */
    /**
     * @var Builder
     */
    private $builder;
    /**
     * @var Sha256
     */
    private $signer;

    // 10天
    public $tokenTtl = 864000;
    // private $tokenTtl = 1;

    public $token;
    public $expireTime;

    public function __construct()
    {
        $this->builder = (new Builder(new JoseEncoder(), ChainedFormatter::default()));
        $this->signer  = new Sha256();
        $this->tokenId = 'gaocaitest';
    }

    /**
     * 获取JWT
     * @param string $id  ID
     * @param array  $ext 扩展信息
     * @return string jwt
     */
    public function createToken(string $id, array $ext = []): string
    {
        $tokenBuilder = $this->builder;
        $algorithm    = $this->signer;
        $signingKey   = InMemory::plainText(random_bytes(32));
        $now          = new \DateTimeImmutable();
        $expireTime   = $now->modify('+' . $this->tokenTtl . ' second');
        $token        = $tokenBuilder->issuedAt($now)
            ->identifiedBy($this->tokenId)
            // 添加域名
            ->expiresAt($expireTime)
            ->withClaim('uid', $id)
            ->withClaim('environment', Yii::$app->params['environment'])
            ->getToken($algorithm, $signingKey);

        $expireTimeTimestamp = $expireTime->getTimestamp();

        $this->expireTime = $expireTimeTimestamp * 1000;
        $this->token      = $token->toString();

        return true;
    }

    /**
     * 检查Token并返回用户ID(还需要检查是否过期)
     * @return bool|int
     */
    public function checkToken($token)
    {
        $parser = new Parser(new JoseEncoder());
        try {
            $token = $parser->parse($token);
        } catch (Exception $e) {
            return false;
        }

        $now = new \DateTimeImmutable();
        // 检查是否已经过期
        if ($token->isExpired($now)) {
            return false;
        }

        assert($token instanceof UnencryptedToken);

        $id = $token->claims()
            ->get('uid');

        $environment = $token->claims()
            ->get('environment');

        if ($environment != Yii::$app->params['environment']) {
            return false;
        }

        return $id;
    }

}