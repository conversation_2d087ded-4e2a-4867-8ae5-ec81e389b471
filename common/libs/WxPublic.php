<?php

namespace common\libs;

use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyMemberMessageConfig;
use common\base\models\BaseCompanyMemberWxBind;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeWxBind;
use common\helpers\DebugHelper;
use common\helpers\MaskHelper;
use common\models\ResumeWxBind;
use EasyWeChat\Factory;
use EasyWeChat\Kernel\Messages\Image;
use EasyWeChat\Kernel\Messages\Text;
use Yii;

/**
 * 微信服务号(公众号相关,这里以后有可能会扩展到单位用的服务号)
 */
class WxPublic
{
    // 初始化
    private static $instance = null;
    private static $app;

    private $config;

    private $type;

    const TYPE_RESUME  = 1;
    const TYPE_COMPANY = 2;

    const QR_SCENE_TEMP_TIME = 300;

    // 这里用小写是为了配合微信的配置
    // 绑定公众号(服务号)
    const QR_CODE_RESUME_BIND_KEY   = 'resume_bind';
    const QR_CODE_RESUME_LOGIN_KEY  = 'resume_login';
    const QR_CODE_COMPANY_BIND_KEY  = 'company_bind';
    const QR_CODE_COMPANY_LOGIN_KEY = 'company_login';

    // 求职者VIP购买引导
    const QR_CODE_RESUME_BUY_GUIDE_KEY = 'resume_buy_guide';

    //扫码关注，但是不触发欢迎语句推送（会在parseQrKey里做统一推送）
    const SCAN_SUBSCRIBE_NOT_SEND_KEY_LIST = [
        'qrscene_resume_buy_guide',
    ];

    // 我要入群点击事件
    const CLICK_TYPE_IN_GROUP_KEY = 'IN_GROUP';

    // 等等扫码
    const LOGIN_QRCODE_STATUS_WAIT = 1;
    // 已经绑定了
    const LOGIN_QRCODE_STATUS_BIND = 2;
    // 还没绑定
    const LOGIN_QRCODE_STATUS_WAIT_BIND = 3;
    // 错误
    const LOGIN_QRCODE_STATUS_WAIT_FAIL = 4;
    //过期
    const LOGIN_QRCODE_STATUS_OVERDUE = 9;

    // 等等扫码
    const BIND_QRCODE_STATUS_WAIT = 1;
    // 已扫码未绑定
    const BIND_QRCODE_STATUS_SCAN_WAIT_BIND = 2;
    // 已扫码已被绑定
    const BIND_QRCODE_STATUS_SCAN_BIND = 3;
    // 错误
    const BIND_QRCODE_STATUS_WAIT_FAIL = 4;
    //过期
    const BIND_QRCODE_STATUS_OVERDUE = 9;

    const QR_CODE_RESUME_BUY_GUIDE_URL = 'https://img.gaoxiaojob.com/uploads/wx_public/resume_buy_guide.png';

    public function __construct($type)
    {
        switch ($type) {
            case self::TYPE_RESUME:
                $config = Yii::$app->params['wx']['personPublic'];
                break;
            case self::TYPE_COMPANY:
                $config = Yii::$app->params['wx']['companyPublic'];
                break;
            default:
                throw new \Exception('未知的微信公众号类型');
        }

        $this->type   = $type;
        $this->config = $config;

        self::$app = Factory::officialAccount($config);
    }

    public static function getInstance($type = self::TYPE_RESUME): WxPublic
    {
        if (empty(self::$instance)) {
            self::$instance = new self($type);
        }

        return self::$instance;
    }

    /**
     * 创建绑定二维码
     * @param $id
     */
    public function oldcreateBindQrCode($id)
    {
        if ($this->type == self::TYPE_RESUME) {
            $model = ResumeWxBind::find()
                ->where(['resume_id' => $id])
                ->one();

            // 1.12
            // if ($model) {
            //     throw new \Exception('用户已经绑定了');
            // }

            $key      = self::QR_CODE_RESUME_BIND_KEY . '_' . $id;
            $cacheKey = Cache::PC_RESUME_WX_QR_CODE_BIND_INFO_KEY;
        } else {
            $model = BaseCompanyMemberWxBind::find()
                ->where(['company_member_id' => $id])
                ->one();
            if ($model) {
                throw new \Exception('用户已经绑定了');
            }
            $key      = self::QR_CODE_COMPANY_BIND_KEY . '_' . $id;
            $cacheKey = Cache::PC_COMPANY_WX_QR_CODE_BIND_INFO_KEY;
        }

        $result = self::$app->qrcode->temporary($key, self::QR_SCENE_TEMP_TIME);

        // 同时在缓存里面生成一个key,用于判断是否已经扫码
        $key      = $cacheKey . ':' . $result['ticket'];
        $tmpToken = Yii::$app->security->generateRandomString();

        $data = json_encode([
            'status' => self::BIND_QRCODE_STATUS_WAIT,
            'token'  => $tmpToken,
        ]);

        // 和二维码一样时间
        // Cache::set($key, self::LOGIN_QRCODE_STATUS_WAIT, self::QR_SCENE_TEMP_TIME);

        Cache::set($key, $data, self::QR_SCENE_TEMP_TIME);
        $imageUrl = self::$app->qrcode->url($result['ticket']);

        return [
            'url'           => $imageUrl,
            'key'           => $key,
            'ticket'        => $result['ticket'],
            'token'         => $tmpToken,
            'expireSeconds' => self::QR_SCENE_TEMP_TIME,
        ];
    }

    public function createBindQrCode($id)
    {
        if ($this->type == self::TYPE_RESUME) {
            $model    = ResumeWxBind::find()
                ->select('unionid')
                ->where(['resume_id' => $id])
                ->asArray()
                ->one();
            $key      = self::QR_CODE_RESUME_BIND_KEY . '_' . $id;
            $cacheKey = Cache::PC_RESUME_WX_QR_CODE_BIND_INFO_KEY;
        } else {
            $model = BaseCompanyMemberWxBind::find()
                ->where(['company_member_id' => $id])
                ->one();
            if ($model) {
                throw new \Exception('用户已经绑定了');
            }
            $key      = self::QR_CODE_COMPANY_BIND_KEY . '_' . $id;
            $cacheKey = Cache::PC_COMPANY_WX_QR_CODE_BIND_INFO_KEY;
        }

        $result = self::$app->qrcode->temporary($key, self::QR_SCENE_TEMP_TIME);

        // 同时在缓存里面生成一个key,用于判断是否已经扫码
        $key      = $cacheKey . ':' . $result['ticket'];
        $tmpToken = Yii::$app->security->generateRandomString();

        $data = json_encode([
            'status' => self::BIND_QRCODE_STATUS_WAIT,
            'token'  => $tmpToken,
        ]);

        // 和二维码一样时间
        // Cache::set($key, self::LOGIN_QRCODE_STATUS_WAIT, self::QR_SCENE_TEMP_TIME);

        Cache::set($key, $data, self::QR_SCENE_TEMP_TIME);
        $imageUrl = self::$app->qrcode->url($result['ticket']);

        return [
            'url'           => $imageUrl,
            'key'           => $key,
            'ticket'        => $result['ticket'],
            'token'         => $tmpToken,
            'expireSeconds' => self::QR_SCENE_TEMP_TIME,
        ];
    }

    /**
     * 服务号绑定出动态码
     * 兼容已经绑定过用户出静态码
     */
    public function getBindQrCode($id, $expireSeconds = self::QR_SCENE_TEMP_TIME)
    {
        if ($this->type == self::TYPE_RESUME) {
            $model = ResumeWxBind::find()
                ->where(['resume_id' => $id])
                ->one();
            if ($model) {
                return [
                    'url'           => Yii::$app->params['wx']['personPublicCodeUrl'],
                    'key'           => '',
                    'ticket'        => '',
                    'token'         => '',
                    'expireSeconds' => '',
                ];
            }

            $key      = self::QR_CODE_RESUME_BIND_KEY . '_' . $id;
            $cacheKey = Cache::PC_RESUME_WX_QR_CODE_BIND_INFO_KEY;
        } else {
            $key      = self::QR_CODE_COMPANY_BIND_KEY . '_' . $id;
            $cacheKey = Cache::PC_COMPANY_WX_QR_CODE_BIND_INFO_KEY;
            $model    = BaseCompanyMemberWxBind::find()
                ->where(['company_member_id' => $id])
                ->one();
            if ($model) {
                return [
                    'url'           => Yii::$app->params['wx']['companyPublicCodeUrl'],
                    'key'           => '',
                    'ticket'        => '',
                    'token'         => '',
                    'expireSeconds' => '',
                ];
            }

            // 还有一种情况,就是这个单位没有通过审核,也就是BaseCompanyMemberInfo没有信息
            if (!BaseCompanyMemberInfo::findIsExist(['member_id' => $id])) {
                return [
                    'url'           => Yii::$app->params['wx']['companyPublicCodeUrl'],
                    'key'           => '',
                    'ticket'        => '',
                    'token'         => '',
                    'expireSeconds' => '',
                ];
            }
        }

        $result = self::$app->qrcode->temporary($key, $expireSeconds);

        // 同时在缓存里面生成一个key,用于判断是否已经扫码
        $key      = $cacheKey . ':' . $result['ticket'];
        $tmpToken = Yii::$app->security->generateRandomString();

        $data = json_encode([
            'status' => self::BIND_QRCODE_STATUS_WAIT,
            'token'  => $tmpToken,
        ]);

        Cache::set($key, $data, $expireSeconds);
        $imageUrl = self::$app->qrcode->url($result['ticket']);

        return [
            'url'           => $imageUrl,
            'key'           => $key,
            'ticket'        => $result['ticket'],
            'token'         => $tmpToken,
            'expireSeconds' => $expireSeconds,
        ];
    }

    /**
     * 生成登录的qrcode
     * @return array
     * @throws \yii\base\Exception
     */
    public function createLoginQrcode()
    {
        // 判断一下是求职者还是单位
        if ($this->type == self::TYPE_RESUME) {
            $key      = self::QR_CODE_RESUME_LOGIN_KEY;
            $cacheKey = Cache::PC_RESUME_WX_QR_CODE_LOGIN_INFO_KEY;
        } else {
            $key      = self::QR_CODE_COMPANY_LOGIN_KEY;
            $cacheKey = Cache::PC_COMPANY_WX_QR_CODE_LOGIN_INFO_KEY;
        }

        // 发送到微信生成一个临时的二维码,时间300s
        $result = self::$app->qrcode->temporary($key, self::QR_SCENE_TEMP_TIME);

        // 同时在缓存里面生成一个key,用于判断是否已经扫码
        $key      = $cacheKey . ':' . $result['ticket'];
        $tmpToken = Yii::$app->security->generateRandomString();

        $data = json_encode([
            'status' => self::LOGIN_QRCODE_STATUS_WAIT,
            'token'  => $tmpToken,
        ]);

        // 和二维码一样时间
        // Cache::set($key, self::LOGIN_QRCODE_STATUS_WAIT, self::QR_SCENE_TEMP_TIME);

        Cache::set($key, $data, self::QR_SCENE_TEMP_TIME);

        return [
            'url'           => self::$app->qrcode->url($result['ticket']),
            'ticket'        => $result['ticket'],
            'token'         => $tmpToken,
            'expireSeconds' => self::QR_SCENE_TEMP_TIME,
        ];
    }

    public function createBuyGuideQrcode()
    {
        $key = self::QR_CODE_RESUME_BUY_GUIDE_KEY;
        // 发送到微信生成一个临时的二维码,时间300s
        // 这个码扫了以后会带场景值到服务号
        $result = self::$app->qrcode->forever($key);

        return [
            'url'    => self::$app->qrcode->url($result['ticket']),
            'ticket' => $result['ticket'],
        ];
    }

    /**
     * 检查登录二维码的状态
     * @param $ticket
     * @param $token
     * @return array|mixed
     * @throws \Exception
     */
    public function checkLoginQrcode($ticket, $token)
    {
        if ($this->type == self::TYPE_RESUME) {
            $cacheKey = Cache::PC_RESUME_WX_QR_CODE_LOGIN_INFO_KEY;
        } else {
            $cacheKey = Cache::PC_COMPANY_WX_QR_CODE_LOGIN_INFO_KEY;
        }
        $key  = $cacheKey . ':' . $ticket;
        $json = Cache::get($key);
        if ($json) {
            $data = json_decode($json, true);
            if ($data['token'] != $token) {
                throw new \Exception('非法扫码');
            }

            if ($data['status'] == self::LOGIN_QRCODE_STATUS_WAIT_BIND || $data['status'] == self::LOGIN_QRCODE_STATUS_BIND) {
                // token校验通过,销毁这个缓存
                Cache::delete($key);
            }

            return $data;
        } else {
            return [
                'status' => self::LOGIN_QRCODE_STATUS_OVERDUE,
                'tips'   => '二维码过期',
            ];
        }
    }

    public function checkBindQrcode($ticket, $token)
    {
        if ($this->type == self::TYPE_RESUME) {
            $cacheKey = Cache::PC_RESUME_WX_QR_CODE_BIND_INFO_KEY;
        } else {
            $cacheKey = Cache::PC_COMPANY_WX_QR_CODE_BIND_INFO_KEY;
        }
        $key  = $cacheKey . ':' . $ticket;
        $json = Cache::get($key);
        if ($json) {
            $data = json_decode($json, true);

            if ($data['token'] != $token) {
                throw new \Exception('非法扫码');
            }

            if ($this->type == self::TYPE_RESUME) {
                //求职者
                if ($data['status'] == self::BIND_QRCODE_STATUS_SCAN_WAIT_BIND) {
                    //暂时没有逻辑处理
                }
                if ($data['status'] == self::BIND_QRCODE_STATUS_SCAN_BIND) {
                    if ($data['bindUnionId']) {
                        $data['tips'] = '该手机号已绑定其他微信，请先解绑。';
                        Cache::delete($key);
                    }

                    if ($data['bindResumeId']) {
                        //获取绑定的其他账号的手机号
                        $memberId   = BaseResume::findOneVal(['id' => $data['bindResumeId']], 'member_id');
                        $mobile     = BaseMember::findOneVal(['id' => $memberId], 'mobile');
                        $mobileMask = MaskHelper::getPhone($mobile);

                        $data['tips'] = '该微信已绑定其他账号：' . $mobileMask . '，请先操作解绑，或者更换微信继续操作。';
                        Cache::delete($key);
                    }
                }
            } else {
                //单位
                if ($data['status'] == self::BIND_QRCODE_STATUS_SCAN_WAIT_BIND) {
                    //暂时没有逻辑处理
                }
                if ($data['status'] == self::BIND_QRCODE_STATUS_SCAN_BIND) {
                    $memberId = $data['bindMemberId'];
                }
            }

            return $data;
        } else {
            return [
                'status' => self::BIND_QRCODE_STATUS_OVERDUE,
                'tips'   => '二维码过期',
            ];
        }
    }

    /**
     * 解析key,返回一些譬如绑定的id等等数据
     * @param $key
     * @param $openid
     * @param $ticket
     * @return string
     */
    public function parseQrKey($key, $openid, $ticket, $event = '')
    {
        // 首先把微信本来的key qrscene_给去掉
        $key = str_replace('qrscene_', '', $key);
        //这里我们对关键字进行检查，如果是求职者的关键字，就进行求职者的处理，如果是单位的关键字，就进行单位的处理
        if (strpos($key, self::QR_CODE_RESUME_BIND_KEY) !== false || strpos($key,
                self::QR_CODE_RESUME_LOGIN_KEY) !== false) {
            $user        = ResumeWxBind::findOne(['openid' => $openid]);
            $unionIdInfo = $this->getUserInfo($openid);

            //=====================求职者处理逻辑开始===================================================
            //=======================================================================================
            // 登录(有可能用户还没有注册,也有可能已经注册了,所以要给前端返回一个东西让其知道)
            if (strpos($key, self::QR_CODE_RESUME_LOGIN_KEY) !== false) {
                // 这里是登录的二维码,需要把这个二维码的状态改成已经绑定
                $cacheKey = Cache::PC_RESUME_WX_QR_CODE_LOGIN_INFO_KEY;
                $key      = $cacheKey . ':' . $ticket;
                $json     = Cache::get($key);
                if ($json) {
                    $data = json_decode($json, true);
                }

                if ($data['status'] == self::LOGIN_QRCODE_STATUS_WAIT) {
                    if ($user->resume_id) {
                        // 表示已经绑定了,直接登录成功
                        $data['status']   = self::LOGIN_QRCODE_STATUS_BIND;
                        $data['memberId'] = BaseResume::findOneVal(['id' => $user->resume_id], 'member_id');
                        $data['resumeId'] = $user->resume_id;
                    } else {
                        // 表示还没有绑定,需要绑定
                        $data['status'] = self::LOGIN_QRCODE_STATUS_WAIT_BIND;
                        $data['openid'] = $openid;
                    }

                    Cache::set($key, json_encode($data), self::QR_SCENE_TEMP_TIME);

                    return '扫码成功';
                } else {
                    return '二维码过期';
                }
            }
            // 这里的key有很多情况,所以需要根据情况来解析出合适的数据
            if (strpos($key, self::QR_CODE_RESUME_BIND_KEY) !== false) {
                // bind_169 , qrscene_bind_169
                $resumeId = str_replace(self::QR_CODE_RESUME_BIND_KEY . '_', '', $key);

                $cacheKey = Cache::PC_RESUME_WX_QR_CODE_BIND_INFO_KEY;
                $key      = $cacheKey . ':' . $ticket;
                $json     = Cache::get($key);

                if ($json) {
                    $data = json_decode($json, true);
                } else {
                    return '二维码过期';
                }
                // 绑定用户
                if ($data['status'] == self::BIND_QRCODE_STATUS_WAIT) {
                    //  之前是还没扫成功的,现在扫成功了
                    if ($user->resume_id) {
                        if ($user->resume_id != $resumeId) {
                            $data['bindResumeId'] = $user->resume_id;
                            $txt                  = '已经绑定过了';
                            $data['status']       = self::BIND_QRCODE_STATUS_SCAN_BIND;
                        } else {
                            $data['status'] = self::BIND_QRCODE_STATUS_SCAN_WAIT_BIND;
                        }
                    } elseif ($user->unionid != $unionIdInfo['unionid']) {
                        //unionid匹配不上
                        $data['status']      = self::BIND_QRCODE_STATUS_SCAN_BIND;
                        $data['bindUnionId'] = $user->unionid;
                        $txt                 = '已经绑定过了';
                    } else {
                        $data['status'] = self::BIND_QRCODE_STATUS_SCAN_WAIT_BIND;
                        // 表示还没有绑定,需要绑定
                        $user->resume_id = $resumeId;
                        $user->save();
                        $txt = '绑定成功';
                    }

                    Cache::set($key, json_encode($data), self::QR_SCENE_TEMP_TIME);

                    return $txt;
                }
            }
            //=======================================================================================
            //=====================求职者处理逻辑结束===================================================

        } elseif (strpos($key, self::QR_CODE_COMPANY_BIND_KEY) !== false || strpos($key,
                self::QR_CODE_COMPANY_LOGIN_KEY) !== false) {
            $user = BaseCompanyMemberWxBind::findOne(['openid' => $openid]);
            //扫码登录
            if (strpos($key, self::QR_CODE_COMPANY_LOGIN_KEY) !== false) {
                $cacheKey = Cache::PC_COMPANY_WX_QR_CODE_LOGIN_INFO_KEY;
                $key      = $cacheKey . ':' . $ticket;
                $json     = Cache::get($key);
                if ($json) {
                    $data = json_decode($json, true);
                    if ($data['status'] == self::LOGIN_QRCODE_STATUS_WAIT) {//扫了码要更新状态了
                        if ($user->company_member_id && $user->company_id) {
                            // 表示已经绑定了,直接登录成功
                            $data['status']    = self::LOGIN_QRCODE_STATUS_BIND;
                            $data['memberId']  = $user->company_member_id;
                            $data['companyId'] = $user->company_id;;

                            // 登录成功由消息中心去出消息,这里不做文案处理
                            $txt = '';
                        } else {
                            // 表示还没有绑定,需要绑定
                            $data['status'] = self::LOGIN_QRCODE_STATUS_WAIT_BIND;
                            $data['openid'] = $openid;
                            $txt            = '您好，您的账号尚未绑定高校人才网单位账号，请先在高校人才网电脑端操作绑定。绑定成功，可使用微信扫码快捷登录单位后台、实时接收简历投递消息通知';
                        }
                        //更新登录缓存与状态让check接口知道
                        Cache::set($key, json_encode($data), self::QR_SCENE_TEMP_TIME);

                        return $txt;
                    } else {
                        return '二维码过期';
                    }
                } else {
                    return '二维码过期';
                }
            }

            //扫码绑定
            if (strpos($key, self::QR_CODE_COMPANY_BIND_KEY) !== false) {
                // bind_169 , qrscene_bind_169
                $memberId = str_replace(self::QR_CODE_COMPANY_BIND_KEY . '_', '', $key);
                $cacheKey = Cache::PC_COMPANY_WX_QR_CODE_BIND_INFO_KEY;
                $key      = $cacheKey . ':' . $ticket;
                $json     = Cache::get($key);
                if ($json) {
                    $data = json_decode($json, true);

                    if ($data['status'] == self::BIND_QRCODE_STATUS_WAIT) {
                        //  之前是还没扫成功的,现在扫成功了
                        if ($user->company_member_id && $user->company_id) {
                            // 这里有一个比较特殊的用例,就是之前就绑定了同样的微信
                            // if ($memberId == $user->company_member_id) {
                            //     $data['status'] = self::BIND_QRCODE_STATUS_SCAN_WAIT_BIND;
                            // } else {
                            //     $data['status']       = self::BIND_QRCODE_STATUS_SCAN_BIND;
                            //     $data['bindMemberId'] = $user->company_member_id;
                            //     $data['tips']         = '该微信已绑定其他账号，请先操作解绑，或者更换微信继续操作。';
                            //     $txt                  = '';
                            // }
                            $data['status']       = self::BIND_QRCODE_STATUS_SCAN_BIND;
                            $data['bindMemberId'] = $user->company_member_id;
                            $data['tips']         = '该微信已绑定其他账号，请先操作解绑，或者更换微信继续操作。';
                            $txt                  = '';
                        } else {
                            $data['status'] = self::BIND_QRCODE_STATUS_SCAN_WAIT_BIND;
                            // 表示还没有绑定,需要绑定
                            $user->company_member_id = $memberId;
                            $user->company_id        = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId],
                                'company_id');
                            $res                     = $user->save();
                            //绑定成功初始化消息配置
                            if ($res) {
                                BaseCompanyMemberInfo::confirmBind($memberId);
                                BaseCompanyMemberMessageConfig::initBindConfig($memberId);
                            }
                            $txt = '绑定成功！您可扫码快捷登录高校人才网单位端、实时接收简历投递消息通知';
                        }
                        Cache::set($key, json_encode($data), self::QR_SCENE_TEMP_TIME);

                        return $txt;
                    }
                } else {
                    return '二维码过期';
                }
            }
        } elseif ($key == self::QR_CODE_RESUME_BUY_GUIDE_KEY) {
            //用户扫描引导购买二维码，触发关注消息后推送
            $config = \Yii::$app->params['wx']['personPublic'];

            $app = Factory::officialAccount($config);
            if ($event == 'subscribe') {
                //如果是订阅的，推送多一条消息
                //获取关注推送消息
                $subscribeText    = self::getSubscribeText();
                $messageSubscribe = new Text($subscribeText);

                $app->customer_service->message($messageSubscribe)
                    ->to($openid)
                    ->send();
            }

            //获取引导购买消息
            $guideBuyCodeText = self::getGuideBuyCodeText();

            $messageGuideBuyCode = new Text($guideBuyCodeText);

            $app->customer_service->message($messageGuideBuyCode)
                ->to($openid)
                ->send();

            return '';
        }
    }

    /**
     * 获取订阅推送消息
     * @return string
     */
    public static function getSubscribeText()
    {
        $txt = "英才您好，欢迎加入【高校人才网】，开启职场新篇章！" . PHP_EOL;
        $txt .= PHP_EOL;
        $txt .= "🎉升级钻石VIP：尊享投递置顶、编制查询、竞争力分析等11项特权，全方位助力你高效求职！<a href='https://m.gaoxiaojob.com/vip.html'>立即体验</a>" . PHP_EOL;
        $txt .= "🎉高校求职服务：尊享岗位筛选、简历代投等6大权益！<a href='http://t.jugaocai.com/9GMZeP'>点击了解</a>" . PHP_EOL;
        $txt .= PHP_EOL;
        $txt .= "同步关注公众号#高才-高校人才网，每天都能获取全国高校、科研及医疗机构等单位的人才需求信息!";

        return $txt;
    }

    /**
     * 获取引导购买消息内容
     * @return string
     */
    public static function getGuideBuyCodeText()
    {
        $txt = "求职实用工具，助您赢在起跑线！点击了解详情——" . PHP_EOL;
        $txt .= "👉<a href='https://m.gaoxiaojob.com/vip.html'>高才VIP</a>，畅享11大求职权益" . PHP_EOL;
        $txt .= "👉<a href='https://m.gaoxiaojob.com/job-fast.html'>求职快</a>，简历曝光翻倍提升" . PHP_EOL;
        $txt .= "👉<a href='https://m.gaoxiaojob.com/competitive-power.html'>竞争力洞察</a>，知己知彼更清晰" . PHP_EOL;

        return $txt;
    }

    /**
     * 获取绑定账号内容
     * @return string
     */
    public static function getBindWxText()
    {
        $appid = Yii::$app->params['wx']['personMiniApp']['app_id'];

        $txt = "您尚未绑定账号" . PHP_EOL;
        $txt .= "绑定后可以在微信中实时接收投递反馈等重要信息" . PHP_EOL;
        $txt .= "👉<a data-miniprogram-appid='$appid' data-miniprogram-path='packages/auth/index' href='https://www.gaoxiaojob.com/member/person/setting'>点击绑定账号</a>" . PHP_EOL;

        return $txt;
    }

    public function sendWxMaterial($key)
    {
        $config = $this->config;
        $media  = $config['material'][$key];
        $type   = $media['type'];
        $id     = $media['id'];

        switch ($type) {
            case 'image':
                return new Image($id);

            default:
                break;
        }

        return $key;
    }

    /**
     * 解析key,返回一些譬如绑定的id等等数据
     * @param $key
     * @param $user ResumeWxBind
     * @return string
     */
    // public function parseQrCodeKey($key)
    // {
    //     // 首先把微信本来的key qrscene_给去掉
    //     $key = str_replace('qrscene_', '', $key);
    //
    //     // 这里的key有很多情况,所以需要根据情况来解析出合适的数据
    //     if (strpos($key, self::QR_CODE_RESUME_BIND_KEY) !== false) {
    //         // bind_169 , qrscene_bind_169
    //         $id = str_replace(self::QR_CODE_RESUME_BIND_KEY . '_', '', $key);
    //
    //         return $id;
    //     }
    // }

    public function sendTemplateMessage($openId, $templateId, $data, $url = '', $miniPagePath = '')
    {
        if ($miniPagePath) {
            return self::$app->template_message->send([
                'touser'      => $openId,
                'template_id' => $templateId,
                'miniprogram' => [
                    'appid'    => Yii::$app->params['wx']['personMiniApp']['app_id'],
                    'pagepath' => $miniPagePath,
                ],
                'data'        => $data,
            ]);
        }

        return self::$app->template_message->send([
            'touser'      => $openId,
            'template_id' => $templateId,
            'url'         => $url,
            'data'        => $data,
        ]);
    }

    public function createMenu($menus)
    {
        return self::$app->menu->create($menus);
    }

    public function getUserInfo($openId)
    {
        return self::$app->user->get($openId);
    }

    /**
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     */
    public function getPrivateTemplates()
    {
        return self::$app->template_message->getPrivateTemplates();
    }

    /**
     * @param $mainId
     * @return array
     * @throws \yii\base\Exception
     */
    public function cancelWxBind($mainId)
    {
        //根据端口type处理相应逻辑
        if ($this->type == self::TYPE_RESUME) {
            $res = BaseResumeWxBind::cancelWxBind($mainId);
        } else {
            //首先这里是单位的绑定取消-解绑
            //$mainId是memberID
            //先解除绑定关系
            $cancel = BaseCompanyMemberInfo::cancelBind($mainId);
            if ($cancel) {
                //成功-将推送消息参数全部关闭
                $res = BaseCompanyMemberMessageConfig::initUnBindConfig($mainId);
            } else {
                $res = false;
            }
        }
        if ($res) {
            return [
                'status' => 1,
                'tips'   => '解绑成功',
            ];
        } else {
            return [
                'status' => -1,
                'tips'   => '解绑失败',
            ];
        }
    }

    /**
     * 获取微信授权链接
     */
    public function getAuthorizeUrl($redirect = '', $scopes = 'snsapi_userinfo')
    {
        return self::$app->oauth->scopes([$scopes])
            ->redirect($redirect);
    }

    /**
     * 获取微信用户信息
     */
    public function getUserInfoByCode($code)
    {
        $user = self::$app->oauth->userFromCode($code);
        $user = $user->toArray();

        return $user;
    }

    public function createShareConfig()
    {
        $js = self::$app->jssdk->buildConfig([
            'updateAppMessageShareData',
            'updateTimelineShareData',
        ], true);

        return $js;
    }

    public function sendCustomerMessage($openId, $message)
    {
        return self::$app->customer_service->message($message)
            ->to($openId)
            ->send();
    }
}
