<?php
namespace common\libs\CompanyAuthority;

use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyPackageConfig;

class CompanyAuthorityRule extends BaseRule
{
    const IS_HAVE_YES   = 1; //有
    const IS_HAVE_NO    = 2; //无
    const IS_HANDLE_YES = 1; //可操作
    const IS_HANDLE_NO  = 2; //不可操作

    //权限列表
    const COMPANY_AUTHORITY_LIST = [
        //职位管理
        'jobList'                  => [
            BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_SUPER  => [
                [
                    'button'    => 'edit',
                    'Chinese'   => '编辑',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'redistribution',
                    'Chinese'   => '再发布',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'copy',
                    'Chinese'   => '复制',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'look',
                    'Chinese'   => '查看（预览）',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'refresh',
                    'Chinese'   => '刷新',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'offline',
                    'Chinese'   => '下线',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'contactSynergy',
                    'Chinese'   => '协同者账号筛选项',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'company',
                    'Chinese'   => '职位联系人筛选项',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
            ],
            BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_VIP    => [
                [
                    'button'    => 'edit',
                    'Chinese'   => '编辑',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'redistribution',
                    'Chinese'   => '再发布',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'copy',
                    'Chinese'   => '复制',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'look',
                    'Chinese'   => '查看（预览）',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'refresh',
                    'Chinese'   => '刷新',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'offline',
                    'Chinese'   => '下线',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'contactSynergy',
                    'Chinese'   => '协同者账号筛选项',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'company',
                    'Chinese'   => '职位联系人筛选项',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
            ],
            BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_NORMAL => [
                [
                    'button'    => 'edit',
                    'Chinese'   => '编辑',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'redistribution',
                    'Chinese'   => '再发布',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'copy',
                    'Chinese'   => '复制',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'look',
                    'Chinese'   => '查看（预览）',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'refresh',
                    'Chinese'   => '刷新',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'offline',
                    'Chinese'   => '下线',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'contactSynergy',
                    'Chinese'   => '协同者账号筛选项',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'company',
                    'Chinese'   => '职位联系人筛选项',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
            ],
        ],

        //公告管理
        'announcementList'         => [
            BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_SUPER  => [
                [
                    'button'    => 'edit',
                    'Chinese'   => '编辑',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'redistribution',
                    'Chinese'   => '再发布',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'sort',
                    'Chinese'   => '排序',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'look',
                    'Chinese'   => '查看（预览）',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'refresh',
                    'Chinese'   => '刷新',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'offline',
                    'Chinese'   => '下线',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'contactSynergy',
                    'Chinese'   => '协同者账号筛选项',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
            ],
            BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_VIP    => [
                [
                    'button'    => 'edit',
                    'Chinese'   => '编辑',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'redistribution',
                    'Chinese'   => '再发布',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'copy',
                    'Chinese'   => '复制',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'look',
                    'Chinese'   => '查看（预览）',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'refresh',
                    'Chinese'   => '刷新',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'offline',
                    'Chinese'   => '下线',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'contactSynergy',
                    'Chinese'   => '协同者账号筛选项',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
            ],
            BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_NORMAL => [
                [
                    'button'    => 'edit',
                    'Chinese'   => '编辑',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'redistribution',
                    'Chinese'   => '再发布',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'copy',
                    'Chinese'   => '复制',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'look',
                    'Chinese'   => '查看（预览）',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'refresh',
                    'Chinese'   => '刷新',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'offline',
                    'Chinese'   => '下线',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'contactSynergy',
                    'Chinese'   => '协同者账号筛选项',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
            ],
        ],

        //平台应聘/简历列表/其他应聘
        'resumeList'               => [
            BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_SUPER  => [
                [
                    'button'    => 'look',
                    'Chinese'   => '查看',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'share',
                    'Chinese'   => '分享',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'state',
                    'Chinese'   => '状态切分',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'batchDownload',
                    'Chinese'   => '批量下载',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'contactSynergy',
                    'Chinese'   => '协同者账号筛选项',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'company',
                    'Chinese'   => '职位联系人筛选项',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
            ],
            BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_VIP    => [
                [
                    'button'    => 'look',
                    'Chinese'   => '查看',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'share',
                    'Chinese'   => '分享',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'state',
                    'Chinese'   => '状态切分',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'batchDownload',
                    'Chinese'   => '批量下载',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'contactSynergy',
                    'Chinese'   => '协同者账号筛选项',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'company',
                    'Chinese'   => '职位联系人筛选项',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
            ],
            BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_NORMAL => [
                [
                    'button'    => 'look',
                    'Chinese'   => '查看',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'share',
                    'Chinese'   => '分享',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'state',
                    'Chinese'   => '状态切分',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'batchDownload',
                    'Chinese'   => '批量下载',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'contactSynergy',
                    'Chinese'   => '协同者账号筛选项',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
                [
                    'button'    => 'company',
                    'Chinese'   => '职位联系人筛选项',
                    'is_have'   => self::IS_HAVE_NO,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
            ],
        ],

        //单位简历库
        'companyResumeLibraryList' => [
            BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_SUPER  => [
                //高级会员
                BaseCompanyPackageConfig::COMPANY_ROLE_SENIOR => [
                    [
                        'button'    => 'look',
                        'Chinese'   => '查看',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                    [
                        'button'    => 'share',
                        'Chinese'   => '分享',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                    [
                        'button'    => 'download',
                        'Chinese'   => '下载',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                    [
                        'button'    => 'collect',
                        'Chinese'   => '收藏',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                    [
                        'button'    => 'invitation',
                        'Chinese'   => '邀约投递',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                ],
                //非高级会员
                BaseCompanyPackageConfig::COMPANY_ROLE_FREE   => [
                    [
                        'button'    => 'look',
                        'Chinese'   => '查看',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                    [
                        'button'    => 'share',
                        'Chinese'   => '分享',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                    [
                        'button'    => 'download',
                        'Chinese'   => '下载',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                    [
                        'button'    => 'collect',
                        'Chinese'   => '收藏',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                    [
                        'button'    => 'invitation',
                        'Chinese'   => '邀约投递',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                ],
                //非高级会员
                BaseCompanyPackageConfig::COMPANY_ROLE_EXPIRE => [
                    [
                        'button'    => 'look',
                        'Chinese'   => '查看',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                    [
                        'button'    => 'share',
                        'Chinese'   => '分享',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                    [
                        'button'    => 'download',
                        'Chinese'   => '下载',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                    [
                        'button'    => 'collect',
                        'Chinese'   => '收藏',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                    [
                        'button'    => 'invitation',
                        'Chinese'   => '邀约投递',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                ],
            ],
            BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_VIP    => [
                [
                    'button'    => 'look',
                    'Chinese'   => '查看',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'share',
                    'Chinese'   => '分享',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'download',
                    'Chinese'   => '下载',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'collect',
                    'Chinese'   => '收藏',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'invitation',
                    'Chinese'   => '邀约投递',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
            ],
            BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_NORMAL => [
                [
                    'button'    => 'look',
                    'Chinese'   => '查看',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'share',
                    'Chinese'   => '分享',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'download',
                    'Chinese'   => '下载',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'collect',
                    'Chinese'   => '收藏',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_YES,
                ],
                [
                    'button'    => 'invitation',
                    'Chinese'   => '邀约投递',
                    'is_have'   => self::IS_HAVE_YES,
                    'is_handle' => self::IS_HANDLE_NO,
                ],
            ],
        ],

        //找人才搜索人才/找人才我的收藏/找人才邀约记录
        'findTalentList'           => [
            BaseCompanyPackageConfig::COMPANY_ROLE_SENIOR => [
                BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_SUPER  => [
                    [
                        'button'    => 'look',
                        'Chinese'   => '查看',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                    [
                        'button'    => 'share',
                        'Chinese'   => '分享',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                    [
                        'button'    => 'collect',
                        'Chinese'   => '收藏',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                    [
                        'button'    => 'invitation',
                        'Chinese'   => '邀约',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                    [
                        'button'    => 'download',
                        'Chinese'   => '下载',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                ],
                BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_VIP    => [
                    [
                        'button'    => 'look',
                        'Chinese'   => '查看',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                    [
                        'button'    => 'share',
                        'Chinese'   => '分享',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                    [
                        'button'    => 'collect',
                        'Chinese'   => '收藏',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                    [
                        'button'    => 'invitation',
                        'Chinese'   => '邀约',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                    [
                        'button'    => 'download',
                        'Chinese'   => '下载',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                ],
                BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_NORMAL => [
                    [
                        'button'    => 'look',
                        'Chinese'   => '查看',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'share',
                        'Chinese'   => '分享',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'collect',
                        'Chinese'   => '收藏',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'invitation',
                        'Chinese'   => '邀约',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'download',
                        'Chinese'   => '下载',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                ],
            ],

            BaseCompanyPackageConfig::COMPANY_ROLE_FREE => [
                BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_SUPER  => [
                    [
                        'button'    => 'look',
                        'Chinese'   => '查看',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'share',
                        'Chinese'   => '分享',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'collect',
                        'Chinese'   => '收藏',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'invitation',
                        'Chinese'   => '邀约',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'download',
                        'Chinese'   => '下载',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                ],
                BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_VIP    => [
                    [
                        'button'    => 'look',
                        'Chinese'   => '查看',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_YES,
                    ],
                    [
                        'button'    => 'share',
                        'Chinese'   => '分享',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'collect',
                        'Chinese'   => '收藏',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'invitation',
                        'Chinese'   => '邀约',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'download',
                        'Chinese'   => '下载',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                ],
                BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_NORMAL => [
                    [
                        'button'    => 'look',
                        'Chinese'   => '查看',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'share',
                        'Chinese'   => '分享',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'collect',
                        'Chinese'   => '收藏',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'invitation',
                        'Chinese'   => '邀约',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'download',
                        'Chinese'   => '下载',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                ],
            ],

            BaseCompanyPackageConfig::COMPANY_ROLE_EXPIRE => [
                BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_SUPER  => [
                    [
                        'button'    => 'look',
                        'Chinese'   => '查看',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'share',
                        'Chinese'   => '分享',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'collect',
                        'Chinese'   => '收藏',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'invitation',
                        'Chinese'   => '邀约',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'download',
                        'Chinese'   => '下载',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                ],
                BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_VIP    => [
                    [
                        'button'    => 'look',
                        'Chinese'   => '查看',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'share',
                        'Chinese'   => '分享',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'collect',
                        'Chinese'   => '收藏',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'invitation',
                        'Chinese'   => '邀约',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'download',
                        'Chinese'   => '下载',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                ],
                BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_NORMAL => [
                    [
                        'button'    => 'look',
                        'Chinese'   => '查看',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'share',
                        'Chinese'   => '分享',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'collect',
                        'Chinese'   => '收藏',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'invitation',
                        'Chinese'   => '邀约',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                    [
                        'button'    => 'download',
                        'Chinese'   => '下载',
                        'is_have'   => self::IS_HAVE_YES,
                        'is_handle' => self::IS_HANDLE_NO,
                    ],
                ],
            ],
        ],
    ];
}