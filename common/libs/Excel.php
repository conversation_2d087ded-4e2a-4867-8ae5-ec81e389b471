<?php
namespace common\libs;

use common\helpers\ArrayHelper;
use Vtiful\Kernel\Excel as E;
use Vtiful\Kernel\Format;
use Yii;
use yii\base\Exception;

/**
 * 这里是使用一个内存占用比较小的excel导出工具
 * https://xlswriter-docs.viest.me/zh-cn/an-zhuang
 */
class Excel
{

    private $excelModel;
    private $baseDirPath;
    public  $saveFile;
    public  $savePath;

    // 最终包含文件名的路径
    // public $savePathFile;

    public function __construct()
    {
        if (!is_dir('uploads/')) {
            @mkdir('uploads/');
        }

        if (!is_dir('uploads/excel/')) {
            @mkdir('uploads/excel/');
        }

        // 主要是检查一下文件夹是否存在,没有存在就创建一下
        $dirPath           = 'uploads/excel/' . date('Ymd');
        $this->baseDirPath = $dirPath;
        $realPath          = Yii::getAlias('@admin') . '/web/' . $dirPath;

        // $dirPath = Yii::getAlias('@adminUploads') . '/excel/' . date('Ymd');

        if (!is_dir($realPath)) {
            @mkdir($realPath);
        }

        $this->savePath = $realPath;

        $config = [
            'path' => $realPath,
        ];

        $this->excelModel = new E($config);
    }

    /**
     * 默认的导出,居中,有边框
     * @param       $data
     * @param       $fileName
     * @param array $header
     * @return string
     */
    public function export($data, $header = [], $fileName = '')
    {
        if (!$fileName) {
            $fileName = CUR_TIMESTAMP;
        }
        $filePath   = $this->excelModel->fileName($fileName . '.xlsx');
        $fileHandle = $filePath->getHandle();
        $format     = new Format($fileHandle);
        $style      = $format->align(Format::FORMAT_ALIGN_CENTER, Format::FORMAT_ALIGN_VERTICAL_CENTER)
            ->border(Format::BORDER_THIN)
            ->toResource();
        $rs         = $this->excelModel->defaultFormat($style);
        if ($header) {
            $rs->header($header);
        }

        $rs->data($data)
            ->output();

        // 上传到七牛云
        // $qiniu = new Qiniu('file');
        // $qiniu->upload($file->tempName, $path);

        // $this->savePathFile = $this->savePath . '/' . $fileName . '.xlsx';

        return 'https://' . Yii::$app->params['adminHost'] . '/' . $this->baseDirPath . '/' . $fileName . '.xlsx';
    }

    /**
     * 读取Excel,批量导入数据
     * @param string $filePath
     * @param array  $fieldArr
     * @param array  $column
     * @param array  $columnType
     * @return array $excelData
     */
    public function import($filePath, $fieldArr = [], $column = 10, $columnType = [])
    {
        $file_array = explode('/', $filePath);
        $fileName   = array_pop($file_array); // 文件名称

        if (!in_array('xlsx', explode('.', $fileName))) {
            throw new Exception('仅支持xlsx文件导入');
        }

        $path   = implode('/', $file_array); // 文件路径
        $config = ['path' => $path];
        $excel  = new E($config);

        $columnList = [];
        for ($i = 0; $i < $column; $i++) {
            if (isset($columnType[$i]) && strlen($columnType[$i]) > 0) {
                $columnList[] = $columnType[$i];
            } else {
                $columnList[] = \Vtiful\Kernel\Excel::TYPE_STRING;
            }
        }
        // 读取测试文件
        $data_excel = $excel->openFile($fileName)
            ->setType($columnList)
            ->openSheet();
        $data       = $data_excel->getSheetData(); // 获取表格内数据

        $fields = array_shift($data); // 首行标题title

        $field_array = [
            $fields,
            array_filter($fieldArr),
        ];

        array_walk($data, function (&$value, $key, $field_array) {
            if (array_filter($value)) {
                $row  = [];
                $temp = array_combine($field_array[0], $value);
                foreach ($temp as $k => $v) {
                    if (isset($field_array[1][$k]) && $k !== '') {
                        $row[$field_array[1][$k]] = $v;
                    }
                }
                $row && $value = $row;
            } else {
                $value = '';
            }
        }, $field_array);
        $excelData = array_filter($data);

        return $excelData;
    }

    public function multipleSheet($array)
    {
        $fileName   = CUR_TIMESTAMP . '.xlsx';
        $filePath   = $this->excelModel->fileName($fileName, $array[0]['sheetName'] ?? '');
        $fileHandle = $filePath->getHandle();
        $format     = new Format($fileHandle);
        $style      = $format->align(Format::FORMAT_ALIGN_CENTER, Format::FORMAT_ALIGN_VERTICAL_CENTER)
            ->border(Format::BORDER_THIN)
            ->toResource();
        $rs         = $this->excelModel->defaultFormat($style);
        foreach ($array as $key => $value) {
            if ($key == 0) {
                $rs->header($value['headers'])
                    ->data($value['data'])
                    ->setColumn('A:AZ', '20');
            } else {
                $rs->addSheet($value['sheetName'] ?? '')
                    ->header($value['headers'])
                    ->data($value['data'])
                    ->setColumn('A:AZ', '20');
            }
        }

        $rs->output();

        $this->saveFile = $this->savePath . '/' . $fileName;

        return 'http://' . Yii::$app->params['adminHost'] . '/' . $this->baseDirPath . '/' . CUR_TIMESTAMP . '.xlsx';
    }
}