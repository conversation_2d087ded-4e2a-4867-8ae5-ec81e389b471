<?php

namespace common\libs;

use common\components\MessageException;
use common\helpers\TimeHelper;

/**
 * 需求来源
 * http://zentao.jugaocai.com/index.php?m=story&f=view&storyID=286&version=0&param=0&storyType=story&t=html&m=story&f=view&storyID=286&version=0&param=0&storyType=story&tid=la7ia054
 *
 * 参考文档 https://ziyuan.baidu.com/college/articleinfo?id=2210
 *
 *
 * <script type="application/ld+json">
 * {
 * "@context": "https://ziyuan.baidu.com/contexts/cambrian.jsonld",
 * "@id": "http://www.gaoxiaojob.com/news/detail/1284.html",
 * "pubDate": "2022-11-15T12:16:58",
 * "upDate": "2022-11-22T20:10:16"
 * }
 * </script>
 */
class BaiduTimeFactor
{

    const CONTEXT = "https://ziyuan.baidu.com/contexts/cambrian.jsonld";

    // 其实这里只接受信息生成js,然后方便前端使用
    public static function create($pubDate = '', $upDate = '')
    {
        $context = self::CONTEXT;
        $id      = \Yii::$app->request->absoluteUrl;

        // 初始化 JSON-LD 数组
        $jsonLd = [
            '@context' => $context,
            '@id'      => $id,
        ];

        // 如果提供了 pubDate，则添加到 JSON-LD 数组中
        if (!empty($pubDate)) {
            $pubDate           = date('Y-m-d\TH:i:s', strtotime($pubDate));
            $jsonLd['pubDate'] = $pubDate; // 只有当 pubDate 存在时才添加
        }

        // 如果提供了 upDate，则添加到 JSON-LD 数组中
        if (!empty($upDate) && $upDate != TimeHelper::ZERO_TIME) {
            $upDate           = date('Y-m-d\TH:i:s', strtotime($upDate));
            $jsonLd['upDate'] = $upDate; // 只有当 upDate 存在时才添加
        }

        // 将 JSON-LD 数组转换为 JSON 字符串
        $jsonLdString = json_encode($jsonLd, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);

        // 直接输出到前端使用的 js
        $js = <<<JS
<script type="application/ld+json">
{$jsonLdString}
</script>
JS;

        // 塞到 yii 全局变量里面去
        \Yii::$app->params['baiduTimeFactor'] = $js;

        return $js;
    }

    /**
     * 创建时间因子，如果在$hour前就取前一天，否则取当天
     * @param $hour 1-24
     * @return string
     * @throws MessageException
     */
    public static function createMetaTagWithHour($hour)
    {
        $context = self::CONTEXT;
        $id      = \Yii::$app->request->absoluteUrl;

        // 验证输入是否在 1 - 24 范围内
        if ($hour < 1 || $hour > 24) {
            throw new MessageException('输入的小时数必须在 1 到 24 之间。');
        }
        // 将传入的小时数转换为 HH:00:00 格式
        $timePoint = sprintf('%02d:00:00', $hour);
        $nowTime   = date('H:i:s');

        if ($nowTime < $timePoint) {
            // 如果当前时间小于传入的小时数，则将发布时间设置为前一天
            $pubDate = date('Y-m-d\T' . $timePoint, strtotime('-1 day'));
        } else {
            // 否则，将发布时间设置为当天
            $pubDate = date('Y-m-d\T' . $timePoint);
        }

        $js = <<<JS
<script type="application/ld+json">
{
"@context": "{$context}",
"@id": "{$id}",
"upDate": "{$pubDate}"
}
</script>
JS;

        // 塞到yii全局变量里面去
        \Yii::$app->params['baiduTimeFactor'] = $js;

        return $js;
    }

    // 专门用于给只为b页面使用(时间比较特别,要是在08:00前就显示前一天+08:00,要是在08:00后就显示今天+08:00
    public static function createRczhaopin()
    {
        return self::createMetaTagWithHour(8);
    }

    // 专门用于给只为Hotword页面使用(时间比较特别,要是在08:00前就显示前一天+08:00,要是在08:00后就显示今天+08:00
    public static function createHotword()
    {
        return self::createMetaTagWithHour(8);
    }
}
