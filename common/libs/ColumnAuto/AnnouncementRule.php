<?php
namespace common\libs\ColumnAuto;

use common\base\models\BaseArticleAttribute;
use common\base\models\BaseHomeColumn;

class AnnouncementRule extends BaseRule
{

    // 只要职位里面有对应的学科就会去到对应的栏目页
    const MAJOR_RULE = [
        '1'   => [
            'name'       => '哲学',
            'columnId'   => '233',
            'columnName' => '人文社科',
        ],
        '2'   => [
            'name'       => '经济学',
            'columnId'   => '233',
            'columnName' => '人文社科',
        ],
        '3'   => [
            'name'       => '法学',
            'columnId'   => '233',
            'columnName' => '人文社科',
        ],
        '4'   => [
            'name'       => '教育学',
            'columnId'   => '233',
            'columnName' => '人文社科',
        ],
        '5'   => [
            'name'       => '文学',
            'columnId'   => '233',
            'columnName' => '人文社科',
        ],
        '6'   => [
            'name'       => '历史学',
            'columnId'   => '233',
            'columnName' => '人文社科',
        ],
        '7'   => [
            'name'       => '理学',
            'columnId'   => '234',
            'columnName' => '理工农医',
        ],
        '8'   => [
            'name'       => '工学',
            'columnId'   => '234',
            'columnName' => '理工农医',
        ],
        '9'   => [
            'name'       => '农学',
            'columnId'   => '234',
            'columnName' => '理工农医',
        ],
        '10'  => [
            'name'              => '医学',
            'columnId'          => '234',
            'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
            'columnName'        => '理工农医',
        ],
        '12'  => [
            'name'       => '管理学',
            'columnId'   => '233',
            'columnName' => '人文社科',
        ],
        '13'  => [
            'name'       => '艺术学',
            'columnId'   => '233',
            'columnName' => '人文社科',
        ],
        '16'  => [
            'name'       => '哲学',
            'columnId'   => '119',
            'columnName' => '哲学',
        ],
        '17'  => [
            'name'       => '理论经济学',
            'columnId'   => '120',
            'columnName' => '理论经济学',
        ],
        '18'  => [
            'name'       => '应用经济学',
            'columnId'   => '121',
            'columnName' => '应用经济学',
        ],
        '19'  => [
            'name'       => '法学',
            'columnId'   => '122',
            'columnName' => '法学',
        ],
        '20'  => [
            'name'       => '政治学',
            'columnId'   => '123',
            'columnName' => '政治学',
        ],
        '21'  => [
            'name'       => '社会学',
            'columnId'   => '124',
            'columnName' => '社会学',
        ],
        '22'  => [
            'name'       => '民族学',
            'columnId'   => '125',
            'columnName' => '民族学',
        ],
        '23'  => [
            'name'       => '马克思主义理论',
            'columnId'   => '126',
            'columnName' => '马克思主义理论',
        ],
        '24'  => [
            'name'       => '公安学',
            'columnId'   => '127',
            'columnName' => '公安学',
        ],
        '25'  => [
            'name'       => '教育学',
            'columnId'   => '128',
            'columnName' => '教育学',
        ],
        '26'  => [
            'name'       => '心理学',
            'columnId'   => '129',
            'columnName' => '心理学',
        ],
        '27'  => [
            'name'       => '体育学',
            'columnId'   => '130',
            'columnName' => '体育学',
        ],
        '28'  => [
            'name'       => '中国语言文学',
            'columnId'   => '131',
            'columnName' => '中国语言文学',
        ],
        '29'  => [
            'name'       => '外国语言文学',
            'columnId'   => '132',
            'columnName' => '外国语言文学',
        ],
        '30'  => [
            'name'       => '新闻传播学',
            'columnId'   => '133',
            'columnName' => '新闻传播学',
        ],
        '31'  => [
            'name'       => '考古学',
            'columnId'   => '134',
            'columnName' => '考古学',
        ],
        '32'  => [
            'name'       => '中国史',
            'columnId'   => '135',
            'columnName' => '中国史',
        ],
        '33'  => [
            'name'       => '世界史',
            'columnId'   => '136',
            'columnName' => '世界史',
        ],
        '34'  => [
            'name'       => '数学',
            'columnId'   => '137',
            'columnName' => '数学',
        ],
        '35'  => [
            'name'       => '物理学',
            'columnId'   => '138',
            'columnName' => '物理学',
        ],
        '36'  => [
            'name'       => '化学',
            'columnId'   => '139',
            'columnName' => '化学',
        ],
        '37'  => [
            'name'       => '天文学',
            'columnId'   => '140',
            'columnName' => '天文学',
        ],
        '38'  => [
            'name'       => '地理学',
            'columnId'   => '141',
            'columnName' => '地理学',
        ],
        '39'  => [
            'name'       => '大气科学',
            'columnId'   => '142',
            'columnName' => '大气科学',
        ],
        '40'  => [
            'name'       => '海洋科学',
            'columnId'   => '143',
            'columnName' => '海洋科学',
        ],
        '41'  => [
            'name'       => '地球物理学',
            'columnId'   => '144',
            'columnName' => '地球物理学',
        ],
        '42'  => [
            'name'       => '地质学',
            'columnId'   => '145',
            'columnName' => '地质学',
        ],
        '43'  => [
            'name'       => '生物学',
            'columnId'   => '146',
            'columnName' => '生物学',
        ],
        '44'  => [
            'name'       => '系统科学',
            'columnId'   => '147',
            'columnName' => '系统科学',
        ],
        '45'  => [
            'name'       => '科学技术史',
            'columnId'   => '148',
            'columnName' => '科学技术史',
        ],
        '46'  => [
            'name'       => '生态学',
            'columnId'   => '149',
            'columnName' => '生态学',
        ],
        '47'  => [
            'name'       => '统计学',
            'columnId'   => '150',
            'columnName' => '统计学',
        ],
        '48'  => [
            'name'       => '力学',
            'columnId'   => '151',
            'columnName' => '力学',
        ],
        '49'  => [
            'name'       => '机械工程',
            'columnId'   => '152',
            'columnName' => '机械工程',
        ],
        '50'  => [
            'name'       => '光学工程',
            'columnId'   => '153',
            'columnName' => '光学工程',
        ],
        '51'  => [
            'name'       => '仪器科学与技术',
            'columnId'   => '154',
            'columnName' => '仪器科学与技术',
        ],
        '52'  => [
            'name'       => '材料科学与工程',
            'columnId'   => '155',
            'columnName' => '材料科学与工程',
        ],
        '53'  => [
            'name'       => '冶金工程',
            'columnId'   => '156',
            'columnName' => '冶金工程',
        ],
        '54'  => [
            'name'       => '动力工程及工程热物理',
            'columnId'   => '157',
            'columnName' => '动力工程及工程热物理',
        ],
        '55'  => [
            'name'       => '电气工程',
            'columnId'   => '158',
            'columnName' => '电气工程',
        ],
        '56'  => [
            'name'       => '电子科学与技术',
            'columnId'   => '159',
            'columnName' => '电子科学与技术',
        ],
        '57'  => [
            'name'       => '信息与通信工程',
            'columnId'   => '160',
            'columnName' => '信息与通信工程',
        ],
        '58'  => [
            'name'       => '控制科学与工程',
            'columnId'   => '161',
            'columnName' => '控制科学与工程',
        ],
        '59'  => [
            'name'       => '计算机科学与技术',
            'columnId'   => '162',
            'columnName' => '计算机科学与技术',
        ],
        '60'  => [
            'name'       => '建筑学',
            'columnId'   => '163',
            'columnName' => '建筑学',
        ],
        '61'  => [
            'name'       => '土木工程',
            'columnId'   => '164',
            'columnName' => '土木工程',
        ],
        '62'  => [
            'name'       => '水利工程',
            'columnId'   => '165',
            'columnName' => '水利工程',
        ],
        '63'  => [
            'name'       => '测绘科学与技术',
            'columnId'   => '166',
            'columnName' => '测绘科学与技术',
        ],
        '64'  => [
            'name'       => '化学工程与技术',
            'columnId'   => '167',
            'columnName' => '化学工程与技术',
        ],
        '65'  => [
            'name'       => '地质资源与地质工程',
            'columnId'   => '168',
            'columnName' => '地质资源与地质工程',
        ],
        '66'  => [
            'name'       => '矿业工程',
            'columnId'   => '169',
            'columnName' => '矿业工程',
        ],
        '67'  => [
            'name'       => '石油与天然气工程',
            'columnId'   => '170',
            'columnName' => '石油与天然气工程',
        ],
        '68'  => [
            'name'       => '纺织科学与工程',
            'columnId'   => '171',
            'columnName' => '纺织科学与工程',
        ],
        '69'  => [
            'name'       => '轻工技术与工程',
            'columnId'   => '172',
            'columnName' => '轻工技术与工程',
        ],
        '70'  => [
            'name'       => '交通运输工程',
            'columnId'   => '173',
            'columnName' => '交通运输工程',
        ],
        '71'  => [
            'name'       => '船舶与海洋工程',
            'columnId'   => '174',
            'columnName' => '船舶与海洋工程',
        ],
        '72'  => [
            'name'       => '航空宇航科学与技术',
            'columnId'   => '175',
            'columnName' => '航空宇航科学与技术',
        ],
        '73'  => [
            'name'       => '兵器科学与技术',
            'columnId'   => '176',
            'columnName' => '兵器科学与技术',
        ],
        '74'  => [
            'name'       => '核科学与技术',
            'columnId'   => '177',
            'columnName' => '核科学与技术',
        ],
        '75'  => [
            'name'       => '农业工程',
            'columnId'   => '178',
            'columnName' => '农业工程',
        ],
        '76'  => [
            'name'       => '林业工程',
            'columnId'   => '179',
            'columnName' => '林业工程',
        ],
        '77'  => [
            'name'       => '环境科学与工程',
            'columnId'   => '180',
            'columnName' => '环境科学与工程',
        ],
        '78'  => [
            'name'       => '生物医学工程',
            'columnId'   => '181',
            'columnName' => '生物医学工程',
        ],
        '79'  => [
            'name'       => '食品科学与工程',
            'columnId'   => '182',
            'columnName' => '食品科学与工程',
        ],
        '80'  => [
            'name'       => '城乡规划学',
            'columnId'   => '183',
            'columnName' => '城乡规划学',
        ],
        '81'  => [
            'name'       => '风景园林学',
            'columnId'   => '184',
            'columnName' => '风景园林学',
        ],
        '82'  => [
            'name'       => '软件工程',
            'columnId'   => '185',
            'columnName' => '软件工程',
        ],
        '83'  => [
            'name'       => '生物工程',
            'columnId'   => '186',
            'columnName' => '生物工程',
        ],
        '84'  => [
            'name'       => '安全科学与工程',
            'columnId'   => '187',
            'columnName' => '安全科学与工程',
        ],
        '85'  => [
            'name'       => '公安技术',
            'columnId'   => '188',
            'columnName' => '公安技术',
        ],
        '86'  => [
            'name'       => '网络空间安全',
            'columnId'   => '189',
            'columnName' => '网络空间安全',
        ],
        // '87'  => [
        //     'name'       => '电子信息',
        //     'columnId'   => '190',
        //     'columnName' => '电子信息',
        // ],
        '88'  => [
            'name'       => '作物学',
            'columnId'   => '191',
            'columnName' => '作物学',
        ],
        '89'  => [
            'name'       => '园艺学',
            'columnId'   => '192',
            'columnName' => '园艺学',
        ],
        '90'  => [
            'name'       => '农业资源利用',
            'columnId'   => '193',
            'columnName' => '农业资源利用',
        ],
        '91'  => [
            'name'       => '植物保护',
            'columnId'   => '194',
            'columnName' => '植物保护',
        ],
        '92'  => [
            'name'       => '畜牧学',
            'columnId'   => '195',
            'columnName' => '畜牧学',
        ],
        '93'  => [
            'name'              => '兽医学',
            'columnId'          => '196',
            'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
            'columnName'        => '兽医学',
        ],
        '94'  => [
            'name'       => '林学',
            'columnId'   => '197',
            'columnName' => '林学',
        ],
        '95'  => [
            'name'       => '水产',
            'columnId'   => '198',
            'columnName' => '水产',
        ],
        '96'  => [
            'name'       => '草学',
            'columnId'   => '199',
            'columnName' => '草学',
        ],
        '97'  => [
            'name'              => '基础医学',
            'columnId'          => '200',
            'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
            'columnName'        => '基础医学',
        ],
        '98'  => [
            'name'              => '临床医学',
            'columnId'          => '201',
            'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
            'columnName'        => '临床医学',
        ],
        '99'  => [
            'name'              => '口腔医学',
            'columnId'          => '202',
            'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
            'columnName'        => '口腔医学',
        ],
        '100' => [
            'name'              => '公共卫生与预防医学',
            'columnId'          => '203',
            'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
            'columnName'        => '公共卫生与预防医学',
        ],
        '101' => [
            'name'              => '中医学',
            'columnId'          => '204',
            'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
            'columnName'        => '中医学',
        ],
        '102' => [
            'name'       => '中西医结合',
            'columnId'   => '205',
            'columnName' => '中西医结合',
        ],
        '103' => [
            'name'       => '药学',
            'columnId'   => '206',
            'columnName' => '药学',
        ],
        '104' => [
            'name'       => '中药学',
            'columnId'   => '207',
            'columnName' => '中药学',
        ],
        '105' => [
            'name'              => '特种医学',
            'columnId'          => '208',
            'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
            'columnName'        => '特种医学',
        ],
        '106' => [
            'name'       => '医学技术',
            'columnId'   => '209',
            'columnName' => '医学技术',
        ],
        '107' => [
            'name'       => '护理学',
            'columnId'   => '210',
            'columnName' => '护理学',
        ],
        '108' => [
            'name'       => '军事思想及军事历史',
            'columnId'   => '211',
            'columnName' => '军事思想及军事历史',
        ],
        '109' => [
            'name'       => '战略学',
            'columnId'   => '212',
            'columnName' => '战略学',
        ],
        '110' => [
            'name'       => '战役学',
            'columnId'   => '213',
            'columnName' => '战役学',
        ],
        '111' => [
            'name'       => '战术学',
            'columnId'   => '214',
            'columnName' => '战术学',
        ],
        '112' => [
            'name'       => '军队指挥学',
            'columnId'   => '215',
            'columnName' => '军队指挥学',
        ],
        '113' => [
            'name'       => '军事管理学',
            'columnId'   => '216',
            'columnName' => '军事管理学',
        ],
        '114' => [
            'name'       => '军队政治工作学',
            'columnId'   => '217',
            'columnName' => '军队政治工作学',
        ],
        '115' => [
            'name'       => '军事后勤学',
            'columnId'   => '218',
            'columnName' => '军事后勤学',
        ],
        '116' => [
            'name'       => '军事装备学',
            'columnId'   => '219',
            'columnName' => '军事装备学',
        ],
        '117' => [
            'name'       => '军事训练学',
            'columnId'   => '220',
            'columnName' => '军事训练学',
        ],
        '118' => [
            'name'       => '管理科学与工程',
            'columnId'   => '221',
            'columnName' => '管理科学与工程',
        ],
        '119' => [
            'name'       => '工商管理',
            'columnId'   => '222',
            'columnName' => '工商管理',
        ],
        '120' => [
            'name'       => '农林经济管理',
            'columnId'   => '223',
            'columnName' => '农林经济管理',
        ],
        '121' => [
            'name'       => '公共管理',
            'columnId'   => '224',
            'columnName' => '公共管理',
        ],
        '122' => [
            'name'       => '图书馆、情报与档案管理',
            'columnId'   => '225',
            'columnName' => '图书馆、情报与档案管理',
        ],
        '123' => [
            'name'       => '艺术学理论',
            'columnId'   => '226',
            'columnName' => '艺术学理论',
        ],
        '124' => [
            'name'       => '音乐与舞蹈学',
            'columnId'   => '227',
            'columnName' => '音乐与舞蹈学',
        ],
        '125' => [
            'name'       => '戏剧与影视学',
            'columnId'   => '228',
            'columnName' => '戏剧与影视学',
        ],
        '126' => [
            'name'       => '美术学',
            'columnId'   => '229',
            'columnName' => '美术学',
        ],
        '127' => [
            'name'       => '设计学',
            'columnId'   => '230',
            'columnName' => '设计学',
        ],
        '128' => [
            'name'       => '集成电路科学与工程',
            'columnId'   => '231',
            'columnName' => '集成电路科学与工程',
        ],
        '129' => [
            'name'       => '国家安全学',
            'columnId'   => '232',
            'columnName' => '国家安全学',
        ],
        '130' => [
            'name'       => '专业未分类',
            'columnId'   => '236',
            'columnName' => '专业未分类',
        ],
        '712' => [
            'name'       => '理科大类',
            'columnId'   => '532',
            'columnName' => '理科大类',
        ],
        '713' => [
            'name'       => '工科大类',
            'columnId'   => '531',
            'columnName' => '工科大类',
        ],
        '716' => [
            'name'       => '专业不限',
            'columnId'   => '235',
            'columnName' => '专业不限',
        ],
        // 专业数字经济 专业id为 748 栏目id为 533
        // 添加了专业翻译 专业id为 750 栏目id为 534
        // 添加了专业纳米科学与工程 专业id为 774 栏目id为 535
        // 添加了专业智能科学与技术 专业id为 776 栏目id为 536
        // 添加了专业人工智能 专业id为 778 栏目id为 537
        // 添加了专业遥感科学与技术 专业id为 780 栏目id为 538
        // 添加了专业旅游与酒店管理 专业id为 782 栏目id为 539
        // 添加了专业区域国别学 专业id为 785 栏目id为 540
        '748' => [
            'name'       => '数字经济',
            'columnId'   => '533',
            'columnName' => '数字经济',
        ],
        '750' => [
            'name'       => '翻译',
            'columnId'   => '534',
            'columnName' => '翻译',
        ],
        '774' => [
            'name'       => '纳米科学与工程',
            'columnId'   => '535',
            'columnName' => '纳米科学与工程',
        ],
        '776' => [
            'name'       => '智能科学与技术',
            'columnId'   => '536',
            'columnName' => '智能科学与技术',
        ],
        '778' => [
            'name'       => '人工智能',
            'columnId'   => '537',
            'columnName' => '人工智能',
        ],
        '780' => [
            'name'       => '遥感科学与技术',
            'columnId'   => '538',
            'columnName' => '遥感科学与技术',
        ],
        '782' => [
            'name'       => '旅游与酒店管理',
            'columnId'   => '539',
            'columnName' => '旅游与酒店管理',
        ],
        '785' => [
            'name'       => '区域国别学',
            'columnId'   => '540',
            'columnName' => '区域国别学',
        ],
        '802' => [
            'name'       => '金融学',
            'columnId'   => '545',
            'columnName' => '金融学',
        ],
        '804' => [
            'name'       => '能源动力',
            'columnId'   => '546',
            'columnName' => '能源动力',
        ],
        '812' => [
            'name'       => '审计',
            'columnId'   => '547',
            'columnName' => '审计',
        ],
    ];

    // 只要职位里面有对应城市(二级)
    const CITY_RULE = [
        '821'  => [
            'name' => '南京',
            'list' => [
                [
                    'columnId'          => '512',
                    'columnName'        => '南京高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '513',
                    'columnName'        => '南京机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,

                ],
                [
                    'columnId'          => '514',
                    'columnName'        => '南京中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '515',
                    'columnName'        => '南京科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '516',
                    'columnName'        => '南京医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '517',
                    'columnName'  => '南京企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '934'  => [
            'name' => '杭州',
            'list' => [
                [
                    'columnId'          => '524',
                    'columnName'        => '杭州高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '525',
                    'columnName'        => '杭州机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,

                ],
                [
                    'columnId'          => '526',
                    'columnName'        => '杭州中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '527',
                    'columnName'        => '杭州科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '528',
                    'columnName'        => '杭州医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '529',
                    'columnName'  => '杭州企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1710' => [
            'name' => '武汉',
            'list' => [
                [
                    'columnId'          => '506',
                    'columnName'        => '武汉高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '507',
                    'columnName'        => '武汉机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,

                ],
                [
                    'columnId'          => '508',
                    'columnName'        => '武汉中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '509',
                    'columnName'        => '武汉科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '510',
                    'columnName'        => '武汉医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '511',
                    'columnName'  => '武汉企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1965' => [
            'name' => '广州',
            'list' => [
                [
                    'columnId'          => '494',
                    'columnName'        => '广州高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '495',
                    'columnName'        => '广州机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,

                ],
                [
                    'columnId'          => '496',
                    'columnName'        => '广州中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '497',
                    'columnName'        => '广州科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '498',
                    'columnName'        => '广州医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '499',
                    'columnName'  => '广州企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1988' => [
            'name' => '深圳',
            'list' => [
                [
                    'columnId'          => '488',
                    'columnName'        => '深圳高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '489',
                    'columnName'        => '深圳机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,

                ],
                [
                    'columnId'          => '490',
                    'columnName'        => '深圳中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '491',
                    'columnName'        => '深圳科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '492',
                    'columnName'        => '深圳医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '493',
                    'columnName'  => '深圳企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '2368' => [
            'name' => '成都',
            'list' => [
                [
                    'columnId'          => '500',
                    'columnName'        => '成都高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '501',
                    'columnName'        => '成都机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,

                ],
                [
                    'columnId'          => '502',
                    'columnName'        => '成都中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '503',
                    'columnName'        => '成都科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '504',
                    'columnName'        => '成都医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '505',
                    'columnName'  => '成都企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '2899' => [
            'name' => '西安',
            'list' => [
                [
                    'columnId'          => '518',
                    'columnName'        => '西安高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '519',
                    'columnName'        => '西安机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,

                ],
                [
                    'columnId'          => '520',
                    'columnName'        => '西安中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '521',
                    'columnName'        => '西安科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '522',
                    'columnName'        => '西安医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '523',
                    'columnName'  => '西安企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
    ];

    // 只要职位里面有对应城市(二级)单独,这里的规则特别简单,就是只要职位列表里面有这个城市,就回去对应的栏目
    const ALONE_CITY_RULE = [
        '38'   => [
            'columnId' => '101',
            'name'     => '石家庄',
        ],
        '61'   => [
            'columnId' => '102',
            'name'     => '唐山',
        ],
        '221'  => [
            'columnId' => '100',
            'name'     => '太原',
        ],
        '352'  => [
            'columnId' => '104',
            'name'     => '呼和浩特',
        ],
        '362'  => [
            'columnId' => '103',
            'name'     => '包头',
        ],
        '467'  => [
            'columnId' => '113',
            'name'     => '沈阳',
        ],
        '481'  => [
            'columnId' => '112',
            'name'     => '大连',
        ],
        '586'  => [
            'columnId' => '109',
            'name'     => '长春',
        ],
        '597'  => [
            'columnId' => '110',
            'name'     => '吉林',
        ],
        '656'  => [
            'columnId' => '111',
            'name'     => '哈尔滨',
        ],
        '833'  => [
            'columnId' => '57',
            'name'     => '无锡',
        ],
        '842'  => [
            'columnId' => '58',
            'name'     => '徐州',
        ],
        '853'  => [
            'columnId' => '52',
            'name'     => '常州',
        ],
        '861'  => [
            'columnId' => '55',
            'name'     => '苏州',
        ],
        '871'  => [
            'columnId' => '54',
            'name'     => '南通',
        ],
        '880'  => [
            'columnId' => '53',
            'name'     => '连云港',
        ],
        '906'  => [
            'columnId' => '59',
            'name'     => '扬州',
        ],
        '913'  => [
            'columnId' => '60',
            'name'     => '镇江',
        ],
        '920'  => [
            'columnId' => '56',
            'name'     => '泰州',
        ],
        '948'  => [
            'columnId' => '63',
            'name'     => '宁波',
        ],
        '960'  => [
            'columnId' => '66',
            'name'     => '温州',
        ],
        '972'  => [
            'columnId' => '62',
            'name'     => '嘉兴',
        ],
        '980'  => [
            'columnId' => '61',
            'name'     => '湖州',
        ],
        '986'  => [
            'columnId' => '64',
            'name'     => '绍兴',
        ],
        '1010' => [
            'columnId' => '67',
            'name'     => '舟山',
        ],
        '1015' => [
            'columnId' => '65',
            'name'     => '台州',
        ],
        '1047' => [
            'columnId' => '68',
            'name'     => '合肥',
        ],
        '1057' => [
            'columnId' => '70',
            'name'     => '芜湖',
        ],
        '1081' => [
            'columnId' => '69',
            'name'     => '马鞍山',
        ],
        '1169' => [
            'columnId' => '71',
            'name'     => '福州',
        ],
        '1183' => [
            'columnId' => '73',
            'name'     => '厦门',
        ],
        '1209' => [
            'columnId' => '72',
            'name'     => '泉州',
        ],
        '1222' => [
            'columnId' => '74',
            'name'     => '漳州',
        ],
        '1264' => [
            'columnId' => '76',
            'name'     => '南昌',
        ],
        '1285' => [
            'columnId' => '75',
            'name'     => '九江',
        ],
        '1376' => [
            'columnId' => '78',
            'name'     => '济南',
        ],
        '1387' => [
            'columnId' => '80',
            'name'     => '青岛',
        ],
        '1421' => [
            'columnId' => '82',
            'name'     => '烟台',
        ],
        '1447' => [
            'columnId' => '77',
            'name'     => '济宁',
        ],
        '1466' => [
            'columnId' => '81',
            'name'     => '威海',
        ],
        '1479' => [
            'columnId' => '79',
            'name'     => '临沂',
        ],
        '1533' => [
            'columnId' => '87',
            'name'     => '郑州',
        ],
        '1556' => [
            'columnId' => '86',
            'name'     => '洛阳',
        ],
        '1740' => [
            'columnId' => '84',
            'name'     => '宜昌',
        ],
        '1754' => [
            'columnId' => '83',
            'name'     => '襄阳',
        ],
        '1828' => [
            'columnId' => '85',
            'name'     => '长沙',
        ],
        '1999' => [
            'columnId' => '96',
            'name'     => '珠海',
        ],
        '2003' => [
            'columnId' => '95',
            'name'     => '汕头',
        ],
        '2011' => [
            'columnId' => '93',
            'name'     => '佛山',
        ],
        '2025' => [
            'columnId' => '98',
            'name'     => '湛江',
        ],
        '2050' => [
            'columnId' => '94',
            'name'     => '惠州',
        ],
        '2091' => [
            'columnId' => '92',
            'name'     => '东莞',
        ],
        '2123' => [
            'columnId' => '97',
            'name'     => '中山',
        ],
        '2163' => [
            'columnId' => '91',
            'name'     => '南宁',
        ],
        '2177' => [
            'columnId' => '90',
            'name'     => '柳州',
        ],
        '2189' => [
            'columnId' => '89',
            'name'     => '桂林',
        ],
        '2215' => [
            'columnId' => '88',
            'name'     => '北海',
        ],
        '2292' => [
            'columnId' => '99',
            'name'     => '海口',
        ],
        '2416' => [
            'columnId' => '117',
            'name'     => '绵阳',
        ],
        '2573' => [
            'columnId' => '114',
            'name'     => '贵阳',
        ],
        '2589' => [
            'columnId' => '115',
            'name'     => '遵义',
        ],
        '2671' => [
            'columnId' => '116',
            'name'     => '昆明',
        ],
        '2817' => [
            'columnId' => '118',
            'name'     => '拉萨',
        ],
        '3023' => [
            'columnId' => '107',
            'name'     => '兰州',
        ],
        '3127' => [
            'columnId' => '105',
            'name'     => '西宁',
        ],
        '3179' => [
            'columnId' => '106',
            'name'     => '银川',
        ],
        '3207' => [
            'columnId' => '108',
            'name'     => '乌鲁木齐',
        ],
        '3871' => [
            'columnId' => '236',
            'name'     => '其他',
        ],
    ];

    // 只要职位里面有这些省
    const PROVINCE_RULE = [
        '1'    => [
            'name' => '北京',
            'list' => [
                [
                    'columnId'          => '284',
                    'columnName'        => '北京高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '285',
                    'columnName'        => '北京机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '286',
                    'columnName'        => '北京中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '287',
                    'columnName'        => '北京科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '288',
                    'columnName'        => '北京医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '289',
                    'columnName'  => '北京企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '19'   => [
            'name' => '天津',
            'list' => [
                [
                    'columnId'          => '296',
                    'columnName'        => '天津高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '297',
                    'columnName'        => '天津机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '298',
                    'columnName'        => '天津中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '299',
                    'columnName'        => '天津科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '300',
                    'columnName'        => '天津医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '301',
                    'columnName'  => '天津企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '37'   => [
            'name' => '河北',
            'list' => [
                [
                    'columnId'          => '308',
                    'columnName'        => '河北高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '309',
                    'columnName'        => '河北机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '310',
                    'columnName'        => '河北中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '311',
                    'columnName'        => '河北科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '312',
                    'columnName'        => '河北医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '313',
                    'columnName'  => '河北企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '220'  => [
            'name' => '山西',
            'list' => [
                [
                    'columnId'          => '314',
                    'columnName'        => '山西高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '315',
                    'columnName'        => '山西机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '316',
                    'columnName'        => '山西中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '317',
                    'columnName'        => '山西科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '318',
                    'columnName'        => '山西医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '319',
                    'columnName'  => '山西企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '351'  => [
            'name' => '内蒙古',
            'list' => [
                [
                    'columnId'          => '320',
                    'columnName'        => '内蒙古高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '321',
                    'columnName'        => '内蒙古机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '322',
                    'columnName'        => '内蒙古中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '323',
                    'columnName'        => '内蒙古科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '324',
                    'columnName'        => '内蒙古医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '325',
                    'columnName'  => '内蒙古企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '466'  => [
            'name' => '辽宁',
            'list' => [
                [
                    'columnId'          => '326',
                    'columnName'        => '辽宁高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '327',
                    'columnName'        => '辽宁机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '328',
                    'columnName'        => '辽宁中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '329',
                    'columnName'        => '辽宁科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '330',
                    'columnName'        => '辽宁医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '331',
                    'columnName'  => '辽宁企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '585'  => [
            'name' => '吉林',
            'list' => [
                [
                    'columnId'          => '332',
                    'columnName'        => '吉林高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '333',
                    'columnName'        => '吉林机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '334',
                    'columnName'        => '吉林中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '335',
                    'columnName'        => '吉林科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '336',
                    'columnName'        => '吉林医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '337',
                    'columnName'  => '吉林企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '655'  => [
            'name' => '黑龙江',
            'list' => [
                [
                    'columnId'          => '338',
                    'columnName'        => '黑龙江高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '339',
                    'columnName'        => '黑龙江机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '340',
                    'columnName'        => '黑龙江中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '341',
                    'columnName'        => '黑龙江科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '342',
                    'columnName'        => '黑龙江医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '343',
                    'columnName'  => '黑龙江企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '801'  => [
            'name' => '上海',
            'list' => [
                [
                    'columnId'          => '290',
                    'columnName'        => '上海高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '291',
                    'columnName'        => '上海机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '292',
                    'columnName'        => '上海中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '293',
                    'columnName'        => '上海科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '294',
                    'columnName'        => '上海医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '295',
                    'columnName'  => '上海企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '820'  => [
            'name' => '江苏',
            'list' => [
                [
                    'columnId'          => '344',
                    'columnName'        => '江苏高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '345',
                    'columnName'        => '江苏机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '346',
                    'columnName'        => '江苏中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '347',
                    'columnName'        => '江苏科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '348',
                    'columnName'        => '江苏医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '349',
                    'columnName'  => '江苏企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '933'  => [
            'name' => '浙江',
            'list' => [
                [
                    'columnId'          => '350',
                    'columnName'        => '浙江高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '351',
                    'columnName'        => '浙江机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '352',
                    'columnName'        => '浙江中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '353',
                    'columnName'        => '浙江科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '354',
                    'columnName'        => '浙江医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '355',
                    'columnName'  => '浙江企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1046' => [
            'name' => '安徽',
            'list' => [
                [
                    'columnId'          => '356',
                    'columnName'        => '安徽高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '357',
                    'columnName'        => '安徽机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '358',
                    'columnName'        => '安徽中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '359',
                    'columnName'        => '安徽科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '360',
                    'columnName'        => '安徽医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '361',
                    'columnName'  => '安徽企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1168' => [
            'name' => '福建',
            'list' => [
                [
                    'columnId'          => '362',
                    'columnName'        => '福建高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '363',
                    'columnName'        => '福建机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '364',
                    'columnName'        => '福建中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '365',
                    'columnName'        => '福建科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '366',
                    'columnName'        => '福建医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '367',
                    'columnName'  => '福建企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1263' => [
            'name' => '江西',
            'list' => [
                [
                    'columnId'          => '368',
                    'columnName'        => '江西高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '369',
                    'columnName'        => '江西机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '370',
                    'columnName'        => '江西中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '371',
                    'columnName'        => '江西科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '372',
                    'columnName'        => '江西医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '373',
                    'columnName'  => '江西企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1375' => [
            'name' => '山东',
            'list' => [
                [
                    'columnId'          => '374',
                    'columnName'        => '山东高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '375',
                    'columnName'        => '山东机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '376',
                    'columnName'        => '山东中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '377',
                    'columnName'        => '山东科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '378',
                    'columnName'        => '山东医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '379',
                    'columnName'  => '山东企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1532' => [
            'name' => '河南',
            'list' => [
                [
                    'columnId'          => '380',
                    'columnName'        => '河南高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '381',
                    'columnName'        => '河南机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '382',
                    'columnName'        => '河南中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '383',
                    'columnName'        => '河南科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '384',
                    'columnName'        => '河南医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '385',
                    'columnName'  => '河南企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1709' => [
            'name' => '湖北',
            'list' => [
                [
                    'columnId'          => '386',
                    'columnName'        => '湖北高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '387',
                    'columnName'        => '湖北机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '388',
                    'columnName'        => '湖北中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '389',
                    'columnName'        => '湖北科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '390',
                    'columnName'        => '湖北医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '391',
                    'columnName'  => '湖北企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1827' => [
            'name' => '湖南',
            'list' => [
                [
                    'columnId'          => '392',
                    'columnName'        => '湖南高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '393',
                    'columnName'        => '湖南机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '394',
                    'columnName'        => '湖南中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '395',
                    'columnName'        => '湖南科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '396',
                    'columnName'        => '湖南医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '397',
                    'columnName'  => '湖南企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1964' => [
            'name' => '广东',
            'list' => [
                [
                    'columnId'          => '398',
                    'columnName'        => '广东高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '399',
                    'columnName'        => '广东机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '400',
                    'columnName'        => '广东中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '401',
                    'columnName'        => '广东科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '402',
                    'columnName'        => '广东医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '403',
                    'columnName'  => '广东企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '2162' => [
            'name' => '广西',
            'list' => [
                [
                    'columnId'          => '404',
                    'columnName'        => '广西高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '405',
                    'columnName'        => '广西机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '406',
                    'columnName'        => '广西中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '407',
                    'columnName'        => '广西科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '408',
                    'columnName'        => '广西医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '409',
                    'columnName'  => '广西企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '2291' => [
            'name' => '海南',
            'list' => [
                [
                    'columnId'          => '410',
                    'columnName'        => '海南高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '411',
                    'columnName'        => '海南机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '412',
                    'columnName'        => '海南中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '413',
                    'columnName'        => '海南科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '414',
                    'columnName'        => '海南医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '415',
                    'columnName'  => '海南企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '2323' => [
            'name' => '重庆',
            'list' => [
                [
                    'columnId'          => '302',
                    'columnName'        => '重庆高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '303',
                    'columnName'        => '重庆机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '304',
                    'columnName'        => '重庆中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '305',
                    'columnName'        => '重庆科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '306',
                    'columnName'        => '重庆医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '307',
                    'columnName'  => '重庆企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '2367' => [
            'name' => '四川',
            'list' => [
                [
                    'columnId'          => '416',
                    'columnName'        => '四川高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '417',
                    'columnName'        => '四川机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '418',
                    'columnName'        => '四川中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '419',
                    'columnName'        => '四川科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '420',
                    'columnName'        => '四川医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '421',
                    'columnName'  => '四川企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '2572' => [
            'name' => '贵州',
            'list' => [
                [
                    'columnId'          => '422',
                    'columnName'        => '贵州高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '423',
                    'columnName'        => '贵州机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '424',
                    'columnName'        => '贵州中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '425',
                    'columnName'        => '贵州科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '426',
                    'columnName'        => '贵州医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '427',
                    'columnName'  => '贵州企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '2670' => [
            'name' => '云南',
            'list' => [
                [
                    'columnId'          => '428',
                    'columnName'        => '云南高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '429',
                    'columnName'        => '云南机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '430',
                    'columnName'        => '云南中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '431',
                    'columnName'        => '云南科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '432',
                    'columnName'        => '云南医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '433',
                    'columnName'  => '云南企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '2816' => [
            'name' => '西藏',
            'list' => [
                [
                    'columnId'          => '434',
                    'columnName'        => '西藏高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '435',
                    'columnName'        => '西藏机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '436',
                    'columnName'        => '西藏中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '437',
                    'columnName'        => '西藏科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '438',
                    'columnName'        => '西藏医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '439',
                    'columnName'  => '西藏企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '2898' => [
            'name' => '陕西',
            'list' => [
                [
                    'columnId'          => '440',
                    'columnName'        => '陕西高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '441',
                    'columnName'        => '陕西机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '442',
                    'columnName'        => '陕西中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '443',
                    'columnName'        => '陕西科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '444',
                    'columnName'        => '陕西医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '445',
                    'columnName'  => '陕西企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '3022' => [
            'name' => '甘肃',
            'list' => [
                [
                    'columnId'          => '446',
                    'columnName'        => '甘肃高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '447',
                    'columnName'        => '甘肃机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '448',
                    'columnName'        => '甘肃中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '449',
                    'columnName'        => '甘肃科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '450',
                    'columnName'        => '甘肃医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '451',
                    'columnName'  => '甘肃企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '3126' => [
            'name' => '青海',
            'list' => [
                [
                    'columnId'          => '452',
                    'columnName'        => '青海高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '453',
                    'columnName'        => '青海机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '454',
                    'columnName'        => '青海中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '455',
                    'columnName'        => '青海科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '456',
                    'columnName'        => '青海医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '457',
                    'columnName'  => '青海企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '3178' => [
            'name' => '宁夏',
            'list' => [
                [
                    'columnId'          => '458',
                    'columnName'        => '宁夏高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '459',
                    'columnName'        => '宁夏机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '460',
                    'columnName'        => '宁夏中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '461',
                    'columnName'        => '宁夏科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '462',
                    'columnName'        => '宁夏医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '463',
                    'columnName'  => '宁夏企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '3206' => [
            'name' => '新疆',
            'list' => [
                [
                    'columnId'          => '464',
                    'columnName'        => '新疆高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '465',
                    'columnName'        => '新疆机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '466',
                    'columnName'        => '新疆中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '467',
                    'columnName'        => '新疆科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '468',
                    'columnName'        => '新疆医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '469',
                    'columnName'  => '新疆企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '3325' => [
            'name' => '台湾',
            'list' => [
                [
                    'columnId'          => '470',
                    'columnName'        => '台湾高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '471',
                    'columnName'        => '台湾机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '472',
                    'columnName'        => '台湾中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '473',
                    'columnName'        => '台湾科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '474',
                    'columnName'        => '台湾医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '475',
                    'columnName'  => '台湾企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '3716' => [
            'name' => '香港',
            'list' => [
                [
                    'columnId'          => '476',
                    'columnName'        => '香港高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '477',
                    'columnName'        => '香港机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '478',
                    'columnName'        => '香港中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '479',
                    'columnName'        => '香港科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '480',
                    'columnName'        => '香港医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '481',
                    'columnName'  => '香港企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '3738' => [
            'name' => '澳门',
            'list' => [
                [
                    'columnId'          => '482',
                    'columnName'        => '澳门高校',
                    'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_COLLEGE_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '483',
                    'columnName'        => '澳门机关事业',
                    'companyType'       => self::COMPANY_ORGAN_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_ORGAN_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '484',
                    'columnName'        => '澳门中小学',
                    'companyType'       => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
                    'categoryJobLevel1' => self::AREA_PRIMARY_AND_SECONDARY_SCHOOL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '485',
                    'columnName'        => '澳门科研',
                    'companyType'       => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_RESEARCH_INSTITUTION_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'          => '486',
                    'columnName'        => '澳门医学',
                    'companyType'       => self::COMPANY_MEDICAL_TYPE_LIST,
                    'categoryJobLevel1' => self::AREA_MEDICAL_CATEGORY_JOB_LEVEL1,
                ],
                [
                    'columnId'    => '487',
                    'columnName'  => '澳门企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
    ];

    // 人才专场规则1,只要单位类型是这里面的id,就会自动到对应的栏目去
    const TALENT_SPECIAL_RULE_1 = [
        '1' => [
            'name'       => '双一流院校',
            'columnId'   => '272',
            'columnName' => '双一流院校人才招聘',

        ],
        '3' => [
            'name'       => '普通本科院校',
            'columnId'   => '273',
            'columnName' => '高职高专人才招聘',

        ],
        '4' => [
            'name'       => '高职高专院校',
            'columnId'   => '274',
            'columnName' => '党校（行政学院）招聘',
        ],
    ];

    // 人才专场规则2,只要职位里面的任意一个学历要求符合下面的,都会自动到对应的栏目去
    const TALENT_SPECIAL_RULE_2 = [
        self::EDUCATION_DOCTOR_CODE => [
            'name'       => '博士研究生',
            'columnId'   => '275',
            'columnName' => '博士人才招聘',
        ],
    ];

    // 海归人才规则(里面有一个比较特殊的,就是如果职位要求里面的学历要求是海归,就不需要判断学历要求了,两者有其一就可以了)
    // 并且这里面的id,其实是省的key,是省的key
    const TALENT_BROAD_RULE = [
        // 华东
        '267' => [
            '801'  => ['name' => '上海'],
            '820'  => ['name' => '江苏'],
            '933'  => ['name' => '浙江'],
            '1046' => ['name' => '安徽'],
            '1168' => ['name' => '福建'],
            '1263' => ['name' => '江西'],
            '1375' => ['name' => '山东'],
            '3325' => ['name' => '台湾'],
        ],
        // 华中
        '268' => [
            '1532' => ['name' => '河南'],
            '1709' => ['name' => '湖北'],
            '1827' => ['name' => '湖南'],
            '1964' => ['name' => '广东'],
            '2162' => ['name' => '广西'],
            '2291' => ['name' => '海南'],
            '3716' => ['name' => '香港'],
            '3738' => ['name' => '澳门'],
        ],
        // 华北
        '269' => [
            '1'   => ['name' => '北京'],
            '19'  => ['name' => '天津'],
            '37'  => ['name' => '河北'],
            '220' => ['name' => '山西'],
            '351' => ['name' => '内蒙古'],
            '466' => ['name' => '辽宁'],
            '585' => ['name' => '吉林'],
            '655' => ['name' => '黑龙江'],
        ],
        // 西南
        '270' => [
            '2323' => ['name' => '重庆'],
            '2367' => ['name' => '四川'],
            '2572' => ['name' => '贵州'],
            '2670' => ['name' => '云南'],
            '2816' => ['name' => '西藏'],
            '2898' => ['name' => '陕西'],
            '3022' => ['name' => '甘肃'],
            '3126' => ['name' => '青海'],
            '3178' => ['name' => '宁夏'],
            '3206' => ['name' => '新疆'],
        ],
    ];

    // 博士后规则(岗位类型是博士后(这里的key有点不一样,这里的key是栏目id,而后面的list则是哪些企业去到这些栏目)
    const POSTDOC_RULE = [
        '263' => self::COMPANY_COLLEGE_TYPE_LIST,
        '264' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
        '265' => self::COMPANY_GENERAL_AND_ORGAN_TYPE_LIST,
        '266' => self::COMPANY_MEDICAL_TYPE_LIST,
    ];

    // 企业招聘规则(这里面有两个种元素,只要符合某一元素就可以到对应的栏目了
    // 知名企业
    // 政府国有企业
    // 银行、信用社等金融机构
    // 中小成长型企业
    // 公立（国有）
    // 民营（私营）/公私混合/中外合资（合营）/外企（外商）独资/非盈利组织及其他
    const COMPANY_GENERAL_RULE = [
        // 知名企业
        '259' => [
            'companyType' => [
                // 知名企业
                self::COMPANY_TYPE_FAMOUS_CODE,
            ],
        ],
        // 国有（政府）企业
        '260' => [
            'companyType'   => [
                // 国有
                self::COMPANY_TYPE_STATE_OWNED_CODE,
            ],
            'companyNature' => [
                // 国有
                self::COMPANY_NATURE_STATE_OWNED_CODE,
            ],

        ],
        // 金融机构
        '261' => [
            'companyType' => [
                self::COMPANY_TYPE_BANK_CODE,
            ],
        ],
        // 中小创新型企业
        '262' => [
            'companyType'   => [
                self::COMPANY_TYPE_SMALL_AND_MEDIUM_CODE,
            ],
            'companyNature' => [
                self::COMPANY_NATURE_PRIVATE_CODE,
                self::COMPANY_NATURE_PUBLIC_AND_PRIVATE_CODE,
                self::COMPANY_NATURE_CHINESE_AND_FOREIGN_CODE,
                self::COMPANY_NATURE_FOREIGN_CODE,
            ],

        ],

    ];

    // 医学人才规则,这里面的key全部都是栏目,然后对应的其他就是规则了(这里的职位类型都是level1?)
    const MEDICAL_RULE = [
        // 卫生系统统一招聘
        '256' => [
            'categoryJob' => [
                64,
                82,
                83,
                84,
                85,
                86,
                87,
                88,
                89,
                90,
                91,
                92,
                93,
                94,
                96,
            ],
            // 事业单位
            'companyType' => [
                self::COMPANY_TYPE_ADMIN_DEPARTMENT_CODE,
                self::COMPANY_TYPE_PUBLIC_INSTITUTION_CODE,
            ],
        ],
        // 医疗单位自主招聘
        '257' => [
            /**
             * 医院；
             * 其他医疗机构；
             */
            'companyType' => [
                self::COMPANY_TYPE_HOSPITAL_CODE,
                self::COMPANY_TYPE_OTHER_MEDICAL_CODE,
            ],
        ],
        // 医卫院校（院系）招聘
        '258' => [
            // 医疗卫生专业岗
            /**
             *
             * 中科院系统；
             * 人文社科研究机构（事业单位类型）；
             * 自然与应用科研机构（事业单位类型）；
             * 企业研发机构；
             * 双一流院校；
             * 普通本科院校；
             * 高职高专院校；
             * 党校与行政学院；
             * 中小学；
             * 中专&职业中学&技师学院；
             * 幼儿园；
             */
            'categoryJob' => [
                64,
                82,
                83,
                84,
                85,
                86,
                87,
                88,
                89,
                90,
                91,
                92,
                93,
                94,
                96,
                95,
            ],
            'companyType' => [
                self::COMPANY_TYPE_DOUBLE_TOP_COLLEGE_CODE,
                self::COMPANY_TYPE_ORDINARY_UNDERGRADUATE_COLLEGE_CODE,
                self::COMPANY_TYPE_HIGHER_VOCATIONAL_COLLEGE_CODE,
                self::COMPANY_TYPE_PARTY_COLLEGE_CODE,
                self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE,
                self::COMPANY_TYPE_MIDDLE_VOCATIONAL_SCHOOL_CODE,
                self::COMPANY_TYPE_KINDERGARTEN_CODE,
                self::COMPANY_TYPE_CHINESE_ACADEMY_OF_SCIENCES_CODE,
                self::COMPANY_TYPE_SOCIAL_SCIENCE_CODE,
                self::COMPANY_TYPE_NATURAL_AND_APPLIED_RESEARCH_CODE,
                self::COMPANY_TYPE_GENERAL_RESEARCH_CODE,
            ],
            // 这个是比较特殊的,单独一个职位类型满足就可以进去这个栏目了
            // 'aloneCategoryJob' => [64],
        ],
    ];

    // 政府与事业单位规则
    const PUBLIC_INSTITUTION_RULE = [
        // 只有军队武警会去到武警单位
        '255' => [
            'companyType' => [self::COMPANY_TYPE_POLICE_FORCE_CODE],
        ],
    ];

    /**
     * 中科院系统研究机构    中科院系统
     * 人文社科研究机构    人文社科研究机构（事业单位类型）
     * 自然与应用科研机构    自然与应用科研机构（事业单位类型）
     * 企业研发机构    企业研发机构
     */
    const SCIENTIFIC_RESEARCH_TALENT_RULE = [
        '247' => [
            'companyType' => [self::COMPANY_TYPE_CHINESE_ACADEMY_OF_SCIENCES_CODE],
        ],
        '248' => [
            'companyType' => [self::COMPANY_TYPE_SOCIAL_SCIENCE_CODE],
        ],
        '249' => [
            'companyType' => [self::COMPANY_TYPE_NATURAL_AND_APPLIED_RESEARCH_CODE],
        ],
        '250' => [
            'companyType' => [self::COMPANY_TYPE_GENERAL_RESEARCH_CODE],
        ],
    ];

    /**
     * 中小学
     */
    const PRIMARY_AND_SECONDARY_SCHOOL_RULE = [
        // 教育系统统招
        '243' => [
            // 事业单位
            'companyType' => [
                self::COMPANY_TYPE_ADMIN_DEPARTMENT_CODE,
                self::COMPANY_TYPE_PUBLIC_INSTITUTION_CODE,
            ],
            /**
             * 中小学骨干教师岗
             * 中小学普通教师岗
             * 中职教师岗（公共课类）
             * 中职教师岗（专业课类）
             * 学前教师/幼师
             * 教务岗
             * 实验技术岗
             * 生活老师
             * 专职班主任/辅导员
             * 其他教学/行政支撑岗
             * 中小学校长/校领导/单位负责人
             * 中小学校中层党政部门负责人
             * 教研员
             * 幼儿园园长
             */
            'categoryJob' => [
                48,
                49,
                50,
                51,
                52,
                53,
                54,
                55,
                56,
                57,
                42,
                47,
                58,
                43,
            ],
        ],
        '244' => [
            'companyType'      => [
                self::COMPANY_TYPE_DOUBLE_TOP_COLLEGE_CODE,
                self::COMPANY_TYPE_ORDINARY_UNDERGRADUATE_COLLEGE_CODE,
                self::COMPANY_TYPE_HIGHER_VOCATIONAL_COLLEGE_CODE,
            ],
            'categoryJob'      => [
                48,
                49,
                53,
                54,
                55,
                56,
                57,
                42,
                47,
            ],
            'aloneCompanyType' => [self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE],
        ],
        '245' => [
            'aloneCompanyType' => [self::COMPANY_TYPE_MIDDLE_VOCATIONAL_SCHOOL_CODE],
            'aloneCategoryJob' => [
                50,
                51,
            ],
        ],
        '246' => [
            'companyType'      => [
                self::COMPANY_TYPE_DOUBLE_TOP_COLLEGE_CODE,
                self::COMPANY_TYPE_ORDINARY_UNDERGRADUATE_COLLEGE_CODE,
                self::COMPANY_TYPE_HIGHER_VOCATIONAL_COLLEGE_CODE,
            ],
            'categoryJob'      => [
                52,
                43,
                57,
            ],
            // 幼儿园
            'aloneCompanyType' => [self::COMPANY_TYPE_KINDERGARTEN_CODE],
        ],
    ];

    /**
     * 238    学科带头人、教授招聘
     * 239    中高层干部
     * 240    教学科研人才
     * 241    教辅、行政、实验、助理人员招聘
     * 242    高校辅导员
     */
    const COLLEGE_RULE = [
        '238' => [
            'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,
            /**
             * 教学岗（高等院校）    学科带头人/学术骨干
             * 教学岗（高等院校）    教授/副教授
             * 教学岗（高等院校）    学术领军人才
             */
            'categoryJob' => [
                31,
                32,
                30,
                59,
            ],
        ],
        '239' => [
            'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,
            /**
             * 中高级管理岗（教育/科研机构）    高校校长/校领导/单位负责人
             * 中高级管理岗（教育/科研机构）    二级学院院长/副院长
             * 中高级管理岗（教育/科研机构）    系/研究所/实验室负责人
             * 中高级管理岗（教育/科研机构）    高校中层党政部门负责人
             */
            'categoryJob' => [
                41,
                44,
                45,
                46,
            ],
        ],
        '240' => [
            'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,
            /**
             * 教学岗（高等院校）    专职教师/教学科研岗    33
             * 博士后    博士后    29
             * 科学研究岗（教育/科研/卫生单位）    高级研究人员（正/副研究员）    60
             * 科学研究岗（教育/科研/卫生单位）    专职科研岗    61
             * 科学研究岗（教育/科研/卫生单位）    科研领军人才    59
             * 科学研究岗（教育/科研/卫生单位）    医学教学/科研人才    64
             */
            'categoryJob' => [
                33,
                29,
                60,
                61,
                64,
            ],
        ],
        '241' => [
            'companyType'       => self::COMPANY_COLLEGE_TYPE_LIST,
            'categoryJob'       => [
                35,
                36,
                37,
                38,
                39,
                40,
                62,
                63,
                65,
            ],
            /**
             * 人力/行政/财务岗    一级下所有二级
             * 法务/翻译/咨询/教培岗
             * 销售/商务/客服/招生岗
             * 市场/公关/广告/会展岗
             * 编辑/出版/传媒/文化岗
             * 互联网产品/设计/运营岗
             * 技术研究/开发/测试/运维岗
             * 电子/通信/硬件/半导体岗
             * 生产制造/机械/汽车专业岗
             * 能源/矿产/电力/环保专业岗
             * 化工/轻工/食品专业岗
             * 生物/医药/医疗器械专业岗
             * 金融专业岗
             * 房地产/建筑/物业专业岗
             * 供应链/物流/运输/采购/贸易专业岗
             * 零售/生活服务/农林牧渔专业岗
             * 管培生/其他
             */
            'categoryJobLevel1' => [
                12,
                13,
                14,
                15,
                16,
                17,
                18,
                19,
                20,
                21,
                22,
                23,
                24,
                25,
                26,
                27,
                28,
            ],
        ],
        '242' => [
            'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,
            /**
             * 教学支撑岗（高等院校）    辅导员岗
             */
            'categoryJob' => [34],
        ],
    ];

    //【海外地区人才招聘】栏目公告调用规则：
    //①公告下职位工作地点为海外地区，则公告信息自动调至该栏目；含在线、下线状态职位；不含隐藏。
    const ABROAD_AREA_RULE = [
        // 海外地区人才招聘
        '271' => [
            'columnId' => 271,
            // 海外
            'areaId'   => [3870],
        ],
    ];

    //年度招聘栏目公告调用规则：
    //①公告所在单位，单位类型为 双一流院校；或 普通本科院校；或 高职高专院校；或 党校与行政学院；
    //②公告下在线状态 在招人数≥13 或 职位数量≥13；
    const ANNUAL_RECRUITMENT_RULE = [
        // 年度招聘
        '277' => [
            'columnId'    => 277,
            // 双一流院校
            'companyType' => [
                self::COMPANY_TYPE_DOUBLE_TOP_COLLEGE_CODE,
                self::COMPANY_TYPE_ORDINARY_UNDERGRADUATE_COLLEGE_CODE,
                // self::COMPANY_TYPE_HIGHER_VOCATIONAL_COLLEGE_CODE,
                // self::COMPANY_TYPE_PARTY_COLLEGE_CODE,
            ],
        ],
    ];

    // 海外栏目的一些相关规则
    // 求贤公告
    // 属性【焦点】+合作单位+单位类型+职位类型+学历/职称
    // 合作单位
    // 双一流院校；
    // 中国科学院系统；
    // 人文社科研究机构（事业单位类型）；
    // 自然与应用科研机构（事业单位类型）；
    // 医院
    // 事业单位

    // 双一流院校；
    // 中国科学院系统；
    // 人文社科研究机构（事业单位类型）；
    // 自然与应用科研机构（事业单位类型）；
    // 医院
    // 事业单位

    // 学术领军人才/顶尖人才；
    // 学科带头人/学术骨干；
    // 教授/副教授；
    // 助理教授/助理副教授；
    // 专职教师/教学科研岗
    // 高校校长/校领导/单位负责人
    // 二级学院院长/副院长
    // 系/研究所/实验室负责人
    // 高校/科研机构中层党政部门负责人
    // 科研领军人才/杰出人才
    // 高级研究人员（正/副研究员）
    // 助理研究员/研究实习员
    // 专职科研岗
    // 医学教学/科研人才
    // 科研领军人才/杰出人才
    // 高级研究人员（正/副研究员）
    // 助理研究员/研究实习员
    // 专职科研岗
    // 院长/副院长
    // 学科带头人/学术骨干
    // 科室主任/副主任中高层管理岗
    // 主任医师/副主任医师
    // 主治医师/住院医师/医生
    // 博士
    // 副高及以上职称
    // 副高及以上职称
    // 副高及以上职称
    // 副高及以上职称
    // 副高及以上职称
    const ABROAD_QIUXIAN_RULE = [
        // 属性
        'ATTRIBUTE'              => [
            BaseArticleAttribute::ATTRIBUTE_FOCUS,
            BaseArticleAttribute::ATTRIBUTE_RECOMMEND,
        ],
        // 必须是合作单位
        'COOPERATIVE_UNIT'       => [
            1,
            2,
        ],
        /**
         * 双一流院校；
         * 中国科学院系统；
         * 人文社科研究机构（事业单位类型）；
         * 自然与应用科研机构（事业单位类型）；
         * 医院
         * 事业单位
         */
        'COMPANY_TYPE'           => [
            // 不同的属性匹配的类型
            BaseArticleAttribute::ATTRIBUTE_FOCUS     => [
                self::COMPANY_TYPE_DOUBLE_TOP_COLLEGE_CODE,
                self::COMPANY_TYPE_CHINESE_ACADEMY_OF_SCIENCES_CODE,
                self::COMPANY_TYPE_SOCIAL_SCIENCE_CODE,
                self::COMPANY_TYPE_NATURAL_AND_APPLIED_RESEARCH_CODE,
                self::COMPANY_TYPE_HOSPITAL_CODE,
                // self::COMPANY_TYPE_ADMIN_DEPARTMENT_CODE,
                self::COMPANY_TYPE_PUBLIC_INSTITUTION_CODE,
            ],
            /**
             * 普通本科院校；
             * 中国科学院系统；
             * 人文社科研究机构（事业单位类型）；
             * 自然与应用科研机构（事业单位类型）；
             */
            BaseArticleAttribute::ATTRIBUTE_RECOMMEND => [
                self::COMPANY_TYPE_ORDINARY_UNDERGRADUATE_COLLEGE_CODE,
                self::COMPANY_TYPE_CHINESE_ACADEMY_OF_SCIENCES_CODE,
                self::COMPANY_TYPE_SOCIAL_SCIENCE_CODE,
                self::COMPANY_TYPE_NATURAL_AND_APPLIED_RESEARCH_CODE,
            ],

        ],
        // 性质
        'COMPANY_NATURE'         => [
            self::COMPANY_NATURE_STATE_OWNED_CODE,
            self::COMPANY_NATURE_PUBLIC_AND_PRIVATE_CODE,
            self::COMPANY_NATURE_CHINESE_AND_FOREIGN_CODE,
            self::COMPANY_NATURE_FOREIGN_CODE,
            self::COMPANY_NATURE_OTHER_CODE,
        ],
        // 职位类型和职称，这两者是并的关系
        'CATEGORY_JOB_AND_TITLE' => [
            [
                /**
                 * 学术领军人才/顶尖人才；
                 * 学科带头人/学术骨干；
                 * 教授/副教授；
                 * 高校校长/校领导/单位负责人
                 * 二级学院院长/副院长
                 * 系/研究所/实验室负责人
                 * 高校/科研机构中层党政部门负责人
                 * 科研领军人才/杰出人才
                 * 高级研究人员（正/副研究员）
                 * 科研领军人才/杰出人才
                 * 高级研究人员（正/副研究员）
                 * 院长/副院长
                 * 学科带头人/学术骨干
                 * 科室主任/副主任中高层管理岗
                 */
                'CATEGORY_JOB' => [
                    30,
                    31,
                    32,
                    41,
                    44,
                    45,
                    46,
                    59,
                    60,
                    66,
                    67,
                    82,
                    83,
                    266,
                ],
                // 副高及以上职称
                'TITLE_TYPE'   => self::TITLE_VICE_SENIOR_CODE,
                'EDUCATION'    => [
                    self::EDUCATION_DOCTOR_CODE,
                ],
            ],
            [
                /**
                 * 助理教授/助理副教授；
                 * 专职教师/教学科研岗
                 * 助理研究员/研究实习员
                 * 专职科研岗
                 * 医学教学/科研人才
                 * 助理研究员/研究实习员
                 * 专职科研岗
                 * 主任医师/副主任医师
                 * 主治医师/住院医师/医生
                 */
                'CATEGORY_JOB' => [
                    259,
                    33,
                    262,
                    61,
                    64,
                    264,
                    68,
                    84,
                    85,
                ],
                'EDUCATION'    => [
                    self::EDUCATION_DOCTOR_CODE,
                ],
            ],

        ],
        // 教育经历

    ];

    // 海外优青
    const ABROAD_YOUQING_RULE = [
        BaseHomeColumn::ABROAD_YOUQING_ID => [
            'CATEGORY_JOB' => [
                277,
            ],
        ],
    ];

    // 引才活动
    const ABROAD_YINCAI_RULE = [
        'ATTRIBUTE' => [
            BaseArticleAttribute::ATTRIBUTE_HOME_ACTIVITY,
        ],
    ];
}