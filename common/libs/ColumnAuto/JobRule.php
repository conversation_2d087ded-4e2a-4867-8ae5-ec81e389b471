<?php
namespace common\libs\ColumnAuto;

class JobRule extends BaseRule
{

    // 单独单位类型
    const COMPANY_TYPE_RULE = [
        // 双一流院校
        self::COMPANY_TYPE_DOUBLE_TOP_COLLEGE_CODE           => [
            'name'       => '双一流院校',
            'columnId'   => '272',
            'columnName' => '双一流院校人才招聘',
        ],
        // 高职高专院校
        self::COMPANY_TYPE_HIGHER_VOCATIONAL_COLLEGE_CODE    => [
            'name'       => '高职高专',
            'columnId'   => '273',
            'columnName' => '高职高专人才招聘',
        ],
        // 党校与行政学院
        self::COMPANY_TYPE_PARTY_COLLEGE_CODE                => [
            'name'       => '党校（行政学院）',
            'columnId'   => '274',
            'columnName' => '党校（行政学院）招聘',
        ],
        // 中科院系统
        self::COMPANY_TYPE_CHINESE_ACADEMY_OF_SCIENCES_CODE  => [
            'name'       => '中国科学院系统研究机构',
            'columnId'   => '247',
            'columnName' => '中国科学院系统研究机构',
        ],
        // 人文社科研究机构（事业单位类型）
        self::COMPANY_TYPE_SOCIAL_SCIENCE_CODE               => [
            'name'       => '人文社科研究机构',
            'columnId'   => '248',
            'columnName' => '人文社科研究机构',
        ],
        // 自然与应用科研机构
        self::COMPANY_TYPE_NATURAL_AND_APPLIED_RESEARCH_CODE => [
            'name'       => '自然与应用科研机构',
            'columnId'   => '249',
            'columnName' => '自然与应用科研',
        ],
        // 企业研发机构
        self::COMPANY_TYPE_GENERAL_RESEARCH_CODE             => [
            'name'       => '企业研发机构',
            'columnId'   => '250',
            'columnName' => '企业研发机构招聘',
        ],
        // 中小学
        self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE => [
            'name'       => '中小学',
            'columnId'   => '244',
            'columnName' => '中小学自主招聘',
        ],
        // 中专&职业中学&技师学院
        self::COMPANY_TYPE_MIDDLE_VOCATIONAL_SCHOOL_CODE     => [
            'name'       => '中专&职业中学&技师学院',
            'columnId'   => '245',
            'columnName' => '职业中学招聘',
        ],
        // 幼儿园
        self::COMPANY_TYPE_KINDERGARTEN_CODE                 => [
            'name'       => '幼儿园教师招聘',
            'columnId'   => '246',
            'columnName' => '幼儿园自主招聘',
        ],
        // 医院
        self::COMPANY_TYPE_HOSPITAL_CODE                     => [
            'name'       => '医院',
            'columnId'   => '257',
            'columnName' => '医疗单位自主招聘',
        ],
        // 其他医疗机构
        self::COMPANY_TYPE_OTHER_MEDICAL_CODE                => [
            'name'       => '其他医疗机构',
            'columnId'   => '257',
            'columnName' => '医疗单位自主招聘',
        ],
        // 知名企业
        self::COMPANY_TYPE_FAMOUS_CODE                       => [
            'name'       => '知名企业',
            'columnId'   => '259',
            'columnName' => '知名企业自主招聘',
        ],
        // 政府国有企业
        self::COMPANY_TYPE_STATE_OWNED_CODE                  => [
            'name'       => '政府国有企业',
            'columnId'   => '260',
            'columnName' => '政府国有企业自主招聘',
        ],
        // 银行、信用社等金融机构
        self::COMPANY_TYPE_BANK_CODE                         => [
            'name'       => '银行、信用社等金融机构',
            'columnId'   => '261',
            'columnName' => '金融机构',
        ],
        // 中小成长型企业
        self::COMPANY_TYPE_SMALL_AND_MEDIUM_CODE             => [
            'name'       => '中小成长型企业',
            'columnId'   => '262',
            'columnName' => '中小创新型企业',
        ],
    ];
    // 单位类型+地区(市)
    const COMPANY_TYPE_AND_CITY_RULE = [
        '821'  => [
            'name' => '南京',
            'list' => [
                [
                    'columnId'    => '512',
                    'columnName'  => '南京高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,
                ],
                [
                    'columnId'    => '513',
                    'columnName'  => '南京机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '514',
                    'columnName'  => '南京中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,
                ],
                [
                    'columnId'    => '515',
                    'columnName'  => '南京科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                ],
                [
                    'columnId'    => '516',
                    'columnName'  => '南京医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,
                ],
                [
                    'columnId'    => '517',
                    'columnName'  => '南京企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '934'  => [
            'name' => '杭州',
            'list' => [
                [
                    'columnId'    => '524',
                    'columnName'  => '杭州高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,
                ],
                [
                    'columnId'    => '525',
                    'columnName'  => '杭州机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '526',
                    'columnName'  => '杭州中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,
                ],
                [
                    'columnId'    => '527',
                    'columnName'  => '杭州科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                ],
                [
                    'columnId'    => '528',
                    'columnName'  => '杭州医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,
                ],
                [
                    'columnId'    => '529',
                    'columnName'  => '杭州企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1710' => [
            'name' => '武汉',
            'list' => [
                [
                    'columnId'    => '506',
                    'columnName'  => '武汉高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,
                ],
                [
                    'columnId'    => '507',
                    'columnName'  => '武汉机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '508',
                    'columnName'  => '武汉中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,
                ],
                [
                    'columnId'    => '509',
                    'columnName'  => '武汉科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                ],
                [
                    'columnId'    => '510',
                    'columnName'  => '武汉医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,
                ],
                [
                    'columnId'    => '511',
                    'columnName'  => '武汉企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1965' => [
            'name' => '广州',
            'list' => [
                [
                    'columnId'    => '494',
                    'columnName'  => '广州高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,
                ],
                [
                    'columnId'    => '495',
                    'columnName'  => '广州机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '496',
                    'columnName'  => '广州中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,
                ],
                [
                    'columnId'    => '497',
                    'columnName'  => '广州科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                ],
                [
                    'columnId'    => '498',
                    'columnName'  => '广州医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,
                ],
                [
                    'columnId'    => '499',
                    'columnName'  => '广州企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1988' => [
            'name' => '深圳',
            'list' => [
                [
                    'columnId'    => '488',
                    'columnName'  => '深圳高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,
                ],
                [
                    'columnId'    => '489',
                    'columnName'  => '深圳机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '490',
                    'columnName'  => '深圳中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,
                ],
                [
                    'columnId'    => '491',
                    'columnName'  => '深圳科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                ],
                [
                    'columnId'    => '492',
                    'columnName'  => '深圳医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,
                ],
                [
                    'columnId'    => '493',
                    'columnName'  => '深圳企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '2368' => [
            'name' => '成都',
            'list' => [
                [
                    'columnId'    => '500',
                    'columnName'  => '成都高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,
                ],
                [
                    'columnId'    => '501',
                    'columnName'  => '成都机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '502',
                    'columnName'  => '成都中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,
                ],
                [
                    'columnId'    => '503',
                    'columnName'  => '成都科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                ],
                [
                    'columnId'    => '504',
                    'columnName'  => '成都医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,
                ],
                [
                    'columnId'    => '505',
                    'columnName'  => '成都企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '2899' => [
            'name' => '西安',
            'list' => [
                [
                    'columnId'    => '518',
                    'columnName'  => '西安高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,
                ],
                [
                    'columnId'    => '519',
                    'columnName'  => '西安机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '520',
                    'columnName'  => '西安中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,
                ],
                [
                    'columnId'    => '521',
                    'columnName'  => '西安科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
                ],
                [
                    'columnId'    => '522',
                    'columnName'  => '西安医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,
                ],
                [
                    'columnId'    => '523',
                    'columnName'  => '西安企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
    ];
    // 单位类型+地区(省)
    const COMPANY_TYPE_AND_PROVINCE_RULE = [
        '1'    => [
            'name' => '北京',
            'list' => [
                [
                    'columnId'    => '284',
                    'columnName'  => '北京高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '285',
                    'columnName'  => '北京机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '286',
                    'columnName'  => '北京中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '287',
                    'columnName'  => '北京科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '288',
                    'columnName'  => '北京医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '289',
                    'columnName'  => '北京企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '19'   => [
            'name' => '天津',
            'list' => [
                [
                    'columnId'    => '296',
                    'columnName'  => '天津高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '297',
                    'columnName'  => '天津机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '298',
                    'columnName'  => '天津中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '299',
                    'columnName'  => '天津科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '300',
                    'columnName'  => '天津医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '301',
                    'columnName'  => '天津企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '37'   => [
            'name' => '河北',
            'list' => [
                [
                    'columnId'    => '308',
                    'columnName'  => '河北高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '309',
                    'columnName'  => '河北机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '310',
                    'columnName'  => '河北中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '311',
                    'columnName'  => '河北科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '312',
                    'columnName'  => '河北医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '313',
                    'columnName'  => '河北企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '220'  => [
            'name' => '山西',
            'list' => [
                [
                    'columnId'    => '314',
                    'columnName'  => '山西高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '315',
                    'columnName'  => '山西机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '316',
                    'columnName'  => '山西中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '317',
                    'columnName'  => '山西科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '318',
                    'columnName'  => '山西医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '319',
                    'columnName'  => '山西企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '351'  => [
            'name' => '内蒙古',
            'list' => [
                [
                    'columnId'    => '320',
                    'columnName'  => '内蒙古高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '321',
                    'columnName'  => '内蒙古机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '322',
                    'columnName'  => '内蒙古中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '323',
                    'columnName'  => '内蒙古科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '324',
                    'columnName'  => '内蒙古医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '325',
                    'columnName'  => '内蒙古企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '466'  => [
            'name' => '辽宁',
            'list' => [
                [
                    'columnId'    => '326',
                    'columnName'  => '辽宁高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '327',
                    'columnName'  => '辽宁机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '328',
                    'columnName'  => '辽宁中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '329',
                    'columnName'  => '辽宁科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '330',
                    'columnName'  => '辽宁医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '331',
                    'columnName'  => '辽宁企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '585'  => [
            'name' => '吉林',
            'list' => [
                [
                    'columnId'    => '332',
                    'columnName'  => '吉林高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '333',
                    'columnName'  => '吉林机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '334',
                    'columnName'  => '吉林中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '335',
                    'columnName'  => '吉林科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '336',
                    'columnName'  => '吉林医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '337',
                    'columnName'  => '吉林企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '655'  => [
            'name' => '黑龙江',
            'list' => [
                [
                    'columnId'    => '338',
                    'columnName'  => '黑龙江高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '339',
                    'columnName'  => '黑龙江机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '340',
                    'columnName'  => '黑龙江中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '341',
                    'columnName'  => '黑龙江科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '342',
                    'columnName'  => '黑龙江医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '343',
                    'columnName'  => '黑龙江企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '801'  => [
            'name' => '上海',
            'list' => [
                [
                    'columnId'    => '290',
                    'columnName'  => '上海高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '291',
                    'columnName'  => '上海机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '292',
                    'columnName'  => '上海中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '293',
                    'columnName'  => '上海科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '294',
                    'columnName'  => '上海医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '295',
                    'columnName'  => '上海企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '820'  => [
            'name' => '江苏',
            'list' => [
                [
                    'columnId'    => '344',
                    'columnName'  => '江苏高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '345',
                    'columnName'  => '江苏机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '346',
                    'columnName'  => '江苏中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '347',
                    'columnName'  => '江苏科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '348',
                    'columnName'  => '江苏医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '349',
                    'columnName'  => '江苏企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '933'  => [
            'name' => '浙江',
            'list' => [
                [
                    'columnId'    => '350',
                    'columnName'  => '浙江高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '351',
                    'columnName'  => '浙江机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '352',
                    'columnName'  => '浙江中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '353',
                    'columnName'  => '浙江科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '354',
                    'columnName'  => '浙江医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '355',
                    'columnName'  => '浙江企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1046' => [
            'name' => '安徽',
            'list' => [
                [
                    'columnId'    => '356',
                    'columnName'  => '安徽高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '357',
                    'columnName'  => '安徽机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '358',
                    'columnName'  => '安徽中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '359',
                    'columnName'  => '安徽科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '360',
                    'columnName'  => '安徽医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '361',
                    'columnName'  => '安徽企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1168' => [
            'name' => '福建',
            'list' => [
                [
                    'columnId'    => '362',
                    'columnName'  => '福建高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '363',
                    'columnName'  => '福建机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '364',
                    'columnName'  => '福建中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '365',
                    'columnName'  => '福建科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '366',
                    'columnName'  => '福建医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '367',
                    'columnName'  => '福建企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1263' => [
            'name' => '江西',
            'list' => [
                [
                    'columnId'    => '368',
                    'columnName'  => '江西高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '369',
                    'columnName'  => '江西机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '370',
                    'columnName'  => '江西中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '371',
                    'columnName'  => '江西科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '372',
                    'columnName'  => '江西医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '373',
                    'columnName'  => '江西企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1375' => [
            'name' => '山东',
            'list' => [
                [
                    'columnId'    => '374',
                    'columnName'  => '山东高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '375',
                    'columnName'  => '山东机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '376',
                    'columnName'  => '山东中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '377',
                    'columnName'  => '山东科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '378',
                    'columnName'  => '山东医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '379',
                    'columnName'  => '山东企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1532' => [
            'name' => '河南',
            'list' => [
                [
                    'columnId'    => '380',
                    'columnName'  => '河南高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '381',
                    'columnName'  => '河南机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '382',
                    'columnName'  => '河南中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '383',
                    'columnName'  => '河南科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '384',
                    'columnName'  => '河南医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '385',
                    'columnName'  => '河南企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1709' => [
            'name' => '湖北',
            'list' => [
                [
                    'columnId'    => '386',
                    'columnName'  => '湖北高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '387',
                    'columnName'  => '湖北机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '388',
                    'columnName'  => '湖北中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '389',
                    'columnName'  => '湖北科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '390',
                    'columnName'  => '湖北医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '391',
                    'columnName'  => '湖北企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1827' => [
            'name' => '湖南',
            'list' => [
                [
                    'columnId'    => '392',
                    'columnName'  => '湖南高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '393',
                    'columnName'  => '湖南机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '394',
                    'columnName'  => '湖南中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '395',
                    'columnName'  => '湖南科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '396',
                    'columnName'  => '湖南医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '397',
                    'columnName'  => '湖南企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '1964' => [
            'name' => '广东',
            'list' => [
                [
                    'columnId'    => '398',
                    'columnName'  => '广东高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '399',
                    'columnName'  => '广东机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '400',
                    'columnName'  => '广东中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '401',
                    'columnName'  => '广东科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '402',
                    'columnName'  => '广东医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '403',
                    'columnName'  => '广东企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '2162' => [
            'name' => '广西',
            'list' => [
                [
                    'columnId'    => '404',
                    'columnName'  => '广西高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '405',
                    'columnName'  => '广西机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '406',
                    'columnName'  => '广西中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '407',
                    'columnName'  => '广西科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '408',
                    'columnName'  => '广西医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '409',
                    'columnName'  => '广西企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '2291' => [
            'name' => '海南',
            'list' => [
                [
                    'columnId'    => '410',
                    'columnName'  => '海南高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '411',
                    'columnName'  => '海南机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '412',
                    'columnName'  => '海南中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '413',
                    'columnName'  => '海南科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '414',
                    'columnName'  => '海南医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '415',
                    'columnName'  => '海南企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '2323' => [
            'name' => '重庆',
            'list' => [
                [
                    'columnId'    => '302',
                    'columnName'  => '重庆高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '303',
                    'columnName'  => '重庆机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '304',
                    'columnName'  => '重庆中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '305',
                    'columnName'  => '重庆科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '306',
                    'columnName'  => '重庆医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '307',
                    'columnName'  => '重庆企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '2367' => [
            'name' => '四川',
            'list' => [
                [
                    'columnId'    => '416',
                    'columnName'  => '四川高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '417',
                    'columnName'  => '四川机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '418',
                    'columnName'  => '四川中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '419',
                    'columnName'  => '四川科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '420',
                    'columnName'  => '四川医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '421',
                    'columnName'  => '四川企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '2572' => [
            'name' => '贵州',
            'list' => [
                [
                    'columnId'    => '422',
                    'columnName'  => '贵州高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '423',
                    'columnName'  => '贵州机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '424',
                    'columnName'  => '贵州中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '425',
                    'columnName'  => '贵州科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '426',
                    'columnName'  => '贵州医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '427',
                    'columnName'  => '贵州企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '2670' => [
            'name' => '云南',
            'list' => [
                [
                    'columnId'    => '428',
                    'columnName'  => '云南高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '429',
                    'columnName'  => '云南机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '430',
                    'columnName'  => '云南中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '431',
                    'columnName'  => '云南科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '432',
                    'columnName'  => '云南医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '433',
                    'columnName'  => '云南企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '2816' => [
            'name' => '西藏',
            'list' => [
                [
                    'columnId'    => '434',
                    'columnName'  => '西藏高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '435',
                    'columnName'  => '西藏机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '436',
                    'columnName'  => '西藏中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '437',
                    'columnName'  => '西藏科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '438',
                    'columnName'  => '西藏医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '439',
                    'columnName'  => '西藏企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '2898' => [
            'name' => '陕西',
            'list' => [
                [
                    'columnId'    => '440',
                    'columnName'  => '陕西高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '441',
                    'columnName'  => '陕西机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '442',
                    'columnName'  => '陕西中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '443',
                    'columnName'  => '陕西科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '444',
                    'columnName'  => '陕西医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '445',
                    'columnName'  => '陕西企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '3022' => [
            'name' => '甘肃',
            'list' => [
                [
                    'columnId'    => '446',
                    'columnName'  => '甘肃高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '447',
                    'columnName'  => '甘肃机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '448',
                    'columnName'  => '甘肃中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '449',
                    'columnName'  => '甘肃科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '450',
                    'columnName'  => '甘肃医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '451',
                    'columnName'  => '甘肃企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '3126' => [
            'name' => '青海',
            'list' => [
                [
                    'columnId'    => '452',
                    'columnName'  => '青海高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '453',
                    'columnName'  => '青海机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '454',
                    'columnName'  => '青海中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '455',
                    'columnName'  => '青海科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '456',
                    'columnName'  => '青海医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '457',
                    'columnName'  => '青海企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '3178' => [
            'name' => '宁夏',
            'list' => [
                [
                    'columnId'    => '458',
                    'columnName'  => '宁夏高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '459',
                    'columnName'  => '宁夏机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '460',
                    'columnName'  => '宁夏中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '461',
                    'columnName'  => '宁夏科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '462',
                    'columnName'  => '宁夏医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '463',
                    'columnName'  => '宁夏企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '3206' => [
            'name' => '新疆',
            'list' => [
                [
                    'columnId'    => '464',
                    'columnName'  => '新疆高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '465',
                    'columnName'  => '新疆机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '466',
                    'columnName'  => '新疆中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '467',
                    'columnName'  => '新疆科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '468',
                    'columnName'  => '新疆医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '469',
                    'columnName'  => '新疆企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '3325' => [
            'name' => '台湾',
            'list' => [
                [
                    'columnId'    => '470',
                    'columnName'  => '台湾高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '471',
                    'columnName'  => '台湾机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '472',
                    'columnName'  => '台湾中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '473',
                    'columnName'  => '台湾科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '474',
                    'columnName'  => '台湾医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '475',
                    'columnName'  => '台湾企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '3716' => [
            'name' => '香港',
            'list' => [
                [
                    'columnId'    => '476',
                    'columnName'  => '香港高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '477',
                    'columnName'  => '香港机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '478',
                    'columnName'  => '香港中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '479',
                    'columnName'  => '香港科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '480',
                    'columnName'  => '香港医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '481',
                    'columnName'  => '香港企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
        '3738' => [
            'name' => '澳门',
            'list' => [
                [
                    'columnId'    => '482',
                    'columnName'  => '澳门高校',
                    'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,

                ],
                [
                    'columnId'    => '483',
                    'columnName'  => '澳门机关事业',
                    'companyType' => self::COMPANY_ORGAN_TYPE_LIST,

                ],
                [
                    'columnId'    => '484',
                    'columnName'  => '澳门中小学',
                    'companyType' => self::COMPANY_SCHOOL_TYPE_LIST,

                ],
                [
                    'columnId'    => '485',
                    'columnName'  => '澳门科研',
                    'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,

                ],
                [
                    'columnId'    => '486',
                    'columnName'  => '澳门医学',
                    'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,

                ],
                [
                    'columnId'    => '487',
                    'columnName'  => '澳门企业',
                    'companyType' => self::COMPANY_GENERAL_TYPE_LIST,
                ],
            ],

        ],
    ];
    // 单位类型+职位类型
    const COMPANY_TYPE_AND_JOB_TYPE_RULE = [
        // 学科带头人、教授招聘
        '238' => [
            'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,
            /**
             * 教学岗（高等院校）    学科带头人/学术骨干
             * 教学岗（高等院校）    教授/副教授
             * 教学岗（高等院校）    学术领军人才
             * 科学研究岗（教育/科研/卫生单位）-科研领军人才
             */
            'categoryJob' => [
                31,
                32,
                30,
                59,
            ],
        ],
        // 中高层干部
        '239' => [
            'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,
            /**
             * 中高级管理岗（教育/科研机构）    高校校长/校领导/单位负责人
             * 中高级管理岗（教育/科研机构）    二级学院院长/副院长
             * 中高级管理岗（教育/科研机构）    系/研究所/实验室负责人
             * 中高级管理岗（教育/科研机构）    高校中层党政部门负责人
             */
            'categoryJob' => [
                41,
                44,
                45,
                46,
            ],
        ],
        // 教学科研人才
        '240' => [
            'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,
            /**
             * 教学岗（高等院校）    专职教师/教学科研岗    33
             * 博士后    博士后    29
             * 科学研究岗（教育/科研/卫生单位）    高级研究人员（正/副研究员）    60
             * 科学研究岗（教育/科研/卫生单位）    专职科研岗    61
             * 科学研究岗（教育/科研/卫生单位）    科研领军人才    59
             * 科学研究岗（教育/科研/卫生单位）    医学教学/科研人才    64
             */
            'categoryJob' => [
                33,
                29,
                60,
                61,
                64,
            ],
        ],
        // 教辅、行政、实验、助理人员招聘
        '241' => [
            'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,
            /**
             * 35    3    教学支撑岗（高等院校）    教务岗
             * 36    3    教学支撑岗（高等院校）    实验技术岗
             * 37    3    教学支撑岗（高等院校）    图书馆岗
             * 38    3    教学支撑岗（高等院校）    党务行政岗
             * 39    3    教学支撑岗（高等院校）    技术支撑岗
             * 40    3    教学支撑岗（高等院校）    其他支撑岗
             * 62    7    科学研究岗（教育/科研/卫生单位）    科研助理岗
             * 63    7    科学研究岗（教育/科研/卫生单位）    实验技术岗
             * 65    7    科学研究岗（教育/科研/卫生单位）    其他科研支撑岗
             */
            'categoryJob' => [
                35,
                36,
                37,
                38,
                39,
                40,
                62,
                63,
                65,
            ],

        ],
        // 高校辅导员
        '242' => [
            'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,
            /**
             * 教学支撑岗（高等院校）    辅导员岗
             */
            'categoryJob' => [34],
        ],
        // 教育系统统招
        '243' => [
            // 教育系统招考
            'companyType' => [
                self::COMPANY_TYPE_ADMIN_DEPARTMENT_CODE,
                self::COMPANY_TYPE_PUBLIC_INSTITUTION_CODE,
            ],
            /**
             * 中小学骨干教师岗
             * 中小学普通教师岗
             * 中职教师岗（公共课类）
             * 中职教师岗（专业课类）
             * 学前教师/幼师
             * 教务岗
             * 实验技术岗
             * 生活老师
             * 专职班主任/辅导员
             * 其他教学/行政支撑岗
             * 中小学校长/校领导/单位负责人
             * 中小学校中层党政部门负责人
             * 教研员
             * 幼儿园园长
             */
            'categoryJob' => [
                48,
                49,
                50,
                51,
                52,
                53,
                54,
                55,
                56,
                57,
                42,
                47,
                58,
                43,
            ],
        ],
        '244' => [
            'companyType' => [
                self::COMPANY_TYPE_DOUBLE_TOP_COLLEGE_CODE,
                self::COMPANY_TYPE_ORDINARY_UNDERGRADUATE_COLLEGE_CODE,
                self::COMPANY_TYPE_HIGHER_VOCATIONAL_COLLEGE_CODE,
            ],
            'categoryJob' => [
                48,
                49,
                53,
                54,
                55,
                56,
                57,
                42,
                47,
            ],
        ],
        // 幼儿园教师招聘
        '246' => [
            'companyType' => [
                self::COMPANY_TYPE_DOUBLE_TOP_COLLEGE_CODE,
                self::COMPANY_TYPE_ORDINARY_UNDERGRADUATE_COLLEGE_CODE,
                self::COMPANY_TYPE_HIGHER_VOCATIONAL_COLLEGE_CODE,
            ],
            'categoryJob' => [
                52,
                43,
                57,
            ],
        ],
        // 卫生系统统一招聘
        '256' => [
            // 事业单位
            'companyType' => [
                self::COMPANY_TYPE_ADMIN_DEPARTMENT_CODE,
                self::COMPANY_TYPE_PUBLIC_INSTITUTION_CODE,
            ],
            /**
             * 医疗卫生专业岗-主治医师/住院医师/医生
             * 医疗卫生专业岗-主任医师、副主任医师
             * 医疗卫生专业岗-普通医技岗
             * 医疗卫生专业岗-普通药师岗
             * 医疗卫生专业岗-普通护理岗
             * 医疗卫生专业岗-中高级医技岗
             * 医疗卫生专业岗-中高级药师岗
             * 医疗卫生专业岗-中高级护理岗
             * 医疗卫生专业岗-学科带头人/学术骨干
             * 医疗卫生专业岗-院长/副院长
             * 医疗卫生专业岗-规培岗
             * 医疗卫生专业岗-党务行政岗
             * 医疗卫生专业岗-校医人员
             * 医疗卫生专业岗-医务医辅人员
             * 医疗卫生专业岗-其他医疗卫生岗
             */
            'categoryJob' => [
                64,
                82,
                83,
                84,
                85,
                86,
                87,
                88,
                89,
                90,
                91,
                92,
                93,
                94,
                96,
            ],
        ],
        // 医卫院校（院系）招聘
        '258' => [
            /**
             * 双一流院校
             * 普通本科院校
             * 高职高专院校
             * 党校与行政学院；
             * 中小学；
             * 中专&职业中学&技师学院；
             * 幼儿园；
             * 中科院系统；
             * 人文社科研究机构（事业单位类型）；
             * 自然与应用科研机构（事业单位类型）；
             */
            'companyType' => [
                self::COMPANY_TYPE_DOUBLE_TOP_COLLEGE_CODE,
                self::COMPANY_TYPE_ORDINARY_UNDERGRADUATE_COLLEGE_CODE,
                self::COMPANY_TYPE_HIGHER_VOCATIONAL_COLLEGE_CODE,
                self::COMPANY_TYPE_PARTY_COLLEGE_CODE,
                self::COMPANY_TYPE_PRIMARY_AND_SECONDARY_SCHOOL_CODE,
                self::COMPANY_TYPE_MIDDLE_VOCATIONAL_SCHOOL_CODE,
                self::COMPANY_TYPE_KINDERGARTEN_CODE,
                self::COMPANY_TYPE_CHINESE_ACADEMY_OF_SCIENCES_CODE,
                self::COMPANY_TYPE_SOCIAL_SCIENCE_CODE,
                self::COMPANY_TYPE_NATURAL_AND_APPLIED_RESEARCH_CODE,
            ],
            // 医疗卫生专业岗
            /**
             *
             * 中科院系统；
             * 人文社科研究机构（事业单位类型）；
             * 自然与应用科研机构（事业单位类型）；
             * 企业研发机构；
             * 双一流院校；
             * 普通本科院校；
             * 高职高专院校；
             * 党校与行政学院；
             * 中小学；
             * 中专&职业中学&技师学院；
             * 幼儿园；
             */
            'categoryJob' => [
                64,
                82,
                83,
                84,
                85,
                86,
                87,
                88,
                89,
                90,
                91,
                92,
                93,
                94,
                96,
                95,
            ],
        ],
        // 高校博士后
        '263' => [
            'companyType' => self::COMPANY_COLLEGE_TYPE_LIST,
            'categoryJob' => [self::CATEGORY_JOB_POSTDOC],
        ],
        '264' => [
            'companyType' => self::COMPANY_RESEARCH_INSTITUTION_TYPE_LIST,
            'categoryJob' => [
                self::CATEGORY_JOB_POSTDOC,
                self::CATEGORY_JOB_SPECIAL_RESEARCH_ASSISTANT,
            ],
        ],
        '265' => [
            'companyType' => self::COMPANY_GENERAL_AND_ORGAN_TYPE_LIST,
            'categoryJob' => [self::CATEGORY_JOB_POSTDOC],
        ],
        '266' => [
            'companyType' => self::COMPANY_MEDICAL_TYPE_LIST,
            'categoryJob' => [self::CATEGORY_JOB_POSTDOC],
        ],
    ];
    // 单独地区(这里的id是地区id,里面才是栏目id)
    /**
     *38    石家庄
     * 61    唐山
     * 221    太原
     * 352    呼和浩特
     * 362    包头
     * 467    沈阳
     * 481    大连
     * 586    长春
     * 597    吉林
     * 656    哈尔滨
     * 833    无锡
     * 842    徐州
     * 853    常州
     * 861    苏州
     * 871    南通
     * 880    连云港
     * 906    扬州
     * 913    镇江
     * 920    泰州
     * 948    宁波
     * 960    温州
     * 972    嘉兴
     * 980    湖州
     * 986    绍兴
     * 1010    舟山
     * 1015    台州
     * 1047    合肥
     * 1057    芜湖
     * 1081    马鞍山
     * 1169    福州
     * 1183    厦门
     * 1209    泉州
     * 1222    漳州
     * 1264    南昌
     * 1285    九江
     * 1376    济南
     * 1387    青岛
     * 1421    烟台
     * 1447    济宁
     * 1466    威海
     * 1479    临沂
     * 1533    郑州
     * 1556    洛阳
     * 1740    宜昌
     * 1754    襄阳
     * 1828    长沙
     * 1999    珠海
     * 2003    汕头
     * 2011    佛山
     * 2025    湛江
     * 2050    惠州
     * 2091    东莞
     * 2123    中山
     * 2163    南宁
     * 2177    柳州
     * 2189    桂林
     * 2215    北海
     * 2292    海口
     * 2416    绵阳
     * 2573    贵阳
     * 2589    遵义
     * 2671    昆明
     * 2817    拉萨
     * 3023    兰州
     * 3127    西宁
     * 3179    银川
     * 3207    乌鲁木齐
     * 3326    台湾
     * 3717    香港
     * 3739    澳门
     */
    const CITY_RULE = [
        '38'   => [
            'id'   => 101,
            'name' => '石家庄',
        ],
        '61'   => [
            'id'   => 102,
            'name' => '唐山',
        ],
        '221'  => [
            'id'   => 100,
            'name' => '太原',
        ],
        '352'  => [
            'id'   => 104,
            'name' => '呼和浩特',
        ],
        '362'  => [
            'id'   => 103,
            'name' => '包头',
        ],
        '467'  => [
            'id'   => 113,
            'name' => '沈阳',
        ],
        '481'  => [
            'id'   => 112,
            'name' => '大连',
        ],
        '586'  => [
            'id'   => 109,
            'name' => '长春',
        ],
        '597'  => [
            'id'   => 19,
            'name' => '吉林',
        ],
        '656'  => [
            'id'   => 111,
            'name' => '哈尔滨',
        ],
        '833'  => [
            'id'   => 57,
            'name' => '无锡',
        ],
        '842'  => [
            'id'   => 58,
            'name' => '徐州',
        ],
        '853'  => [
            'id'   => 52,
            'name' => '常州',
        ],
        '861'  => [
            'id'   => 55,
            'name' => '苏州',
        ],
        '871'  => [
            'id'   => 54,
            'name' => '南通',
        ],
        '880'  => [
            'id'   => 53,
            'name' => '连云港',
        ],
        '906'  => [
            'id'   => 59,
            'name' => '扬州',
        ],
        '913'  => [
            'id'   => 60,
            'name' => '镇江',
        ],
        '920'  => [
            'id'   => 56,
            'name' => '泰州',
        ],
        '948'  => [
            'id'   => 63,
            'name' => '宁波',
        ],
        '960'  => [
            'id'   => 66,
            'name' => '温州',
        ],
        '972'  => [
            'id'   => 62,
            'name' => '嘉兴',
        ],
        '980'  => [
            'id'   => 61,
            'name' => '湖州',
        ],
        '986'  => [
            'id'   => 64,
            'name' => '绍兴',
        ],
        '1010' => [
            'id'   => 67,
            'name' => '舟山',
        ],
        '1015' => [
            'id'   => 65,
            'name' => '台州',
        ],
        '1047' => [
            'id'   => 68,
            'name' => '合肥',
        ],
        '1057' => [
            'id'   => 70,
            'name' => '芜湖',
        ],
        '1081' => [
            'id'   => 69,
            'name' => '马鞍山',
        ],
        '1169' => [
            'id'   => 71,
            'name' => '福州',
        ],
        '1183' => [
            'id'   => 73,
            'name' => '厦门',
        ],
        '1209' => [
            'id'   => 72,
            'name' => '泉州',
        ],
        '1222' => [
            'id'   => 74,
            'name' => '漳州',
        ],
        '1264' => [
            'id'   => 76,
            'name' => '南昌',
        ],
        '1285' => [
            'id'   => 75,
            'name' => '九江',
        ],
        '1376' => [
            'id'   => 78,
            'name' => '济南',
        ],
        '1387' => [
            'id'   => 80,
            'name' => '青岛',
        ],
        '1421' => [
            'id'   => 82,
            'name' => '烟台',
        ],
        '1447' => [
            'id'   => 77,
            'name' => '济宁',
        ],
        '1466' => [
            'id'   => 81,
            'name' => '威海',
        ],
        '1479' => [
            'id'   => 79,
            'name' => '临沂',
        ],
        '1533' => [
            'id'   => 87,
            'name' => '郑州',
        ],
        '1556' => [
            'id'   => 86,
            'name' => '洛阳',
        ],
        '1740' => [
            'id'   => 84,
            'name' => '宜昌',
        ],
        '1754' => [
            'id'   => 83,
            'name' => '襄阳',
        ],
        '1828' => [
            'id'   => 85,
            'name' => '长沙',
        ],
        '1999' => [
            'id'   => 96,
            'name' => '珠海',
        ],
        '2003' => [
            'id'   => 95,
            'name' => '汕头',
        ],
        '2011' => [
            'id'   => 93,
            'name' => '佛山',
        ],
        '2025' => [
            'id'   => 98,
            'name' => '湛江',
        ],
        '2050' => [
            'id'   => 94,
            'name' => '惠州',
        ],
        '2091' => [
            'id'   => 92,
            'name' => '东莞',
        ],
        '2123' => [
            'id'   => 97,
            'name' => '中山',
        ],
        '2163' => [
            'id'   => 91,
            'name' => '南宁',
        ],
        '2177' => [
            'id'   => 90,
            'name' => '柳州',
        ],
        '2189' => [
            'id'   => 89,
            'name' => '桂林',
        ],
        '2215' => [
            'id'   => 88,
            'name' => '北海',
        ],
        '2292' => [
            'id'   => 99,
            'name' => '海口',
        ],
        '2416' => [
            'id'   => 117,
            'name' => '绵阳',
        ],
        '2573' => [
            'id'   => 114,
            'name' => '贵阳',
        ],
        '2589' => [
            'id'   => 115,
            'name' => '遵义',
        ],
        '2671' => [
            'id'   => 116,
            'name' => '昆明',
        ],
        '2817' => [
            'id'   => 118,
            'name' => '拉萨',
        ],
        '3023' => [
            'id'   => 107,
            'name' => '兰州',
        ],
        '3127' => [
            'id'   => 105,
            'name' => '西宁',
        ],
        '3179' => [
            'id'   => 106,
            'name' => '银川',
        ],
        '3207' => [
            'id'   => 108,
            'name' => '乌鲁木齐',
        ],
        '3326' => [
            'id'   => 42,
            'name' => '台湾',
        ],
        '3717' => [
            'id'   => 43,
            'name' => '香港',
        ],
        '3739' => [
            'id'   => 44,
            'name' => '澳门',
        ],
    ];
    // 地区+(学历/海归)
    const AREA_AND_EDUCATION_ABROAD_RULE = [
        // 华东海归人才
        '267' => [
            /**
             * 江苏、浙江、上海、福建、安徽、江西、山东、台湾
             */
            'areaId'    => [
                801,
                820,
                933,
                1046,
                1168,
                1263,
                1375,
                3325,
            ],
            'education' => [self::EDUCATION_DOCTOR_CODE],
        ],
        // 华中、华南海归人才
        '268' => [
            /**
             * 河南
             * 湖北
             * 湖南
             * 广东
             * 广西
             * 海南
             * 香港
             * 澳门
             */
            'areaId'    => [
                1532,
                1709,
                1827,
                1964,
                2162,
                2291,
                3716,
                3738,
            ],
            'education' => [self::EDUCATION_DOCTOR_CODE],
        ],
        // 华北、东北海归人才
        '269' => [
            /**
             * 北京
             * 天津
             * 河北
             * 山西
             * 内蒙古
             * 辽宁
             * 吉林
             * 黑龙江
             */
            'areaId'    => [
                1,
                19,
                37,
                220,
                351,
                466,
                585,
                655,
            ],
            'education' => [self::EDUCATION_DOCTOR_CODE],
        ],
        // 西南、西北海归人才
        '270' => [
            /**
             * 重庆
             * 四川
             * 贵州
             * 云南
             * 西藏
             * 陕西
             * 甘肃
             * 青海
             * 宁夏
             * 新疆
             */
            'areaId'    => [
                2323,
                2367,
                2572,
                2670,
                2816,
                2898,
                3022,
                3126,
                3178,
                3206,
            ],
            'education' => [self::EDUCATION_DOCTOR_CODE],
        ],
    ];
    // 单独栏目(只要公告是在这个栏目下面,职位就在这个栏目下面)
    // 高校2023年度人才招聘
    // 政府引才活动
    // 公务员招考
    // 遴选与选调
    // 机关与事业单位统一招考
    // 机关与事业单位自主招聘
    // 军队武警招聘
    // 招聘会专场
    const COLUMN_RULE = [
        277,
        276,
        251,
        252,
        253,
        254,
        255,
        530,
    ];
    // 栏目/地区(3870)
    const COLUMN_OR_AREA_RULE = [
        // 海外地区人才招聘
        '271' => [
            'columnId' => 271,
            // 海外
            'areaId'   => [3870],
        ],
    ];
    // 学科
    const MAJOR_RULE = [
        '1' => [
            'name'       => '哲学',
            'columnId'   => '233',
            'columnName' => '人文社科',
        ],
        '2' => [
            'name'       => '经济学',
            'columnId'   => '233',
            'columnName' => '人文社科',
        ],
        '3' => [
            'name'       => '法学',
            'columnId'   => '233',
            'columnName' => '人文社科',
        ],
        '4' => [
            'name'       => '教育学',
            'columnId'   => '233',
            'columnName' => '人文社科',
        ],
        '5' => [
            'name'       => '文学',
            'columnId'   => '233',
            'columnName' => '人文社科',
        ],
        '6' => [
            'name'       => '历史学',
            'columnId'   => '233',
            'columnName' => '人文社科',
        ],

        '12' => [
            'name'       => '管理学',
            'columnId'   => '233',
            'columnName' => '人文社科',
        ],
        '13' => [
            'name'       => '艺术学',
            'columnId'   => '233',
            'columnName' => '人文社科',
        ],
        '7'  => [
            'name'       => '理学',
            'columnId'   => '234',
            'columnName' => '理工农医',
        ],
        '8'  => [
            'name'       => '工学',
            'columnId'   => '234',
            'columnName' => '理工农医',
        ],
        '9'  => [
            'name'       => '农学',
            'columnId'   => '234',
            'columnName' => '理工农医',
        ],
        '10' => [
            'name'       => '医学',
            'columnId'   => '234',
            'columnName' => '理工农医',
        ],

        '16'  => [
            'name'       => '哲学',
            'columnId'   => '119',
            'columnName' => '哲学',
        ],
        '17'  => [
            'name'       => '理论经济学',
            'columnId'   => '120',
            'columnName' => '理论经济学',
        ],
        '18'  => [
            'name'       => '应用经济学',
            'columnId'   => '121',
            'columnName' => '应用经济学',
        ],
        '19'  => [
            'name'       => '法学',
            'columnId'   => '122',
            'columnName' => '法学',
        ],
        '20'  => [
            'name'       => '政治学',
            'columnId'   => '123',
            'columnName' => '政治学',
        ],
        '21'  => [
            'name'       => '社会学',
            'columnId'   => '124',
            'columnName' => '社会学',
        ],
        '22'  => [
            'name'       => '民族学',
            'columnId'   => '125',
            'columnName' => '民族学',
        ],
        '23'  => [
            'name'       => '马克思主义理论',
            'columnId'   => '126',
            'columnName' => '马克思主义理论',
        ],
        '24'  => [
            'name'       => '公安学',
            'columnId'   => '127',
            'columnName' => '公安学',
        ],
        '25'  => [
            'name'       => '教育学',
            'columnId'   => '128',
            'columnName' => '教育学',
        ],
        '26'  => [
            'name'       => '心理学',
            'columnId'   => '129',
            'columnName' => '心理学',
        ],
        '27'  => [
            'name'       => '体育学',
            'columnId'   => '130',
            'columnName' => '体育学',
        ],
        '28'  => [
            'name'       => '中国语言文学',
            'columnId'   => '131',
            'columnName' => '中国语言文学',
        ],
        '29'  => [
            'name'       => '外国语言文学',
            'columnId'   => '132',
            'columnName' => '外国语言文学',
        ],
        '30'  => [
            'name'       => '新闻传播学',
            'columnId'   => '133',
            'columnName' => '新闻传播学',
        ],
        '31'  => [
            'name'       => '考古学',
            'columnId'   => '134',
            'columnName' => '考古学',
        ],
        '32'  => [
            'name'       => '中国史',
            'columnId'   => '135',
            'columnName' => '中国史',
        ],
        '33'  => [
            'name'       => '世界史',
            'columnId'   => '136',
            'columnName' => '世界史',
        ],
        '34'  => [
            'name'       => '数学',
            'columnId'   => '137',
            'columnName' => '数学',
        ],
        '35'  => [
            'name'       => '物理学',
            'columnId'   => '138',
            'columnName' => '物理学',
        ],
        '36'  => [
            'name'       => '化学',
            'columnId'   => '139',
            'columnName' => '化学',
        ],
        '37'  => [
            'name'       => '天文学',
            'columnId'   => '140',
            'columnName' => '天文学',
        ],
        '38'  => [
            'name'       => '地理学',
            'columnId'   => '141',
            'columnName' => '地理学',
        ],
        '39'  => [
            'name'       => '大气科学',
            'columnId'   => '142',
            'columnName' => '大气科学',
        ],
        '40'  => [
            'name'       => '海洋科学',
            'columnId'   => '143',
            'columnName' => '海洋科学',
        ],
        '41'  => [
            'name'       => '地球物理学',
            'columnId'   => '144',
            'columnName' => '地球物理学',
        ],
        '42'  => [
            'name'       => '地质学',
            'columnId'   => '145',
            'columnName' => '地质学',
        ],
        '43'  => [
            'name'       => '生物学',
            'columnId'   => '146',
            'columnName' => '生物学',
        ],
        '44'  => [
            'name'       => '系统科学',
            'columnId'   => '147',
            'columnName' => '系统科学',
        ],
        '45'  => [
            'name'       => '科学技术史',
            'columnId'   => '148',
            'columnName' => '科学技术史',
        ],
        '46'  => [
            'name'       => '生态学',
            'columnId'   => '149',
            'columnName' => '生态学',
        ],
        '47'  => [
            'name'       => '统计学',
            'columnId'   => '150',
            'columnName' => '统计学',
        ],
        '48'  => [
            'name'       => '力学',
            'columnId'   => '151',
            'columnName' => '力学',
        ],
        '49'  => [
            'name'       => '机械工程',
            'columnId'   => '152',
            'columnName' => '机械工程',
        ],
        '50'  => [
            'name'       => '光学工程',
            'columnId'   => '153',
            'columnName' => '光学工程',
        ],
        '51'  => [
            'name'       => '仪器科学与技术',
            'columnId'   => '154',
            'columnName' => '仪器科学与技术',
        ],
        '52'  => [
            'name'       => '材料科学与工程',
            'columnId'   => '155',
            'columnName' => '材料科学与工程',
        ],
        '53'  => [
            'name'       => '冶金工程',
            'columnId'   => '156',
            'columnName' => '冶金工程',
        ],
        '54'  => [
            'name'       => '动力工程及工程热物理',
            'columnId'   => '157',
            'columnName' => '动力工程及工程热物理',
        ],
        '55'  => [
            'name'       => '电气工程',
            'columnId'   => '158',
            'columnName' => '电气工程',
        ],
        '56'  => [
            'name'       => '电子科学与技术',
            'columnId'   => '159',
            'columnName' => '电子科学与技术',
        ],
        '57'  => [
            'name'       => '信息与通信工程',
            'columnId'   => '160',
            'columnName' => '信息与通信工程',
        ],
        '58'  => [
            'name'       => '控制科学与工程',
            'columnId'   => '161',
            'columnName' => '控制科学与工程',
        ],
        '59'  => [
            'name'       => '计算机科学与技术',
            'columnId'   => '162',
            'columnName' => '计算机科学与技术',
        ],
        '60'  => [
            'name'       => '建筑学',
            'columnId'   => '163',
            'columnName' => '建筑学',
        ],
        '61'  => [
            'name'       => '土木工程',
            'columnId'   => '164',
            'columnName' => '土木工程',
        ],
        '62'  => [
            'name'       => '水利工程',
            'columnId'   => '165',
            'columnName' => '水利工程',
        ],
        '63'  => [
            'name'       => '测绘科学与技术',
            'columnId'   => '166',
            'columnName' => '测绘科学与技术',
        ],
        '64'  => [
            'name'       => '化学工程与技术',
            'columnId'   => '167',
            'columnName' => '化学工程与技术',
        ],
        '65'  => [
            'name'       => '地质资源与地质工程',
            'columnId'   => '168',
            'columnName' => '地质资源与地质工程',
        ],
        '66'  => [
            'name'       => '矿业工程',
            'columnId'   => '169',
            'columnName' => '矿业工程',
        ],
        '67'  => [
            'name'       => '石油与天然气工程',
            'columnId'   => '170',
            'columnName' => '石油与天然气工程',
        ],
        '68'  => [
            'name'       => '纺织科学与工程',
            'columnId'   => '171',
            'columnName' => '纺织科学与工程',
        ],
        '69'  => [
            'name'       => '轻工技术与工程',
            'columnId'   => '172',
            'columnName' => '轻工技术与工程',
        ],
        '70'  => [
            'name'       => '交通运输工程',
            'columnId'   => '173',
            'columnName' => '交通运输工程',
        ],
        '71'  => [
            'name'       => '船舶与海洋工程',
            'columnId'   => '174',
            'columnName' => '船舶与海洋工程',
        ],
        '72'  => [
            'name'       => '航空宇航科学与技术',
            'columnId'   => '175',
            'columnName' => '航空宇航科学与技术',
        ],
        '73'  => [
            'name'       => '兵器科学与技术',
            'columnId'   => '176',
            'columnName' => '兵器科学与技术',
        ],
        '74'  => [
            'name'       => '核科学与技术',
            'columnId'   => '177',
            'columnName' => '核科学与技术',
        ],
        '75'  => [
            'name'       => '农业工程',
            'columnId'   => '178',
            'columnName' => '农业工程',
        ],
        '76'  => [
            'name'       => '林业工程',
            'columnId'   => '179',
            'columnName' => '林业工程',
        ],
        '77'  => [
            'name'       => '环境科学与工程',
            'columnId'   => '180',
            'columnName' => '环境科学与工程',
        ],
        '78'  => [
            'name'       => '生物医学工程',
            'columnId'   => '181',
            'columnName' => '生物医学工程',
        ],
        '79'  => [
            'name'       => '食品科学与工程',
            'columnId'   => '182',
            'columnName' => '食品科学与工程',
        ],
        '80'  => [
            'name'       => '城乡规划学',
            'columnId'   => '183',
            'columnName' => '城乡规划学',
        ],
        '81'  => [
            'name'       => '风景园林学',
            'columnId'   => '184',
            'columnName' => '风景园林学',
        ],
        '82'  => [
            'name'       => '软件工程',
            'columnId'   => '185',
            'columnName' => '软件工程',
        ],
        '83'  => [
            'name'       => '生物工程',
            'columnId'   => '186',
            'columnName' => '生物工程',
        ],
        '84'  => [
            'name'       => '安全科学与工程',
            'columnId'   => '187',
            'columnName' => '安全科学与工程',
        ],
        '85'  => [
            'name'       => '公安技术',
            'columnId'   => '188',
            'columnName' => '公安技术',
        ],
        '86'  => [
            'name'       => '网络空间安全',
            'columnId'   => '189',
            'columnName' => '网络空间安全',
        ],
        // '87'  => [
        //     'name'       => '电子信息',
        //     'columnId'   => '190',
        //     'columnName' => '电子信息',
        // ],
        '88'  => [
            'name'       => '作物学',
            'columnId'   => '191',
            'columnName' => '作物学',
        ],
        '89'  => [
            'name'       => '园艺学',
            'columnId'   => '192',
            'columnName' => '园艺学',
        ],
        '90'  => [
            'name'       => '农业资源利用',
            'columnId'   => '193',
            'columnName' => '农业资源利用',
        ],
        '91'  => [
            'name'       => '植物保护',
            'columnId'   => '194',
            'columnName' => '植物保护',
        ],
        '92'  => [
            'name'       => '畜牧学',
            'columnId'   => '195',
            'columnName' => '畜牧学',
        ],
        '93'  => [
            'name'       => '兽医学',
            'columnId'   => '196',
            'columnName' => '兽医学',
        ],
        '94'  => [
            'name'       => '林学',
            'columnId'   => '197',
            'columnName' => '林学',
        ],
        '95'  => [
            'name'       => '水产',
            'columnId'   => '198',
            'columnName' => '水产',
        ],
        '96'  => [
            'name'       => '草学',
            'columnId'   => '199',
            'columnName' => '草学',
        ],
        '97'  => [
            'name'       => '基础医学',
            'columnId'   => '200',
            'columnName' => '基础医学',
        ],
        '98'  => [
            'name'       => '临床医学',
            'columnId'   => '201',
            'columnName' => '临床医学',
        ],
        '99'  => [
            'name'       => '口腔医学',
            'columnId'   => '202',
            'columnName' => '口腔医学',
        ],
        '100' => [
            'name'       => '公共卫生与预防医学',
            'columnId'   => '203',
            'columnName' => '公共卫生与预防医学',
        ],
        '101' => [
            'name'       => '中医学',
            'columnId'   => '204',
            'columnName' => '中医学',
        ],
        '102' => [
            'name'       => '中西医结合',
            'columnId'   => '205',
            'columnName' => '中西医结合',
        ],
        '103' => [
            'name'       => '药学',
            'columnId'   => '206',
            'columnName' => '药学',
        ],
        '104' => [
            'name'       => '中药学',
            'columnId'   => '207',
            'columnName' => '中药学',
        ],
        '105' => [
            'name'       => '特种医学',
            'columnId'   => '208',
            'columnName' => '特种医学',
        ],
        '106' => [
            'name'       => '医学技术',
            'columnId'   => '209',
            'columnName' => '医学技术',
        ],
        '107' => [
            'name'       => '护理学',
            'columnId'   => '210',
            'columnName' => '护理学',
        ],
        '108' => [
            'name'       => '军事思想及军事历史',
            'columnId'   => '211',
            'columnName' => '军事思想及军事历史',
        ],
        '109' => [
            'name'       => '战略学',
            'columnId'   => '212',
            'columnName' => '战略学',
        ],
        '110' => [
            'name'       => '战役学',
            'columnId'   => '213',
            'columnName' => '战役学',
        ],
        '111' => [
            'name'       => '战术学',
            'columnId'   => '214',
            'columnName' => '战术学',
        ],
        '112' => [
            'name'       => '军队指挥学',
            'columnId'   => '215',
            'columnName' => '军队指挥学',
        ],
        '113' => [
            'name'       => '军事管理学',
            'columnId'   => '216',
            'columnName' => '军事管理学',
        ],
        '114' => [
            'name'       => '军队政治工作学',
            'columnId'   => '217',
            'columnName' => '军队政治工作学',
        ],
        '115' => [
            'name'       => '军事后勤学',
            'columnId'   => '218',
            'columnName' => '军事后勤学',
        ],
        '116' => [
            'name'       => '军事装备学',
            'columnId'   => '219',
            'columnName' => '军事装备学',
        ],
        '117' => [
            'name'       => '军事训练学',
            'columnId'   => '220',
            'columnName' => '军事训练学',
        ],
        '118' => [
            'name'       => '管理科学与工程',
            'columnId'   => '221',
            'columnName' => '管理科学与工程',
        ],
        '119' => [
            'name'       => '工商管理',
            'columnId'   => '222',
            'columnName' => '工商管理',
        ],
        '120' => [
            'name'       => '农林经济管理',
            'columnId'   => '223',
            'columnName' => '农林经济管理',
        ],
        '121' => [
            'name'       => '公共管理',
            'columnId'   => '224',
            'columnName' => '公共管理',
        ],
        '122' => [
            'name'       => '图书馆、情报与档案管理',
            'columnId'   => '225',
            'columnName' => '图书馆、情报与档案管理',
        ],
        '123' => [
            'name'       => '艺术学理论',
            'columnId'   => '226',
            'columnName' => '艺术学理论',
        ],
        '124' => [
            'name'       => '音乐与舞蹈学',
            'columnId'   => '227',
            'columnName' => '音乐与舞蹈学',
        ],
        '125' => [
            'name'       => '戏剧与影视学',
            'columnId'   => '228',
            'columnName' => '戏剧与影视学',
        ],
        '126' => [
            'name'       => '美术学',
            'columnId'   => '229',
            'columnName' => '美术学',
        ],
        '127' => [
            'name'       => '设计学',
            'columnId'   => '230',
            'columnName' => '设计学',
        ],
        '128' => [
            'name'       => '集成电路科学与工程',
            'columnId'   => '231',
            'columnName' => '集成电路科学与工程',
        ],
        '129' => [
            'name'       => '国家安全学',
            'columnId'   => '232',
            'columnName' => '国家安全学',
        ],
        '130' => [
            'name'       => '专业未分类',
            'columnId'   => '236',
            'columnName' => '专业未分类',
        ],
        '712' => [
            'name'       => '理科大类',
            'columnId'   => '532',
            'columnName' => '理科大类',
        ],
        '713' => [
            'name'       => '工科大类',
            'columnId'   => '531',
            'columnName' => '工科大类',
        ],
        '716' => [
            'name'       => '专业不限',
            'columnId'   => '235',
            'columnName' => '专业不限',
        ],
        // 专业数字经济 专业id为 748 栏目id为 533
        // 添加了专业翻译 专业id为 750 栏目id为 534
        // 添加了专业纳米科学与工程 专业id为 774 栏目id为 535
        // 添加了专业智能科学与技术 专业id为 776 栏目id为 536
        // 添加了专业人工智能 专业id为 778 栏目id为 537
        // 添加了专业遥感科学与技术 专业id为 780 栏目id为 538
        // 添加了专业旅游与酒店管理 专业id为 782 栏目id为 539
        // 添加了专业区域国别学 专业id为 785 栏目id为 540
        '748' => [
            'name'       => '数字经济',
            'columnId'   => '533',
            'columnName' => '数字经济',
        ],
        '750' => [
            'name'       => '翻译',
            'columnId'   => '534',
            'columnName' => '翻译',
        ],
        '774' => [
            'name'       => '纳米科学与工程',
            'columnId'   => '535',
            'columnName' => '纳米科学与工程',
        ],
        '776' => [
            'name'       => '智能科学与技术',
            'columnId'   => '536',
            'columnName' => '智能科学与技术',
        ],
        '778' => [
            'name'       => '人工智能',
            'columnId'   => '537',
            'columnName' => '人工智能',
        ],
        '780' => [
            'name'       => '遥感科学与技术',
            'columnId'   => '538',
            'columnName' => '遥感科学与技术',
        ],
        '782' => [
            'name'       => '旅游与酒店管理',
            'columnId'   => '539',
            'columnName' => '旅游与酒店管理',
        ],
        '785' => [
            'name'       => '区域国别学',
            'columnId'   => '540',
            'columnName' => '区域国别学',
        ],
        '802' => [
            'name'       => '金融学',
            'columnId'   => '545',
            'columnName' => '金融学',
        ],
        '804' => [
            'name'       => '能源动力',
            'columnId'   => '546',
            'columnName' => '能源动力',
        ],
        '812' => [
            'name'       => '审计',
            'columnId'   => '547',
            'columnName' => '审计',
        ],
    ];
    // 学历
    const EDUCATION_RULE = [
        // 博士人才招聘
        '275' => [
            'education' => [self::EDUCATION_DOCTOR_CODE],
        ],
    ];

}