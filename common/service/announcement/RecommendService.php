<?php

namespace common\service\announcement;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseArticleColumn;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use yii\base\Exception;

class RecommendService extends BaseService
{
    const LIMIT_DAY = 5;
    private $surplusNum;
    private $announcementInfo;
    private $fieldList;
    private $attributeList;
    private $limitDayQuery;
    private $announcementIdList;
    private $publicQuery;

    /**
     * 设置初始化数据
     * @param $params
     * @return $this
     */
    public function setData($params)
    {
        $this->surplusNum = $params['limit'];
        //获取职位信息
        $announcementInfo = BaseAnnouncement::findOne(['id' => $params['announcementId']]);
        $companyId        = BaseAnnouncement::findOneVal(['id' => $params['announcementId']], 'company_id');

        //拼接传入的公告基本数据，用于下面的查询
        $this->announcementInfo     = [
            'id'              => $params['announcementId'],
            'company_id'      => $companyId,
            'company_city_id' => BaseCompany::findOneVal(['id' => $companyId], 'city_id'),
            'home_column_id'  => BaseArticle::findOneVal(['id' => $announcementInfo['article_id']], 'home_column_id'),
        ];
        $this->announcementIdList[] = $params['announcementId'];
        //规则2、3、4都是用这个属性列表
        $this->attributeList = [
            BaseArticleAttribute::ATTRIBUTE_HOME_TOP_ONE,
            BaseArticleAttribute::ATTRIBUTE_HOME_TOP_TWO,
            BaseArticleAttribute::ATTRIBUTE_HOME_TOP,
            BaseArticleAttribute::ATTRIBUTE_ROLLING,
            BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP,
            BaseArticleAttribute::ATTRIBUTE_HOME,
            BaseArticleAttribute::ATTRIBUTE_FOCUS,
        ];

        return $this;
    }

    /**
     * 获取推荐列表
     * @return array
     * @throws \Exception
     */
    public function getRecommendList()
    {
        $this->getSelectField();
        $actionName = 'list';
        $allList    = [];
        for ($i = 1; $i < 6; $i++) {
            $fullActionName = $actionName . $i;
            if ($this->surplusNum > 0) {
                //调用公告的查询方法
                $this->getPublicQuery();
                //调用具体的查询方法
                $list = self::$fullActionName();
                foreach ($list as &$item) {
                    $this->announcementIdList[] = $item['id'];
                    $item['sort']               = $i;
                }
                $allList[] = $list;
            } else {
                break;
            }
        }
        //去除数组里的空数组
        $allList          = array_filter($allList);
        $announcementList = [];

        foreach ($allList as $list) {
            foreach ($list as $announcement) {
                $announcementList[] = $announcement;
            }
        }

        return $this->getFullInfo($announcementList);
    }

    /**
     * 补充完整信息
     * @param $announcementList
     * @return array
     * @throws \Exception
     */
    private function getFullInfo($announcementList)
    {
        if (!empty($announcementList)) {
            foreach ($announcementList as $k => &$announcement) {
                //获取学历
                $announcement['education'] = trim(BaseJob::getAnnouncementJobEducationType($announcement['id']));
                //获取职位数量
                $announcement['jobAmount'] = BaseJob::getAnnouncementJobAmount($announcement['id']);
                //获取地区
                $announcement['areaName'] = BaseAnnouncement::getAnnouncementAreaName($announcement['id']);
                //获取url
                $announcement['url'] = BaseAnnouncement::getDetailUrl($announcement['id']);
            }

            return $announcementList;
        } else {
            return [];
        }
    }

    /**
     * 获取共同查询字段
     * @return void
     */
    private function getSelectField()
    {
        $this->fieldList = [
            'a.title',
            'an.id',
            'a.refresh_time',
        ];
    }

    /**
     * 获取限制天数查询
     * @return void
     */
    private function getDayLimit()
    {
        $this->limitDayQuery = [
            '>=',
            'a.refresh_time',
            date('Y-m-d', strtotime("-" . self::LIMIT_DAY . " day")),
        ];
    }

    /**
     * 公共的查询方法
     */
    private function getPublicQuery()
    {
        $this->publicQuery = BaseAnnouncement::find()
            ->alias('an')
            ->leftJoin(['a' => BaseArticle::tableName()], 'a.id=an.article_id')
            ->where([
                'a.is_delete' => BaseArticle::IS_DELETE_NO,
                'a.is_show'   => BaseArticle::IS_SHOW_YES,
                'a.status'    => BaseArticle::STATUS_ONLINE,
            ])
            ->andFilterWhere([
                'not in',
                'an.id',
                $this->announcementIdList,
            ])
            ->select($this->fieldList)
            ->asArray()
            ->groupBy('an.id')
            ->orderBy('a.refresh_time desc')
            ->limit($this->surplusNum);
    }

    /**
     * No1：该单位的其他公告；
     * @return array|\yii\db\ActiveRecord[]
     */
    private function List1()
    {
        $list = $this->publicQuery->andWhere(['an.company_id' => $this->announcementInfo['company_id']])
            ->all();

        foreach ($list as &$item) {
            $item['sort'] = 1;
        }

        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * No2：当前浏览公告所属主栏目下：近5天发布的、配置有首头1/首头2/首页置顶/滚动/栏目置顶/首页/焦点属性的公告、且公告所属单位与当前浏览公告的所属单位同城；
     * @return array|\yii\db\ActiveRecord[]
     */
    private function list2()
    {
        $this->getDayLimit();
        //获取属性列表
        $list = $this->publicQuery->leftJoin(['c' => BaseCompany::tableName()], 'c.id = an.company_id')
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'a.id = aa.article_id')
            ->andWhere([
                'aa.type'          => $this->attributeList,
                'a.home_column_id' => $this->announcementInfo['home_column_id'],
                'c.city_id'        => $this->announcementInfo['company_city_id'],
            ])
            ->andWhere($this->limitDayQuery)
            ->andWhere([
                '<>',
                'c.id',
                $this->announcementInfo['company_id'],
            ])
            ->all();
        foreach ($list as &$item) {
            $item['sort'] = 2;
        }
        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * No3：当前浏览公告所属主栏目下：近5天发布的、配置有首头1/首头2/首页置顶/滚动/栏目置顶/首页/焦点属性的、其他合作单位的公告；
     * @return array|\yii\db\ActiveRecord[]
     */
    private function list3()
    {
        $this->getDayLimit();
        $list = $this->publicQuery->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'a.id = aa.article_id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = an.company_id')
            ->andWhere([
                'aa.type'          => $this->attributeList,
                'a.home_column_id' => $this->announcementInfo['home_column_id'],
                'c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
            ])
            ->andWhere($this->limitDayQuery)
            ->andWhere([
                '<>',
                'c.id',
                $this->announcementInfo['company_id'],
            ])
            ->all();
        foreach ($list as &$item) {
            $item['sort'] = 3;
        }
        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * No4：当前浏览公告所属主栏目下：近5天发布的、配置有首头1/首头2/首页置顶/滚动/栏目置顶/首页/焦点属性的、其他非合作单位的公告；
     * @return array|\yii\db\ActiveRecord[]
     */
    private function list4()
    {
        $this->getDayLimit();
        //获取属性列表
        $list = $this->publicQuery->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'a.id = aa.article_id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = an.company_id')
            ->andWhere([
                'aa.type'          => $this->attributeList,
                'a.home_column_id' => $this->announcementInfo['home_column_id'],
                'c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_NO,
            ])
            ->andWhere($this->limitDayQuery)
            ->andWhere([
                '<>',
                'c.id',
                $this->announcementInfo['company_id'],
            ])
            ->all();
        foreach ($list as &$item) {
            $item['sort'] = 4;
        }
        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 与当前浏览公告所属单位同城的 其他单位的公告；
     * @return mixed
     */
    private function list5()
    {
        $list = $this->publicQuery->leftJoin(['c' => BaseCompany::tableName()], 'c.id = an.company_id')
            ->andWhere([
                'c.city_id' => $this->announcementInfo['company_city_id'],
            ])
            ->andWhere([
                '<>',
                'c.id',
                $this->announcementInfo['company_id'],
            ])
            ->all();
        foreach ($list as &$item) {
            $item['sort'] = 5;
        }
        $this->surplusNum -= count($list);

        return $list;
    }

}