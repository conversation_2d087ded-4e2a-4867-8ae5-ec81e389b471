<?php

namespace common\service\announcement;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseArticle;
use common\base\models\BaseCompany;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseOffSiteJobApply;
use common\helpers\IpHelper;
use common\helpers\TimeHelper;
use yii\base\Exception;

class DeleteService extends BaseService
{
    /**
     * 执行删除
     */
    public function run()
    {
        switch ($this->actionType) {
            case self::ACTION_TYPE_DELETE:
                $this->beforeRun();
                $this->delete();
                break;
            case self::ACTION_TYPE_REDUCTION:
                $this->beforeRun();
                $this->reduction();
                break;
            default:
                throw new Exception('操作类型错误');
        }

        $this->afterRun();
    }

    /**
     * 设置删除
     * @return $this
     */
    public function setDelete(): DeleteService
    {
        $this->actionType = self::ACTION_TYPE_DELETE;

        return $this;
    }

    /**
     * 设置还原
     * @return $this
     */
    public function setReduction(): DeleteService
    {
        $this->actionType = self::ACTION_TYPE_REDUCTION;

        return $this;
    }

    /**
     * 删除
     * @throws Exception
     */
    private function delete()
    {
        $this->deleteRun();
    }

    /**
     * 还原
     * @throws Exception
     */
    private function reduction()
    {
        $this->reductionRun();
    }

    /**
     * 设置要删除的数据
     * @param $params
     * @return $this
     * @throws Exception
     */
    public function setDeleteData($params): DeleteService
    {
        $this->setAnnouncement($params['id']);
        $this->setCompany($params['company_id']);
        if ($this->announcementModel->create_type == BaseAnnouncement::CREATE_TYPE_ADMIN) {
            if ($this->companyModel->is_cooperation == BaseCompany::COOPERATIVE_UNIT_YES && $this->articleModel->first_release_time != TimeHelper::ZERO_TIME) {
                throw new Exception('合作单位有审核通过历史的公告，不可删除');
            }
        }

        if ($this->announcementModel->audit_status == BaseAnnouncement::STATUS_AUDIT_AWAIT) {
            throw new Exception('待审核公告，不可删除');
        }

        $this->jobList = BaseJob::find()
            ->select('id,status,audit_status')
            ->where(['announcement_id' => $this->announcementId])
            ->asArray()
            ->all();

        $jobIds = [];
        foreach ($this->jobList as $item) {
            if ($this->companyModel->is_cooperation == BaseCompany::COOPERATIVE_UNIT_YES) {
                $jobApply = BaseJobApply::findOne(['job_id' => $item['id']]);
            } else {
                $jobApply = BaseOffSiteJobApply::findOne(['job_id' => $item['id']]);
            }
            if ($jobApply) {
                throw new Exception('该公告的职位存在投递数据，不可删除');
            }
            if ($item['audit_status'] == BaseJob::STATUS_WAIT_AUDIT) {
                throw new Exception('待审核职位，不可删除');
            }

            $jobIds[] = $item['id'];
        }

        $this->jobIds   = $jobIds;
        $this->jobModel = BaseJob::find()
            ->select('status')
            ->where([
                'in',
                'id',
                $this->jobIds,
            ]);

        return $this;
    }

    /**
     * 删除公告
     */
    private function deleteRun()
    {
        $articleModel              = $this->articleModel;
        $articleModel->is_delete   = BaseArticle::STATUS_ACTIVE;
        $articleModel->delete_time = CUR_DATETIME;
        if (!$articleModel->save()) {
            throw new Exception($articleModel->getFirstErrorsMessage());
        }

        // 删除公告的同时一并删除公告下的所有职位
        if (!BaseJob::updateAll([
            'status'      => BaseJob::STATUS_DELETE,
            'delete_time' => CUR_DATETIME,
        ], ['id' => $this->jobIds])) {
            throw new Exception('职位删除失败');
        }

        $this->operationRecord = '删除公告id:' . $this->announcementId . '，职位id:' . $this->jobIds;
    }

    /**
     * 设置还原的数据
     * @param $params
     * @return $this
     * @throws Exception
     */
    public function setReductionData($params): DeleteService
    {
        $this->setAnnouncement($params['id']);

        $jobList = BaseJob::find()
            ->select('id,status,audit_status,first_release_time')
            ->where(['announcement_id' => $this->announcementId])
            ->asArray()
            ->all();
        if (!$jobList) {
            throw new Exception('该公告缺少职位信息');
        }

        $jobIds = [];
        foreach ($this->jobList as $item) {
            $jobIds[] = $item['id'];
        }

        $this->jobIds  = $jobIds;
        $this->jobList = $jobList;

        return $this;
    }

    /**
     * 还原公告
     */
    private function reductionRun()
    {
        $articleModel      = $this->articleModel;
        $announcementModel = $this->announcementModel;
        $jobIds            = $this->jobIds;
        $jobList           = $this->jobList;

        switch ($articleModel['status']) {
            case BaseArticle::STATUS_ONLINE:
                //在线的公告
                $articleModel->status = BaseArticle::STATUS_OFFLINE;
                break;
            case BaseArticle::STATUS_STAGING:
                //待发布的公告
                $announcementModel->audit_status = BaseAnnouncement::STATUS_AUDIT_STAGING;
                $articleModel->status            = BaseArticle::STATUS_STAGING;
                break;
            case BaseArticle::STATUS_OFFLINE:
                //下线的公告
                $articleModel->status = BaseArticle::STATUS_OFFLINE;
                break;
            default:
                throw new Exception('该公告不支持还原操作');
        }

        foreach ($jobList as $item) {
            $jobModel = BaseJob::findOne(['id' => $item['id']]);

            if ($articleModel['status'] == BaseArticle::STATUS_ONLINE) {//在线的公告
                switch ($item['audit_status']) {
                    // 审核拒绝的职位
                    case BaseJob::AUDIT_STATUS_REFUSE_AUDIT:
                        // 有审核通过历史
                        if ($item['first_release_time'] != TimeHelper::ZERO_TIME) {
                            $jobModel->audit_status = BaseJob::AUDIT_STATUS_REFUSE_AUDIT;
                        } else {
                            $jobModel->status = BaseJob::STATUS_OFFLINE;
                        }
                        break;
                    // 审核通过的职位
                    case BaseJob::AUDIT_STATUS_PASS_AUDIT:
                        // 审核通过的职位
                        $jobModel->status = BaseJob::STATUS_OFFLINE;
                        break;
                }
            } elseif ($articleModel['status'] == BaseArticle::STATUS_STAGING) {//待发布的公告
                switch ($item['audit_status']) {
                    // 编辑中的职位
                    case BaseJob::AUDIT_STATUS_WAIT:
                        $jobModel->status       = BaseJob::STATUS_WAIT;
                        $jobModel->audit_status = BaseJob::AUDIT_STATUS_WAIT;
                        break;
                    // 待审核的职位
                    case BaseJob::AUDIT_STATUS_WAIT_AUDIT:
                        // 审核拒绝的职位
                    case BaseJob::AUDIT_STATUS_REFUSE_AUDIT:
                        $jobModel->status       = BaseJob::STATUS_WAIT;
                        $jobModel->audit_status = BaseJob::AUDIT_STATUS_REFUSE_AUDIT;
                        break;
                }
            } elseif ($articleModel['status'] == BaseArticle::STATUS_OFFLINE) {//下线的公告
                // 有审核通过历史
                if ($item['first_release_time'] != TimeHelper::ZERO_TIME) {
                    $jobModel->status = BaseJob::STATUS_OFFLINE;
                } else {
                    $jobModel->audit_status = BaseJob::AUDIT_STATUS_REFUSE_AUDIT;
                }
            } else {
                throw new Exception('该公告不支持还原操作');
            }

            if (!$jobModel->save()) {
                throw new Exception($jobModel->getFirstErrorsMessage());
            }
        }

        $articleModel->is_delete = BaseArticle::STATUS_DELETE;
        if (!$articleModel->save()) {
            throw new Exception($articleModel->getFirstErrorsMessage());
        }

        $this->operationRecord = '还原公告id:' . $this->announcementId . '，职位id:' . implode(',', $jobIds);
    }

    /**
     * 公告删除操作日志
     * @param
     * @throws Exception
     * @throws \yii\base\NotSupportedException
     */
    protected function deleteLog()
    {
        //操作动作入表
        $handleBefore = [
            'operation_record' => '-',
        ];

        $handleAfter  = [
            'operation_record' => $this->operationRecord,
        ];
        $handleLogArr = [
            'add_time'        => CUR_DATETIME,
            'announcement_id' => $this->announcementId,
            'handle_type'     => (string)BaseAnnouncementHandleLog::HANDLE_TYPE_DELETE,
            'handler_type'    => $this->operatorType,
            'handler_id'      => $this->operatorId,
            'handler_name'    => $this->operatorUserName ?: '',
            'handle_before'   => json_encode($handleBefore),
            'handle_after'    => json_encode($handleAfter),
            'ip'              => IpHelper::getIpInt(),
        ];
        BaseAnnouncementHandleLog::createInfo($handleLogArr);
    }

}
