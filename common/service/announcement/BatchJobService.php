<?php

namespace common\service\announcement;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobTemp;
use common\base\models\BaseMajor;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\helpers\ValidateHelper;
use yii\base\Exception;

class BatchJobService extends BaseService
{
    /**
     * 执行添加
     */
    public function run()
    {
        $this->beforeRun();
        $this->batchAddRun();
        //        $this->afterRun();
    }

    /**
     * 设置好要处理的数据
     * @param $data
     * @return $this
     * @throws Exception
     */
    public function setData($data): BatchJobService
    {
        //保存单位信息
        $this->companyModel = BaseCompany::findOne(['id' => $data['company_id']]);
        if ($this->companyModel->is_cooperation == BaseAnnouncement::IS_COOPERATION_NO) {
            if ($data['extra_notify_address']) {
                throw new Exception('非合作单位不允许填写邮箱通知地址');
            }
        }
        $checkData = [
            'name',
            'job_category_id',
            'education_type',
            'amount',
            'duty',
            'requirement',
        ];

        foreach ($checkData as $list) {
            if (strlen($data[$list]) < 1) {
                throw new Exception('参数' . $list . '不能为空');
            }
        }
        // 清除文本换行
        $data = StringHelper::cleanLineFeed($data, [
            'duty',
            'requirement',
            'remark',
        ]);

        // 开始组装数据
        $data['is_temp'] = BaseJobTemp::IS_TEMP_YES;
        // 合并识别学科与自选学科
        if (!empty($data['major_id']) && !empty($data['major_ai'])) {
            $data['major_id'] = array_unique(array_merge($data['major_id'], $data['major_ai']));
        } elseif (!empty($data['major_id']) && empty($data['major_ai'])) {
            $data['major_id'] = $data['major_id'];
        } elseif (empty($data['major_id']) && !empty($data['major_ai'])) {
            $data['major_id'] = $data['major_ai'];
        } else {
            $data['major_id'] = [];
        }

        // 薪资范围
        if (!empty($data['min_wage_month']) && !empty($data['max_wage_month'])) {
            // 月薪
            $data['wage_type']     = BaseJob::TYPE_WAGE_MONTH;
            $data['is_negotiable'] = BaseJob::IS_NEGOTIABLE_YES;
            $data['min_wage']      = $data['min_wage_month'];
            $data['max_wage']      = $data['max_wage_month'];
        } elseif (!empty($data['min_wage_year']) && !empty($data['max_wage_year'])) {
            // 年薪
            $data['wage_type']     = BaseJob::TYPE_WAGE_YEARS;
            $data['is_negotiable'] = BaseJob::IS_NEGOTIABLE_YES;
            $data['min_wage']      = $data['min_wage_year'];
            $data['max_wage']      = $data['max_wage_year'];
        } else {
            // 面议
            $data['wage_type']     = BaseJob::TYPE_WAGE_NEGOTIABLE;
            $data['is_negotiable'] = BaseJob::IS_NEGOTIABLE_NO;
            $data['min_wage']      = 0;
            $data['max_wage']      = 0;
        }
        //报名方式与通知地址不可同时填写
        if ($data['apply_type'] && $data['extra_notify_address']) {
            throw new Exception('报名方式与投递通知邮箱不可同时填写');
        }
        $data['extra_notify_address'] = $data['extra_notify_address'] ?: '';
        if ($this->companyModel->is_cooperation == BaseAnnouncement::IS_COOPERATION_NO && isset($data['delivery_way']) && $data['delivery_way'] == BaseJob::DELIVERY_WAY_PLATFORM) {
            throw new Exception('报名方式填写错误');
        }
        if (isset($data['delivery_way']) && $data['delivery_way'] > 0) {
            if ($data['delivery_way'] == BaseJob::DELIVERY_WAY_EMAIL_LINK) {
                if (empty($data['apply_type']) || empty($data['apply_address'])) {
                    throw new Exception('报名方式没有勾选或者投递地址为空');
                }
                $applyTypeArr = explode(',', $data['apply_type']);
                $isEmail      = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr);
                //校验
                BaseJob::validateApplyAddress($applyTypeArr, $data['apply_address']);
                if ($isEmail) {
                    $data['delivery_way'] = BaseJob::DELIVERY_WAY_EMAIL;
                } else {
                    $data['delivery_way'] = BaseJob::DELIVERY_WAY_LINK;
                }
            } else {//deliveryWay=1
                $data['apply_type']    = '';
                $data['apply_address'] = '';
            }
        } else {
            if ($data['apply_type']) {
                $applyTypeArr = explode(',', $data['apply_type']);
                $isEmail      = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr);
                if (empty($data['apply_address'])) {
                    throw new Exception('投递地址不能为空');
                }
                //校验
                BaseJob::validateApplyAddress($applyTypeArr, $data['apply_address']);
                //判断投递方式
                if ($isEmail) {
                    $data['delivery_way'] = BaseJob::DELIVERY_WAY_EMAIL;
                } else {
                    $data['delivery_way'] = BaseJob::DELIVERY_WAY_LINK;
                }
            } else {
                $data['apply_type']    = '';
                $data['apply_address'] = '';
                //判断投递方式
                if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
                    $data['delivery_way'] = BaseJob::DELIVERY_WAY_PLATFORM;
                } else {
                    $data['delivery_way'] = 0;
                }
            }
        }

        //检查通知邮箱的格式
        if ($data['extra_notify_address']) {
            $data['extra_notify_address'] = BaseJob::checkEmailApplyAddress($data['extra_notify_address']);
        }
        //处理纯职位投递类型；选择投递类型那就站外投递，没有就站内投递
        if ($this->companyModel->is_cooperation == BaseAnnouncement::IS_COOPERATION_YES) {
            if ($data['delivery_way'] == 0) {
                $data['delivery_type'] = 0;//跟公告
            } elseif ($data['delivery_way'] == BaseJob::DELIVERY_WAY_LINK) {
                $data['delivery_type'] = BaseJob::DELIVERY_TYPE_OUTSIDE;
            } else {
                $data['delivery_type'] = BaseJob::DELIVERY_TYPE_INSIDE;
            }
        } else {
            if ($data['delivery_way'] == 0) {
                $data['delivery_type'] = 0;
            } else {
                $data['delivery_type'] = BaseJob::DELIVERY_TYPE_OUTSIDE;
            }
        }
        //这里处理子账号的导入运营是有这个字段导入contact_synergy_email而单位端是没有这个字段
        $data['job_contact_synergy_ids'] = [];
        if (isset($data['contact_synergy_email'])) {
            $contactSynergyEmailArr = explode(',', $data['contact_synergy_email']);
            foreach ($contactSynergyEmailArr as $value) {
                $record_id = BaseCompanyMemberInfo::validateEmailMember($this->companyModel->id, $value);
                if ($record_id) {
                    array_push($data['job_contact_synergy_ids'], $record_id);
                }
            }

            //处理职位联系人
            $data['job_contact_id'] = count($data['job_contact_synergy_ids']) > 0 ? $data['job_contact_synergy_ids'][0] : BaseCompanyMemberInfo::findOneVal([
                'company_id'          => $this->companyModel->id,
                'company_member_type' => BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN,
            ], 'id');
        } else {
            $data['job_contact_id'] = BaseCompanyMemberInfo::findOneVal([
                'company_id'          => $this->companyModel->id,
                'company_member_type' => BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN,
            ], 'id');
        }

        $this->jobTempData = $data;

        return $this;
    }

    /**
     * 添加
     * @throws Exception
     */
    private function batchAddRun()
    {
        $data                    = $this->jobTempData;
        $model                   = BaseJobTemp::findOne(['id' => $data['id']]) ?: new BaseJobTemp();
        $delivery_limit_type_arr = [];
        if (isset($data['delivery_limit_type_file']) && !empty($data['delivery_limit_type_file'])) {
            array_push($delivery_limit_type_arr, $data['delivery_limit_type_file']);
        }
        if (isset($data['delivery_limit_type_education']) && !empty($data['delivery_limit_type_education'])) {
            array_push($delivery_limit_type_arr, $data['delivery_limit_type_education']);
        }
        $delivery_limit_type = '';
        if (count($delivery_limit_type_arr) > 0) {
            $delivery_limit_type = implode(',', $delivery_limit_type_arr);
        }
        $data['major_id']            = implode(',', $data['major_id']);
        $data['welfare_tag']         = implode(',', $data['welfare_tag']);
        $model->announcement_id      = $data['announcement_id'] ?: 0;
        $model->company_id           = $data['company_id'] ?: 0;
        $model->is_temp              = BaseJobTemp::IS_TEMP_YES;
        $model->name                 = $data['name'];
        $model->code                 = $data['code'] ?: '';
        $model->job_category_id      = $data['job_category_id'];
        $model->education_type       = $data['education_type'];
        $model->major_id             = $data['major_id'] ?: '';
        $model->nature_type          = $data['nature_type'];
        $model->wage_type            = $data['wage_type'];
        $model->is_negotiable        = $data['is_negotiable'];
        $model->apply_type           = $data['apply_type'] ?: '';
        $model->apply_address        = $data['apply_address'] ?: '';
        $model->min_wage             = $data['min_wage'];
        $model->max_wage             = $data['max_wage'];
        $model->experience_type      = $data['experience_type'];
        $model->age_type             = $data['age_type'];
        $model->title_type           = $data['title_type'];
        $model->political_type       = $data['political_type'];
        $model->abroad_type          = $data['abroad_type'];
        $model->amount               = $data['amount'];
        $model->department           = $data['department'];
        $model->province_id          = $data['province_id'];
        $model->city_id              = $data['city_id'];
        $model->welfare_tag          = $data['welfare_tag'] ?: '';
        $model->period_date          = $data['period_date'] ?: TimeHelper::ZERO_TIME;
        $model->duty                 = $data['duty'];
        $model->requirement          = $data['requirement'];
        $model->remark               = $data['remark'] ?: '';
        $model->audit_status         = BaseJob::AUDIT_STATUS_WAIT_AUDIT;
        $model->create_type          = !empty($data['id']) ? self::CREATE_TYPE_EDIT : self::CREATE_TYPE_ADD;
        $model->delivery_limit_type  = $delivery_limit_type;
        $model->delivery_type        = $data['delivery_type'];
        $model->delivery_way         = $data['delivery_way'];
        $model->extra_notify_address = $data['extra_notify_address'];
        $model->contact_id           = $data['job_contact_id'];
        $model->contact_synergy_id   = $data['job_contact_synergy_ids'] ? implode(',',
            $data['job_contact_synergy_ids']) : '';

        //判断操作平台为运营平台
        if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
            $model->establishment_type = $data['establishment_type'] ?: '';
            $model->is_establishment   = !empty($data['establishment_type']) ? BaseJob::IS_ESTABLISHMENT_YES : BaseJob::IS_ESTABLISHMENT_NO;
        } else {
            $model->establishment_type = '';
            $model->is_establishment   = BaseJob::IS_ESTABLISHMENT_NO;
        }
        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        $model     = $model->toArray();
        $majorArr  = explode(',', $model['major_id']);
        $majorName = BaseMajor::find()
            ->where(['id' => $majorArr])
            ->select('name')
            ->asArray()
            ->all();

        $majorNameOne = [];
        foreach ($majorName as $item) {
            $majorNameOne[] = $item['name'];
        }

        $model['id']             = (string)$model['id'];
        $model['job_id']         = (string)$model['job_id'];
        $model['auditStatusTxt'] = '-';
        $model['statusTxt']      = '待发布';
        $model['majorTxt']       = implode(';', $majorNameOne);
        $model['educationTxt']   = BaseDictionary::getEducationName($model['education_type']) ?: '';
        $model['areaTxt']        = BaseArea::getAreaName($model['city_id']) ?: '';
        $model['canDel']         = true;
        if (!$model['min_wage'] && !$model['max_wage']) {
            $model['wage'] = '面议';
        } else {
            $model['wage'] = BaseJob::formatWage($model['min_wage'], $model['max_wage'], $model['wage_type']) ?: '-';
        }
        $contact_synergy_ids              = $model['contact_synergy_id'] ? explode(',',
            $model['contact_synergy_id']) : [];
        $model['job_contact_synergy']     = count($contact_synergy_ids) > 0 ? BaseCompanyMemberInfo::getInfoMany($contact_synergy_ids) : [];
        $model['job_contact_synergy_num'] = count($contact_synergy_ids);
        foreach ($model['job_contact_synergy'] as &$item) {
            $item['is_contact'] = 0;
            if ($item['id'] == $model['contact_id']) {
                $item['is_contact'] = 1;
            }
        }
        $model['job_contact'] = BaseCompanyMemberInfo::getInfoOne($model['contact_id']);
        $information          = [];
        if ($model['areaTxt']) {
            array_push($information, $model['areaTxt']);
        }
        if ($model['amount']) {
            array_push($information, "招{$model['amount']}人");
        }
        if ($model['educationTxt']) {
            array_push($information, $model['educationTxt']);
        }
        if ($model['wage']) {
            array_push($information, $model['wage']);
        }
        if ($model['majorTxt']) {
            array_push($information, $model['majorTxt']);
        }
        $model['information'] = implode(' | ', $information);

        $this->jobTempData = $model;
    }

}
