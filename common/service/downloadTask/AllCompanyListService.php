<?php

namespace common\service\downloadTask;

use admin\models\JobApply;
use common\base\models\BaseAdmin;
use common\base\models\BaseAdminPosition;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementCollect;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyCollect;
use common\base\models\BaseCompanyGroup;
use common\base\models\BaseCompanyGroupRelation;
use common\base\models\BaseCompanyMemberConfig;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobCollect;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseMember;
use common\base\models\BaseMemberActionLog;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeLibraryInviteLog;
use common\base\models\BaseTrade;
use common\helpers\TimeHelper;
use common\helpers\UUIDHelper;
use common\libs\Cache;
use common\models\Admin;
use common\models\CompanyContact;
use common\models\Member;
use Yii;

class AllCompanyListService
{

    public $params;

    public $adminId;

    public $adminCompanyList = [];

    public function setParams($params)
    {
        $this->params = $params;

        // 根据adminId去找是否是销售人员
        $admin         = BaseAdmin::findOne($params['adminId']);
        $this->adminId = $admin->id;
        // 找到全部自己的单位
        $companyArray = BaseCompany::find()
            ->select('id')
            ->where(['admin_id' => $admin->id])
            ->asArray()
            ->column();

        // 如果是销售人员,则只能看到自己的单位
        $this->adminCompanyList = $companyArray;

        return $this;
    }

    public function run($params)
    {
        $select = [
            'c.id',
            'c.member_id',
            'c.full_name',
            'c.admin_id',
            'c.province_id',
            'c.city_id',
            'c.district_id',
            'c.nature',
            'c.type',
            'c.industry_id',
            'c.source_type',
            'c.admin_id',
            'c.create_admin_id',
            'c.is_cooperation',
            'c.status as statusCompany',
            'c.address',
            'c.package_type',
            'c.click',
            'm.status as statusMember',
            'm.username',
            'm.mobile',
            'm.add_time',
            'm.last_active_time',
            'm.last_login_time',
            'cc.name as contact',
            'cc.mobile as cMobile',
            'cmc.total as sub_total',
            'cmc.used as sub_used',
            'cmi.contact as company_info_contact',
            'cmi.department as company_info_department',
            'GROUP_CONCAT(cg.id) as groupIds',
            'GROUP_CONCAT(cg.group_name) as groupNames',
        ];

        $query = BaseCompany::find()
            ->alias('c')
            ->leftJoin(['cc' => CompanyContact::tableName()], 'cc.company_id = c.id')
            ->leftJoin(['cmi' => BaseCompanyMemberInfo::tableName()], 'cmi.member_id=c.member_id')
            ->leftJoin(['cmc' => BaseCompanyMemberConfig::tableName()], 'cmc.company_id=c.id')
            ->innerJoin(['m' => BaseMember::tableName()], 'm.id = c.member_id')
            ->leftJoin(['a' => BaseAdmin::tableName()], 'a.id = c.admin_id')
            ->leftJoin(['cgr' => BaseCompanyGroupRelation::tableName()], 'cgr.company_id=c.id')
            ->leftJoin(['cg' => BaseCompanyGroup::tableName()], 'cg.id=cgr.group_id')
            ->where(['c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES])
            ->groupBy('c.id');
        // 审核已通过单位
        $query->andFilterWhere([
            'c.status' => BaseCompany::STATUS_ACTIVE,
            'm.status' => [
                BaseMember::STATUS_ACTIVE,
                BaseMember::STATUS_ILLEGAL,
            ],
        ]);

        //单位名称、编号
        JobApply::uidJudgeWhere($params['companySearch'], 'c.id', 'c.full_name', $query);

        //所在地区
        if ($params['area']) {
            $query->andFilterWhere([
                'in',
                'c.city_id',
                $params['area'],
            ]);
        }
        if ($params['groupIds']) {
            $groupIdsArr = explode(',', $params['groupIds']);
            $query->andFilterWhere(['cgr.group_id' => $groupIdsArr]);
        }
        //是否隐藏
        $query->andFilterWhere([
            'c.is_hide' => $params['isHide'],
        ]);
        //联系号码
        $query->andFilterWhere([
            'or',
            [
                'm.mobile' => $params['mobile'],
            ],
            [
                'cc.mobile' => $params['mobile'],
            ],
        ]);
        //注册邮箱
        $query->andFilterCompare('m.email', $params['email'], 'like');
        //最后登陆时间
        if ($params['lastLoginTime']) {
            $query->andFilterWhere([
                'between',
                'm.last_login_time',
                TimeHelper::dayToBeginTime($params['lastLoginTime']),
                TimeHelper::dayToEndTime($params['lastLoginTime']),
            ]);
        }

        //单位帐号创建时间
        if ($params['lastLoginTimeFrom']) {
            $query->andFilterWhere([
                '>=',
                'm.last_login_time',
                TimeHelper::dayToBeginTime($params['lastLoginTimeFrom']),
            ]);
        }

        if ($params['lastLoginTimeTo']) {
            $query->andFilterWhere([
                '<',
                'm.last_login_time',
                TimeHelper::dayToEndTime($params['lastLoginTimeTo']),
            ]);
        }

        //单位帐号状态
        $query->andFilterWhere([
            'm.status' => $params['status'],
        ]);
        //单位性质
        $query->andFilterWhere([
            'c.nature' => $params['nature'],
        ]);
        //单位类型
        $query->andFilterWhere([
            'c.type' => $params['type'],
        ]);
        //所属行业
        if ($params['industryId']) {
            $query->andFilterWhere([
                'in',
                'c.industry_id',
                $params['industryId'],
            ]);
        }
        //单位帐号创建时间
        if ($params['addTimeFrom']) {
            $query->andFilterWhere([
                '>=',
                'm.add_time',
                TimeHelper::dayToBeginTime($params['addTimeFrom']),
            ]);
        }

        if ($params['addTimeTo']) {
            $query->andFilterWhere([
                '<',
                'm.add_time',
                TimeHelper::dayToEndTime($params['addTimeTo']),
            ]);
        }
        //活跃时间
        if ($params['lastActiveTimeFrom'] && $params['lastActiveTimeTo']) {
            $query->andFilterWhere([
                'between',
                'm.last_active_time',
                TimeHelper::dayToBeginTime($params['lastActiveTimeFrom']),
                TimeHelper::dayToEndTime($params['lastActiveTimeTo']),
            ]);
        }

        //单位入网来源
        $query->andFilterWhere([
            'c.source_type' => $params['sourceType'],
        ]);

        //套餐类型
        $query->andFilterWhere([
            'c.package_type' => $params['packageType'],
        ]);

        //投递类型
        $query->andFilterCompare('delivery_type', $params['deliveryType']);
        //账号性质
        $query->andFilterCompare('account_nature', $params['accountNature']);
        $query->andFilterCompare('c.is_miniapp', $params['isMiniapp']);

        // 业务员
        $query->andFilterWhere([
            'or',
            [
                '=',
                'a.username',
                $params['salesman'],
            ],
            [
                'like',
                'a.name',
                $params['salesman'],
            ],

        ]);
        if ($this->adminId) {
            $query->andWhere(['c.admin_id' => $this->adminId]);
        }

        // 审核已通过单位
        //        $query->andFilterWhere([
        //            'c.status' => BaseCompany::STATUS_ACTIVE,
        //            'm.status' => [
        //                BaseMember::STATUS_ACTIVE,
        //                BaseMember::STATUS_ILLEGAL,
        //            ],
        //        ]);

        //所在地区

        //创建人帐号
        if (!empty($params['createUserName'])) {
            $createMemberId = BaseMember::find()
                ->select('id')
                ->andWhere([
                    'like',
                    'username',
                    $params['createUserName'],
                ])
                ->scalar();
            $createAdminId  = \admin\models\Admin::find()
                ->select('id')
                ->andWhere([
                    'like',
                    'name',
                    $params['createUserName'],
                ])
                ->scalar();
            $createId       = $createMemberId ?: $createAdminId;
            $query->andFilterWhere([
                'c.create_admin_id' => $createId,
            ]);
        }

        $orderBy = 'm.add_time desc';

        $list = $query->select($select)
            ->orderBy($orderBy)
            ->asArray()
            // ->limit(100)
            ->all();

        //获取地区表缓存
        $cache     = Yii::$app->cache;
        $areaCache = $cache->get(Cache::PC_ALL_AREA_TABLE_KEY);
        if (!$areaCache) {
            $areaCache = BaseArea::setAreaCache();
        }
        //当前月
        $currentMonth = date('m');
        //上个月
        $lastMonth = date('m', strtotime('-1 month'));
        //上上个月
        $lastLastMonth = date('m', strtotime('-2 month'));
        foreach ($list as &$item) {
            $item['packageNameTxt']  = BaseCompany::PACKAGE_TYPE_LIST[$item['package_type']];
            $item['uid']             = UUIDHelper::encrypt(UUIDHelper::TYPE_COMPANY, $item['id']);
            $item['natureTxt']       = BaseDictionary::getCompanyNatureName($item['nature']);
            $item['industryTxt']     = BaseTrade::getIndustryName($item['industry_id']);
            $item['typeTxt']         = BaseDictionary::getCompanyTypeName($item['type']);
            $item['statusMemberTxt'] = BaseMember::COMPANY_ACCOUNTS_STATUS_LIST[$item['statusMember']];
            $item['sourceTypeName']  = BaseCompany::TYPE_SOURCE_LIST[$item['source_type']];
            $item['name']            = BaseAdmin::findOneVal(['id' => $item['admin_id']], 'name') ?: '';
            $item['address']         = str_replace(',', '',
                $areaCache[$item['city_id']]['full_name'] . $item['address']);

            if ($item['source_type'] == BaseCompany::TYPE_SOURCE_ADD) {
                $item['createUserName'] = Admin::findOneVal(['id' => $item['create_admin_id']], 'name') ?: '';
            } else {
                //                $item['createUserName'] = BaseMember::findOneVal(['id' => $item['create_admin_id']], 'username') ?: '';
                $item['createUserName'] = $item['company_info_contact'];
                if ($item['company_info_department']) {
                    $item['createUserName'] .= '(' . $item['company_info_department'] . ')';
                }
            }
            // 如果是邮箱注册回显联系号码
            $item['mobile']      = $item['cMobile'] ?: $item['mMobile'];
            $item['viewingRate'] = BaseCompany::statCompanyViewingRate($item['id']);
            $item['sub_total']   = $item['sub_total'] ? 0 : $item['sub_total'];
            $item['sub_used']    = $item['sub_used'] ? 0 : $item['sub_used'];
        }

        if ($this->adminId) {
            $headers = [
                '单位名称',
                '联系人',
                '会员类型',
                '创建时间',
                '活跃时间',
                '最近登陆',
                '单位性质',
                '单位类型',
                '所属行业',
                '业务员',
                '简历查看率',
                '总投递量',
                '硕士总投递次数',
                '博士总投递次数',
                '邮件/平台投递量',
                '硕士邮件/平台投递次数',
                '博士邮件/平台投递次数',
                '网址投递量',
                '硕士网址投递次数',
                '博士网址投递次数',
                '登录次数',
                '简历下载点数',
                '查看简历数',
                '收藏人才数',
                '简历分享次数',
                '邀约投递数',
                '简历扣点下载数',
                '单位主页的收藏量',
                '单位主页的阅读量',
                '公告发布数量',
                '公告阅读次数',
                '公告收藏数量',
                '职位发布个数',
                '职位收藏量',
                '职位总阅读量',
                '职位刷新次数',
                '单位群组ID',
                '单位群组名称',

            ];
        } else {
            // 导出的时候,全部字段都给,不用管前端应该要什么字段,这里面有
            // UID
            // 单位名称
            // 用户名
            // 联系人
            // 联系电话
            // 会员类型
            // 创建时间
            // 活跃时间
            // 最近登陆
            // 所在地
            // 操作
            // 单位性质
            // 单位类型
            // 所属行业
            // 单位状态
            // 创建人
            // 入网来源
            // 业务员
            // 登录次数
            // 简历下载点数
            //    查看简历数（去重，查看同一用户简历记为一次）
            //    收藏人才数
            //    简历分享次数
            //    邀约投递数
            //    简历扣点下载数
            /**
             * 2022-10-24 梁凤妍 提出的需求
             * 单位维度：单位主页的收藏量【无】、单位主页的阅读量【无】、单位风采图和横幅图片更新维护情况【前端可查看】……
             * 公告维度：公告发布数量、公告阅读次数、公告收藏数量【无】……
             * 职位维度：职位发布个数、职位收藏量【无】、职位总阅读量、职位刷新次数、
             */
            $headers = [
                '单位ID',
                '单位名称',
                '用户名',
                '联系人姓名',
                '联系电话',
                '会员类型',
                '创建时间',
                '单位地址',
                '单位性质',
                '单位类型',
                '所属行业',
                '单位状态',
                '创建人',
                '入网来源',
                '业务员',
                '简历查看率',
                '总投递量',
                '硕士总投递次数',
                '博士总投递次数',
                '邮件/平台投递量',
                '硕士邮件/平台投递次数',
                '博士邮件/平台投递次数',
                '网址投递量',
                '硕士网址投递次数',
                '博士网址投递次数',
                '登录次数',
                '简历下载点数',
                '查看简历数',
                '收藏人才数',
                '简历分享次数',
                '邀约投递数',
                '简历扣点下载数',
                '单位主页的收藏量',
                '单位主页的阅读量',
                '公告发布数量',
                '公告阅读次数',
                '公告收藏数量',
                '职位发布个数',
                '职位收藏量',
                '职位总阅读量',
                '职位刷新次数',
                '子账号数量已创建/总量',
                '注册账号ID',
                '高级会员开通时间',
                '高级会员到期时间',
                '公告发布总次数',
                '公告发布剩余次数',
                '职位发布总次数',
                '职位发布剩余次数',
                '职位刷新总次数',
                '职位刷新剩余次数',
                '简历下载总点数',
                '简历下载剩余点数',
                '单位端职位邀约总次数',
                '单位端职位邀约投递次数',
                "单位端{$currentMonth}月职位邀约总次数",
                "单位端{$lastMonth}月职位邀约总次数",
                "单位端{$lastLastMonth}月职位邀约总次数",
                "单位端{$currentMonth}月职位邀约投递次数",
                "单位端{$lastMonth}月职位邀约投递次数",
                "单位端{$lastLastMonth}月职位邀约投递次数",
                "单位端{$currentMonth}月新增博士数量",
                "单位端{$lastMonth}月新增博士数量",
                "单位群组ID",
                "单位群组名称",
            ];
        }

        $data = [];
        foreach ($list as &$item) {
            // 剩余简历下载点数	查看简历数（去重，查看同一用户简历记为一次）	收藏人才数	简历分享次数	邀约投递数	简历扣点下载数
            $item = array_merge($item, BaseCompany::resumeLibraryData($item['id']));
            // 偷偷添加一些运营数据,站外投递,站内投递
            // 找到站外投递次数,站内投递次数
            $apply_data = BaseCompany::getCompanyApply($item['id']);

            $loginCount = BaseMemberActionLog::find()
                ->where([
                    'member_id' => $item['member_id'],
                    'is_login'  => BaseMemberActionLog::IS_LOGIN_YES,
                ])
                ->count();

            // 收藏量
            $collectCount = BaseCompanyCollect::find()
                ->where([
                    'company_id' => $item['id'],
                ])
                ->count();

            // 找到所有有审核通过历史的公告和职位
            $announcementIds = BaseAnnouncement::find()
                ->alias('a')
                ->innerJoin(['b' => BaseArticle::tableName()], 'a.article_id = b.id')
                ->select('a.id,b.click')
                ->where([
                    'company_id' => $item['id'],
                ])
                ->andWhere([
                    '<>',
                    'b.refresh_time',
                    TimeHelper::ZERO_TIME,
                ])
                ->asArray()
                ->all();

            $announcementCollectCount = 0;
            $announcementClickCount   = 0;
            $jobCollectCount          = 0;
            $jobRefreshCount          = 0;
            $jobClickCount            = 0;

            // 找职位
            $jobIds = BaseJob::find()
                ->select('id,click')
                ->where([
                    'company_id' => $item['id'],
                ])
                ->andWhere([
                    'status' => [
                        BaseJob::STATUS_ONLINE,
                        BaseJob::STATUS_OFFLINE,
                    ],
                ])
                ->asArray()
                ->all();

            // 循环公告去找收藏量
            foreach ($announcementIds as $announcementId) {
                $announcementClickCount   += $announcementId['click'];
                $announcementCollectCount += BaseAnnouncementCollect::find()
                    ->where([
                        'announcement_id' => $announcementId['id'],
                    ])
                    ->count();
            }

            // 循环职位去找收藏量和刷新量
            foreach ($jobIds as $jobId) {
                $jobClickCount   += $jobId['click'];
                $jobCollectCount += BaseJobCollect::find()
                    ->where([
                        'job_id' => $jobId['id'],
                    ])
                    ->count();
                $jobRefreshCount += BaseJobHandleLog::find()
                    ->where([
                        'job_id'      => $jobId['id'],
                        'handle_type' => BaseJobHandleLog::HANDLE_TYPE_REFRESH,
                    ])
                    ->count();
            }

            if (!$this->adminId) {
                //单位套餐信息
                $companyPackageConfigInfo = BaseCompanyPackageConfig::getCompanyPackageConfigInfo($item['id']);
                //单位邀约信息
                $companyInviteInfo = $this->getInviteData($item['id']);
                $data[]            = [
                    $item['uid'],
                    $item['full_name'],
                    $item['username'],
                    $item['contact'],
                    $item['mobile'],
                    $item['packageNameTxt'],
                    $item['add_time'],
                    $item['address'],
                    $item['natureTxt'],
                    $item['typeTxt'],
                    $item['industryTxt'],
                    $item['statusMemberTxt'],
                    $item['createUserName'],
                    $item['sourceTypeName'],
                    $item['name'],
                    $item['viewingRate'],
                    $apply_data['applyAmount'],
                    $apply_data['masterApplyAmount'],
                    $apply_data['doctorApplyAmount'],
                    $apply_data['emailPlatApplyAmount'],
                    $apply_data['emailPlatMasterApplyAmount'],
                    $apply_data['emailPlatDoctorApplyAmount'],
                    $apply_data['linkApplyAmount'],
                    $apply_data['linkMasterApplyAmount'],
                    $apply_data['linkDoctorApplyAmount'],
                    $loginCount,
                    $item['remainResumeDownloadPoint'],
                    $item['resumeView'],
                    $item['resumeCollect'],
                    $item['resumeShare'],
                    $item['resumeInvite'],
                    $item['consumeResumeDownloadPoint'],
                    $collectCount,
                    $item['click'],
                    count($announcementIds),
                    $announcementClickCount,
                    $announcementCollectCount,
                    count($jobIds),
                    $jobCollectCount,
                    $jobClickCount,
                    $jobRefreshCount,
                    $item['sub_used'] . '/' . $item['sub_total'],
                    $item['member_id'],
                    $companyPackageConfigInfo['effect_time'] ?: '',
                    $companyPackageConfigInfo['expire_time'] ?: '',
                    $companyPackageConfigInfo['total_announcement_amount'] ?: 0,
                    $companyPackageConfigInfo['announcement_amount'] ?: 0,
                    $companyPackageConfigInfo['total_job_amount'] ?: 0,
                    $companyPackageConfigInfo['job_amount'] ?: 0,
                    $companyPackageConfigInfo['total_job_refresh_amount'] ?: 0,
                    $companyPackageConfigInfo['job_refresh_amount'] ?: 0,
                    $companyPackageConfigInfo['total_resume_download_amount'] ?: 0,
                    $companyPackageConfigInfo['resume_download_amount'] ?: 0,
                    BaseResumeLibraryInviteLog::getCompanyInviteNumber($item['id']),
                    $companyInviteInfo['applyAmount'],
                    $companyInviteInfo['currentMonthInviteAmount'],
                    $companyInviteInfo['lastMonthInviteAmount'],
                    $companyInviteInfo['lastLastMonthInviteAmount'],
                    $companyInviteInfo['currentMonthApplyAmount'],
                    $companyInviteInfo['lastMonthApplyAmount'],
                    $companyInviteInfo['lastLastMonthApplyAmount'],
                    $companyInviteInfo['currentMonthDoctorResumeAmount'],
                    $companyInviteInfo['lastMonthDoctorResumeAmount'],
                    $item['groupIds'] ?: '',
                    $item['groupNames'] ?: '',
                ];
            } else {
                $data[] = [
                    $item['full_name'],
                    $item['contact'],
                    $item['packageNameTxt'],
                    $item['add_time'],
                    $item['last_active_time'],
                    $item['last_login_time'],
                    $item['natureTxt'],
                    $item['typeTxt'],
                    $item['industryTxt'],
                    $item['name'],
                    $item['viewingRate'],
                    $apply_data['applyAmount'],
                    $apply_data['masterApplyAmount'],
                    $apply_data['doctorApplyAmount'],
                    $apply_data['emailPlatApplyAmount'],
                    $apply_data['emailPlatMasterApplyAmount'],
                    $apply_data['emailPlatDoctorApplyAmount'],
                    $apply_data['linkApplyAmount'],
                    $apply_data['linkMasterApplyAmount'],
                    $apply_data['linkDoctorApplyAmount'],
                    $loginCount,
                    $item['remainResumeDownloadPoint'],
                    $item['resumeView'],
                    $item['resumeCollect'],
                    $item['resumeShare'],
                    $item['resumeInvite'],
                    $item['consumeResumeDownloadPoint'],
                    $collectCount,
                    $item['click'],
                    count($announcementIds),
                    $announcementClickCount,
                    $announcementCollectCount,
                    count($jobIds),
                    $jobCollectCount,
                    $jobClickCount,
                    $jobRefreshCount,
                    $item['groupIds'] ?: '',
                    $item['groupNames'] ?: '',
                ];
            }
        }

        return [
            'data'    => $data,
            'headers' => $headers,
        ];

        // $excel    = new Excel();
        // $this->export($data, $headers);

        // try {
        //     $wxWork = new WxWork();
        //     $wxWork->downLoadMessage($adminId, $fileName, '单位列表');
        // } catch (\Exception $e) {
        // }

        // return [
        //     'excelUrl' => $fileName,
        // ];
    }

    /**
     * 这里处理
     * 单位端职位邀约投递次数
     * 近三个月单位端职位邀约次数
     * 近三个月单位端职位邀约投递次数
     * 博士简历当月新增数量
     * 博士简历上月新增数量
     */
    private function getInviteData($companyId)
    {
        //单位端邀约数据
        $inviteData = BaseResumeLibraryInviteLog::find()
            ->select('resume_id,add_time,job_id')
            ->where([
                'company_id' => $companyId,
            ])
            ->asArray()
            ->all();
        //当前月
        $currentMonth = date('m');
        //当前月开始时间与结束时间
        $currentMonthStartTime = date('Y-m-01 00:00:00');
        $currentMonthEndTime   = date('Y-m-t 23:59:59');
        //上月
        $lastMonth = date('m', strtotime('-1 month'));
        //上月开始时间与结束时间
        $lastMonthStartTime = date('Y-m-01 00:00:00', strtotime('-1 month'));
        $lastMonthEndTime   = date('Y-m-t 23:59:59', strtotime('-1 month'));
        //上上月
        $lastLastMonth = date('m', strtotime('-2 month'));
        //上上月开始时间与结束时间
        $lastLastMonthStartTime = date('Y-m-01 00:00:00', strtotime('-2 month'));
        $lastLastMonthEndTime   = date('Y-m-t 23:59:59', strtotime('-2 month'));
        //单位端职位邀约投递次数
        $applyAmount = 0;
        //近三个月单位端职位邀约次数
        $currentMonthInviteAmount  = 0;
        $lastMonthInviteAmount     = 0;
        $lastLastMonthInviteAmount = 0;
        //近三个月单位端职位邀约投递次数
        $currentMonthApplyAmount  = 0;
        $lastMonthApplyAmount     = 0;
        $lastLastMonthApplyAmount = 0;
        //博士简历当月新增数量
        $currentMonthDoctorResumeAmount = 0;
        //博士简历上月新增数量
        $lastMonthDoctorResumeAmount = 0;
        foreach ($inviteData as $key => $value) {
            //单位端职位邀约投递次数
            $applyItemAmount = BaseJobApplyRecord::find()
                ->where([
                    'resume_id' => $value['resume_id'],
                    'job_id'    => $value['job_id'],
                ])
                ->andWhere([
                    '>',
                    'add_time',
                    $value['add_time'],
                ])
                ->groupBy('resume_id')
                ->count();
            $applyAmount     += $applyItemAmount;
            //近三个月单位端职位邀约次数
            if ($currentMonth == date('m', strtotime($value['add_time']))) {
                $currentMonthInviteAmount++;
            }
            if ($lastMonth == date('m', strtotime($value['add_time']))) {
                $lastMonthInviteAmount++;
            }
            if ($lastLastMonth == date('m', strtotime($value['add_time']))) {
                $lastLastMonthInviteAmount++;
            }
            //近三个月单位端职位邀约投递次数
            $currentMonthApplyAmountItem  = BaseJobApplyRecord::find()
                ->select(['resume_id'])
                ->where([
                    'resume_id' => $value['resume_id'],
                    'job_id'    => $value['job_id'],
                ])
                ->andWhere([
                    '>',
                    'add_time',
                    $value['add_time'],
                ])
                ->andWhere([
                    'between',
                    'add_time',
                    $currentMonthStartTime,
                    $currentMonthEndTime,
                ])
                ->groupBy('resume_id')
                ->asArray()
                ->column();
            $currentMonthApplyAmount      += count($currentMonthApplyAmountItem);
            $lastMonthApplyAmountItem     = BaseJobApplyRecord::find()
                ->select(['resume_id'])
                ->where([
                    'resume_id' => $value['resume_id'],
                    'job_id'    => $value['job_id'],
                ])
                ->andWhere([
                    '>',
                    'add_time',
                    $value['add_time'],
                ])
                ->andWhere([
                    'between',
                    'add_time',
                    $lastMonthStartTime,
                    $lastMonthEndTime,
                ])
                ->andWhere([
                    'not in',
                    'resume_id',
                    $currentMonthApplyAmountItem,
                ])
                ->groupBy('resume_id')
                ->column();
            $lastMonthApplyAmount         += count($lastMonthApplyAmountItem);
            $lastLastMonthApplyAmountItem = BaseJobApplyRecord::find()
                ->select(['resume_id'])
                ->where([
                    'resume_id' => $value['resume_id'],
                    'job_id'    => $value['job_id'],
                ])
                ->andWhere([
                    '>',
                    'add_time',
                    $value['add_time'],
                ])
                ->andWhere([
                    'between',
                    'add_time',
                    $lastLastMonthStartTime,
                    $lastLastMonthEndTime,
                ])
                ->andWhere([
                    'not in',
                    'resume_id',
                    array_merge($currentMonthApplyAmountItem, $lastMonthApplyAmountItem),
                ])
                ->groupBy('resume_id')
                ->column();
            $lastLastMonthApplyAmount     += count($lastLastMonthApplyAmountItem);
        }
        //获取上月之前所有的投递过该单位的博士简历ID
        $doctorResumeId = BaseJobApplyRecord::find()
            ->select(['jar.resume_id'])
            ->alias('jar')
            ->leftJoin(['r' => BaseResume::tableName()], 'r.id=jar.resume_id')
            ->leftJoin(['re' => BaseResumeEducation::tableName()], 're.id=r.last_education_id')
            ->where([
                're.education_id' => BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                'jar.company_id'  => $companyId,
            ])
            ->andWhere([
                '<',
                'jar.add_time',
                $lastMonthStartTime,
            ])
            ->groupBy('jar.resume_id')
            ->column();
        //上月新增博士简历ID
        $lastMonthDoctorResumeId     = BaseJobApplyRecord::find()
            ->select(['jar.resume_id'])
            ->alias('jar')
            ->leftJoin(['r' => BaseResume::tableName()], 'r.id=jar.resume_id')
            ->leftJoin(['re' => BaseResumeEducation::tableName()], 're.id=r.last_education_id')
            ->where([
                're.education_id' => BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                'jar.company_id'  => $companyId,
            ])
            ->andWhere([
                'between',
                'jar.add_time',
                $lastMonthStartTime,
                $lastMonthEndTime,
            ])
            ->andWhere([
                'not in',
                'jar.resume_id',
                $doctorResumeId,
            ])
            ->groupBy('jar.resume_id')
            ->column();
        $lastMonthDoctorResumeAmount += count($lastMonthDoctorResumeId);

        //当月新增博士简历ID
        $currentMonthDoctorResumeId     = BaseJobApplyRecord::find()
            ->select(['jar.resume_id'])
            ->alias('jar')
            ->leftJoin(['r' => BaseResume::tableName()], 'r.id=jar.resume_id')
            ->leftJoin(['re' => BaseResumeEducation::tableName()], 're.id=r.last_education_id')
            ->where([
                're.education_id' => BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                'jar.company_id'  => $companyId,
            ])
            ->andWhere([
                'between',
                'jar.add_time',
                $currentMonthStartTime,
                $currentMonthEndTime,
            ])
            ->andWhere([
                'not in',
                'jar.resume_id',
                array_merge($doctorResumeId, $lastMonthDoctorResumeId),
            ])
            ->groupBy('jar.resume_id')
            ->column();
        $currentMonthDoctorResumeAmount += count($currentMonthDoctorResumeId);

        return [
            'applyAmount'                    => $applyAmount,
            'currentMonthInviteAmount'       => $currentMonthInviteAmount,
            'lastMonthInviteAmount'          => $lastMonthInviteAmount,
            'lastLastMonthInviteAmount'      => $lastLastMonthInviteAmount,
            'currentMonthApplyAmount'        => $currentMonthApplyAmount,
            'lastMonthApplyAmount'           => $lastMonthApplyAmount,
            'lastLastMonthApplyAmount'       => $lastLastMonthApplyAmount,
            'currentMonthDoctorResumeAmount' => $currentMonthDoctorResumeAmount,
            'lastMonthDoctorResumeAmount'    => $lastMonthDoctorResumeAmount,
        ];
    }

}
