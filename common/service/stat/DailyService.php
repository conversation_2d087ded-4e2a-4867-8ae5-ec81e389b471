<?php

namespace common\service\stat;

use admin\models\Admin;
use admin\models\Member;
use common\base\models\BaseAdmin;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyContact;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseDictionary;
use common\base\models\BaseFile;
use common\base\models\BaseJobApply;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeLibrary;
use common\helpers\FileHelper;
use common\helpers\UUIDHelper;
use common\libs\Excel;
use common\models\JobApply;
use common\models\OffSiteJobApply;

/**
 * 一些日常的数据
 */
class DailyService extends BaseService
{

    public $beginTime;
    public $endTime;

    /**
     * 获取每一天的注册人数
     */
    public function getAllDataNewResumeData()
    {
        // 先找到全部的注册人数
        $pcData = BaseMember::find()
            ->select('count(*) as count, date(add_time) date')
            ->where([
                'type'        => BaseMember::TYPE_PERSON,
                'source_type' => BaseMember::SOURCE_TYPE_PC,
            ])
            ->groupBy('date(add_time)')
            ->asArray()
            ->all();

        $h5Data = BaseMember::find()
            ->select('count(*) as count, date(add_time) as date')
            ->where([
                'type'        => BaseMember::TYPE_PERSON,
                'source_type' => BaseMember::SOURCE_TYPE_H5,
            ])
            ->groupBy('date(add_time)')
            ->asArray()
            ->all();

        $allData = [];
        foreach ($pcData as $item) {
            $allData[$item['date']] = [
                'pc'    => $item['count'],
                'total' => $item['count'],
            ];
        }

        foreach ($h5Data as $item) {
            if (isset($allData[$item['date']])) {
                $allData[$item['date']]['h5']    = $item['count'];
                $allData[$item['date']]['total'] += $item['count'];
            } else {
                $allData[$item['date']] = [
                    'h5'    => $item['count'],
                    'total' => $item['count'],
                ];
            }
        }

        // 排序
        ksort($allData);

        return $allData;
    }

    public function getAllJobApplyList()
    {
        $onSiteData = JobApply::find()
            ->select('count(*) as count, date(add_time) as date')
            ->groupBy('date(add_time)')
            ->asArray()
            ->all();

        $offSiteData = OffSiteJobApply::find()
            ->select('count(*) as count, date(add_time) as date')
            ->groupBy('date(add_time)')
            ->asArray()
            ->all();

        $allData = [];

        foreach ($onSiteData as $item) {
            $allData[$item['date']] = [
                'onSite' => $item['count'],
                'total'  => $item['count'],
            ];
        }

        foreach ($offSiteData as $item) {
            if (isset($allData[$item['date']])) {
                $allData[$item['date']]['offSite'] = $item['count'];
                $allData[$item['date']]['total']   += $item['count'];
            } else {
                $allData[$item['date']] = [
                    'offSite' => $item['count'],
                    'total'   => $item['count'],
                ];
            }
        }

        ksort($allData);

        return $allData;
    }

    /**
     * 注册数据，报名数据，博士人数，75%以上人数
     */
    public function getResumeData()
    {
        // 全部注册人数
        $totalResume = BaseResume::find()
            ->count();
        // 教育水平分组
        $educationGroup = BaseResume::find()
            ->select([
                'top_education_code',
                'count(*) as count',
            ])
            ->groupBy('top_education_code')
            ->asArray()
            ->all();

        // %75以上的简历数据
        $top75Resume = BaseResume::find()
            ->where([
                '>=',
                'complete',
                75,
            ])
            ->count();

        // $params   = \Yii::$app->params['doubleMeetingActivity2022'];
        // $jobId    = $params['jobId'];
        // $jobApply = JobApply::find()
        //     ->where([
        //         'job_id' => $jobId,
        //     ])
        //     ->count();

        $resumeList = BaseResume::find()
            ->select([
                'count(*) as total',
                'date(add_time) as date',
            ])
            ->andFilterWhere([
                '>=',
                'add_time',
                $this->beginTime,
            ])
            ->andFilterWhere([
                '<=',
                'add_time',
                $this->endTime,
            ])
            ->groupBy('date(add_time)')
            ->orderBy('date(add_time) desc')
            ->indexBy('date')
            ->asArray()
            ->all();

        $libraryResumeCount = BaseResumeLibrary::find()
            ->count();

        // 今天注册人数
        $todayResume = BaseResume::find()
            ->where([
                '>=',
                'add_time',
                CUR_DATE . ' 00:00:00',
            ])
            ->count();

        // 昨天的日期
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        // 上周的日期
        $lastWeek = date('Y-m-d', strtotime('-7 day'));
        // 现在的时分秒
        $nowTime = date('H:i:s');
        // 昨天到现在这个时间点注册人数
        $yesterdayResume = BaseResume::find()
            ->where([
                '>=',
                'add_time',
                $yesterday . ' 00:00:00',
            ])
            ->andWhere([
                '<=',
                'add_time',
                $yesterday . " $nowTime",
            ])
            ->count();

        // 上周到现在这个时间点注册人数
        $lastWeekResume = BaseResume::find()
            ->where([
                '>=',
                'add_time',
                $lastWeek . ' 00:00:00',
            ])
            ->andWhere([
                '<=',
                'add_time',
                $lastWeek . " $nowTime",
            ])
            ->count();

        // 这个月的注册人数
        $thisMonthResume = BaseResume::find()
            ->where([
                '>=',
                'add_time',
                date('Y-m-01 00:00:00'),
            ])
            ->count();

        // $str = '人才总体数据' . PHP_EOL;
        $str = '注册人数总:' . $totalResume . '人 | ';
        // $str .= '双会报名人数:' . $jobApply . '人次' . PHP_EOL;
        $str .= '超过75%:' . $top75Resume . '人(' . round($top75Resume / $totalResume * 100, 2) . '%) | ';
        $str .= '人才库:' . $libraryResumeCount . '人(' . round($libraryResumeCount / $totalResume * 100,
                2) . '%)' . PHP_EOL;

        $str .= '教育水平分布' . PHP_EOL;
        foreach ($educationGroup as $item) {
            $education = BaseDictionary::getEducationName($item['top_education_code']) ?: '未填写';
            $str       .= $education . ':' . $item['count'] . '人(' . round($item['count'] / $totalResume * 100,
                    2) . '%)，';
        }

        $str .= PHP_EOL;

        $str .= '注册人数趋势' . PHP_EOL;
        $str .= "今天{$todayResume}人,昨天到现在这个时间点{$yesterdayResume}人,上周到现在这个时间点{$lastWeekResume}人,这个月注册{$thisMonthResume}人" . PHP_EOL;
        foreach ($resumeList as $item) {
            $str .= $item['date'] . ':' . $item['total'] . '人' . PHP_EOL;
        }

        return $str;
    }

    public function allJobApply()
    {
        $listOnSite  = $this->onSiteJobApply();
        $listOffSite = $this->offSiteJobApply();
        // 循环上面两个,把相同日期的加起来
        $list = [];
        foreach ($listOnSite as $date => $count) {
            $list[$date] = $count['total'];
        }

        foreach ($listOffSite as $date => $count) {
            if (isset($list[$date])) {
                $list[$date] += $count['total'];
            } else {
                $list[$date] = $count['total'];
            }
        }

        $str = '';
        foreach ($list as $key => $item) {
            $str .= $key . '日:站内投递' . $listOnSite[$key]['total'] . '个,站外投递' . $listOffSite[$key]['total'] . '个,合计' . $item . '个' . PHP_EOL;
        }

        return $str;
    }

    public function onSiteJobApply()
    {
        $list = BaseJobApply::find()
            ->select([
                'count(*) as total',
                'date(add_time) as date',
            ])
            ->andFilterWhere([
                '>=',
                'add_time',
                $this->beginTime,
            ])
            ->andFilterWhere([
                '<=',
                'add_time',
                $this->endTime,
            ])
            ->groupBy('date(add_time)')
            ->orderBy('date(add_time) desc')
            ->indexBy('date')
            ->asArray()
            ->all();

        return $list;
    }

    public function offSiteJobApply()
    {
        /**
         * # select count(*) as total, date(add_time)
         * # from off_site_job_apply
         * # group by date(add_time)
         * # order by date(add_time) desc;
         */
        $list = OffSiteJobApply::find()
            ->select([
                'count(*) as total',
                'date(add_time) as date',
            ])
            ->andFilterWhere([
                '>=',
                'add_time',
                $this->beginTime,
            ])
            ->andFilterWhere([
                '<=',
                'add_time',
                $this->endTime,
            ])
            ->groupBy('date(add_time)')
            ->orderBy('date(add_time) desc')
            ->indexBy('date')
            ->asArray()
            ->all();

        return $list;
    }

    public function getCompanyHomePageDate()
    {
        // UID	单位名称	会员类型	创建时间	创建人	入网来源	业务员	logo	单位主页背景	单位风采（数量）	英文名称	单位官网
        $list = BaseCompany::find()
            ->alias('c')
            ->select([
                'c.full_name',
                'c.id',
                'c.admin_id',
                'c.add_time',
                'create_admin_id',
                'c.source_type',
                'logo_url',
                'head_banner_url',
                'style_atlas',
                'website',
                'english_name',
            ])
            ->leftJoin(['cc' => BaseCompanyContact::tableName()], 'cc.company_id = c.id')
            ->innerJoin(['m' => BaseMember::tableName()], 'm.id = c.member_id')
            ->leftJoin(['a' => BaseAdmin::tableName()], 'a.id = c.admin_id')
            ->where([
                'c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
                'c.status'         => BaseCompany::STATUS_ACTIVE,
                'm.status'         => [
                    Member::STATUS_ACTIVE,
                    Member::STATUS_ILLEGAL,
                ],
            ])
            ->asArray()
            ->all();

        $headers = [
            'UID',
            '单位名称',
            '会员类型',
            '创建时间',
            '创建人',
            '入网来源',
            '业务员',
            'logo',
            '单位主页背景',
            '单位风采（数量）',
            '英文名称',
            '单位官网',
        ];

        foreach ($list as &$item) {
            $item['uid']            = UUIDHelper::encrypt(UUIDHelper::TYPE_COMPANY, $item['id']);
            $item['memberTypeName'] = BaseCompanyPackageConfig::getCompanyRoleMess($item['id'])['package_type_name'];
            $item['adminName']      = Admin::findOneVal(['id' => $item['admin_id']], 'name') ?: '';
            if ($item['source_type'] == BaseCompany::TYPE_SOURCE_ADD) {
                $item['createUserName'] = BaseAdmin::findOneVal(['id' => $item['create_admin_id']], 'name') ?: '';
            } else {
                $item['createUserName'] = BaseMember::findOneVal(['id' => $item['create_admin_id']], 'username') ?: '';
            }

            $item['sourceTypeName']  = BaseCompany::TYPE_SOURCE_LIST[$item['source_type']];
            $item['styleAtlasUrl']   = FileHelper::getListByIds($item['style_atlas']);
            $item['styleAtlasList']  = implode('<br>', $item['styleAtlasUrl']);
            $item['styleAtlasCount'] = count($item['styleAtlasUrl']);
            $data[]                  = [
                $item['uid'],
                $item['full_name'],
                $item['memberTypeName'],
                $item['add_time'],
                $item['createUserName'],
                $item['sourceTypeName'],
                $item['adminName'],
                $item['logo_url'],
                $item['head_banner_url'],
                $item['styleAtlasCount'],
                $item['english_name'],
                $item['website'],
            ];
        }

        $excel    = new Excel();
        $fileName = $excel->export($data, $headers);

        return $fileName;
    }


    //
    // public function setTime()
    // {
    //     return $this;
    // }

    public function setTimeLastDay()
    {
        $this->beginTime = date('Y-m-d 00:00:00', strtotime('-1 days'));
        $this->endTime   = date('Y-m-d 23:59:59', strtotime('-1 days'));

        return $this;
    }

    public function setTimeLast3Day()
    {
        $this->beginTime = date('Y-m-d 00:00:00', strtotime('-3 days'));
        $this->endTime   = date('Y-m-d 23:59:59', strtotime('-1 days'));

        return $this;
    }

    public function setTimeLast7Day()
    {
        $this->beginTime = date('Y-m-d 00:00:00', strtotime('-7 days'));
        $this->endTime   = date('Y-m-d 23:59:59', strtotime('-1 days'));

        return $this;
    }

    public function setTimeLast30Day()
    {
        $this->beginTime = date('Y-m-d 00:00:00', strtotime('-30 days'));
        $this->endTime   = date('Y-m-d 23:59:59', strtotime('-1 days'));

        return $this;
    }

    public function setTimeLast90Day()
    {
        $this->beginTime = date('Y-m-d 00:00:00', strtotime('-90 days'));
        $this->endTime   = date('Y-m-d 23:59:59', strtotime('-1 days'));

        return $this;
    }

    public function setTimeToday()
    {
        $this->beginTime = CUR_DATE;

        return $this;
    }
}
