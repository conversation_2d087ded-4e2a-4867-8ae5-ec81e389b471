<?php

namespace common\service\stat;

use common\base\models\BaseActivityForm;
use common\base\models\BaseActivityFormRegistrationForm;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseChatMessage;
use common\base\models\BaseChatRoom;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyClickLog;
use common\base\models\BaseCompanyGroupRelation;
use common\base\models\BaseDictionary;
use common\base\models\BaseHomePosition;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeIntention;
use common\base\models\BaseResumeIntentionAreaRelation;
use common\base\models\BaseResumeLibrary;
use common\base\models\BaseResumeLibraryInviteLog;
use common\base\models\BaseResumeResearchDirection;
use common\base\models\BaseResumeSetting;
use common\base\models\BaseResumeTag;
use common\base\models\BaseResumeTagRelation;
use common\base\models\BaseResumeWork;
use common\base\models\BaseShowcase;
use common\base\models\BaseShowcasePackingRelationship;
use common\helpers\TimeHelper;
use common\helpers\UUIDHelper;
use common\libs\Excel;
use common\models\Admin;
use common\models\HomePosition;
use common\models\Major;
use common\models\Showcase;
use frontendPc\models\ResumeEducation;

/**
 * 一些日常的数据
 */
class Tmp extends BaseService
{
    /**
     * 用于了解人才库人才分布情况，为销售等相关工作提供数据支撑
     *
     * 用户id
     * 姓名
     * 性别
     * 年龄（结合出生日期计算）
     * 政冶面貌
     * 最高学历毕业院校
     * 统招
     * 985/211
     * 留学
     * 最高学历水平
     * 专业(一级)
     * 专业(二级)
     * 专业(三级)
     * 意向省份（城币所属省份）
     * 意向城市
     */
    public function getResume20221102()
    {
        $list = BaseResumeLibrary::find()
            ->select('b.id,b.member_id,b.name,gender,a.resume_id')
            ->alias('a')
            ->innerJoin(['b' => BaseResume::tableName()], 'a.resume_id=b.id')
            ->asArray()
            ->all();

        $data = [];

        foreach ($list as &$item) {
            $item['uid']       = UUIDHelper::encrypt(UUIDHelper::TYPE_PERSON, $item['resume_id']);
            $item['gender']    = BaseResume::getGenderName($item['gender']);
            $item['info']      = BaseResume::getUserInfo($item['member_id']);
            $isEducationAbroad = ResumeEducation::find()
                ->where([
                    'resume_id' => $item['id'],
                    'is_abroad' => 1,
                ])
                ->exists();

            $isWorkAbroad = BaseResumeWork::find()
                ->where([
                    'resume_id' => $item['id'],
                    'is_abroad' => 1,
                ])
                ->exists();

            $majorId     = $item['info']['major_id'] ?? 0;
            $majorLevel3 = Major::find()
                ->where([
                    'id' => $majorId,
                ])
                ->select('id,name,parent_id')
                ->asArray()
                ->one();
            $majorLevel2 = Major::find()
                ->where([
                    'id' => $majorLevel3['parent_id'],
                ])
                ->select('id,name,parent_id')
                ->asArray()
                ->one();

            $majorLevel1 = Major::find()
                ->where([
                    'id' => $majorLevel2['parent_id'],
                ])
                ->select('id,name,parent_id')
                ->asArray()
                ->one();

            // 找意向城市
            $intentionList = BaseResumeIntention::getFullInfo($item['id']);

            // $categoryLevel2 = BaseCategoryJob::find()
            //     ->where([
            //         'id' => $item['job_category_id'],
            //     ])
            //     ->select('id,name,parent_id')
            //     ->asArray()
            //     ->one();
            //
            // $categoryLevel1 = BaseCategoryJob::find()
            //     ->where([
            //         'id' => $categoryLevel2['parent_id'],
            //     ])
            //     ->select('id,name,parent_id')
            //     ->asArray()
            //     ->one();

            foreach ($intentionList as $item1) {
                $data[] = [
                    $item['uid'],
                    $item['name'],
                    $item['gender'],
                    $item['info']['age'],
                    $item['info']['politicalStatusName'],
                    $item['info']['schoolName'],
                    $item['info']['is_recruitment'] == 1 ? '是' : '否',
                    $item['info']['is_project_school'] == 1 ? '是' : '否',
                    $isEducationAbroad == 1 ? '是' : '否',
                    $isWorkAbroad == 1 ? '是' : '否',
                    $isWorkAbroad || $isEducationAbroad ? '是' : '否',
                    $item['info']['educationName'],
                    $majorLevel1['name'] ?? '',
                    $majorLevel2['name'] ?? '',
                    $majorLevel3['name'] ?? '',
                    $item1['jobCategoryLevel1'],
                    $item1['jobCategoryLevel2'],
                    implode(',', $item1['provinceList']),
                    implode(',', $item1['cityList']),

                ];
            }
        }

        $header = [
            '用户id',
            '姓名',
            '性别',
            '年龄',
            '政治面貌',
            '最高学历毕业院校',
            '统招',
            '985/211',
            '留学',
            '海外工作经历',
            '海外经历',
            '最高学历水平',
            '专业(一级)',
            '专业(二级)',
            '专业(三级)',
            '意向职位(一级)',
            '意向职位(二级)',
            '意向省份',
            '意向城市',
        ];

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '人才库人才分布情况');
        bb($rs);
    }

    /**
     * 姓名
     * 最高学历 学校名称
     * 最高学历 所在专业
     * 最高学历 学历水平
     * 是否有海外经历(工作 实习 研究经验)
     */
    public function getResume20221115()
    {
        $list = BaseResume::find()
            ->alias('a')
            ->select('b.school,b.major_id,b.education_id,a.id,a.name,a.id')
            ->leftJoin(['b' => BaseResumeEducation::tableName()], 'a.last_education_id=b.id')
            ->asArray()
            ->all();

        $header = [
            '姓名',
            '最高学历 学校名称',
            '最高学历 所在专业',
            '最高学历 学历水平',
            '是否有海外工作经验',
            '是否有海外教育经验',
        ];
        $data   = [];
        foreach ($list as &$item) {
            $majorId     = $item['major_id'] ?? 0;
            $majorLevel3 = Major::find()
                ->where([
                    'id' => $majorId,
                ])
                ->select('id,name,parent_id')
                ->asArray()
                ->one();

            $item['majorName'] = $majorLevel3['name'] ?? '';

            $majorLevel2 = Major::find()
                ->where([
                    'id' => $majorLevel3['parent_id'],
                ])
                ->select('id,name,parent_id')
                ->asArray()
                ->one();

            $majorLevel1 = Major::find()
                ->where([
                    'id' => $majorLevel2['parent_id'],
                ])
                ->select('id,name,parent_id')
                ->asArray()
                ->one();

            $isEducationAbroad = ResumeEducation::find()
                ->where([
                    'resume_id' => $item['id'],
                    'is_abroad' => 1,
                ])
                ->exists();

            $isWorkAbroad = BaseResumeWork::find()
                ->where([
                    'resume_id' => $item['id'],
                    'is_abroad' => 1,
                ])
                ->exists();

            $educationName = BaseDictionary::getEducationName($item['education_id']);

            $data[] = [
                $item['name'],
                $item['school'],
                $majorLevel1['name'] ?? '',
                $educationName,
                $isWorkAbroad ? '是' : '否',
                $isEducationAbroad ? '是' : '否',
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '人才库人才分布情况');
        bb($rs);
    }

    /**
     * 意向城市里有  广州  的，海外博士的一些数据
     *
     * 姓名    性别    年龄    现居住地    毕业时间    意向城市    意向职位    最高学历 学校名称    最高学历 所在专业    最高学历 学历水平    是否有海外工作经验    是否有海外教育经验
     *   最近的一份工作单位名称
     */
    public function getResume20230131()
    {
        // 广州的areaid = 1965
        $list = BaseResumeIntention::find()
            ->select('a.id,a.name,a.age,b.area_id,a.gender,a.age,last_education_id,c.school,c.major_id,c.education_id,residence,c.end_date')
            ->alias('b')
            ->innerJoin(['a' => BaseResume::tableName()], 'a.id=b.resume_id')
            ->innerJoin(['c' => BaseResumeEducation::tableName()], 'c.id=a.last_education_id')
            ->where(['a.top_education_code' => BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE])
            ->andWhere(new \yii\db\Expression("FIND_IN_SET(:area_id, area_id)", [":area_id" => 1965]))
            ->asArray()
            ->all();

        $header = [
            '姓名',
            '性别',
            '年龄',
            '现居住地',
            '毕业时间',
            '意向城市',
            '意向职位',
            '最高学历 学校名称',
            '最高学历 所在专业一级',
            '最高学历 所在专业二级',
            '最高学历 所在专业三级',
            '最高学历 学历水平',
            '是否有海外工作经验',
            '是否有海外教育经验',
            '最近的一份工作单位名称',
        ];

        $data = [];

        foreach ($list as $item) {
            $majorId     = $item['major_id'] ?? 0;
            $majorLevel3 = Major::find()
                ->where([
                    'id' => $majorId,
                ])
                ->select('id,name,parent_id')
                ->asArray()
                ->one();

            $item['majorName'] = $majorLevel3['name'] ?? '';

            $majorLevel2 = Major::find()
                ->where([
                    'id' => $majorLevel3['parent_id'],
                ])
                ->select('id,name,parent_id')
                ->asArray()
                ->one();

            $majorLevel1 = Major::find()
                ->where([
                    'id' => $majorLevel2['parent_id'],
                ])
                ->select('id,name,parent_id')
                ->asArray()
                ->one();

            $isEducationAbroad = ResumeEducation::find()
                ->where([
                    'resume_id' => $item['id'],
                    'is_abroad' => 1,
                ])
                ->exists();

            $isWorkAbroad = BaseResumeWork::find()
                ->where([
                    'resume_id' => $item['id'],
                    'is_abroad' => 1,
                ])
                ->exists();

            $educationName = BaseDictionary::getEducationName($item['education_id']);

            $residence = BaseArea::getAreaName($item['residence']);

            $allIntention = BaseResumeIntention::find()
                ->where([
                    'resume_id' => $item['id'],
                ])
                ->asArray()
                ->all();

            $category = [];
            foreach ($allIntention as $intention) {
                $category[] = BaseCategoryJob::getName($intention['job_category_id']);
            }

            $categoryName = implode('，', $category);
            // 过滤重复
            $categoryName = implode('，', array_unique(explode('，', $categoryName)));

            $work = BaseResumeWork::find()
                ->where([
                    'resume_id' => $item['id'],
                ])
                ->orderBy('end_date desc')
                ->asArray()
                ->one();

            $companyName = $work['company'] ?? '';

            /**
             * '姓名',
             * '性别',
             * '年龄',
             * '现居住地',
             * '毕业时间',
             * '意向城市',
             * '意向职位',
             * '最高学历 学校名称',
             * '最高学历 所在专业一级',
             * '最高学历 所在专业二级',
             * '最高学历 学历水平三级',
             * '是否有海外工作经验',
             * '是否有海外教育经验',
             * '最近的一份工作单位名称'
             */
            $data[] = [
                $item['name'],
                BaseResume::getGenderName($item['gender']),
                $item['age'],
                $residence,
                $item['end_date'],
                '广州',
                $categoryName,
                $item['school'],
                $majorLevel1['name'],
                $majorLevel2['name'],
                $majorLevel3['name'],
                $educationName,
                $isWorkAbroad ? '是' : '否',
                $isEducationAbroad ? '是' : '否',
                $companyName,
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '人才库人才分布情况' . time());

        bb($rs);
    }

    /**
     * http://zentao.jugaocai.com/index.php?m=story&f=view&storyID=356&version=0&param=0&storyType=story
     *
     * 应聘职位名称 应聘者姓名 应聘时间  应聘者最高学历 应聘者专业 应聘者籍贯 应聘者年龄 博士毕业院校 硕士毕业院校 本科毕业院校 博士后出站单位
     */
    public function getResume20230220()
    {
        $companyId = 13332;
        // 找到所有投递
        $list = BaseJobApply::find()
            ->alias('a')
            ->select('a.id,a.job_id,a.resume_id,a.status,a.add_time,b.name as jobName,c.name,c.last_education_id,d.education_id,d.major_id,age,native_place_area_id,e.name as majorName')
            ->innerJoin(['b' => BaseJob::tableName()], 'a.job_id=b.id')
            ->innerJoin(['c' => BaseResume::tableName()], 'a.resume_id=c.id')
            ->innerJoin(['d' => BaseResumeEducation::tableName()], 'c.last_education_id=d.id')
            ->innerJoin(['e' => BaseMajor::tableName()], 'e.id=d.major_id')
            ->where([
                'b.company_id' => $companyId,
            ])
            ->asArray()
            ->all();

        $header = [
            '应聘职位名称',
            '应聘者姓名',
            '应聘时间',
            '应聘者最高学历',
            '应聘者专业',
            '应聘者籍贯',
            '应聘者年龄',
            '博士毕业院校',
            '硕士毕业院校',
            '本科毕业院校',
            '博士后出站单位',
        ];

        $data = [];

        foreach ($list as $item) {
            $doctor = BaseResumeEducation::find()
                ->where([
                    'resume_id'    => $item['resume_id'],
                    'education_id' => BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                    'status'       => 1,
                ])
                ->asArray()
                ->one();

            $master = BaseResumeEducation::find()
                ->where([
                    'resume_id'    => $item['resume_id'],
                    'education_id' => BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE,
                    'status'       => 1,
                ])
                ->asArray()
                ->one();

            $undergraduate = BaseResumeEducation::find()
                ->where([
                    'resume_id'    => $item['resume_id'],
                    'education_id' => BaseResumeEducation::EDUCATION_TYPE_UNDERGRADUATE_CODE,
                    'status'       => 1,
                ])
                ->asArray()
                ->one();

            $doctorAfter = BaseResumeWork::find()
                               ->where([
                                   'resume_id'  => $item['resume_id'],
                                   'is_postdoc' => 1,
                                   'status'     => 1,
                               ])
                               ->asArray()
                               ->one()['company'];

            $data[] = [
                $item['jobName'],
                $item['name'],
                $item['add_time'],
                BaseDictionary::getEducationName($item['education_id']),
                $item['majorName'],
                BaseArea::getAreaName($item['native_place_area_id']) ?: '',
                $item['age'],
                $doctor['school'] ?? '',
                $master['school'] ?? '',
                $undergraduate['school'] ?? '',
                $doctorAfter ?? '',
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '人才库人才分布情况' . time());

        bb($rs);
    }

    /**
     * 姓名    手机号    毕业时间    毕业院校    学历    当前所在地
     * 教育经历包含下面院校
     * 哈尔滨工业大学
     * 哈尔滨工程大学
     * 吉林大学
     * 东北师范大学
     * 长春理工大学
     * 黑龙江科技大学
     * 黑龙江大学
     * 东北林业大学
     * 东北农业大学
     * 哈尔滨师范大学
     */
    public function getResume20230316()
    {
        $list = BaseResume::find()
            ->select('a.id,a.name,c.mobile,b.end_date,b.school,b.begin_date,residence,education_id')
            ->alias('a')
            ->innerJoin(['b' => BaseResumeEducation::tableName()], 'a.last_education_id=b.id')
            ->innerJoin(['c' => BaseMember::tableName()], 'a.member_id=c.id')
            ->where([
                'in',
                'b.school',
                [
                    // '哈尔滨工业大学',
                    // '哈尔滨工程大学',
                    // '吉林大学',
                    // '东北师范大学',
                    // '长春理工大学',
                    // '黑龙江科技大学',
                    // '黑龙江大学',
                    // '东北林业大学',
                    // '东北农业大学',
                    // '哈尔滨师范大学',
                    // '四川大学',
                    // '电子科技大学',
                    // '西南交通大学',
                    // '西南财经大学',
                    // '西南石油大学',
                    // '成都理工大学',
                    // '重庆大学',
                    // '西南大学',
                    // '长安大学',
                    // '西北工业大学',
                    // '西安交通大学',
                    // '西安电子科技大学',
                    // '陕西师范大学',
                    // '西北大学',
                    // '西安建筑科技大学',
                    // '陕西科技大学',
                    // '武汉大学',
                    // '华中科技大学',
                    // '华中农业大学',
                    // '中南财经政法大学',
                    // '武汉理工大学',
                    // '中国地质大学（武汉）',
                    // '华中师范大学',
                    // '哈尔滨工业大学',
                    // '哈尔滨工程大学',
                    // '吉林大学',
                    // '东北师范大学',
                    // '长春理工大学',
                    // '黑龙江科技大学',
                    // '黑龙江大学',
                    // '东北林业大学',
                    // '东北农业大学',
                    // '哈尔滨师范大学',
                ],
            ])
            ->andWhere([
                'between',
                'b.end_date',
                '2022-01-01',
                '2023-12-31',
            ])
            ->andWhere([
                'b.education_id' => [
                    BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                ],
            ])
            ->asArray()
            ->all();

        $header = [
            '姓名',
            '手机号',
            '毕业时间',
            '毕业院校',
            '学历',
            '当前所在地',
        ];
        $data   = [];
        foreach ($list as &$item) {
            $item['residence'] = BaseArea::getAreaName($item['residence']);
            $item['education'] = BaseDictionary::getEducationName($item['education_id']);
            $data[]            = [
                $item['name'],
                $item['mobile'],
                $item['end_date'],
                $item['school'],
                $item['education'],
                $item['residence'],
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '人才库人才分布情况' . time());
        bb($rs);
    }

    /**
     * 最高学历：硕士及以上
     *
     * 求职意向：教学岗（高等院校）——专职教师/教学科研岗
     *
     * 最近活跃时间：2022.11.1-2023.2.28
     *
     * 毕业时间：2023.1-2023.12
     *
     * 简历隐藏状态：关闭
     *
     * 表头是UID\联系邮箱\最高学历\毕业时间\最近活跃时间\简历隐藏状态
     *
     */
    public function getResume20230418()
    {
        $list = BaseResume::find()
            ->select('a.id,a.name,c.email,b.end_date,b.school,residence,education_id,last_active_time')
            ->alias('a')
            ->innerJoin(['b' => BaseResumeEducation::tableName()], 'a.last_education_id=b.id')
            ->innerJoin(['c' => BaseMember::tableName()], 'a.member_id=c.id')
            ->innerJoin(['d' => BaseResumeIntention::tableName()], 'a.id=d.resume_id')
            ->innerJoin(['e' => BaseResumeSetting::tableName()], 'a.id=e.resume_id')
            ->where([
                'between',
                'b.end_date',
                '2023-01-01',
                '2023-12-31',
            ])
            ->andWhere([
                'between',
                'c.last_active_time',
                '2023-01-01 00:00:00',
                '2023-02-28 23:59:59',
            ])
            ->andWhere(['d.job_category_id' => 33])
            ->andWhere([
                'e.is_hide_resume' => [
                    0,
                    2,
                ],
            ])
            ->andWhere([
                'b.education_id' => [
                    BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                ],
            ])
            ->asArray()
            ->all();

        $header = [
            'UID',
            '联系邮箱',
            '最高学历',
            '毕业时间',
            '最近活跃时间',
            '简历隐藏状态',
        ];

        $data = [];
        foreach ($list as &$item) {
            $item['education'] = BaseDictionary::getEducationName($item['education_id']);
            $item['uid']       = UUIDHelper::encrypt(1, $item['id']);

            $data[] = [
                $item['uid'],
                $item['email'],
                $item['education'],
                $item['end_date'],
                $item['last_active_time'],
                '关闭',
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '人才库人才分布情况' . time());
        bb($rs);
    }

    /**
     * http://zentao.jugaocai.com/index.php?m=story&f=view&id=421
     *
     * 421 投递数据导出-中国科学院计算技术研究所
     */
    public function getResume20230426()
    {
        /**
         * 姓名    最高学历    学科    毕业院校    投递职位    用人部门
         */
        $list = BaseJobApplyRecord::find()
            ->where(['company_id' => 29517])
            ->asArray()
            ->orderBy('id desc')
            ->all();

        foreach ($list as $item) {
            $resume    = BaseResume::findOne($item['resume_id']);
            $edId      = $resume->last_education_id;
            $education = BaseResumeEducation::findOne($edId);
            $major     = BaseMajor::findOne($education->major_id);

            $job = BaseJob::findOne($item['job_id']);

            $data[] = [
                $resume->name,
                BaseDictionary::getEducationName($education->education_id),
                $major->name ?: $education->major_custom,
                $education->school,
                $job->name,
                $job->department,
                substr($item['add_time'], 0, 10),
            ];
        }

        $header = [
            '姓名',
            '最高学历',
            '学科',
            '毕业院校',
            '投递职位',
            '用人部门',
            '投递时间',
        ];

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '投递数据导出-中国科学院计算技术研究所' . time());
        bb($rs);
    }

    /**
     * http://zentao.jugaocai.com/index.php?m=story&f=view&id=447
     *
     *
     * 筛选条件：毕业时间2023.5.1-2023.12.31
     *
     * 最高学历硕士/博士
     * 最近活跃时间3.1-4.30
     * 简历完善度40%+
     * 简历隐藏状态关闭
     *
     *     'UID',
     * '联系邮箱',
     * '最高学历',
     * '毕业时间',
     * '最高学历',
     * '最近活跃时间',
     * '简历完善度',
     */
    public function getResume20230518()
    {
        $list = BaseResume::find()
            ->select('a.id,a.name,c.email,b.end_date,b.school,residence,education_id,last_active_time,complete')
            ->alias('a')
            ->innerJoin(['b' => BaseResumeEducation::tableName()], 'a.last_education_id=b.id')
            ->innerJoin(['c' => BaseMember::tableName()], 'a.member_id=c.id')
            ->innerJoin(['d' => BaseResumeIntention::tableName()], 'a.id=d.resume_id')
            ->innerJoin(['e' => BaseResumeSetting::tableName()], 'a.id=e.resume_id')
            ->where([
                'between',
                'b.end_date',
                '2023-05-01',
                '2023-12-31',
            ])
            ->andWhere([
                'between',
                'c.last_active_time',
                '2023-03-01 00:00:00',
                '2023-04-30 23:59:59',
            ])
            ->andWhere([
                'e.is_hide_resume' => [
                    0,
                    2,
                ],
            ])
            ->andWhere([
                'b.education_id' => [
                    BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                ],
            ])
            ->andWhere([
                '>=',
                'complete',
                40,
            ])
            ->asArray()
            ->all();

        $header = [
            'UID',
            '联系邮箱',
            '最高学历',
            '毕业时间',
            '最近活跃时间',
            '简历完善度',
        ];

        $data = [];
        foreach ($list as &$item) {
            $item['education'] = BaseDictionary::getEducationName($item['education_id']);
            $item['uid']       = UUIDHelper::encrypt(1, $item['id']);

            $data[] = [
                $item['uid'],
                $item['email'],
                $item['education'],
                $item['end_date'],
                $item['last_active_time'],
                $item['complete'],
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '人才库人才分布情况' . time());
        bb($rs);
    }

    /**
     * 应客户推广部单位活动推广需求，先须配合导出一些满足条件的人才id，具体如下：
     *
     * 1、导出所有“现居住地”为“上海”、且最高学历为“博士研究生”的人才id；
     *
     * 2、导出“现居住地”或“意向城市”为“上海”且最高学历为“博士研究生”的人才id（id去重）；
     *
     * 3、导出“现居住地”或“意向城市”为“上海”且最高学历为“硕士研究生”且“近90天活跃”的人才id（id去重）；
     *
     * 4、导出所有“现居住地”为“重庆”、且最高学历为“博士研究生”的人才id（id去重）
     *
     * 1、以上4条需求分开导出文件；
     *
     * 2、文件格式：txt；
     *
     * 3、多个id“、”隔开；
     */
    public function getResume202305181()
    {
        // 上海
        $cityId1 = 801;
        $cityId2 = 802;

        // 重庆
        $cityId3 = 2323;
        $cityId4 = 2324;

        //

        $list = BaseResume::find()
            ->select('a.id,a.name,c.email,b.end_date,b.school,residence,education_id,last_active_time,complete,residence')
            ->alias('a')
            ->innerJoin(['b' => BaseResumeEducation::tableName()], 'a.last_education_id=b.id')
            ->innerJoin(['c' => BaseMember::tableName()], 'a.member_id=c.id')
            ->innerJoin(['e' => BaseResumeSetting::tableName()], 'a.id=e.resume_id')
            ->where([
                'residence' => [
                    $cityId1,
                    $cityId2,
                ],
            ])
            ->andWhere([
                'b.education_id' => [
                    BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                ],
            ])
            ->asArray()
            ->all();

        $data = [];
        foreach ($list as $item) {
            $item['education'] = BaseDictionary::getEducationName($item['education_id']);
            $item['uid']       = UUIDHelper::encrypt(1, $item['id']);

            $data[] = $item['uid'];
        }

        $rs = implode('、', $data);

        aa($rs);
        aa("=================");

        $list1 = BaseResume::find()
            ->select('a.id,a.name,c.email,b.end_date,b.school,residence,education_id,last_active_time,complete,residence,d.area_id')
            ->alias('a')
            ->innerJoin(['b' => BaseResumeEducation::tableName()], 'a.last_education_id=b.id')
            ->innerJoin(['c' => BaseMember::tableName()], 'a.member_id=c.id')
            ->innerJoin(['d' => BaseResumeIntentionAreaRelation::tableName()], 'a.id=d.resume_id')
            ->innerJoin(['e' => BaseResumeSetting::tableName()], 'a.id=e.resume_id')
            ->where([
                'or',
                [
                    'residence' => [
                        $cityId1,
                        $cityId2,
                    ],
                ],
                [
                    'd.area_id' => [
                        $cityId1,
                        $cityId2,
                    ],
                ],
            ])
            ->andWhere([
                'b.education_id' => [
                    BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                ],
            ])
            ->asArray()
            ->all();

        foreach ($list1 as $item) {
            $item['education'] = BaseDictionary::getEducationName($item['education_id']);
            $item['uid']       = UUIDHelper::encrypt(1, $item['id']);

            $data2[] = $item['uid'];
        }

        $rs = implode('、', $data2);

        aa($rs);
        aa("=================");

        $list2 = BaseResume::find()
            ->select('a.id,a.name,c.email,b.end_date,b.school,residence,education_id,last_active_time,complete,residence,d.area_id')
            ->alias('a')
            ->innerJoin(['b' => BaseResumeEducation::tableName()], 'a.last_education_id=b.id')
            ->innerJoin(['c' => BaseMember::tableName()], 'a.member_id=c.id')
            ->innerJoin(['d' => BaseResumeIntentionAreaRelation::tableName()], 'a.id=d.resume_id')
            ->innerJoin(['e' => BaseResumeSetting::tableName()], 'a.id=e.resume_id')
            ->where([
                'or',
                [
                    'residence' => [
                        $cityId1,
                        $cityId2,
                    ],
                ],
                [
                    'd.area_id' => [
                        $cityId1,
                        $cityId2,
                    ],
                ],
            ])
            ->andWhere([
                'b.education_id' => [
                    BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE,
                ],
            ])
            ->andWhere([
                'between',
                'last_active_time',
                '2023-02-16 00:00:00',
                '2023-05-19 23:59:59',
            ])
            ->asArray()
            ->all();

        foreach ($list2 as $item) {
            $item['education'] = BaseDictionary::getEducationName($item['education_id']);
            $item['uid']       = UUIDHelper::encrypt(1, $item['id']);

            $data1[] = $item['uid'];
        }

        $rs = implode('、', $data1);

        aa($rs);
        aa("=================");

        $list3 = BaseResume::find()
            ->select('a.id,a.name,c.email,b.end_date,b.school,residence,education_id,last_active_time,complete,residence,d.area_id')
            ->alias('a')
            ->innerJoin(['b' => BaseResumeEducation::tableName()], 'a.last_education_id=b.id')
            ->innerJoin(['c' => BaseMember::tableName()], 'a.member_id=c.id')
            ->innerJoin(['d' => BaseResumeIntentionAreaRelation::tableName()], 'a.id=d.resume_id')
            ->innerJoin(['e' => BaseResumeSetting::tableName()], 'a.id=e.resume_id')
            ->where([
                'residence' => [
                    $cityId3,
                    $cityId4,
                ],
            ])
            ->andWhere([
                'b.education_id' => [
                    BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                ],
            ])
            ->asArray()
            ->all();

        foreach ($list3 as $item) {
            $item['education'] = BaseDictionary::getEducationName($item['education_id']);
            $item['uid']       = UUIDHelper::encrypt(1, $item['id']);

            $data3[] = $item['uid'];
        }

        $rs = implode('、', $data3);

        aa($rs);
        aa("=================");

        exit;
    }

    /**
     * 1、人才简历完善度＜40%，且最近7天活跃（包括导出日在内的近7日）；
     *
     * 2、表格导出：
     *
     * 人才id
     * 简历完善度
     * 注册时间
     * 注册端口
     * 最近活跃时间
     */
    public function getResume20230524()
    {
        $list = BaseResume::find()
            ->alias('a')
            ->select('a.id,complete,last_active_time,a.add_time,source_type')
            ->innerJoin(['b' => BaseMember::tableName()], 'a.member_id=b.id')
            ->where([
                '<=',
                'complete',
                40,
            ])
            ->andWhere([
                '>=',
                'last_active_time',
                '2023-05-17 00:00:00',
            ])
            ->asArray()
            ->all();

        $header = [
            '人才id',
            '简历完善度',
            '注册时间',
            '注册端口',
            '最近活跃时间',
        ];

        $data = [];

        $typeList = BaseMember::SOURCE_TYPE_LIST;
        foreach ($list as &$item) {
            $item['uid']    = UUIDHelper::encrypt(1, $item['id']);
            $item['source'] = $typeList[$item['source_type']];

            $data[] = [
                $item['uid'],
                $item['complete'],
                $item['add_time'],
                $item['source'],
                $item['last_active_time'],
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '人才库人才分布情况' . time());
        bb($rs);
    }

    public function getResume20230625()
    {
        // select a.add_time,c.full_name,c.department,j.name,r.name,re.major_id_level_3
        // from job_apply_record a
        // inner  join  company c on a.company_id = c.id
        // inner join  job j on a.job_id = j.id
        // inner join resume r on a.resume_id = r.id
        // inner join resume_education re on r.last_education_id = re.id
        // where a.company_id = 595
        $list = BaseJobApplyRecord::find()
            ->alias('a')
            ->select('a.add_time,c.full_name,c.department,j.name as jobName,r.name,re.major_id_level_3,major_custom,d.name as major,re.education_id,school')
            ->innerJoin(['c' => BaseCompany::tableName()], 'a.company_id=c.id')
            ->innerJoin(['j' => BaseJob::tableName()], 'a.job_id=j.id')
            ->innerJoin(['r' => BaseResume::tableName()], 'a.resume_id=r.id')
            ->innerJoin(['re' => BaseResumeEducation::tableName()], 'r.last_education_id=re.id')
            ->leftJoin(['d' => BaseMajor::tableName()], 're.major_id_level_3=d.id')
            ->where([
                'a.company_id' => 595,
            ])
            // a.add_time 在23年1月到6月
            ->andWhere([
                'between',
                'a.add_time',
                '2023-01-01 00:00:00',
                '2023-06-30 23:59:59',
            ])
            ->asArray()
            ->all();

        // 姓名  最高学历 学科 毕业院校 投递职位 用人部门 投递时间

        $header = [
            '姓名',
            '最高学历',
            '学科',
            '毕业院校',
            '投递职位',
            '用人部门',
            '投递时间',
        ];

        $data = [];

        foreach ($list as &$item) {
            $data[] = [
                $item['name'],
                BaseDictionary::getEducationName($item['education_id']),
                $item['major_custom'] ? $item['major_custom'] : $item['major'],
                $item['school'],
                $item['jobName'],
                $item['department'],
                substr($item['add_time'], 0, 10),
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '人才库人才分布情况' . time());
        bb($rs);
    }

    // 禅道4987
    // 日期	站内投递（次）	站外投递（次）-总	站外投递-网址	站外投递-邮箱	投递次数-PC端	投递次数-移动端	求职者注册量（人）-总	求职者注册量-PC端	求职者注册量-移动端	单位端投递邀约次数（次）
    public function get4987()
    {
        // 从今年年初到现在,每天一行
        // 先算今天是第几天
        $today = date('Y-m-d');
        $day   = date('z', strtotime($today)) + 1;

        $header = [
            '日期',
            '站内投递（次）',
            '站外投递（次）-总',
            '站外投递-网址',
            '站外投递-邮箱',
            '投递次数-PC端',
            '投递次数-移动端',
            '求职者注册量（人）-总',
            '求职者注册量-PC端',
            '求职者注册量-移动端',
            '单位端投递邀约次数（次）',
        ];
        // 从第一天开始
        $list = [];
        for ($i = 1; $i <= $day; $i++) {
            $date = date('Y-m-d', strtotime($today . ' -' . ($day - $i) . ' day'));
            // 站内投递次数
            $in = BaseJobApplyRecord::find()
                ->where([
                    'between',
                    'add_time',
                    $date . ' 00:00:00',
                    $date . ' 23:59:59',
                ])
                ->andWhere([
                    'delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_INSIDE,
                ])
                ->count();

            // 站外投递次数-网址
            $outLink = BaseJobApplyRecord::find()
                ->where([
                    'between',
                    'add_time',
                    $date . ' 00:00:00',
                    $date . ' 23:59:59',
                ])
                ->andWhere([
                    'delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE,
                    'delivery_way'  => BaseJobApplyRecord::DELIVERY_WAY_LINK,
                ])
                ->count();

            // 站外投递次数-邮箱
            $outEmail = BaseJobApplyRecord::find()
                ->where([
                    'between',
                    'add_time',
                    $date . ' 00:00:00',
                    $date . ' 23:59:59',
                ])
                ->andWhere([
                    'delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE,
                    'delivery_way'  => BaseJobApplyRecord::DELIVERY_WAY_EMAIL,
                ])
                ->count();

            // 投递次数-PC端
            $pc = BaseJobApplyRecord::find()
                ->where([
                    'between',
                    'add_time',
                    $date . ' 00:00:00',
                    $date . ' 23:59:59',
                ])
                ->andWhere([
                    'platform' => BaseJobApplyRecord::PLATFORM_PC,
                ])
                ->count();

            // 投递次数-移动端
            $mobile = BaseJobApplyRecord::find()
                ->where([
                    'between',
                    'add_time',
                    $date . ' 00:00:00',
                    $date . ' 23:59:59',
                ])
                ->andWhere([
                    'platform' => BaseJobApplyRecord::PLATFORM_H5,
                ])
                ->count();

            // 求职者注册量（人）-总
            $user = BaseResume::find()
                ->where([
                    'between',
                    'add_time',
                    $date . ' 00:00:00',
                    $date . ' 23:59:59',
                ])
                ->count();

            // 求职者注册量-PC端
            $userPc = BaseMember::find()
                ->where([
                    'between',
                    'add_time',
                    $date . ' 00:00:00',
                    $date . ' 23:59:59',
                ])
                ->andWhere([
                    'type'        => BaseMember::TYPE_PERSON,
                    'source_type' => 1,
                ])
                ->count();

            // 求职者注册量-移动端
            $userMobile = BaseMember::find()
                ->where([
                    'between',
                    'add_time',
                    $date . ' 00:00:00',
                    $date . ' 23:59:59',
                ])
                ->andWhere([
                    'type'        => BaseMember::TYPE_PERSON,
                    'source_type' => 2,
                ])
                ->count();

            // 单位端投递邀约次数（次）
            $company = BaseResumeLibraryInviteLog::find()
                ->where([
                    'between',
                    'add_time',
                    $date . ' 00:00:00',
                    $date . ' 23:59:59',
                ])
                ->count();

            $list[] = [
                $date,
                $in,
                $outLink + $outEmail,
                $outLink,
                $outEmail,
                $pc,
                $mobile,
                $user,
                $userPc,
                $userMobile,
                $company,
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($list, $header, '禅道4987' . time());
        bb($rs);
    }

    /**
     * @return void累计报名次数（次）-总    累计报名次数-PC端    累计报名次数-H5端    累计报名人数    累计注册人数    累计注册人数（博士）    累计注册人数（硕士）
     * 统计2023.03.09-2023.07.12间，全端口通过双会报名表单成功报名的总次数    统计2023.03.09-2023.07.12间，在PC端通过双会报名表单成功报名的总次数
     * 统计2023.03.09-2023.07.12间，在H5端通过双会报名表单成功报名的总次数    统计2023.03.09-2023.07.12间通过双会报名表单成功报名的总人数（人去重）
     * "统计2023.03.09-2023.07.12间，通过双会报名表单注册的总人数；去重 判断逻辑：首次报名时间（日维度）=注册时间（日维度），视为通过表单注册的用户"    累计注册人数中博士占比
     * 累计注册人数中博士占比
     */
    public function get49871()
    {
        // 累计报名次数（次）-总
        $total       = BaseActivityForm::find()
            ->count();
        $totalResume = BaseActivityForm::find()
            ->groupBy('resume_id')
            ->count();
    }

    /**
     * http://zentao.jugaocai.com/index.php?m=story&f=view&id=490
     *
     *
     * ①学历：博士研究生
     *
     * ②意向城市：北京
     * ③活跃时间：2023年5-7月
     * ④简历隐藏状态：关闭
     *
     *     'UID',
     * '联系邮箱',
     * '最高学历',
     * '毕业时间',
     * '最高学历',
     * '最近活跃时间',
     * '简历完善度',
     */
    public function getResume20230718()
    {
        $list = BaseResume::find()
            ->select('a.id,a.name,c.email,b.end_date,b.school,residence,education_id,last_active_time,complete')
            ->alias('a')
            ->innerJoin(['b' => BaseResumeEducation::tableName()], 'a.last_education_id=b.id')
            ->innerJoin(['c' => BaseMember::tableName()], 'a.member_id=c.id')
            ->innerJoin(['d' => BaseResumeIntentionAreaRelation::tableName()], 'a.id=d.resume_id')
            ->innerJoin(['e' => BaseResumeSetting::tableName()], 'a.id=e.resume_id')
            ->where([
                'between',
                'c.last_active_time',
                '2023-05-01 00:00:00',
                '2023-07-30 23:59:59',
            ])
            ->andWhere([
                'e.is_hide_resume' => [
                    0,
                    2,
                ],
                'd.area_id'        => [
                    1,
                    2,
                ],
            ])
            ->andWhere([
                'b.education_id' => [
                    BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                ],
            ])
            ->asArray()
            ->all();

        $header = [
            'UID',
            '联系邮箱',
            '最高学历',
            '毕业时间',
            '最近活跃时间',
            '简历完善度',
            '站内投递次数',
            '站外投递次数',
        ];

        $data = [];
        foreach ($list as &$item) {
            $item['education'] = BaseDictionary::getEducationName($item['education_id']);
            $item['uid']       = UUIDHelper::encrypt(1, $item['id']);

            $in = BaseJobApplyRecord::find()
                ->where([
                    'delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_INSIDE,
                    'resume_id'     => $item['id'],
                ])
                ->count();

            // 站外投递次数-邮箱
            $out = BaseJobApplyRecord::find()
                ->where([
                    'delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE,
                    'resume_id'     => $item['id'],
                ])
                ->count();

            $data[] = [
                $item['uid'],
                $item['email'],
                $item['education'],
                $item['end_date'],
                $item['last_active_time'],
                $item['complete'],
                $in,
                $out,
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '人才库人才分布情况' . time());
        bb($rs);
    }

    /**
     * http://zentao.jugaocai.com/index.php?m=story&f=view&id=506
     *
     *
     * ①学历：博士研究生、硕士研究生
     *
     * ②意向城市：
     * 浙江省及下含城市、江苏省及下含城市、上海市
     * ③活跃时间：2023年5-8月
     * ④简历隐藏状态：关闭
     *
     *     'UID',
     * '联系邮箱',
     * '最高学历',
     * '毕业时间',
     * '最高学历',
     * '最近活跃时间',
     * '简历完善度',
     */
    public function getResume20230726()
    {
        // 天津 黑龙江 吉林 辽宁  这几个省份和下面的全部城市

        $areaLevelNameList = [
            '天津',
            '黑龙江',
            '吉林',
            '辽宁',
        ];

        $areaList = BaseArea::find()
            ->select('id')
            ->where([
                'level' => 1,
                'name'  => $areaLevelNameList,
            ])
            ->asArray()
            ->column();

        // 找到这个省份下面的所有城市
        $areaList2 = BaseArea::find()
            ->select('id')
            ->where([
                'parent_id' => $areaList,
            ])
            ->asArray()
            ->column();

        // 合并数组
        $areaList = array_merge($areaList, $areaList2);

        $list = BaseResume::find()
            ->select('a.id,a.name,c.email,b.end_date,b.school,residence,education_id,last_active_time,complete')
            ->alias('a')
            ->innerJoin(['b' => BaseResumeEducation::tableName()], 'a.last_education_id=b.id')
            ->innerJoin(['c' => BaseMember::tableName()], 'a.member_id=c.id')
            ->innerJoin(['d' => BaseResumeIntentionAreaRelation::tableName()], 'a.id=d.resume_id')
            ->innerJoin(['e' => BaseResumeSetting::tableName()], 'a.id=e.resume_id')
            ->where([
                'between',
                'c.last_active_time',
                '2023-05-01 00:00:00',
                '2023-07-30 23:59:59',
            ])
            ->andWhere([
                'e.is_hide_resume' => [
                    0,
                    2,
                ],
                'd.area_id'        => $areaList,
            ])
            ->andWhere([
                'b.education_id' => [
                    BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                ],
            ])
            ->asArray()
            ->all();

        $header = [
            'UID',
            '联系邮箱',
            '最高学历',
            '毕业时间',
            '最近活跃时间',
            '简历完善度',
            '站内投递次数',
            '站外投递次数',
        ];

        $data = [];
        foreach ($list as &$item) {
            $item['education'] = BaseDictionary::getEducationName($item['education_id']);
            $item['uid']       = UUIDHelper::encrypt(1, $item['id']);

            $in = BaseJobApplyRecord::find()
                ->where([
                    'delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_INSIDE,
                    'resume_id'     => $item['id'],
                ])
                ->count();

            // 站外投递次数-邮箱
            $out = BaseJobApplyRecord::find()
                ->where([
                    'delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE,
                    'resume_id'     => $item['id'],
                ])
                ->count();

            $data[] = [
                $item['uid'],
                $item['email'],
                $item['education'],
                $item['end_date'],
                $item['last_active_time'],
                $item['complete'],
                $in,
                $out,
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '人才库人才分布情况' . time());
        bb($rs);
    }

    /**
     * http://zentao.jugaocai.com/index.php?m=story&f=view&id=490
     *
     *
     * ①学历：博士研究生
     *
     * ②浙江省及下含城市、江苏省及下含城市、上海市
     * ③活跃时间：2023年5-8月
     * ④简历隐藏状态：关闭
     *
     *     'UID',
     * '联系邮箱',
     * '最高学历',
     * '毕业时间',
     * '最高学历',
     * '最近活跃时间',
     * '简历完善度',
     * '站内投递次数'
     * '站外投递次数'
     */
    public function getResume20230801()
    {
        // 天津 黑龙江 吉林 辽宁  这几个省份和下面的全部城市

        $areaLevelNameList = [
            '浙江',
            '江苏',
            '上海',
        ];

        $areaList = BaseArea::find()
            ->select('id')
            ->where([
                'level' => 1,
                'name'  => $areaLevelNameList,
            ])
            ->asArray()
            ->column();

        // 找到这个省份下面的所有城市
        $areaList2 = BaseArea::find()
            ->select('id')
            ->where([
                'parent_id' => $areaList,
            ])
            ->asArray()
            ->column();

        // 合并数组
        $areaList = array_merge($areaList, $areaList2);

        $list = BaseResume::find()
            ->select('a.id,a.name,c.email,b.end_date,b.school,residence,education_id,last_active_time,complete')
            ->alias('a')
            ->innerJoin(['b' => BaseResumeEducation::tableName()], 'a.last_education_id=b.id')
            ->innerJoin(['c' => BaseMember::tableName()], 'a.member_id=c.id')
            ->innerJoin(['d' => BaseResumeIntentionAreaRelation::tableName()], 'a.id=d.resume_id')
            ->innerJoin(['e' => BaseResumeSetting::tableName()], 'a.id=e.resume_id')
            ->where([
                'between',
                'c.last_active_time',
                '2023-05-01 00:00:00',
                '2023-07-30 23:59:59',
            ])
            ->andWhere([
                'e.is_hide_resume' => [
                    0,
                    2,
                ],
                'd.area_id'        => $areaList,
            ])
            ->andWhere([
                'b.education_id' => [
                    BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE,
                ],
            ])
            ->asArray()
            ->all();

        $header = [
            'UID',
            '联系邮箱',
            '最高学历',
            '毕业时间',
            '最近活跃时间',
            '简历完善度',
            '站内投递次数',
            '站外投递次数',
        ];

        $data = [];
        foreach ($list as &$item) {
            $item['education'] = BaseDictionary::getEducationName($item['education_id']);
            $item['uid']       = UUIDHelper::encrypt(1, $item['id']);

            $in = BaseJobApplyRecord::find()
                ->where([
                    'delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_INSIDE,
                    'resume_id'     => $item['id'],
                ])
                ->count();

            // 站外投递次数-邮箱
            $out = BaseJobApplyRecord::find()
                ->where([
                    'delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE,
                    'resume_id'     => $item['id'],
                ])
                ->count();

            $data[] = [
                $item['uid'],
                $item['email'],
                $item['education'],
                $item['end_date'],
                $item['last_active_time'],
                $item['complete'],
                $in,
                $out,
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '人才库人才分布情况' . time());
        bb($rs);
    }

    /**
     * http://zentao.jugaocai.com/index.php?m=story&f=view&id=552
     *
     *
     * ①学历：博士研究生
     *
     * 广东省及下含城市、福建省及下含城市、福建省及下含城市、香港、澳门
     * ③活跃时间：2023年5-8月
     * ④简历隐藏状态：关闭
     *
     *     'UID',
     * '联系邮箱',
     * '最高学历',
     * '毕业时间',
     * '最高学历',
     * '最近活跃时间',
     * '简历完善度',
     * '站内投递次数'
     * '站外投递次数'
     */
    public function getResume20230825()
    {
        // 天津 黑龙江 吉林 辽宁  这几个省份和下面的全部城市

        $areaLevelNameList = [
            '广东',
            '福建',
            '香港',
            '澳门',
        ];

        $areaList = BaseArea::find()
            ->select('id')
            ->where([
                'level' => 1,
                'name'  => $areaLevelNameList,
            ])
            ->asArray()
            ->column();

        // 找到这个省份下面的所有城市
        $areaList2 = BaseArea::find()
            ->select('id')
            ->where([
                'parent_id' => $areaList,
            ])
            ->asArray()
            ->column();

        // 合并数组
        $areaList = array_merge($areaList, $areaList2);

        $list = BaseResume::find()
            ->select('a.id,a.name,c.email,b.end_date,b.school,residence,education_id,last_active_time,complete')
            ->alias('a')
            ->innerJoin(['b' => BaseResumeEducation::tableName()], 'a.last_education_id=b.id')
            ->innerJoin(['c' => BaseMember::tableName()], 'a.member_id=c.id')
            ->innerJoin(['d' => BaseResumeIntentionAreaRelation::tableName()], 'a.id=d.resume_id')
            ->innerJoin(['e' => BaseResumeSetting::tableName()], 'a.id=e.resume_id')
            ->where([
                'between',
                'c.last_active_time',
                '2023-05-01 00:00:00',
                '2023-08-30 23:59:59',
            ])
            ->andWhere([
                'e.is_hide_resume' => [
                    0,
                    2,
                ],
                'd.area_id'        => $areaList,
            ])
            ->andWhere([
                'b.education_id' => [
                    BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE,
                ],
            ])
            ->asArray()
            ->all();

        $header = [
            'UID',
            '联系邮箱',
            '最高学历',
            '毕业时间',
            '最近活跃时间',
            '简历完善度',
            '站内投递次数',
            '站外投递次数',
        ];

        $data = [];
        foreach ($list as &$item) {
            $item['education'] = BaseDictionary::getEducationName($item['education_id']);
            $item['uid']       = UUIDHelper::encrypt(1, $item['id']);

            $in = BaseJobApplyRecord::find()
                ->where([
                    'delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_INSIDE,
                    'resume_id'     => $item['id'],
                ])
                ->count();

            // 站外投递次数-邮箱
            $out = BaseJobApplyRecord::find()
                ->where([
                    'delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE,
                    'resume_id'     => $item['id'],
                ])
                ->count();

            $data[] = [
                $item['uid'],
                $item['email'],
                $item['education'],
                $item['end_date'],
                $item['last_active_time'],
                $item['complete'],
                $in,
                $out,
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '人才库人才分布情况' . time());
        bb($rs);
    }

    /**
     * http://zentao.jugaocai.com/index.php?m=story&f=view&id=565
     * 筛选条件：
     *
     * ①学历：博士研究生
     *
     * ②意向城市：
     * 四川省及下含城市、云南省及下含城市、贵州省及下含城市、重庆市
     * ③活跃时间：2023年6-9月
     * ④简历隐藏状态：关闭
     */
    public function getResume20230907()
    {
        $areaLevelNameList = [
            '四川',
            '云南',
            '贵州',
            '重庆',
        ];

        $areaList = BaseArea::find()
            ->select('id')
            ->where([
                'level' => 1,
                'name'  => $areaLevelNameList,
            ])
            ->asArray()
            ->column();

        // 找到这个省份下面的所有城市
        $areaList2 = BaseArea::find()
            ->select('id')
            ->where([
                'parent_id' => $areaList,
            ])
            ->asArray()
            ->column();

        // 合并数组
        $areaList = array_merge($areaList, $areaList2);

        $list = BaseResume::find()
            ->select('a.id,a.name,c.email,b.end_date,b.school,residence,education_id,last_active_time,complete')
            ->alias('a')
            ->innerJoin(['b' => BaseResumeEducation::tableName()], 'a.last_education_id=b.id')
            ->innerJoin(['c' => BaseMember::tableName()], 'a.member_id=c.id')
            ->innerJoin(['d' => BaseResumeIntentionAreaRelation::tableName()], 'a.id=d.resume_id')
            ->innerJoin(['e' => BaseResumeSetting::tableName()], 'a.id=e.resume_id')
            ->where([
                'between',
                'c.last_active_time',
                '2023-06-01 00:00:00',
                '2023-09-30 23:59:59',
            ])
            ->andWhere([
                'e.is_hide_resume' => [
                    0,
                    2,
                ],
                'd.area_id'        => $areaList,
            ])
            ->andWhere([
                'b.education_id' => [
                    BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE,
                ],
            ])
            ->asArray()
            ->all();

        $header = [
            'UID',
            '联系邮箱',
            '最高学历',
            '毕业时间',
            '最近活跃时间',
            '简历完善度',
            '站内投递次数',
            '站外投递次数',
        ];

        $data = [];
        foreach ($list as &$item) {
            $item['education'] = BaseDictionary::getEducationName($item['education_id']);
            $item['uid']       = UUIDHelper::encrypt(1, $item['id']);

            $in = BaseJobApplyRecord::find()
                ->where([
                    'delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_INSIDE,
                    'resume_id'     => $item['id'],
                ])
                ->count();

            // 站外投递次数-邮箱
            $out = BaseJobApplyRecord::find()
                ->where([
                    'delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE,
                    'resume_id'     => $item['id'],
                ])
                ->count();

            $data[] = [
                $item['uid'],
                $item['email'],
                $item['education'],
                $item['end_date'],
                $item['last_active_time'],
                $item['complete'],
                $in,
                $out,
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '人才库人才分布情况' . time());
        bb($rs);
    }

    /**
     * 背景 ：成都站招聘会人才邀请支持。
     *
     * 需求：导出平台人才邮箱数据：
     *
     * 最高学历要求-博士，
     *
     * 博士学历毕业院校为：四川大学、电子科技大学、西南交通大学、西南财经大学、四川农业大学、成都理工大学、西南石油大学、西南科技大学、四川师范大学、成都体育学院、西南民族大学、成都中医药大学 重庆大学、西南大学
     *
     * 简历完善度-无要求；
     *
     * 博士学历毕业时间-21年及之后均可。
     *
     * 表格字段： 人才ID、联系邮箱、最高学历、毕业时间、最近活跃时间、简历完善度
     */
    public function getResume20230913()
    {
        $schoolList = [
            '四川大学',
            '电子科技大学',
            '西南交通大学',
            '西南财经大学',
            '四川农业大学',
            '成都理工大学',
            '西南石油大学',
            '西南科技大学',
            '四川师范大学',
            '成都体育学院',
            '西南民族大学',
            '成都中医药大学',
            '重庆大学',
            '西南大学',
        ];

        // $schoolList = [
        //     '西北大学',
        //     '西安交通大学',
        //     '西北工业大学',
        //     '长安大学',
        //     '西北农林科技大学',
        //     '西安电子科技大学',
        //     '陕西师范大学',
        //     '西安理工大学',
        //     '西安科技大学',
        //     '西安建筑科技大学',
        //     '西安外国语大学',
        //     '陕西科技大学',
        // ];

        // 按照最高学历博士包含上面学校的
        $allResumeList = BaseResumeEducation::find()
            ->select('resume_id')
            ->where([
                'school'       => $schoolList,
                'education_id' => BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                'status'       => 1,
            ])
            ->andWhere([
                '>=',
                'end_date',
                '2022-01-01',
            ])
            ->groupBy('resume_id')
            ->asArray()
            ->all();

        // 表格字段： 人才ID、联系邮箱、最高学历、毕业时间、毕业院校、最近活跃时间、简历完善度

        $header = [
            'UID',
            '联系邮箱',
            '最高学历',
            '毕业时间',
            '毕业院校',
            '最近活跃时间',
            '简历完善度',
        ];

        $data = [];
        foreach ($allResumeList as $item) {
            // 找最高学历,毕业时间,毕业院校,最近活跃时间,简历完善度
            $memberId = BaseResume::findOneVal(['id' => $item['resume_id']], 'member_id');
            // 找邮箱和活跃时间
            $member = BaseMember::findOne($memberId);
            $resume = BaseResume::findOne($item['resume_id']);
            // 最高学历id
            $last_education_id = $resume->last_education_id;
            $resumeEducation   = BaseResumeEducation::findOne($last_education_id);
            $data[]            = [
                UUIDHelper::encrypt(1, $resume->id),
                $member->email,
                '博士',
                $resumeEducation->end_date,
                $resumeEducation->school,
                $member->last_active_time,
                $resume->complete,
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '人才库人才分布情况' . time());
        bb($rs);
    }

    /**
     * 背景：需优化单位中心非合作单位展示内容，将部分单位进行隐藏操作，现需对已创建单位进行盘查。
     *
     * 需求：导出平台非合作单位类型单位账号 。
     * 表格字段如下：单位ID、单位名称、单位类型、单位性质、单位下最近一条公告发布日期、是否有在线公告、是否有在线职位、单位是否隐藏状态
     */
    public function getCompany20230914()
    {
        $list = BaseCompany::find()
            ->select([
                'id',
                'full_name',
                'type',
                'nature',
                'is_hide',
            ])
            ->where([
                'is_cooperation' => BaseCompany::COOPERATIVE_UNIT_NO,
            ])
            ->asArray()
            ->all();

        $header = [
            '单位ID',
            '单位名称',
            '单位类型',
            '单位性质',
            '单位下最近一条公告发布日期',
            '是否有在线公告',
            '是否有在线职位',
            '单位是否隐藏状态',

        ];

        $data = [];
        foreach ($list as $item) {
            $id = UUIDHelper::encrypt(2, $item['id']);
            // 最后一条公告?
            $lastAnnouncement = BaseAnnouncement::find()
                ->alias('a')
                ->innerJoin(['b' => BaseArticle::tableName()], 'a.article_id=b.id')
                ->select('a.id,a.title,first_release_time,refresh_date')
                ->where([
                    'company_id' => $item['id'],
                ])
                ->orderBy(['refresh_date' => SORT_DESC])
                ->asArray()
                ->one();

            // 2023-09-14需要转成 2023年9月14日的格式
            $lastAnnouncementTime = $lastAnnouncement['refresh_date'] ? date('Y年m月d日',
                strtotime($lastAnnouncement['refresh_date'])) : '';

            $isExistAnnouncement = BaseAnnouncement::find()
                ->alias('a')
                ->innerJoin(['b' => BaseArticle::tableName()], 'a.article_id=b.id')
                ->where([
                    'company_id' => $item['id'],
                    'a.status'   => 1,
                    'b.status'   => 1,
                ])
                ->exists() ? '是' : '否';

            $isExistJob = BaseJob::find()
                ->where([
                    'company_id' => $item['id'],
                    'status'     => 1,
                ])
                ->exists() ? '是' : '否';

            $data[] = [
                $id,
                $item['full_name'],
                BaseDictionary::getCompanyTypeName($item['type']),
                BaseDictionary::getCompanyNatureName($item['nature']),
                $lastAnnouncementTime,
                $isExistAnnouncement,
                $isExistJob,
                $item['is_hide'] == 1 ? '是' : '否',
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '人才库人才分布情况' . time());
        bb($rs);
    }

    /**
     * 2022.05.01 ~ 2023.09.25 平台投递数据情况；
     *
     * 按日维度导出；具体需求数据详见附件表格
     * 当日有进行站内投递的人数，去重
     * 统计该日站外-邮件投递次数
     * 统计该日网址投递次数
     * 统计该日PC端站内投递次数
     * 统计该日PC端站外-邮件投递次数
     * 统计该日PC端网址投递次数
     * 统计该日H5端站内投递次数
     * 统计该日H5端站外-邮件投递次数
     * 统计该日H5端站外-网址投递次数
     * 统计该日小程序端站内投递次数
     * 当日已被查看的站内投递次数
     * 当日站内投递中，最新状态为“待处理”的投递数量
     * 当日站内投递中，最新状态为“通过初筛”的投递数量
     * 当日站内投递中，最新状态为“已邀面”的投递数量
     * 当日站内投递中，最新状态为“不合适”的投递数量
     * 当日站内投递中，最新状态为“已录用”的投递数量
     */
    public function getJobApply20231017()
    {
        // 写一个开始日期,结束日期,循环
        $beginData = '2022-05-01';
        $endData   = '2023-09-25';
        // $endData   = '2022-05-02';

        $dataLength = TimeHelper::computeDaySub($endData, $beginData);

        $header = [
            '时间',
            '站内投递',
            '站内投递人数',
            '站外-邮件',
            '站外网址',
            'PC端站内投递',
            'PC端站外-邮件',
            'PC端站外-网址',
            'H5端站内投递',
            'H5端站外-邮件',
            'H5端站外-网址',
            '小程序端站内投递',
            '“被查看”投递',
            '“待处理”投递',
            '“通过初筛”投递',
            '“已邀面”投递',
            '“不合适”投递',
            '“已录用”投递',
        ];

        $data = [];

        for ($i = 0; $i < $dataLength; $i++) {
            $date      = date('Y-m-d', strtotime($beginData) + $i * 3600 * 24);
            $beginTime = date('Y-m-d 00:00:00', strtotime($date));
            $endTime   = date('Y-m-d 23:59:59', strtotime($date));

            // 当日站内投递次数
            $todayInCount = BaseJobApplyRecord::find()
                ->where([
                    'between',
                    'add_time',
                    $beginTime,
                    $endTime,
                ])
                ->andWhere(['delivery_type' => 2])
                ->count();

            // 当日有进行站内投递的人数,去重
            $todayInMemberCount = BaseJobApplyRecord::find()
                ->where([
                    'between',
                    'add_time',
                    $beginTime,
                    $endTime,
                ])
                ->andWhere(['delivery_type' => 2])
                ->groupBy('resume_id')
                ->count();

            // 统计该日站外-邮件投递次数
            $todayOutEmailCount = BaseJobApplyRecord::find()
                ->where([
                    'between',
                    'add_time',
                    $beginTime,
                    $endTime,
                ])
                ->andWhere(['delivery_type' => 1])
                ->andWhere(['delivery_way' => 2])
                ->count();

            // 统计该日网址投递次数
            $todayUrlCount = BaseJobApplyRecord::find()
                ->where([
                    'between',
                    'add_time',
                    $beginTime,
                    $endTime,
                ])
                ->andWhere(['delivery_type' => 1])
                ->andWhere(['delivery_way' => 3])
                ->count();

            // 统计该日PC端站内投递次数
            $todayPcInCount = BaseJobApplyRecord::find()
                ->where([
                    'between',
                    'add_time',
                    $beginTime,
                    $endTime,
                ])
                ->andWhere(['delivery_type' => 2])
                ->andWhere(['platform' => 1])
                ->count();

            // 统计该日PC端站外-邮件投递次数
            $todayPcOutEmailCount = BaseJobApplyRecord::find()
                ->where([
                    'between',
                    'add_time',
                    $beginTime,
                    $endTime,
                ])
                ->andWhere(['delivery_type' => 1])
                ->andWhere(['platform' => 1])
                ->andWhere(['delivery_way' => 2])
                ->count();

            // 统计该日PC端网址投递次数
            $todayPcUrlCount = BaseJobApplyRecord::find()
                ->where([
                    'between',
                    'add_time',
                    $beginTime,
                    $endTime,
                ])
                ->andWhere(['platform' => 1])
                ->andWhere(['delivery_way' => 3])
                ->count();

            // 统计该日H5端站内投递次数
            $todayH5InCount = BaseJobApplyRecord::find()
                ->where([
                    'between',
                    'add_time',
                    $beginTime,
                    $endTime,
                ])
                ->andWhere(['delivery_type' => 2])
                ->andWhere(['platform' => 2])
                ->count();

            // 统计该日H5端站外-邮件投递次数
            $todayH5OutEmailCount = BaseJobApplyRecord::find()
                ->where([
                    'between',
                    'add_time',
                    $beginTime,
                    $endTime,
                ])
                ->andWhere(['delivery_type' => 1])
                ->andWhere(['platform' => 2])
                ->andWhere(['delivery_way' => 2])
                ->count();

            // 统计该日H5端站外-网址投递次数
            $todayH5UrlCount = BaseJobApplyRecord::find()
                ->where([
                    'between',
                    'add_time',
                    $beginTime,
                    $endTime,
                ])
                ->andWhere(['platform' => 2])
                ->andWhere(['delivery_way' => 3])
                ->count();

            // 统计该日小程序端站内投递次数
            $todayAppInCount = BaseJobApplyRecord::find()
                ->where([
                    'between',
                    'add_time',
                    $beginTime,
                    $endTime,
                ])
                ->andWhere(['delivery_type' => 2])
                ->andWhere(['platform' => 3])
                ->count();

            // 当日已被查看的站内投递次数
            $allInIdArray = BaseJobApplyRecord::find()
                ->where([
                    'between',
                    'add_time',
                    $beginTime,
                    $endTime,
                ])
                ->andWhere(['delivery_type' => 2])
                ->select('apply_id')
                ->asArray()
                ->column();

            $todayApplyCheckCount = BaseJobApply::find()
                ->where([
                    'id'       => $allInIdArray,
                    'is_check' => 1,
                ])
                ->count();

            // 当日站内投递中，最新状态为“待处理”的投递数量
            $todayApplyWaitHandelCount = BaseJobApply::find()
                ->where([
                    'id' => $allInIdArray,
                ])
                ->andWhere(['status' => 1])
                ->count();
            // 当日站内投递中，最新状态为“通过初筛”的投递数量
            $todayApplyPassHandelCount = BaseJobApply::find()
                ->where([
                    'id' => $allInIdArray,
                ])
                ->andWhere(['status' => 2])
                ->count();
            // 当日站内投递中，最新状态为“已邀面”的投递数量
            $todayApplyInvitedHandelCount = BaseJobApply::find()
                ->where([
                    'id' => $allInIdArray,
                ])
                ->andWhere(['status' => 3])
                ->count();
            // 当日站内投递中，最新状态为“不合适”的投递数量
            $todayApplyUnsuitHandelCount = BaseJobApply::find()
                ->where([
                    'id' => $allInIdArray,
                ])
                ->andWhere(['status' => 4])
                ->count();
            // 当日站内投递中，最新状态为“已录用”的投递数量
            $todayApplyRecruitHandelCount = BaseJobApply::find()
                ->where([
                    'id' => $allInIdArray,
                ])
                ->andWhere(['status' => 5])
                ->count();

            $data[] = [
                $date,
                $todayInCount,
                $todayInMemberCount,
                $todayOutEmailCount,
                $todayUrlCount,
                $todayPcInCount,
                $todayPcOutEmailCount,
                $todayPcUrlCount,
                $todayH5InCount,
                $todayH5OutEmailCount,
                $todayH5UrlCount,
                $todayAppInCount,
                $todayApplyCheckCount,
                $todayApplyWaitHandelCount,
                $todayApplyPassHandelCount,
                $todayApplyInvitedHandelCount,
                $todayApplyUnsuitHandelCount,
                $todayApplyRecruitHandelCount,
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '人才库人才分布情况' . time());
        bb($rs);
    }

    /**
     * 表1：筛选最高学历毕业院校为指定院校的博士研究生
     *
     * 姓名：
     *
     * 最高学历要求：博士研究生
     * 最高学历毕业院校：北京大学
     * 清华大学
     * 浙江大学
     * 复旦大学
     * 上海交通大学
     * 中国科学技术大学
     * 南京大学
     * 武汉大学
     * 同济大学
     * 哈尔滨工业大学
     * 南方科技大学
     * 北京师范大学
     * 中山大学（含以上院校字段均可，如清华大学医学院）
     * 简历完善度：
     *
     * 简历是否隐藏：
     *
     * 最高学历专业大类：
     * 是否具有海外教育经历：
     *
     * 是否具有海外工作经历：
     *
     * 最高学历毕业时间：
     * 邮箱地址：
     *
     *
     *
     * 表2：筛选有海外教育经历的博士研究生
     *
     * 姓名：
     *
     * 最高学历要求：
     * 最高学历毕业院校：
     * 简历完善度：
     *
     * 简历是否隐藏：
     *
     * 最高学历专业大类：
     * 是否具有海外教育经历：有
     *
     * 是否具有海外工作经历：
     *
     * 最高学历毕业时间：
     * 邮箱地址：
     *
     */

    public function getResume202310240()
    {
        $schoolList = [
            '北京大学',
            '清华大学',
            '浙江大学',
            '复旦大学',
            '上海交通大学',
            '中国科学技术大学',
            '南京大学',
            '武汉大学',
            '同济大学',
            '哈尔滨工业大学',
            '南方科技大学',
            '北京师范大学',
            '中山大学',
        ];

        $schoolWhere = ['or'];
        foreach ($schoolList as $item) {
            $schoolWhere[] = [
                'like',
                'school',
                $item,
            ];
        }

        $header = [
            '姓名',
            '最高学历要求',
            '最高学历毕业院校',
            '简历完善度',
            '简历是否隐藏',
            '最高学历专业大类',
            '是否具有海外教育经历',
            '是否具有海外工作经历',
            '最高学历毕业时间',
            '邮箱地址',
        ];

        $list = BaseResume::find()
            ->select('r.id,e.school,e.major_id,r.complete,s.is_hide_resume,r.name,end_date,email')
            ->alias('r')
            ->innerJoin(['e' => BaseResumeEducation::tableName()], 'e.id = r.last_education_id')
            ->innerJoin(['m' => BaseMember::tableName()], 'm.id = r.member_id')
            ->innerJoin(['s' => BaseResumeSetting::tableName()], 's.resume_id = r.id')
            ->where(['e.education_id' => 4])
            ->andWhere($schoolWhere)
            ->groupBy('r.id')
            ->asArray()
            ->all();

        foreach ($list as $resume) {
            $resume['education'] = BaseDictionary::getEducationName(4);
            $resume['major']     = BaseMajor::getTop($resume['major_id'])['name'];
            // 是否有海外教育经历
            $resume['is_overseas_education'] = BaseResumeEducation::find()
                ->where([
                    'resume_id' => $resume['id'],
                    'is_abroad' => 1,
                ])
                ->exists() ? '是' : '否';
            // 是否有海外工作经历
            $resume['is_overseas_work'] = BaseResumeWork::find()
                ->where([
                    'resume_id' => $resume['id'],
                    'is_abroad' => 1,
                ])
                ->exists() ? '是' : '否';

            $data[] = [
                $resume['name'],
                '博士研究生',
                $resume['school'],
                $resume['complete'],
                $resume['is_hide_resume'] == 1 ? '是' : '否',
                $resume['major'],
                $resume['is_overseas_education'],
                $resume['is_overseas_work'],
                $resume['end_date'],
                $resume['email'],
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '人才库人才分布情况' . time());
        bb($rs);
        //
        // $data = [];
    }

    public function getResume20231024()
    {
        $header = [
            '姓名',
            '最高学历要求',
            '最高学历毕业院校',
            '简历完善度',
            '简历是否隐藏',
            '最高学历专业大类',
            '是否具有海外教育经历',
            '是否具有海外工作经历',
            '最高学历毕业时间',
            '邮箱地址',
        ];

        $list = BaseResume::find()
            ->select('r.id,e.school,e.major_id,r.complete,s.is_hide_resume,r.name,end_date,email')
            ->alias('r')
            ->innerJoin(['e' => BaseResumeEducation::tableName()], 'e.id = r.last_education_id')
            ->innerJoin(['m' => BaseMember::tableName()], 'm.id = r.member_id')
            ->innerJoin(['s' => BaseResumeSetting::tableName()], 's.resume_id = r.id')
            ->where([
                'e.education_id' => 4,
                'e.is_abroad'    => 1,
            ])
            ->groupBy('r.id')
            ->asArray()
            ->all();

        foreach ($list as $resume) {
            $resume['education'] = BaseDictionary::getEducationName(4);
            $resume['major']     = BaseMajor::getTop($resume['major_id'])['name'];
            // 是否有海外教育经历
            $resume['is_overseas_education'] = BaseResumeEducation::find()
                ->where([
                    'resume_id' => $resume['id'],
                    'is_abroad' => 1,
                ])
                ->exists() ? '是' : '否';
            // 是否有海外工作经历
            $resume['is_overseas_work'] = BaseResumeWork::find()
                ->where([
                    'resume_id' => $resume['id'],
                    'is_abroad' => 1,
                ])
                ->exists() ? '是' : '否';

            $data[] = [
                $resume['name'],
                '博士研究生',
                $resume['school'],
                $resume['complete'],
                $resume['is_hide_resume'] == 1 ? '是' : '否',
                $resume['major'],
                $resume['is_overseas_education'],
                $resume['is_overseas_work'],
                $resume['end_date'],
                $resume['email'],
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '人才库人才分布情况' . time());
        bb($rs);
        //
        // $data = [];
    }

    // 平台注册人才，最高学历为博士的人才意向职位情况，不去重统计
    public function getResume20231115Two()
    {
        // 找到平台里面意向为博士后的 id=1 或者 9
        $resumeList = BaseResumeIntention::find()
            ->select('id,resume_id')
            ->where([
                'job_category_id' => [
                    1,
                    29,
                ],
                'status'          => 1,
            ])
            ->asArray()
            ->column();

        foreach ($resumeList as $item) {
        }
    }

    /**
     *http://zentao.jugaocai.com/index.php?m=story&f=view&id=633&version=0&param=0&storyType=story
     *
     * 应聘职位名称 应聘者姓名 应聘时间  应聘者最高学历 应聘者专业 应聘者籍贯 应聘者年龄 博士毕业院校 硕士毕业院校 本科毕业院校 博士后出站单位
     */

    public function getResume20231204()
    {
        $companyId = 13332;
        // 找到所有投递
        $list = BaseJobApply::find()
            ->alias('a')
            ->select('a.id,a.job_id,a.resume_id,a.status,a.add_time,b.name as jobName,c.name,c.last_education_id,d.education_id,d.major_id,age,native_place_area_id,e.name as majorName')
            ->innerJoin(['b' => BaseJob::tableName()], 'a.job_id=b.id')
            ->innerJoin(['c' => BaseResume::tableName()], 'a.resume_id=c.id')
            ->innerJoin(['d' => BaseResumeEducation::tableName()], 'c.last_education_id=d.id')
            ->innerJoin(['e' => BaseMajor::tableName()], 'e.id=d.major_id')
            ->where([
                'b.company_id' => $companyId,
            ])
            // 2023年4月至今
            ->andWhere([
                '>=',
                'a.add_time',
                '2023-04-01 00:00:00',
            ])
            ->asArray()
            ->all();

        $header = [
            '应聘职位名称',
            '应聘者姓名',
            '应聘时间',
            '应聘者最高学历',
            '应聘者专业',
            '应聘者籍贯',
            '应聘者年龄',
            '博士毕业院校',
            '硕士毕业院校',
            '本科毕业院校',
            '博士后出站单位',
        ];

        $data = [];

        foreach ($list as $item) {
            $doctor = BaseResumeEducation::find()
                ->where([
                    'resume_id'    => $item['resume_id'],
                    'education_id' => BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                    'status'       => 1,
                ])
                ->asArray()
                ->one();

            $master = BaseResumeEducation::find()
                ->where([
                    'resume_id'    => $item['resume_id'],
                    'education_id' => BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE,
                    'status'       => 1,
                ])
                ->asArray()
                ->one();

            $undergraduate = BaseResumeEducation::find()
                ->where([
                    'resume_id'    => $item['resume_id'],
                    'education_id' => BaseResumeEducation::EDUCATION_TYPE_UNDERGRADUATE_CODE,
                    'status'       => 1,
                ])
                ->asArray()
                ->one();

            $doctorAfter = BaseResumeWork::find()
                               ->where([
                                   'resume_id'  => $item['resume_id'],
                                   'is_postdoc' => 1,
                                   'status'     => 1,
                               ])
                               ->asArray()
                               ->one()['company'];

            $data[] = [
                $item['jobName'],
                $item['name'],
                $item['add_time'],
                BaseDictionary::getEducationName($item['education_id']),
                $item['majorName'],
                BaseArea::getAreaName($item['native_place_area_id']) ?: '',
                $item['age'],
                $doctor['school'] ?? '',
                $master['school'] ?? '',
                $undergraduate['school'] ?? '',
                $doctorAfter ?? '',
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '人才库人才分布情况' . time());

        bb($rs);
    }

    // 表单累计报名人数	表单报名带来的注册人数	表单新增注册博士数量	表单新增注册硕士数量	表单新增注册用户中有投递历史的人数	表单注册用户累计站内投递数量
    public function getResume20240108()
    {
        $list = BaseActivityFormRegistrationForm::find()
            ->select('id,resume_id')
            ->groupBy('resume_id')
            ->asArray()
            ->all();

        $total            = count($list);
        $sameDateTotal    = 0;
        $doctorTotal      = 0;
        $masterTotal      = 0;
        $hasDeliveryTotal = 0;
        $deliveryTotal    = 0;

        foreach ($list as $item) {
            // 找到这个人第一次提交表单的日期
            $firstTime = BaseActivityFormRegistrationForm::find()
                             ->where([
                                 'resume_id' => $item['resume_id'],
                             ])
                             ->orderBy(['add_time' => SORT_ASC])
                             ->one()['add_time'];

            // 截取到日期
            $firstTime = substr($firstTime, 0, 10);
            // 找到这个人注册日期
            $registerTime = BaseResume::find()
                                ->where([
                                    'id' => $item['resume_id'],
                                ])
                                ->one()['add_time'];

            // 截取到日期
            $registerTime = substr($registerTime, 0, 10);

            // 如果注册日期和第一次提交表单日期一样,那么就是同一天
            if ($firstTime == $registerTime) {
                $sameDateTotal++;

                // 找到这个人的最高学历
                $educationId = BaseResume::find()
                                   ->where([
                                       'id' => $item['resume_id'],
                                   ])
                                   ->one()['top_education_code'];

                // 如果最高学历是博士
                if ($educationId == BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE) {
                    $doctorTotal++;
                }

                // 如果最高学历是硕士
                if ($educationId == BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE) {
                    $masterTotal++;
                }

                // 找到这个人的投递记录
                $deliveryCount = BaseJobApplyRecord::find()
                    ->where([
                        'resume_id' => $item['resume_id'],
                    ])
                    ->count();

                // 如果有投递记录
                if ($deliveryCount > 0) {
                    $hasDeliveryTotal++;
                }

                // 找到这个人的投递记录
                $deliveryCount = BaseJobApply::find()
                    ->where([
                        'resume_id' => $item['resume_id'],
                    ])
                    ->count();

                // 如果有投递记录
                if ($deliveryCount > 0) {
                    $deliveryTotal++;
                }
            }
        }

        // 分别输出
        echo '表单累计报名人数:' . $total . PHP_EOL;
        echo '表单报名带来的注册人数:' . $sameDateTotal . PHP_EOL;
        echo '表单新增注册博士数量:' . $doctorTotal . PHP_EOL;
        echo '表单新增注册硕士数量:' . $masterTotal . PHP_EOL;
        echo '表单新增注册用户中有投递历史的人数:' . $hasDeliveryTotal . PHP_EOL;
        echo '表单注册用户累计站内投递数量:' . $deliveryTotal . PHP_EOL;
    }

    // 海外用户数	最高学历为博士的海外用户数	最高学历为硕士的海外用户数	有过投递记录的海外用户数	海外用户累计站内投递数量
    public function getResume20240109()
    {
        // 找到所有的海外用户数（手机号前缀不是86也不是空的
        $list = BaseMember::find()
            ->where([
                '<>',
                'mobile_code',
                '86',
            ])
            ->andWhere([
                '<>',
                'mobile_code',
                '',
            ])
            ->andWhere([
                'type' => 1,
            ])
            ->asArray()
            ->all();

        $overseasTotal = count($list);

        foreach ($list as $item) {
            $resumeId = BaseResume::findOneVal(['member_id' => $item['id']], 'id');

            // 找到这个人的最高学历
            $educationId = BaseResume::find()
                               ->where([
                                   'id' => $resumeId,
                               ])
                               ->one()['top_education_code'];

            // 如果最高学历是博士
            if ($educationId == BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE) {
                $doctorTotal++;
            }

            // 如果最高学历是硕士
            if ($educationId == BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE) {
                $masterTotal++;
            }

            // 找到这个人的投递记录
            $hasDelivery = BaseJobApplyRecord::find()
                ->where([
                    'resume_id' => $resumeId,
                ])
                ->count();

            if ($hasDelivery > 0) {
                $hasDeliveryTotal++;
            }

            // 累计站内投递
            $hadDelivery = BaseJobApply::find()
                ->where([
                    'resume_id' => $resumeId,
                ])
                ->count();

            if ($hadDelivery > 0) {
                $deliveryCount += $hadDelivery;
            }
        }

        echo '海外用户数:' . $overseasTotal . PHP_EOL;
        echo '最高学历为博士的海外用户数:' . $doctorTotal . PHP_EOL;
        echo '最高学历为硕士的海外用户数:' . $masterTotal . PHP_EOL;
        echo '有过投递记录的海外用户数:' . $hasDeliveryTotal . PHP_EOL;
        echo '海外用户累计站内投递数量:' . $deliveryCount . PHP_EOL;
    }

    // http://zentao.jugaocai.com/index.php?m=story&f=view&id=689
    public function getShowcase2020115()
    {
        $select = [
            's.id',
            's.add_time',
            's.status',
            's.is_show',
            's.update_time',
            's.company_id',
            's.company_name',
            's.title',
            's.sub_title',
            's.second_title',
            's.image_url',
            's.image_link',
            's.image_alt',
            's.online_time',
            's.offline_time',
            's.target_link',
            's.home_position_id',
            's.contact_home_position_id',
            's.contact_home_position_sort',
            's.sort',
            's.describe',
            's.type',
            'h.name',
            'h.platform_type',
            'h.number',
            'h.chinese_name',
            'a.name as creator',
            's.target_link_type',
            's.page_link_type',
            's.type',
        ];

        $query = Showcase::find()
            ->alias('s')
            ->leftJoin(['h' => HomePosition::tableName()], 'h.id = s.home_position_id')
            ->leftJoin(['a' => Admin::tableName()], 'a.id = s.creator_id')
            ->select($select);

        //$query->andFilterCompare('s.status', BaseShowcase::STATUS_DELETE, '<>');
        $query->andFilterCompare('s.status', BaseShowcase::STATUS_ONLINE);

        $showcaseList = $query->asArray()
            ->orderBy('s.add_time desc')
            ->all();

        $data = [];

        foreach ($showcaseList as $k => &$v) {
            $showcaseList[$k]['platform_type_title']  = BaseHomePosition::PLATFORM_TYPE_LIST[$v['platform_type']];
            $showcaseList[$k]['statusTxt']            = BaseShowcase::STATUS_LIST[$v['status']];
            $showcaseList[$k]['target_link_type_txt'] = BaseShowcase::TARGET_LINK_TYPE_LIST[$v['target_link_type']];
            $v['platform_type_title']                 = BaseHomePosition::PLATFORM_TYPE_LIST[$v['platform_type']];
            $v['statusTxt']                           = BaseShowcase::STATUS_LIST[$v['status']];
            $v['targetLinkTypeTxt']                   = BaseShowcase::TARGET_LINK_TYPE_LIST[$v['target_link_type']];
            if ($v['type']) {
                $type_name_arr = [];
                foreach (explode(',', $v['type']) as $item) {
                    $item_name       = BaseShowcase::TYPE_NAME[$item];
                    $type_name_arr[] = $item_name;
                }
            }
            //获取一下单位信息
            $companyInfo = BaseCompany::findOne(['id' => $v['company_id']]);
            // 这里去查询一下这个是否打包了
            $v['packingId'] = BaseShowcasePackingRelationship::find()
                                  ->select('showcase_packing_id')
                                  ->where(['showcase_id' => $v['id']])
                                  ->asArray()
                                  ->one()['showcase_packing_id'];

            // 打包ID	广告位类型	广告标题	广告副标题	广告跳转地址	所属平台	广告位置	显示状态	创建人	广告状态
            $data[] = [
                $v['packingId'],
                implode(',', $type_name_arr),
                $v['title'],
                $v['sub_title'],
                $v['target_link'],
                $v['platform_type_title'],
                $v['name'],
                $v['is_show'] == 1 ? '显示' : '隐藏',
                $v['creator'],
                $v['add_time'],
                $v['statusTxt'],
                $companyInfo ? $companyInfo->uuid : 0,
                $companyInfo ? $companyInfo->full_name : '',
            ];
        }

        $header = [
            '打包ID',
            '广告位类型',
            '广告标题',
            '广告副标题',
            '广告跳转地址',
            '所属平台',
            '广告位置',
            '显示状态',
            '创建人',
            '创建时间',
            '广告状态',
            '单位ID',
            '单位名称',
        ];
        $excel  = new Excel();
        $rs     = $excel->export($data, $header, '广告位分布情况' . time());

        bb($rs);
    }

    public function getCompany20240219()
    {
        $list = BaseCompany::find()
            ->select('id,full_name')
            ->where([
                'status'       => 1,
                'package_type' => 2,
            ])
            ->asArray()
            ->all();

        $header = [
            '单位名称',
            '单位主动发起的chat',
            '求职者主动发起并且单位已读',
        ];

        $data = [];
        foreach ($list as $item) {
            // 找到这个单位主动发起的chat
            $companyInitiative = BaseChatRoom::find()
                ->where([
                    'company_id'   => $item['id'],
                    'creator_type' => 2,
                ])
                ->count();

            // 找到求职者主动发起并且单位已读
            $companyRead = BaseChatRoom::find()
                ->alias('r')
                ->where([
                    'company_id'   => $item['id'],
                    'creator_type' => 1,
                    'c.status'     => 2,
                ])
                ->innerJoin(['c' => BaseChatMessage::tableName()], 'r.id = c.chat_room_id')
                ->groupBy('r.id')
                ->count();

            $data[] = [
                $item['full_name'],
                $companyInitiative,
                $companyRead,
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '单位聊天数据' . time());

        bb($rs);
    }

    // 逗号隔开，最终要转回去文本逗号隔开
    private function getDeliveryWayText($deliveryWay)
    {
        $wayList = BaseJob::DELIVERY_WAY_LIST;
        $wayArr  = explode(',', $deliveryWay);
        $wayText = '';
        foreach ($wayArr as $item) {
            $wayText .= $wayList[$item] . ',';
        }

        return rtrim($wayText, ',');
    }

    /**
     * 导出初始发布时间为 ：4.8-4.12的非合作单位投递地址情况数据。
     *
     * 1. 每日：非合作单位公告发布数量；公告投递地址为邮箱的数量；非合作单位职位发布数量；职位投递地址为邮箱的数量=职位维度有配邮箱数据+职位维度本身无配置但公告有配邮箱数据；
     *
     * 2. 周期内各公告维度的相关数据：公告id、初始发布日期、所属单位类型、报名方式、所含职位数量=在线+非在线+隐藏
     *
     * 3. 周期内各职位维度的相关数据：职位id、初始发布日期、所属单位类型、报名方式、
     *
     * `delivery_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '投递类型1=站外投递,2=站内投递',
     * `delivery_way` tinyint(1) NOT NULL DEFAULT '0' COMMENT '投递方式 1平台投递 2邮箱投递 3网址投递',
     */
    public function getAnnouncement20240417()
    {
        $beginTime = '2024-04-08 00:00:00';
        $endTime   = '2024-04-12 23:59:59';

        // 找到初次发布时间是这个时间段内，并且是非合作单位的公告
        $announcementList = BaseAnnouncement::find()
            ->alias('a')
            ->select('a.id,a.delivery_type,a.delivery_way,b.first_release_time,c.type')
            ->innerJoin(['b' => BaseArticle::tableName()], 'a.article_id=b.id')
            ->innerJoin(['c' => BaseCompany::tableName()], 'a.company_id = c.id')
            ->where([
                'between',
                'first_release_time',
                $beginTime,
                $endTime,
            ])
            ->andWhere([
                'c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_NO,
            ])
            ->orderBy('first_release_time asc')
            ->asArray()
            ->all();

        $total                  = 0;
        $day                    = '2024-04-08';
        $totalAnnouncementEmail = 0;
        $dayAnnouncementEmail   = 0;
        $dayList                = [];

        $data = [];
        foreach ($announcementList as $item) {
            // 找公告下面全部职位
            $jobList = BaseJob::find()
                ->where([
                    'announcement_id' => $item['id'],
                ])
                ->asArray()
                ->all();
            $day     = substr($item['first_release_time'], 0, 10);
            // 需要五个数据 公告id、公告初始发布日期、单位类型、报名方式、包含职位数量
            $announcementDeliveryWay = $this->getDeliveryWayText($item['delivery_way']);

            foreach ($jobList as $jobItem) {
                // 如果delivery_way是0就是跟随公告
                if ($jobItem['delivery_way'] == 0) {
                    $deliveryWay = $announcementDeliveryWay;
                } else {
                    // 如果delivery_way不是0就是自己配置的
                    $deliveryWay = $this->getDeliveryWayText($jobItem['delivery_way']);
                }
                $data[] = [
                    $jobItem['id'],
                    $day,
                    BaseDictionary::getCompanyTypeName($item['type']),
                    $deliveryWay,
                ];
            }

            // $jobTotal                  = count($jobList);
            // $isAnnouncementEmail       = false;
            // $jobAnnouncementEmailTotal = 0;
            // $dayList[$day]['announcementTotal']++;
            // $dayList[$day]['jobTotal'] += $jobTotal;
            //
            // // 公告delivery_way包含2就是邮箱delivery_way逗号隔开的
            // if (strpos($item['delivery_way'], '2') !== false) {
            //     $isAnnouncementEmail = true;
            //     $dayList[$day]['announcementEmailTotal']++;
            // }
            //
            // foreach ($jobList as $jobItem) {
            //     // 如果delivery_way是0就是跟随公告
            //     if ($jobItem['delivery_way'] == 0) {
            //         if ($isAnnouncementEmail) {
            //             $jobAnnouncementEmailTotal++;
            //             $dayList[$day]['jobAnnouncementEmailTotal']++;
            //         }
            //     } else {
            //         // 如果delivery_way不是0就是自己配置的，包含2就是邮箱delivery_way逗号隔开的
            //         if (strpos($jobItem['delivery_way'], '2') !== false) {
            //             $jobAnnouncementEmailTotal++;
            //             $dayList[$day]['jobAnnouncementEmailTotal']++;
            //         }
            //     }
            // }
        }

        $header = [
            '公告id',
            '公告初始发布日期',
            '单位类型',
            '报名方式',
            '包含职位数量',
        ];

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '公告投递地址情况数据' . time());
        bb($rs);

        bb($dayList);
    }

    /**
     *
     * 平台学科更新需求，导出含以下关键字的  在线  客户公告
     *
     * 关键词：共计9个
     *
     * 金融
     *
     * 能源动力
     *
     * 清洁能源技术
     *
     * 储能技术
     *
     * 动力工程
     *
     * 航空发动机工程
     *
     * 燃气轮机工程
     *
     * 航天动力工程
     *
     * 审计
     *
     * 表头：公告ID、公告发布日期、关键字、公告标题、公告链接、单位名称
     */
    public function getAnnouncement20240703()
    {
        $textList = [
            '金融',
            '能源动力',
            '清洁能源技术',
            '储能技术',
            '动力工程',
            '航空发动机工程',
            '燃气轮机工程',
            '航天动力工程',
            '审计',
        ];

        $orWhere = ['or'];

        foreach ($textList as $item) {
            $orWhere[] = [
                'like',
                'content',
                $item,
            ];
        }

        $list = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'a.article_id=b.id')
            ->select('a.id,a.title,c.full_name,b.refresh_date,a.uuid,content')
            ->innerJoin(['c' => BaseCompany::tableName()], 'a.company_id = c.id')
            ->where([
                'a.status'         => 1,
                'b.status'         => 1,
                'c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
            ])
            ->andWhere($orWhere)
            ->asArray()
            ->all();

        $excel = new Excel();
        $data  = [
            [
                '公告ID',
                '公告发布日期',
                '关键字',
                '公告标题',
                '公告链接',
                '单位名称',
            ],
        ];

        foreach ($list as $item) {
            $keyword = '';
            foreach ($textList as $text) {
                if (strpos($item['content'], $text) !== false) {
                    $keyword .= $text . ',';
                }
            }
            // 去掉最后的逗号
            $keyword = rtrim($keyword, ',');
            $data[]  = [
                $item['uuid'],
                $item['refresh_date'],
                $keyword,
                $item['title'],
                'https://www.gaoxiaojob.com/announcement/detail/' . $item['id'] . '.html',
                $item['full_name'],
            ];
        }

        $rs = $excel->export($data, '公告投递地址情况数据' . time());
        bb($rs);
    }

    /**
     * 5个表
     * 第一个
     * 1、导出需求：统计工作城市为“杭州”、近30日发布（发布时间取对外发布时间）的职位数据；按对外发布时间倒序排列；
     * 2、投递数据包括站内 & 站外投递数据。
     * 职位id    职位名称    学历要求    在线状态    对外发布时间    初始发布时间    单位id    单位名称    职位详情页近30日浏览量    职位详情页近15日浏览量    职位近30日累计投递量
     *   职位近15日投递量
     *
     * 第二个
     * 1、导出需求：统计工作城市为“杭州”、近30日职位详情页浏览量排行前30的职位数据；按浏览量倒序排列；
     * 2、投递数据包括站内 &站外投递数据。
     * 职位id    职位名称    学历要求    在线状态    对外发布时间    初始发布时间    单位id    单位名称    职位详情页近30日浏览量    职位近30日投递量
     *
     * 第三个
     * 1、导出需求：统计工作城市包含“杭州”、近30日发布（发布时间取对外发布时间）的公告数据；按对外发布时间倒序排列；
     * 2、投递数据包括站内 & 站外投递数据。
     * 公告id    公告名称    学历要求    在线状态    对外发布时间    初始发布时间    单位id    单位名称    公告详情页近30日浏览量    公告详情页近15日浏览量    公告近30日累计投递量
     *   公告近15日投递量
     *
     * 第四个
     * 1、导出需求：统计工作城市包含“杭州”、近30日公告详情页浏览量排行前30的公告数据；按浏览量倒序排列
     * 2、投递数据包括站内 &站外投递数据。
     * 公告id    公告名称    学历要求    在线状态    对外发布时间    初始发布时间    单位id    单位名称    公告详情页近30日浏览量    公告近30日投递量
     *
     * 第五个
     * 1、导出需求：统计工作城市为“杭州”、近30日单位主页浏览量排行前30的单位数据；按单位主页浏览量倒序排列
     * 2、投递数据包括站内 &站外投递数据。
     * 单位id    单位名称    单位主页近30日浏览量    单位近30日累计投递量
     */
    public function getCompany20240708()
    {
        // 30天前
        $beginDate = date('Y-m-d', strtotime('-30 day'));
        $beginTime = TimeHelper::dayToBeginTime($beginDate);

        $begin15Date = date('Y-m-d', strtotime('-15 day'));
        $begin15Time = TimeHelper::dayToBeginTime($begin15Date);

        // 找到所有地区包含杭州的公告

        $list = BaseCompany::find()
            ->where(['city_id' => 934])
            ->asArray()
            ->all();

        $header = [
            '单位id',
            '单位名称',
            '单位主页近30日浏览量',
            '单位近30日累计投递量',
        ];

        $data = [];

        foreach ($list as &$item) {
            $item['amount'] = BaseCompanyClickLog::find()
                ->where([
                    'company_id' => $item['id'],
                ])
                ->andWhere([
                    '>=',
                    'add_time',
                    $beginTime,
                ])
                ->count();
        }

        // 排序
        array_multisort(array_column($list, 'amount'), SORT_DESC, $list);

        foreach ($list as $item1) {
            $data[] = [
                $item1['uuid'],
                $item1['full_name'],
                $item1['amount'],
                BaseJobApplyRecord::find()
                    ->where([
                        'company_id' => $item1['id'],
                    ])
                    ->andWhere([
                        '>=',
                        'add_time',
                        $beginTime,
                    ])
                    ->count(),
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '单位数据' . time());
        bb($rs);

        //
        // // 找到发布时间是30天内的
        // $list = BaseArticle::find()
        //     ->alias('a')
        //     ->select('a.id as aId,b.id as bId,uuid,a.release_time,a.first_release_time,a.refresh_date,a.title,company_id,b.status')
        //     ->innerJoin(['b' => BaseAnnouncement::tableName()], 'a.id = b.article_id')
        //     ->where([
        //         'a.id' => array_column($list, 'announcement_id'),
        //     ])
        //     ->andWhere([
        //         '>=',
        //         'a.release_time',
        //         $beginTime,
        //     ])
        //     ->asArray()
        //     ->all();
        //
        // $header = [
        //     '公告id',
        //     '公告名称',
        //     '学历要求',
        //     '在线状态',
        //     '对外发布时间',
        //     '初始发布时间',
        //     '单位id',
        //     '单位名称',
        //     '公告详情页近30日浏览量',
        //     '公告详情页近15日浏览量',
        //     '公告近30日累计投递量',
        //     '公告近15日投递量',
        // ];
        //
        // $data = [];
        //
        //
        // foreach ($list as &$item1) {
        //     // 30天内浏览量
        //     $item1['amount'] = BaseArticleClickLog::find()
        //         ->where([
        //             'article_id' => $item1['aId'],
        //         ])
        //         ->andWhere([
        //             '>=',
        //             'add_time',
        //             $beginTime,
        //         ])
        //         ->count();
        // }
        //
        // array_multisort(array_column($list, 'amount'), SORT_DESC, $list);
        //
        // foreach ($list as $item) {
        //     $data[] = [
        //         $item['uuid'],
        //         $item['title'],
        //         BaseJob::getAnnouncementJobEducationType($item['bId']),
        //         BaseAnnouncement::STATUS_RECRUIT_LIST[$item['status']],
        //         $item['refresh_date'],
        //         substr($item['first_release_time'], 0, 10),
        //         BaseCompany::findOneVal(['id' => $item['company_id']], 'uuid'),
        //         BaseCompany::findOneVal(['id' => $item['company_id']], 'full_name'),
        //         $item['amount'],
        //         BaseArticleClickLog::find()
        //             ->where([
        //                 'article_id' => $item['aId'],
        //             ])
        //             ->andWhere([
        //                 '>=',
        //                 'add_time',
        //                 $begin15Time,
        //             ])
        //             ->count(),
        //         BaseJobApplyRecord::find()
        //             ->where([
        //                 'announcement_id' => $item['bId'],
        //             ])
        //             ->andWhere([
        //                 '>=',
        //                 'add_time',
        //                 $beginTime,
        //             ])
        //             ->count(),
        //
        //         BaseJobApplyRecord::find()
        //             ->where([
        //                 'announcement_id' => $item['bId'],
        //             ])
        //             ->andWhere([
        //                 '>=',
        //                 'add_time',
        //                 $begin15Time,
        //             ])
        //             ->count(),
        //     ];
        // }
        //
        // $excel = new Excel();
        // $rs    = $excel->export($data, $header, '公告数据' . time());
        // bb($rs);

        // 找到杭州的职位
        // $list1 = BaseJob::find()
        //     ->where([
        //         'city_id' => 934,
        //     ])
        //     ->andWhere([
        //         '>=',
        //         'refresh_time',
        //         $beginTime,
        //     ])
        //     ->orderBy('refresh_time desc')
        //     ->asArray()
        //     ->all();
        //
        // $header1 = [
        //     '职位id',
        //     '职位名称',
        //     '学历要求',
        //     '在线状态',
        //     '对外发布时间',
        //     '初始发布时间',
        //     '单位id',
        //     '单位名称',
        //     '职位详情页近30日浏览量',
        //     '职位详情页近15日浏览量',
        //     '职位近30日累计投递量',
        //     '职位近15日投递量',
        // ];
        //
        // $data1 = [];
        //
        // foreach ($list1 as $item) {
        //     $data1[] = [
        //         $item['uuid'],
        //         $item['name'],
        //         BaseDictionary::getEducationName($item['education_type']),
        //         BaseJob::JOB_STATUS_NAME[$item['status']],
        //         $item['refresh_date'],
        //         substr($item['first_release_time'], 0, 10),
        //         BaseCompany::findOneVal(['id' => $item['company_id']], 'uuid'),
        //         BaseCompany::findOneVal(['id' => $item['company_id']], 'full_name'),
        //         BaseJobClickLog::find()
        //             ->where([
        //                 'job_id' => $item['id'],
        //             ])
        //             ->andWhere([
        //                 '>=',
        //                 'add_time',
        //                 $beginTime,
        //             ])
        //             ->count(),
        //         BaseJobClickLog::find()
        //             ->where([
        //                 'job_id' => $item['id'],
        //             ])
        //             ->andWhere([
        //                 '>=',
        //                 'add_time',
        //                 $begin15Time,
        //             ])
        //             ->count(),
        //         BaseJobApplyRecord::find()
        //             ->where([
        //                 'job_id' => $item['id'],
        //             ])
        //             ->andWhere([
        //                 '>=',
        //                 'add_time',
        //                 $beginTime,
        //             ])
        //             ->count(),
        //         BaseJobApplyRecord::find()
        //             ->where([
        //                 'job_id' => $item['id'],
        //             ])
        //             ->andWhere([
        //                 '>=',
        //                 'add_time',
        //                 $begin15Time,
        //             ])
        //             ->count(),
        //     ];
        // }
        //
        // foreach ($list1 as &$item) {
        //     // 30天内浏览量
        //     $item['amount'] = BaseJobClickLog::find()
        //         ->where([
        //             'job_id' => $item['id'],
        //         ])
        //         ->andWhere([
        //             '>=',
        //             'add_time',
        //             $beginTime,
        //         ])
        //         ->count();
        // }
        //
        // // 按照浏览量排序
        // array_multisort(array_column($list1, 'amount'), SORT_DESC, $list1);
        //
        // $data1 = [];
        //
        // foreach ($list1 as $item1) {
        //     $data1[]  = [
        //         $item1['uuid'],
        //         $item1['name'],
        //         BaseDictionary::getEducationName($item1['education_type']),
        //         BaseJob::JOB_STATUS_NAME[$item1['status']],
        //         $item1['refresh_date'],
        //         substr($item1['first_release_time'], 0, 10),
        //         BaseCompany::findOneVal(['id' => $item1['company_id']], 'uuid'),
        //         BaseCompany::findOneVal(['id' => $item1['company_id']], 'full_name'),
        //         BaseJobClickLog::find()
        //             ->where([
        //                 'job_id' => $item1['id'],
        //             ])
        //             ->andWhere([
        //                 '>=',
        //                 'add_time',
        //                 $beginTime,
        //             ])
        //             ->count(),
        //         BaseJobApplyRecord::find()
        //             ->where([
        //                 'job_id' => $item1['id'],
        //             ])
        //             ->andWhere([
        //                 '>=',
        //                 'add_time',
        //                 $beginTime,
        //             ])
        //             ->count(),
        //     ];
        // }
        //
        // $excel = new Excel();
        // $rs    = $excel->export($data1, $header1, '职位数据' . time());
        // bb($rs);
    }

    // 需求：在线的客户信息，职位学科含【电子信息】的数据
    public function getAnnouncement20240712()
    {
        // $majorId = 87
        $jobIds = BaseJobMajorRelation::find()
            ->where([
                'major_id' => 87,
            ])
            ->select('job_id')
            ->asArray()
            ->column();

        $header = [
            '公告发布时间',
            '公告标题',
            '公告链接',
            '职位ID',
            '职位链接',
            '单位名称',
        ];

        $data = [];
        foreach ($jobIds as $jobId) {
            $jobInfo = BaseJob::find()
                ->select([
                    'a.uuid as jobUid',
                    'a.id as jobId',
                    'a.name as jobName',
                    'c.id as announcementId',
                    'c.uuid as announcementUid',
                    'b.full_name',
                    'c.title',
                    'd.refresh_date',
                ])
                ->alias('a')
                ->innerJoin(['b' => BaseCompany::tableName()], 'a.company_id=b.id')
                ->leftJoin(['c' => BaseAnnouncement::tableName()], 'a.announcement_id=c.id')
                ->leftJoin(['d' => BaseArticle::tableName()], 'c.article_id=d.id')
                ->where([
                    'a.id'           => $jobId,
                    'a.status'       => 1,
                    'is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
                ])
                ->asArray()
                ->one();

            if ($jobInfo['jobUid']) {
                $data[] = [
                    $jobInfo['refresh_date'],
                    $jobInfo['title'],
                    'https://www.gaoxiaojob.com/announcement/detail/' . $jobInfo['announcementId'] . '.html',
                    $jobInfo['jobUid'],
                    'https://www.gaoxiaojob.com/job/detail/' . $jobInfo['jobId'] . '.html',
                    $jobInfo['full_name'],
                ];
            }
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '职位数据' . time());
        bb($rs);
    }

    // https://zentao.jugaocai.com/index.php?m=story&f=storyView&storyID=866
    // 【江西泰豪动漫职业学院】所收简历信息导出需求
    public function getJobApplyList()
    {
        $memberId  = 134;
        $companyId = 133;
        $list      = BaseJobApply::find()
            ->select([
                'status',
                'resume_id',
                'add_time',
                'job_id',
            ])
            ->where([
                'company_id' => $companyId,
            ])
            ->asArray()
            ->all();

        // 简历状态	职位名称	公告名称	人才姓名	户籍/国籍	性别	出生年月	最高学历毕业院校	最高学历	最高教育经历一级专业	最高教育经历二级专业	最高教育经历三级专业	研究方向	投递时间	求职状态	工作经历1（职位）	工作经历1（时间）	工作经历2（职位）	工作经历2（时间）	联系方式 （电话）	联系方式 （邮箱）
        $header = [
            '简历状态',
            '职位名称',
            '公告名称',
            '人才姓名',
            '户籍/国籍',
            '性别',
            '出生年月',
            '最高学历毕业院校',
            '最高学历',
            '最高教育经历一级专业',
            '最高教育经历二级专业',
            '最高教育经历三级专业',
            '研究方向',
            '投递时间',
            '求职状态',
            '工作经历1（职位）',
            '工作经历1（时间）',
            '工作经历2（职位）',
            '工作经历2（时间）',
            '联系方式 （电话）',
            '联系方式 （邮箱）',
        ];

        $data = [];
        foreach ($list as $item) {
            $statusTxt           = $item['status'] == 1 ? '待处理' : '';
            $jobName             = BaseJob::findOneVal(['id' => $item['job_id']], 'name');
            $announcementName    = BaseAnnouncement::findOneVal(['id' => $item['job_id']], 'title');
            $resumeModel         = BaseResume::findOne($item['resume_id']);
            $resumeName          = $resumeModel->name;
            $householdRegisterId = $resumeModel->household_register_id;
            $resumeAge           = $resumeModel->age;
            $resumeGender        = BaseResume::GENDER_LIST[$resumeModel->gender];
            $resumeBirth         = $resumeModel->birthday;
            $topEducation        = BaseResumeEducation::findOne($resumeModel->last_education_id);
            $allWork             = self::getAllWork($resumeModel->id);
            $householdRegister   = BaseArea::findOneVal(['id' => $householdRegisterId], 'name');
            $workStatus          = BaseDictionary::getJobStatusName($resumeModel->work_status);
            $memberModel         = BaseMember::findOne($resumeModel->member_id);
            $researchDirection   = BaseResumeResearchDirection::find()
                ->select([
                    'content',
                ])
                ->where([
                    'resume_id' => $resumeModel->id,
                    'status'    => BaseResumeResearchDirection::STATUS_ACTIVE,
                ])
                ->asArray()
                ->one();

            $data[] = [
                $statusTxt,
                $jobName,
                $announcementName,
                $resumeName,
                $householdRegister,
                $resumeGender,
                $resumeBirth,
                $topEducation->school,
                BaseDictionary::getEducationName($topEducation->education_id),
                BaseMajor::findOneVal(['id' => $topEducation->major_id_level_1], 'name'),
                BaseMajor::findOneVal(['id' => $topEducation->major_id_level_2], 'name'),
                BaseMajor::findOneVal(['id' => $topEducation->major_id_level_3], 'name'),
                $researchDirection['content'],
                substr($item['add_time'], 0, 10),
                $workStatus,
                $allWork[0]['job_name'] ?? '',
                $allWork[0]['begin_date'] . ' - ' . $allWork[0]['end_date'],
                $allWork[1]['job_name'] ?? '',
                $allWork[1]['begin_date'] . ' - ' . $allWork[1]['end_date'],
                $memberModel->mobile,
                $memberModel->email,
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '简历数据' . time());
        bb($rs);
    }

    /**
     * 2星及以上单位投递数据，表头：人才对单位投递次数排行前五（24年年度）的数据，单位名称，单位ID，人才姓名，人才ID，人才对该单位的投递次数，分别投递的职位及时间
     *
     * https://zentao.jugaocai.com/index.php?m=story&f=view&id=925
     */
    public function getApply20240909()
    {
        $list = BaseCompanyGroupRelation::find()
            ->select([
                'company_id',
            ])
            ->where([
                'group_id' => [
                    2,
                    3,
                    4,
                    5,
                    6,
                ],
            ])
            ->orderBy('company_id')
            // ->limit(10)
            ->asArray()
            ->column();

        $data = [];
        foreach ($list as $item) {
            // 找到2024年投递前五的人员
            $companyMax5 = BaseJobApplyRecord::find()
                // sum as total,resume_id
                ->select('count(*) as total,resume_id')
                ->where([
                    'company_id' => $item,
                ])
                ->andWhere([
                    '>=',
                    'add_time',
                    '2024-01-01 00:00:00',
                ])
                ->groupBy('resume_id')
                ->limit(5)
                ->orderBy('total desc')
                ->asArray()
                ->all();

            $companyInfo = BaseCompany::findOne($item);
            // 单位ID	单位名称	求职者ID	求职者姓名	求职者最高学历	对该单位投递次数（2024年度累计投递次数）	投递的职位ID（多个ID","隔开；以下同）	投递职位名称(同ID顺序一一对应)	投递时间(同ID顺序一一对应)	职位学历要求(顺序一一对应)
            // 找到这个人对于这个单位的投递信息
            foreach ($companyMax5 as $applyItem) {
                $resumeId   = $applyItem['resume_id'];
                $resumeInfo = BaseResume::findOne($resumeId);
                $list       = BaseJobApplyRecord::find()
                    ->select([
                        'b.uuid',
                        'job_apply_record.add_time',
                        'b.name',
                        'b.education_type',
                    ])
                    ->innerJoin(['b' => BaseJob::tableName()], 'b.id = job_id')
                    ->where([
                        'job_apply_record.company_id' => $item,
                        'resume_id'                   => $resumeId,
                    ])
                    ->andWhere([
                        '>=',
                        'job_apply_record.add_time',
                        '2024-01-01 00:00:00',
                    ])
                    ->orderBy('job_apply_record.id')
                    ->asArray()
                    ->all();

                $jobIdArr   = [];
                $jobNameArr = [];
                $jobTimeArr = [];
                $jobEduArr  = [];

                foreach ($list as $jobItem) {
                    $jobIdArr[]   = $jobItem['uuid'];
                    $jobNameArr[] = $jobItem['name'];
                    // 转成y/m/d的格式
                    $time         = strtotime($jobItem['add_time']);
                    $jobTimeArr[] = date('Y/m/d', $time);
                    $jobEduArr[]  = BaseDictionary::getEducationName($jobItem['education_type']);
                }

                $data[] = [
                    $companyInfo->uuid,
                    $companyInfo->full_name,
                    $resumeInfo->uuid,
                    $resumeInfo->name,
                    BaseDictionary::getEducationName($resumeInfo->top_education_code),
                    $applyItem['total'],
                    implode(',', $jobIdArr),
                    implode(',', $jobNameArr),
                    implode(',', $jobTimeArr),
                    implode(',', $jobEduArr),
                ];
            }
        }

        $excel  = new Excel();
        $header = [
            '单位ID',
            '单位名称',
            '求职者ID',
            '求职者姓名',
            '求职者最高学历',
            '对该单位投递次数（2024年度累计投递次数）',
            '投递的职位ID（多个ID","隔开；以下同）',
            '投递职位名称(同ID顺序一一对应)',
            '投递时间(同ID顺序一一对应)',
            '职位学历要求(顺序一一对应)',
        ];

        $rs = $excel->export($data, $header, '单位投递数据' . time());
        bb($rs);
    }

    /**
     * https://zentao.jugaocai.com/index.php?m=story&f=view&id=932
     * 1、识别维度：在线简历“姓名”字段包含英文或特殊字符 或 中文字数超过4个
     *
     * ID  姓名 年龄 注册时间 户籍/国籍  人才自定义头像链接（如未上传则不展示） 平台站内投递量（累计）  最高学历所学专业 博士毕业院校 硕士毕业院校 本科毕业院校 投递（站内）职位名称  该职位最低学历要求
     * 该职位学科要求 该次投递时间  职位所属单位名称
     */
    public function getResume20240913()
    {
        $sql  = "SELECT id,name FROM resume WHERE name REGEXP '[A-Za-z]' OR CHAR_LENGTH(name) > 4";
        $list = \Yii::$app->db->createCommand($sql)
            ->queryAll();
        $data = [];
        foreach ($list as $item) {
            $resumeId   = $item['id'];
            $resume     = BaseResume::findOne($resumeId);
            $member     = BaseMember::findOne($resume->member_id);
            $resumeInfo = $this->getAllEducation($resumeId);
            $applyList  = $this->getOnlineApply([
                'resume_id' => $resumeId,
            ]);
            // 户籍/国籍
            $householdRegister = BaseArea::findOneVal(['id' => $resume->household_register_id], 'name');
            $avatar            = $member->avatar ? 'http://img.gaoxiaojob.com/' . $member->avatar : '';
            if ($applyList[0]) {
                foreach ($applyList as $apply) {
                    $data[] = [
                        $resume['uuid'],
                        $resume['name'],
                        $resume->age,
                        $householdRegister,
                        $avatar,
                        count($applyList),
                        $resumeInfo['topEducation']['majorLevel1Name'],
                        $resumeInfo['doctor']['school'] ?? '',
                        $resumeInfo['master']['school'] ?? '',
                        $resumeInfo['bachelor']['school'] ?? '',
                        $apply['name'],
                        BaseDictionary::getEducationName($apply['education_type']),
                        $apply['major'],
                        $apply['add_time'],
                        $apply['full_name'],
                    ];
                }
            } else {
                $data[] = [
                    $resume['uuid'],
                    $resume['name'],
                    $resume->age,
                    $householdRegister,
                    $avatar,
                    count($applyList),
                    $resumeInfo['topEducation']['majorLevel1Name'],
                    $resumeInfo['doctor']['school'] ?? '',
                    $resumeInfo['master']['school'] ?? '',
                    $resumeInfo['bachelor']['school'] ?? '',
                    '',
                    '',
                    '',
                    '',
                    '',
                ];
            }
        }

        $excel  = new Excel();
        $header = [
            'ID',
            '姓名',
            '年龄',
            '户籍/国籍',
            '人才自定义头像链接（如未上传则不展示）',
            '平台站内投递量（累计）',
            '最高学历所学专业',
            '博士毕业院校',
            '硕士毕业院校',
            '本科毕业院校',
            '投递（站内）职位名称',
            '该职位最低学历要求',
            '该职位学科要求',
            '该次投递时间',
            '职位所属单位名称',
        ];
        $rs     = $excel->export($data, $header, '在线简历数据' . time());
        bb($rs);
    }

    // 导出需求：导出最高学历为博士的人才相关数据
    // ID	姓名	出生日期	现居住地	户籍/国籍	简历是否开放	简历完善度	博士毕业院校	博士教育经历起始时间	是否海外经历	博士专业（一级）	博士专业（二级）	博士专业（三级）	研究方向	硕士毕业院校	硕士教育经历起始时间	硕士专业（一级-二级-三级）	本科毕业院校	本科教育经历起始时间	本科专业（一级-二级-三级）	时间段1	单位名称	职位名称	是否海外
    public function getResume20241022()
    {
        $list = BaseResume::find()
            ->where([
                'top_education_code' => 4,
            ])
            ->asArray()
            ->all();

        $data   = [];
        $header = [
            'ID',
            '姓名',
            '出生日期',
            '现居住地',
            '户籍/国籍',
            '简历是否开放',
            '简历完善度',
            '博士毕业院校',
            '博士教育经历起始时间',
            '是否海外经历',
            '博士专业（一级）',
            '博士专业（二级）',
            '博士专业（三级）',
            '研究方向',
            '硕士毕业院校',
            '硕士教育经历起始时间',
            '硕士专业（一级-二级-三级）',
            '本科毕业院校',
            '本科教育经历起始时间',
            '本科专业（一级-二级-三级）',
            '时间段1',
            '单位名称',
            '职位名称',
            '是否海外',
            '时间段2',
            '单位名称',
            '职位名称',
            '是否海外',
            '时间段3',
            '单位名称',
            '职位名称',
            '是否海外',
            '时间段4',
            '单位名称',
            '职位名称',
            '是否海外',
            '时间段5',
            '单位名称',
            '职位名称',
            '是否海外',
            '时间段6',
            '单位名称',
            '职位名称',
            '是否海外',
        ];
        foreach ($list as $item) {
            $resumeId          = $item['id'];
            $householdRegister = BaseArea::findOneVal(['id' => $item['household_register_id']], 'name');
            $residence         = BaseArea::getAreaName($item['residence']);
            $resumeSetting     = BaseResumeSetting::find()
                ->where([
                    'resume_id' => $resumeId,
                ])
                ->asArray()
                ->one();
            $isOpen            = $resumeSetting['is_hide_resume'] == 1 ? '否' : '是';
            // 完善度
            $complete   = $item['complete'] . '%';
            $isOverseas = $item['is_abroad'] == 1 ? '是' : '否';
            $resumeInfo = $this->getAllEducation($resumeId);
            // 博士毕业院校
            $doctorSchool = $resumeInfo['doctor']['school'];
            // 博士教育经历起始时间 begin_date  - end_date
            $doctorBeginDate = $resumeInfo['doctor']['begin_date'] . ' - ' . $resumeInfo['doctor']['end_date'];
            // 博士专业（一级）
            $doctorMajorLevel1Name = $resumeInfo['doctor']['majorLevel1Name'];
            // 博士专业（二级）
            $doctorMajorLevel2Name = $resumeInfo['doctor']['majorLevel2Name'];
            // 博士专业（三级）
            $doctorMajorLevel3Name = $resumeInfo['doctor']['majorLevel3Name'];
            // 研究方向
            $researchDirection = BaseResumeResearchDirection::find()
                ->select([
                    'content',
                ])
                ->where([
                    'resume_id' => $resumeId,
                    'status'    => BaseResumeResearchDirection::STATUS_ACTIVE,
                ])
                ->asArray()
                ->one();
            // 硕士毕业院校
            $masterSchool = $resumeInfo['master']['school'];
            if ($masterSchool) {
                // 硕士教育经历起始时间
                $masterBeginDate = $resumeInfo['master']['begin_date'] . ' - ' . $resumeInfo['master']['end_date'];
                // 硕士专业（一级-二级-三级）
                $masterMajorName = $resumeInfo['master']['majorLevel1Name'] . '-' . $resumeInfo['master']['majorLevel2Name'] . '-' . $resumeInfo['master']['majorLevel3Name'];
            } else {
                $masterBeginDate = '';
                $masterMajorName = '';
            }
            // 本科毕业院校
            $bachelorSchool = $resumeInfo['bachelor']['school'];
            if ($bachelorSchool) {
                // 本科教育经历起始时间
                $bachelorBeginDate = $resumeInfo['bachelor']['begin_date'] . ' - ' . $resumeInfo['bachelor']['end_date'];
                // 本科专业（一级-二级-三级）
                $bachelorMajorName = $resumeInfo['bachelor']['majorLevel1Name'] . '-' . $resumeInfo['bachelor']['majorLevel2Name'] . '-' . $resumeInfo['bachelor']['majorLevel3Name'];
                // 本科可以自定义
                if ($resumeInfo['bachelor']['major_custom']) {
                    $bachelorMajorName = $resumeInfo['bachelor']['major_custom'];
                }
            } else {
                $bachelorBeginDate = '';
                $bachelorMajorName = '';
            }
            $thisItem = [
                $item['uuid'],
                $item['name'],
                $item['birthday'],
                $residence,
                $householdRegister,
                $isOpen,
                $complete,
                $doctorSchool,
                $doctorBeginDate,
                $isOverseas,
                $doctorMajorLevel1Name,
                $doctorMajorLevel2Name,
                $doctorMajorLevel3Name,
                $researchDirection['content'],
                $masterSchool,
                $masterBeginDate,
                $masterMajorName,
                $bachelorSchool,
                $bachelorBeginDate,
                $bachelorMajorName,
            ];

            // 递归找工作经历
            $workList = $this->getAllWork($resumeId);
            foreach ($workList as $work) {
                $thisItem[] = $work['begin_date'] . ' - ' . $work['end_date'];
                $thisItem[] = $work['company'];
                $thisItem[] = $work['job_name'];
                $thisItem[] = $work['is_abroad'] == 1 ? '是' : '否';
            }

            $data[] = $thisItem;
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '博士简历数据' . time());
        bb($rs);
    }

    # https://zentao.jugaocai.com/index.php?m=story&f=view&id=1063
    # 应销售需求，需要帮华南理工大学广州国际校区（2024.12.1-2025.3.11）、广东轻工职业技术大学（2024.8.1-2025.1.31）两个单位的投递人才数据导出，导出表格字段如表所示，两个单位需要的不一样，所以分开了两个sheet；
    # sheet2
    // 个人信息							教育经历								工作经历（有几段就展示几段；按各段工作经历开始时间倒序依次展示）
    // 序号	姓名	性别	年龄	手机号	邮箱	最高学历	博士就读学校1	博士专业1	博士就读学校2	博士专业2	硕士就读学校1	研究生专业1	硕士就读学校2	研究生专业2	时间段1	单位名称	职位名称	时间段2	单位名称	职位名称	时间段N	单位名称	职位名称
    // 1	高小才	女	22	13300000001	<EMAIL>	博士研究生	清华大学	体育学			清华大学	体育学
    // "展示博士阶段就读学校；
    // 有几段展示几段，为空则不展示"	"展示该博士阶段所学二级专业；
    // 为空则不展示"			"展示硕士阶段就读学校；
    // 有几段展示几段，为空则不展示"	展示该硕士阶段所学专业			"展示该段工作经历在职时间：入职时间-离职时间
    // 例：2010/01 - 2022/03"	展示该段工作经历“单位名称”	展示该段工作经历“职位名称”
    public function getResume202503131()
    {
        // 华南理工大学广州国际校区
        $sheetCompany2Id = '619';

        // 先拿到所有的投递人才id
        $resumeIds = $this->getAllResumeByCompanyId($sheetCompany2Id);

        $data = [];

        $header = [
            '序号',
            '姓名',
            '性别',
            '年龄',
            '手机号',
            '邮箱',
            '最高学历',
            '博士就读学校',
            '博士专业',
            '硕士就读学校',
            '研究生专业',
            '时间段1',
            '单位名称',
            '职位名称',
            '时间段2',
            '单位名称',
            '职位名称',
            '时间段3',
            '单位名称',
            '职位名称',
            '时间段4',
            '单位名称',
            '职位名称',
            '时间段5',
            '单位名称',
            '职位名称',
            '时间段6',
            '单位名称',
            '职位名称',
        ];

        foreach ($resumeIds as $k => $resumeId) {
            // 个人信息
            $resumeInfo = $this->getBaseInfoByResumeId($resumeId);

            // 如果不是博士，跳过
            if ($resumeInfo['education_id'] != BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE) {
                continue;
            }

            // 获取教育经历全部
            $educationList = $this->getAllEducation($resumeId);

            // 获取工作经历全部
            $workList = $this->getAllWork($resumeId);

            $dataOne = [
                $k + 1,
                $resumeInfo['name'],
                $resumeInfo['genderTxt'],
                $resumeInfo['age'],
                $resumeInfo['fullMobile'],
                $resumeInfo['email'],
                $resumeInfo['educationName'],
                $educationList['doctor']['school'],
                $educationList['doctor']['majorLevel3Name'],
                $educationList['master']['school'],
                $educationList['master']['majorLevel3Name'],
            ];

            foreach ($workList as $work) {
                $dataOne[] = $work['begin_date'] . ' - ' . $work['end_date'];
                $dataOne[] = $work['company'];
                $dataOne[] = $work['job_name'];
            }

            $data[] = $dataOne;
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '华南理工大学广州国际校区' . time());
        bb($rs);
    }

    // 广东轻工职业技术大学（2024.8.1-2025.1.31）两个单位的投递人才数据导出，导出表格字段如表所示，两个单位需要的不一样，所以分开了两个sheet；
    # sheet2
    // 序号	姓名	性别	年龄	手机号	最高学历	博士就读学校1	博士专业1	是否职称
    public function getResume202503132()
    {
        // 华南理工大学广州国际校区
        $sheetCompany2Id = '100211';

        // 先拿到所有的投递人才id
        $resumeIds = $this->getAllResumeByCompanyId($sheetCompany2Id);

        $data = [];

        $header = [
            '序号',
            '姓名',
            '性别',
            '年龄',
            '手机号',
            '邮箱',
            '最高学历',
            '博士就读学校',
            '博士专业',
            '硕士就读学校',
            '研究生专业',
            '是否职称',
        ];

        foreach ($resumeIds as $k => $resumeId) {
            // 个人信息
            $resumeInfo = $this->getBaseInfoByResumeId($resumeId);

            // 如果不是博士，跳过
            if ($resumeInfo['education_id'] != BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE) {
                continue;
            }

            // 获取教育经历全部
            $educationList = $this->getAllEducation($resumeId);

            $dataOne = [
                $k + 1,
                $resumeInfo['name'],
                $resumeInfo['genderTxt'],
                $resumeInfo['age'],
                $resumeInfo['fullMobile'],
                $resumeInfo['email'],
                $resumeInfo['educationName'],
                $educationList['doctor']['school'],
                $educationList['doctor']['majorLevel3Name'],
                $resumeInfo['titleId'] ? '是' : '否',
            ];

            $data[] = $dataOne;
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '广东轻工职业技术大学' . time());
        bb($rs);
    }


    # https://zentao.jugaocai.com/index.php?m=story&f=view&id=1063
    # 应销售需求，需要帮华南理工大学广州国际校区（2024.12.1-2025.3.11）、广东轻工职业技术大学（2024.8.1-2025.1.31）两个单位的投递人才数据导出，导出表格字段如表所示，两个单位需要的不一样，所以分开了两个sheet；
    # sheet2
    // 序号	应聘的二级学院	姓名	性别	年龄	手机号	邮箱	最高学历	博士毕业年份	研究方向	博士就读学校1	博士专业1	硕士就读学校1	研究生专业1	时间段1	单位名称	职位名称	时间段2	单位名称	职位名称	时间段N	单位名称	职位名称
    public function getResume20250313()
    {
        // 华南理工大学广州国际校区
        $sheetCompany2Id = '619';

        // 先拿到所有的投递人才id
        $list = BaseJobApply::find()
            ->select([
                'resume_id',
                'job_name',
            ])
            ->where(['company_id' => $sheetCompany2Id])
            ->andWhere([
                '>=',
                'add_time',
                '2024-12-01 00:00:00',
            ])
            ->asArray()
            ->all();

        $data = [];

        $header = [
            '序号',
            '应聘的二级学院',
            '姓名',
            '性别',
            '年龄',
            '手机号',
            '邮箱',
            '最高学历',
            '博士毕业年份',
            '研究方向',
            '博士就读学校',
            '博士专业',
            '硕士就读学校',
            '研究生专业',
            '时间段1',
            '单位名称',
            '职位名称',
            '时间段2',
            '单位名称',
            '职位名称',
            '时间段3',
            '单位名称',
            '职位名称',
            '时间段4',
            '单位名称',
            '职位名称',
            '时间段5',
            '单位名称',
            '职位名称',
            '时间段6',
            '单位名称',
            '职位名称',
        ];

        foreach ($list as $k => $item) {
            // 个人信息
            $resumeId   = $item['resume_id'];
            $resumeInfo = $this->getBaseInfoByResumeId($resumeId);

            // 如果不是博士，跳过
            if ($resumeInfo['education_id'] != BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE) {
                continue;
            }

            // 研究方向
            $researchDirection = BaseResumeResearchDirection::find()
                ->select([
                    'content',
                ])
                ->where([
                    'resume_id' => $resumeId,
                    'status'    => BaseResumeResearchDirection::STATUS_ACTIVE,
                ])
                ->asArray()
                ->one();

            // 获取教育经历全部
            $educationList = $this->getAllEducation($resumeId);

            // 获取工作经历全部
            $workList = $this->getAllWork($resumeId);

            $dataOne = [
                $k + 1,
                $item['job_name'],
                // 应聘的二级学院
                $resumeInfo['name'],
                $resumeInfo['genderTxt'],
                $resumeInfo['age'],
                $resumeInfo['fullMobile'],
                $resumeInfo['email'],
                $resumeInfo['educationName'],
                $educationList['doctor']['end_date'],
                // 博士毕业年份
                $researchDirection['content'] ?? '',
                // 研究方向
                $educationList['doctor']['school'],
                $educationList['doctor']['majorLevel3Name'],
                $educationList['master']['school'],
                $educationList['master']['majorLevel3Name'],
            ];

            foreach ($workList as $work) {
                $dataOne[] = $work['begin_date'] . ' - ' . $work['end_date'];
                $dataOne[] = $work['company'];
                $dataOne[] = $work['job_name'];
            }

            $data[] = $dataOne;
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '华南理工大学广州国际校区' . time());
        bb($rs);
    }

    /**
     * https://zentao.jugaocai.com/index.php?m=story&f=storyView&storyID=1153
     * 需对新增的外籍人才进行标签补充，需导出相关人才数据。
     *
     * 1、识别维度：在线简历“姓名”字段包含 英文 或 特殊字符 或 中文字数超过4个
     *
     * 表格字段如下：
     *
     * ID  /姓名 /年龄 /注册时间 /户籍/国籍 /人才自定义头像链接（如未上传则不展示）/ 平台站内投递量（累计）/  最高学历 /最高学历所学专业/  博士毕业院校 /硕士毕业院校 /本科毕业院校/  人才标签（没有则空）
     */
    public function getResume20250422()
    {
        $sql  = "SELECT id,name FROM resume WHERE name REGEXP '[A-Za-z]' OR CHAR_LENGTH(name) > 4";
        $list = \Yii::$app->db->createCommand($sql)
            ->queryAll();
        $data = [];
        foreach ($list as $item) {
            $resumeId   = $item['id'];
            $resume     = BaseResume::findOne($resumeId);
            $member     = BaseMember::findOne($resume->member_id);
            $resumeInfo = $this->getAllEducation($resumeId);
            // 户籍/国籍
            $householdRegister = BaseArea::findOneVal(['id' => $resume->household_register_id], 'name');
            $avatar            = $member->avatar ? 'http://img.gaoxiaojob.com/' . $member->avatar : '';
            $tagList           = BaseResumeTag::getTagList($resumeId);
            $applyCount        = BaseJobApply::find()
                ->where(['resume_id' => $resumeId])
                ->count();
            $data[]            = [
                $resume['uuid'],
                $resume['name'],
                $resume->age,
                $householdRegister,
                $avatar,
                $resumeInfo['topEducation']['majorLevel1Name'],
                $resumeInfo['doctor']['school'] ?? '',
                $resumeInfo['master']['school'] ?? '',
                $resumeInfo['bachelor']['school'] ?? '',
                $applyCount,
                implode(',', $tagList),
            ];
        }

        $excel  = new Excel();
        $header = [
            'ID',
            '姓名',
            '年龄',
            '户籍/国籍',
            '人才自定义头像链接（如未上传则不展示）',
            '平台站内投递量（累计）',
            '最高学历所学专业',
            '博士毕业院校',
            '硕士毕业院校',
            '本科毕业院校',
            '人才标签',
        ];
        $rs     = $excel->export($data, $header, '在线简历数据' . time());
        bb($rs);
    }

    /**
     * 为导流更多用户使用“高才博士后”站点。现需研发协助导出平台人才邮件地址，进行邮件推广触达。因涉及人才敏感信息，故进行工作请示。
     * 触达将分批次进行，需如下信息：
     * 需求1：导出截至目前，平台已注册用户中：近6个月，投递过任：博士后岗位、特别研究助理岗位的人才邮箱，含站内站外 。
     * 字段涉及：人才ID、邮箱、最高学历
     * 人才ID、最高学历字段导出是为了同时核查一下不符合学历要求的人才进行投递的情况；
     *
     * 需求2：导出截至目前，平台已注册用户中：求职意向含：博士后、特别研究助理的人才邮箱。
     * 字段：人才ID、邮箱、最高学历、注册时间、最近登录时间
     */
    public function getResume20250522()
    {
        $categoryId = [
            29,
            263,
        ];

        // 6个月前的时间
        // $sixMonthAgo = date('Y-m-d H:i:s', strtotime('-6 month'));
        //
        // $list = BaseJobApplyRecord::find()
        //     ->alias('a')
        //     ->select('c.uuid,d.email,c.top_education_code')
        //     ->innerJoin(['b' => BaseJob::tableName()], 'a.job_id = b.id')
        //     ->innerJoin(['c' => BaseResume::tableName()], 'a.resume_id = c.id')
        //     ->innerJoin(['d' => BaseMember::tableName()], 'c.member_id = d.id')
        //     ->where(['b.job_category_id' => $categoryId])
        //     ->andWhere([
        //         '>=',
        //         'a.add_time',
        //         $sixMonthAgo,
        //     ])
        //     ->groupBy('a.resume_id')
        //     ->asArray()
        //     ->all();
        //
        // $data = [];
        //
        // foreach ($list as $item) {
        //     $data[] = [
        //         $item['uuid'],
        //         $item['email'],
        //         BaseDictionary::getEducationName($item['top_education_code']),
        //     ];
        // }
        //
        // $excel  = new Excel();
        // $header = [
        //     '人才ID',
        //     '邮箱',
        //     '最高学历',
        // ];
        //
        // $rs = $excel->export($data, $header, '博士后岗位投递数据' . time());

        $list = BaseResumeIntention::find()
            ->select('b.uuid,c.email,b.top_education_code,b.add_time,c.last_login_time')
            ->alias('a')
            ->innerJoin(['b' => BaseResume::tableName()], 'a.resume_id = b.id')
            ->innerJoin(['c' => BaseMember::tableName()], 'b.member_id = c.id')
            ->where([
                'a.job_category_id' => $categoryId,
                'a.status'          => 1,
            ])
            ->groupBy('b.id')
            ->asArray()
            ->all();

        foreach ($list as $item) {
            $data[] = [
                $item['uuid'],
                $item['email'],
                BaseDictionary::getEducationName($item['top_education_code']),
                $item['add_time'],
                $item['last_login_time'],
            ];
        }

        $excel  = new Excel();
        $header = [
            '人才ID',
            '邮箱',
            '最高学历',
            '注册时间',
            '最近登录时间',
        ];

        $rs = $excel->export($data, $header, '博士后岗位投递数据' . time());

        bb($rs);
    }

    /**
     * 需求描述
     * 导出2025.01.01~2025.06.12 有活跃记录、且符合以下要求① 或② 的人才数据（① ② 符合其一即导出）：
     *
     * ① “姓名”字段包含英文字符；
     *
     * ②  户籍国籍 /
     * 籍贯为：越南/老挝/柬埔寨/缅甸/泰国/马来西亚/新加坡/印度尼西亚/菲律宾/文莱/东帝汶/印度/巴基斯坦/斯里兰卡/尼泊尔/孟加拉国/不丹/马尔代夫/土耳其/格鲁吉亚/阿塞拜疆/亚美尼亚/塞浦路斯/黎巴嫩/巴勒斯坦/以色列/叙利亚/伊拉克/伊朗/阿富汗/约旦/沙特阿拉伯/也门/阿曼/科威特/巴林/卡塔尔/阿拉伯联合酋长国/哈萨克斯坦/吉尔吉斯斯坦/塔吉克斯坦/乌兹别克斯坦/土库曼斯坦
     * 等的人才
     *
     * 表头如下
     * ID    姓名    年龄    户籍/国籍    籍贯    人才标签（多个“、”分割）    人才自定义头像链接（如未上传则不展示）    平台站内投递量（累计）    最高学历所学专业（精确至三级）
     * 博士毕业院校    硕士毕业院校    本科毕业院校    注册时间    最近一次活跃时间
     */
    public function getResume20250612()
    {
        $sql = "SELECT uuid, name, household_register_id, residence,last_active_time,resume.id
FROM resume
inner join member on resume.member_id = member.id
WHERE (name REGEXP '[A-Za-z]'
   OR household_register_id IN (SELECT id
                                FROM area
                                WHERE name IN
                                      ('越南', '老挝', '柬埔寨', '缅甸', '泰国', '马来西亚', '新加坡', '印度尼西亚',
                                       '菲律宾', '文莱', '东帝汶', '印度', '巴基斯坦', '斯里兰卡', '尼泊尔', '孟加拉国',
                                       '不丹', '马尔代夫', '土耳其', '格鲁吉亚', '阿塞拜疆', '亚美尼亚', '塞浦路斯',
                                       '黎巴嫩', '巴勒斯坦', '以色列', '叙利亚', '伊拉克', '伊朗', '阿富汗', '约旦',
                                       '沙特阿拉伯', '也门', '阿曼', '科威特', '巴林', '卡塔尔', '阿拉伯联合酋长国',
                                       '哈萨克斯坦', '吉尔吉斯斯坦', '塔吉克斯坦', '乌兹别克斯坦', '土库曼斯坦'))
   OR native_place_area_id IN (SELECT id
                               FROM area
                               WHERE name IN
                                     ('越南', '老挝', '柬埔寨', '缅甸', '泰国', '马来西亚', '新加坡', '印度尼西亚',
                                      '菲律宾', '文莱', '东帝汶', '印度', '巴基斯坦', '斯里兰卡', '尼泊尔', '孟加拉国',
                                      '不丹', '马尔代夫', '土耳其', '格鲁吉亚', '阿塞拜疆', '亚美尼亚', '塞浦路斯',
                                      '黎巴嫩', '巴勒斯坦', '以色列', '叙利亚', '伊拉克', '伊朗', '阿富汗', '约旦',
                                      '沙特阿拉伯', '也门', '阿曼', '科威特', '巴林', '卡塔尔', '阿拉伯联合酋长国',
                                      '哈萨克斯坦', '吉尔吉斯斯坦', '塔吉克斯坦', '乌兹别克斯坦', '土库曼斯坦'))) and member.last_active_time > '2025-01-01 00:00:00'";

        $list = \Yii::$app->db->createCommand($sql)
            ->queryAll();
        $data = [];
        foreach ($list as $item) {
            $resumeId   = $item['id'];
            $resume     = BaseResume::findOne($resumeId);
            $member     = BaseMember::findOne($resume->member_id);
            $resumeInfo = $this->getAllEducation($resumeId);
            // 户籍/国籍
            $householdRegister = BaseArea::findOneVal(['id' => $item['household_register_id']], 'name');
            // 籍贯
            $residence  = BaseArea::getAreaName($item['residence']);
            $avatar     = $member->avatar ? 'http://img.gaoxiaojob.com/' . $member->avatar : '';
            $tagList    = BaseResumeTag::getTabNameList($resumeId);
            $applyCount = BaseJobApply::find()
                ->where(['resume_id' => $resumeId])
                ->count();
            // 最近一次活跃时间
            $lastActiveTime = '';
            if ($member->last_active_time != TimeHelper::ZERO_TIME) {
                $lastActiveTime = $member->last_active_time;
            }
            $data[] = [
                $resume['uuid'],
                $resume['name'],
                $resume->age,
                $householdRegister,
                $residence,
                implode('、', $tagList),
                $avatar,
                $applyCount,
                $resumeInfo['topEducation']['majorLevel1Name'] . '-' . $resumeInfo['topEducation']['majorLevel2Name'] . '-' . $resumeInfo['topEducation']['majorLevel3Name'],
                $resumeInfo['doctor']['school'] ?? '',
                $resumeInfo['master']['school'] ?? '',
                $resumeInfo['bachelor']['school'] ?? '',
                $resume->add_time,
                $lastActiveTime,
            ];
        }
        $excel  = new Excel();
        $header = [
            'ID',
            '姓名',
            '年龄',
            '户籍/国籍',
            '籍贯',
            '人才标签（多个“、”分割）',
            '人才自定义头像链接（如未上传则不展示）',
            '平台站内投递量（累计）',
            '最高学历所学专业（精确至三级）',
            '博士毕业院校',
            '硕士毕业院校',
            '本科毕业院校',
            '注册时间',
            '最近一次活跃时间',
        ];

        $rs = $excel->export($data, $header, '在线简历数据' . time());
        bb($rs);
    }

    /**
     * 背景：高才通售卖支持：业务侧售卖高才通时，较多需要博士人才学科占比、地区分布占比数据。 现计划输出相关数据对业务侧进行同步。
     *
     * 需求：
     *
     * 需协助查询：各学科，博士人才数量；
     *
     * 需协助查询：各地区（一级）省份，意向博士人才数据；
     *
     * 各省份（含省份下城市），博士人才意向数量：
     *
     * 如同一人才同时意向北京、广东，则北京，广东数量各+1；如同一人才同时意向广东、广州，则广东+1；
     *
     * 表格
     * ··    博士人才数量
     * 北京    1100
     * 广东    120
     * 海外
     */

    public function getResume20250623()
    {
        $header = [
            '地区',
            '博士人才数量',
        ];

        // 找到所有博士人才
        $list = BaseResume::find()
            ->select([
                'id',
            ])
            ->where([
                'top_education_code' => BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
            ])
            ->asArray()
            ->all();

        $areaCountList = [];

        foreach ($list as $item) {
            $resumeId = $item['id'];

            $areaIdsArrayList = BaseResumeIntention::find()
                ->select([
                    'area_id',
                ])
                ->where([
                    'resume_id' => $resumeId,
                    'status'    => 1,
                ])
                ->asArray()
                ->all();

            if (!$areaIdsArrayList) {
                continue;
            }

            $thisResumeAreaIdsArray = [];
            foreach ($areaIdsArrayList as $areaIdsArrayItem) {
                $thisItemAreaArray = explode(',', $areaIdsArrayItem['area_id']);
                foreach ($thisItemAreaArray as $thisItemArea) {
                    if (!in_array($thisItemArea, $thisResumeAreaIdsArray)) {
                        $thisResumeAreaIdsArray[] = $thisItemArea;
                    }
                }
            }

            // 找到一个人才全部的地区数组,找地区数据和父地区信息
            $areaList = BaseArea::find()
                ->select([
                    'id',
                    'name',
                    'parent_id',
                    'level',
                ])
                ->where(['id' => $thisResumeAreaIdsArray])
                ->asArray()
                ->all();

            // 拿level=1的id和level=2的parent_id
            $provinceIdList = [];
            foreach ($areaList as $areaItem) {
                if ($areaItem['level'] == 1) {
                    // 一级地区
                    $provinceIdList[] = $areaItem['id'];
                } elseif ($areaItem['level'] == 2) {
                    // 二级地区
                    if (!in_array($areaItem['parent_id'], $provinceIdList)) {
                        $provinceIdList[] = $areaItem['parent_id'];
                    }
                }
            }

            // 去重
            $provinceIdList = array_unique($provinceIdList);

            foreach ($provinceIdList as $province) {
                $areaCountList[$province] += 1;
            }
        }

        $data = [];
        foreach ($areaCountList as $k => $p) {
            $data[] = [
                BaseArea::findOneVal(['id' => $k], 'name'),
                $p,
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '博士人才数量' . time());
        bb($rs);
    }

    /**
     * 平台注册用户，博士人才、最高教育经历（博士学历）相关学科；各大类、一级学科、二级学科专业博士人才数据分布统计：
     * 二级学科（系统中三级）    一级学科（系统中的二级）    学科大类（系统中的一级）    博士人才数量
     * 理论经济学    理论经济学    经济学    1100
     * 理论经济学    应用经济学    经济学    1200
     * 理论经济学    法学与法律    法学    120
     * ···
     */
    public function getResume20250624()
    {
        // 先找到系统中全部的博士人才
        $list = BaseResume::find()
            ->select([
                'id',
                'last_education_id',
            ])
            ->where([
                'top_education_code' => BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
            ])
            ->asArray()
            ->all();

        $data      = [];
        $majorList = [];
        foreach ($list as $item) {
            $resumeId = $item['id'];
            // 获取这个学历的相关信息
            $lastEducationId     = $item['last_education_id'];
            $resumeEducationInfo = BaseResumeEducation::findOne($lastEducationId);
            if (!$resumeEducationInfo) {
                continue;
            }

            $majorLevel3Name = BaseMajor::findOneVal(['id' => $resumeEducationInfo->major_id_level_3], 'name');
            $majorLevel2Name = BaseMajor::findOneVal(['id' => $resumeEducationInfo->major_id_level_2], 'name');
            $majorLevel1Name = BaseMajor::findOneVal(['id' => $resumeEducationInfo->major_id_level_1], 'name');

            $majorList[$resumeEducationInfo->major_id_level_3]['count']    += 1;
            $majorList[$resumeEducationInfo->major_id_level_3]['level3']   = $majorLevel3Name;
            $majorList[$resumeEducationInfo->major_id_level_3]['level2']   = $majorLevel2Name;
            $majorList[$resumeEducationInfo->major_id_level_3]['level1']   = $majorLevel1Name;
            $majorList[$resumeEducationInfo->major_id_level_3]['level3Id'] = $resumeEducationInfo->major_id_level_3;
        }

        // 全部数据获取完毕
        foreach ($majorList as $k => $item) {
            $data[] = [
                $item['level3'],
                $item['level2'],
                $item['level1'],
                $item['count'],
                $item['level3Id'],
            ];
        }

        $header = [
            '二级学科（系统中三级）',
            '一级学科（系统中的二级）',
            '学科大类（系统中的一级）',
            '博士人才数量',
            '二级学科ID',
        ];

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '博士人才学科数据' . time());
        bb($rs);
    }

    /**
     * 硕士研究生人才导出
     *
     * 筛选条件：
     * 1. 简历完善度超过40%
     * 2. 最近活跃时间：2025年4月1日-2025年6月30日
     * 3. 年龄：21-32岁
     * 4. 无"外籍人才"标签
     * 5. 求职状态非"暂不找工作"
     * 6. 最高学历：硕士研究生
     *
     * 导出字段：
     * - 用户ID（UUID）
     * - 邮箱
     * - 专业大类
     * - 参加工作时间
     * - 身份（职场人/应届毕业生）
     * - 硕士毕业院校
     * - 海外经历标签
     */
    public function getMasterTalentExport()
    {
        // 查询符合条件的简历
        $query = BaseResume::find()
            ->alias('r')
            ->innerJoin(['m' => BaseMember::tableName()], 'r.member_id = m.id')
            ->leftJoin(['re' => BaseResumeEducation::tableName()], 'r.last_education_id = re.id')
            ->where([
                'r.status' => BaseResume::STATUS_ACTIVE,
                'm.status' => BaseMember::STATUS_ACTIVE,
            ])
            // 简历完善度超过40%
            ->andWhere([
                '>',
                'r.complete',
                40,
            ])
            // 最近活跃时间：2025年4月1日-2025年6月30日
            ->andWhere([
                'between',
                'm.last_active_time',
                '2025-04-01 00:00:00',
                '2025-06-30 23:59:59',
            ])
            // 年龄：21-32岁
            ->andWhere([
                'between',
                'r.age',
                21,
                32,
            ])
            // 最高学历：硕士研究生
            ->andWhere(['r.top_education_code' => BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE])
            // 求职状态非"暂不找工作" (假设暂不找工作的code是4，需要根据实际情况调整)
            ->andWhere([
                '!=',
                'r.work_status',
                4,
            ]);

        // 排除有"外籍人才"标签的用户
        $foreignTalentTagId = BaseResumeTag::find()
            ->where(['tag' => '外籍人才'])
            ->select('id')
            ->scalar();

        if ($foreignTalentTagId) {
            $excludeResumeIds = BaseResumeTagRelation::find()
                ->where(['resume_tag_id' => $foreignTalentTagId])
                ->select('resume_id')
                ->column();

            if (!empty($excludeResumeIds)) {
                $query->andWhere([
                    'not in',
                    'r.id',
                    $excludeResumeIds,
                ]);
            }
        }

        $list = $query->select([
            'r.id',
            'r.uuid',
            'r.member_id',
            'r.identity_type',
            'r.begin_work_date',
            'm.email',
            're.major_id',
        ])
            ->asArray()
            ->all();

        $header = [
            '用户ID',
            '邮箱',
            '专业大类',
            '参加工作时间',
            '身份',
            '硕士毕业院校',
            '海外经历标签',
        ];

        $data = [];
        foreach ($list as $item) {
            // 获取用户UUID

            // 获取专业大类
            $majorName = '';
            if ($item['major_id']) {
                $majorInfo = BaseMajor::find()
                    ->where(['id' => $item['major_id']])
                    ->select([
                        'name',
                        'parent_id',
                        'level',
                    ])
                    ->asArray()
                    ->one();

                if ($majorInfo) {
                    // 如果是三级专业，需要找到一级专业
                    if ($majorInfo['level'] == 3) {
                        // 找到二级专业
                        $level2Major = BaseMajor::find()
                            ->where(['id' => $majorInfo['parent_id']])
                            ->select(['parent_id'])
                            ->asArray()
                            ->one();

                        if ($level2Major) {
                            // 找到一级专业
                            $level1Major = BaseMajor::find()
                                ->where(['id' => $level2Major['parent_id']])
                                ->select(['name'])
                                ->asArray()
                                ->one();

                            $majorName = $level1Major['name'] ?? '';
                        }
                    } elseif ($majorInfo['level'] == 2) {
                        // 如果是二级专业，直接找一级专业
                        $level1Major = BaseMajor::find()
                            ->where(['id' => $majorInfo['parent_id']])
                            ->select(['name'])
                            ->asArray()
                            ->one();

                        $majorName = $level1Major['name'] ?? '';
                    } elseif ($majorInfo['level'] == 1) {
                        // 如果已经是一级专业
                        $majorName = $majorInfo['name'];
                    }
                }
            }

            // 获取硕士毕业院校
            $masterSchool = BaseResumeEducation::find()
                ->where([
                    'resume_id'    => $item['id'],
                    'education_id' => BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE,
                    'status'       => 1,
                ])
                ->select('school')
                ->scalar();

            // 判断海外经历
            $hasAbroadExperience = false;

            // 检查教育经历中的海外经历
            $hasAbroadEducation = BaseResumeEducation::find()
                ->where([
                    'resume_id' => $item['id'],
                    'is_abroad' => 1,
                    'status'    => 1,
                ])
                ->exists();

            // 检查工作经历中的海外经历
            $hasAbroadWork = BaseResumeWork::find()
                ->where([
                    'resume_id' => $item['id'],
                    'is_abroad' => 1,
                    'status'    => 1,
                ])
                ->exists();

            $hasAbroadExperience = $hasAbroadEducation || $hasAbroadWork;

            // 身份转换
            $identityText = '';
            switch ($item['identity_type']) {
                case BaseResume::IDENTITY_TYPE_WORKER:
                    $identityText = '职场人';
                    break;
                case BaseResume::IDENTITY_TYPE_GRADUATE:
                    $identityText = '应届毕业生';
                    break;
                default:
                    $identityText = '未知';
                    break;
            }

            // 0000-00-00 或者空，都重置为空
            if ($item['begin_work_date'] === '0000-00-00') {
                $item['begin_work_date'] = '';
            }

            $data[] = [
                $item['uuid'],
                $item['email'],
                $majorName,
                $item['begin_work_date'] ?: '',
                $identityText,
                $masterSchool ?: '',
                $hasAbroadExperience ? '是' : '否',
            ];
        }

        $excel = new Excel();
        $rs    = $excel->export($data, $header, '硕士研究生人才导出_' . date('Y-m-d_H-i-s'));
        bb($rs);
    }

}
