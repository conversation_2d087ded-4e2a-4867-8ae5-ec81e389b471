<?php
/**
 * create user：shannon
 * create time：2025/4/7 上午11:42
 */
namespace common\service\company;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\libs\Cache;
use common\service\CommonService;

class RecommendService extends CommonService
{
    /**
     * 非合作单位同类型雇主推荐职位
     * 非合作单位主页，右侧常显新增【同类型雇主职位推荐】模块（* 合作单位主页不作调整）：
     * 1、调用10条 在线 且学历要求为 本科及以上（即职位“学历要求”=本科/硕士研究生/博士研究生）的职位。职位调用优先级：
     * （1）随机调用10条与当前单位“单位类型”相同的其他单位 近30日发布（指对外发布时间；含当前日）的职位；
     * （2）若“单位类型”缺失或（1）规则调用职位不足10条，则随机补充调用单位类型为
     * 双一流院校/普通本科院校/高职高专院校的其他单位近30日（指对外发布时间；含当前日）发布的职位（须剔除规则（1）已调用职位、当前单位的职位，避免重复展示）。
     * 2、展示&交互：
     * （1）排序：按职位发布日（指对外发布日）倒序排；发布日相同，优先展示合作单位职位，再展示非合作单位职位。
     * （2）分页：每页展示5条职位信息。每隔7s后自动滚动到下一页，同时支持用户手动切换翻页。 最多展示两页共计10条职位信息；
     * （3）字段说明及交互见卡片标注。
     * 3、本模块内容更新周期：每隔48小时更新一次即可。
     */
    public function companyTypeJob($companyId)
    {
        // 获取当前单位信息
        $companyInfo = BaseCompany::findOne($companyId);
        // 获取当前单位类型
        $companyType = $companyInfo->type;
        //        $cacheTypeKey = $companyType > 0 ? Cache::ALL_COMPANY_DETAIL_UN_RECOMMEND_KEY . ':' . $companyType : Cache::ALL_COMPANY_DETAIL_UN_RECOMMEND_KEY . ':999999';
        $cacheTypeKey = Cache::ALL_COMPANY_DETAIL_UN_RECOMMEND_KEY . ':' . $companyId;
        $data         = Cache::get($cacheTypeKey);
        if ($data) {
            $list = json_decode($data, true);
        } else {
            $list        = [];
            $commonQuery = BaseJob::find()
                ->select([
                    'j.id',
                    'j.name',
                    'j.amount',
                    'j.announcement_id as announcementId',
                    'j.refresh_time as refreshTime',
                    'j.refresh_date as refreshDate',
                    'j.company_id as companyId',
                    'j.wage_type as wageType',
                    'j.min_wage as minWage',
                    'j.max_wage as maxWage',
                    'j.experience_type as experienceType',
                    'j.education_type as educationType',
                    'j.city_id as cityId',
                    'c.full_name as companyName',
                ])
                ->alias('j')
                ->innerJoin(['c' => BaseCompany::tableName()], 'j.company_id=c.id')
                ->andWhere([
                    'j.status'         => BaseJob::STATUS_ONLINE,
                    'j.education_type' => [
                        BaseDictionary::EDUCATION_DOCTOR_ID,
                        BaseDictionary::EDUCATION_MASTER_ID,
                        BaseDictionary::EDUCATION_POACEAE_ID,
                    ],
                ])
                ->andWhere([
                    '!=',
                    'j.company_id',
                    $companyId,
                ])
                ->andWhere([
                    '>=',
                    'j.refresh_date',
                    date('Y-m-d', strtotime('-30 days')),
                ])
                ->orderBy('rand()');
            if ($companyType > 0) {
                $list1 = (clone $commonQuery)->andWhere([
                    'c.type' => $companyType,
                ])
                    ->limit(10)
                    ->asArray()
                    ->all();
                // 不为空 合并到list
                if (!empty($list1)) {
                    $list = array_merge($list, $list1);
                }
            }
            $listCount = count($list);
            if ($listCount < 10) {
                $list2Query = (clone $commonQuery);
                if ($listCount > 0) {
                    $list2Query->andWhere([
                        'not in',
                        'j.id',
                        array_column($list, 'id'),
                    ]);
                }
                $list2 = $list2Query->andWhere([
                    //双一流院校/普通本科院校/高职高专院校
                    'c.type' => [
                        1,
                        2,
                        3,
                    ],
                ])
                    ->limit(10 - $listCount)
                    ->asArray()
                    ->all();
                // 不为空 合并到list
                if (!empty($list2)) {
                    $list = array_merge($list, $list2);
                }
            }
            //缓存48小时
            Cache::set($cacheTypeKey, json_encode($list), 48 * 3600);
        }

        foreach ($list as &$value) {
            //地区文案
            $value['cityName'] = BaseArea::getAreaName($value['cityId']);
            //公告名称
            $value['announcementName'] = $value['announcementId'] > 0 ? BaseAnnouncement::findOne($value['announcementId'])->title : '';
            //薪资
            $value['wage'] = BaseJob::formatWage($value['minWage'], $value['maxWage'], $value['wageType']);
            //学历要求
            $value['educationTypeName'] = BaseDictionary::getEducationName($value['educationType']);
            //经验要求
            $value['experienceTypeName'] = BaseDictionary::getExperienceName($value['experienceType']);
            //职位详情url
            $value['jobUrl'] = $this->operationPlatform == CommonService::PLATFORM_WEB ? UrlHelper::createPcJobDetailPath($value['id']) : UrlHelper::createH5JobDetailPath($value['id']);
            //单位详情url
            $value['companyUrl'] = $this->operationPlatform == CommonService::PLATFORM_WEB ? UrlHelper::createPcCompanyDetailPath($value['companyId']) : UrlHelper::createH5CompanyDetailPath($value['companyId']);
            //发布时间
            $value['refreshTime'] = TimeHelper::formatDateByYear($value['refreshTime']);
        }

        return $list;
    }
}