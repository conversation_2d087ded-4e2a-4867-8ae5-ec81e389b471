<?php

namespace common\service\hwActivity;

use common\base\models\BaseAdmin;
use common\base\models\BaseAdminPositionMenu;
use common\base\models\BaseArea;
use common\base\models\BaseHwActivity;
use common\base\models\BaseHwActivityCompanyHot;
use common\base\models\BaseHwActivityFeatureTagRelation;
use common\base\models\BaseHwActivityPromotion;
use common\base\models\BaseHwActivitySession;
use common\base\models\BaseHwActivitySessionArea;
use common\helpers\TimeHelper;
use common\libs\WxWork;
use common\helpers\ValidateHelper;
use common\service\CommonService;
use queue\Producer;
use Yii;
use yii\base\Exception;

class saveService extends CommonService

{

    public $saveData = [];

    /* @var BaseHwActivity */
    private $activityModel;
    private $activityId;
    private $isAuthOperation = false;
    private $isAdd           = false;
    /** 1=非招聘会 2=招聘会 */
    private $zhaoPinHuiType;

    public function setData($data)
    {
        $this->saveData        = $data;
        $this->isAuthOperation = BaseAdminPositionMenu::getAuth('sortAndGrounding');

        return $this;
    }

    /**
     * 执行添加
     * @throws Exception
     */
    public function run()
    {
        $this->commonBeforeRun();
        if ($this->saveData['id']) {
            //有ID 编辑
            $this->activityModel = BaseHwActivity::findOne($this->saveData['id']);
            $this->isAdd         = false;
        } else {
            //无ID 新增
            $this->activityModel = new BaseHwActivity();
            $this->isAdd         = true;
        }
        //判断一下类型
        if (in_array($this->saveData['seriesType'], BaseHwActivity::ZHAOPINHUI_UN_TYPE)) {
            $this->zhaoPinHuiType = 1;
            //非招聘会
            $this->zhaoPinHuiUnBeforeRun();
        } else {
            //招聘会
            $this->zhaoPinHuiType = 2;
            $this->zhaoPinHuiBeforeRun();
        }
        $this->clearData();
        $this->saveActivityForm();
        $this->saveSession();
        $this->savePromotion();
        $this->afterRun();

        // 返回当前活动id

        return [
            'id' => $this->activityId,
        ];
    }

    private function clearData()
    {
        if ($this->zhaoPinHuiType == 1) {
            //非招聘会
            $this->saveData['subType']                     = 0;
            $this->saveData['isCustomTime']                = 0;
            $this->saveData['startDate']                   = '';
            $this->saveData['startTime']                   = '';
            $this->saveData['endDate']                     = '';
            $this->saveData['endTime']                     = '';
            $this->saveData['customTime']                  = '';
            $this->saveData['isCustomAddress']             = 0;
            $this->saveData['customAddress']               = '';
            $this->saveData['areaIds']                     = '';
            $this->saveData['detailAddress']               = '';
            $this->saveData['latitude']                    = '';
            $this->saveData['longitude']                   = '';
            $this->saveData['activityNumber']              = '';
            $this->saveData['activityOrganization']        = '';
            $this->saveData['activityDetail']              = '';
            $this->saveData['participationMethod']         = '';
            $this->saveData['activityHighlights']          = '';
            $this->saveData['activityHighlightsTitle']     = '';
            $this->saveData['activityBenefits']            = '';
            $this->saveData['activityBenefitsContent']     = '';
            $this->saveData['attendanceNotes']             = '';
            $this->saveData['participationBenefit']        = '';
            $this->saveData['participationBenefitDetail']  = '';
            $this->saveData['imagePcBannerId']             = 0;
            $this->saveData['imagePcBannerUrl']            = '';
            $this->saveData['imageMiniBannerId']           = 0;
            $this->saveData['imageMiniBannerUrl']          = '';
            $this->saveData['imageMiniMasterId']           = 0;
            $this->saveData['imageMiniMasterUrl']          = '';
            $this->saveData['imageServiceCodeId']          = 0;
            $this->saveData['applyLinkPersonType']         = 0;
            $this->saveData['applyLinkPersonFormId']       = 0;
            $this->saveData['applyLinkPersonFormOptionId'] = 0;
            $this->saveData['applyLinkPerson']             = '';
            $this->saveData['applyPersonTime']             = '';
            $this->saveData['activityDomain']              = '';
            $this->saveData['applyLinkCompany']            = '';
            $this->saveData['applyCompanyTime']            = '';
            $this->saveData['templateId']                  = 0;
            $this->saveData['activityLink']                = '';
            $this->saveData['companyHotList']              = [];
            $this->saveData['wonderfulReview']             = '';
            $this->saveData['imageNoticeId']               = 0;
        } else {
            //招聘会
            $this->saveData['sessionIds']            = '';
            $this->saveData['signEndDate']           = '';
            $this->saveData['signCustomEndDate']     = '';
            $this->saveData['isOutsideUrl']          = 0;
            $this->saveData['logoFileId']            = 0;
            $this->saveData['reviewImgList']         = [];
            $this->saveData['otherDescriptionOne']   = '';
            $this->saveData['otherDescriptionTwo']   = '';
            $this->saveData['otherDescriptionThree'] = '';
            $this->saveData['companyId']             = 0;
        }
    }

    /**
     * 公共验证与数据预处理
     * @return void
     * @throws \Exception
     */
    private function commonBeforeRun()
    {
        if (empty($this->saveData['toHoldType']) || !$this->saveData['seriesType'] || !$this->saveData['type'] || !$this->saveData['name'] || !$this->saveData['groundingStatus']) {
            throw new Exception('缺少必填参数');
        }
        if ($this->saveData['sort'] > 999) {
            throw new Exception('活动排序最大值999');
        }
    }

    /**
     * 非招聘会验证与数据预处理
     * @return void
     * @throws \Exception
     */
    private function zhaoPinHuiUnBeforeRun()
    {
        if (!$this->saveData['sessionIds']) {
            throw new Exception('缺少必填参数');
        }
    }

    /**
     * 招聘会验证与数据预处理
     * @return void
     * @throws \Exception
     */
    private function zhaoPinHuiBeforeRun()
    {
        //        if (!$this->saveData['activityDetail']) {
        //            throw new \Exception('缺少必填参数');
        //        }
        if ($this->saveData['customTime'] == '' && ($this->saveData['startDate'] != TimeHelper::ZERO_DATE)) {
            // 非自定义时间,结束时间必须大于等于开始时间，转成时间戳去判断
            if (strtotime($this->saveData['endDate']) < strtotime($this->saveData['startDate'])) {
                throw new Exception('结束时间必须大于等于开始时间');
            }

            if ($this->saveData['startDate'] == $this->saveData['endDate'] && $this->saveData['startTime'] && $this->saveData['endTime']) {
                if ($this->saveData['startTime'] >= $this->saveData['endTime']) {
                    throw new Exception('结束时间必须大于开始时间');
                }
            }
        }
        //参会福利&参会须知：非必填；单行文本；限制最多150字
        if ($this->saveData['activityBenefits'] && mb_strlen($this->saveData['activityBenefits']) > 150) {
            throw new Exception('参会福利不能超过150字');
        }
        if ($this->saveData['attendanceNotes'] && mb_strlen($this->saveData['attendanceNotes']) > 150) {
            throw new Exception('参会须知不能超过150字');
        }
        if ($this->saveData['applyLinkCompany'] && !ValidateHelper::isUrl($this->saveData['applyLinkCompany'])) {
            throw new Exception('单位报名链接格式错误');
        }
        //原活动亮--现在参会须知的标题
        if ($this->saveData['activityHighlightsTitle'] && mb_strlen($this->saveData['activityHighlightsTitle']) > 6) {
            throw new Exception('参会须知的标题不能超过6字符');
        }

        if ($this->saveData['applyLinkPersonType'] == BaseHwActivity::APPLY_LINK_PERSON_TYPE_OTHER && $this->saveData['applyLinkPerson'] && !ValidateHelper::isUrl($this->saveData['applyLinkPerson'])) {
            throw new Exception('人才报名链接格式错误');
        }
        $existsQuery = BaseHwActivity::find()
            ->where(['activity_link' => $this->saveData['activityLink']]);
        if ($this->saveData['id']) {
            $existsQuery->andWhere([
                '!=',
                'id',
                $this->saveData['id'],
            ]);
        }
        //判断页面链接唯一性
        if ($existsQuery->exists()) {
            throw new Exception('页面链接已存在，请重新编辑页面链接！');
        }
    }

    /**
     * 保存活动
     * @return void
     * @throws Exception
     */
    private function saveActivityForm()
    {
        $this->activityModel->name                    = $this->saveData['name'];
        $this->activityModel->series_type             = $this->saveData['seriesType'];
        $this->activityModel->type                    = $this->saveData['type'];
        $this->activityModel->sub_type                = $this->saveData['subType'] ?: 0;
        $this->activityModel->introduce               = $this->saveData['introduce'] ?: '';
        $this->activityModel->sign_end_date           = $this->saveData['signEndDate'] ?: TimeHelper::ZERO_DATE;
        $this->activityModel->sign_custom_end_date    = $this->saveData['signCustomEndDate'] ?: '';
        $this->activityModel->detail_url              = $this->saveData['detailUrl'] ?: '';
        $this->activityModel->sign_up_url             = $this->saveData['signUpUrl'] ?: '';
        $this->activityModel->main_img_file_id        = $this->saveData['mainImgFileId'] ?: '';
        $this->activityModel->logo_file_id            = $this->saveData['logoFileId'] ?: '';
        $this->activityModel->other_img_one_file_id   = $this->saveData['otherImgOneFileId'] ?: '';
        $this->activityModel->other_img_two_file_id   = $this->saveData['otherImgTwoFileId'] ?: '';
        $this->activityModel->other_img_three_file_id = $this->saveData['otherImgThreeFileId'] ?: '';
        $this->activityModel->other_description_one   = $this->saveData['otherDescriptionOne'] ?: '';
        $this->activityModel->other_description_two   = $this->saveData['otherDescriptionTwo'] ?: '';
        $this->activityModel->other_description_three = $this->saveData['otherDescriptionThree'] ?: '';
        if ($this->isAdd) {
            $this->activityModel->admin_id = Yii::$app->user->id;
            if ($this->isAuthOperation) {
                //添加-有权限
                $this->activityModel->grounding_status = $this->saveData['groundingStatus'] ?: BaseHwActivity::GROUNDING_STATUS_OFF;
                $this->activityModel->sort             = $this->saveData['sort'] ?: 0;
                if ($this->saveData['groundingStatus'] == BaseHwActivity::GROUNDING_STATUS_ON) {
                    $this->activityModel->first_grounding_time = date('Y-m-d H:i:s');
                }
            } else {
                //添加无权限
                $this->activityModel->grounding_status = BaseHwActivity::GROUNDING_STATUS_OFF;
                $this->activityModel->sort             = 0;
            }
        } else {
            //编辑有权限则按照传递内容去修改；无权限则不修改
            $oldFirstGroundingTime = $this->activityModel->first_grounding_time;
            if ($this->isAuthOperation) {
                $this->activityModel->grounding_status = $this->saveData['groundingStatus'] ?: BaseHwActivity::GROUNDING_STATUS_OFF;
                $this->activityModel->sort             = $this->saveData['sort'] ?: 0;
                if ($this->saveData['groundingStatus'] == BaseHwActivity::GROUNDING_STATUS_ON && $oldFirstGroundingTime == TimeHelper::ZERO_TIME) {
                    $this->activityModel->first_grounding_time = date('Y-m-d H:i:s');
                }
            }
        }

        $this->activityModel->company_id                       = $this->saveData['companyId'] ?: 0;
        $this->activityModel->is_outside_url                   = $this->saveData['isOutsideUrl'] ?: 0;
        $this->activityModel->custom_feature_tag               = $this->saveData['customFeatureTag'] ?: '';
        $this->activityModel->review_img_file_ids              = $this->saveData['reviewImgList'] ? json_encode($this->saveData['reviewImgList']) : '';
        $this->activityModel->tags                             = $this->saveData['tags'] ? implode(',',
            $this->saveData['tags']) : '';
        $this->activityModel->longitude                        = $this->saveData['longitude'] ?: '';
        $this->activityModel->latitude                         = $this->saveData['latitude'] ?: '';
        $this->activityModel->activity_organization            = $this->saveData['activityOrganization'] ?: '';
        $this->activityModel->activity_detail                  = $this->saveData['activityDetail'] ?: '';
        $this->activityModel->participation_method             = $this->saveData['participationMethod'] ?: '';
        $this->activityModel->activity_highlights              = $this->saveData['activityHighlights'] ?: '';
        $this->activityModel->activity_highlights_title        = $this->saveData['activityHighlightsTitle'] ?: '参会须知';
        $this->activityModel->activity_benefits                = $this->saveData['activityBenefits'] ?: '';
        $this->activityModel->activity_benefits_content        = $this->saveData['activityBenefitsContent'] ?: '';
        $this->activityModel->attendance_notes                 = $this->saveData['attendanceNotes'] ?: '';
        $this->activityModel->image_pc_banner_id               = $this->saveData['imagePcBannerId'] ?: 0;
        $this->activityModel->image_service_code_id            = $this->saveData['imageServiceCodeId'] ?: 0;
        $this->activityModel->image_mini_master_id             = $this->saveData['imageMiniMasterId'] ?: 0;
        $this->activityModel->image_mini_banner_id             = $this->saveData['imageMiniBannerId'] ?: 0;
        $this->activityModel->image_notice_id                  = $this->saveData['imageNoticeId'] ?: 0;
        $this->activityModel->apply_link_person_type           = $this->saveData['applyLinkPersonType'] ?: 0;
        $this->activityModel->apply_link_person_form_id        = $this->saveData['applyLinkPersonFormId'] ?: 0;
        $this->activityModel->apply_link_person_form_option_id = $this->saveData['applyLinkPersonFormOptionId'] ?: 0;
        $this->activityModel->apply_link_person                = $this->saveData['applyLinkPerson'] ?: '';
        $this->activityModel->apply_person_time                = $this->saveData['applyPersonTime'] ?: TimeHelper::ZERO_DATE;
        $this->activityModel->apply_link_company               = $this->saveData['applyLinkCompany'] ?: '';
        $this->activityModel->apply_company_time               = $this->saveData['applyCompanyTime'] ?: TimeHelper::ZERO_DATE;
        $this->activityModel->template_id                      = $this->saveData['templateId'] ?: BaseHwActivity::TEMPLATE_TYPE_DEFAULT;
        $this->activityModel->activity_link                    = $this->saveData['activityLink'] ?: '';
        $this->activityModel->wonderful_review                 = $this->saveData['wonderfulReview'] ?: '';
        $this->activityModel->activity_number                  = $this->saveData['activityNumber'] ?: '';

        if (count($this->saveData['toHoldType']) > 1) {
            $this->activityModel->to_hold_type = in_array(BaseHwActivity::TO_HOLD_TYPE_ONLINE,
                $this->saveData['toHoldType']) && in_array(BaseHwActivity::TO_HOLD_TYPE_OFFLINE,
                $this->saveData['toHoldType']) ? BaseHwActivity::TO_HOLD_TYPE_ONLINE_AND_OFFLINE : BaseHwActivity::TO_HOLD_TYPE_ONLINE;
        } else {
            $this->activityModel->to_hold_type = $this->saveData['toHoldType'][0];
        }

        if (!$this->activityModel->save()) {
            throw new Exception($this->activityModel->getFirstErrorsMessage());
        }
        $this->activityId = $this->activityModel->id;
        $adminInfo        = BaseAdmin::findOne(Yii::$app->user->id);
        //推送消息
        if ($this->isAdd) {
            (new WxWork())->robotActivityPushMessage($adminInfo->name . '创建了活动：' . $this->activityModel->name . '（{' . BaseHwActivity::SERIES_TEXT_LIST[$this->activityModel->series_type] . '-' . BaseHwActivity::TYPE_TEXT_LIST[$this->activityModel->type] . '}），请及时审核。');
            if ($this->isAuthOperation && $this->saveData['groundingStatus'] == BaseHwActivity::GROUNDING_STATUS_ON) {
                //推送消息审核+上架
                (new WxWork())->message($adminInfo->id,
                    '您创建的活动：' . $this->activityModel->name . '（{' . BaseHwActivity::SERIES_TEXT_LIST[$this->activityModel->series_type] . '-' . BaseHwActivity::TYPE_TEXT_LIST[$this->activityModel->type] . '}）已上架。');
            }
        } else {
            //编辑有权限则按照传递内容去修改；无权限则不修改
            if ($this->isAuthOperation && $oldFirstGroundingTime == TimeHelper::ZERO_TIME && $this->saveData['groundingStatus'] == BaseHwActivity::GROUNDING_STATUS_ON) {
                //发送上架消息
                (new WxWork())->message($adminInfo->id,
                    '您创建的活动：' . $this->activityModel->name . '（{' . BaseHwActivity::SERIES_TEXT_LIST[$this->activityModel->series_type] . '-' . BaseHwActivity::TYPE_TEXT_LIST[$this->activityModel->type] . '}）已上架。');
            }
        }
        //写入特色标签
        $this->setFeatureTag();
    }

    /**
     * 保存特色标签
     * @param $featureTagId
     */
    private function setFeatureTag()
    {
        $featureTagId = $this->saveData['featureTagId'];
        if (!$featureTagId) {
            return true;
        }
        //先删除
        BaseHwActivityFeatureTagRelation::deleteAll(['activity_id' => $this->activityId]);
        //再新增
        foreach ($featureTagId as $item) {
            $relationModel                 = new BaseHwActivityFeatureTagRelation();
            $relationModel->activity_id    = $this->activityId;
            $relationModel->feature_tag_id = $item;
            $relationModel->add_time       = CUR_DATETIME;
            if (!$relationModel->save()) {
                throw new \yii\db\Exception($relationModel->getFirstErrorsMessage());
            }
        }

        return true;
    }

    /**
     * 活动场次已经有临时记录，这里更新
     * @throws \yii\db\Exception
     */
    private function saveSession()
    {
        if ($this->zhaoPinHuiType == 2) {
            if ($this->activityId <= 0) {
                throw new Exception('场次不存在');
            }
            //招聘会系列
            //先获取当前活动的场次
            $sessionModel = BaseHwActivitySession::findOne(['activity_id' => $this->activityId]);
            if (!$sessionModel) {
                //新增
                $sessionModel = new BaseHwActivitySession();
            }
            // 判断结束时间必须大于开始时间
            //isCustomTime 1自定义 2选择时间
            //isCustomAddress 1自定义 2选择点
            $sessionModel->activity_id    = $this->activityId;
            $sessionModel->name           = $this->saveData['name'];
            $sessionModel->custom_time    = $this->saveData['isCustomTime'] == 1 ? ($this->saveData['customTime'] ?: '') : '';
            $sessionModel->start_date     = $this->saveData['isCustomTime'] == 2 ? ($this->saveData['startDate'] ?: TimeHelper::ZERO_DATE) : TimeHelper::ZERO_DATE;
            $sessionModel->start_time     = $this->saveData['isCustomTime'] == 2 ? ($this->saveData['startTime'] ?: '') : '';
            $sessionModel->end_date       = $this->saveData['isCustomTime'] == 2 ? ($this->saveData['endDate'] ?: TimeHelper::ZERO_DATE) : TimeHelper::ZERO_DATE;
            $sessionModel->end_time       = $this->saveData['isCustomTime'] == 2 ? ($this->saveData['endTime'] ?: '') : '';
            $sessionModel->custom_address = $this->saveData['isCustomAddress'] == 1 ? ($this->saveData['customAddress'] ?: '') : '';
            $sessionModel->detail_address = $this->saveData['isCustomAddress'] == 2 ? ($this->saveData['detailAddress'] ?: '') : '';

            if (!$sessionModel->save()) {
                throw new Exception($sessionModel->getFirstErrorsMessage());
            }
            $sessionId = $sessionModel->id;
            if ($this->saveData['isCustomAddress'] == 2) {
                if (!empty($this->saveData['areaIds'])) {
                    //如果存在地点id，先把关联表数据清空
                    BaseHwActivitySessionArea::deleteAll(['session_id' => $sessionId]);
                    foreach ($this->saveData['areaIds'] as $areaId) {
                        $sessionAreaModel              = new BaseHwActivitySessionArea();
                        $sessionAreaModel->activity_id = $sessionModel->activity_id;
                        $sessionAreaModel->session_id  = $sessionId;
                        $sessionAreaModel->area_id     = $areaId;
                        $sessionAreaModel->level       = BaseArea::findOneVal(['id' => $areaId], 'level');
                        if (!$sessionAreaModel->save()) {
                            throw new Exception($sessionAreaModel->getFirstErrorsMessage());
                        }
                    }
                }
            } else {
                BaseHwActivitySessionArea::deleteAll(['session_id' => $sessionId]);
            }

            //这里处理一下 热门单位的逻辑
            $this->saveCompanyHot();
        } else {
            $sessionIdList = explode(',', $this->saveData['sessionIds']);
            $sessionCount  = count($sessionIdList);
            foreach ($sessionIdList as $sessionId) {
                $sessionModel              = BaseHwActivitySession::findOne($sessionId);
                $sessionModel->activity_id = $this->activityId;
                $sessionModel->sort        = $sessionCount;
                $sessionModel->save();
                BaseHwActivitySessionArea::updateAll(['activity_id' => $this->activityId],
                    ['session_id' => $sessionId]);
                $sessionCount -= 1;
            }

            // 并且这里有一个情况，需要删除掉哪些不在sessionIds里面的session(使用deleteAll)
            BaseHwActivitySession::deleteAll([
                'and',
                [
                    'not in',
                    'id',
                    $sessionIdList,
                ],
                ['activity_id' => $this->activityId],
            ]);
            //删除关联的场次ID地区
            BaseHwActivitySessionArea::deleteAll([
                'and',
                [
                    'not in',
                    'session_id',
                    $sessionIdList,
                ],
                ['activity_id' => $this->activityId],
            ]);
        }
    }

    /**
     * 保存推广设置
     * @throws \yii\db\Exception
     */
    private function savePromotion()
    {
        BaseHwActivityPromotion::deleteAll(['activity_id' => $this->activityId]);

        if ($this->saveData['promotionList']) {
            foreach ($this->saveData['promotionList'] as $item) {
                if (strtotime($item['startDate']) - strtotime($item['endDate']) > 0) {
                    throw new Exception('生效日期不能大于失效日期');
                }
                $promotionModel                = new BaseHwActivityPromotion();
                $promotionModel->position_type = $item['positionType'];
                $promotionModel->sort          = $item['sort'];
                $promotionModel->start_date    = $item['startDate'];
                $promotionModel->end_date      = $item['endDate'];
                $promotionModel->img_type      = $item['imgType'];
                $promotionModel->activity_id   = $this->activityId;

                $filed = BaseHwActivityPromotion::SHOW_IMG_FILED_RELATE_LIST[$this->activityModel->series_type][$item['imgType']];
                if (!$this->activityModel->$filed) {
                    $imgType = BaseHwActivityPromotion::SHOW_IMG_TYPE_TEXT_LIST[$this->activityModel->series_type][$item['imgType']];
                    throw new Exception($imgType . '图片未上传');
                }

                // 如果开始时间大于今天，status=2
                if (strtotime($item['startDate']) > strtotime(date('Y-m-d'))) {
                    $promotionModel->status = BaseHwActivityPromotion::STATUS_DELETE;
                }

                $promotionModel->img_file_id = $this->activityModel->$filed;
                $promotionModel->save();
            }
        } else {
            // 前端一个都没传过来，这个时候应该是全部delete掉的
            if ($this->activityId) {
                BaseHwActivityPromotion::deleteAll(['activity_id' => $this->activityId]);
            }
        }
    }

    /**
     * 处理热门单位
     */
    private function saveCompanyHot()
    {
        // 进来先删除所有热门单位
        BaseHwActivityCompanyHot::deleteAll(['activity_id' => $this->activityId]);
        if (!$this->saveData['companyHotList']) {
            return true;
        }
        //执行批量写入
        $rows = [];
        foreach ($this->saveData['companyHotList'] as $item) {
            $rows[] = [
                'activity_id' => $this->activityId,
                'company_id'  => $item['companyId'],
                'sort'        => $item['sort'],
                'link_type'   => $item['linkType'],
            ];
        }
        if ($rows) {
            Yii::$app->db->createCommand()
                ->batchInsert(BaseHwActivityCompanyHot::tableName(), [
                    'activity_id',
                    'company_id',
                    'sort',
                    'link_type',
                ], $rows)
                ->execute();
        }
    }

    /**
     * 数据都入表后的操作
     * @return void
     */
    private function afterRun()
    {
        //更新活动状态、时间
        BaseHwActivity::updateActivityBySession($this->activityId);
        if ($this->zhaoPinHuiType == 2) {
            Producer::hwActivityCompanyQueue($this->activityId);
        }
    }
}