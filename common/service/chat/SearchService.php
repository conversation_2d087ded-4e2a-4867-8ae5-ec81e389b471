<?php

namespace common\service\chat;

use common\base\models\BaseChatMessage;
use common\base\models\BaseChatRoom;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyResumeLibrary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobContactSynergy;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeIntention;
use common\base\models\BaseResumeResearchDirection;
use common\base\models\BaseResumeWork;
use common\helpers\MaskHelper;

class SearchService extends BaseService
{
    // 获取聊天室里面,求职者的信息
    public function companyGetRoomResumeInfo($chatId, $companyMemberId)
    {
        $chat = BaseChatRoom::findOne([
            'uuid'              => $chatId,
            'company_member_id' => $companyMemberId,
        ]);

        /**
         * //简历信息
         * $item['avatar'] = BaseMember::getAvatarMask($item['avatar'], $item['gender']);
         * //教育经历的最后三段
         * $last_education_name = '';
         * $last_major_name     = '';
         * $education_list      = BaseResumeEducation::getLastRecord($resumeId,
         * self::LIST_RESUME_EDUCATION_NUMBER);
         * foreach ($education_list as &$education_item) {
         * if ($education_item['id'] == $item['last_education_id']) {
         * $last_education_name = $education_item['education_name'];
         * $last_major_name     = $education_item['major'] ?: $education_item['major_custom'];
         * }
         * unset($education_item['id'], $education_item['major_id'], $education_item['education_id'], $education_item['is_recruitment']);
         * }
         * $item['education_list'] = $education_list;
         * //工作经验的最后一段
         * $item['work_list']      = BaseResumeWork::getLastRecord($resumeId,
         * self::LIST_RESUME_WORK_NUMBER);
         * $identityExperienceText = BaseResume::getIdentityExperienceText($resumeId);
         * //简历基本信息
         * $resume_info_arr = [
         * ($item['age']) . '岁',
         * $last_education_name,
         * $last_major_name,
         * ];
         * if ($identityExperienceText) {
         * $resume_info_arr[] = $identityExperienceText;
         * }
         * $item['user_info'] = implode('·', $resume_info_arr);
         * //活跃标签
         * $item['active_tag'] = BaseMember::getUserActiveTime($item['member_id']);
         * //求职意向-职位类型
         * $item['job_category_name'] = BaseCategoryJob::getName($item['job_category_id']);
         * //获取研究方向
         * $research_info            = BaseResumeResearchDirection::getInfo($resumeId);
         * $item['research_content'] = empty($research_info->content) ? '' : $research_info->content;
         * //简历名称
         * $item['name'] = $item['name'] ? MaskHelper::getName($item['name']) : '';
         * //简历类型便签
         * $item['resume_type_tag']  = BaseResume::getResumeLevel($resumeId);
         * $item['resume_title_tag'] = BaseResume::getUserSpecialInfo($resumeId);
         *
         * //时间格式化
         * $item['update_time'] = (empty($item['update_time']) || $item['update_time'] == '0000-00-00 00:00:00') ? '-' : date('Y/m/d',
         * strtotime($item['update_time']));
         *
         * // unset($item['age'], $item['work_experience'], $item['job_category_id'], $item['member_id'], $item['last_education_id']);
         * unset($item['work_experience'], $item['member_id'], $item['last_education_id']);
         */

        if (!$chat) {
            return [];
        }

        $resumeId    = $chat->resume_id;
        $resumeModel = BaseResume::findOne($resumeId);
        $memberModel = BaseMember::findOne($resumeModel->member_id);

        $returnInfo = [];

        //简历信息
        $returnInfo['avatar'] = BaseChatMessage::getMessageAvatar($resumeModel->member_id, $chat->company_id);
        //教育经历的最后三段
        $last_education_name = '';
        $last_major_name     = '';
        $education_list      = BaseResumeEducation::getLastRecord($resumeId, 3);
        foreach ($education_list as &$education_item) {
            if ($education_item['id'] == $resumeModel->last_education_id) {
                $last_education_name = $education_item['education_name'];
                $last_major_name     = $education_item['major'] ?: $education_item['major_custom'];
            }
            unset($education_item['id'], $education_item['major_id'], $education_item['education_id'], $education_item['is_recruitment']);
        }
        $returnInfo['education_list'] = $education_list;
        //工作经验的最后一段
        $returnInfo['work_list'] = BaseResumeWork::getLastRecord($resumeId, 1);
        $identityExperienceText  = BaseResume::getIdentityExperienceText($resumeId);
        //简历基本信息
        $resume_info_arr = [];
        if ($resumeModel->age) {
            $resume_info_arr[] = $resumeModel->age . '岁';
        }
        if ($last_education_name) {
            $resume_info_arr[] = $last_education_name;
        }
        if ($last_major_name) {
            $resume_info_arr[] = $last_major_name;
        }
        if ($identityExperienceText) {
            $resume_info_arr[] = $identityExperienceText;
        }

        $returnInfo['user_info'] = implode('|', $resume_info_arr);
        //活跃标签
        $returnInfo['active_tag'] = BaseMember::getUserActiveTime($memberModel->id);
        //求职意向-职位类型
        $returnInfo['job_category_name'] = BaseResumeIntention::getAllJobCategoryText($resumeId, '、');
        //获取研究方向
        $research_info                  = BaseResumeResearchDirection::getInfo($resumeId);
        $returnInfo['research_content'] = empty($research_info->content) ? '' : $research_info->content;

        //简历类型便签
        $returnInfo['resume_type_tag']  = BaseResume::getResumeLevel($resumeId);
        $returnInfo['resume_title_tag'] = BaseResume::getUserSpecialInfo($resumeId);

        //时间格式化
        $returnInfo['update_time'] = (empty($resumeModel->update_time) || $resumeModel->update_time == '0000-00-00 00:00:00') ? '-' : date('Y/m/d',
            strtotime($resumeModel->update_time));

        unset($returnInfo['work_experience'], $returnInfo['member_id'], $returnInfo['last_education_id']);

        // 这里看一下求职者的简历完善度有没有
        $returnInfo['complete'] = $resumeModel->complete;
        $returnInfo['gender']   = $resumeModel->gender;
        if ($resumeModel->complete >= \Yii::$app->params['completeResumePercent']) {
            $returnInfo['isComplete'] = true;
        } else {
            $returnInfo['isComplete'] = false;
        }

        $jobId                    = $chat->current_job_id;
        $returnInfo['isDelivery'] = BaseJobApplyRecord::checkJobApplyStatus($resumeId,
            $jobId) == BaseJob::JOB_APPLY_STATUS_YES ? true : false;

        // 这里需要加上url给前端跳转
        // 应聘管理 member/company/resume/detail/465
        // 简历库  member/company/resume/library/detail/43
        // 人才库 member/company/talent/detail/146
        $isMyJob                 = false;
        $returnInfo['resumeUrl'] = BaseChatRoom::getResumeUrl($chat->id);

        $companyMemberInfoModel = BaseCompanyMemberInfo::findOne(['member_id' => $companyMemberId]);

        if ($companyMemberInfoModel->id) {
            // 找协同
            if (BaseJobContactSynergy::findIsExist([
                'company_member_info_id' => $companyMemberInfoModel->id,
                'job_id'                 => $jobId,
            ])) {
                $isMyJob = true;
            }
        }

        if (!$isMyJob) {
            // 找是否在简历库
            $companyId = $chat->company_id;
            if (BaseCompanyResumeLibrary::findIsExist([
                'resume_id'  => $resumeId,
                'company_id' => $companyId,
            ])) {
                $returnInfo['name'] = $resumeModel->name ?: '';
            } else {
                //简历名称
                $returnInfo['name'] = $resumeModel->name ? MaskHelper::getName($resumeModel->name) : '';
            }
        }
        $returnInfo['resumeId']     = $resumeId;
        $returnInfo['memberStatus'] = $memberModel->status;

        return $returnInfo;
    }
}
