<?php

namespace common\service\specialNeedService;

use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseSpecialNeedApplyLimit;
use common\components\MessageException;
use common\libs\WxWork;

/**
 * 投递特殊限制服务
 * 重构后支持数据库配置，向后兼容原有硬编码逻辑
 */
class JobApplyService extends BaseService
{
    public function check($jobId, $companyId, $announcementId, $resumeId)
    {
        // 优先使用新的配置系统 暂时关闭逻辑
        // try {
        //     BaseSpecialNeedApplyLimit::checkApplyLimit($jobId, $companyId, $announcementId, $resumeId);
        // } catch (MessageException $e) {
        //     // 新系统抛出的异常直接向上传递
        //     throw $e;
        // }

        // 向后兼容：如果新系统未启用或无配置，使用原有逻辑
        if (!$this->isOpen) {
            return true;
        }

        // http://zentao.jugaocai.com/index.php?m=story&f=view&id=775&version=0&param=0&storyType=story
        if ($companyId == 89616) {
            // 同一人才，15天内（含投递日） 只能对单位---中国科学院金属研究所（ID:20089616），进行1次站内投递。若投递次数超限，则用户后续发起站内投递后，在发起投递的页面toast提示3s：
            if (BaseJobApplyRecord::find()
                    ->where([
                        'resume_id'  => $resumeId,
                        'company_id' => $companyId,
                    ])
                    ->andWhere([
                        '>=',
                        'add_time',
                        date('Y-m-d H:i:s', strtotime('-15 days')),
                    ])
                    ->count() > 0) {
                $this->notice($companyId, $resumeId);
                throw new MessageException('您已超出该单位简历投递次数限制，无法再行投递');
            }
        }

        // http://zentao.jugaocai.com/index.php?m=story&f=view&id=841
        // companyId = 4 或者 94557
        if ($companyId == 4 || $companyId == 94557) {
            // 同一人才，15天内（含投递日） 只能对单位---中国科学院金属研究所（ID:20089616），进行1次站内投递。若投递次数超限，则用户后续发起站内投递后，在发起投递的页面toast提示3s：
            if (BaseJobApplyRecord::find()
                    ->where([
                        'resume_id'  => $resumeId,
                        'company_id' => $companyId,
                    ])
                    ->andWhere([
                        '>=',
                        'add_time',
                        date('Y-m-d H:i:s', strtotime('-15 days')),
                    ])
                    ->count() > 0) {
                $this->notice($companyId, $resumeId);
                throw new MessageException('您已超出该单位简历投递次数限制，无法再行投递');
            }
        }

        // http://zentao.jugaocai.com/index.php?m=story&f=view&id=853
        // companyId = 4 或者 94557
        if ($companyId == 98503) {
            // 同一人才，15天内（含投递日） 只能对单位---中国科学院金属研究所（ID:20089616），进行1次站内投递。若投递次数超限，则用户后续发起站内投递后，在发起投递的页面toast提示3s：
            if (BaseJobApplyRecord::find()
                    ->where([
                        'resume_id'  => $resumeId,
                        'company_id' => $companyId,
                    ])
                    ->andWhere([
                        '>=',
                        'add_time',
                        date('Y-m-d H:i:s', strtotime('-15 days')),
                    ])
                    ->count() > 0) {
                $this->notice($companyId, $resumeId);
                throw new MessageException('您已超出该单位简历投递次数限制，无法再行投递');
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&id=775&version=0&param=0&storyType=story
        // companyId = 80360
        if ($companyId == 80360) {
            // 同一人才，15天内（含投递日） 只能对单位---中国科学院金属研究所（ID:20089616），进行1次站内投递。若投递次数超限，则用户后续发起站内投递后，在发起投递的页面toast提示3s：
            if (BaseJobApplyRecord::find()
                    ->where([
                        'resume_id'  => $resumeId,
                        'company_id' => $companyId,
                    ])
                    ->count() > 0) {
                $this->notice($companyId, $resumeId);
                throw new MessageException('您已超出该单位简历投递次数限制，无法再行投递');
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&id=1177
        // companyId = 60
        if ($companyId == 60) {
            // “海外经历=是”的人才不可投递投递“四川工商职业技术学院”（ID：20000060）下的任意职位，该批用户投递该单位下职位时，在发起投递的页面toast提示：对不起，您暂不符合招聘要求，建议尝试其他机会！

            $isAbroad = BaseResumeEducation::find()
                ->andWhere([
                    'resume_id' => $resumeId,
                    'status'    => BaseResumeEducation::STATUS_ACTIVE,
                    'is_abroad' => BaseResumeEducation::IS_ABROAD_YES,
                ])
                ->asArray()
                ->exists();
            if ($isAbroad) {
                $this->notice($companyId, $resumeId);
                throw new MessageException('对不起，您暂不符合招聘要求，建议尝试其他机会！');
            }
        }

        /**
         * 同一人才，180天内最多只能对单位---四川工程职业技术大学（ID：20096240）进行 3 次站内投递。若投递次数超限，则用户后续发起站内投递后，在发起投递的页面toast提示3s：
         *
         * “您已超出该单位简历投递次数限制，无法再行投递”
         *
         * https://zentao.jugaocai.com/index.php?m=story&f=view&id=1227
         */

        if ($companyId == 96240) {
            // 同一人才，180天内最多只能对单位---四川工程职业技术大学（ID：20096240）进行 3 次站内投递。若投递次数超限，则用户后续发起站内投递后，在发起投递的页面toast提示3s：
            if (BaseJobApplyRecord::find()
                    ->where([
                        'resume_id'  => $resumeId,
                        'company_id' => $companyId,
                    ])
                    ->andWhere([
                        '>=',
                        'add_time',
                        date('Y-m-d H:i:s', strtotime('-180 days')),
                    ])
                    ->count() > 3) {
                $this->notice($companyId, $resumeId);
                throw new MessageException('您已超出该单位简历投递次数限制，无法再行投递');
            }
        }

        return true;
    }

    public function notice($companyId, $resumeId)
    {
        return true;
        $content = '（研发监控内容，可无视）单位id:' . $companyId . '，简历id：' . $resumeId . '，触发特殊投递限制！';
        $wxWork  = WxWork::getInstance();
        $wxWork->robotJobApplyWarring($content);
    }

}
