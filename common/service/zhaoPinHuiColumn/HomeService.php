<?php
/**
 * create user：shannon
 * create time：2025/3/3 上午8:53
 */
namespace common\service\zhaoPinHuiColumn;

use common\base\models\BaseActivityForm;
use common\base\models\BaseActivityFormOptionSign;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseCompany;
use common\base\models\BaseHwActivity;
use common\base\models\BaseHwActivityCompany;
use common\base\models\BaseHwActivityFeatureTagRelation;
use common\base\models\BaseHwActivityPromotion;
use common\base\models\BaseHwActivitySession;
use common\base\models\BaseHwActivitySessionArea;
use common\base\models\BaseHwSpecialActivity;
use common\base\models\BaseNews;
use common\base\models\BaseShowcase;
use common\components\MessageException;
use common\helpers\DebugHelper;
use common\helpers\FileHelper;
use common\helpers\MiniHelper;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\libs\Cache;
use Da\QrCode\Format\vCardFormat;
use Yii;
use yii\db\Expression;

class HomeService extends BaseService
{
    private $params;

    //=====================================================================
    //======================最新活动--筛选项=================================
    //======================最新活动--筛选项=================================
    //=====================================================================
    /** 活动类型 */
    const ACTIVITY_TYPE = [
        [
            'id'   => BaseHwActivity::TYPE_ZHAOPINHUI_OFFLINE_NATIONAL_TOUR,
            'name' => BaseHwActivity::TYPE_ZHAOPINHUI_OFFLINE_NATIONAL_TOUR_NAME,
        ],
        [
            'id'   => BaseHwActivity::TYPE_ZHAOPINHUI_ONLINE_RPO,
            'name' => BaseHwActivity::TYPE_ZHAOPINHUI_ONLINE_RPO_NAME,
        ],
        [
            'id'   => BaseHwActivity::TYPE_ZHAOPINHUI_OFFLINE_RPO_PROJECT,
            'name' => BaseHwActivity::TYPE_ZHAOPINHUI_OFFLINE_RPO_PROJECT_NAME,
        ],
        [
            'id'   => BaseHwActivity::TYPE_ZHAOPINHUI_OFFLINE_GROUP_SPECIAL,
            'name' => BaseHwActivity::TYPE_ZHAOPINHUI_OFFLINE_GROUP_SPECIAL_NAME,
        ],
        [
            'id'   => BaseHwActivity::TYPE_CHUHAI_OVERSEAS_SESSION,
            'name' => BaseHwActivity::TYPE_CHUHAI_OVERSEAS_SESSION_NAME,
        ],
        [
            'id'   => BaseHwActivity::TYPE_GUIGUO_SCHOLARS_FORUM,
            'name' => BaseHwActivity::TYPE_GUIGUO_SCHOLARS_FORUM_NAME,
        ],
        [
            'id'   => BaseHwActivity::TYPE_GUIGUO_STUDENTS_RETURNED_HOME,
            'name' => BaseHwActivity::TYPE_GUIGUO_STUDENTS_RETURNED_HOME_NAME,
        ],
        [
            'id'   => BaseHwActivity::TYPE_GUIGUO_TALENT_CONFERENCE,
            'name' => BaseHwActivity::TYPE_GUIGUO_TALENT_CONFERENCE_NAME,
        ],
        [
            'id'   => BaseHwActivity::TYPE_ZHAOPINHUI_ONLINE_MERITOCRACY,
            'name' => BaseHwActivity::TYPE_ZHAOPINHUI_ONLINE_MERITOCRACY_NAME,
        ],
        [
            'id'   => BaseHwActivity::TYPE_ZHAOPINHUI_ONLINE_ZPH,
            'name' => BaseHwActivity::TYPE_ZHAOPINHUI_ONLINE_ZPH_NAME,
        ],
    ];

    /** 热门地区 */
    //重庆、成都、西安、哈尔滨、长春、北京、济南、南京、上海、杭州、武汉、长沙、广州、深圳、天津、合肥、厦门、香港、澳门、新加坡、英国、瑞典、瑞士、日本、韩国、德国、法国、澳大利亚、新西兰、荷兰、俄罗斯、美国
    //2324,2368,2899,656,586,2,1376,821,802,934,1710,1828,1965,1988,20,1047,1183,3716,3738,3975,3940,3951,3948,3886,3887,3947,3944,3964,3965,3942,3963,3924,
    //北京、天津、哈尔滨、长春、大连（新增）、济南、南京、上海、杭州、合肥、厦门、武汉、长沙、广州、深圳、重庆、成都、西安、香港、澳门、日本、韩国、新加坡、美国、英国、荷兰、法国、德国、瑞士、瑞典、俄罗斯、澳大利亚、新西兰
    //2,20,656,586,481,1376,821,802,934,1047,1183, 1710,1828, 1965,1988,2324,2368,2899,3716,3738,3886,3887,3965,3924,3940,3942,3944,3947,3948,3951,3963,3964,3965
    const ACTIVITY_AREA = [
        2,
        20,
        656,
        586,
        481,
        1376,
        821,
        802,
        934,
        1047,
        1183,
        1710,
        1828,
        1965,
        1988,
        2324,
        2368,
        2899,
        3716,
        3738,
        3886,
        3887,
        3965,
        3924,
        3940,
        3942,
        3944,
        3947,
        3948,
        3951,
        3963,
        3964,
        3965,
    ];

    /** 活动时间 */
    const ACTIVITY_TIME_OVER_THREE_MONTH = 1;
    const ACTIVITY_TIME_THIS_MONTH       = 2;
    const ACTIVITY_TIME_NEXT_THREE_MONTH = 3;
    const ACTIVITY_TIME_THIS_YEAR        = 4;
    const ACTIVITY_TIME_CUSTOM           = 5;
    const ACTIVITY_TIME                  = [
        [
            'name' => '接下来三个月',
            'id'   => self::ACTIVITY_TIME_NEXT_THREE_MONTH,
        ],
        [
            'name' => '本月',
            'id'   => self::ACTIVITY_TIME_THIS_MONTH,
        ],
        [
            'name' => '过去3个月',
            'id'   => self::ACTIVITY_TIME_OVER_THREE_MONTH,
        ],
        [
            'name' => '本年度',
            'id'   => self::ACTIVITY_TIME_THIS_YEAR,
        ],
    ];

    /** 活动状态 */
    // 不限 待举办 进行中 往期回顾
    const ACTIVITY_STATUS_WAIT     = 100001;
    const ACTIVITY_STATUS_PROGRESS = 100002;
    const ACTIVITY_STATUS_HISTORY  = 100003;
    const ACTIVITY_STATUS          = [
        [
            'name' => '待举办',
            'id'   => self::ACTIVITY_STATUS_WAIT,
        ],
        [
            'name' => '进行中',
            'id'   => self::ACTIVITY_STATUS_PROGRESS,
        ],
        [
            'name' => '往期回顾',
            'id'   => self::ACTIVITY_STATUS_HISTORY,
        ],
    ];
    /** 活动关系 */
    const ACTIVITY_STATUS_RELATION = [
        self::ACTIVITY_STATUS_WAIT     => [
            BaseHwActivity::ACTIVITY_CHILD_STATUS_SIGN_UP,
            BaseHwActivity::ACTIVITY_CHILD_STATUS_NOT_START,
            BaseHwActivity::ACTIVITY_CHILD_STATUS_BE_ABOUT_TO_START,
        ],
        self::ACTIVITY_STATUS_PROGRESS => [BaseHwActivity::ACTIVITY_CHILD_STATUS_PROGRESS],
        self::ACTIVITY_STATUS_HISTORY  => [BaseHwActivity::ACTIVITY_CHILD_STATUS_END],

    ];

    /** 举办方式 */
    const ACTIVITY_TO_HOLD = [
        [
            'name' => BaseHwActivity::TO_HOLD_TYPE_OFFLINE_NAME,
            'id'   => BaseHwActivity::TO_HOLD_TYPE_OFFLINE,
        ],
        [
            'name' => BaseHwActivity::TO_HOLD_TYPE_ONLINE_NAME,
            'id'   => BaseHwActivity::TO_HOLD_TYPE_ONLINE,
        ],
    ];

    const KEY_AREA_ID          = 'areaId';
    const KEY_ACTIVITY_STATUS  = 'activityStatus';
    const KEY_FEATURE_TAG      = 'featureTag';
    const KEY_ACTIVITY_TYPE    = 'activityType';
    const KEY_ACTIVITY_TIME    = 'activityTime';
    const KEY_ACTIVITY_TO_HOLD = 'activityToHold';

    //======================最新活动--筛选项=================================
    //===========================结束=======================================
    //=====================================================================

    const COMMON_ITEM = [
        'name' => '不限',
        'id'   => '',
    ];

    private $uri = '/yincai';
    private $localhost;
    private $pathInfo;
    private $seo;

    private function handleParams()
    {
        $this->setUser();
        $host            = Yii::$app->request->hostInfo;
        $this->localhost = $host . $this->uri;
        $pathInfo        = Yii::$app->request->pathInfo;
        // $pathInfo 中含有/yincaipage  则替换/yincai
        if (strpos($pathInfo, 'yincaipage') !== false) {
            $pathInfo = str_replace('yincaipage', 'yincai', $pathInfo);
        }
        $this->pathInfo = $host . ($pathInfo != '/' ? '/' . $pathInfo : $this->uri);
        $this->isCache  = Yii::$app->request->get('isCache');
        //判断是都areaKey
        if (isset($this->params['areaKey']) && $this->params['areaKey']) {
            $areaInfo               = BaseArea::findOne(['spell' => $this->params['areaKey']]);
            $this->params['areaId'] = $areaInfo ? $areaInfo->id : 0;
            if (!$this->params['areaId']) {
                $this->redirect404();
            }
            $seo = Yii::$app->params['tdk']['homeArea'];
            //先转成字符串 替换{n} => $areaInfo->name
            $this->seo = json_decode(str_replace('{n}', $areaInfo->name, json_encode($seo)), true);
        } else {
            $this->seo = Yii::$app->params['tdk']['home'];
        }
    }

    /**
     * 执行程序
     * @param $params
     * @return array
     */
    public function run($params)
    {
        if (!$this->operationPlatform) {
            throw new MessageException('您忘记设置调用端口啦！');
        }
        $this->params = $params;
        $this->handleParams();
        // 1、招聘会_HF
        $showcaseHF = $this->getShowcaseHF();
        // 2、热门场次
        $activityHot = $this->getActivityHot();
        // 3.1 最新活动--特色专场-广告位
        $showcaseTSZC = $this->getShowcaseTSZC();
        // 3.2 最新活动--筛选项
        $activitySearch = $this->getActivitySearch();
        // 3.3 最新活动--列表
        $activityList = $this->getActivityList($this->params);
        //返回
        $result = [
            'showcaseHF'     => $showcaseHF,
            'activityHot'    => $activityHot,
            'showcaseTSZC'   => $showcaseTSZC,
            'activitySearch' => $activitySearch,
            'activityList'   => $activityList,
        ];
        if ($this->operationPlatform != self::PLATFORM_MINI) {
            // 4、活动快讯
            $activityNews             = $this->getActivityNews();
            $result['activityNews']   = $activityNews;
            $result['seo']            = $this->seo;
            $result['isPositionBool'] = !empty($this->params) ? 1 : 2;
            // 5、数据统计
            $result['statisticalData'] = $this->getStatisticalData();
            // 6、 招聘会_合作案例
            $result['showcaseHZAL'] = $this->getShowcaseHZAL();
        }

        return $result;
    }

    /**
     * 列表获取
     * @param $params
     * @return array
     * @throws MessageException
     */
    public function runList($params)
    {
        if (!$this->operationPlatform) {
            throw new MessageException('您忘记设置调用端口啦！');
        }
        $this->params = $params;
        $this->handleParams();

        return $this->getActivityList($this->params);
    }

    /**
     * 列表获取
     * @param $params
     * @return array
     * @throws MessageException
     */
    public function runPcList($params)
    {
        if (!$this->operationPlatform) {
            throw new MessageException('您忘记设置调用端口啦！');
        }
        $this->params = $params;
        $this->handleParams();
        $activitySearch = $this->getActivitySearch();
        $activityList   = $this->getActivityList($this->params);

        return [
            'activitySearch' => $activitySearch,
            'activityList'   => $activityList,
        ];
    }

    /**
     * 首页广告位-招聘会_HF
     * zhaopinhui_HF/mini_zhaopinhui_HF
     * @return array
     */
    private function getShowcaseHF()
    {
        $key = $this->operationPlatform == self::PLATFORM_MINI ? 'mini_zhaopinhui_HF' : 'zhaopinhui_HF';

        return BaseShowcase::getByKey($key);
    }

    /**
     * 首页广告位-招聘会_特色专场
     * zhaopinhui_tesezhuanchang/mini_zhaopinhui_tesezhuanchang
     * @return array
     */
    private function getShowcaseTSZC()
    {
        $key = $this->operationPlatform == self::PLATFORM_MINI ? 'mini_zhaopinhui_tesezhuanchang' : 'zhaopinhui_tesezhuanchang';

        return BaseShowcase::getByKey($key);
    }

    /**
     * 首页广告位-招聘会_合作案例
     * zhaopinhui_hezuoanli
     * @return array
     */
    private function getShowcaseHZAL()
    {
        return BaseShowcase::getByKey('zhaopinhui_hezuoanli');
    }

    /**
     * 热门场次
     * 活动调用，通过【运营端->高才海外->活动管理->新增/编辑活动->“推广设置”】中配置：
     * 1、展示勾选了“PC活动汇总页-热门场次”推广位、且在推广时间内的活动，不限数量；
     * 若无符合条件的活动，则整个模块隐藏；（PS：活动上下架状态不影响该位置的活动调用）
     * 2、排序：
     * （1）按当前活动在“PC活动汇总页-热门场次”推广位中的“推广排序”倒序排列；
     * （2）其次按活动ID倒序排；
     * （3）一行展示N个广告（具体以UI稿为准）。
     *      广告数量超出一行时，展示“下一页”翻页按钮；翻至下一页时，左侧展示“上一页”翻页按钮。
     *      翻页按钮&交互可参考【归国圈->活动专区模块】：https://www.guiguoquan.com
     * 3、字段说明：见卡片标注说明；
     * 4、移入交互&点击热区说明：
     * （1）移入交互：鼠标移入卡片时，显示移入效果（描边+弹起+图片放大）；展示“立即报名”按钮，覆盖举办地点字段。
     * （2）热区①-整张卡片：点击整个热区，新页面打开活动详情页（招聘会系列活动）/活动详情链接（非招聘会系列活动）；
     * （3）热区②-操作按钮：详见按钮标注说明
     */
    private function getActivityHot()
    {
        $list = Cache::get(Cache::ZHAOPINHUI_HOME_ACTIVITY_HOT);
        if ($list && !$this->isCache) {
            $list = json_decode($list, true);
        } else {
            // 1、获取当前时间
            $nowDate = date('Y-m-d');
            $list    = BaseHwActivityPromotion::find()
                ->alias('hap')
                ->select([
                    'ha.series_type as seriesType',
                    'ha.type',
                    'ha.click',
                    'ha.detail_url as detailUrl',
                    'ha.sign_up_url as signUpUrl',
                    'ha.sign_end_date as signEndDate',
                    'ha.is_outside_url as isOutsideUrl',
                    'ha.activity_link as activityLink',
                    'ha.activity_child_status as activityChildStatus',
                    'ha.name',
                    'ha.apply_link_person_type as applyLinkPersonType',
                    'ha.apply_link_person_form_id as applyLinkPersonFormId',
                    'ha.apply_link_person_form_option_id as applyLinkPersonFormOptionId',
                    'ha.apply_link_person as applyLinkPerson',
                    'ha.apply_person_time as applyPersonTime',
                    'ha.wonderful_review as wonderfulReview',
                    'hap.activity_id as activityId',
                    'hap.img_file_id as imgFileId',
                ])
                ->innerJoin(['ha' => BaseHwActivity::tableName()], 'hap.activity_id=ha.id')
                ->where([
                    'hap.status'        => BaseHwActivityPromotion::STATUS_ONLINE,
                    'hap.position_type' => $this->operationPlatform != self::PLATFORM_MINI ? BaseHwActivityPromotion::PROMOTION_POSITION_PC_ACTIVITY_PAGE_HOT : BaseHwActivityPromotion::PROMOTION_POSITION_MINI_ACTIVITY_PAGE_HOT,
                ])
                ->andWhere([
                    'and',
                    [
                        '<=',
                        'hap.start_date',
                        $nowDate,
                    ],
                    [
                        '>=',
                        'hap.end_date',
                        $nowDate,
                    ],
                ])
                ->orderBy('hap.sort desc, hap.activity_id desc')
                ->asArray()
                ->all();

            Cache::set(Cache::ZHAOPINHUI_HOME_ACTIVITY_HOT, json_encode($list), 3600);
        }
        foreach ($list as &$item) {
            $item['isZhaopinhui']             = in_array($item['seriesType'], BaseHwActivity::ZHAOPINHUI_TYPE);
            $item['activityPcUrl']            = $item['isZhaopinhui'] ? BaseHwActivity::getActivityLinkUrl($item['seriesType'],
                $item['activityLink']) : $item['detailUrl'];
            $item['activityPcTargetLinkType'] = 0;
            if ($item['isZhaopinhui']) {
                $item['activityUrl']            = BaseShowcase::urlToMiniRouter(BaseShowcase::PAGE_LINK_TYPE_ACTIVITY_DETAIL,
                    $item['activityId']);
                $item['activityTargetLinkType'] = BaseShowcase::TARGET_LINK_TYPE_STATION;
                $applyPersonTime                = $item['applyPersonTime'];
            } else {
                if (!$item['detailUrl']) {
                    $item['activityUrl']            = '';
                    $item['activityTargetLinkType'] = -1;
                } else {
                    $urlData                        = MiniHelper::getUrlType($item['detailUrl']);
                    $item['activityUrl']            = $urlData['pageType'] > 0 ? BaseShowcase::urlToMiniRouter($urlData['pageType'],
                        $urlData['url']) : $urlData['url'];
                    $item['activityTargetLinkType'] = $urlData['targetType'];
                }
                $applyPersonTime = $item['signEndDate'];
            }
            if ($item['isOutsideUrl'] == BaseHwActivity::IS_OUTSIDE_URL_YES) {
                $item['rel'] = 'nofollow';
            } else {
                $item['rel'] = '';
            }
            $item['activityTime'] = BaseHwActivity::getActivityDate($item['activityId'], 4, false);
            $isWonderfulReview    = (bool)$item['wonderfulReview'];
            $item['applyStatus']  = 2;
            if ($item['applyLinkPersonType'] != BaseHwSpecialActivity::APPLY_LINK_PERSON_TYPE_OTHER && !empty($this->resumeId)) {
                $item['applyStatus'] = BaseActivityFormOptionSign::getResumeActivityApplyStatus($this->resumeId,
                    $item['applyLinkPersonFormId'], $item['applyLinkPersonFormOptionId']);
            }

            $activityBtnInfo                 = $this->getBtnTextAndLink($item['activityId'], $item['seriesType'],
                $item['activityChildStatus'], $item['detailUrl'], $item['signUpUrl'], $item['activityLink'],
                $isWonderfulReview, $item['applyStatus'], $applyPersonTime);
            $item['btnText']                 = $activityBtnInfo['btnText'];
            $item['btnLink']                 = $activityBtnInfo['btnLink'];
            $item['btnType']                 = $activityBtnInfo['btnType'];
            $item['btnIsClick']              = $activityBtnInfo['btnIsClick'];
            $item['btnTargetLinkType']       = $activityBtnInfo['btnTargetLinkType'];
            $item['activityChildStatusText'] = BaseHwActivity::ACTIVITY_CHILD_STATUS_TEXT_LIST[$item['seriesType']][$item['activityChildStatus']];
            $item['typeText']                = BaseHwActivity::TYPE_TEXT_LIST[$item['type']];
            $item['area']                    = BaseHwActivity::getAddressBySeries($item['activityId']);
            $item['image']                   = FileHelper::getFullPathById($item['imgFileId']);
            if (in_array($item['seriesType'], BaseHwActivity::ZHAOPINHUI_TYPE)) {
                //若数值＜1w，则四舍五入取整后展示具体数据；若数值≥1w，则以w为单位取值，四舍五入，最多展示到小数点后两位
                $click         = $item['click'] * 26.1;
                $item['click'] = ($click < 10000 ? round($click) : round($click / 10000,
                            2) . 'w') . ($this->operationPlatform == self::PLATFORM_MINI ? '人围观' : '');
            } else {
                if ($item['seriesType'] == BaseHwActivity::SERIES_OVERSEAS_RECRUITMENT && $this->operationPlatform != self::PLATFORM_MINI) {
                    $names         = BaseHwActivitySession::find()
                        ->where(['activity_id' => $item['activityId']])
                        ->select('name')
                        ->orderBy('sort desc,id desc')
                        ->column();
                    $item['click'] = implode(',', $names);
                } else {
                    $item['click'] = '火热报名中';
                }
            }
        }

        return $list;
    }

    /**
     * 活动快讯
     * 1、调用资讯属性勾选了“招聘会-活动快讯”、且审核通过的资讯；
     * 无相关资讯时，整个模块隐藏不展示；页面自适应展示。
     * 2、字段说明&排序&分页：
     * （1）发布时间最新的1条资讯，展示在左侧：
     * ① 展示发布时间、资讯标题（超2行…）、资讯摘要（超1行…）、正文头图；
     * ② 点击整张卡片，若该资讯有配置跳转网址，则新页面打开网址；若未配置，则新页面跳转【资讯详情页】；
     * （2）其余资讯，按发布时间倒序，展示在右侧列表，3条/页，最多3页，支持左右滑动切页；
     * ① 展示发布时间、资讯标题（超1行…）、资讯摘要（超2行…）、正文头图；
     * ② 鼠标移入单张卡片，资讯标题显示移入效果（见UI）；
     * ③ 点击单张卡片，若该资讯有配置跳转网址，则新页面打开网址；若未配置，则新页面跳转【资讯详情页】
     */
    private function getActivityNews()
    {
        $data = Cache::get(Cache::ZHAOPINHUI_HOME_ACTIVITY_NEWS);
        if ($data && !$this->isCache) {
            return json_decode($data, true);
        }
        $list           = BaseNews::find()
            ->alias('n')
            ->select([
                'n.id as newsId',
                'art.id as articleId',
                'art.title',
                'art.seo_description as seoDescription',
                'art.cover_thumb as coverThumb',
                'art.refresh_date as refreshDate',
                'art.link_url as linkUrl',
            ])
            ->leftJoin(['art' => BaseArticle::tableName()], 'n.article_id=art.id')
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'aa.article_id=art.id')
            ->where(['aa.type' => BaseArticleAttribute::ATT_HAIYOU_ACTIVITY])
            ->orderBy('art.refresh_time desc,n.id')
            ->limit(10)
            ->asArray()
            ->all();
        $listLeft       = [];
        $listRightTotal = [];
        foreach ($list as $key => &$item) {
            $item['refreshDate'] = TimeHelper::formatDateByYear($item['refreshDate'], '.');
            $item['linkUrl']     = $item['linkUrl'] ?: UrlHelper::createPcNewsDetailPath($item['newsId']);
            if ($key == 0) {
                $listLeft = $item;
            } else {
                $listRightTotal[] = $item;
            }
        }
        $listRightCount = count($listRightTotal);
        if ($listRightCount > 0) {
            $listRight[] = array_slice($listRightTotal, 0, 3);
        }
        if ($listRightCount > 3) {
            $listRight[] = array_slice($listRightTotal, 3, 3);
        }
        if ($listRightCount > 6) {
            $listRight[] = array_slice($listRightTotal, 6, 3);
        }

        $data = [
            'listLeft'  => $listLeft,
            'listRight' => $listRight ?? [],
        ];
        Cache::set(Cache::ZHAOPINHUI_HOME_ACTIVITY_NEWS, json_encode($data), 3600);

        return $data;
    }

    /**
     * 最新活动-筛选项
     */
    private function getActivitySearch()
    {
        $result = [];
        //对地区进行处理
        $result['areaId']       = $this->getArea();
        $result['activityType'] = self::ACTIVITY_TYPE;
        $result['featureTag']   = [];
        foreach (BaseHwActivityFeatureTagRelation::FEATURE_TAG_TEXT_LIST as $k => $v) {
            $result['featureTag'][] = [
                'id'   => $k,
                'name' => $v,
            ];
        };
        $result['activityStatus'] = self::ACTIVITY_STATUS;
        $result['activityToHold'] = self::ACTIVITY_TO_HOLD;
        if ($this->operationPlatform != self::PLATFORM_MINI) {
            $result['activityTime'] = self::ACTIVITY_TIME;
            foreach ($result as &$item) {
                array_unshift($item, self::COMMON_ITEM);
            }
            $result = $this->formatSearchSeo($result);
        } else {
            $commonItem         = self::COMMON_ITEM;
            $commonItem['name'] = '全部';
            array_unshift($result['areaId'], $commonItem);
            array_unshift($result['activityType'], $commonItem);
            array_unshift($result['activityToHold'], $commonItem);
            array_unshift($result['featureTag'], $commonItem);
            array_unshift($result['activityStatus'], $commonItem);
        }
        $result['searchParams'] = $this->params;

        //检验参数
        if ($this->operationPlatform != self::PLATFORM_MINI && $this->params) {
            foreach ($this->params as $key => $i) {
                if (($key == 'activityTime' && $i != 5) && isset($result[$key]) && !in_array($i,
                        array_column($result[$key], 'id'))) {
                    $this->redirect404();
                }
            }
        }

        return $result;
    }

    /**
     * 最新活动-筛选条件seo处理
     */
    private function formatSearchSeo($result)
    {
        $paramSeo = $this->params;
        unset($paramSeo['areaKey'], $paramSeo['page'], $paramSeo['pageSize']);
        //判断参数里面是否含有地区参数
        if (isset($paramSeo[self::KEY_AREA_ID]) && $paramSeo[self::KEY_AREA_ID]) {
            //这时候的host
            $hostUnArea = $this->pathInfo;
        } else {
            $hostUnArea = $this->localhost;
        }
        foreach ($result as $key => &$value) {
            $currentParam = $this->params[$key] ?? 0;
            if ($key == self::KEY_AREA_ID) {
                foreach ($value as &$item) {
                    $newParam = $paramSeo;
                    unset($newParam[$key]);
                    if ($item['id'] == $currentParam) {
                        $item['active'] = true;
                    } else {
                        $item['active'] = false;
                    }
                    $item['url'] = $this->localhost . ((!empty($item['spell']) && $item['id'] != $currentParam) ? '/' . $item['spell'] : '') . ($newParam ? '?' . http_build_query($newParam) : '');
                }
            } else {
                foreach ($value as &$item) {
                    $newParam = $paramSeo;
                    unset($newParam[$key]);
                    unset($newParam[self::KEY_AREA_ID]);
                    if ($key == self::KEY_ACTIVITY_TIME && $currentParam == 5) {
                        unset($newParam['activityCustomTime']);
                    }
                    if (!empty($item['id']) && $item['id'] != $currentParam) {
                        $newParam[$key] = $item['id'];
                    }
                    if ($item['id'] == $currentParam) {
                        $item['active'] = true;
                    } else {
                        $item['active'] = false;
                    }

                    $item['url'] = $hostUnArea . ($newParam ? '?' . http_build_query($newParam) : '');
                }
            }
        }

        return $result;
    }

    /**
     * 最新活动-列表
     *  调用 国内线下招聘会&线上引才活动&出海引才&归国活动系列、已上架状态的所有活动;1、字段说明:见卡片标注说明;
     *  2、移入交互&点击热区说明:
     *  (1)移入交互:标移入整张活动卡片，显示移入效果(见U);(2)热区①活动名称+活动主图:点击整个热区，点击，新页面打开活动详情页(招聘会系列活动)/活动详情链接(非招聘会系列活动);
     *  鼠标移入“活动名称”时，显示移入效果;
     *  (3)热区②-操作按钮:详见按钮标注说明。
     *  (4)热区③-活动地点/活动场次:
     *  鼠标移入整个热区，气泡展示该完整地点信息/场次信息(场次信息气泡复用【高才海外->出海引才】)
     *  3、排序规则:
     *  (1)优先按活动“排序"字段倒序，数字越大排越前;
     *  (2)相同排序号，按活动子状态排序:即将开始>报名中>进行中&正在进行>待举办>已结束，活动状态相同:① 先排有具体活动日期的，按活动开始日期倒序排列，其次按活动ID倒序排列;(②)
     *  最后排“活动日期”为自定义文本的，按活动ID倒序排列。
     *  4、分页:12个/页，翻页查看更多;翻页后，页面滚动至【最新活动】模块。
     *  5、无数据时，页面提示:
     */
    private function getActivityList($searchParams)
    {
        $query = BaseHwActivity::find()
            ->alias('ha')
            ->leftJoin(['hasa' => BaseHwActivitySessionArea::tableName()], 'hasa.activity_id=ha.id')
            ->leftJoin(['haftr' => BaseHwActivityFeatureTagRelation::tableName()], 'haftr.activity_id=ha.id')
            ->where([
                'ha.series_type'      => [
                    BaseHwActivity::SERIES_COME_HOME,
                    BaseHwActivity::SERIES_OVERSEAS_RECRUITMENT,
                    BaseHwActivity::SERIES_ZHAOPINHUI_OFFLINE,
                    BaseHwActivity::SERIES_ZHAOPINHUI_ONLINE,
                ],
                'ha.grounding_status' => BaseHwActivity::GROUNDING_STATUS_ON,
            ])
            ->groupBy('ha.id');

        //地区搜索
        if ($searchParams['areaId']) {
            $childAreaIds = BaseArea::find()
                ->select('id')
                ->where(['parent_id' => $searchParams['areaId']])
                ->andWhere([
                    'or',
                    ['level' => 2],
                    ['level' => 4],
                ])
                ->column();

            $aeaIds = array_unique(array_merge($childAreaIds, [$searchParams['areaId']]));
            $query->andWhere(['hasa.area_id' => $aeaIds]);
        }

        //活动状态搜索
        if ($searchParams['activityStatus']) {
            $activityStatusParams = explode(',', $searchParams['activityStatus']);
            $activityStatus       = [];
            $isWait               = false;
            foreach ($activityStatusParams as $item) {
                if ($item == self::ACTIVITY_STATUS_WAIT) {
                    $isWait = true;
                } else {
                    $activityStatus = array_merge($activityStatus, self::ACTIVITY_STATUS_RELATION[$item]);
                }
            }
            if ($activityStatus && $isWait) {
                $query->andWhere([
                    'or',
                    ['ha.activity_child_status' => $activityStatus],
                    [
                        '>=',
                        'ha.activity_start_date',
                        TimeHelper::dayToEndTime(date('Y-m-d')),
                    ],
                    [
                        'ha.activity_start_date' => TimeHelper::ZERO_DATE,
                    ],
                ]);
            } else {
                if ($activityStatus) {
                    $query->andWhere(['ha.activity_child_status' => $activityStatus]);
                } else {
                    $query->andWhere([
                        'or',
                        [
                            '>=',
                            'ha.activity_start_date',
                            TimeHelper::dayToEndTime(date('Y-m-d')),
                        ],
                        [
                            'ha.activity_start_date' => TimeHelper::ZERO_DATE,
                        ],
                    ]);
                }
            }
        }
        //活动类型搜索
        if ($searchParams['activityType']) {
            $activityType = explode(',', $searchParams['activityType']);
            $query->andWhere(['ha.type' => $activityType]);
        }
        //活动标签搜索
        if ($searchParams['featureTag']) {
            $featureTag = explode(',', $searchParams['featureTag']);
            $query->andWhere(['haftr.feature_tag_id' => $featureTag]);
        }
        //活动举办方式
        if ($searchParams['activityToHold']) {
            $activityToHold   = explode(',', $searchParams['activityToHold']);
            $activityToHold[] = BaseHwActivity::TO_HOLD_TYPE_ONLINE_AND_OFFLINE;
            $query->andWhere(['ha.to_hold_type' => $activityToHold]);
        }
        //活动时间
        if ($searchParams['activityTime'] && in_array($searchParams['activityTime'], [
                self::ACTIVITY_TIME_OVER_THREE_MONTH,
                self::ACTIVITY_TIME_THIS_MONTH,
                self::ACTIVITY_TIME_NEXT_THREE_MONTH,
                self::ACTIVITY_TIME_THIS_YEAR,
            ])) {
            $timeList                  = $this->getActivityTimeRangeByType($searchParams['activityTime']);
            $searchParams['startTime'] = $timeList['startTime'];
            $searchParams['endTime']   = $timeList['endTime'];
        }
        if ($searchParams['activityCustomTime'] && $searchParams['activityTime'] == self::ACTIVITY_TIME_CUSTOM) {
            //2024-08,2025-06
            $activityCustomTimeArr = explode(',', $searchParams['activityCustomTime']);
            if (count($activityCustomTimeArr) != 2) {
                $this->redirect404();
            }
            $startTime                 = $activityCustomTimeArr[0] . '-01 00:00:00';
            $endTime                   = $activityCustomTimeArr[1] . '-01 00:00:00';
            $maxDay                    = date('t', strtotime($endTime));
            $endTime                   = $activityCustomTimeArr[1] . '-' . $maxDay . ' 23:59:59';
            $searchParams['startTime'] = $startTime;
            $searchParams['endTime']   = $endTime;
        }
        if ($searchParams['startTime'] && $searchParams['endTime']) {
            //可能是类型获取的，也可能是自定义的
            $query->andWhere([
                'or',
                [
                    'between',
                    'ha.activity_start_date',
                    $searchParams['startTime'],
                    $searchParams['endTime'],
                ],
                [
                    'between',
                    'ha.activity_end_date',
                    $searchParams['startTime'],
                    $searchParams['endTime'],
                ],
                [
                    'and',
                    [
                        '<=',
                        'ha.activity_start_date',
                        $searchParams['startTime'],
                    ],
                    [
                        '>=',
                        'ha.activity_end_date',
                        $searchParams['endTime'],
                    ],
                ],
            ]);
        }
        $pageSize = $searchParams['pageSize'] ?? ($this->operationPlatform != self::PLATFORM_MINI ? 12 : 30);
        //获取总数量
        $count = $query->count();
        $pages = BaseHwActivity::setPage($count, $searchParams['page'] ?? 1, $pageSize);
        $list  = $query->select([
            'ha.id',
            'ha.series_type as seriesType',
            'ha.type',
            'ha.company_id as companyId',
            'ha.name',
            'ha.to_hold_type as toHoldType',
            'ha.main_img_file_id as mainImgFileId',
            'ha.image_mini_master_id as imageMiniMasterId',
            'ha.participation_company_amount as participationCompanyAmount',
            'ha.activity_child_status as activityChildStatus',
            'ha.activity_link as activityLink',
            'ha.wonderful_review as wonderfulReview',
            'ha.sign_up_url as signUpUrl',
            'ha.sign_end_date as signEndDate',
            'ha.is_outside_url as isOutsideUrl',
            'ha.detail_url as detailUrl',
            'ha.custom_feature_tag as customFeatureTag',
            'ha.apply_link_person_type as applyLinkPersonType',
            'ha.apply_link_person_form_id as applyLinkPersonFormId',
            'ha.apply_link_person_form_option_id as applyLinkPersonFormOptionId',
            'ha.apply_person_time as applyPersonTime',
        ])
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy(new Expression("ha.sort desc,FIELD(ha.activity_child_status," . implode(',
                ', [
                    BaseHwActivity::ACTIVITY_CHILD_STATUS_BE_ABOUT_TO_START,
                    BaseHwActivity::ACTIVITY_CHILD_STATUS_SIGN_UP,
                    BaseHwActivity::ACTIVITY_CHILD_STATUS_PROGRESS,
                    BaseHwActivity::ACTIVITY_CHILD_STATUS_NOT_START,
                    BaseHwActivity::ACTIVITY_CHILD_STATUS_END,
                ]) . ") asc,ha.activity_start_date desc,ha.id desc"))
            ->asArray()
            ->all();
        foreach ($list as &$item) {
            $item['isZhaopinhui'] = in_array($item['seriesType'], BaseHwActivity::ZHAOPINHUI_TYPE);
            if ($this->operationPlatform != self::PLATFORM_MINI) {
                $item['activityUrl']            = $item['isZhaopinhui'] ? BaseHwActivity::getActivityLinkUrl($item['seriesType'],
                    $item['activityLink']) : $item['detailUrl'];
                $item['activityTargetLinkType'] = 0;
                $applyPersonTime                = $item['isZhaopinhui'] ? $item['applyPersonTime'] : $item['signEndDate'];
            } else {
                if ($item['isZhaopinhui']) {
                    $item['activityUrl']            = BaseShowcase::urlToMiniRouter(BaseShowcase::PAGE_LINK_TYPE_ACTIVITY_DETAIL,
                        $item['id']);
                    $item['activityTargetLinkType'] = BaseShowcase::TARGET_LINK_TYPE_STATION;
                    $applyPersonTime                = $item['applyPersonTime'];
                } else {
                    if (!$item['detailUrl']) {
                        $item['activityUrl']            = '';
                        $item['activityTargetLinkType'] = -1;
                    } else {
                        $urlData                        = MiniHelper::getUrlType($item['detailUrl']);
                        $item['activityUrl']            = $urlData['pageType'] > 0 ? BaseShowcase::urlToMiniRouter($urlData['pageType'],
                            $urlData['url']) : $urlData['url'];
                        $item['activityTargetLinkType'] = $urlData['targetType'];
                    }
                    $applyPersonTime = $item['signEndDate'];
                }
            }
            if ($item['isOutsideUrl'] == BaseHwActivity::IS_OUTSIDE_URL_YES) {
                $item['rel'] = 'nofollow';
            } else {
                $item['rel'] = '';
            }
            //类型标签
            $item['typeText']           = BaseHwActivity::TYPE_TEXT_LIST[$item['type']];
            $item['mainImgFileUrl']     = FileHelper::getFullPathById($item['mainImgFileId']);
            $item['imageMiniMasterUrl'] = FileHelper::getFullPathById($item['imageMiniMasterId']);
            $item['featureTag']         = BaseHwActivityFeatureTagRelation::getFeatureTagName($item['id']);
            $item['customFeatureTag']   = $item['customFeatureTag'] ? explode(',', $item['customFeatureTag']) : [];
            $item['activityTime']       = BaseHwActivity::getActivityDate($item['id'], 4);
            $item['toHoldTypeText']     = BaseHwActivity::TO_HOLD_TYPE_TEXT[$item['toHoldType']];
            $item['area']               = BaseHwActivity::getAddressBySeries($item['id']);
            $isWonderfulReview          = (bool)$item['wonderfulReview'];
            $item['applyStatus']        = 2;
            if (($item['applyLinkPersonType'] != BaseHwActivity::APPLY_LINK_PERSON_TYPE_OTHER || $item['applyLinkPersonType'] != BaseHwActivity::APPLY_LINK_PERSON_TYPE_NULL) && !empty($this->resumeId)) {
                $item['applyStatus'] = BaseActivityFormOptionSign::getResumeActivityApplyStatus($this->resumeId,
                    $item['applyLinkPersonFormId'], $item['applyLinkPersonFormOptionId']);
            }
            $activityBtnInfo                 = $this->getBtnTextAndLink($item['id'], $item['seriesType'],
                $item['activityChildStatus'], $item['detailUrl'], $item['signUpUrl'], $item['activityLink'],
                $isWonderfulReview, $item['applyStatus'], $applyPersonTime);
            $item['activityChildStatusText'] = BaseHwActivity::ACTIVITY_CHILD_STATUS_TEXT_LIST[$item['seriesType']][$item['activityChildStatus']];
            $item['btnText']                 = $activityBtnInfo['btnText'];
            $item['btnLink']                 = $activityBtnInfo['btnLink'];
            $item['btnType']                 = $activityBtnInfo['btnType'];
            $item['btnIsClick']              = $activityBtnInfo['btnIsClick'];
            $item['btnTargetLinkType']       = $activityBtnInfo['btnTargetLinkType'];
            $item['tag']                     = array_merge($item['featureTag'], $item['customFeatureTag']);
            $item['sessionList']             = [];
            if ($item['seriesType'] == BaseHwActivity::SERIES_OVERSEAS_RECRUITMENT) {
                $item['sessionList'] = BaseHwActivitySession::getSessionInfoList($item['id']);
            }
            if (in_array($item['seriesType'], BaseHwActivity::ZHAOPINHUI_UN_TYPE)) {
                $companyInfo                        = BaseCompany::findOne($item['companyId']);
                $item['participationCompanyAmount'] = '';
                $item['participationText']          = $companyInfo ? $companyInfo->full_name : '';
            } else {
                $item['participationCompanyAmount'] = $item['participationCompanyAmount'] > 10 ? (int)$item['participationCompanyAmount'] : '更新中';
                $item['participationText']          = $item['participationCompanyAmount'] > 10 ? '家' : '';
            }
        }

        return [
            'list'  => $list,
            'limit' => $pages['limit'],
            'page'  => $pages['page'],
            'count' => (int)$count,
        ];
    }

    /**
     * 获取活动时间类型范围
     * @param $type
     * @return array
     */
    public function getActivityTimeRangeByType($type): array
    {
        switch ($type) {
            case self::ACTIVITY_TIME_OVER_THREE_MONTH:
                $start = date('Y-m-01 00:00:00', strtotime('-4 month'));
                $end   = date('Y-m-d 23:59:59', strtotime("$start +4 month -1 day"));
                break;
            case self::ACTIVITY_TIME_THIS_MONTH:
                $start = date('Y-m-01 00:00:00');
                $end   = date('Y-m-d 23:59:59', strtotime("$start +1 month -1 day"));
                break;
            case self::ACTIVITY_TIME_NEXT_THREE_MONTH:
                $start = date('Y-m-01 00:00:00');
                $end   = date('Y-m-d 23:59:59', strtotime("$start +4 month -1 day"));
                break;
            case self::ACTIVITY_TIME_THIS_YEAR:
                $start = date('Y-01-01 00:00:00');
                $end   = date('Y-m-d 23:59:59', strtotime("$start +1 year -1 day"));
                break;
            default:
                $start = $end = '';
                break;
        }

        return [
            'startTime' => $start,
            'endTime'   => $end,
        ];
    }

    /**
     * 根据活动子状态获取按钮文案以及按钮链接
     */
    private function getBtnTextAndLink(
        $activityId,
        $seriesType,
        $activityChildStatus,
        $detailUrl,
        $signUpUrl,
        $activityLink,
        $isWonderfulReview,
        $applyStatus,
        $applyPersonTime
    ) {
        if ($applyStatus == 1) {
            return [
                'btnText'           => '已报名',
                'btnLink'           => '',
                //1可点击 2不可点击
                'btnIsClick'        => 2,
                'btnTargetLinkType' => 0,
                //1是亮色 2是暗色
                'btnType'           => 2,
            ];
        }
        $btnType           = 1;
        $btnIsClick        = 1;
        $btnTargetLinkType = 0;
        $btnLink           = '';
        switch ($activityChildStatus) {
            case BaseHwActivity::ACTIVITY_CHILD_STATUS_SIGN_UP:
            case BaseHwActivity::ACTIVITY_CHILD_STATUS_NOT_START:
            case BaseHwActivity::ACTIVITY_CHILD_STATUS_BE_ABOUT_TO_START:
            case BaseHwActivity::ACTIVITY_CHILD_STATUS_PROGRESS:
                $btnText = '立即报名';
                break;
            case BaseHwActivity::ACTIVITY_CHILD_STATUS_END:
                $btnText    = '已结束';
                $btnType    = 2;
                $btnIsClick = 2;
                break;
        }

        if (in_array($seriesType, BaseHwActivity::ZHAOPINHUI_TYPE)) {
            if ($activityChildStatus == BaseHwActivity::ACTIVITY_CHILD_STATUS_END && $isWonderfulReview) {//有精彩回顾
                $btnText    = '精彩回顾';
                $btnType    = 2;
                $btnIsClick = 1;
                if ($this->operationPlatform != self::PLATFORM_MINI) {
                    $btnLink = BaseHwActivity::getActivityLinkUrl($seriesType, $activityLink) . '?showTab=review';
                } else {
                    $btnLink           = BaseShowcase::urlToMiniRouter(BaseShowcase::PAGE_LINK_TYPE_ACTIVITY_DETAIL,
                            $activityId) . '&showTab=review';
                    $btnTargetLinkType = BaseShowcase::TARGET_LINK_TYPE_STATION;
                }
            } else {
                $urlInfo           = BaseHwActivity::getPersonApplyLinkUrl($activityId);
                $btnLink           = $urlInfo['link'];
                $btnTargetLinkType = $urlInfo['linkType'];

                if ($activityChildStatus != BaseHwActivity::ACTIVITY_CHILD_STATUS_END && $applyPersonTime != TimeHelper::ZERO_DATE && strtotime(TimeHelper::dayToEndTime($applyPersonTime)) < time()) {
                    //报名链接不为空 且截止时间不为空 且当前时间是否已超过“人才报名截止日期”
                    $btnText = '立即查看';
                }
                if (!$btnLink) {
                    //链接为空
                    if ($this->operationPlatform != self::PLATFORM_MINI) {
                        $btnLink = BaseHwActivity::getActivityLinkUrl($seriesType, $activityLink);
                    } else {
                        $btnLink           = BaseShowcase::urlToMiniRouter(BaseShowcase::PAGE_LINK_TYPE_ACTIVITY_DETAIL,
                            $activityId);
                        $btnTargetLinkType = BaseShowcase::TARGET_LINK_TYPE_STATION;
                    }
                }
            }
        } else {
            if ($activityChildStatus != BaseHwActivity::ACTIVITY_CHILD_STATUS_END) {
                $btnLink = $signUpUrl ?: $detailUrl;
                if ($signUpUrl && $applyPersonTime != TimeHelper::ZERO_DATE && strtotime(TimeHelper::dayToEndTime($applyPersonTime)) < time()) {
                    //报名链接不为空 且截止时间不为空 且当前时间是否已超过“人才报名截止日期”
                    $btnText = '立即查看';
                    $btnLink = $detailUrl;
                }
                if ($this->operationPlatform == self::PLATFORM_MINI && $btnLink) {
                    $linkData          = MiniHelper::getUrlType($btnLink);
                    $btnLink           = $linkData['pageType'] > 0 ? BaseShowcase::urlToMiniRouter($linkData['pageType'],
                        $linkData['url']) : $linkData['url'];
                    $btnTargetLinkType = $linkData['targetType'];
                }
            }
        }

        return [
            'btnText'           => $btnText ?? '',
            'btnLink'           => $btnLink,
            'btnType'           => $btnType,
            'btnIsClick'        => $btnIsClick,
            'btnTargetLinkType' => $btnTargetLinkType,
        ];
    }

    /**
     * 地区筛选处理
     * @return array
     */
    private function getArea()
    {
        $areaList = Cache::get(Cache::ZHAOPINHUI_HOME_AREA_KEY);
        if (!$areaList || $this->isCache) {
            //查出海外四级的三级数据
            $areaList = BaseArea::find()
                ->select([
                    'id',
                    'name',
                    'spell',
                ])
                ->where([
                    'id' => self::ACTIVITY_AREA,
                ])
                //按照self::ACTIVITY_AREA排序
                ->orderBy(new Expression('FIELD(id,' . implode(',', self::ACTIVITY_AREA) . ') asc'))
                ->asArray()
                ->all();

            Cache::set(Cache::ZHAOPINHUI_HOME_AREA_KEY, json_encode($areaList));

            return $areaList;
        } else {
            return json_decode($areaList, true);
        }
    }

    /**
     * 获取数据统计
     * 数据卡片（刷新本页面重新加载数据）
     * 1、“累计举办”数值计算：200+本页面【最新活动】模块调用所有活动的总量；
     * 2、“参与单位”数值计算：2000+本页面【最新活动】模块所有活动的“关联单位”总量；“关联单位”须按单位ID去重后进行统计；
     * 3、“对接人选”数值计算：60000+本页面【最新活动】模块所有活动的人才报名总量：
     * 3.1 人才报名数据取值规则：
     * ① “招聘会活动系列”，取“人才报名链接”所关联的表单的报名数据； “非招聘会活动系列”取“活动报名链接”关联表单的报名数据；
     * ②  若关联表单为内部报名表，实际报名数据需*1.7（若报名链接具体至表单选项，须取该选项报名数据）；
     * 若非内部报名表，则每条活动的默认报名数据为200。
     * 4、数据展示规则&加载动效：
     * 4.1 若数值＜10w，则直接对数值进行四舍五入后取整展示。
     * 若数值≥10w，则以w为单位进行展示，并进行四舍五入，最多保留到小数点后一位。
     * 4.2 打开本页面，动态加载该模块所有数据；
     */
    public function getStatisticalData()
    {
        $result = Cache::get(Cache::ZHAOPINHUI_HOME_STATISTICAL_DATA);
        if ($result && !$this->isCache) {
            return json_decode($result, true);
        }
        $activityCount  = BaseHwActivity::find()
            ->where([
                'series_type'      => [
                    BaseHwActivity::SERIES_COME_HOME,
                    BaseHwActivity::SERIES_OVERSEAS_RECRUITMENT,
                    BaseHwActivity::SERIES_ZHAOPINHUI_OFFLINE,
                    BaseHwActivity::SERIES_ZHAOPINHUI_ONLINE,
                ],
                'grounding_status' => BaseHwActivity::GROUNDING_STATUS_ON,
            ])
            ->count();
        $unActivityData = BaseHwActivity::find()
            ->select([
                'id',
                'sign_up_url',
            ])
            ->where([
                'series_type'      => [
                    BaseHwActivity::SERIES_COME_HOME,
                    BaseHwActivity::SERIES_OVERSEAS_RECRUITMENT,
                ],
                'grounding_status' => BaseHwActivity::GROUNDING_STATUS_ON,
            ])
            ->asArray()
            ->all();
        $unCompanyIds   = BaseHwActivity::find()
            ->select('company_id')
            ->where([
                'series_type'      => [
                    BaseHwActivity::SERIES_COME_HOME,
                    BaseHwActivity::SERIES_OVERSEAS_RECRUITMENT,
                ],
                'grounding_status' => BaseHwActivity::GROUNDING_STATUS_ON,
            ])
            ->andWhere([
                '>',
                'company_id',
                0,
            ])
            ->asArray()
            ->column();
        $zActivityData  = BaseHwActivity::find()
            ->select([
                'id',
                'apply_link_person_type',
                'apply_link_person_form_id',
                'apply_link_person_form_option_id',
            ])
            ->where([
                'series_type'      => [
                    BaseHwActivity::SERIES_ZHAOPINHUI_OFFLINE,
                    BaseHwActivity::SERIES_ZHAOPINHUI_ONLINE,
                ],
                'grounding_status' => BaseHwActivity::GROUNDING_STATUS_ON,
            ])
            ->asArray()
            ->all();
        $zActivityIds   = array_column($zActivityData, 'id');
        $zCompanyIds    = BaseHwActivityCompany::find()
            ->select('company_id')
            ->where(['activity_id' => $zActivityIds])
            ->asArray()
            ->column();

        //非招聘会对接人数
        $applicantTotalUn = 0;
        foreach ($unActivityData as $item) {
            if ($item['sign_up_url']) {
                $urlArr = explode('?', $item['sign_up_url']);
                $formId = BaseActivityForm::findOne(['link' => $urlArr[0]]);
                if ($formId) {
                    $applicantTotalUn += (BaseActivityFormOptionSign::find()
                            ->where(['activity_form_id' => $formId])
                            ->count() * 1.7);
                } else {
                    $applicantTotalUn += 200;
                }
            } else {
                $applicantTotalUn += 200;
            }
        }

        //招聘会对接人数
        $applicantTotalZ = 0;
        foreach ($zActivityData as $item) {
            if ($item['apply_link_person_type'] == BaseHwSpecialActivity::APPLY_LINK_PERSON_TYPE_OTHER) {
                $applicantTotalZ += 200;
            } elseif ($item['apply_link_person_type'] == BaseHwSpecialActivity::APPLY_LINK_PERSON_TYPE_FORM) {
                $applicantTotalZ += (BaseActivityFormOptionSign::find()
                        ->where(['activity_form_id' => $item['apply_link_person_form_id']])
                        ->count() * 1.7);
            } elseif ($item['apply_link_person_type'] == BaseHwSpecialActivity::APPLY_LINK_PERSON_TYPE_FORM_OPTION) {
                $applicantTotalZ += (BaseActivityFormOptionSign::find()
                        ->where([
                            'activity_form_id' => $item['apply_link_person_form_id'],
                            'option_id'        => $item['apply_link_person_form_option_id'],
                        ])
                        ->count() * 1.7);
            }
        }

        $companyIds = array_unique(array_merge($unCompanyIds, $zCompanyIds));
        // 累计举办
        $holdTotal = 200 + $activityCount;
        // 参与单位
        $companyTotal = 2000 + count($companyIds);
        // 对接人选
        $applicantTotal = round(60000 + $applicantTotalUn + $applicantTotalZ);
        $applicantTotal = $applicantTotal > 100000 ? round($applicantTotal / 10000,
                2) . '<span>w</span>' : $applicantTotal;
        $result         = [
            'holdTotal'      => $holdTotal,
            'companyTotal'   => $companyTotal,
            'applicantTotal' => $applicantTotal,
        ];

        Cache::set(Cache::ZHAOPINHUI_HOME_STATISTICAL_DATA, json_encode($result), 21600);

        return $result;
    }
}