<?php
/**
 * create user：shannon
 * create time：2025/2/12 上午11:37
 */
namespace common\service\zhaoPinHuiColumn;

use common\base\models\BaseActivityForm;
use common\base\models\BaseActivityFormIntentionOption;
use common\base\models\BaseActivityFormOptionSign;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseHwActivity;
use common\base\models\BaseHwActivityAnnouncement;
use common\base\models\BaseHwActivityCompany;
use common\base\models\BaseHwSpecialActivity;
use common\base\models\BaseHwSpecialActivityRelation;
use common\base\models\BaseJob;
use common\base\models\BaseJobCategoryRelation;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseMajor;
use common\base\models\BaseResume;
use common\base\models\BaseShowcase;
use common\components\MessageException;
use common\helpers\ArrayHelper;
use common\helpers\UrlHelper;
use Yii;
use common\base\models\BaseMember;
use common\helpers\TimeHelper;
use common\libs\Cache;
use common\service\CommonService;
use yii\db\conditions\AndCondition;
use yii\db\conditions\OrCondition;
use yii\db\Expression;

class BaseService extends CommonService
{
    protected $isCache = true;//暂时true
    protected $memberId;
    protected $resumeId;

    /**
     * 设置用户
     */
    protected function setUser()
    {
        switch ($this->operationPlatform) {
            case self::PLATFORM_WEB:
            case self::PLATFORM_WEB_PERSON:
            case self::PLATFORM_H5:
            case self::PLATFORM_ZHAOPINHUI:
                //检测是否有用户登录
                if (!Yii::$app->user->isGuest) {
                    //获取登录用户ID
                    $this->memberId = Yii::$app->user->id;
                }
                break;
            case self::PLATFORM_MINI:
                //小程序获取member_id
                $this->memberId = BaseMember::getMiniMemberId();
                break;
            //            default:
            //                throw new MessageException('您没有权限执行此操作！');
        }
        if ($this->memberId) {
            $this->resumeId = BaseResume::findOne(['member_id' => $this->memberId])->id;
        }
    }

    /**
     * 获取日期配置，如果数组返回数组类型
     * @param $dateType
     * @param $startDate
     * @param $endDate
     * @param $startTime
     * @param $endTime
     * @return string
     */
    public static function formatActivityDateText(
        $dateType,
        $startDate,
        $endDate,
        $startTime,
        $endTime,
        $isWeek = true
    ): string {
        $weekDay = TimeHelper::getTimestampWeekday(strtotime($startDate));
        switch ($dateType) {
            case 2:
            case 5:
                $formatDate = 'Y年m月d日';
                break;
            case 3:
                $formatDate = 'm.d';
                break;
            case 1:
            case 4:
            default:
                $formatDate = 'Y.m.d';
                break;
        }
        $formatStartDate = date($formatDate, strtotime($startDate));
        $formatEndDate   = date($formatDate, strtotime($endDate));
        // 不展示日期

        $formatStartTime = '';
        $formatEndTime   = '';
        if ($startTime) {
            $startTime       = substr_replace($startTime, ':', 2, 0) . ':00';
            $formatStartTime = date('H:i', strtotime($startDate . ' ' . $startTime));
        }
        if ($endTime) {
            $endTime       = substr_replace($endTime, ':', 2, 0) . ':00';
            $formatEndTime = date('H:i', strtotime($endDate . ' ' . $endTime));
        }

        if ($dateType == 5) {
            $formatStartTime = '';
            $formatEndTime   = '';
        }

        // 配置日期
        if ($startDate == $endDate) {
            $activityDateText = $formatStartDate . ($isWeek ? "(周{$weekDay}) " : ' ') . implode('-', array_filter([
                    $formatStartTime,
                    $formatEndTime,
                ]));
        } else {
            // 如果等于4，不返回时间
            if ($dateType == 2 || $dateType == 4) {
                $formatStartTime = '';
                $formatEndTime   = '';
            }
            $activityDateText = implode('～', array_filter([
                $formatStartDate . (!$formatStartTime ? '' : ' ' . $formatStartTime),
                $formatEndDate . (!$formatEndTime ? '' : ' ' . $formatEndTime),
            ]));
        }

        return $activityDateText;
    }

    /**
     * 字符串转数组
     * @param $str
     * @return string
     */
    private static function strToArr($str)
    {
        if (is_string($str)) {
            return explode(',', $str);
        }

        return $str;
    }

    /**
     * 活动参会列表 用于查看活动参会列表数据
     * @param array $params 查询条件
     * @return array
     */
    public static function getActivityCompany(array $params = [], $resumeId = 0): array
    {
        $activityId        = intval($params['activityId']);
        $specialActivityId = intval($params['specialActivityId']);
        if ($activityId) {
            $specialActivityId = 0;
        }

        if (empty($activityId) && empty($specialActivityId)) {
            throw new MessageException('查询活动单位不能为空');
        }
        if (empty($activityId)) {
            $cacheKey        = sprintf(Cache::SPECIAL_ACTIVITY_RELATION_ACTIVITY_SORT, $specialActivityId);
            $orderCompanyIds = Cache::get($cacheKey);// 编辑和更新会有记录
            if (empty($orderCompanyIds)) {
                $orderCompanyIds = 1;
            }
            $order       = new Expression('FIELD(c.id,' . $orderCompanyIds . ')');
            $activityIds = BaseHwSpecialActivityRelation::find()
                ->where(['special_id' => $specialActivityId])
                ->select('activity_id')
                ->column();
        } else {
            $activityIds = [$activityId];
            $order       = 'ac.sort_point asc';
        }

        $orWhere = [];
        if (!empty($params['areaId'])) {
            $params['areaId'] = explode(',', $params['areaId']);
            $orWhere          = [
                [
                    'in',
                    'c.city_id',
                    $params['areaId'],
                ],
                [
                    'in',
                    'c.province_id',
                    $params['areaId'],
                ],
            ];
        }

        $where = [
            [
                'in',
                'ac.activity_id',
                $activityIds,
            ],
        ];

        $companyQuery = BaseHwActivityCompany::find()
            ->alias('ac')
            ->innerJoin(['c' => BaseCompany::tableName()], 'c.id=ac.company_id')
            ->groupBy('c.id')
            ->select([
                'c.id',
                'ac.company_id',
                'c.short_name',
                'c.full_name',
                'c.logo_url',
                'c.province_id',
                'c.city_id',
                'c.type',
                'c.nature',
                'ac.is_top',
            ])
            ->andWhere(new OrCondition($orWhere));

        $params['type'] = array_filter(self::strToArr($params['type']));
        if (!empty($params['type'])) {
            $where[] = [
                'in',
                'c.type',
                $params['type'],
            ];
        }

        // 职位学科和职位类型
        if (!empty($params['majorId']) || !empty($params['categoryId'])) {
            $announcementIds = BaseHwActivityAnnouncement::find()
                ->select('announcement_id')
                ->where(['activity_id' => $activityIds])
                ->column();
            $companyQuery->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.company_id=ac.company_id')
                ->andWhere(['a.id' => $announcementIds]);

            if (!empty($params['type'])) {
                $where[] = [
                    'in',
                    'a.id',
                    $announcementIds,
                ];
            }
        }

        if (!empty($params['majorId'])) {
            $companyQuery->leftJoin(['mr' => BaseJobMajorRelation::tableName()], 'mr.announcement_id=a.id');
            $where[] = [
                'in',
                'mr.major_id',
                self::strToArr($params['majorId']),
            ];
        }

        if (!empty($params['categoryId'])) {
            $companyQuery->leftJoin(['cr' => BaseJobCategoryRelation::tableName()], 'cr.announcement_id=a.id');
            $where[] = [
                'in',
                'cr.category_id',
                self::strToArr($params['categoryId']),
            ];
        }

        $count = $companyQuery->andWhere(new AndCondition($where))
            ->count();
        $pages = self::setPage($count, $params['page'], $params['pageSize'] ?? 18);
        $list  = $companyQuery->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($order)
            ->asArray()
            ->all();

        $list = self::formatActivityCompany($list, $activityIds);
        $list = self::formatActivityCompanyApplyStatus($list, $resumeId, $specialActivityId, $activityId);

        return [
            'list'  => $list,
            'limit' => $pages['limit'],
            'page'  => $pages['page'],
            'count' => (int)$count,
            'isEnd' => ($pages['page'] * 18) >= (int)$count ? 1 : 2,
        ];
    }

    /**
     * 配置用户的报名情况，全部数据一样，所以不能直接在列表配置
     * @param $list
     * @param $resumeId
     * @param $specialId
     * @param $activityId
     * @return mixed
     */
    private static function formatActivityCompanyApplyStatus($list, $resumeId, $specialId, $activityId)
    {
        $applyLink     = '';
        $activityInfo  = '';
        $applyLinkType = 1;
        $isEnd         = 2;
        if ($specialId) {
            $activityInfo = BaseHwSpecialActivity::findOne($specialId);
            if ($activityInfo) {
                $activityInfo  = $activityInfo->toArray();
                $applyLinkInfo = SpecialActivityService::getPersonApplyLinkUrl($activityInfo['id'], $activityInfo);
                $applyLink     = $applyLinkInfo['link'];
                $applyLinkType = $applyLinkInfo['linkType'];
                if ($activityInfo['status'] == BaseHwSpecialActivity::STATUS_COMPLETED) {
                    $isEnd = 1;
                }
            }
        } else {
            if ($activityId) {
                $activityInfo = BaseHwActivity::findOne($activityId);
                if ($activityInfo) {
                    $activityInfo  = $activityInfo->toArray();
                    $applyLinkInfo = BaseHwActivity::getPersonApplyLinkUrl($activityId);
                    $applyLink     = $applyLinkInfo['link'];
                    $applyLinkType = $applyLinkInfo['linkType'];
                    if ($activityInfo['activity_child_status'] == BaseHwActivity::ACTIVITY_CHILD_STATUS_END) {
                        $isEnd = 1;
                    }
                }
            }
        }

        $type     = $activityInfo['apply_link_person_type'];
        $formId   = $activityInfo['apply_link_person_form_id'];
        $optionId = $activityInfo['apply_link_person_form_option_id'];

        // 用户报名状态
        $applyStatus = 2;
        if ($type != BaseHwActivity::APPLY_LINK_PERSON_TYPE_OTHER && !empty($resumeId)) {
            $applyStatus = BaseActivityFormOptionSign::getResumeActivityApplyStatus($resumeId, $formId, $optionId);
        }

        foreach ($list as &$item) {
            $item['applyDetail'] = [
                'applyStatus'   => $applyStatus,
                'applyLink'     => $applyLink,
                'isEnd'         => $isEnd,
                'applyLinkType' => $applyLinkType,
            ];
            $btnIsClick          = 1;//1可点击 2不可点击
            $btnType             = 1; //2:灰色，1:亮色
            $btnLink             = $applyLink;
            $btnIsShow           = 1;//1显示 2不显示
            $btnText             = '立即报名';
            if (!$applyLink) {
                $btnIsShow = 2;
            }

            if ($specialId) {
                if ($isEnd == 1) {
                    $btnIsShow = 2;
                } else {
                    if ($type == BaseHwSpecialActivity::APPLY_LINK_PERSON_TYPE_NULL) {
                        $btnIsShow = 2;
                    } else {
                        if ($activityInfo['apply_person_time'] == TimeHelper::ZERO_DATE || !$activityInfo['apply_person_time']) {
                            $btnIsShow = 1;
                        } else {
                            $applyPersonTime = strtotime($activityInfo['apply_person_time'] . ' 23:59:59');
                            if ($applyPersonTime > time()) {
                                $btnIsShow = 1;
                            } else {
                                $btnIsShow = 2;
                            }
                        }
                    }
                }
            } elseif ($activityId) {
                if ($isEnd == 1) {
                    $btnIsShow = 2;
                } else {
                    if ($applyStatus == 1) {
                        $btnIsClick = 2;
                        $btnIsShow  = 1;
                        $btnText    = '已报名';
                        $btnType    = 2;
                    } else {
                        if ($type == BaseHwActivity::APPLY_LINK_PERSON_TYPE_NULL) {
                            $btnIsShow = 2;
                        } else {
                            if ($activityInfo['apply_person_time'] == TimeHelper::ZERO_DATE || !$activityInfo['apply_person_time']) {
                                $btnIsShow = 1;
                            } else {
                                $applyPersonTime = strtotime($activityInfo['apply_person_time'] . ' 23:59:59');
                                if ($applyPersonTime > time()) {
                                    $btnIsShow = 1;
                                } else {
                                    $btnIsShow = 2;
                                }
                            }
                        }
                    }
                }
            }

            $item['applyBtnData'] = [
                'btnText'    => $btnText,
                'btnLink'    => $btnLink,
                'btnIsClick' => $btnIsClick,
                'btnIsShow'  => $btnIsShow,
                'btnType'    => $btnType,
            ];
        }

        return $list;
    }

    /**
     * 整理样式
     * @param $companyList    array 涉及到的单位列表
     * @param $allActivityIds array 涉及到所有活动id
     * @return array
     * @throws \Exception
     */
    private static function formatActivityCompany($companyList, $allActivityIds): array
    {
        foreach ($companyList as &$item) {
            // cardTag单位类型丨单位性质丨单位所在省份-城市
            $cardTag = [];
            // cardTag2招聘岗位&参会场次：
            $cardTag2 = '';
            // 亮点描述/专业方向/本活动关联公告标题
            $cardTag3 = '';

            // 该单位最新公告信息
            $earliestAnnouncement = BaseHwActivityAnnouncement::getEarliestAnnouncementByActivityId($allActivityIds,
                $item['company_id']);

            $item['announcementId'] = '';
            $item['targetUrl']      = '';
            $item['targetType']     = 0;
            if ($earliestAnnouncement) {
                $item['targetUrl']       = PLATFORM != 'MINI' ? UrlHelper::createPcAnnouncementDetailPath($earliestAnnouncement['id']) : BaseShowcase::urlToMiniRouter(BaseShowcase::PAGE_LINK_TYPE_ANNOUNCEMENT_DETAIL,
                    $earliestAnnouncement['id']);
                $item['targetType']      = BaseShowcase::TARGET_LINK_TYPE_STATION;
                $item['announcementUrl'] = UrlHelper::createPcAnnouncementDetailPath($earliestAnnouncement['id']);
                $item['announcementId']  = $earliestAnnouncement['id'];
            }

            if (!$item['announcementUrl']) {
                $item['targetUrl']  = PLATFORM != 'MINI' ? UrlHelper::createPcCompanyDetailPath($item['company_id']) : BaseShowcase::urlToMiniRouter(BaseShowcase::PAGE_LINK_TYPE_COMPANY_DETAIL,
                    $item['company_id']);
                $item['targetType'] = BaseShowcase::TARGET_LINK_TYPE_STATION;
            }

            //获取单位类型
            $type = BaseDictionary::getCompanyTypeName($item['type']);
            //            //获取单位性质
            //            $nature = BaseDictionary::getCompanyNatureName($item['nature']);

            // 若单位所在城市为“北京市、天津市、上海市、重庆市” 直辖市，仅展示直辖市信息
            $addressArr   = [];
            $cityName     = BaseArea::getAreaName($item['city_id']);
            $provinceName = BaseArea::getAreaName($item['province_id']);
            if ($cityName == $provinceName) {
                $addressArr[] = $cityName;
            } else {
                $addressArr[] = $provinceName;
                $addressArr[] = $cityName;
            }
            $addressText = implode('-', array_filter($addressArr));

            $cardTag[]       = $type;
            $cardTag[]       = $addressText;
            $item['cardTag'] = implode('丨', array_filter($cardTag));

            // card2
            $activityJobContent = self::getCompanyCategoryText($earliestAnnouncement);
            if ($activityJobContent) {
                $cardTag2 = $activityJobContent;
            }
            $item['cardTag2'] = $cardTag2;

            // card3
            if (!empty($earliestAnnouncement['highlights_describe'])) {
                // 亮点
                $cardTag3 = [
                    'value' => $earliestAnnouncement['highlights_describe'],
                    'type'  => 1,
                ];
            } else {
                // 专业方向
                $majorNameArr = BaseJobMajorRelation::find()
                    ->alias('mr')
                    ->leftJoin(['m' => BaseMajor::tableName()], 'mr.major_id = m.id')
                    ->where([
                        'mr.announcement_id' => $earliestAnnouncement['id'],
                        'mr.level'           => 2,
                    ])
                    ->groupBy('m.id')
                    ->select(['m.name'])
                    ->column();
                if ($majorNameArr) {
                    $cardTag3 = [
                        'value' => implode('，', $majorNameArr),
                        'type'  => 2,
                    ];
                } else {
                    // 公告标题
                    if (!empty($earliestAnnouncement['title'])) {
                        $cardTag3 = [
                            'value' => $earliestAnnouncement['title'],
                            'type'  => 3,
                        ];
                    } else {
                        $cardTag3 = [
                            'value' => '该单位招聘简章更新中，敬请关注！',
                            'type'  => 4,
                        ];
                    }
                }
            }
            $item['cardTag3'] = $cardTag3;
            $item['name']     = empty($item['short_name']) ? $item['full_name'] : $item['short_name'];
            $item['logo_url'] = empty($item['logo_url']) ? Yii::$app->params['defaultCompanyLogo'] : $item['logo_url'];
        }

        return $companyList;
    }

    /**
     * 获取招聘单位信息
     * @param $companyId
     * @param $allActivityIds
     * @param $earliestAnnouncement
     */
    private static function getCompanyCategoryText($earliestAnnouncement)
    {
        // 最新的招聘岗位
        if (!empty($earliestAnnouncement['activity_job_content'])) {
            return $earliestAnnouncement['activity_job_content'];
        } else {
            if ($earliestAnnouncement) {
                // 获取最早的一条公告id
                $jobCategoryList = BaseJobCategoryRelation::find()
                    ->alias('jcr')
                    ->leftJoin(['cj' => BaseCategoryJob::tableName()], 'jcr.category_id = cj.id')
                    ->leftJoin(['j' => BaseJob::tableName()], 'j.id = jcr.job_id')
                    ->andWhere([
                        'j.status'            => [
                            BaseJob::STATUS_ONLINE,
                            BaseJob::STATUS_OFFLINE,
                        ],
                        'j.is_show'           => BaseJob::IS_SHOW_YES,
                        'jcr.announcement_id' => $earliestAnnouncement['id'],
                        'cj.level'            => 2,
                    ])
                    ->select([
                        'cj.name',
                        'cj.id',
                        'COUNT(cj.id) as category_num',
                    ])
                    ->groupBy('cj.id')
                    ->orderBy('category_num desc, cj.id desc')
                    ->asArray()
                    ->all();

                $activityJobContent = implode('、', array_column($jobCategoryList, 'name'));
                if ($activityJobContent) {
                    return $activityJobContent;
                }
            }
        }

        return '';
    }

    /**
     * 参会单位里面的筛选信息
     * @param $specialActivityId int 全部的时候传这个
     * @param $activityId        int  单个的时候传这个，$specialActivityId传0
     */
    public static function getActivityCompanySearchCompanyTypeParams($specialActivityId = 0, $activityId = 0)
    {
        if ($activityId) {
            $redisKey = sprintf(Cache::ACTIVITY_COMPANY_TYPE_PARAMS, 0, $activityId);
        } else {
            $redisKey = sprintf(Cache::ACTIVITY_COMPANY_TYPE_PARAMS, $specialActivityId, 0);
        }
        $typeList = Cache::get($redisKey);
        $typeList = json_decode($typeList, true);
        if (!$typeList) {
            return [];
        }

        return BaseDictionary::find()
            ->select([
                'name',
                'code',
            ])
            ->andWhere(['code' => $typeList])
            ->andWhere(['type' => 25])
            ->orderBy(new Expression('FIELD(code,' . implode(',', $typeList) . ')'))
            ->asArray()
            ->all();
    }

    /**
     * 参会单位里面的筛选信息
     * * @param $specialActivityId int 全部的时候传这个
     * * @param $activityId        int  单个的时候传这个，$specialActivityId传0
     */
    public static function getActivityCompanySearchMajorParams($specialActivityId = 0, $activityId = 0)
    {
        $majorList = BaseMajor::getAllForJobSelect();

        if ($activityId) {
            $redisKey = sprintf(Cache::ACTIVITY_COMPANY_MAJOR_PARAMS, 0, $activityId);
        } else {
            $redisKey = sprintf(Cache::ACTIVITY_COMPANY_MAJOR_PARAMS, $specialActivityId, 0);
        }

        $hotList = Cache::get($redisKey);
        $hotList = json_decode($hotList, true);
        if ($hotList) {
            $childrenMajor = [];
            foreach ($hotList as $item) {
                $childrenMajor[] = [
                    'k'           => $item['id'],
                    'v'           => $item['name'],
                    'parentId'    => $item['parent_id'],
                    'topParentId' => $item['parent_id'],
                ];
            }
            array_unshift($majorList, [
                'k'        => 0,
                'v'        => '热门学科',
                'parentId' => 0,
                'children' => $childrenMajor,
            ]);
        }

        return $majorList;
    }

    /**
     * 参会单位里面的筛选信息
     * @param $specialActivityId int 全部的时候传这个
     * @param $activityId        int  单个的时候传这个，$specialActivityId传0
     */
    public static function getActivityCompanySearchCityParams($specialActivityId = 0, $activityId = 0)
    {
        if ($specialActivityId) {
            $redisKey = sprintf(Cache::ACTIVITY_COMPANY_AREA_PARAMS, $specialActivityId, 0);
        } else {
            $redisKey = sprintf(Cache::ACTIVITY_COMPANY_AREA_PARAMS, 0, $activityId);
        }
        $areaList = Cache::get($redisKey);

        $areaList = json_decode($areaList, true);

        if (PLATFORM != 'MINI') {
            array_unshift($areaList, [
                'name' => '不限',
                'id'   => '',
            ]);
        }

        return $areaList;
    }

    /**
     * 处理富文本由一级标题
     * @param $content
     * @return string|string[]
     */
    public static function formatContentV1($content)
    {
        if (empty($content)) {
            return '';
        }
        //        <div class="welfare-wrapper">
        //                                <div class="welfare-title">参会享交通福利</div>
        //                                <div class="welfare-content">
        //    人才参会享市区内交通报销（60元内/人），地铁、公交、打车都可以，轻松出行无负担！参会时现场扫码填写相关报销信息、上传报销凭证，会后10个工作日内完成报销。（具体操作方式可以现场找工作人员了解）
        //                                </div>
        //                            </div>
        //        <div class="welfare-wrapper"><div class="welfare-title">参会享交通福利</div><div class="welfare-content">人才参会享市区内交通报销（60元内/人），地铁</div></div>
        // 获取所有的$label标签
        preg_match_all('/<h4[^>]*([\s\S]*?)<\/h4>/i', $content, $matches);
        if ($matches[0]) {
            foreach ($matches[0] as $match) {
                // 去掉html
                $txt     = strip_tags($match);
                $h1      = '</div></div><div class="welfare-wrapper"><div class="welfare-title">' . $txt . '</div><div class="welfare-content">';
                $content = str_replace($match, $h1, $content);
            }
            $content = '<div class="welfare-wrapper"><div class="welfare-title">' . $content . '</div></div>';
            $content = str_replace('<div class="welfare-wrapper"><div class="welfare-title"></div></div>', '',
                $content);
        }

        $content = str_replace('<img', '<img class="rich-img"', $content);

        return $content;
    }

    /**
     * 小程序--处理富文本由一级标题
     * @param $content
     * @return string|string[]
     */
    public static function formatContentMiniV1($content)
    {
        if (empty($content)) {
            return '';
        }
        //        <view class="welfare-common">
        //          <view class="welfare-title">参会享交通福利</view>
        //          <view class="welfare-body">
        //            人才参会享市区内交通报销（60元内/人），地铁、公交、打车都可以，轻松出行 场找工作人员了解）
        //            </view>
        //        </view>
        //        <view class="welfare-common">
        //          <view class="welfare-title">参会享交通福利</view>
        //          <view class="welfare-body">
        //            人才参会享市区内交通报销（60元内/人），地铁、公交、打车都可以，轻松出行 场找工作人员了解）
        //            </view>
        //        </view>
        // 获取所有的$label标签
        preg_match_all('/<h4[^>]*([\s\S]*?)<\/h4>/i', $content, $matches);
        if ($matches[0]) {
            foreach ($matches[0] as $match) {
                // 去掉html
                $txt     = strip_tags($match);
                $h1      = '</view></view><view class="welfare-common"><view class="welfare-title">' . $txt . '</view><view class="welfare-body">';
                $content = str_replace($match, $h1, $content);
            }
            $content = '<view class="welfare-common"><view class="welfare-title">' . $content . '</view></view>';
            $content = str_replace('<view class="welfare-common"><view class="welfare-title"></view></view>', '',
                $content);
            $content = str_replace('<img', '<img class="rich-img"', $content);
        }

        return $content;
    }

    /**
     * 处理富文本有两级标题
     * @param $content
     * @return string
     */
    public static function formatContentV2($content)
    {
        if (empty($content)) {
            return '';
        }
        preg_match_all('/<h4[^>]*([\s\S]*?)<\/h4>/i', $content, $matchesOne);

        if ($matchesOne[0]) {
            // 每两个h5标签之间的内容用一个div=common-wrapper包裹
            foreach ($matchesOne[0] as $matchOne) {
                // 去掉html
                $txt = strip_tags($matchOne);
                // 获取所有的$labelTwo标签
                $h1      = '</div></div></div><div class="common-wrapper wow animate__animated animate__fadeInUp animated"><div class="common-title-content"><div class="common-title"><span>' . $txt . '</span></div></div><div class="common-content--border"><div class="common-content">';
                $content = str_replace($matchOne, $h1, $content);
            }

            $content = '<div class="common-wrapper wow animate__animated animate__fadeInUp animated"><div class="common-content--border"><div class="common-content">' . $content . '</div></div></div>';
            $content = str_replace('<div class="common-wrapper wow animate__animated animate__fadeInUp animated"><div class="common-content--border"><div class="common-content"></div></div></div>',
                '', $content);
            // 找到第一个common-content里面的内容
            preg_match_all('/<div class="common-content">([\s\S]*?)<\/div>/i', $content, $matches);
            $div1Base = $matches[1][0];

            // 去掉里面全部的html标签(除了img)
            $div1 = strip_tags($div1Base, '<img>');
            $div1 = str_replace(' ', '', $div1);
            if (empty($div1)) {
                $replace = '<div class="common-wrapper"><div class="common-content">' . $div1Base . '</div></div>';
                $content = str_replace($replace, '', $content);
            }

            preg_match_all('/<div class="common-content--border"><div class="common-content">([\s\S]*?)<\/div><\/div>/i',
                $content, $matchesTwo);
            foreach ($matchesTwo[1] as $matchTwo) {
                preg_match_all('/<h5[^>]*([\s\S]*?)<\/h5>/i', $matchTwo, $matchTwoItem);
                foreach ($matchTwoItem[0] as $matchTwoItemKey => $matchTwoItemValue) {
                    // 去掉html
                    $txt = strip_tags($matchTwoItemValue);
                    $h2  = '';
                    if ($matchTwoItemKey != 0) {
                        $h2 .= '</div></div><div class="common-content--border"><div class="common-content">';
                    }
                    //                    $h2      .= '<div class="common-content-tittle"><div class="common-content-sort">' . (($matchTwoItemKey + 1 < 10) ? '0' . ($matchTwoItemKey + 1) : $matchTwoItemKey + 1) . '</div> <div class="common-content-name">' . $txt . '</div></div>';
                    $h2      .= '<div class="common-content-tittle"><div class="common-content-sort">&nbsp;</div><div class="common-content-name">' . $txt . '</div></div>';
                    $content = str_replace($matchTwoItemValue, $h2, $content);
                }
                unset($matchTwoItem);
            }
        } else {
            $position = strpos($content, '<img');
            $txt      = strip_tags($content);
            if ($txt || $position !== false) {
                $content = '<div class="common-wrapper wow animate__animated animate__fadeInUp animated"><div class="common-content--border"><div class="common-content">' . $content . '</div></div></div>';
            }
        }

        $content = str_replace('<img', '<img class="rich-img"', $content);
        // 再做一个保底，去掉所有没有用的空内容，只留标题
        $content = str_replace('<div class="common-content--border"><div class="common-content"></div></div>', '',
            $content);

        return $content;
    }

    /**
     * 处理富文本有两级标题
     * @param $content
     * @return string
     */
    public static function formatContentV3($content)
    {
        if (empty($content)) {
            return '';
        }
        preg_match_all('/<h4[^>]*([\s\S]*?)<\/h4>/i', $content, $matchesOne);

        if ($matchesOne[0]) {
            // 每两个h5标签之间的内容用一个div=common-wrapper包裹
            foreach ($matchesOne[0] as $matchOne) {
                // 去掉html
                $txt = strip_tags($matchOne);
                // 获取所有的$labelTwo标签
                $h1      = '</div></div></div><div class="common-wrapper wow animate__animated animate__fadeInUp animated"><div class="common-title-content"><div class="common-title"><span>' . $txt . '</span></div></div><div class="common-content--border"><div class="common-content">';
                $content = str_replace($matchOne, $h1, $content);
            }

            $content = '<div class="common-wrapper wow animate__animated animate__fadeInUp animated"><div class="common-content--border"><div class="common-content">' . $content . '</div></div></div>';
            $content = str_replace('<div class="common-wrapper wow animate__animated animate__fadeInUp animated"><div class="common-content--border"><div class="common-content"></div></div></div>',
                '', $content);
            // 找到第一个common-content里面的内容
            preg_match_all('/<div class="common-content">([\s\S]*?)<\/div>/i', $content, $matches);
            $div1Base = $matches[1][0];

            // 去掉里面全部的html标签(除了img)
            $div1 = strip_tags($div1Base, '<img>');
            $div1 = str_replace(' ', '', $div1);
            if (empty($div1)) {
                $replace = '<div class="common-wrapper"><div class="common-content">' . $div1Base . '</div></div>';
                $content = str_replace($replace, '', $content);
            }

            preg_match_all('/<div class="common-content--border"><div class="common-content">([\s\S]*?)<\/div><\/div>/i',
                $content, $matchesTwo);
            foreach ($matchesTwo[1] as $matchTwo) {
                preg_match_all('/<h5[^>]*([\s\S]*?)<\/h5>/i', $matchTwo, $matchTwoItem);
                foreach ($matchTwoItem[0] as $matchTwoItemKey => $matchTwoItemValue) {
                    // 去掉html
                    $txt = strip_tags($matchTwoItemValue);
                    $h2  = '';
                    if ($matchTwoItemKey != 0) {
                        $h2 .= '</div></div><div class="common-content--border"><div class="common-content">';
                    }
                    //                    $h2      .= '<div class="common-content-tittle"><div class="common-content-sort">' . (($matchTwoItemKey + 1 < 10) ? '0' . ($matchTwoItemKey + 1) : $matchTwoItemKey + 1) . '</div> <div class="common-content-name">' . $txt . '</div></div>';
                    $h2      .= '<div class="common-content-tittle"><div class="common-content-name">' . $txt . '</div></div>';
                    $content = str_replace($matchTwoItemValue, $h2, $content);
                }
                unset($matchTwoItem);
            }
        } else {
            $position = strpos($content, '<img');
            $txt      = strip_tags($content);
            if ($txt || $position !== false) {
                $content = '<div class="common-wrapper wow animate__animated animate__fadeInUp animated"><div class="common-content--border"><div class="common-content">' . $content . '</div></div></div>';
            }
        }

        $content = str_replace('<img', '<img class="rich-img"', $content);
        // 再做一个保底，去掉所有没有用的空内容，只留标题
        $content = str_replace('<div class="common-content--border"><div class="common-content"></div></div>', '',
            $content);

        return $content;
    }

    /**
     * 小程序--处理富文本有两级标题
     * @param $content
     * @return string
     */
    public static function formatContentMiniV2($content)
    {
        if (empty($content)) {
            return '';
        }
        preg_match_all('/<h4[^>]*([\s\S]*?)<\/h4>/i', $content, $matchesOne);
        if ($matchesOne[0]) {
            // 每两个h5标签之间的内容用一个div=common-wrapper包裹
            foreach ($matchesOne[0] as $key => $matchOne) {
                // 去掉html
                $txt = strip_tags($matchOne);
                // 获取所有的$labelTwo标签
                $h1      = '</view></view><view class="common-wrapper"><view class="common-title-content"><view class="common-title"><text class="title">' . $txt . '</text></view></view><view class="common-content">';
                $content = str_replace($matchOne, $h1, $content);
            }

            $content = '<view class="common-wrapper"><view class="common-content">' . $content . '</view></view>';
            $content = str_replace('<view class="common-wrapper"><view class="common-content"></view></view>', '',
                $content);

            preg_match_all('/<view class="common-content">([\s\S]*?)<\/view>/i', $content, $matchesTwo);
            foreach ($matchesTwo[1] as $key => $matchTwo) {
                preg_match_all('/<h5[^>]*([\s\S]*?)<\/h5>/i', $matchTwo, $matchTwoItem);
                if (!$matchTwoItem[0]) {
                    if ($key == 0) {
                        $contentArr = explode($matchTwo, $content);
                        if (strpos($contentArr[0], 'class="title"') === false) {
                            $content = str_replace($matchTwo,
                                '<view class="common-content-text">' . $matchTwo . '</view>', $content);
                            continue;
                        } else {
                            $content = str_replace($matchTwo,
                                '<view class="common-content-text pd-50">' . $matchTwo . '</view>', $content);
                            continue;
                        }
                    } else {
                        $content = str_replace($matchTwo,
                            '<view class="common-content-text pd-50">' . $matchTwo . '</view>', $content);
                        continue;
                    }
                }
                foreach ($matchTwoItem[0] as $matchTwoItemKey => $matchTwoItemValue) {
                    // 处理一种特殊情况
                    // 第一个元素进来看一下整个内容里面
                    $isPd30 = true;
                    if ($matchTwoItemKey == 0) {
                        //看一下第一个元素前面是否有内容
                        $contentArr = explode($matchTwoItemValue, $matchTwo);
                        //第一个元素去标签是否为空
                        $elementContent = strip_tags($contentArr[0]);
                        //有内容
                        if ($elementContent) {
                            $h2Text  = '<view class="common-second-wrapper not-bg pd-50">' . $elementContent . '</view>';
                            $content = str_replace($contentArr[0], $h2Text, $content);
                            $isPd30  = false;
                        }
                    }
                    // 去掉html
                    $txt = strip_tags($matchTwoItemValue);
                    $h2  = '';
                    if ($matchTwoItemKey != 0) {
                        $h2 .= '</view></view>';
                    }
                    //                    $h2      .= '<view class="common-second-wrapper"><view class="common-second-title ' . ($matchTwoItemKey == 0 && $isPd30 ? 'pd-30' : '') . '">' . (($matchTwoItemKey + 1 < 10) ? '0' . ($matchTwoItemKey + 1) : $matchTwoItemKey + 1) . $txt . '</view><view class="common-second-content">';
                    $h2      .= '<view class="common-second-wrapper"><view class="common-second-title ' . ($matchTwoItemKey == 0 && $isPd30 ? 'pd-30' : '') . '">' . $txt . '</view><view class="common-second-content">';
                    $content = str_replace($matchTwoItemValue, $h2, $content);
                }
                unset($matchTwoItem);
            }
        }
        $content = str_replace('<img', '<img class="rich-img"', $content);
        $content = str_replace('<view class="common-content"></view>', '', $content);
        $content = str_replace('<view class="common-content"><view class="common-second-wrapper"></view></view>', '',
            $content);
        $content = str_replace('<view class="common-content"><view class="common-content-text"></view></view>', '',
            $content);
        $content = str_replace('<view class="common-content"><view class="common-content-text pd-50"></view></view>',
            '', $content);

        return $content;
    }

    /**
     * 小程序--处理富文本有两级标题
     * @param $content
     * @return string
     */
    //    public static function formatContentMiniV22($content)
    //    {
    //        if (empty($content)) {
    //            return '';
    //        }
    //        preg_match_all('/<h4[^>]*([\s\S]*?)<\/h4>/i', $content, $matchesOne);
    //        if ($matchesOne[0]) {
    //            // 每两个h5标签之间的内容用一个div=common-wrapper包裹
    //            foreach ($matchesOne[0] as $matchOne) {
    //                // 去掉html
    //                $txt = strip_tags($matchOne);
    //                // 获取所有的$labelTwo标签
    //                $h1      = '</view></view></view><view class="common-wrapper"><view class="common-title-content"><view class="common-title"><text class="title">' . $txt . '</text></view></view><view class="common-content">';
    //                $content = str_replace($matchOne, $h1, $content);
    //            }
    //
    //            $content = '<view class="common-wrapper"><view class="common-title-content"><view class="common-title">' . $content . '</view></view></view>';
    //            $content = str_replace('<view class="common-wrapper"><view class="common-title-content"><view class="common-title"></view></view></view>',
    //                '', $content);
    //            preg_match_all('/<view class="common-content">([\s\S]*?)<\/view>/i', $content, $matchesTwo);
    //            foreach ($matchesTwo[1] as $matchTwo) {
    //                preg_match_all('/<h5[^>]*([\s\S]*?)<\/h5>/i', $matchTwo, $matchTwoItem);
    //                if (!$matchTwoItem[0]) {
    //                    $content = str_replace($matchTwo, '<view class="common-second-wrapper">' . $matchTwo . '</view>',
    //                        $content);
    //                    continue;
    //                }
    //                foreach ($matchTwoItem[0] as $matchTwoItemKey => $matchTwoItemValue) {
    //                    // 去掉html
    //                    $txt = strip_tags($matchTwoItemValue);
    //                    $h2  = '';
    //                    if ($matchTwoItemKey != 0) {
    //                        $h2 .= '</view></view>';
    //                    }
    //                    $h2      .= '<view class="common-second-wrapper"><view class="common-second-title">' . (($matchTwoItemKey + 1 < 10) ? '0' . ($matchTwoItemKey + 1) : $matchTwoItemKey + 1) . $txt . '</view><view class="common-second-content">';
    //                    $content = str_replace($matchTwoItemValue, $h2, $content);
    //                }
    //                unset($matchTwoItem);
    //            }
    //        }
    //        $content = str_replace('<img', '<img class="rich-img"', $content);
    //
    //        return $content;
    //    }

    /**
     * 获取当前域名
     * @return string
     */
    public static function getDomain()
    {
        $url = 'https://zhaopinhui.gaoxiaojob.com';
        if (Yii::$app->params['environment'] != 'prod') {
            $urlLocal = 'https://zhaopinhui.' . Yii::$app->params['pcHost'];

            // 去掉www.
            $urlLocal = str_replace('www.', '', $urlLocal);
            $url      = str_replace('https://zhaopinhui.gaoxiaojob.com', $urlLocal, $url);
        }

        return $url;
    }

    /**
     * 获取页面链接
     * @param $specialLink
     * @return string
     */
    public static function getSpecialLink($specialLink, $id = 0)
    {
        if (empty($specialLink)) {
            $specialDetail = BaseHwSpecialActivity::findOne($id);
            $specialLink   = $specialDetail->special_link;
        }

        return BaseService::getDomain() . '/zhuanchang/' . $specialLink . '.html';
    }
}