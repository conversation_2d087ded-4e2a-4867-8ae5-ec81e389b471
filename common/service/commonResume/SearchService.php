<?php

namespace common\service\commonResume;

// 查询服务
use common\base\BaseActiveRecord;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCertificate;
use common\base\models\BaseChatRoom;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyCollect;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseCompanyResumeLibrary;
use common\base\models\BaseCompanyResumePvTotal;
use common\base\models\BaseCompanyViewResume;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyHandleLog;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseMemberLoginForm;
use common\base\models\BaseMemberMessage;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAcademicBook;
use common\base\models\BaseResumeAcademicPage;
use common\base\models\BaseResumeAcademicPatent;
use common\base\models\BaseResumeCertificate;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeIntention;
use common\base\models\BaseResumeLibrary;
use common\base\models\BaseResumeLibraryCollect;
use common\base\models\BaseResumeLibraryDownloadLog;
use common\base\models\BaseResumeLibraryInviteLog;
use common\base\models\BaseResumeOtherSkill;
use common\base\models\BaseResumeResearchDirection;
use common\base\models\BaseResumeResearchProject;
use common\base\models\BaseResumeSetting;
use common\base\models\BaseResumeSkill;
use common\base\models\BaseResumeTopConfig;
use common\base\models\BaseResumeWork;
use common\base\models\BaseShieldCompany;
use common\base\models\BaseSkill;
use common\base\models\BaseSystemConfig;
use common\helpers\DebugHelper;
use common\helpers\FileHelper;
use common\helpers\InterlacePageHelper;
use common\helpers\MaskHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\libs\Cache;
use common\libs\CompanyAuthority\CompanyAuthorityClassify;
use common\libs\WxWork;
use common\models\ResumeLibraryCollect;
use common\service\companyAuth\ButtonGroupAuthService;
use common\service\meilisearch\resume\ResumeLibraryDocumentService;
use common\service\resume\ResumeCacheService;
use common\service\resume\ResumeLibraryService;
use common\service\resumeRemind\ActionService;
use common\service\resumeRemind\ResumeRemindApplication;
use frontendPc\models\Dictionary;
use phpDocumentor\Reflection\DocBlock\Tags\Reference\Reference;
use phpDocumentor\Reflection\Types\Self_;
use Yii;
use yii\base\Exception;
use yii\db\Expression;

class SearchService extends BaseService
{
    //搜索人才列表最多返回的数量
    const MAX_RESUME_LIBRARY_LIST_COUNT = 2000;

    private $searchParams;
    private $data;

    /**
     * 获取免费会员的提示
     * 高级会员普通子账号提示
     * @return string[]
     */
    public function getFreeTipsInfo($companyId)
    {
        $this->setData(['companyId' => $companyId]);
        $companyPackageType = $this->companyModel->package_type;
        if (!$companyPackageType) {
            throw new \Exception('单位会员类型不存在');
        }
        if ($companyPackageType == BaseCompany::PACKAGE_TYPE_SENIOR) {
            $memberId   = Yii::$app->user->id;
            $memberRule = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'member_rule');
            if ($memberRule == BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_NORMAL) {
                $data = [
                    'content'    => '<p>当前账号暂无相关权限！请向单位管理员申</p><p>请授权，或联系专属客服升级账号权益，享受超值</p>人才服务！咨询热线：020-85611139</p>',
                    'connectUrl' => 'http://www.gaoxiaojob.com/zhaopin/aboutUs/productService.html',
                    'qq'         => '2881224205',
                    'title'      => '升级享权益',
                ];

                return $data;
            }

            //throw new \Exception('高级会员无需提示');
            return [];
        }

        $packTypeName = BaseCompany::PACKAGE_TYPE_LIST[$companyPackageType];
        //暂时写死，也不知道会不会改动
        $data = [
            'content'    => '<p>您当前是' . $packTypeName . '，暂不支持此操作。</p><p>升级高级会员，可享受超值人才服务！</p><p>咨询热线：020-85611139</p>',
            'connectUrl' => 'http://www.gaoxiaojob.com/zhaopin/aboutUs/productService.html',
            'qq'         => '2881224205',
            'title'      => '升级享权益',
        ];

        return $data;
    }

    public function getChatTipsInfo($companyId)
    {
        $this->setData(['companyId' => $companyId]);
        $companyPackageType = $this->companyModel->package_type;
        if (!$companyPackageType) {
            throw new \Exception('单位会员类型不存在');
        }
        if ($companyPackageType == BaseCompany::PACKAGE_TYPE_SENIOR) {
            $memberId   = Yii::$app->user->id;
            $memberRule = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'member_rule');
            if ($memberRule == BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_NORMAL) {
                $data = [
                    'content'    => '<p>当前账号暂相关权限！请向单位管理员申</p><p>请授权，或联系专属客服升级账号权益，享受超值</p>人才服务！咨询热线：020-85611139</p>',
                    'connectUrl' => 'http://www.gaoxiaojob.com/zhaopin/aboutUs/productService.html',
                    'qq'         => '2881224205',
                    'title'      => '升级享权益',
                ];

                return $data;
            }
            throw new \Exception('高级会员无需提示');
        }

        $packTypeName = BaseCompany::PACKAGE_TYPE_LIST[$companyPackageType];
        //暂时写死，也不知道会不会改动
        $data = [
            'content'    => '<p>您当前是' . $packTypeName . '，暂不支持此操作。</p><p>升级高级会员，可享受超值人才服务！</p><p>咨询热线：020-85611139</p>',
            'connectUrl' => 'http://www.gaoxiaojob.com/zhaopin/aboutUs/productService.html',
            'qq'         => '2881224205',
            'title'      => '升级享权益',
        ];

        return $data;
    }

    private function setData($data)
    {
        $this->actionType = self::ACTION_TYPE_SEARCH;

        $companyId = $data['companyId'];
        if (!$companyId) {
            throw new \Exception('单位id为空');
        }

        $this->setCompany($companyId);
        $this->setPackage();
    }

    /**
     * @throws \yii\base\Exception
     */
    public function getList($params)
    {
        // BaseActiveRecord::openDb2();
        $this->setData($params);
        $this->searchParams = $params;
        $companyId          = $params['companyId'];

        if (!$this->searchParams['rule_bool']) {
            //由于免费会员也可以进行筛选，这里不做抽离了，限制其筛选结果
            $this->searchParams['page']     = $this->searchParams['page'] <= 3 ? $this->searchParams['page'] : 3;
            $this->searchParams['pageSize'] = $this->searchParams['pageSize'] > 0 ? $this->searchParams['pageSize'] : \Yii::$app->params['resumeLibraryDefaultPageSize'];
        }
        $search_params_key = [
            'keyword',
            'educationId',
            'majorId',
            'residenceId',
            'cityId',
            'identityType',
            'jobCategoryId',
            'workStatus',
            'arriveDateType',
            'workExperience',
            'workExperienceMin',
            'workExperienceMax',
            'titleId',
            'isAbroad',
            'householdRegisterId',
            'isProjectSchool',
            'ageMin',
            'ageMax',
            'political',
            'gender',
            'updateBeginTime',
            'updateEndTime',
            'graduateBeginDate',
            'graduateEndDate',
            'isFollow',
            'hideInvite',
            'hideDownload',
            'hideApply',
            'hideChat',
            'directionContent',
            'boShiHouExperience',
            'educationEndDateStart',
            'educationEndDateEnd',
            'hideViewPast60Days',
            'hideViewLess65Percentage',
            //'isHideComplete',
        ];
        $isSearch          = false;
        foreach ($search_params_key as $value_key) {
            if (isset($this->searchParams[$value_key]) && mb_strlen($this->searchParams[$value_key]) > 0) {
                $isSearch = true;
                break;
            }
        }
        if ($isSearch) {
            //有搜索条件
            $result = $this->searchData();
        } else {
            //无搜索条件
            $result = $this->noSearchData();
            //求职者屏蔽了单位，简历要进行剔除
            $shield_resume_ids = BaseShieldCompany::find()
                ->where([
                    'company_id' => $this->searchParams['companyId'],
                    'status'     => BaseShieldCompany::STATUS_ACTIVE,
                ])
                ->select('resume_id')
                ->column();
            foreach ($result['list'] as $item) {
                $resume = BaseResume::findOne($item['resume_id']);
                if ($resume->is_resume_library == BaseResume::IS_RESUME_LIBRARY_NO || in_array($item['resume_id'],
                        $shield_resume_ids)) {
                    $cacheKey = Cache::COMPANY_RESUME_LIBRARY_SEARCH . ':' . $this->searchParams['page'] . '_' . $this->searchParams['pageSize'];
                    Cache::delete($cacheKey);
                    $result = $this->noSearchData();
                    break;
                }
            }
        }
        foreach ($result['list'] as $k => &$item) {
            //            if (isset($shield_resume_ids) && in_array($item['resume_id'], $shield_resume_ids)) {
            //                unset($result['list'][$k]);
            //                continue;
            //            }
            //            $resume = BaseResume::findOne($item['resume_id']);
            //            if ($resume->is_resume_library == BaseResume::IS_RESUME_LIBRARY_NO) {
            //                unset($result['list'][$k]);
            //                continue;
            //            }
            //读取求职者缓存信息
            $resumeInfo = ResumeCacheService::get($item['resume_id'], [
                ResumeCacheService::RESUME_CACHE_KEY_INFO,
                ResumeCacheService::RESUME_CACHE_KEY_TOP_EDUCATION,
                ResumeCacheService::RESUME_CACHE_KEY_EDUCATION,
                ResumeCacheService::RESUME_CACHE_KEY_WORK,
                ResumeCacheService::RESUME_CACHE_KEY_RESEARCH_DIRECTION,
            ]);
            if (isset($item['is_resume_top'])) {
                $item['is_resume_top_text'] = $item['is_resume_top'] == 2 ? '置顶' : '';
            } else {
                $item['is_resume_top']      = '1';
                $item['is_resume_top_text'] = '';
            }
            //补充信息
            $item['age']            = $resumeInfo['info']['age'];
            $item['gender']         = $resumeInfo['info']['gender'];
            $item['workExperience'] = $resumeInfo['info']['workExperience'];
            //名称脱敏
            $item['name'] = MaskHelper::getName($resumeInfo['info']['name'], $resumeInfo['info']['gender']);
            //获取最高学历
            $topEducation = $resumeInfo['topEducation']['educationName'];
            //获取最高学历的所学专业
            $topEducationMajor = $resumeInfo['topEducation']['majorName'];
            //拼接用户的信息：工作经验——学历——年龄——最高学历专业——居住地
            $topEducation      = $topEducation ?: '';
            $age               = $resumeInfo['info']['age'] ? $resumeInfo['info']['age'] . '岁' : '0岁';
            $topEducationMajor = $topEducationMajor ?: '';
            $userInfo          = [
                'age'               => $age,
                'topEducation'      => $topEducation,
                'topEducationMajor' => $topEducationMajor ?: $resumeInfo['topEducation']['majorCustom'],
            ];
            //身份年限获取
            $identityExperience = BaseResume::getIdentityExperienceText($item['resume_id']);
            if ($identityExperience) {
                $userInfo['identityExperience'] = $identityExperience;
            }
            $item['userInfo']       = implode('·', $userInfo);
            $item['resumeTitleTag'] = BaseResume::getUserSpecialInfo($item['resume_id']);
            //获取研究方向
            $item['researchContent'] = $resumeInfo['researchDirection']['content'];
            //获取头像
            $item['avatar'] = self::getAvatar($resumeInfo['info']['avatar'], $resumeInfo['info']['age']['gender']);
            //获取第一个求职意向，后面的用...表示
            $item['jobCategoryText'] = BaseResumeIntention::getResumeLibraryJobCategoryText($item['resume_id']);
            //$item['jobCategoryFullText'] = BaseResumeIntention::getAllJobCategoryText($item['resume_id']);
            //获取最近2段教育经历
            $item['lastTwoEducationRecord'] = array_splice($resumeInfo['education'], 0, 3);
            foreach ($item['lastTwoEducationRecord'] as &$education) {
                if ($education['endDate'] == TimeHelper::ZERO_DATE) {
                    $education['endDate'] = '至今';
                } else {
                    $education['endDate'] = TimeHelper::formatToYearMonth($education['endDate'], '.');
                }
                $education['major']     = $education['majorId'] == 0 ? $education['majorCustom'] : $education['majorName'];
                $education['beginDate'] = TimeHelper::formatToYearMonth($education['beginDate'], '.');
            }
            //获取最近1段工作经历,加个[0]转数组，方便前端使用
            $item['lastWorkRecord'] = array_splice($resumeInfo['work'], 0, 1);
            foreach ($item['lastWorkRecord'] as &$work) {
                if ($work['endDate'] == TimeHelper::ZERO_DATE) {
                    $work['endDate'] = '至今';
                } else {
                    $work['endDate'] = TimeHelper::formatToYearMonth($work['endDate'], '.');
                }
                $work['beginDate'] = TimeHelper::formatToYearMonth($work['beginDate'], '.');
            }
            //获取收藏状态
            $item['isCollect'] = BaseResumeLibraryCollect::checkMemberCollectStatus($item['resume_id'],
                $this->searchParams['memberId']);
            //获取下载状态
            $item['isDownload'] = BaseCompanyResumeLibrary::checkDownLoadStatus($item['resume_id'], $companyId);
            //获取邀约状态
            $item['isInvite'] = BaseResumeLibraryInviteLog::checkInviteStatus($item['resume_id'], $companyId);
            //获取应聘状态
            $item['isApply'] = BaseJobApply::checkApplyRecord($item['resume_id'], $companyId);
            //获取用户的标签
            $item['labelType'] = self::getResumeLabel($item['resume_id'], $companyId);
            //获取30天内是否被查看
            $item['is_resume_check'] = BaseCompanyViewResume::getCheckStatus($item['resume_id'], $companyId);
            //应聘或者下载了简历，即存在单位简历库的记录需要返回个字段供前端判断
            $companyResumeLibraryId         = BaseCompanyResumeLibrary::findOneVal([
                'resume_id'  => $item['resume_id'],
                'company_id' => $companyId,
            ], 'id');
            $item['isCompanyResumeLibrary'] = BaseCompanyResumeLibrary::HAS_RECORD_NO;
            $item['companyResumeLibraryId'] = $companyResumeLibraryId ?: 0;
            if (!empty($companyResumeLibraryId)) {
                $item['isCompanyResumeLibrary'] = BaseCompanyResumeLibrary::HAS_RECORD_YES;
            }
            //简历更新时间
            $item['last_update_time'] = TimeHelper::formatDateByYear($resumeInfo['info']['lastUpdateTime'], '/');
            //获取用户最后登陆时间
            $item['activeTime'] = BaseMember::getUserActiveTime($item['member_id']);
            //简历类型（暂时写死）
            $resumeLevelInfo        = self::getResumeLevel($item['resume_id']);
            $item['resumeTypeText'] = $resumeLevelInfo['name'];
            $item['resumeType']     = $resumeLevelInfo['type'];
            //获取简历完成度
            $item['resumePercent'] = intval($resumeInfo['info']['complete']);
            //做一个PV统计
            BaseCompanyResumePvTotal::updateDailyTotalPv($item['resume_id']);
            // 按钮组
            $item['buttonGroup'] = (new ButtonGroupAuthService())->setType(ButtonGroupAuthService::TYPE_RESUME)
                ->run($item['resume_id']);

            //            if (isset($item['is_resume_top'])) {
            //                $item['is_resume_top_text'] = $item['is_resume_top'] == 2 ? '置顶' : '';
            //            } else {
            //                $item['is_resume_top']      = '1';
            //                $item['is_resume_top_text'] = '';
            //            }
            //            //名称脱敏
            //            $item['name'] = MaskHelper::getName($item['name'], $item['gender']);
            //            //获取最高学历
            //            $topEducation = BaseDictionary::getEducationName($item['top_education_code']);
            //            //获取最高学历的所学专业
            //            $topEducationInfo  = BaseResumeEducation::findOne($item['last_education_id']);
            //            $topEducationMajor = BaseMajor::getMajorName($topEducationInfo->major_id);
            //            //拼接用户的信息：工作经验——学历——年龄——最高学历专业——居住地
            //            $topEducation      = $topEducation ?: '';
            //            $age               = $item['age'] ? $item['age'] . '岁' : '';
            //            $topEducationMajor = $topEducationMajor ?: '';
            //            $userInfo          = [
            //                'age'               => $age,
            //                'topEducation'      => $topEducation,
            //                'topEducationMajor' => $topEducationMajor ?: $topEducationInfo->major_custom,
            //            ];
            //            //身份年限获取
            //            $identityExperience = BaseResume::getIdentityExperienceText($item['resume_id']);
            //            if ($identityExperience) {
            //                $userInfo['identityExperience'] = $identityExperience;
            //            }
            //            $item['userInfo']       = implode('·', $userInfo);
            //            $item['resumeTitleTag'] = BaseResume::getUserSpecialInfo($item['resume_id']);
            //            //获取研究方向
            //            $research_info           = BaseResumeResearchDirection::getInfo($item['resume_id']);
            //            $item['researchContent'] = empty($research_info->content) ? '' : $research_info->content;
            //            //获取头像
            //            $avatar         = BaseMember::findOneVal(['id' => $item['member_id']], 'avatar');
            //            $item['avatar'] = self::getAvatar($avatar, $item['gender']);
            //            //获取第一个求职意向，后面的用...表示
            //            $item['jobCategoryText']     = BaseResumeIntention::getResumeLibraryJobCategoryText($item['resume_id']);
            //            $item['jobCategoryFullText'] = BaseResumeIntention::getAllJobCategoryText($item['resume_id']);
            //            //获取最近2段教育经历
            //            $item['lastTwoEducationRecord'] = BaseResumeEducation::getLastRecord($item['resume_id'], 3);
            //            // 获取最近1段工作经历,加个[0]转数组，方便前端使用
            //            $item['lastWorkRecord'] = BaseResumeWork::getLastRecord($item['resume_id'], 1);
            //            // 获取收藏状态
            //            $item['isCollect'] = BaseResumeLibraryCollect::checkMemberCollectStatus($item['resume_id'],
            //                $this->searchParams['memberId']);
            //            //获取下载状态
            //            $item['isDownload'] = BaseCompanyResumeLibrary::checkDownLoadStatus($item['resume_id'], $companyId);
            //            //获取邀约状态
            //            $item['isInvite'] = BaseResumeLibraryInviteLog::checkInviteStatus($item['resume_id'], $companyId);
            //            //获取应聘状态
            //            $item['isApply'] = BaseJobApply::checkApplyRecord($item['resume_id'], $companyId);
            //            //获取用户的标签
            //            $item['labelType'] = self::getResumeLabel($item['resume_id'], $companyId);
            //            //获取30天内是否被查看
            //            $item['is_resume_check'] = BaseCompanyViewResume::getCheckStatus($item['resume_id'], $companyId);
            //            //应聘或者下载了简历，即存在单位简历库的记录需要返回个字段供前端判断
            //            $companyResumeLibraryId         = BaseCompanyResumeLibrary::findOneVal([
            //                'resume_id'  => $item['resume_id'],
            //                'company_id' => $companyId,
            //            ], 'id');
            //            $item['isCompanyResumeLibrary'] = BaseCompanyResumeLibrary::HAS_RECORD_NO;
            //            if (!empty($companyResumeLibraryId)) {
            //                $item['isCompanyResumeLibrary'] = BaseCompanyResumeLibrary::HAS_RECORD_YES;
            //            }
            //            //简历更新时间
            //            $item['last_update_time'] = TimeHelper::formatDateByYear($item['last_update_time'], '/');
            //
            //            //获取用户最后登陆时间
            //            $item['activeTime'] = BaseMember::getUserActiveTime($item['member_id']);
            //            //简历类型（暂时写死）
            //            $resumeLevelInfo        = self::getResumeLevel($item['resume_id']);
            //            $item['resumeTypeText'] = $resumeLevelInfo['name'];
            //            $item['resumeType']     = $resumeLevelInfo['type'];
            //            //获取简历完成度
            //            $item['resumePercent'] = (int)BaseResume::getComplete($item['member_id']);
            //            array_push($ids, $item['resume_id']);
            //            //做一个PV统计
            //            BaseCompanyResumePvTotal::updateDailyTotalPv($item['resume_id']);
        }
        if (!$this->searchParams['rule_bool']) {
            //免费会员最多查看3页
            if ($result['page']['limit'] * 3 < $result['page']['count']) {
                $result['page']['count'] = $result['page']['limit'] * 3;
            }
        }

        //数组重置索引
        $result['list'] = array_values($result['list']);

        // BaseActiveRecord::closeDb2();

        return $result;
    }

    /**
     * 获取人才详情
     * @param $params
     * @return array|void
     */
    public function getDetail($params)
    {
        $this->setData($params);
        //        if ($this->isFreePackage) {
        //            //免费会员无查看权限
        //            return [];
        //        }
        $this->searchParams = $params;
        $this->actionType   = self::ACTION_TYPE_VIEW;
        $this->companyId    = $params['companyId'];
        $this->resumeId     = $params['resumeId'];
        $this->setResume($this->resumeId);

        $memberId   = $this->resumeModel->member_id;
        $resumeInfo = BaseResume::getInfo($memberId);

        //人才库这边要做特殊处理
        //手机、邮箱脱敏
        //        $resumeInfo['userInfo']['mobile'] = MaskHelper::getPhone($resumeInfo['userInfo']['mobile']);
        $resumeInfo['userInfo']['fullMobile'] = MaskHelper::getFullPhone($resumeInfo['userInfo']['mobileCode'],
            $resumeInfo['userInfo']['mobile']);
        unset($resumeInfo['userInfo']['mobile']);
        $resumeInfo['userInfo']['email'] = MaskHelper::getEmail($resumeInfo['userInfo']['email']);
        //姓名部分
        $resumeInfo['userInfo']['name'] = MaskHelper::getName($resumeInfo['userInfo']['name'],
            $resumeInfo['userInfo']['gender']);
        //头像
        if (!$resumeInfo['userInfo']['isDefaultAvatar']) {
            //不是默认头像，给个模糊处理
            $resumeInfo['userInfo']['avatar'] = MaskHelper::getImage($resumeInfo['userInfo']['avatar']);
        }

        //获取活跃度
        $resumeInfo['userInfo']['activeTime'] = BaseMember::getUserActiveTime($memberId);
        //获取简历的收藏状态
        $resumeInfo['isCollect'] = BaseResumeLibraryCollect::checkCollectStatus($this->resumeId, $this->companyId);

        //获取简历操作日志
        $logParams             = [
            'resumeId'  => $this->resumeId,
            'companyId' => $this->companyId,
            'pageSize'  => 10,
        ];
        $resumeInfo['logList'] = $this->getHandleLog($logParams)['list'];

        // 之前的消息中心并入这里
        BaseCompanyViewResume::create($this->companyId, $this->resumeId);

        // $this->sendMessage();

        $this->log();

        return $resumeInfo;
    }

    public function getHandleLog($params)
    {
        $query    = BaseJobApplyHandleLog::find()
            ->where([
                'resume_id'  => $params['resumeId'],
                'company_id' => $params['companyId'],
            ]);
        $count    = $query->count();
        $pageSize = $params['pageSize'] ?: \Yii::$app->params['handleLogDefaultPageSize'];

        $pages = BaseJobApplyHandleLog::setPage($count, $params['page'], $pageSize);
        $list  = $query->select([
            'id',
            'add_time',
            'job_apply_id',
            'handler_type',
            'handler_name',
            'handle_type',
            'title',
            'content',
            'handle_id',
        ])
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('add_time desc')
            ->asArray()
            ->all();

        foreach ($list as $k => $jobApplyLog) {
            $list[$k]['handle_type_title'] = $list[$jobApplyLog['handle_type']];
            //邀约人账号信息
            if ($jobApplyLog['handler_type'] == 2) {
                $companyMemberInfo = BaseCompanyMemberInfo::findOne(['member_id' => $jobApplyLog['handle_id']]);
                if ($companyMemberInfo) {
                    $list[$k]['contact']    = $companyMemberInfo['contact'];
                    $list[$k]['department'] = $companyMemberInfo['department'];
                } else {
                    $list[$k]['contact']    = $jobApplyLog['handler_name'];
                    $list[$k]['department'] = '';
                }
            }
        }

        return [
            'list' => $list,
            'page' => [
                'count' => intval($count),
                'limit' => intval($pages['limit']),
                'page'  => intval($pages['page']),
            ],
        ];
    }

    /**
     * @param $resumeId
     * 获取可以邀请的职位列表
     */
    public function getInviteJobList($companyId, $resumeId, $jobId)
    {
        $data = [
            'companyId' => $companyId,
        ];

        //        // 看看简历在不在人才库里面
        //        if (!BaseResumeLibrary::find()
        //            ->where(['resume_id' => $resumeId])
        //            ->exists()) {
        //            // 直接不允许邀请
        //            throw new \Exception('该人才不在人才库,暂不允许邀约');
        //
        //            return [];
        //        }

        $this->setData($data);

        //先做个简单判断，如果未发布职位，直接返回空
        $hasJobRecord = BaseJob::find()
            //->leftJoin(['b' => BaseAnnouncement::tableName()], 'b.id = job.announcement_id')
            ->where([
                'company_id' => $companyId,
                'status'     => BaseJob::STATUS_ACTIVE,
            ])
            ->asArray()
            ->one();
        if (empty($hasJobRecord)) {
            return [];
        } else {
            // 主要就是找在线职位,并且过去30天内没有邀请过该简历(时间格式是Y-m-d)
            // 首先找到该简历在30天内邀请过的职位id
            //            $inviteJobIds = BaseResumeLibraryInviteLog::find()
            //                ->where([
            //                    'resume_id'  => $resumeId,
            //                    'company_id' => $companyId,
            //                ])
            //                ->andWhere([
            //                    '>=',
            //                    'add_time',
            //                    date('Y-m-d', strtotime('-30 days')),
            //                ])
            //                ->select('job_id')
            //                ->asArray()
            //                ->column();

            // 然后再找所有我的在线职位过滤掉上面的那些职位id
            $query = BaseJob::find()
                ->alias('j')
                ->leftJoin(['b' => BaseAnnouncement::tableName()], 'b.id = j.announcement_id')
                ->where([
                    //'j.company_id' => $companyId,
                    'j.status' => BaseJob::STATUS_ACTIVE,
                ]);
            //                ->andWhere([
            //                    'not in',
            //                    'j.id',
            //                    $inviteJobIds,
            //                ]);
            /** 这里协同职位影响*/
            $memberId      = Yii::$app->user->id;
            $authorityList = (new CompanyAuthorityClassify())->run([
                'associatedField' => 'j.company_id',
                'memberId'        => $memberId,
                'query'           => $query,
                'returnType'      => CompanyAuthorityClassify::DATA_JOB_COOPERATE,
            ]);
            if ($authorityList) {
                $query = $authorityList['query'];
            }
            //获取该单位最近一次邀约的职位
            $inviteInfo = BaseResumeLibraryInviteLog::find()
                ->select([
                    'job_id',
                    'resume_id',
                    'add_time',
                ])
                ->where(['company_id' => $companyId])
                ->orderBy('add_time desc')
                ->asArray()
                ->one();
            $day_30     = strtotime('-30 days');
            //最近30天内所邀请的职位ID
            $inviteInfos30 = BaseResumeLibraryInviteLog::find()
                ->select([
                    'job_id',
                    'resume_id',
                ])
                ->where(['company_id' => $companyId])
                ->andWhere(['resume_id' => $resumeId])
                ->andWhere([
                    '>=',
                    'add_time',
                    date('Y-m-d 00:00:00', $day_30),
                ])
                ->asArray()
                ->all();
            $order         = [];
            // 1、列表所选中职位＞该账号最近一次邀约且在线的职位＞其他在线职位（按发布时间倒序展示）
            // 2、若为单个邀约（非批量邀约），则置底展示近30天已邀约过该人才的职位；职位卡片展示“30天内已邀约”标签。
            $jobIds30 = $inviteInfos30 ? array_column($inviteInfos30, 'job_id') : [];
            if ($jobId && !in_array($jobId, $jobIds30)) {
                $order[] = new Expression('FIELD(j.id,' . $jobId . ') desc');
            }
            if ($inviteInfo['job_id'] && !in_array($inviteInfo['job_id'], $jobIds30)) {
                $order[] = new Expression('FIELD(j.id,' . $inviteInfo['job_id'] . ') desc');
            }

            //            if ($inviteInfo && $inviteInfo['resume_id'] == $resumeId && $day_30 < strtotime($inviteInfo['add_time'])) {
            //                if ($jobId && $jobId != $inviteInfo['job_id']) {
            //                    $order[] = new Expression('FIELD(j.id,' . $jobId . ') desc');
            //                }
            //
            //                if ($inviteInfo['job_id']) {
            //                    $order[] = new Expression('FIELD(j.id,' . $inviteInfo['job_id'] . ') asc');
            //                }
            //            } else {
            //                if ($jobId) {
            //                    $order[] = new Expression('FIELD(j.id,' . $jobId . ') desc');
            //                }
            //
            //                if ($inviteInfo['job_id']) {
            //                    $order[] = new Expression('FIELD(j.id,' . $inviteInfo['job_id'] . ') desc');
            //                }
            //            }
            if (count($jobIds30) > 0) {
                $order[] = new Expression('FIELD(j.id,' . implode(',', $jobIds30) . ') asc');
            }
            $order['j.refresh_time'] = SORT_DESC;

            $list = $query->select([
                'j.id',
                'j.name',
                'j.education_type',
                'j.city_id',
                'j.name',
                'b.title',
            ])
                ->orderBy($order)
                ->asArray()
                ->all();
            if (count($list) == 0) {
                throw new \Exception('暂无可邀请职位');
            }
            foreach ($list as &$item) {
                $item['educationName'] = BaseDictionary::getEducationName($item['education_type']);
                $item['cityName']      = BaseArea::getAreaName($item['city_id']);
                $item['tag']           = '';
                foreach ($inviteInfos30 as $in) {
                    if ($in['resume_id'] == $resumeId && $in['job_id'] == $item['id']) {
                        $item['tag'] = '30天内已邀约';
                    }
                }
            }

            return [
                'jobList' => $list,
            ];
        }
    }

    /**
     * 邀约职位检查
     */
    public function getInviteJobCheck($companyId, $resumeIds, $jobId)
    {
        $configDetail       = BaseCompanyPackageConfig::getCompanyPackageConfigDetail($companyId);
        $inviteSourceAmount = $configDetail['invite_source_amount'];    // 剩余邀约次数
        $smsAmount          = $configDetail['sms_amount'];  // 剩余短信条数

        $resumeArr      = explode(',', $resumeIds);
        $resumeAmount   = count($resumeArr);
        $notAllowAmount = 0;
        foreach ($resumeArr as $resumeId) {
            // 看看简历在不在人才库里面
            if (!BaseResume::find()
                    ->andWhere([
                        'id'                => $resumeId,
                        'is_resume_library' => BaseResume::IS_RESUME_LIBRARY_YES,
                    ])
                    ->exists() || BaseResumeLibraryInviteLog::find()
                    ->where([
                        'company_id' => $companyId,
                        'job_id'     => $jobId,
                        'resume_id'  => $resumeId,
                    ])
                    ->andWhere([
                        '>=',
                        'add_time',
                        date('Y-m-d H:i:s', (time() - 30 * 24 * 3600)),
                    ])
                    ->exists()) {
                // 直接不允许邀请
                $notAllowAmount++;
            }
        }

        $noticeTips = '30天内已邀约或人才已退出人才库。';
        // 1）若N=被邀约候选人数量，展示提示文案：* 以上候选人不符合邀约条件，请重新选择候选人
        // 2）若N＜被邀约候选人数量，展示提示文案：* 有N位候选人不符合邀约条件，将为您自动过滤
        $canAllowAmout = $resumeAmount - $notAllowAmount;
        if ($resumeAmount == $notAllowAmount) {
            $canAllowAmout = 0;
            $notice        = $notAllowAmount > 0 ? '* 候选人均已邀约过/退出人才库，不符合邀约条件，请重新选择候选人' : '';
        } else {
            $canAllowAmout = $canAllowAmout > $inviteSourceAmount ? $inviteSourceAmount : $canAllowAmout;
            $notice        = $notAllowAmount > 0 ? '* ' . $notAllowAmount . '位30天内已邀约过该职位/退出人才库，将为您智能过滤；您当前还可邀约' . $canAllowAmout . '位' : '';
        }

        return [
            'notice'     => $notice ?? '',
            'noticeTips' => $noticeTips,
            'noticeData' => [
                // 剩余短信数量
                'smsAmount'      => $smsAmount,
                // 可发送数量
                'canAllowAmout'  => $canAllowAmout,
                'notAllowAmount' => $notAllowAmount,
                'allNotAllow'    => $resumeAmount == $notAllowAmount ? 1 : 0,
            ],
        ];
    }

    /**
     * 批量邀约职位列表
     * @param $companyId
     * @param $resumeId
     * @param $jobId
     * @return array|\yii\db\ActiveRecord[]
     * @throws Exception
     */
    public function getInviteBatchJobList($companyId, $resumeIds, $jobId)
    {
        $data = [
            'companyId' => $companyId,
        ];

        $this->setData($data);
        $notice     = '';
        $noticeTips = '';
        if ($jobId > 0) {
            $res        = $this->getInviteJobCheck($companyId, $resumeIds, $jobId);
            $notice     = $res['notice'];
            $noticeTips = $res['noticeTips'];
            $noticeData = $res['noticeData'];
        }

        //先做个简单判断，如果未发布职位，直接返回空
        $hasJobRecord = BaseJob::find()
            //->leftJoin(['b' => BaseAnnouncement::tableName()], 'b.id = job.announcement_id')
            ->where([
                'company_id' => $companyId,
                'status'     => BaseJob::STATUS_ACTIVE,
            ])
            ->asArray()
            ->one();
        if (empty($hasJobRecord)) {
            return [];
        } else {
            // 然后再找所有我的在线职位过滤掉上面的那些职位id
            $memberId = Yii::$app->user->id;
            $query    = BaseJob::find()
                ->alias('j')
                ->leftJoin(['b' => BaseAnnouncement::tableName()], 'b.id = j.announcement_id')
                ->where([
                    'j.status' => BaseJob::STATUS_ACTIVE,
                ]);
            /** 这里协同职位影响*/
            $authorityList = (new CompanyAuthorityClassify())->run([
                'associatedField' => 'j.company_id',
                'memberId'        => $memberId,
                'query'           => $query,
                'returnType'      => CompanyAuthorityClassify::DATA_JOB_COOPERATE,
            ]);
            if ($authorityList) {
                $query = $authorityList['query'];
            }
            //获取该单位最近一次邀约的职位
            $inviteInfo = BaseResumeLibraryInviteLog::find()
                ->select([
                    'job_id',
                    'resume_id',
                    'add_time',
                ])
                ->where(['company_id' => $companyId])
                ->orderBy('add_time desc')
                ->asArray()
                ->one();

            $order = [];
            // 1、列表所选中职位＞该账号最近一次邀约且在线的职位＞其他在线职位（按发布时间倒序展示）
            if ($jobId) {
                $order[] = new Expression('FIELD(j.id,' . $jobId . ') desc');
            }

            if ($inviteInfo['job_id']) {
                $order[] = new Expression('FIELD(j.id,' . $inviteInfo['job_id'] . ') desc');
            }
            $order['j.refresh_time'] = SORT_DESC;
            $list                    = $query->select([
                'j.id',
                'j.name',
                'j.education_type',
                'j.city_id',
                'j.name',
                'b.title',
            ])
                ->orderBy($order)
                ->asArray()
                ->all();

            if (count($list) == 0) {
                throw new \Exception('暂无可邀请职位');
            }
            foreach ($list as &$item) {
                $item['educationName'] = BaseDictionary::getEducationName($item['education_type']);
                $item['cityName']      = BaseArea::getAreaName($item['city_id']);
                $item['tag']           = '';
            }

            return [
                'jobList'    => $list,
                'notice'     => $notice ?? '',
                'noticeTips' => $noticeTips,
                'noticeData' => $noticeData ?? [],
            ];
        }
    }

    /**
     * 获取求职者的邀请投递列表
     * @param $params
     * @return array|\yii\db\ActiveRecord[]
     */
    public function getPersonInviteJobList($params)
    {
        $query = BaseResumeLibraryInviteLog::find()
            ->alias('il')
            ->leftJoin(['j' => BaseJob::tableName()], 'j.id=il.job_id')
            ->where(['resume_id' => $params['resumeId']])
            ->andWhere([
                'j.status' => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ]);

        $count = $query->count();

        $pageSize = $params['pageSize'] ?: \Yii::$app->params['defaultPageSize'];
        $pages    = BaseActiveRecord::setPage($count, $params['page'], $pageSize);

        $list = $query->select([
            'il.id as logId',
            'il.company_id',
            'il.job_id',
            'j.name as jobName',
            'il.add_time',
            'j.status as jobStatus',
            'j.release_time',
            'j.province_id',
            'j.city_id',
        ])
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('jobStatus desc,il.add_time desc')
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            //先判断职位状态
            if ($item['jobStatus'] == BaseJob::STATUS_OFFLINE) {
                //已经下线
                $item['status']     = BaseResumeLibraryInviteLog::STATUS_OFFLINE;
                $item['statusText'] = '已下线';
            } elseif ($item['jobStatus'] == BaseJob::STATUS_ONLINE) {
                //如果职位状态正常，7天内没有任何投递记录，就可以申请操作，也就是未申请状态
                //查看有没有投递记录
                $applyRecord = BaseJobApply::checkJobApplyStatus($params['memberId'], $item['job_id']);
                if ($applyRecord == BaseJob::JOB_APPLY_STATUS_YES) {
                    //已经申请了
                    $item['status']     = BaseResumeLibraryInviteLog::STATUS_APPLY;
                    $item['statusText'] = '已申请';
                } elseif ($applyRecord == BaseJob::JOB_APPLY_STATUS_NO) {
                    //还未申请
                    $item['status']     = BaseResumeLibraryInviteLog::STATUS_UNAPPLY;
                    $item['statusText'] = '申请';
                }
            }
            //获取职位url
            $item['jobUrl'] = BaseJob::getDetailUrl($item['job_id']);
            //获取单位名称和url
            $item['companyName'] = BaseCompany::findOneVal(['id' => $item['company_id']], 'full_name');
            $item['companyUrl']  = BaseCompany::getDetailUrl($item['company_id']);
            //拼接省市
            $item['city']         = BaseArea::getAreaName($item['province_id']) . '-' . BaseArea::getAreaName($item['city_id']);
            $item['release_time'] = date('Y-m-d', strtotime($item['release_time']));
            $item['add_time']     = date('Y-m-d', strtotime($item['add_time']));
            unset($item['province_id']);
            unset($item['city_id']);
            unset($item['jobStatus']);
        }

        return [
            'list' => $list,
            'page' => [
                'count' => intval($count),
                'limit' => intval($pages['limit']),
                'page'  => intval($pages['page']),
            ],
        ];
    }

    /**
     * 获取个人中心页面的几条数据
     * @param $params
     * @return array|\yii\db\ActiveRecord[]
     */
    public function getPersonCenterInviteJobList($params)
    {
        $query = BaseResumeLibraryInviteLog::find()
            ->alias('il')
            ->leftJoin(['j' => BaseJob::tableName()], 'j.id=il.job_id')
            ->where(['resume_id' => $params['resumeId']])
            ->andWhere([
                'j.status' => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ]);

        $list = $query->select([
            'il.id as logId',
            'il.company_id',
            'il.job_id',
            'j.name as jobName',
            'il.add_time',
            'j.status as jobStatus',
        ])
            ->limit($params['limit'])
            ->orderBy('jobStatus desc,il.add_time desc')
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            //先判断职位状态
            if ($item['jobStatus'] == BaseJob::STATUS_OFFLINE) {
                //已经下线
                $item['status']     = BaseResumeLibraryInviteLog::STATUS_OFFLINE;
                $item['statusText'] = '已下线';
            } elseif ($item['jobStatus'] == BaseJob::STATUS_ONLINE) {
                //如果职位状态正常，7天内没有任何投递记录，就可以申请操作，也就是未申请状态
                //查看有没有投递记录
                $applyRecord = BaseJobApply::checkJobApplyStatus($params['memberId'], $item['job_id']);
                if ($applyRecord == BaseJob::JOB_APPLY_STATUS_YES) {
                    //已经申请了
                    $item['status']     = BaseResumeLibraryInviteLog::STATUS_APPLY;
                    $item['statusText'] = '已申请';
                } elseif ($applyRecord == BaseJob::JOB_APPLY_STATUS_NO) {
                    //还未申请
                    $item['status']     = BaseResumeLibraryInviteLog::STATUS_UNAPPLY;
                    $item['statusText'] = '申请';
                }
            }
            //获取职位url
            $item['jobUrl'] = BaseJob::getDetailUrl($item['job_id']);
            //获取单位名称和url
            $item['companyName'] = BaseCompany::findOneVal(['id' => $item['company_id']], 'full_name');
            $item['companyUrl']  = BaseCompany::getDetailUrl($item['company_id']);
            $item['add_time']    = date('Y-m-d', strtotime($item['add_time']));
            unset($item['company_id']);
            unset($item['jobStatus']);
        }

        return $list;
    }

    /**
     * @throws \yii\base\Exception
     */
    public function getCollectList($params)
    {
        $companyId = $params['companyId'];

        //        if ($this->isFreePackage) {
        //            return $this->getFreeList();
        //        }
        $query = BaseResumeLibraryCollect::find()
            ->alias('rc')
            ->leftJoin(['r' => BaseResume::tableName()], 'r.id=rc.resume_id')
            ->where(['rc.member_id' => Yii::$app->user->id]);
        //->where(['rc.company_id' => $companyId]);

        //关键词查询
        if (!empty($params['keyword'])) {
            //关键词搜索支持以下字段模糊搜索：
            //【教育经历】-学校名称、
            //【研究方向】、
            //【工作经历】-单位名称/职位名称/工作内容、
            //【科研项目】-项目名称/所属单位/项目描述、
            //【学术论文】-论文题目/论文描述、
            //【学术专利】-专利名称/专利描述、
            //【学术专著】-著作名称、
            //【资质证书】-证书名称、
            //【技能/语言】-技能/语言名称、
            //【个人优势】
            //暂时通过一个方法去联表取出resumeId
            $resumeIds = BaseResume::getIdsByKeyword($params['keyword']);
            //这里不管有没有resumeId，都是查询过的结果，必须andWhere进去
            $query->andWhere(['r.id' => $resumeIds]);
        }

        //最高学历
        if (!empty($params['educationId'])) {
            // 数组
            $educationIdArr = explode(',', $params['educationId']);
            $query->andWhere(['r.top_education_code' => $educationIdArr]);
        }

        //教育表内查询
        if (!empty($params['majorId'])) {
            if (!empty($params['educationId'])) {
                $query->leftJoin(['e' => BaseResumeEducation::tableName()], 'e.id=r.last_education_id');
            } else {
                $query->leftJoin(['e' => BaseResumeEducation::tableName()], 'e.resume_id=r.id');
            }
            //专业是多选二级，要先查到3级的数据
            $majorList = explode(',', $params['majorId']);
            $query->andWhere([
                'e.major_id' => $majorList,
                'e.status'   => BaseResumeEducation::STATUS_ACTIVE,
            ]);
        }

        //海外经历
        if (!empty($params['isAbroad'])) {
            $query->andWhere(['r.is_abroad' => $params['isAbroad']]);
        }

        //现居住地查询
        if (!empty($params['residenceId'])) {
            $query->andWhere(['r.residence' => explode(',', $params['residenceId'])]);
        }

        //求职意向查询
        if (!empty($params['cityId']) || !empty($params['jobCategoryId']) || !empty($params['wageId'])) {
            //意向城市、求职意向、薪资待遇
            $query->leftJoin(['i' => BaseResumeIntention::tableName()], 'i.resume_id=rc.resume_id');
            $query->andWhere(['i.status' => BaseResumeIntention::STATUS_ACTIVE]);

            if (!empty($params['cityId'])) {
                $cityArr   = explode(',', $params['cityId']);
                $cityQuery = BaseActiveRecord::formatFindInSetQuery($cityArr, 'i.area_id');
                $query->andWhere($cityQuery);
            }

            if (!empty($params['jobCategoryId'])) {
                $jobCategoryArray = explode(',', $params['jobCategoryId']);
                $query->andWhere(['job_category_id' => $jobCategoryArray]);
            }
            if (!empty($params['wageId'])) {
                $wageInfo = BaseDictionary::getMinAndMaxWage($params['wageId']);

                //面议
                if ($wageInfo['min'] == 0 && $wageInfo['max'] == 0) {
                    $query->andWhere([
                        'i.min_wage' => 0,
                    ]);
                    $query->andWhere([
                        'i.max_wage' => 0,
                    ]);
                } else {
                    if ($wageInfo['min'] > 0) {
                        $query->andWhere([
                            '>=',
                            'i.min_wage',
                            (int)$wageInfo['min'],
                        ]);
                    }

                    if ($wageInfo['max'] > 0) {
                        $query->andWhere([
                            '<=',
                            'i.max_wage',
                            (int)$wageInfo['max'],
                        ]);
                    }
                    //除了以上条件，还必须保证两个不能同事为空
                    $query->andWhere([
                        'or',
                        [
                            '>',
                            'i.max_wage',
                            0,
                        ],
                        [
                            '>',
                            'i.min_wage',
                            0,
                        ],
                    ]);
                }
            }
        }

        //工作年限
        if (($params['workExperience'] && mb_strlen($params['workExperienceMin']) > 0) || ($params['workExperience'] && mb_strlen($params['workExperienceMax']) > 0)) {
            throw new Exception('参数错误');
        }
        if (!empty($params['workExperience'])) {
            if ($params['workExperience'] == 1) {
                //应届生/在校生
                $query->andFilterWhere(['r.identity_type' => BaseResume::IDENTITY_TYPE_GRADUATE]);
            } else {
                $workYearInfo = BaseDictionary::WORK_YEARS_LIST[$params['workExperience']];
                $query->andFilterWhere([
                    'between',
                    'r.work_experience',
                    $workYearInfo['min'],
                    $workYearInfo['max'],
                ]);
            }
        }
        if (mb_strlen($params['workExperienceMin']) > 0 && mb_strlen($params['workExperienceMax']) > 0 && $params['workExperienceMin'] > $params['workExperienceMax']) {
            throw new Exception('自定义工作年限最小值不允许大于最大值');
        }
        //自定义工作年限
        if (mb_strlen($params['workExperienceMin']) > 0) {
            $query->andFilterWhere([
                '>=',
                'r.work_experience',
                $params['workExperienceMin'],
            ]);
        }
        if (mb_strlen($params['workExperienceMax']) > 0) {
            $query->andFilterWhere([
                '<=',
                'r.work_experience',
                $params['workExperienceMax'],
            ]);
        }

        //求职状态
        if (!empty($params['workStatus'])) {
            $query->andWhere(['r.work_status' => $params['workStatus']]);
        }
        //到岗时间
        if (!empty($params['arriveDateType'])) {
            $query->andFilterWhere(['r.arrive_date_type' => $params['arriveDateType']]);
        }
        //职称
        if (!empty($params['titleId'])) {
            $titleArray = explode(',', $params['titleId']);
            $titleList  = BaseDictionary::getAllChildTitleArray($titleArray);

            $titleCondition = ['or'];
            foreach ($titleList as $item) {
                $titleCondition[] = "find_in_set(" . $item . ",r.title_id)";
            }
            $query->andWhere($titleCondition);
        }
        //户籍国籍
        if (!empty($params['householdRegisterId'])) {
            $householdRegisterIdArr = explode(',', $params['householdRegisterId']);
            $query->andWhere(['r.household_register_id' => $householdRegisterIdArr]);
        }
        //政治面貌
        if (!empty($params['political'])) {
            $political = explode(',', $params['political']);
            $query->andWhere(['r.political_status_id' => $political]);
        }
        //年龄
        if (mb_strlen($params['ageMin']) > 0 && mb_strlen($params['ageMax']) > 0 && $params['ageMin'] > $params['ageMax']) {
            throw new Exception('年龄最小值不允许大于最大值');
        }
        if (mb_strlen($params['ageMin']) > 0) {
            $query->andWhere([
                '>=',
                'r.age',
                $params['ageMin'],
            ]);
        }
        if (mb_strlen($params['ageMax']) > 0) {
            $query->andWhere([
                '<=',
                'r.age',
                $params['ageMax'],
            ]);
        }

        //性别
        if (!empty($params['gender'])) {
            $query->andWhere(['r.gender' => $params['gender']]);
        }
        //简历更新时间
        if (!empty($params['updateBeginTime']) && !empty($params['updateEndTime'])) {
            $query->andWhere([
                'between',
                'r.last_update_time',
                TimeHelper::dayToBeginTime($params['updateBeginTime']),
                TimeHelper::dayToEndTime($params['updateEndTime']),
            ]);
        }
        //985/211
        if ($params['isProjectSchool']) {
            if ($params['isProjectSchool'] == 2) {
                $query->andFilterWhere([
                    'r.is_project_school' => [
                        1,
                        2,
                    ],
                ]);
            } else {
                $query->andFilterWhere([
                    '<>',
                    'r.is_project_school',
                    [
                        1,
                        2,
                    ],
                ]);
            }
        }
        //简历收藏时间
        if (!empty($params['collectBeginTime']) && !empty($params['collectEndTime'])) {
            $query->andWhere([
                'between',
                'rc.add_time',
                TimeHelper::dayToBeginTime($params['collectBeginTime']),
                TimeHelper::dayToEndTime($params['collectEndTime']),
            ]);
        }

        //求职者身份选择（-1缺失，1职场人，2应届生）
        //        if (!empty($params['identityType'])) {
        //            $query->andWhere(['r.identity_type' => $params['identityType']]);
        //        }

        //毕业时间
        if ($params['graduateBeginDate'] && $params['graduateEndDate']) {
            $query->leftJoin(['el' => BaseResumeEducation::tableName()], 'el.id=r.last_education_id');
            $query->andWhere([
                'between',
                'el.end_date',
                TimeHelper::dayToBeginTime($params['graduateBeginDate']),
                TimeHelper::dayToEndTime($params['graduateEndDate']),
            ]);
        }

        $query->groupBy('rc.resume_id');
        $count = $query->count();

        //根据收藏时间排序
        if (!empty($params['collectTimeSort'])) {
            if ($params['collectTimeSort'] == BaseResumeLibraryCollect::SORT_COLLECT_TIME_DESC) {
                $sort = 'rc.add_time desc,rc.id desc';
            } elseif ($params['collectTimeSort'] == BaseResumeLibraryCollect::SORT_COLLECT_TIME_ASC) {
                $sort = 'rc.add_time asc,rc.id desc';
            }
        }

        //根据更新时间排序
        if (!empty($params['updateTimeSort'])) {
            if ($params['updateTimeSort'] == BaseResume::SORT_UPDATE_TIME_DESC) {
                $sort = 'r.last_update_time desc,rc.id desc';
            } elseif ($params['updateTimeSort'] == BaseResume::SORT_UPDATE_TIME_ASC) {
                $sort = 'r.last_update_time asc,rc.id desc';
            }
        }
        //如果没有传，默认收藏时间倒叙
        if (empty($sort)) {
            $sort = 'rc.add_time desc,rc.id desc';
        }

        $pageSize = $params['pageSize'] ?: \Yii::$app->params['defaultPageSize'];
        $pages    = BaseActiveRecord::setPage($count, $params['page'], $pageSize);
        $list     = $query->select([
            'rc.resume_id',
            'rc.company_id',
            'rc.add_time',
            'r.name',
            'r.gender',
            'r.work_experience',
            'r.age',
            'r.top_education_code',
            'r.member_id',
            'r.residence',
            'r.last_education_id',
            'r.last_update_time',
        ])
            ->asArray()
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($sort)
            ->all();

        foreach ($list as $k => &$item) {
            //获取现居住地
            $residence = BaseArea::getAreaName($item['residence']) ?: '';
            //获取最高学历
            $topEducation = BaseDictionary::getEducationName($item['top_education_code']);
            //获取最高学历的所学专业
            $topEducationInfo  = BaseResumeEducation::findOne($item['last_education_id']);
            $topEducationMajor = BaseMajor::getMajorName($topEducationInfo->major_id);
            //判断是否有工作经验
            //拼接用户的信息：工作经验——学历——年龄——最高学历专业——居住地
            $identityExperienceText = BaseResume::getIdentityExperienceText($item['resume_id']);
            $topEducation           = $topEducation ?: '';
            $age                    = $item['age'] ? $item['age'] . '岁' : '';
            $topEducationMajor      = $topEducationMajor ?: '';
            $userInfo               = [
                'age'               => $age,
                'topEducation'      => $topEducation,
                'topEducationMajor' => $topEducationMajor ?: $topEducationInfo->major_custom,
            ];
            //注意这个字段是否替换
            if ($identityExperienceText) {
                $userInfo['workExperience'] = $identityExperienceText;
            }
            $item['userInfo'] = $this->connectUserInfo($userInfo);

            //获取头像
            $avatar         = BaseMember::findOneVal(['id' => $item['member_id']], 'avatar');
            $item['avatar'] = self::getAvatar($avatar, $item['gender']);
            //获取第一个求职意向，后面的用...表示
            $item['jobCategoryText']     = BaseResumeIntention::getResumeLibraryJobCategoryText($item['resume_id']);
            $item['jobCategoryFullText'] = BaseResumeIntention::getAllJobCategoryText($item['resume_id']);

            //获取最近2段教育经历
            $item['lastTwoEducationRecord'] = BaseResumeEducation::getLastRecord($item['resume_id'], 2);

            //获取最近1段工作经历,加个[0]转数组，方便前端使用
            $item['lastWorkRecord'] = BaseResumeWork::getLastRecord($item['resume_id'], 1);

            //获取收藏状态
            $item['isCollect'] = BaseResumeLibraryCollect::checkCollectStatus($item['resume_id'], $companyId);

            //获取下载状态
            $item['isDownload'] = BaseCompanyResumeLibrary::checkDownLoadStatus($item['resume_id'], $companyId);

            //获取邀约状态
            $item['isInvite'] = BaseResumeLibraryInviteLog::checkInviteStatus($item['resume_id'], $companyId);

            //获取应聘状态
            $item['isApply'] = BaseJobApply::checkApplyRecord($item['resume_id'], $companyId);

            //获取标签
            $item['labelType'] = self::getResumeLabel($item['resume_id'], $companyId);
            //获取30天内是否被查看
            $item['is_resume_check'] = BaseCompanyViewResume::getCheckStatus($item['resume_id'], $item['company_id']);
            //简历更新时间
            $item['last_update_time'] = TimeHelper::formatDateByYear($item['last_update_time'], '/');
            //收藏时间
            $item['collectTime'] = TimeHelper::formatDateByYear($item['add_time'], '/');

            //获取用户最后登陆时间
            $item['activeTime'] = BaseMember::getUserActiveTime($item['member_id']);
            //简历类型（暂时写死）
            $resumeLevelInfo        = self::getResumeLevel($item['resume_id']);
            $item['resumeTypeText'] = $resumeLevelInfo['name'];
            $item['resumeType']     = $resumeLevelInfo['type'];
            $item['resumeTitleTag'] = BaseResume::getUserSpecialInfo($item['resume_id']);

            //应聘或者下载了简历，即存在单位简历库的记录需要返回个字段供前端判断
            $companyResumeLibraryId         = BaseCompanyResumeLibrary::findOneVal([
                'resume_id'  => $item['resume_id'],
                'company_id' => $companyId,
            ], 'id');
            $item['isCompanyResumeLibrary'] = BaseCompanyResumeLibrary::HAS_RECORD_NO;
            $item['companyResumeLibraryId'] = $companyResumeLibraryId ?: 0;
            if (!empty($companyResumeLibraryId)) {
                $item['isCompanyResumeLibrary'] = BaseCompanyResumeLibrary::HAS_RECORD_YES;
            } else {
                $item['name'] = MaskHelper::getName($item['name']);
            }
            //做一个PV统计
            BaseCompanyResumePvTotal::updateDailyTotalPv($item['resume_id']);
            $item['memberStatus'] = strval(BaseMember::findOne($item['member_id'])->status);
            //按钮组
            $item['buttonGroup'] = (new ButtonGroupAuthService())->setType(ButtonGroupAuthService::TYPE_RESUME)
                ->setParams(['memberStatus' => $item['memberStatus']])
                ->handleResumeButtonGroup($item['resume_id']);
        }

        /** 这里拿一下账号的权限*/
        $companyAuthorityList = (new CompanyAuthorityClassify())->justGetCompanyResumeLibraryList([
            'memberId'         => Yii::$app->user->id,
            'companyAuthority' => 'findTalentList',
        ]);

        return [
            'list'                 => $list,
            'page'                 => [
                'count' => (int)$count,
                'limit' => (int)$pages['limit'],
                'page'  => (int)$pages['page'],
            ],
            'companyAuthorityList' => $companyAuthorityList,
        ];
    }

    /**
     * 输入条件
     * @param $params
     *
     * page
     * pageSize
     * keyword 关键字
     * jobId  邀约职位id
     * announcementId 邀约公告id
     * topEducationCode 最高学历
     * majorId 学科专业
     * residence  所在地区
     * areaId 意向城市
     * workYears 工作年限
     * wageId 期望月薪
     * titleId 职称
     * isAbroad isAbroad
     * sendType 发送方式
     * addTimeFrom
     * addTimeTo
     *
     *  返回数据
     *  简历id
     *  姓名
     *  最高学历
     *  毕业学校
     *  专业
     *  工作年限
     *  意向城市
     *  邀约职位
     *  关联公告
     *  发送方式
     *  邀约时间
     *  邀约人账号
     * @throws \yii\base\Exception
     */
    public function getInviteList($params)
    {
        $companyId      = $params['companyId'];
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        $this->setData(['companyId' => $companyId]);
        $memberId = Yii::$app->user->id;

        $query = BaseResumeLibraryInviteLog::find()
            ->alias('a')
            ->select([
                'a.id',
                'a.is_apply',
                'a.job_id',
                'b.name',
                'b.last_education_id',
                'b.top_education_code',
                'a.add_time',
                'a.resume_id',
                'b.residence',
                'b.title_id',
                'c.name as jobName',
                'c.announcement_id',
                'b.work_experience',
                'a.add_time',
                'b.age',
                'b.gender',
                'a.company_member_id',
                'b.member_id',
            ])
            ->innerJoin(['b' => BaseResume::tableName()], 'a.resume_id=b.id')
            ->innerJoin(['c' => BaseJob::tableName()], 'a.job_id=c.id');
        //->where(['a.company_id' => $companyId]);
        $companyMemberType = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'company_member_type');
        if ($companyMemberType == BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN) {
            $query->where(['a.company_id' => $companyId]);
        } else {
            $query->where(['a.company_member_id' => $memberId]);
        }

        //邀请人
        $query->andFilterCompare('a.company_member_id', $params['handleId']);

        //求职者身份选择（-1缺失，1职场人，2应届生）
        //        $query->andFilterCompare('b.identity_type', $params['identityType']);
        //毕业时间
        if ($params['graduateBeginDate'] && $params['graduateEndDate']) {
            $query->leftJoin(['el' => BaseResumeEducation::tableName()], 'el.id=b.last_education_id');
            $query->andWhere([
                'between',
                'el.end_date',
                TimeHelper::dayToBeginTime($params['graduateBeginDate']),
                TimeHelper::dayToEndTime($params['graduateEndDate']),
            ]);
        }

        // 最高教育经历
        $query->andFilterCompare('b.top_education_code', $params['topEducationCode']);
        $query->andFilterWhere(['c.id' => $params['jobId']]);
        $query->andFilterWhere(['c.announcement_id' => $params['announcementId']]);
        $query->andFilterWhere(['a.is_apply' => $params['isApply']]);

        if ($params['majorId']) {
            if (!empty($params['topEducationCode'])) {
                $query->innerJoin(['d' => BaseResumeEducation::tableName()], 'b.last_education_id=d.id');
            } else {
                $query->innerJoin(['d' => BaseResumeEducation::tableName()], 'a.resume_id=d.resume_id');
            }
            $majorIds = explode(',', $params['majorId']);
            $query->andWhere([
                'd.major_id' => $majorIds,
                'd.status'   => BaseResumeEducation::STATUS_ACTIVE,
            ]);
            $query->addSelect([
                'd.major_id',
            ]);
        }

        // 所在地区
        if ($params['residence']) {
            $areaId = BaseArea::getCityIds($params['residence']);
            $query->andWhere(['b.residence' => $areaId]);
        }

        // 工作年限
        if (($params['workExperience'] && mb_strlen($params['experienceMin']) > 0) || ($params['workExperience'] && mb_strlen($params['experienceMax']) > 0)) {
            throw new Exception('参数错误');
        }
        if ($params['workExperience']) {
            if ($params['workExperience'] == 1) {
                //应届生/在校生
                $query->andFilterWhere(['b.identity_type' => BaseResume::IDENTITY_TYPE_GRADUATE]);
            } else {
                // 这里
                $workYearInfo = BaseDictionary::WORK_YEARS_LIST[$params['workExperience']];
                $query->andFilterWhere([
                    'between',
                    'b.work_experience',
                    $workYearInfo['min'],
                    $workYearInfo['max'],
                ]);
            }
        }

        if (mb_strlen($params['experienceMin']) > 0 && mb_strlen($params['experienceMax']) > 0 && $params['experienceMin'] > $params['experienceMax']) {
            throw new Exception('自定义工作年限最小值不允许大于最大值');
        }
        //自定义工作年限
        if (mb_strlen($params['experienceMin']) > 0) {
            $query->andFilterWhere([
                '>=',
                'b.work_experience',
                $params['experienceMin'],
            ]);
        }
        if (mb_strlen($params['experienceMax']) > 0) {
            $query->andFilterWhere([
                '<=',
                'b.work_experience',
                $params['experienceMax'],
            ]);
        }
        //政治面貌
        if (!empty($params['political'])) {
            $political = explode(',', $params['political']);
            $query->andWhere(['b.political_status_id' => $political]);
        }
        //年龄
        if (mb_strlen($params['ageMin']) > 0 && mb_strlen($params['ageMax']) > 0 && $params['ageMin'] > $params['ageMax']) {
            throw new Exception('年龄最小值不允许大于最大值');
        }
        if (mb_strlen($params['ageMin']) > 0) {
            $query->andWhere([
                '>=',
                'b.age',
                $params['ageMin'],
            ]);
        }
        if (mb_strlen($params['ageMax']) > 0) {
            $query->andWhere([
                '<=',
                'b.age',
                $params['ageMax'],
            ]);
        }

        // 这两个都属于意向里面的字段
        if ($params['wageId'] || $params['areaId']) {
            $query->innerJoin(['e' => BaseResumeIntention::tableName()], 'a.resume_id=e.resume_id');
        }

        // 意向城市
        if ($params['areaId']) {
            // 使用find_in_set查询
            $areaArr     = explode(',', $params['areaId']);
            $araAOrWhere = [
                'or',
            ];
            foreach ($areaArr as $k => $v) {
                // 只要符合一个条件就可以了
                $araAOrWhere[] = new Expression("find_in_set({$v},e.area_id)");
            }

            $query->andWhere($araAOrWhere);
        }

        // 期望月薪
        if ($params['wageId']) {
            $wageInfo = Dictionary::getMinAndMaxWage($params['wageId']);
            $query->addSelect([
                'e.min_wage',
                'e.max_wage',
            ]);
            //面议
            if ($wageInfo['min'] == 0 && $wageInfo['max'] == 0) {
                $query->andWhere([
                    'e.min_wage' => 0,
                ]);
                $query->andWhere([
                    'e.max_wage' => 0,
                ]);
            } else {
                if ($wageInfo['min'] > 0) {
                    $query->andWhere([
                        '>=',
                        'e.min_wage',
                        (int)$wageInfo['min'],
                    ]);
                }

                if ($wageInfo['max'] > 0) {
                    $query->andWhere([
                        '<=',
                        'e.max_wage',
                        (int)$wageInfo['max'],
                    ]);
                }
                //除了以上条件，还必须保证两个不能同事为空
                $query->andWhere([
                    'or',
                    [
                        '>',
                        'e.max_wage',
                        0,
                    ],
                    [
                        '>',
                        'e.min_wage',
                        0,
                    ],
                ]);
            }
        }

        // 职称
        if (!empty($params['titleId'])) {
            // $titleList = BaseDictionary::getAllChildTitile($params['titleId']);
            // $query->andWhere(['b.title_id' => $titleList]);

            $titleArray = explode(',', $params['titleId']);
            $titleList  = BaseDictionary::getAllChildTitleArray($titleArray);

            $titleCondition = ['or'];
            foreach ($titleList as $item) {
                $titleCondition[] = "find_in_set(" . $item . ",b.title_id)";
            }
            $query->andWhere($titleCondition);
        }
        // 是否出国
        if ($params['isAbroad']) {
            //            // 工作经历有海外?
            //            $query->innerJoin(['f' => BaseResumeWork::tableName()], 'a.resume_id=f.resume_id');
            //            $query->addSelect([
            //                'f.is_abroad',
            //            ]);
            $query->andWhere(['b.is_abroad' => $params['isAbroad']]);
        }

        // TODO 发送方式
        if ($params['sendType']) {
        }

        // 邀约时间
        if ($params['addTimeFrom']) {
            $query->andWhere([
                '>=',
                'a.add_time',
                $params['addTimeFrom'],
            ]);
        }

        if ($params['addTimeTo']) {
            $query->andWhere([
                '<=',
                'a.add_time',
                TimeHelper::dayToEndTime($params['addTimeTo']),
            ]);
        }
        // 关键字搜索
        if (!empty($params['keyword'])) {
            //暂时通过一个方法去联表取出resumeId
            $resumeIds = BaseResume::getIdsByKeyword($params['keyword']);
            //     //这里不管有没有  resumeId，都是查询过的结果，必须andWhere进去
            $query->andWhere(['a.resume_id' => $resumeIds]);

            //关键词搜索支持以下字段模糊搜索：
            //【教育经历】-学校名称、
            //【研究方向】、
            //【工作经历】-单位名称/职位名称/工作内容、
            //【科研项目】-项目名称/所属单位/项目描述、
            //【学术论文】-论文题目/论文描述、
            //【学术专利】-专利名称/专利描述、
            //【学术专著】-著作名称、
            //【资质证书】-证书名称、
            //【技能/语言】-技能/语言名称、
            //【个人优势】
        }
        $count    = $query->count();
        $pageSize = $params['pageSize'] ?: \Yii::$app->params['defaultPageSize'];
        $pages    = BaseActiveRecord::setPage($count, $params['page'], $pageSize);

        $list = $query->limit($pages['limit'])
            ->offset($pages['offset'])
            ->asArray()
            ->orderBy('a.add_time desc')
            ->all();

        foreach ($list as &$item) {
            //根据求职者身份，获取求职者工作经验或者毕业时间文案
            $item['identityExperienceText'] = BaseResume::getIdentityExperienceText($item['resume_id']);
            $item['topEducation']           = BaseResumeEducation::EDUCATION_TYPE_LIST[$item['top_education_code']];
            // 毕业院校,去最后一段教育经历里面的毕业院校
            $item['school']       = BaseResumeEducation::findOneVal(['id' => $item['last_education_id']], 'school');
            $item['residenceTxt'] = BaseArea::findOneVal(['id' => $item['residence']], 'name');
            // 找公告的标题
            $item['announcementTitle'] = BaseAnnouncement::findOneVal(['id' => $item['announcement_id']], 'title');
            // 意向城市,这里会涉及到很多的意向城市
            $item['areaTxt'] = BaseResumeIntention::getAllCityName($item['resume_id']);
            // 专业
            $item['majorTxt'] = BaseResumeEducation::getAllMajorName($item['resume_id']);

            //$item['majorIds'] = BaseResumeEducation::getAllMajorId($item['resume_id']);

            //获取下载状态
            $item['isDownload'] = BaseCompanyResumeLibrary::checkDownLoadStatus($item['resume_id'], $companyId);

            //获取应聘状态
            //$item['isApply'] = BaseJobApply::checkApplyRecord($item['resume_id'], $companyId);
            $item['isApplyText'] = BaseResumeLibraryInviteLog::IS_APPLY_LIST[$item['is_apply']];
            $item['isApplyTime'] = BaseResumeLibraryInviteLog::getInviteApplyTime($item['job_id'], $item['resume_id'],
                $item['add_time']);

            //应聘或者下载了简历，即存在单位简历库的记录需要返回个字段供前端判断
            $companyResumeLibraryId         = BaseCompanyResumeLibrary::findOneVal([
                'resume_id'  => $item['resume_id'],
                'company_id' => $companyId,
            ], 'id');
            $item['isCompanyResumeLibrary'] = BaseCompanyResumeLibrary::HAS_RECORD_NO;
            if (!empty($companyResumeLibraryId)) {
                $item['isCompanyResumeLibrary'] = BaseCompanyResumeLibrary::HAS_RECORD_YES;
            } else {
                $item['name'] = MaskHelper::getName($item['name']);
            }

            $item['memberStatus'] = strval(BaseMember::findOne($item['member_id'])->status);
            //基本信息
            $information = [];
            if ($item['memberStatus'] != BaseMember::STATUS_RESUME_CANCELED) {
                if ($item['age']) {
                    array_push($information, $item['age'] . '岁');
                }
                if ($item['gender']) {
                    array_push($information, $item['gender'] == 1 ? '男' : '女');
                }
                if ($item['topEducation']) {
                    array_push($information, $item['topEducation']);
                }
                if ($item['majorTxt']) {
                    array_push($information, $item['majorTxt']);
                }
                if ($item['school']) {
                    array_push($information, $item['school']);
                }
            }

            $item['information'] = implode(' | ', $information);

            //邀约人账号信息
            $companyMemberInfo = BaseCompanyMemberInfo::findOne(['member_id' => $item['company_member_id']]);
            if ($companyMemberInfo['department']) {
                $item['handler'] = $companyMemberInfo['contact'] . '(' . $companyMemberInfo['department'] . ')';
            } else {
                $item['handler'] = $companyMemberInfo['contact'] ?: '';
            }
            //做一个PV统计
            BaseCompanyResumePvTotal::updateDailyTotalPv($item['resume_id']);
        }

        /** 这里拿一下账号的权限*/
        $companyAuthorityList = (new CompanyAuthorityClassify())->justGetCompanyResumeLibraryList([
            'memberId'         => Yii::$app->user->id,
            'companyAuthority' => 'findTalentList',
        ]);

        return [
            'list'                 => $list,
            'page'                 => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$pages['page'],
            ],
            'companyAuthorityList' => $companyAuthorityList,
        ];
    }

    /**
     * 获取邀约过的职位列表
     */
    public function getJobInviteList($params)
    {
        $companyId = $params['companyId'];

        // $query = BaseResumeLibraryInviteLog::find()
        //     ->alias('a')
        //     ->select('c.name,c.id')
        //     ->innerJoin(['c' => BaseJob::tableName()], 'a.job_id=c.id')
        //     ->where(['a.company_id' => $companyId]);
        //
        // $query->andFilterWhere([
        //     'like',
        //     'c.name',
        //     $params['keyword'],
        // ]);
        //
        // return $query->asArray()
        //     ->all();

        // 需求改为这个单位全部在线和下线的职位
        $query = BaseJob::find()
            ->alias('j')
            ->select([
                'j.id',
                'j.name',
            ])
            ->where([
                'j.company_id' => $companyId,
                'j.status'     => BaseJob::STATUS_ONLINE,
            ])
            ->andFilterWhere([
                'like',
                'j.name',
                $params['keyword'],
            ]);

        return $query->asArray()
            ->all();
    }

    /**
     * 获取邀约过的公告列表
     */
    public function getAnnouncementInviteList($params)
    {
        $companyId = $params['companyId'];

        // $query = BaseResumeLibraryInviteLog::find()
        //     ->alias('a')
        //     ->select('b.title,b.id')
        //     ->innerJoin(['c' => BaseJob::tableName()], 'a.job_id=c.id')
        //     ->innerJoin(['b' => BaseAnnouncement::tableName()], 'c.announcement_id=b.id')
        //     ->where(['a.company_id' => $companyId]);
        //
        // $query->andFilterWhere([
        //     'like',
        //     'b.title',
        //     $params['keyword'],
        // ]);
        //
        // return $query->asArray()
        //     ->all();

        $query = BaseAnnouncement::find()
            ->alias('a')
            ->select('a.id,a.title as name')
            ->where([
                'a.company_id' => $companyId,
                'status'       => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_ONLINE,
                ],
            ]);

        $query->andFilterWhere([
            'like',
            'title',
            $params['keyword'],
        ]);

        return $query->asArray()
            ->all();
    }

    public function sendMessage()
    {
        // 写一个强提醒?

        // $inner_link_params = ['id' => $this->companyId];
        $title = "【简历查看提醒】“{$this->companyModel->full_name}”查看了您的简历";
        // //判断今天是否有发生站内信了，如果没有，再发送
        // $messageInfo = BaseMemberMessage::find()
        //     ->where([
        //         'type'              => BaseMemberMessage::TYPE_RESUME_CHECK,
        //         'member_id'         => $this->resumeModel->member_id,
        //         'inner_link_params' => json_encode($inner_link_params),
        //         'title'             => $title,
        //     ])
        //     ->andWhere([
        //         'between',
        //         'add_time',
        //         TimeHelper::dayToBeginTime(date('Y-m-d')),
        //         TimeHelper::dayToEndTime(date('Y-m-d', strtotime('+1 days'))),
        //     ])
        //     ->one();
        // if (!empty($messageInfo)) {
        //     return;
        // }
        // //判断用户是否关闭了消息通知
        // $openViewMessage = BaseResumeSetting::findOneVal(['resume_id' => $this->resumeId], 'is_company_view_me');
        // if ($openViewMessage == BaseResumeSetting::IS_COMPANY_VIEW_ME_NO) {
        //     return;
        // }
        //
        // BaseMemberMessage::send($this->resumeModel->member_id, BaseMemberMessage::TYPE_RESUME_CHECK, $title,
        //     "“{$this->companyModel->full_name}”于高校人才网人才库中搜索并查看了您的在线简历。现在是简历投递最佳时间，请勿错过！查看单位招聘详情！",
        //     BaseMemberMessage::LINK_TYPE_COMPANY_DETAIL, ['id' => $this->companyId]);

        BaseMemberMessage::send($this->resumeModel->member_id, BaseMemberMessage::TYPE_RESUME_CHECK, $title,
            "“{$this->companyModel->full_name}”于高校人才网人才库中搜索并查看了您的在线简历。现在是简历投递最佳时间，请勿错过！查看单位招聘详情！",
            BaseMemberMessage::LINK_TYPE_COMPANY_DETAIL, ['id' => $this->companyId]);
    }

    private function connectUserInfo($data)
    {
        if (!empty($data)) {
            $text = '';
            foreach ($data as $k => $v) {
                if (!empty($v)) {
                    $text .= $v . ' | ';
                }
            }
            if (empty($text)) {
                return '';
            }

            return substr($text, 0, -3);
        } else {
            return '';
        }
    }

    private function getResumeLabel($resumeId, $companyId)
    {
        //1、若求职者发起“应聘”操作在先，则标签展示“已应聘”，不因单位后续进行邀约操作而改变；
        //2、若单位发起“下载”操作在先，则标签展示“已下载”，不因单位后续的职位邀约、求职者发起应聘而改变；
        //3、若单位发起“邀约”在先，则标签展示“已邀约”；若后续单位进行了下载，或求职者发起应聘后，标签随着改变；
        //      若后续单位先进行了下载操作，则标签变为“已下载”，后续标签不再改变；
        //      若后续求职者先发起了应聘，则标签变为“已应聘”，后续标签不再改变；
        $data = [];
        //获取用户简历库数据，根据此来计算信息
        $companyResumeLibraryInfo = BaseCompanyResumeLibrary::find()
            ->where([
                'resume_id'  => $resumeId,
                'company_id' => $companyId,
            ])
            ->select([
                'download_time',
                'apply_time',
                'source_type',
            ])
            ->asArray()
            ->one();

        switch ($companyResumeLibraryInfo['source_type']) {
            case BaseCompanyResumeLibrary::SOURCE_TYPE_APPLY:
                $data[] = [
                    'type'     => 1,
                    'typeText' => '已应聘',
                    'val'      => strtotime($companyResumeLibraryInfo['apply_time']),
                ];
                break;
            case BaseCompanyResumeLibrary::SOURCE_TYPE_DOWNLOAD:
                $data[] = [
                    'type'     => 2,
                    'typeText' => '已下载',
                    'val'      => strtotime($companyResumeLibraryInfo['download_time']),
                ];
                break;
            case BaseCompanyResumeLibrary::SOURCE_TYPE_BOTH:
                $data[] = [
                    'type'     => 1,
                    'typeText' => '已应聘',
                    'val'      => strtotime($companyResumeLibraryInfo['apply_time']),
                ];
                $data[] = [
                    'type'     => 2,
                    'typeText' => '已下载',
                    'val'      => strtotime($companyResumeLibraryInfo['download_time']),
                ];
                break;
        }

        $last30Days = TimeHelper::dayToBeginTime(date('Y-m-d', strtotime('-30 days')));
        //获取求职者最早的应聘、下载、邀约记录，来求得状态标签
        $inviteInfo = BaseResumeLibraryInviteLog::find()
            ->where([
                'resume_id'  => $resumeId,
                'company_id' => $companyId,
            ])
            ->andWhere([
                '>=',
                'add_time',
                $last30Days,
            ])
            ->select(['add_time'])
            ->orderBy('add_time asc')
            ->asArray()
            ->one();
        if (!empty($inviteInfo)) {
            $inviteTime = strtotime($inviteInfo['add_time']);
            $data[]     = [
                'type'     => 3,
                'typeText' => '已邀约',
                'val'      => $inviteTime,
            ];
        }
        if (empty($data)) {
            return '';
        }

        $rel = array_column($data, 'val');
        array_multisort($rel, SORT_ASC, $data);

        if ($data[0]['type'] != 3 || count($data) == 1) {
            return $data[0]['type'];
        } else {
            return $data[1]['type'];
        }
    }

    /**
     * 搜索条件
     */
    private function searchData()
    {
        $companyId = $this->searchParams['companyId'];
        $params    = $this->searchParams;
        // 非免费会员,开启查询服务
        $query = BaseResume::find()
            ->alias('r')
            ->leftJoin(['m' => Basemember::tableName()], 'r.member_id=m.id')
            ->leftJoin(['rtc' => BaseResumeTopConfig::tableName()],
                'r.id=rtc.resume_id and rtc.status=' . BaseResumeTopConfig::STATUS_EFFECT)
            ->andWhere(['r.is_resume_library' => BaseResume::IS_RESUME_LIBRARY_YES]);
        //求职者屏蔽了单位，简历要进行剔除
        $shield_resume_ids = BaseShieldCompany::find()
            ->where([
                'company_id' => $companyId,
                'status'     => BaseShieldCompany::STATUS_ACTIVE,
            ])
            ->select('resume_id')
            ->column();
        if (count($shield_resume_ids) > 0) {
            $query->andWhere([
                'not in',
                'r.id',
                $shield_resume_ids,
            ]);
        }

        // //关键词查询
        if (!empty($params['keyword'])) {
            //     //关键词搜索支持以下字段模糊搜索：
            //     //【教育经历】-学校名称、
            //     //【研究方向】、
            //     //【工作经历】-单位名称/职位名称/工作内容、
            //     //【科研项目】-项目名称/所属单位/项目描述、
            //     //【学术论文】-论文题目/论文描述、
            //     //【学术专利】-专利名称/专利描述、
            //     //【学术专著】-著作名称、
            //     //【资质证书】-证书名称、
            //     //【技能/语言】-技能/语言名称、
            //     //【个人优势】
            //暂时通过一个方法去联表取出resumeId
            // $resumeIds = BaseResume::getIdsByKeyword($params['keyword']);
            // 经过meilisearch获取
            $resumeIds = (new ResumeLibraryDocumentService())->setKeyword($params['keyword'])
                ->run();
            if (count($resumeIds) == ResumeLibraryDocumentService::LIMIT) {
                $resumeIds = BaseResume::getIdsByKeyword($params['keyword']);
            } else {
                // 通知系统管理员
                WxWork::getInstance()
                    ->robotMessageToSystem('人才库搜索关键字命中Meilisearch： ' . $params['keyword']);
            }
            //     //这里不管有没有resumeId，都是查询过的结果，必须andWhere进去
            $query->andWhere(['r.id' => $resumeIds]);
        }

        //现居住地查询
        if (!empty($params['residenceId'])) {
            $query->andWhere(['r.residence' => explode(',', $params['residenceId'])]);
        }

        //隐藏简历完善度不足65%的博士人才
        //        if (!empty($params['isHideComplete'])) {
        //            $query->andWhere([
        //                '>',
        //                'r.complete',
        //                65,
        //            ]);
        //        }

        //工作年限
        if (($params['workExperience'] && mb_strlen($params['workExperienceMin']) > 0) || ($params['workExperience'] && mb_strlen($params['workExperienceMax']) > 0)) {
            throw new Exception('参数错误');
        }
        if (!empty($params['workExperience'])) {
            if ($params['workExperience'] == 1) {
                //应届生/在校生
                $query->andFilterWhere(['r.identity_type' => BaseResume::IDENTITY_TYPE_GRADUATE]);
            } else {
                $workYearInfo = BaseDictionary::WORK_YEARS_LIST[$params['workExperience']];
                $query->andFilterWhere([
                    'between',
                    'r.work_experience',
                    $workYearInfo['min'],
                    $workYearInfo['max'],
                ]);
            }
        }
        if (mb_strlen($params['workExperienceMin']) > 0 && mb_strlen($params['workExperienceMax']) > 0 && $params['workExperienceMin'] > $params['workExperienceMax']) {
            throw new Exception('自定义工作年限最小值不允许大于最大值');
        }
        //自定义工作年限
        if (mb_strlen($params['workExperienceMin']) > 0) {
            $query->andFilterWhere([
                '>=',
                'r.work_experience',
                $params['workExperienceMin'],
            ]);
        }
        if (mb_strlen($params['workExperienceMax']) > 0) {
            $query->andFilterWhere([
                '<=',
                'r.work_experience',
                $params['workExperienceMax'],
            ]);
        }

        //求职状态
        if (!empty($params['workStatus'])) {
            $query->andWhere(['r.work_status' => $params['workStatus']]);
        }
        //到岗时间
        if (!empty($params['arriveDateType'])) {
            $query->andFilterWhere(['r.arrive_date_type' => $params['arriveDateType']]);
        }
        //职称
        if (!empty($params['titleId'])) {
            $titleArray = explode(',', $params['titleId']);
            $titleList  = BaseDictionary::getAllChildTitleArray($titleArray);

            $titleCondition = ['or'];
            foreach ($titleList as $item) {
                $titleCondition[] = "find_in_set(" . $item . ",r.title_id)";
            }
            $query->andWhere($titleCondition);
        }
        //户籍国籍
        if (!empty($params['householdRegisterId'])) {
            //前端传的可能是多个id，这里用in查询
            $householdRegisterIdArr = explode(',', $params['householdRegisterId']);
            $query->andWhere(['r.household_register_id' => $householdRegisterIdArr]);
        }

        //年龄
        if (mb_strlen($params['ageMin']) > 0 && mb_strlen($params['ageMax']) > 0 && $params['ageMin'] > $params['ageMax']) {
            throw new Exception('年龄最小值不允许大于最大值');
        }
        if (mb_strlen($params['ageMin']) > 0) {
            $query->andWhere([
                '>=',
                'r.age',
                $params['ageMin'],
            ]);
        }
        if (mb_strlen($params['ageMax']) > 0) {
            $query->andWhere([
                '<=',
                'r.age',
                $params['ageMax'],
            ]);
        }

        //性别
        if (!empty($params['gender'])) {
            $query->andWhere(['r.gender' => $params['gender']]);
        }
        //简历更新时间
        if (!empty($params['updateBeginTime']) && !empty($params['updateEndTime'])) {
            $query->andWhere([
                'between',
                'r.last_update_time',
                TimeHelper::dayToBeginTime($params['updateBeginTime']),
                TimeHelper::dayToEndTime($params['updateEndTime']),
            ]);
        }
        //985/211
        if ($params['isProjectSchool']) {
            if ($params['isProjectSchool'] == 2) {
                //字典表数据不对应，这里转换下
                $params['isProjectSchool'] = 1;
            }
            if ($params['isProjectSchool'] == 1) {
                $query->andFilterWhere([
                    'r.is_project_school' => 1,
                ]);
            } else {
                $query->andFilterWhere([
                    ' <> ',
                    'r.is_project_school',
                    1,
                ]);
            }
        }
        if (!empty($params['educationId'])) {
            // 前端传过来有可能是逗号隔开
            $educationIdArr = explode(',', $params['educationId']);
            $query->andWhere(['r.top_education_code' => $educationIdArr]);
        }

        //教育表内查询
        if (!empty($params['majorId'])) {
            if (!empty($params['educationId'])) {
                $query->leftJoin(['e' => BaseResumeEducation::tableName()], 'e.id = r.last_education_id');
            } else {
                $query->leftJoin(['e' => BaseResumeEducation::tableName()], 'e.resume_id = r.id');
            }
            $majorList = explode(',', $params['majorId']);
            $query->andWhere([
                'e.major_id' => $majorList,
                'e.status'   => BaseResumeEducation::STATUS_ACTIVE,
            ]);
        }
        if (empty($params['majorId']) && $params['educationEndDateStart'] && $params['educationEndDateEnd']) {
            $query->leftJoin(['e' => BaseResumeEducation::tableName()], 'e.id = r.last_education_id');
        }
        if ($params['educationEndDateStart'] && $params['educationEndDateEnd']) {
            $query->andFilterWhere([
                'between',
                'e.end_date',
                TimeHelper::monthToBeginTime($params['educationEndDateStart']),
                TimeHelper::monthToBeginTime($params['educationEndDateEnd']),
            ]);
        }

        if (!empty($params['isAbroad'])) {
            $query->andWhere(['r.is_abroad' => $params['isAbroad']]);
            //            if ($params['isAbroad'] == 1) {
            //                $query->andWhere([
            //                    'or',
            //                    [
            //                        'e.is_abroad' => $params['isAbroad'],
            //                        'e.status'    => BaseResumeEducation::STATUS_ACTIVE,
            //                    ],
            //                    [
            //                        'w.is_abroad' => $params['isAbroad'],
            //                        'w.status'    => BaseResumeWork::STATUS_ACTIVE,
            //                    ],
            //                ]);
            //            } else {
            //                $notAbroadWorkResumeIds      = BaseResumeWork::getAbroadResumeIds();
            //                $notAbroadEducationResumeIds = BaseResumeEducation::getAbroadResumeIds();
            //                $notAbroadResumeIds          = array_unique(array_merge($notAbroadWorkResumeIds,
            //                    $notAbroadEducationResumeIds));
            //                $query->andWhere([
            //                    'not in',
            //                    'r.id',
            //                    $notAbroadResumeIds,
            //                ]);
            //            }
        }

        // 政治面貌
        if (!empty($params['political'])) {
            $political = explode(',', $params['political']);
            $query->andWhere(['r.political_status_id' => $political]);
        }

        //求职意向查询
        if (!empty($params['cityId']) || !empty($params['jobCategoryId']) || !empty($params['wageId'])) {
            //意向城市、求职意向、薪资待遇
            $query->innerJoin(['i' => BaseResumeIntention::tableName()], 'i.resume_id = r.id');
            $query->andWhere(['i.status' => BaseResumeIntention::STATUS_ACTIVE]);
            if (!empty($params['cityId'])) {
                $cityArr   = explode(',', $params['cityId']);
                $cityQuery = BaseActiveRecord::formatFindInSetQuery($cityArr, 'i.area_id');
                $query->andWhere($cityQuery);
            }
            if (!empty($params['jobCategoryId'])) {
                $jobCategoryArray = explode(',', $params['jobCategoryId']);
                $query->andWhere(['job_category_id' => $jobCategoryArray]);
            }
            if (!empty($params['wageId'])) {
                $wageInfo = BaseDictionary::getMinAndMaxWage($params['wageId']);

                //面议
                if ($wageInfo['min'] == 0 && $wageInfo['max'] == 0) {
                    $query->andWhere([
                        'i.min_wage' => 0,
                    ]);
                    $query->andWhere([
                        'i.max_wage' => 0,
                    ]);
                } else {
                    if ($wageInfo['min'] > 0) {
                        $query->andWhere([
                            ' >= ',
                            'i.min_wage',
                            (int)$wageInfo['min'],
                        ]);
                    }

                    if ($wageInfo['max'] > 0) {
                        $query->andWhere([
                            ' <= ',
                            'i.max_wage',
                            (int)$wageInfo['max'],
                        ]);
                    }
                    //除了以上条件，还必须保证两个不能同事为空
                    $query->andWhere([
                        ' or ',
                        [
                            ' > ',
                            'i.max_wage',
                            0,
                        ],
                        [
                            ' > ',
                            'i.min_wage',
                            0,
                        ],
                    ]);
                }
            }
        }

        //隐藏已应聘的简历,曾经投递过该单位职位的人才简历。显示“已应聘”标签。
        if ($params['hideApply'] == 1) {
            //查找投递过本单位的简历id
            $jobList        = BaseJobApply::find()
                ->where(['company_id' => $companyId])
                ->select('resume_id')
                ->indexBy('resume_id')
                ->asArray()
                ->all();
            $applyResumeIds = array_unique(array_keys($jobList));
            //把投递了的排除
            $query->andWhere([
                'not in',
                'r.id',
                $applyResumeIds,
            ]);
        }

        //隐藏已下载简历
        if ($params['hideDownload'] == 1) {
            //获取当前已经下载的简历id
            $downloadResumeList = BaseCompanyResumeLibrary::find()
                ->where(['company_id' => $companyId])
                ->select('resume_id')
                ->indexBy('resume_id')
                ->asArray()
                ->all();
            $downloadResumeIds  = array_unique(array_keys($downloadResumeList));
            $query->andWhere([
                'not in',
                "r.id",
                $downloadResumeIds,
            ]);
        }
        //隐藏近30天已邀约简历
        if ($params['hideInvite'] == 1) {
            $last30Days      = TimeHelper::dayToBeginTime(date('Y-m-d', strtotime('-30 days')));
            $logList         = BaseResumeLibraryInviteLog::find()
                ->where(['company_id' => $companyId])
                ->andWhere([
                    ' >= ',
                    'add_time',
                    $last30Days,
                ])
                ->select('resume_id')
                ->indexBy('resume_id')
                ->asArray()
                ->all();
            $inviteResumeIds = array_unique(array_keys($logList));
            $query->andWhere([
                'not in',
                "r.id",
                $inviteResumeIds,
            ]);
        }
        //隐藏近30天已聊过
        if ($params['hideChat'] == 1) {
            $last30Days    = TimeHelper::dayToBeginTime(date('Y-m-d', strtotime('-30 days')));
            $chatResumeIds = BaseChatRoom::find()
                ->andWhere([
                    ' >= ',
                    'last_talk_time',
                    $last30Days,
                ])
                ->select('resume_id')
                ->column();
            $query->andWhere([
                'not in',
                "r.id",
                $chatResumeIds,
            ]);
        }

        // 隐藏近60天已查看
        if (isset($params['hideViewPast60Days']) && $params['hideViewPast60Days'] == 1) {
            $check_in_service_time = date('Y-m-d H:i:s', strtotime('-60 day'));
            //获取查看的简历
            $q               = BaseCompanyViewResume::find();
            $view_resume_ids = $q->select('resume_id')
                ->andWhere([
                    'company_id' => $companyId,
                ])
                ->andWhere([
                    '>',
                    'last_time',
                    $check_in_service_time,
                ])
                ->column();
            if ($view_resume_ids) {
                $query->andWhere([
                    'not in',
                    "r.id",
                    $view_resume_ids,
                ]);
            }
        }

        // 隐藏完善度＜65%博士  前端虽然加上了博士的这个文案，但是实际上只是要过滤掉完善度小于65%的简历
        if ($params['hideViewLess65Percentage'] == 1) {
            $query->andWhere([
                '>=',
                'r.complete',
                65,
            ]);
        }

        //只看关注我的
        if ($params['isFollow'] == 1) {
            $query->innerJoin(['cc' => BaseCompanyCollect::tableName()], 'cc.member_id = r.member_id');
            $query->andWhere([
                'cc.company_id' => $companyId,
                'cc.status'     => BaseCompanyCollect::STATUS_ACTIVE,
            ]);
        }

        //求职者身份选择（-1缺失，1职场人，2应届生）
        //        if (!empty($params['identityType'])) {
        //            $query->andWhere(['r.identity_type' => $params['identityType']]);
        //        }

        //毕业时间
        if ($params['graduateBeginDate'] && $params['graduateEndDate']) {
            $query->innerJoin(['el' => BaseResumeEducation::tableName()], 'el.id = r.last_education_id');
            $query->andWhere([
                'between',
                'el.end_date',
                TimeHelper::dayToBeginTime($params['graduateBeginDate']),
                TimeHelper::dayToEndTime($params['graduateEndDate']),
            ]);
        }

        // 研究方向
        if (!empty($params['directionContent'])) {
            $query->innerJoin(['dir' => BaseResumeResearchDirection::tableName()], 'dir.resume_id = r.id');

            $query->andFilterWhere([
                'like',
                'dir.content',
                $params['directionContent'],
            ]);
        }

        // 博士后经历筛选 - 优化：直接使用resume表的冗余字段
        if (!empty($params['boShiHouExperience'])) {
            if ($params['boShiHouExperience'] == 1) {
                // 有博士后经历
                $query->andWhere(['r.is_postdoc' => BaseResume::IS_POSTDOC_YES]);
            } else {
                // 无博士后经历
                $query->andWhere(['r.is_postdoc' => BaseResume::IS_POSTDOC_NO]);
            }
        }

        $query->groupBy('r.id');
        $count    = $query->count();
        $pageSize = $this->searchParams['pageSize'] > 0 ? $this->searchParams['pageSize'] : \Yii::$app->params['resumeLibraryDefaultPageSize'];
        $pages    = BaseActiveRecord::setPage($count, $this->searchParams['page'], $pageSize);
        $list     = $query->select([
            'r.id as resume_id',
            //'r.name',
            //'r.work_experience',
            //'r.age',
            //'r.gender',
            //'r.top_education_code',
            'r.member_id',
            //'r.residence',
            //'r.last_education_id',
            //'r.last_update_time',
            "DATE_FORMAT(m.last_active_time, '%Y-%m-%d') AS last_active_date",
            'rtc.id as top_id',
            'IF(rtc.id>0,2,1) as is_resume_top',
        ])
            ->asArray()// ->orderBy('last_update_time desc')  人才库已经按照更新时间倒序去入库了.所以排序规则不再需要按照更新时间来倒序了
            ->orderBy('is_resume_top desc,last_active_date desc,r.complete desc,r.id desc')
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->all();

        return [
            'list' => $list,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$pages['page'],
            ],
        ];
    }

    /**
     * 无搜索条件
     */
    private function noSearchData()
    {
        return $this->getNoSearchData();
    }

    /**
     * 获取无搜索条件的数据缓存
     * @return array
     */
    public function getNoSearchData()
    {
        $cacheKey = Cache::COMPANY_RESUME_LIBRARY_SEARCH . ':' . $this->searchParams['page'] . '_' . $this->searchParams['pageSize'];
        $data     = Cache::get($cacheKey);
        if ($data) {
            return json_decode($data, true);
        }

        return $this->setNoSearchData();
    }

    /**
     * 设置无搜索条件的数据缓存
     * @return array
     * @throws \Exception
     */
    public function setNoSearchData($params = [])
    {
        if (!empty($params)) {
            $this->searchParams = $params;
        }
        //先获取在前的数据集合
        $common_query = BaseResume::find()
            ->alias('r')
            ->leftJoin(['m' => Basemember::tableName()], 'r.member_id=m.id')
            ->andWhere(['r.is_resume_library' => BaseResume::IS_RESUME_LIBRARY_YES]);
        //隐藏简历完善度不足65%的博士人才
        //        if (!empty($this->searchParams['isHideComplete'])) {
        //            $common_query->andWhere([
        //                '>',
        //                'r.complete',
        //                65,
        //            ]);
        //        }
        //求职者屏蔽了单位，简历要进行剔除
        $shield_resume_ids = BaseShieldCompany::find()
            ->where([
                'company_id' => $this->searchParams['companyId'],
                'status'     => BaseShieldCompany::STATUS_ACTIVE,
            ])
            ->select('resume_id')
            ->column();
        if (count($shield_resume_ids) > 0) {
            $common_query->andWhere([
                'not in',
                'r.id',
                $shield_resume_ids,
            ]);
        }
        $before_query = clone $common_query;
        $after_query  = clone $common_query;

        $before_query->leftJoin(['rtc' => BaseResumeTopConfig::tableName()],
            'r.id=rtc.resume_id and rtc.status =' . BaseResumeTopConfig::STATUS_EFFECT)
            //->andWhere(['rtc.status' => BaseResumeTopConfig::STATUS_EFFECT])
            ->andWhere([
                '>',
                'rtc.id',
                0,
            ])
            ->select([
                'r.id as resume_id',
                //'r.name',
                //'r.work_experience',
                //'r.age',
                //'r.gender',
                //'r.top_education_code',
                'r.member_id',
                //'r.residence',
                //'r.last_education_id',
                //'r.last_update_time',
                "DATE_FORMAT(m.last_active_time, '%Y-%m-%d') AS last_active_date",
                'IF(rtc.id>0,2,1) as is_resume_top',
            ]);

        $before_count = $before_query->count();
        $after_query->select([
            'r.id as resume_id',
            //'r.name',
            //'r.work_experience',
            //'r.age',
            //'r.gender',
            //'r.top_education_code',
            'r.member_id',
            //'r.residence',
            //'r.last_education_id',
            //'r.last_update_time',
            "DATE_FORMAT(m.last_active_time, '%Y-%m-%d') AS last_active_date",
        ]);
        //剔除掉置顶的人
        $resume_top_ids = BaseResumeTopConfig::getEffectResumeIds();
        if (count($resume_top_ids) > 0) {
            $after_query->andWhere([
                'not in',
                'r.id',
                $resume_top_ids,
            ]);
        }
        //        $after_count = $after_query->count();
        $after_count = self::MAX_RESUME_LIBRARY_LIST_COUNT - $before_count;
        //首先根据页数与数量进行当前页数量穿插计算
        $interlace_result = InterlacePageHelper::pageInterlace($before_count, $after_count, $this->searchParams['page'],
            $this->searchParams['pageSize'], 3, 3);

        // 有可能因为数量本身没那么多
        if ($interlace_result == false) {
            return [];
        }
        $before_list = $before_query->offset($interlace_result['offset_before'])
            ->limit($interlace_result['limit_before'])
            ->orderBy('last_active_date desc')//,rl.id asc
            ->asArray()
            ->all();
        $after_list  = $after_query->offset($interlace_result['offset_after'])
            ->limit($interlace_result['limit_after'])
            ->orderBy('last_active_date desc,r.complete desc,r.id desc')
            ->asArray()
            ->all();
        $list        = InterlacePageHelper::dataInterlace($before_list, $after_list, $interlace_result);
        $result      = [
            'list' => $list,
            'page' => [
                'count' => (int)$before_count + (int)$after_count,
                'limit' => (int)$this->searchParams['pageSize'],
                'page'  => (int)$this->searchParams['page'],
            ],
        ];
        $cacheKey    = Cache::COMPANY_RESUME_LIBRARY_SEARCH . ':' . $this->searchParams['page'] . '_' . $this->searchParams['pageSize'];
        Cache::setex($cacheKey, 10800, json_encode($result));

        return $result;
    }
}
