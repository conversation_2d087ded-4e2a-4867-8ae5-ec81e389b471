<?php

namespace common\service\commonResume;

// 邀约服务
use common\base\models\BaseCompanyInviteSystemConfig;
use common\base\models\BaseCompanyPackageChangeDetailLog;
use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseJob;
use common\base\models\BaseMember;
use common\base\models\BaseMemberMessage;
use common\base\models\BaseResume;
use common\base\models\BaseResumeLibrary;
use common\base\models\BaseResumeLibraryInviteLog;
use common\base\models\BaseResumeSetting;
use common\base\models\BaseSystemConfig;
use common\components\MessageException;
use common\helpers\DebugHelper;
use common\libs\EmailQueue;
use common\service\companyPackage\CompanyPackageApplication;
use common\service\companyPackage\ConsumeService;
use common\service\messageCenter\MessageCenterApplication;
use Faker\Provider\Base;
use queue\Producer;
use Yii;
use yii\base\Exception;

class InviteService extends BaseService
{

    protected $companyId;
    protected $resumeId;
    protected $isSendEmail;
    protected $needSendSmsResume;    // 需要发送短信的简历
    protected $isSendSms;   // 是否需要法短信
    protected $remark;

    protected $logId;

    /**
     * @var BaseJob
     */
    private $jobModel;

    public function run($companyId, $resumeId, $jobId, $remark, $isSendEmail, $isSendSms)
    {
        $this->companyId   = $companyId;
        $this->resumeId    = $resumeId;
        $this->jobId       = $jobId;
        $this->isSendEmail = $isSendEmail;
        $this->remark      = $remark;
        $this->actionType  = self::ACTION_TYPE_INVITE;
        $this->isSendSms   = $isSendSms;

        $this->setData();
        $this->check();
        $this->checkSms();

        $this->send();
        $this->log();

        return [
            'successInviteAmount' => 1,
        ];
    }

    private function checkSms()
    {
        if ($this->isSendSms == 1) {
            $this->needSendSmsResume = BaseResume::getChinaMobileResume([$this->resumeId]);

            if (count($this->needSendSmsResume) > $this->companyPackageConfigModel->sms_amount) {
                throw new MessageException('短信余量不足！');
            }
        }
    }

    private function setData()
    {
        $this->setCompany($this->companyId);
        $this->setPackage();

        $this->setResume($this->resumeId);
        $this->jobModel = BaseJob::find()
            ->select([
                'id',
                'company_id',
                'status',
                'name',
            ])
            ->where(['id' => $this->jobId])
            ->one();
    }

    private function check()
    {
        BaseMember::checkMemberIsCanceled(0, $this->resumeId);

        // 检查一下职位的情况,首先是不是这个单位的,是不是在线的
        if ($this->jobModel->company_id != $this->companyId) {
            throw new Exception('对不起，当前账号暂无操作权限，请刷新页面重试');
        }
        if ($this->jobModel->status != BaseJob::STATUS_ACTIVE) {
            // 抛出异常
            throw new Exception('只能邀请在线职位');
        }

        // 检查一下这个简历在不在简历库
        if (!BaseResume::find()
            ->where([
                'id'                => $this->resumeId,
                'is_resume_library' => BaseResume::IS_RESUME_LIBRARY_YES,
            ])
            ->exists()) {
            throw new Exception('简历不存在人才库');
        }
        // 检查30天内是否邀请过这个职位这个人
        $rs = BaseResumeLibraryInviteLog::find()
                  ->select([
                      'add_time',
                  ])
                  ->where([
                      'company_id' => $this->companyId,
                      'job_id'     => $this->jobId,
                      'resume_id'  => $this->resumeId,
                  ])
                  ->orderBy('id desc')
                  ->asArray()
                  ->one()['add_time'];
        if ($rs) {
            // 如果是30天内就不允许再邀请了
            if (CUR_TIMESTAMP - strtotime($rs) < 30 * 24 * 3600) {
                throw new Exception('30天内同一个职位不能邀请同一个人');
            }
        }

        // 检查自己是否有套餐啊
        if ($this->isFreePackage) {
            throw new Exception('普通会员不能进行邀约');
        }
        //看看当单位是否有邀约资源配置
        $invite_source_info = BaseCompanyInviteSystemConfig::find()
            ->where([
                'company_id' => $this->companyId,
                'is_delete'  => BaseCompanyInviteSystemConfig::STATUS_NORMAL,
            ])
            ->one();
        if ($invite_source_info) {
            $time       = BaseCompanyInviteSystemConfig::getTime($invite_source_info['type']);
            $limitCount = $invite_source_info['invite_number'];
        } else {
            $time       = BaseCompanyInviteSystemConfig::getTime(BaseCompanyInviteSystemConfig::TYPE_WEEK);
            $limitCount = BaseSystemConfig::getValue(BaseSystemConfig::RESUME_LIBRARY_COMPANY_INVITE_COUNT_DAY_KEY);
        }
        // 检查本周是否超过了邀约次数
        $checkCount = BaseResumeLibraryInviteLog::find()
            ->andWhere([
                'company_id' => $this->companyId,
            ])
            ->andWhere([
                'between',
                'add_time',
                $time['start_time'],
                $time['end_time'],
            ])
            ->count();

        if ($checkCount >= $limitCount) {
            throw new Exception($time['name'] . '邀约次数已经用完');
        }

        // 拿配置

        // if ($this->isSendSms) {
        //     if ($this->companyPackageConfigModel->sms_amount <= 1) {
        //     }
        // }

        // 检查一下对于这个简历是否已经邀约过了
        // 这里需要看看是否发送短信, 如果发送短信, 则需要看看短信余额是否充足

    }

    /**
     * 实际的邀请逻辑
     * @throws Exception
     */
    private function send()
    {
        // 其实就是往邀请表里面写一个邀请数据
        try {
            $model                    = new BaseResumeLibraryInviteLog();
            $model->company_id        = $this->companyId;
            $model->job_id            = $this->jobId;
            $model->resume_id         = $this->resumeId;
            $model->is_remind_check   = BaseResumeLibraryInviteLog::IS_REMIND_CHECK_NO;
            $model->remark            = $this->remark ?: '';
            $model->company_member_id = Yii::$app->user->id;
            $model->is_apply          = BaseResumeLibraryInviteLog::IS_INVITE_NO;

            if ($this->isSendEmail == 1) {
                $model->type = BaseResumeLibraryInviteLog::TYPE_EMAIL;
            }

            $isSendSms = (in_array($this->resumeId, $this->needSendSmsResume) && $this->isSendSms == 1);
            if ($isSendSms) {
                $model->type = BaseResumeLibraryInviteLog::TYPE_SMS;
            }

            if ($isSendSms && $this->isSendEmail) {
                $model->type = BaseResumeLibraryInviteLog::TYPE_EMAIL_AND_SMS;
            }

            if (!$model->save()) {
                throw new Exception($model->getFirstErrorsMessage());
            }

            $return = $this->sendMessage($model);

            $model->email_log_id = $return['emailId'];
            $model->sms_log_id   = $return['smsId'];

            $model->save();
            $this->logId = $model->id;

            // 扣除所有
            if ($model->sms_log_id) {
                $smsLogMsg = '邀约通知；简历ID：' . $model->resume_id;

                $companyPackageApplication = CompanyPackageApplication::getInstance();
                $changeLogId               = $companyPackageApplication->setSmsAmount(ConsumeService::HANDLE_TYPE_SMS_SEND,
                    $this->companyId, 1, $smsLogMsg, CompanyPackageApplication::ALGORITHM_CUT);

                BaseCompanyPackageChangeDetailLog::saveDetail([
                    'type'          => BaseCompanyPackageChangeDetailLog::TYPE_COMPANY_JOB_INVITE,
                    'company_id'    => $this->companyId,
                    'resume_id'     => $model->resume_id,
                    'resource_id'   => $model->sms_log_id,
                    'change_log_id' => $changeLogId,
                    'job_id'        => $model->job_id,
                    'member_id'     => \Yii::$app->user->id,
                ]);
            }
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }


    // private function setSms()
    // {
    //     return true;
    // }
    //
    // private function setEmail()
    // {
    //     return true;
    // }
    //
    // private function sendSms()
    // {
    // }

    /**
     * @throws \Exception
     */
    // private function sendEmail()
    // {
    //     // 找到求职者的邮箱
    //     $isSendEmail = $this->isSendEmail == 1 ? $this->isSendEmail : 0;
    //
    //     $data =  (new MessageCenterApplication())->jobInvitation($this->resumeModel->member_id, $this->jobModel->id,
    //         $this->remark, $isSendEmail);
    //
    //     return $data;
    // }

    // 这里比较简单,就是写一个站内信内求职者

    /**
     * @throws Exception
     */
    private function sendMessage(BaseResumeLibraryInviteLog $model)
    {
        $isSendEmail = 0;
        $isSendSms   = 0;
        if ($model->type == BaseResumeLibraryInviteLog::TYPE_EMAIL_AND_SMS) {
            $isSendEmail = 1;
            $isSendSms   = 1;
        } elseif ($model->type == BaseResumeLibraryInviteLog::TYPE_SMS) {
            $isSendSms = 1;
        } elseif ($model->type == BaseResumeLibraryInviteLog::TYPE_EMAIL) {
            $isSendEmail = 1;
        }

        $data = (new MessageCenterApplication())->jobInvitation($model->id, $isSendEmail, $isSendSms);

        return $data;

        //如果有备注，需加上
        // if (!empty($this->remark)) {
        //     $remark = "邀请备注：$this->remark";
        // } else {
        //     $remark = '';
        // }
        // $params = [
        //     'id'     => $this->jobId,
        //     'remark' => $remark,
        //     'tips'   => 'Tips：简历越完整，被查看的几率越高，更容易受到用人单位的青睐哦～',
        // ];
        // //判断用户是否关闭了消息通知
        // $openViewMessage = BaseResumeSetting::findOneVal(['resume_id' => $this->resumeId], 'is_company_view_me');
        // if ($openViewMessage == BaseResumeSetting::IS_JOB_INVITE_NO) {
        //     return;
        // }
        // $title = "【投递邀请】“{$this->companyModel->full_name}”对您很感兴趣，邀请您投递职位！";
        // // 写个站内信
        // BaseMemberMessage::send($this->resumeModel->member_id, BaseMemberMessage::TYPE_RESUME_JOB_APPLY_INVITE, $title,
        //     "{$this->companyModel->full_name}对您的简历很感兴趣，邀请您投递“{$this->jobModel->name}”职位，期待您的回复。",
        //     BaseMemberMessage::LINK_TYPE_JOB_DETAIL, $params);
    }
}
