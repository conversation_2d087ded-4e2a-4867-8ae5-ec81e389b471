<?php

namespace common\service\commonResume;

// 收藏服务
use common\base\models\BaseMember;
use common\base\models\BaseMemberMessage;
use common\base\models\BaseResumeLibrary;
use common\base\models\BaseResumeSetting;
use common\base\models\BaseResumeStatData;
use common\helpers\TimeHelper;
use common\models\ResumeLibraryCollect;
use Yii;
use yii\base\Exception;

class CollectService extends BaseService
{

    public function run($companyId, $resumeId)
    {
        $this->companyId = $companyId;
        $this->resumeId  = $resumeId;

        $this->setData();
        $this->check();
        $this->create();
        $this->log();
    }

    private function setData()
    {
        $this->actionType = self::ACTION_TYPE_COLLECT;
        $this->setCompany($this->companyId);
        $this->setPackage();
        $this->setResume($this->resumeId);
    }

    private function check()
    {
        BaseMember::checkMemberIsCanceled('', $this->resumeId);
        // 检查一下这个简历在不在简历库
        // if (!BaseResumeLibrary::find()
        //     ->where(['resume_id' => $this->resumeId])
        //     ->exists()) {
        //     throw new Exception('简历不存在人才库');
        // }
        // 检查自己是否有套餐
        //        if ($this->isFreePackage) {
        //            throw new Exception('普通会员不能收藏');
        //        }
    }

    private function create()
    {
        // 检查一下自己是否已经收藏了这个简历,要是收藏过了,就变为取消?
        $model = ResumeLibraryCollect::findOne([
            'company_id' => $this->companyId,
            'resume_id'  => $this->resumeId,
            'member_id'  => Yii::$app->user->id,
        ]);

        if ($model) {
            $this->actionType = self::ACTION_TYPE_CANCEL_COLLECT;
            $model->delete();
            BaseResumeStatData::updateAllCounters([
                'resume_library_collect_amount' => -1,
            ], [
                'resume_id' => $this->resumeId,
            ]);
        } else {
            $this->actionType  = self::ACTION_TYPE_COLLECT;
            $model             = new ResumeLibraryCollect();
            $model->company_id = $this->companyId;
            $model->resume_id  = $this->resumeId;
            $model->member_id  = Yii::$app->user->id;
            if (!$model->save()) {
                throw new Exception($model->getFirstErrorsMessage());
            }
            BaseResumeStatData::updateAllCounters([
                'resume_library_collect_amount' => 1,
            ], [
                'resume_id' => $this->resumeId,
            ]);
            $this->sendMessage();
        }
    }

    public function sendMessage()
    {
        $title             = "【简历收藏提醒】“{$this->companyModel->full_name}” 对您的简历非常感兴趣！投递最佳时间，请勿错过！";
        $inner_link_params = ['id' => $this->companyId];

        //判断今天是否有发生收藏的站内信了，如果没有，再发送
        $messageInfo = BaseMemberMessage::find()
            ->where([
                'type'              => BaseMemberMessage::TYPE_RESUME_COLLECT,
                'member_id'         => $this->resumeModel->member_id,
                'title'             => $title,
                'inner_link_params' => json_encode($inner_link_params),
            ])
            ->andWhere([
                'between',
                'add_time',
                TimeHelper::dayToBeginTime(date('Y-m-d')),
                TimeHelper::dayToEndTime(date('Y-m-d', strtotime('+1 days'))),
            ])
            ->one();

        if (!empty($messageInfo)) {
            return;
        }
        //判断用户是否关闭了消息通知
        $openViewMessage = BaseResumeSetting::findOneVal(['resume_id' => $this->resumeId], 'is_company_view_me');
        if ($openViewMessage == BaseResumeSetting::IS_COMPANY_VIEW_ME_NO) {
            return;
        }

        BaseMemberMessage::send($this->resumeModel->member_id, BaseMemberMessage::TYPE_RESUME_COLLECT, $title,
            "“{$this->companyModel->full_name}”对您的简历非常感兴趣并进行了收藏！现在是简历投递最佳时间，请勿错过！查看单位招聘详情！",
            BaseMemberMessage::LINK_TYPE_COMPANY_DETAIL, $inner_link_params);
    }
}
