<?php

namespace common\service\commonResume;

// 分享服务
use common\base\models\BaseArea;
use common\base\models\BaseDictionary;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseMemberLoginForm;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeLibrary;
use common\base\models\BaseResumeShare;
use common\base\models\BaseShortLink;
use common\helpers\MaskHelper;
use common\helpers\UrlHelper;
use common\helpers\UUIDHelper;
use common\libs\ShortLink;
use Faker\Provider\Uuid;
use yii\base\Exception;

class ShareService extends BaseService
{

    private $password;
    private $shorLinkId;
    private $shorLink;
    private $expireTime;
    private $sharecode;

    const DEFAULT_OVERDUE_TIME = 604800; // 默认过期时间(7天)

    const DEFAULT_PASSWORD = '8888';

    public function run($companyId, $resumeId, $password)
    {
        $this->companyId  = $companyId;
        $this->resumeId   = $resumeId;
        $this->password   = $password;
        $this->actionType = self::ACTION_TYPE_SHARE;

        $this->setData();
        $this->check();
        $this->log();

        return $this->create();
    }

    public function getInfo($companyId, $resumeId)
    {
        $this->companyId = $companyId;
        $this->resumeId  = $resumeId;
        $this->setData();

        // 组装一下分享所需的数据?

        return $this->getInfoData();
    }

    private function getInfoData()
    {
        $name             = $this->resumeModel->name;
        $age              = $this->resumeModel->age;
        $gender           = $this->resumeModel->gender;
        $workExperience   = $this->resumeModel->work_experience;
        $residence        = $this->resumeModel->residence ?: '';
        $topEducationInfo = BaseResumeEducation::getTopEducationInfo($this->resumeId);
        $majorId          = $topEducationInfo['major_id'];
        // 拿专业名称
        $majorName = BaseMajor::findOneVal(['id' => $majorId], 'name');
        if ($residence) {
            $cityName = BaseArea::findOneVal(['id' => $residence], 'name') ?: '';
        }

        // 拿头像
        $avatar     = BaseMember::findOneVal(['id' => $this->resumeModel->member_id], 'avatar');
        $realAvatar = self::getAvatar($avatar, $gender);

        $showName = MaskHelper::getName($name, $gender);
        // 最高学历
        $educationCode = $this->resumeModel->top_education_code;
        $topEducation  = BaseDictionary::getEducationName($educationCode);

        // 找一下之前是否已经分享过了
        $model = BaseResumeShare::findOne([
            'company_id' => $this->companyId,
            'resume_id'  => $this->resumeId,
        ]);

        $data = [
            'name'           => $showName,
            'age'            => $age ? $age . '岁' : '',
            'topEducation'   => $topEducation,
            'workExperience' => $workExperience ? $workExperience . '年经验' : '*年经验',
            'avatar'         => $realAvatar,
            'isShare'        => '0',
            'majorName'      => $majorName,
            'cityName'       => $cityName ?: '',
            'school'         => $topEducationInfo['school'] ?: '',
        ];

        if ($model) {
            // 续期
            $model->expire_time = $this->getExpireTime();
            $model->save();

            $code = BaseShortLink::findOneVal([
                'id' => $model->short_link_id,
            ], 'code');

            $url = UrlHelper::createShortLink($code);

            $data['url']        = $url;
            $data['password']   = $model->password;
            $data['expireTime'] = $model->expire_time;
            $data['isShare']    = '1';
        } else {
            $data['password'] = self::DEFAULT_PASSWORD;
        }

        return $data;
    }

    private function setData()
    {
        $this->setCompany($this->companyId);
        $this->setPackage();
        $this->setResumeInfo();
    }

    /**
     * 简历库来自投递的，不能判断是否在人才库，这里单独处理
     * @return void
     * @throws \yii\db\Exception
     */
    private function setResumeInfo()
    {
        //调整下结构，简历拿到这里来
        $this->resumeModel = BaseResume::findOne($this->resumeId);
        if (!$this->resumeModel) {
            throw new \yii\db\Exception('简历不存在');
        }

        BaseMember::checkMemberIsCanceled($this->resumeModel->member_id);
    }

    private function check()
    {
        // 检查密码(4位数字)
        if (!preg_match('/^\d{4}$/', $this->password)) {
            throw new Exception('密码必须是4位数字');
        }

        //前端已经做了限制，这里去除
        // 检查自己是否有套餐
//        if ($this->isFreePackage) {
//            throw new Exception('普通会员不能进行分享');
//        }
    }

    private function create()
    {
        // 看看自己是否已经分享过了?超过7天就失效了?
        $model = BaseResumeShare::findOne([
            'company_id' => $this->companyId,
            'resume_id'  => $this->resumeId,
        ]);
        if ($model) {
            $model->password    = $this->password;
            $model->expire_time = $this->getExpireTime();
            $model->save();

            $code = BaseShortLink::findOneVal([
                'id' => $model->short_link_id,
            ], 'code');

            $this->expireTime = $model->expire_time;

            $this->shorLink = UrlHelper::createShortLink($code);
        } else {
            $code            = $this->generateCode();
            $this->sharecode = $code;
            $this->createUrl();
            // 如果分享过,这个时候就直接返回之前的分享给他就可以了,如果没有分享过就创建一个分享
            $model                = new BaseResumeShare();
            $model->company_id    = $this->companyId;
            $model->resume_id     = $this->resumeId;
            $model->password      = $this->password;
            $model->short_link_id = $this->shorLinkId;
            $model->expire_time   = $this->getExpireTime();
            $model->code          = $code;
            if (!$model->save()) {
                throw new Exception($model->getFirstErrorsMessage());
            }

            $this->expireTime = $model->expire_time;
        }

        return [
            'url'        => $this->shorLink,
            'expireTime' => $this->expireTime,
            'password'   => $this->password,
        ];
    }

    /**
     * 生成一个分享码
     * @return string
     */
    private function generateCode()
    {
        $code = Uuid::uuid();
        if (BaseResumeShare::find()
            ->where(['code' => $code])
            ->exists()) {
            return self::generateCode();
        }

        return $code;
    }

    private function createUrl()
    {
        $url = UrlHelper::createResumeShare($this->sharecode);

        // 换短链
        $return = ShortLink::create($url);

        $this->shorLinkId = $return['id'];

        $this->shorLink = $return['url'];
    }

    private function getExpireTime()
    {
        $time = CUR_TIMESTAMP + self::DEFAULT_OVERDUE_TIME;

        return date('Y-m-d H:i:s', $time);
    }
}
