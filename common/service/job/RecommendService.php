<?php

namespace common\service\job;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseMajor;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEducation;
use common\helpers\TimeHelper;
use yii\base\Exception;

class RecommendService extends BaseService
{
    private $majorId;
    private $memberId;
    private $surplusNum;
    private $jobInfo;
    private $fieldList;
    private $jobIdList;
    private $publicQuery;

    /**
     * 设置初始数据
     * @param $params
     * @return $this
     * @throws Exception
     */
    public function setData($params)
    {
        $this->memberId   = $params['memberId'];
        $this->surplusNum = $params['limit'];
        if ($params['memberId']) {
            //获取用户最高学历id
            $memberTopEducationId = BaseResume::findOneVal(['member_id' => $params['memberId']], 'last_education_id');
            //获取最高学历需求专业
            $topEducationMajorId = BaseResumeEducation::findOneVal(['id' => $memberTopEducationId], 'major_id');
            //由于求职者专业是选到3级的，但是职位是到2级到，需要选上一级
            $this->majorId = BaseMajor::findOneVal(['id' => $topEducationMajorId], 'parent_id');
        }
        //获取职位信息
        $this->jobInfo     = BaseJob::findOne(['id' => $params['jobId']]);
        $this->jobIdList[] = $params['jobId'];

        return $this;
    }

    /**
     * 获取推荐列表
     * @return array
     * @throws \Exception
     */
    public function getRecommendList()
    {
        $allList = [];
        if ($this->memberId && $this->majorId) {
            //如果登陆了，且有专业id，那么使用登陆后的规则进行推荐，规则条数为6条
            $actionName = 'loginList';
            $ruleNum    = 6;
        } else {
            //如果未登陆，或者没有专业id，那么使用没登陆的规则进行推荐，规则条数为5条
            $actionName = 'unLoginList';
            $ruleNum    = 5;
        }
        $this->getSelectField();
        for ($i = 1; $i < $ruleNum + 1; $i++) {
            $fullActionName = $actionName . $i;
            if ($this->surplusNum > 0) {
                //调用公告的查询方法
                $this->getPublicQuery();
                //调用具体的查询方法
                $list = self::$fullActionName();
                foreach ($list as &$item) {
                    $this->jobIdList[] = $item['id'];
                    $item['sort']      = $i;
                }
                $allList[] = $list;
            } else {
                break;
            }
        }
        //去除数组里的空数组
        $allList = array_filter($allList);
        $jobList = [];
        foreach ($allList as $list) {
            foreach ($list as $job) {
                $jobList[] = $job;
            }
        }

        //补充字段
        return $this->getFullInfo($jobList);
    }

    /**
     * 获取完整的信息列表
     * @param $jobList
     * @return array
     * @throws \Exception
     */
    private function getFullInfo($jobList)
    {
        if (!empty($jobList)) {
            foreach ($jobList as $k => &$job) {
                //获取职位地区
                $job['areaName'] = BaseArea::getAreaName($job['city_id']);
                //获取职位单位信息
                $companyInfo          = BaseCompany::findOne(['id' => $job['company_id']]);
                $job['companyName']   = $companyInfo['full_name'];
                $job['companyType']   = BaseDictionary::getCompanyTypeName($companyInfo['type']);
                $job['companyNature'] = BaseDictionary::getCompanyNatureName($companyInfo['nature']);

                //获取薪资
                $job['wage'] = BaseJob::formatWage($job['min_wage'], $job['max_wage'], $job['wage_type']);
                //获取学历
                $job['education'] = BaseDictionary::getEducationName($job['education_type']);
                //获取经验要求
                $job['experience'] = BaseDictionary::getExperienceName($job['experience_type']);
                //获取跳转连接
                $job['url'] = BaseJob::getDetailUrl($job['id']);
                //公告
                $job['announcement'] = BaseAnnouncement::findOneVal(['id' => $job['announcement_id']], 'title');
                //发布时间
                $job['refreshTime'] = TimeHelper::formatDateByYear($job['refresh_time']);
            }

            return $jobList;
        } else {
            return [];
        }
    }

    /**
     * 设置要获取的字段
     * @return void
     */
    private function getSelectField()
    {
        $this->fieldList = [
            'j.id',
            'j.name',
            'j.wage_type',
            'j.min_wage',
            'j.max_wage',
            'j.experience_type',
            'j.education_type',
            'j.amount',
            'j.city_id',
            'j.announcement_id',
            'j.company_id',
            'j.refresh_time',
        ];
    }

    private function getPublicQuery()
    {
        $this->publicQuery = BaseJob::find()
            ->alias('j')
            ->where(['j.status' => BaseJob::STATUS_ONLINE])
            ->andFilterWhere([
                'not in',
                'j.id',
                $this->jobIdList,
            ])
            ->select($this->fieldList)
            ->limit($this->surplusNum)
            ->asArray();
    }

    /**
     * 用户登陆情况的第一种情况
     * 当前浏览职位所属单位的其他职位，满足：
     * 职位工作地点所属省份、学历要求、职位类型同当前所浏览的职位信息匹配，且需求专业包含求职者需求专业（指求职者最高学历的需求专业；须上溯至二级专业）的职位；
     * @return array|\yii\db\ActiveRecord[]
     */
    private function loginList1()
    {
        $list = $this->publicQuery->andWhere([
            'province_id'     => $this->jobInfo->province_id,
            'company_id'      => $this->jobInfo->company_id,
            'education_type'  => $this->jobInfo->education_type,
            'job_category_id' => $this->jobInfo->job_category_id,
        ])
            ->innerJoin(['r' => BaseJobMajorRelation::tableName()], 'r.job_id=j.id')
            ->andWhere(['r.major_id' => $this->majorId])
            ->all();

        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 用户登陆情况的第2种情况
     * 其他合作单位的职位：职位工作地点所属省份、职位类型同当前所浏览的职位匹配，且需求专业包含求职者需求专业（最高学历的需求专业；二级专业）的职位；
     * @return array|\yii\db\ActiveRecord[]
     */
    private function loginList2()
    {
        $list = $this->publicQuery->innerJoin(['c' => BaseCompany::tableName()], 'c.id=j.company_id')
            ->andWhere([
                'j.province_id'     => $this->jobInfo->province_id,
                'j.job_category_id' => $this->jobInfo->job_category_id,
                'c.is_cooperation'  => BaseCompany::COOPERATIVE_UNIT_YES,
            ])
            ->innerJoin(['r' => BaseJobMajorRelation::tableName()], 'r.job_id=j.id')
            ->andWhere(['r.major_id' => $this->majorId])
            ->all();

        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 用户登陆情况的第3种情况
     * 非合作单位的职位：职位工作地点所属省份、职位类型同当前所浏览的职位匹配，且需求专业包含求职者需求专业（最高学历的需求专业；二级专业）的职位；
     * @return array|\yii\db\ActiveRecord[]
     */
    private function loginList3()
    {
        $list = $this->publicQuery->innerJoin(['c' => BaseCompany::tableName()], 'c.id=j.company_id')
            ->andWhere([
                'j.province_id'     => $this->jobInfo->province_id,
                'j.job_category_id' => $this->jobInfo->job_category_id,
                'c.is_cooperation'  => BaseCompany::COOPERATIVE_UNIT_NO,
            ])
            ->innerJoin(['r' => BaseJobMajorRelation::tableName()], 'r.job_id=j.id')
            ->andWhere(['r.major_id' => $this->majorId])
            ->all();

        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 用户登陆情况的第4种情况
     * 随机推荐需求专业包含求职者需求专业（最高学历的需求专业；二级专业）的职位
     * @return array|\yii\db\ActiveRecord[]
     */
    private function loginList4()
    {
        $list             = $this->publicQuery->innerJoin(['r' => BaseJobMajorRelation::tableName()], 'r.job_id=j.id')
            ->andWhere(['r.major_id' => $this->majorId])
            ->all();
        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 用户登陆情况的第5种情况
     * 随机推荐职位类型同当前所浏览职位相同的职位
     * @return array|\yii\db\ActiveRecord[]
     */
    private function loginList5()
    {
        $list             = $this->publicQuery->andWhere(['job_category_id' => $this->jobInfo->job_category_id])
            ->all();
        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 用户登陆情况的第6种情况
     * 随机推荐职位工作地点所属省份同当前所浏览职位匹配的职位
     * @return array|\yii\db\ActiveRecord[]
     */
    private function loginList6()
    {
        $list             = $this->publicQuery->andWhere(['province_id' => $this->jobInfo->province_id])
            ->all();
        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 用户未登陆情况的第1种情况
     * 职位工作地点所属省份、学历要求、职位类型同当前所浏览的职位匹配的职位；
     * @return array|\yii\db\ActiveRecord[]
     */
    private function unLoginList1()
    {
        $list             = $this->publicQuery->andWhere([
            'province_id'     => $this->jobInfo->province_id,
            'company_id'      => $this->jobInfo->company_id,
            'education_type'  => $this->jobInfo->education_type,
            'job_category_id' => $this->jobInfo->job_category_id,
        ])
            ->all();
        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 用户未登陆情况的第2种情况
     * 其他合作单位的职位：职位工作地点所属省份、职位类型同当前所浏览的职位匹配的职位；
     * @return array|\yii\db\ActiveRecord[]
     */
    private function unLoginList2()
    {
        $list             = $this->publicQuery->innerJoin(['c' => BaseCompany::tableName()], 'c.id=j.company_id')
            ->andWhere([
                'j.province_id'     => $this->jobInfo->province_id,
                'j.job_category_id' => $this->jobInfo->job_category_id,
                'c.is_cooperation'  => BaseCompany::COOPERATIVE_UNIT_YES,
            ])
            ->all();
        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 用户未登陆情况的第3种情况
     * 非合作单位的职位：职位工作地点所属省份、职位类型同当前所浏览的职位匹配的职位；
     * @return array|\yii\db\ActiveRecord[]
     */
    private function unLoginList3()
    {
        $list             = $this->publicQuery->innerJoin(['c' => BaseCompany::tableName()], 'c.id=j.company_id')
            ->andWhere([
                'j.province_id'     => $this->jobInfo->province_id,
                'j.job_category_id' => $this->jobInfo->job_category_id,
                'c.is_cooperation'  => BaseCompany::COOPERATIVE_UNIT_NO,
            ])
            ->all();

        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 用户未登陆情况的第4种情况
     * 随机推荐职位类型同当前所浏览职位相同的职位
     * @return array|\yii\db\ActiveRecord[]
     */
    private function unLoginList4()
    {
        $list             = $this->publicQuery->andWhere(['job_category_id' => $this->jobInfo->job_category_id])
            ->all();
        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 用户未登陆情况的第5种情况
     * 随机推荐职位工作地点所属省份同当前所浏览职位匹配的职位
     * @return array|\yii\db\ActiveRecord[]
     */
    private function unLoginList5()
    {
        $list             = $this->publicQuery->andWhere(['province_id' => $this->jobInfo->province_id])
            ->all();
        $this->surplusNum -= count($list);

        return $list;
    }

}