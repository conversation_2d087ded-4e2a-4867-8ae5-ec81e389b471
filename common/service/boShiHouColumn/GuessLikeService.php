<?php

namespace common\service\boShiHouColumn;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleClickLog;
use common\base\models\BaseCompany;
use common\base\models\BaseGuessLikeAnnouncement;
use common\base\models\BaseJob;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeComplete;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeIntention;
use common\base\models\BaseResumeIntentionAreaRelation;
use common\helpers\ArrayHelper;
use common\helpers\DebugHelper;
use common\helpers\IpHelper;
use common\helpers\UrlHelper;
use common\libs\Cache;
use common\models\AnnouncementCollect;
use common\models\JobApplyRecord;

// 推荐前提，近30天发布，含博士后职位类型在线职位的关联公告
// https://www.kdocs.cn/view/l/ciSgt9KnWuTe
class GuessLikeService extends BaseService
{

    const JOB_TYPE         = 29;
    const MAX_ANNOUNCEMENT = 6;

    // 重点一二线城市
    const TOP_CITY_LIST = self::TOP_AREA_KEY_CITY;

    // 当前ip相关信息
    public $ip;
    public $cityId;
    public $provinceId;

    // 当前用户相关信息
    public $memberId                = 0;
    public $resumeId                = 0;
    public $resumeMajor             = [];
    public $resumeIntentionArea     = [];
    public $resumeIntentionCity     = [];
    public $resumeIntentionProvince = [];

    // 这些是需要过滤的（浏览、收藏、推荐过的等等）
    public $filterAnnouncementList = [];
    public $browseHistoryList      = [];
    // A1~AN查询出来的，需要下一次查询的时候，过滤掉这些
    public $queryFilterAnnouncementList = [];

    // 合集
    public $cityList     = [];
    public $majorList    = [];
    public $provinceList = [];

    public $returnList = [];

    public function getList()
    {
        // 首先获取好用户的信息
        $this->setInfo();
        $this->getLikeList();
        $this->setBrowseHistory();

        return $this->returnList;
    }

    public function setInfo()
    {
        try {
            $memberId = \Yii::$app->user->id;
            if (\Yii::$app->user->identity->type != BaseMember::TYPE_PERSON) {
                $memberId = 0;
            }
            $ip             = IpHelper::getIp();
            $this->memberId = $memberId;
        } catch (\Exception $e) {
            $this->resumeId = 0;
        }

        // 广州
        // $ip = '**************';
        // 美国
        // $ip = '***********';
        // 湖北武汉
        // $ip = '*************';

        $areaInfo = IpHelper::getAreaIds($ip);

        $this->ip         = $areaInfo['ip'];
        $this->cityId     = $areaInfo['cityId'];
        $this->provinceId = $areaInfo['provinceId'];

        if ($memberId) {
            $resumeId       = BaseResume::findOneVal(['member_id' => $memberId], 'id');
            $this->resumeId = $resumeId;
            $this->getResumeInfo();
            // 当前用户resumeId

        } else {
            if ($this->cityId) {
                $this->cityList = [$this->cityId];
            }
            if ($this->provinceId) {
                $this->provinceList = [$this->provinceId];
            }
        }

        $this->getBrowseHistory();

        $this->getFilter();
    }

    public function getResumeInfo()
    {
        // 获取简历完善度
        $resumeStepNum = BaseResumeComplete::getResumeStep($this->resumeId);

        $resumeInfo = BaseResume::find()
            ->select(['last_education_id'])
            ->where(['id' => $this->resumeId])
            ->asArray()
            ->one();

        $this->resumeMajor = BaseResumeEducation::find()
            ->select(['major_id_level_2'])
            ->where(['id' => $resumeInfo['last_education_id']])
            ->asArray()
            ->column();

        // 职位是2级的，求职者的专业是3级的
        // $this->resumeMajor = BaseMajor::find()
        //     ->select('parent_id')
        //     ->where([
        //         'id' => $topEducationMajor,
        //     ])
        //     ->asArray()
        //     ->column();

        if ($resumeStepNum <= 3) {
            // 用户未完成前三步
            $this->cityList     = [$this->cityId];
            $this->provinceList = [$this->provinceId];
            $this->majorList    = $this->resumeMajor;
        } else {
            // 所有的意向工作地点
            $intentionArea = BaseResumeIntentionAreaRelation::find()
                ->alias('a')
                ->select('a.area_id,a.level')
                ->innerJoin(['b' => BaseResumeIntention::tableName()], 'a.intention_id = b.id')
                ->where([
                    'a.resume_id' => $this->resumeId,
                    'status'      => 1,
                ])
                ->asArray()
                ->all();

            foreach ($intentionArea as $item) {
                $this->resumeIntentionArea[] = $item['area_id'];

                if ($item['level'] == 1) {
                    $this->resumeIntentionProvince[] = $item['area_id'];
                } elseif ($item['level'] == 2) {
                    $this->resumeIntentionCity[]     = $item['area_id'];
                    $this->resumeIntentionProvince[] = BaseArea::findOneVal(['id' => $item['area_id']], 'parent_id');
                }
            }

            $this->cityList     = $this->resumeIntentionCity;
            $this->provinceList = $this->resumeIntentionProvince;
            $this->majorList    = $this->resumeMajor;
        }
    }

    // 获取过滤项
    // 过滤该用户已浏览、已收藏、已投递的公告
    public function getFilter()
    {
        if ($this->memberId) {
            // 获取已浏览
            $articleList = BaseArticleClickLog::find()
                ->select('article_id')
                ->where([
                    'member_id' => $this->memberId,
                ])
                ->asArray()
                ->groupBy('article_id')
                ->column();

            // 换成公告id
            $browseAnnouncementList = BaseAnnouncement::find()
                ->select('id')
                ->where([
                    'article_id' => $articleList,
                ])
                ->asArray()
                ->column();

            // 获取已收藏
            $collectAnnouncementList = AnnouncementCollect::find()
                ->select('announcement_id')
                ->where([
                    'member_id' => $this->memberId,
                ])
                ->asArray()
                ->groupBy('announcement_id')
                ->column();

            // 获取已投递
            $deliveryAnnouncementList = JobApplyRecord::find()
                ->select('announcement_id')
                ->where([
                    'resume_id' => $this->resumeId,
                ])
                ->groupBy('announcement_id')
                ->asArray()
                ->column();
        } else {
            $browseAnnouncementList       = [];
            $collectAnnouncementList      = [];
            $deliveryAnnouncementList     = [];
            $lastSevenDayAnnouncementList = [];
        }

        // 获取7天内被推荐过的公告
        $lastSevenDayAnnouncementList = [];
        if ($this->browseHistoryList[0]) {
            foreach ($this->browseHistoryList as $item) {
                if ($item >= 3) {
                    $lastSevenDayAnnouncementList[] = $item;
                }
            }
        }

        // 合并起来
        $this->filterAnnouncementList = array_merge($browseAnnouncementList, $collectAnnouncementList,
            $deliveryAnnouncementList, $lastSevenDayAnnouncementList);

        $list = ArrayHelper::merge($browseAnnouncementList, $collectAnnouncementList);
        $list = ArrayHelper::merge($list, $deliveryAnnouncementList);
        $list = ArrayHelper::merge($list, $lastSevenDayAnnouncementList);

        $this->filterAnnouncementList = $list;
        // 全部过滤掉的公告列表

    }

    public function getLikeList()
    {
        // 暂时关闭缓存
        $data = $this->getCache();
        if (!$data) {
            $configFunctionList = [
                'getA1List',
                'getA2List',
                'getA3List',
                'getA4List',
                'getA5List',
                'getA6List',
            ];

            // 如果有专业合集就需要从A1开始，要是没有就从A4开始
            if (!$this->majorList[0]) {
                $configFunctionList = array_slice($configFunctionList, 3);
            }

            $list = [];
            foreach ($configFunctionList as $method) {
                $newList = $this->{$method}();

                foreach ($newList as $newItem) {
                    $list[] = $newItem;
                }

                $this->queryFilterAnnouncementList = ArrayHelper::merge($this->queryFilterAnnouncementList, $newList);
            }
        } else {
            $list = $data;
        }

        $idList = [];

        // list一个一个出来
        $pullCount = 0;
        foreach ($list as $item) {
            if (in_array($item, $this->filterAnnouncementList)) {
                $pullCount += 1;
                continue;
            }

            if (count($idList) >= self::MAX_ANNOUNCEMENT) {
                break;
            }
            $pullCount += 1;

            $idList[] = $item;
        }

        // 把剩余的又放回去
        $this->setCache(array_slice($list, $pullCount));

        // 在这里最终拿到公告的一些基本信息（标题，连接，发布日）
        // returnList顺序要按照list的顺序
        $infoList = BaseAnnouncement::find()
            ->alias('a')
            ->select([
                'a.id',
                'r.title',
                'r.refresh_date',
            ])
            ->innerJoin(['r' => BaseArticle::tableName()], 'r.id = a.article_id')
            ->where(['a.id' => $idList])
            ->asArray()
            ->indexBy('id')
            ->all();

        // returnList顺序要按照list的顺序

        $returnList = [];
        foreach ($idList as $it) {
            $returnList[] = [
                'id'           => $it,
                'title'        => $infoList[$it]['title'],
                'url'          => UrlHelper::createPcAnnouncementDetailPath($it),
                'time'         => date('m.d', strtotime($infoList[$it]['refresh_date'])),
                'refresh_date' => $infoList[$it]['refresh_date'],
            ];
        }

        $this->returnList = $returnList;
    }

    public function getCache()
    {
        $data = Cache::get($this->getCacheKey());

        if ($data) {
            return json_decode($data, true);
        }

        return false;
    }

    public function setCache($data)
    {
        // 保留3小时
        Cache::set($this->getCacheKey(), json_encode($data), 10800);
    }

    public function getCacheKey()
    {
        $baseKey = Cache::BOSHIHOU_HOME_GUESS_LIKE_KEY;
        if ($this->resumeId) {
            $baseKey .= ':' . $this->resumeId;
        } else {
            $baseKey .= ':' . $this->ip;
        }

        return $baseKey;
    }

    // 城市+专业匹配
    public function getA1List()
    {
        $list = $this->getAnnouncementList([
            'majorId' => $this->majorList,
            'cityId'  => $this->cityList,
        ]);

        return $list;
    }

    // 省份+专业匹配
    public function getA2List()
    {
        $list = $this->getAnnouncementList([
            'majorId'    => $this->majorList,
            'provinceId' => $this->provinceList,
        ]);

        return $list;
    }

    // 专业匹配
    public function getA3List()
    {
        $list = $this->getAnnouncementList([
            'majorId' => $this->majorList,
        ]);

        return $list;
    }

    // 城市匹配
    public function getA4List()
    {
        $list = $this->getAnnouncementList([
            'cityId' => $this->cityList,
        ]);

        return $list;
    }

    // 省份匹配
    public function getA5List()
    {
        $list = $this->getAnnouncementList([
            'provinceId' => $this->provinceList,
        ]);

        return $list;
    }

    // 重点城市匹配
    public function getA6List()
    {
        $list = $this->getAnnouncementList([
            'cityId' => self::TOP_CITY_LIST,
        ]);

        return $list;
    }

    // 需要把浏览记录存起来，便于过滤，7天内在推荐过三次的公告
    public function setBrowseHistory()
    {
        $list = $this->returnList;

        // 组成数据批量写入
        $data = [];
        foreach ($list as $item) {
            $data[] = [
                $item['id'],
                $this->resumeId ?: 0,
                $this->ip,
                date('Y-m-d H:i:s'),
            ];
        }

        $columns = [
            'announcement_id',
            'resume_id',
            'ip',
            'add_time',
        ];

        \Yii::$app->db->createCommand()
            ->batchInsert(BaseGuessLikeAnnouncement::tableName(), $columns, $data)
            ->execute();
    }

    public function getBrowseHistory()
    {
        $query = BaseGuessLikeAnnouncement::find()
            ->select('announcement_id')
            ->where([
                // 7天内
                '>=',
                'add_time',
                date('Y-m-d H:i:s', strtotime('-7 days')),
            ]);

        if ($this->resumeId) {
            $query->andWhere([
                'resume_id' => $this->resumeId,
            ]);
        } else {
            $query->andWhere([
                'resume_id' => 0,
                'ip'        => $this->ip,
            ]);
        }

        $list = $query->having('count(announcement_id) >= 3')
            ->groupBy('announcement_id')
            ->column();

        $this->browseHistoryList = $list;

        return $list;
    }

    // 根据不同条件去拿公告
    public function getAnnouncementList($params)
    {
        $query = BaseJob::find()
            ->alias('j')
            ->innerJoin(['c' => BaseCompany::tableName()], 'c.id = company_id')
            ->innerJoin(['a' => BaseAnnouncement::tableName()], 'a.id = j.announcement_id')
            ->innerJoin(['r' => BaseArticle::tableName()], 'r.id = a.article_id')
            ->select(['j.announcement_id'])
            ->where([
                'j.status'          => 1,
                'r.status'          => 1,
                'j.job_category_id' => self::JOB_TYPE,
            ])
            // 发布日期在30天内的
            ->andWhere([
                '>=',
                'r.refresh_time',
                date('Y-m-d H:i:s', strtotime('-30 days')),
            ])
            ->andWhere([
                '!=',
                'j.announcement_id',
                0,
            ])
            ->andFilterWhere([
                'not in',
                'j.announcement_id',
                $this->filterAnnouncementList,
            ])
            ->andFilterWhere([
                'not in',
                'j.announcement_id',
                $this->queryFilterAnnouncementList,
            ]);

        // 城市
        if (count($params['cityId']) > 0) {
            $query->andWhere(['j.city_id' => $params['cityId']]);
        }

        // 省份
        if (count($params['provinceId']) > 0) {
            $query->andWhere(['j.province_id' => $params['provinceId']]);
        }
        // 专业
        if (count($params['majorId']) > 0) {
            $query->innerJoin(['m' => BaseJobMajorRelation::tableName()], 'm.job_id = j.id');
            $query->andWhere(['m.major_id' => $params['majorId']]);
        }

        $query->groupBy('j.announcement_id')
            ->orderBy('c.sort desc, r.refresh_time desc');

        $list = $query->asArray()
            ->column();

        return $list;
    }

    public function log($message)
    {
        $path = \Yii::getAlias('@log/debug/guesslike.log');

        DebugHelper::log($message, $path);
    }
}