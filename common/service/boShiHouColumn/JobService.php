<?php
/**
 * create user：shannon
 * create time：2024/9/18 上午9:52
 */
namespace common\service\boShiHouColumn;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyFeatureTag;
use common\base\models\BaseCompanyFeatureTagRelation;
use common\base\models\BaseCompanyGroupRelation;
use common\base\models\BaseCompanyGroupScoreSystem;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobApplyRecordExtra;
use common\base\models\BaseJobBoshihou;
use common\base\models\BaseJobClickLog;
use common\base\models\BaseJobColumn;
use common\base\models\BaseJobExtra;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseJobTopConfig;
use common\base\models\BaseKeywordsPreprocess;
use common\base\models\BaseMajor;
use common\base\models\BaseMemberSearchLog;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseWelfareLabel;
use common\components\MessageException;
use common\helpers\InterlacePageHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\libs\Cache;
use common\service\search\PcJobListService;
use yii\db\conditions\AndCondition;
use yii\db\Expression;
use Yii;

class JobService extends BaseService
{
    /** 参数 */
    private $parmas;

    /**
     * 初始化
     * @return $this
     */
    public function init()
    {
        //先获取参数
        $this->parmas  = Yii::$app->request->get();
        $this->isCache = (bool)Yii::$app->request->get('isCache', 0);
        unset($this->parmas['isCache']);
        if (!isset($this->parmas['type']) || $this->parmas['type'] != self::JOB_TYPE) {
            $this->parmas['type'] = self::JOB_TYPE;
        }

        return $this;
    }

    /**
     * 执行业务逻辑
     */
    public function run()
    {
        //初始化
        $this->init();
        //获取SEO
        $searchData = (new SeoAnnouncementJobService())->setCache($this->isCache)
            ->getSeoSearchRun($this->parmas);

        return [
            'showcaseA1' => ShowcaseService::getAnnouncementAndJobA1(),
            'showcaseB1' => ShowcaseService::getAnnouncementAndJobB1(),
            'searchData' => $searchData,
            'listData'   => $this->getList($searchData['currentSearchParams']),
            'ranking'    => self::getRankingList($this->isCache),
        ];
    }

    /**
     * 职位榜单
     * 调用&排序规则：
     * （1）取调用至本站点的在线职位（不含已下线、已隐藏）；
     * （2）按近30天职位浏览量（全端口数据）倒序，展示排行前10的职位；
     * （3）同一单位只取浏览量最高的一个职位
     */
    public static function getRankingList($isCache)
    {
        $list = Cache::get(Cache::BOSHIHOU_JOB_RANKING);
        if ($list && !$isCache) {
            return json_decode($list, true);
        }

        return [];
    }

    public static function getRankingListCache()
    {
        $data       = BaseJobBoshihou::find()
            ->alias('jb')
            ->select([
                'j.id',
                'j.name',
                'j.major_id as majorId',
                'j.city_id as cityId',
                'c.id as companyId',
                'c.full_name as companyName',
                'count(jcl.id) as clickNumber',
            ])
            ->innerJoin(['j' => BaseJob::tableName()], 'jb.job_id = j.id')
            ->innerJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
            //            ->leftJoin(['jc' => BaseJobColumn::tableName()], 'j.id = jc.job_id')
            ->leftJoin(['jcl' => BaseJobClickLog::tableName()], 'j.id=jcl.job_id')
            ->andWhere([
                //                'jc.column_id'      => self::Column_ID,
                'j.job_category_id' => self::BOSHIHOU_JOB_CATEGORY_ID,
                'j.status'          => BaseJob::STATUS_ONLINE,
                'j.is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->groupBy('j.id')
            ->andWhere([
                '>',
                'jcl.add_time',
                date('Y-m-d H:i:s', time() - 30 * 24 * 3600),
            ])
            ->orderBy('clickNumber desc,j.id desc')
            ->limit(100)
            ->asArray()
            ->all();
        $companyIds = [];
        $list       = [];
        $i          = 0;
        foreach ($data as $item) {
            if (!in_array($item['companyId'], $companyIds) && $i < 6) {
                $majorIds = $item['majorId'] ? explode(',', $item['majorId']) : [];
                $length   = count($majorIds);
                $major    = $length > 0 ? ($length > 1 ? BaseMajor::getAllMajorName($majorIds[0]) . "等" : BaseMajor::getAllMajorName($majorIds[0])) : '';
                $list[]   = [
                    'id'          => $item['id'],
                    'name'        => $item['name'],
                    'wage'        => BaseJob::getWageText($item['id']),
                    'companyName' => $item['companyName'],
                    'cityName'    => BaseArea::getAreaName($item['cityId']),
                    'majorText'   => $major,
                    'url'         => UrlHelper::createPcJobDetailPath($item['id']),
                ];
                $i++;
                $companyIds[] = $item['companyId'];
            }
        }

        Cache::set(Cache::BOSHIHOU_JOB_RANKING, json_encode($list), 86400);

        return $list;
    }

    /**
     * 职位列表
     * @param $params
     * @throws \Exception
     */
    public function getList($params)
    {
        $page = isset($params['page']) && $params['page'] ? $params['page'] : 1;
        if (empty($params) && $page > self::AJ_LIMIT_MAX_PAGE) {
            $page = self::AJ_LIMIT_MAX_PAGE;
        }
        unset($params['page']);
        if ($page <= self::AJ_LIMIT_MAX_PAGE && empty($params)) {
            //第一页打一个缓存
            $list = Cache::get(Cache::BOSHIHOU_JOB_LIST_PAGE_ONE . ':' . $page);
            if ($list && !$this->isCache) {
                return json_decode($list, true);
            }
        }
        $query = BaseJobBoshihou::find()
            ->alias('jb')
            ->select([
                'j.id as jobId',
                'j.name as jobName',
                'j.city_id as cityId',
                'j.amount',
                'j.major_id as majorId',
                'j.announcement_id as announcementId',
                'j.welfare_tag as welfareTag',
                'j.refresh_time as refreshTime',
                'j.status',
                'je.is_pi as isPi',
                'c.id as companyId',
                'c.full_name as companyName',
                'c.type as companyType',
                'c.logo_url as companyLogo',
            ])
            ->innerJoin(['j' => BaseJob::tableName()], 'jb.job_id = j.id')
            ->innerJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id = j.announcement_id')
            ->leftJoin(['art' => BaseArticle::tableName()], 'a.article_id = art.id')
            ->leftJoin(['je' => BaseJobExtra::tableName()], 'je.job_id = jb.job_id');
        //关键字搜索
        if (isset($params['keyword']) && $params['keyword'] && trim($params['keyword'])) {
            //公告名称/单位名称
            $query->andWhere([
                'or',
                [
                    'like',
                    'j.name',
                    $params['keyword'],
                ],
                [
                    'like',
                    'a.title',
                    $params['keyword'],
                ],
                [
                    'like',
                    'c.full_name',
                    $params['keyword'],
                ],
            ]);
        }
        //地区
        if (isset($params['areaId']) && $params['areaId']) {
            $query->andWhere([
                'or',
                ['j.city_id' => $params['areaId']],
                ['j.province_id' => $params['areaId']],
            ]);
        }
        //单位类型
        if (isset($params['companyType']) && $params['companyType']) {
            $query->andWhere(['c.type' => explode('_', $params['companyType'])]);
        }
        //领域、专业条件是不会同时存在的
        //领域
        if (isset($params['specialtyId']) && $params['specialtyId']) {
            $query->leftJoin(['jmr' => BaseJobMajorRelation::tableName()], 'jmr.job_id = j.id');
            $majorIds = BaseMajor::find()
                ->select('id')
                ->where([
                    'major_specialty_id' => $params['specialtyId'],
                    'status'             => BaseMajor::STATUS_ACTIVE,
                ])
                ->column();
            $query->andWhere(['jmr.major_id' => $majorIds]);
        }
        //专业
        if (isset($params['majorId']) && $params['majorId']) {
            $query->leftJoin(['jmr' => BaseJobMajorRelation::tableName()], 'jmr.job_id = j.id');
            $query->andWhere(['jmr.major_id' => $params['majorId']]);
        }
        //发布时间筛选
        if (isset($params['refreshTime']) && $params['refreshTime']) {
            $time = $this->parseTime($params['refreshTime']);
            $query->andWhere([
                '>=',
                'j.refresh_time',
                $time['startTime'],
            ])
                ->andWhere([
                    '<=',
                    'j.refresh_time',
                    $time['endTime'],
                ]);
        }
        //是否PI直招
        if (isset($params['isPi']) && $params['isPi'] == BaseCompanyFeatureTag::PI_TAG_ID) {
            $query->andWhere(['je.is_pi' => BaseJobExtra::IS_PI_YES]);
        }

        //获取需要置顶的职位3条
        $topQuery = clone $query;
        $topList  = $topQuery->leftJoin(['jtc' => BaseJobTopConfig::tableName()], 'jtc.job_id = j.id')
            ->addSelect(new Expression("'0' as isRecommend"))
            ->addSelect(new Expression("'1' as isTop"))
            ->andWhere([
                'jtc.is_run' => BaseJobTopConfig::IS_RUN_YES,
                'j.status'   => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->orderBy('jtc.add_time desc')
            ->groupBy('j.id')
            //->limit(3)
            ->asArray()
            ->all();
        //第一组3个置顶
        $top3 = array_slice($topList, 0, 3);
        //第一组剩下置顶
        $top = array_slice($topList, 3);
        //指定的职位IDs
        $topJobIds = array_column($topList, 'jobId');
        //序号列表
        $numberQuery = clone $query;
        $numberQuery->leftJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()],
            'c.group_score_system_id = cgss.id')
            ->andWhere([
                'not in',
                'j.id',
                $topJobIds,
            ])
            ->andWhere([
                'j.status' => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->orderBy('j.status desc,j.refresh_date desc,je.is_boshihou_pay asc,cgss.score desc,j.id desc');
        $numberList = $numberQuery->groupBy('j.id')
            ->asArray()
            ->all();
        $i          = 1;
        foreach ($numberList as &$item) {
            $item['number'] = $i;
            $i++;
        }
        $jobIdNumberList = array_column($numberList, 'number', 'jobId');
        //获取第二组数据
        //2.a 规则列表
        $jobQueryA = clone $query;
        $jobListA  = $jobQueryA->addSelect([
            'doctorNumber' => BaseJobApplyRecord::find()
                ->alias('jar')
                ->select('count(jar.id)')
                ->leftJoin(['r' => BaseResume::tableName()], 'r.id= jar.resume_id')
                ->andWhere([
                    'r.top_education_code' => BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                ])
                ->andWhere([
                    '>',
                    'jar.add_time',
                    date('Y-m-d H:i:s', time() - 30 * 24 * 3600),
                ])
                ->andWhere('jar.job_id=j.id'),
        ])
            ->andWhere(['j.status' => BaseJob::STATUS_ONLINE,])
            ->andWhere(['je.is_boshihou_pay' => BaseJobExtra::IS_BOSHIHOU_PAY_YES,])
            ->andWhere([
                'not in',
                'j.id',
                $topJobIds,
            ])
            //发布时间非30天内 首发：发布时间=初始发布时间
            ->andWhere([
                '<=',
                'j.refresh_time',
                date('Y-m-d H:i:s', time() - 30 * 24 * 3600),
            ])
            ->andWhere('j.first_release_time=j.refresh_time')
            ->having([
                'doctorNumber' => 0,
            ])
            ->groupBy('j.id')
            ->orderBy('art.refresh_time desc,j.refresh_time desc,j.id desc')
            ->asArray()
            ->all();

        $jobIdsA          = [];
        $companyIdsA      = [];
        $companyIdsCA     = [];
        $announcementIdsA = [];
        foreach ($jobListA as $itemA) {
            //同一单位，最多取对外发布时间最新的3条公告；同一公告，只取对外发布时间最新的1个职位
            if ($itemA['announcementId'] > 0) {
                if ((!isset($companyIdsA[$itemA['companyId']]) || (count($companyIdsA[$itemA['companyId']]) < 3 && !in_array($itemA['announcementId'],
                                $companyIdsA[$itemA['companyId']]))) && (!isset($announcementIdsA[$itemA['announcementId']]) || (count($announcementIdsA[$itemA['announcementId']]) < 1 && !in_array($itemA['jobId'],
                                $announcementIdsA[$itemA['announcementId']])))) {
                    $jobIdsA[]                          = $itemA['jobId'];
                    $companyIdsA[$itemA['companyId']][] = $itemA['announcementId'];

                    $announcementIdsA[$itemA['announcementId']][] = $itemA['jobId'];
                }
            } else {
                //                //同一单位，只取对外发布时间最新的1个职位
                //                if (!isset($companyIdsCA[$itemA['companyId']]) || (count($companyIdsCA[$itemA['companyId']]) < 1 && !in_array($itemA['jobId'],
                //                            $companyIdsCA[$itemA['companyId']]))) {
                //                    $jobIdsA[]                           = $itemA['jobId'];
                //                    $companyIdsCA[$itemA['companyId']][] = $itemA['jobId'];
                //                }
                //纯职位
                $jobIdsA[] = $itemA['jobId'];
            }
        }
        //2.b 规则列表
        $jobQueryB        = clone $query;
        $jobListB         = $jobQueryB->andWhere(['j.status' => BaseJob::STATUS_ONLINE,])
            ->leftJoin(['cgr' => BaseCompanyGroupRelation::tableName()], 'c.id=cgr.company_id')
            ->leftJoin(['cftr' => BaseCompanyFeatureTagRelation::tableName()], 'c.id=cftr.company_id')
            ->andWhere([
                'not in',
                'j.id',
                $topJobIds,
            ])
            ->andWhere([
                'not in',
                'j.id',
                $jobIdsA,
            ])
            ->andWhere([
                'cftr.feature_tag_id' => BaseCompanyFeatureTag::PI_TAG_ID,
                'cgr.group_id'        => self::COMPANY_GROUP_ID,
            ])
            ->groupBy('j.id')
            ->orderBy('j.refresh_time desc,j.id desc')
            ->asArray()
            ->all();
        $jobIdsB          = [];
        $announcementIdsB = [];
        foreach ($jobListB as $itemB) {
            //同一公告，只取对外发布时间最新的1个职位
            if ($itemB['announcementId'] > 0) {
                if ((!isset($announcementIdsB[$itemB['announcementId']]) || count($announcementIdsB[$itemB['announcementId']]) < 1)) {
                    $jobIdsB[]                                    = $itemB['jobId'];
                    $announcementIdsB[$itemB['announcementId']][] = $itemB['jobId'];
                }
            } else {
                //纯职位
                $jobIdsB[] = $itemB['jobId'];
            }
        }
        //2.c 规则列表
        $jobQueryC        = clone $query;
        $jobListC         = $jobQueryC->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'aa.article_id = art.id')
            ->andWhere(['j.status' => BaseJob::STATUS_ONLINE,])
            ->andWhere([
                'not in',
                'j.id',
                $topJobIds,
            ])
            ->andWhere([
                'not in',
                'j.id',
                $jobIdsA,
            ])
            ->andWhere([
                'not in',
                'j.id',
                $jobIdsB,
            ])
            ->andWhere([
                'aa.type' => BaseArticleAttribute::ATTRIBUTE_DOCTOR_PUSH,
            ])
            ->orderBy('j.refresh_time desc,j.id desc')
            ->groupBy('j.id')
            ->asArray()
            ->all();
        $jobIdsC          = [];
        $announcementIdsC = [];
        foreach ($jobListC as $itemC) {
            //同一公告，只取对外发布时间最新的1个职位
            if ($itemC['announcementId'] > 0) {
                if ((!isset($announcementIdsC[$itemC['announcementId']]) || count($announcementIdsC[$itemC['announcementId']]) < 1)) {
                    $jobIdsC[]                                    = $itemC['jobId'];
                    $announcementIdsC[$itemC['announcementId']][] = $itemC['jobId'];
                }
            } else {
                //纯职位
                $jobIdsC[] = $itemC['jobId'];
            }
        }
        //第二组职位集合
        $JobIdRecommend = array_merge($jobIdsA, $jobIdsB, $jobIdsC);
        // 按$JobIdRecommend获取职位并排序
        // 排序规则：
        //  1、按发布日期倒序；
        //  2、同一发布日：
        //  （1）“加推”属性 > 含“PI”单位特色标签的单位的职位 > 其他符合条件的职位；
        //  （2）其次，按职位所属单位群组权重分值倒序展示；单位群组权重分值相同，再按职位ID倒序展示。
        $recommendQuery = clone $query;
        $recommendList  = $recommendQuery->addSelect(['recommendType' => new Expression("CASE WHEN aa.type=22 THEN 300 WHEN cftr.feature_tag_id=1 THEN 200 ELSE 100 END")])
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'aa.article_id = art.id and aa.type=22')
            ->leftJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'c.group_score_system_id = cgss.id')
            ->leftJoin(['cftr' => BaseCompanyFeatureTagRelation::tableName()],
                'c.id=cftr.company_id and cftr.feature_tag_id=1')
            ->addSelect(new Expression("'0' as isTop"))
            ->addSelect(new Expression("'1' as isRecommend"))
            ->andWhere(['j.id' => $JobIdRecommend])
            ->orderBy('j.refresh_date desc,recommendType desc,cgss.score desc,j.id desc')
            ->asArray()
            ->all();
        foreach ($recommendList as &$recommendItem) {
            $recommendItem['number'] = $jobIdNumberList[$recommendItem['jobId']];
        }
        //3、第三组普通列表
        $generalQuery = clone $query;
        $generalQuery->leftJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()],
            'c.group_score_system_id = cgss.id')
            ->addSelect(new Expression("'0' as isTop"))
            ->addSelect(new Expression("'0' as isRecommend"))
            ->andWhere([
                'not in',
                'j.id',
                $topJobIds,
            ])
            ->andWhere([
                'not in',
                'j.id',
                $JobIdRecommend,
            ])
            ->andWhere([
                'j.status' => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->orderBy('j.status desc,j.refresh_date desc,je.is_boshihou_pay asc,cgss.score desc,j.id desc')
            ->groupBy('j.id');
        if (empty($params)) {
            $top3Count          = count($top3);
            $topCount           = count($top);
            $recommendListCount = count($recommendList);
            $generalQuery->limit((self::AJ_LIMIT_MAX_PAGE * 20) - $top3Count - $topCount - $recommendListCount);
        }
        $generalList = $generalQuery->asArray()
            ->all();
        foreach ($generalList as &$generalItem) {
            $generalItem['number'] = $jobIdNumberList[$generalItem['jobId']];
        }

        $listOriginal  = [];
        $listKey       = 0;
        $listItemEmpty = [];
        // 职位穿插规则：
        //（1）3急-  3普-1急-2荐-  3普-1急-2荐…
        //    任一组别条数不足时，直接接入下一组别，如：
        //    ① 普不足3条-1急-2荐-1急-2荐…
        //    ② 急不足，则3普-2荐-3普-2荐…
        //    ③ 荐不足，则3普-1急-3普-1急…
        //    ④ 急、荐均不足，则普-普-普-普-普-普…
        //（2）优推职位组的职位，其穿插排序不得低于其自然排序（即作为普通职位时的排序序号）；
        //    若低于，则该条优推职位，按自然排序生效（此时不显示“荐”标签&【优推职位卡片样式
        $generalNumber      = 0;
        $generalNumberMax   = 3;
        $recommendNumber    = 0;
        $recommendNumberMax = 2;
        $topNumber          = 0;
        $topNumberMax       = 1;
        $interleavedSort    = 1;
        $generalListSort    = [];
        $topSort            = [];
        $recommendListSort  = [];
        while (count($generalList) > 0 || count($recommendList) > 0 || count($top) > 0) {
            //1、先处理$generalList=3
            if (count($generalList) > 0) {
                //在$generalList中在头部取出三条普通职位放入$list
                for ($i = 0; $i < $generalNumberMax; $i++) {
                    if (count($generalList) > 0) {
                        $generalInfo = array_shift($generalList);
                        if (isset($generalInfo['interleavedSortOriginal']) && $generalInfo['interleavedSortOriginal'] > 0) {
                            $generalInfo['interleavedSort'] = $generalInfo['interleavedSortOriginal'];
                        } else {
                            $generalInfo['interleavedSort'] = $interleavedSort;
                            $interleavedSort++;
                        }
                        $generalInfo['listKey'] = $listKey;
                        $listKey++;
                        $generalListSort[] = $generalInfo;
                    }
                }
            }
            //2、再处理$top=1
            if (count($top) > 0) {
                //在$top中取出一条置顶职位放入$list
                $topInfo                    = array_shift($top);
                $topInfo['interleavedSort'] = $interleavedSort;
                $topInfo['listKey']         = $listKey;
                $topSort[]                  = $topInfo;
                $listKey++;
                $interleavedSort++;
            }
            //3、再处理$recommendList=2
            if (count($recommendList) > 0) {
                //在$recommendList中取出两条优推职位放入$list
                for ($j = 0; $j < $recommendNumberMax; $j++) {
                    if (count($recommendList) > 0) {
                        $recommendInfo = array_shift($recommendList);
                        if ($recommendInfo['number'] < $interleavedSort) {
                            //变成普通职位
                            $recommendInfo['isRecommend']             = '2';
                            $recommendInfo['interleavedSortOriginal'] = $interleavedSort;
                            $generalListSort[]                        = $recommendInfo;
                        } else {
                            $recommendInfo['interleavedSort'] = $interleavedSort;
                            $recommendInfo['listKey']         = $listKey;
                            $recommendListSort[]              = $recommendInfo;
                        }
                        $listKey++;
                        $interleavedSort++;
                    }
                }
            }
        }
        //$generalListSort二维数组按照number排序
        usort($generalListSort, function ($a, $b) {
            return $a['number'] - $b['number'];
        });
        //重置索引
        $generalListSort = array_values($generalListSort);
        while (count($generalListSort) > 0 || count($recommendListSort) > 0 || count($topSort) > 0) {
            //1、先处理$generalList=3
            if (count($generalListSort) > 0) {
                //在$generalList中在头部取出三条普通职位放入$list
                for ($i = 0; $i < $generalNumberMax; $i++) {
                    if (count($generalListSort) > 0) {
                        $generalInfoSort = array_shift($generalListSort);
                        $listOriginal[]  = $generalInfoSort;
                    }
                }
            }
            //2、再处理$top=1
            if (count($topSort) > 0) {
                //在$top中取出一条置顶职位放入$list
                $topInfoSort    = array_shift($topSort);
                $listOriginal[] = $topInfoSort;
            }
            //3、再处理$recommendList=2
            if (count($recommendListSort) > 0) {
                //在$recommendList中取出两条优推职位放入$list
                for ($j = 0; $j < $recommendNumberMax; $j++) {
                    if (count($recommendListSort) > 0) {
                        $recommendInfoSort = array_shift($recommendListSort);
                        $listOriginal[]    = $recommendInfoSort;
                    }
                }
            }
        }
        //这时候将三条置顶职位放到第一位
        $listOriginal = array_merge($top3, $listOriginal);
        $count        = count($listOriginal);
        $pageCount    = ceil($count / 20);
        if ($count > 0 && $page > $pageCount) {
            Yii::$app->response->setStatusCode(404)
                ->send();
            echo Yii::$app->view->renderFile('@app/views/home/<USER>', ['message' => '这里是404页面']);
            exit;
        }
        $pageSize = 20;
        $offset   = ($page - 1) * $pageSize;
        $list     = array_slice($listOriginal, $offset, $pageSize);
        foreach ($list as &$item) {
            //公告名称
            $item['announcementName'] = $item['announcementId'] > 0 ? BaseAnnouncement::findOne($item['announcementId'])->title : '';
            //福利名称
            $item['welfareName'] = $item['welfareTag'] ? implode('、',
                BaseWelfareLabel::getWelfareLabelNameList($item['welfareTag'])) : '';
            //专业名称
            $item['majorName'] = BaseMajor::getAllMajorName(explode(',', $item['majorId']));
            //单位类型名称
            $item['companyTypeName'] = BaseDictionary::getCompanyTypeName($item['companyType']);
            //公司logo
            $item['companyLogo'] = BaseCompany::getCompanyLogo($item['companyLogo']);
            //公告详情url
            $item['announcementUrl'] = $item['announcementId'] > 0 ? UrlHelper::createPcAnnouncementDetailPath($item['announcementId']) : '';
            //职位详情url
            $item['jobUrl'] = UrlHelper::createPcJobDetailPath($item['jobId']);
            //公司详情url
            $item['companyUrl'] = UrlHelper::createPcCompanyDetailPath($item['companyId']);
            //薪资
            $item['wage'] = BaseJob::getWageText($item['jobId']);
            //地区
            $item['cityName']    = BaseArea::getAreaName($item['cityId']);
            $item['refreshTime'] = TimeHelper::formatDateByYear($item['refreshTime'], '.');
        }

        $result = [
            'list'      => $list,
            'count'     => $count,
            'pageCount' => $pageCount,
        ];
        //第一页 无参数 允许缓存 则缓存
        if ($page <= self::AJ_LIMIT_MAX_PAGE && empty($params)) {
            //缓存60分钟
            Cache::set(Cache::BOSHIHOU_JOB_LIST_PAGE_ONE . ':' . $page, json_encode($result), 3600);
        }

        return $result;
    }

    /**
     * 职位列表
     * @param $params
     * @throws \Exception
     */
    private function getList123($params)
    {
        $page = isset($params['page']) && $params['page'] ? $params['page'] : 1;
        unset($params['page']);
        if ($page == 1 && empty($params)) {
            //第一页打一个缓存
            $list = Cache::get(Cache::BOSHIHOU_JOB_LIST_PAGE_ONE);
            if ($list && !$this->isCache) {
                return json_decode($list, true);
            }
        }
        $query = BaseJobBoshihou::find()
            ->alias('jb')
            ->select([
                'j.id as jobId',
                'j.name as jobName',
                'j.city_id as cityId',
                'j.amount',
                'j.major_id as majorId',
                'j.announcement_id as announcementId',
                'j.welfare_tag as welfareTag',
                'j.refresh_time as refreshTime',
                'j.status',
                'je.is_pi as isPi',
                'c.id as companyId',
                'c.full_name as companyName',
                'c.type as companyType',
                'c.logo_url as companyLogo',
            ])
            ->innerJoin(['j' => BaseJob::tableName()], 'jb.job_id = j.id')
            ->innerJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id = j.announcement_id')
            ->leftJoin(['art' => BaseArticle::tableName()], 'a.article_id = art.id')
            ->leftJoin(['je' => BaseJobExtra::tableName()], 'je.job_id = jb.job_id');
        //关键字搜索
        if (isset($params['keyword']) && $params['keyword'] && trim($params['keyword'])) {
            //公告名称/单位名称
            $query->andWhere([
                'or',
                [
                    'like',
                    'j.name',
                    $params['keyword'],
                ],
                [
                    'like',
                    'c.full_name',
                    $params['keyword'],
                ],
            ]);
        }
        //地区
        if (isset($params['areaId']) && $params['areaId']) {
            $query->andWhere([
                'or',
                ['j.city_id' => $params['areaId']],
                ['j.province_id' => $params['areaId']],
            ]);
        }
        //单位类型
        if (isset($params['companyType']) && $params['companyType']) {
            $query->andWhere(['c.type' => explode('_', $params['companyType'])]);
        }
        //领域、专业条件是不会同时存在的
        //领域
        if (isset($params['specialtyId']) && $params['specialtyId']) {
            $query->leftJoin(['jmr' => BaseJobMajorRelation::tableName()], 'jmr.job_id = j.id');
            $majorIds = BaseMajor::find()
                ->select('id')
                ->where([
                    'major_specialty_id' => $params['specialtyId'],
                    'status'             => BaseMajor::STATUS_ACTIVE,
                ])
                ->column();
            $query->andWhere(['jmr.major_id' => $majorIds]);
        }
        //专业
        if (isset($params['majorId']) && $params['majorId']) {
            $query->leftJoin(['jmr' => BaseJobMajorRelation::tableName()], 'jmr.job_id = j.id');
            $query->andWhere(['jmr.major_id' => $params['majorId']]);
        }
        //发布时间筛选
        if (isset($params['refreshTime']) && $params['refreshTime']) {
            $time = $this->parseTime($params['refreshTime']);
            $query->andWhere([
                '>=',
                'j.refresh_time',
                $time['startTime'],
            ])
                ->andWhere([
                    '<=',
                    'j.refresh_time',
                    $time['endTime'],
                ]);
        }
        //是否PI直招
        if (isset($params['isPi']) && $params['isPi'] == BaseCompanyFeatureTag::PI_TAG_ID) {
            $query->andWhere(['je.is_pi' => BaseJobExtra::IS_PI_YES]);
        }

        //获取需要置顶的职位3条
        $topQuery = clone $query;
        $topList  = $topQuery->leftJoin(['jtc' => BaseJobTopConfig::tableName()], 'jtc.job_id = j.id')
            ->addSelect(new Expression("'0' as isRecommend"))
            ->addSelect(new Expression("'1' as isTop"))
            ->andWhere([
                'jtc.is_run' => BaseJobTopConfig::IS_RUN_YES,
                'j.status'   => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->orderBy('jtc.add_time desc')
            ->groupBy('j.id')
            //->limit(3)
            ->asArray()
            ->all();
        //第一组3个置顶
        $top3 = array_splice($topList, 0, 3);
        //第一组剩下置顶
        $top = array_splice($topList, 3);
        //指定的职位IDs
        $topJobIds = array_column($topList, 'jobId');
        //序号列表
        $numberQuery = clone $query;
        $numberQuery->leftJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()],
            'c.group_score_system_id = cgss.id')
            ->andWhere([
                'not in',
                'j.id',
                $topJobIds,
            ])
            ->andWhere([
                'j.status' => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->orderBy('j.status desc,j.refresh_date desc,je.is_boshihou_pay asc,cgss.score desc,j.id desc');
        $numberList = $numberQuery->groupBy('j.id')
            ->asArray()
            ->all();
        $i          = 1;
        foreach ($numberList as &$item) {
            $item['number'] = $i;
            $i++;
        }
        $jobIdNumberList = array_column($numberList, 'number', 'jobId');
        //获取第二组数据
        //2.a 规则列表
        $jobQueryA = clone $query;
        $jobListA  = $jobQueryA->addSelect([
            'doctorNumber' => BaseJobApplyRecord::find()
                ->alias('jar')
                ->select('count(jar.id)')
                ->leftJoin(['r' => BaseResume::tableName()], 'r.id= jar.resume_id')
                ->andWhere([
                    'r.top_education_code' => BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                ])
                ->andWhere('jar.job_id=j.id'),
        ])
            ->andWhere(['j.status' => BaseJob::STATUS_ONLINE,])
            ->andWhere(['je.is_boshihou_pay' => BaseJobExtra::IS_BOSHIHOU_PAY_YES,])
            ->andWhere([
                'not in',
                'j.id',
                $topJobIds,
            ])
            //发布时间非30天内 首发：发布时间=初始发布时间
            ->andWhere([
                '<=',
                'j.refresh_time',
                date('Y-m-d H:i:s', time() - 30 * 24 * 3600),
            ])
            ->andWhere('j.first_release_time=j.refresh_time')
            ->having([
                'doctorNumber' => 0,
            ])
            ->groupBy('j.id')
            ->orderBy('art.refresh_time desc,j.refresh_time desc,j.id desc')
            ->asArray()
            ->all();

        $jobIdsA          = [];
        $companyIdsA      = [];
        $announcementIdsA = [];
        foreach ($jobListA as $itemA) {
            //同一单位，最多取对外发布时间最新的3条公告；同一公告，只取对外发布时间最新的1个职位
            if ((!isset($companyIdsA[$itemA['companyId']]) || (count($companyIdsA[$itemA['companyId']]) < 3 && !in_array($itemA['announcementId'],
                            $companyIdsA[$itemA['companyId']]))) && (!isset($announcementIdsA[$itemA['announcementId']]) || (count($announcementIdsA[$itemA['announcementId']]) < 1 && !in_array($itemA['jobId'],
                            $announcementIdsA[$itemA['announcementId']])))) {
                $jobIdsA[]                          = $itemA['jobId'];
                $companyIdsA[$itemA['companyId']][] = $itemA['announcementId'];

                $announcementIdsA[$itemA['announcementId']][] = $itemA['jobId'];
            }
        }

        //2.b 规则列表
        $jobQueryB        = clone $query;
        $jobListB         = $jobQueryB->andWhere(['j.status' => BaseJob::STATUS_ONLINE,])
            ->leftJoin(['cgr' => BaseCompanyGroupRelation::tableName()], 'c.id=cgr.company_id')
            ->leftJoin(['cftr' => BaseCompanyFeatureTagRelation::tableName()], 'c.id=cftr.company_id')
            ->andWhere([
                'not in',
                'j.id',
                $topJobIds,
            ])
            ->andWhere([
                'not in',
                'j.id',
                $jobIdsA,
            ])
            ->andWhere([
                'cftr.feature_tag_id' => BaseCompanyFeatureTag::PI_TAG_ID,
                'cgr.group_id'        => self::COMPANY_GROUP_ID,
            ])
            ->groupBy('j.id')
            ->orderBy('j.refresh_time desc,j.id desc')
            ->asArray()
            ->all();
        $jobIdsB          = [];
        $announcementIdsB = [];
        foreach ($jobListB as $itemB) {
            //同一公告，只取对外发布时间最新的1个职位
            if (!isset($announcementIdsB[$itemB['announcementId']]) || count($announcementIdsB[$itemB['announcementId']]) < 1) {
                $jobIdsB[]                                    = $itemB['jobId'];
                $announcementIdsB[$itemB['announcementId']][] = $itemB['jobId'];
            }
        }
        //2.c 规则列表
        $jobQueryC = clone $query;
        $jobListC  = $jobQueryC->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'aa.article_id = art.id')
            ->andWhere(['j.status' => BaseJob::STATUS_ONLINE,])
            ->andWhere([
                'not in',
                'j.id',
                $topJobIds,
            ])
            ->andWhere([
                'not in',
                'j.id',
                $jobIdsA,
            ])
            ->andWhere([
                'not in',
                'j.id',
                $jobIdsB,
            ])
            ->andWhere([
                'aa.type' => BaseArticleAttribute::ATTRIBUTE_DOCTOR_PUSH,
            ])
            ->orderBy('j.refresh_time desc,j.id desc')
            ->groupBy('a.id,j.id')
            ->asArray()
            ->all();
        $jobIdsC   = array_column($jobListC, 'jobId');
        //第二组职位集合
        $JobIdRecommend = array_merge($jobIdsA, $jobIdsB, $jobIdsC);
        // 按$JobIdRecommend获取职位并排序
        // 排序规则：
        //  1、按发布日期倒序；
        //  2、同一发布日：
        //  （1）“加推”属性 > 含“PI”单位特色标签的单位的职位 > 其他符合条件的职位；
        //  （2）其次，按职位所属单位群组权重分值倒序展示；单位群组权重分值相同，再按职位ID倒序展示。
        $recommendQuery = clone $query;
        $recommendList  = $recommendQuery->addSelect(['recommendType' => new Expression("CASE WHEN aa.type=22 THEN 300 WHEN cftr.feature_tag_id=1 THEN 200 ELSE 100 END")])
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'aa.article_id = art.id')
            ->leftJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'c.group_score_system_id = cgss.id')
            ->leftJoin(['cftr' => BaseCompanyFeatureTagRelation::tableName()], 'c.id=cftr.company_id')
            ->addSelect(new Expression("'0' as isTop"))
            ->addSelect(new Expression("'1' as isRecommend"))
            ->andWhere(['j.id' => $JobIdRecommend])
            ->orderBy('j.refresh_date desc,recommendType desc,cgss.score desc,j.id desc')
            ->groupBy('j.id')
            ->asArray()
            ->all();
        foreach ($recommendList as &$recommendItem) {
            $recommendItem['number'] = $jobIdNumberList[$recommendItem['jobId']];
        }
        //3、第三组普通列表
        $generalQuery = clone $query;
        $generalList  = $generalQuery->leftJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()],
            'c.group_score_system_id = cgss.id')
            ->addSelect(new Expression("'0' as isTop"))
            ->addSelect(new Expression("'0' as isRecommend"))
            ->andWhere([
                'not in',
                'j.id',
                $topJobIds,
            ])
            ->andWhere([
                'not in',
                'j.id',
                $JobIdRecommend,
            ])
            ->andWhere([
                'j.status' => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->orderBy('j.status desc,j.refresh_date desc,je.is_boshihou_pay asc,cgss.score desc,j.id desc')
            ->groupBy('j.id')
            ->asArray()
            ->all();
        foreach ($generalList as &$generalItem) {
            $generalItem['number'] = $jobIdNumberList[$generalItem['jobId']];
        }

        $listOriginal  = [];
        $listKey       = 0;
        $listItemEmpty = [];
        // 职位穿插规则：
        //（1）3急-  3普-1急-2荐-  3普-1急-2荐…
        //    任一组别条数不足时，直接接入下一组别，如：
        //    ① 普不足3条-1急-2荐-1急-2荐…
        //    ② 急不足，则3普-2荐-3普-2荐…
        //    ③ 荐不足，则3普-1急-3普-1急…
        //    ④ 急、荐均不足，则普-普-普-普-普-普…
        //（2）优推职位组的职位，其穿插排序不得低于其自然排序（即作为普通职位时的排序序号）；
        //    若低于，则该条优推职位，按自然排序生效（此时不显示“荐”标签&【优推职位卡片样式
        $generalNumber      = 0;
        $generalNumberMax   = 3;
        $recommendNumber    = 0;
        $recommendNumberMax = 2;
        $topNumber          = 0;
        $topNumberMax       = 1;
        $interleavedSort    = 1;
        while (count($generalList) > 0 || count($recommendList) > 0 || count($top) > 0) {
            //1、先处理$generalList=3
            if (count($generalList) > 0) {
                //在$generalList中在头部取出三条普通职位放入$list
                for ($i = 0; $i < $generalNumberMax; $i++) {
                    if (count($generalList) > 0) {
                        $generalInfo = array_shift($generalList);
                        if (isset($generalInfo['interleavedSortOriginal']) && $generalInfo['interleavedSortOriginal'] > 0) {
                            $generalInfo['interleavedSort'] = $generalInfo['interleavedSortOriginal'];
                        } else {
                            $generalInfo['interleavedSort'] = $interleavedSort;
                            $interleavedSort++;
                        }
                        $generalInfo['listKey'] = $listKey;
                        $listKey++;
                        $listOriginal[] = $generalInfo;
                    }
                }
            }
            //2、再处理$top=1
            if (count($top) > 0) {
                //在$top中取出一条置顶职位放入$list
                $topInfo                    = array_shift($top);
                $topInfo['interleavedSort'] = $interleavedSort;
                $topInfo['listKey']         = $listKey;
                $listOriginal[]             = $topInfo;
                $listKey++;
                $interleavedSort++;
            }
            //3、再处理$recommendList=2
            if (count($recommendList) > 0) {
                //保存要进普通列表的元素
                $recommendListSave = [];
                //在$recommendList中取出两条优推职位放入$list
                for ($j = 0; $j < $recommendNumberMax; $j++) {
                    if (count($recommendList) > 0) {
                        $recommendInfo = array_shift($recommendList);
                        if ($recommendInfo['number'] < $interleavedSort) {
                            //变成普通职位
                            $recommendInfo['isRecommend']             = '2';
                            $recommendInfo['interleavedSortOriginal'] = $interleavedSort;
                            //塞到普通列表--直接塞入导致位置倒置
                            //array_unshift($generalList, $recommendInfo);
                            array_unshift($recommendListSave, $recommendInfo);
                            //这时候当前位置就少了一个推荐职位，所以$list缺省key记录下来
                            $listItemEmpty[]        = $listKey;
                            $listOriginal[$listKey] = [];
                        } else {
                            $recommendInfo['interleavedSort'] = $interleavedSort;
                            $listItemEmptyMin                 = count($listItemEmpty) > 0 ? array_shift($listItemEmpty) : 0;
                            //正常推荐职位放位置时候要看原始位置之前有没有空位，用则追溯放到前位，并且记录自身位置空缺，否则放入自身位置
                            if ($listItemEmptyMin > 0 && $listItemEmptyMin < $listKey) {
                                //追溯并记录自身
                                $recommendInfo['listKey']        = $listItemEmptyMin;
                                $listOriginal[$listItemEmptyMin] = $recommendInfo;
                                $listOriginal[$listKey]          = [];
                                //$list[]                          = $recommendInfo;
                                $listItemEmpty[] = $listKey;
                            } else {
                                //自身位置
                                $recommendInfo['listKey'] = $listKey;
                                $listOriginal[]           = $recommendInfo;
                            }
                        }
                        $listKey++;
                        $interleavedSort++;
                    }
                }
                if ($recommendListSave) {
                    //塞回普通列表
                    foreach ($recommendListSave as $saveItem) {
                        array_unshift($generalList, $saveItem);
                    }
                }
            }
        }
        //把剩下的空位置删掉
        foreach ($listItemEmpty as $key) {
            unset($listOriginal[$key]);
        }
        //         重新索引数组
        $listOriginal = array_values($listOriginal);
        //这时候将三条置顶职位放到第一位
        $listOriginal = array_merge($top3, $listOriginal);
        $pageSize     = 20;
        $offset       = ($page - 1) * $pageSize;
        $list         = array_slice($listOriginal, $offset, $pageSize);

        foreach ($list as &$item) {
            //公告名称
            $item['announcementName'] = $item['announcementId'] > 0 ? BaseAnnouncement::findOne($item['announcementId'])->title : '';
            //福利名称
            $item['welfareName'] = $item['welfareTag'] ? implode('、',
                BaseWelfareLabel::getWelfareLabelNameList($item['welfareTag'])) : '';
            //专业名称
            $item['majorName'] = BaseMajor::getAllMajorName(explode(',', $item['majorId']));
            //单位类型名称
            $item['companyTypeName'] = BaseDictionary::getCompanyTypeName($item['companyType']);
            //公司logo
            $item['companyLogo'] = BaseCompany::getCompanyLogo($item['companyLogo']);
            //公告详情url
            $item['announcementUrl'] = $item['announcementId'] > 0 ? UrlHelper::createAnnouncementDetailPath($item['announcementId']) : '';
            //职位详情url
            $item['jobUrl'] = UrlHelper::createPcJobDetailPath($item['jobId']);
            //公司详情url
            $item['companyUrl'] = UrlHelper::createCompanyDetailPath($item['companyId']);
            //薪资
            $item['wage'] = BaseJob::getWageText($item['jobId']);
            //地区
            $item['cityName']    = BaseArea::getAreaName($item['cityId']);
            $item['refreshTime'] = TimeHelper::formatDateByYear($item['refreshTime'], '.');
        }
        $count  = count($listOriginal);
        $result = [
            'list'      => $list,
            'count'     => $count,
            'pageCount' => ceil($count / 20),
        ];
        //第一页 无参数 允许缓存 则缓存
        if ($page == 1 && empty($params)) {
            //缓存5分钟
            Cache::set(Cache::BOSHIHOU_JOB_LIST_PAGE_ONE, json_encode($result), 300);
        }

        return $result;
    }
}