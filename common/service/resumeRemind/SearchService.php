<?php

namespace common\service\resumeRemind;

use common\base\models\BaseAdminJobInvite;
use common\base\models\BaseCompanyViewResume;
use common\base\models\BaseJobApply;
use common\base\models\BaseResumeLibraryInviteLog;
use common\base\models\BaseResumeRemind;

class SearchService extends BaseService
{

    public function getOne($resumeId, $type)
    {
    }

    /**
     * @param $resumeId
     * @return array|\yii\db\ActiveRecord|null
     * @throws \Exception
     */
    public function getAll($resumeId)
    {

        $select = [
            "job_apply_all_count",
            "job_apply_wait_check_count",
            "job_apply_check_count",
            "job_apply_pass_count",
            "job_apply_invite_count",
            "job_apply_no_pass_count",
            "job_apply_employed_count",
            "company_view_count",
            "job_invite_count",
        ];

        // 这里直接去拿全部表数据?
        $rs = BaseResumeRemind::find()
            ->select($select)
            ->where(['resume_id' => $resumeId])
            ->asArray()
            ->one();

        if (empty($rs)) {
            $id = BaseResumeRemind::create($resumeId);

            $rs = BaseResumeRemind::find()
                ->select($select)
                ->where(['id' => $id])
                ->asArray()
                ->one();
        }

        // 前端需要全部是number
        foreach ($rs as $key => $value) {
            $rs[$key] = (int)$value;
        }

        return $rs;
    }

    public function updateAll($resumeId)
    {
        BaseJobApply::updateAll([
            'is_resume_operation_remind_check' => BaseJobApply::IS_RESUME_OPERATION_REMIND_CHECK_NO,
            'is_resume_check_remind_check'     => BaseJobApply::IS_RESUME_CHECK_REMIND_CHECK_NO,
        ], ['resume_id' => $resumeId]);

        // 找到单位查看更新
        BaseCompanyViewResume::updateAll([
            'is_resume_view' => BaseCompanyViewResume::IS_RESUME_VIEW_NO,
        ], ['resume_id' => $resumeId]);

        // 找到单位邀约
        BaseResumeLibraryInviteLog::updateAll([
            'is_remind_check' => BaseResumeLibraryInviteLog::IS_REMIND_CHECK_NO,
        ], ['resume_id' => $resumeId]);

        // 找到管理员邀约
        BaseAdminJobInvite::updateAll([
            'is_remind_check' => BaseAdminJobInvite::IS_REMIND_CHECK_NO,
        ], ['resume_id' => $resumeId]);

        (new ActionService())->updateAll($resumeId);
    }

}