<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\announcement;

use admin\models\Announcement;
use admin\models\AnnouncementHandleLog;
use admin\models\Area;
use admin\models\Article;
use admin\models\Dictionary;
use admin\models\HomeColumn;
use admin\models\Job;
use admin\models\JobEdit;
use admin\models\Major;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementAreaRelation;
use common\base\models\BaseAnnouncementEdit;
use common\base\models\BaseAnnouncementEducationRelation;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseArticleColumn;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseFile;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHwActivity;
use common\base\models\BaseHwActivityAnnouncement;
use common\base\models\BaseJob;
use common\base\models\BaseMajor;
use common\helpers\FileHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\helpers\UUIDHelper;
use Faker\Provider\Base;
use yii\base\Exception;
use Yii;

/**
 * 公告审核详情
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class AuditDetailService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 公告审核
     * @return array
     * @throws Exception
     * @throws \common\components\MessageException
     */
    public function run()
    {
        $announcementId = Yii::$app->request->get('id');
        if (!$announcementId) {
            throw new Exception('参数错误');
        }
        $select = [
            'a.id',
            'a.company_id as companyId',
            'a.add_time as addTime',
            'a.period_date as periodDate',
            'a.article_id as articleId',
            'a.work_area_id as workAreaId',
            'a.major_ids as majorIds',
            'a.apply_type as applyType',
            'a.apply_address as applyAddress',
            'a.audit_status as auditStatus',
            'a.template_id as templateId',
            'a.file_ids as fileIds',
            'a.delivery_type as deliveryType',
            'a.extra_notify_address as extraNotifyAddress',
            'a.is_attachment_notice as isAttachmentNotice',
            'a.status',
            'a.title',
            'a.address_hide_status as addressHideStatus',
            'a.sub_title as subTitle',
            'a.highlights_describe as highlightsDescribe',
            'a.background_img_file_id as backgroundImgFileId',
            'a.background_img_file_id_2 as backgroundImgFileId2',
            'a.background_img_file_id_3 as backgroundImgFileId3',
            'a.background_img_file_type as backgroundImgFileType',
            'a.activity_job_content as activityJobContent',
            'ar.home_column_id as homeColumnId',
            'ar.home_sub_column_ids as homeSubColumnIds',
            'ar.content',
            'ar.first_release_time as firstReleaseTime',
            'ar.apply_audit_time as applyAuditTime',
        ];

        $announcementInfo = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['ar' => BaseArticle::tableName()], 'ar.id = a.article_id')
            ->select($select)
            ->where(['a.id' => $announcementId])
            ->asArray()
            ->one();
        if (!$announcementInfo) {
            throw new Exception('当前公告不存在');
        }
        if ($announcementInfo['auditStatus'] != BaseAnnouncement::STATUS_AUDIT_AWAIT) {
            throw new Exception('公告不在审核状态');
        }
        $this->announcementId   = $announcementId;
        $this->announcementInfo = $announcementInfo;
        $this->setCompany($announcementInfo['companyId']);
        //这时候说明这条公告是正常的审核
        if ($this->announcementInfo['status'] == BaseAnnouncement::STATUS_STAGING) {
            //说明公告是初次审核
            $this->processInfo();
            //这里没有审核记录，展示详情内容
            $jobList                               = $this->getAddJobList();
            $this->announcementInfo['editorType']  = strval(BaseAnnouncement::TYPE_EDITOR_OTHER);
            $this->announcementInfo['jobNum']      = BaseJob::getAnnouncementJobAmount($announcementId, 3);
            $this->announcementInfo['amountCount'] = BaseJob::getAnnouncementJobRecruitAmount($announcementId, 3);
            //公告职位附件
            if (!empty($this->announcementInfo['fileIds'])) {
                $fileList = BaseAnnouncement::getAppendixList($this->announcementInfo['fileIds']);
            }
            $historyStatus      = 2;
            $historyStatusTitle = "无审核通过历史";
        } else {
            //说明公告已经有审核通过历史
            //这里有审核通过记录，拿到最新的公告审核操作记录
            $announcementHandleLog = BaseAnnouncementHandleLog::find()
                ->where([
                    'announcement_id' => $this->announcementId,
                    'handle_type'     => BaseAnnouncementHandleLog::HANDLE_TYPE_EDIT,
                ])
                ->select([
                    'id',
                    'handle_before as handleBefore',
                    'handle_after as handleAfter',
                    'editor_type as editorType',
                ])
                ->limit(1)
                ->orderBy('id desc')
                ->asArray()
                ->one();

            $this->announcementInfo['fileHandleBefore'] = [];
            $this->announcementInfo['fileHandleAfter']  = [];
            $isAnnouncementChange                       = false;
            $isJobAddChange                             = false;
            $isJobEditChange                            = false;

            switch ($announcementHandleLog['editorType']) {
                // ==仅修改公告==
                case Announcement::TYPE_EDITOR_ANNOUNCEMENT:
                    $isAnnouncementChange = true;
                    break;
                // ==仅新增职位==
                case Announcement::TYPE_ADD_JOB:
                    $isJobAddChange = true;
                    break;
                // ==仅修改职位==
                case Announcement::TYPE_EDITOR_JOB:
                    $isJobEditChange = true;
                    break;
                // ==修改职位+新增职位==
                case Announcement::TYPE_EDITOR_ADD_JOB:
                    $isJobAddChange  = true;
                    $isJobEditChange = true;
                    break;
                // ==修改公告+修改职位==
                case Announcement::TYPE_EDITOR_ANNOUNCEMENT_JOB:
                    $isAnnouncementChange = true;
                    $isJobEditChange      = true;
                    break;
                // ==修改公告+新增职位==
                case Announcement::TYPE_EDITOR_ANNOUNCEMENT_ADD_JOB:
                    $isAnnouncementChange = true;
                    $isJobAddChange       = true;
                    break;
                // ==修改公告+修改职位+新增职位==
                case Announcement::TYPE_EDITOR_ANNOUNCEMENT_JOB_ADD_JOB:
                    //修改公告
                    $isAnnouncementChange = true;
                    $isJobEditChange      = true;
                    $isJobAddChange       = true;
                    break;
            }
            if ($isAnnouncementChange) {
                $announcementEditInfo = BaseAnnouncementEdit::find()
                    ->where([
                        'announcement_id' => $this->announcementId,
                    ])
                    ->select([
                        'id',
                        'edit_content as editContent',
                    ])
                    ->orderBy('id desc')
                    ->asArray()
                    ->one();

                $editContentArr = json_decode($announcementEditInfo['editContent'], true);
                if (isset($editContentArr['file_ids'])) {
                    $fileHandleBeforeIds = $this->announcementInfo['fileIds'] ?: '';
                    $fileHandleAfterIds  = $editContentArr['file_ids'] ?: '';
                    // 是否修改公告职位附件
                    if ($fileHandleBeforeIds) {
                        $this->announcementInfo['fileHandleBefore'] = $this->getJobFileList($fileHandleBeforeIds);
                    }
                    if ($fileHandleAfterIds) {
                        $this->announcementInfo['fileHandleAfter'] = $this->getJobFileList($fileHandleAfterIds);
                    }
                }
                if (isset($editContentArr['content'])) {
                    $this->announcementInfo['announcementHandleBefore'] = $this->announcementInfo['content'] ?: '';
                    $this->announcementInfo['announcementHandleAfter']  = $editContentArr['content'] ?: '';
                }
            }
            if ($isJobAddChange) {
                $this->announcementInfo['authJobList'] = $this->getAddJobList();
            }
            if ($isJobEditChange) {
                $this->announcementInfo['modifyBeforeList'] = $this->getEditJobList();
            }
            $this->announcementInfo['editorType']    = $announcementHandleLog['editorType'];
            $this->announcementInfo['editorTypeTxt'] = BaseAnnouncement::TYPE_EDITOR_LIST[$announcementHandleLog['editorType']] ?: '';
            $historyStatus                           = 1;
            $historyStatusTitle                      = "有审核通过历史";
        }

        // 审核处理历史列表
        $handleLogList = (new AuditHistoryLogService)->setPlatform($this->operationPlatform)
            ->setParams(['id' => $this->announcementId])
            ->run();

        return [
            'baseInfo'           => $this->announcementInfo,
            'jobList'            => $jobList ?: [],
            'fileList'           => $fileList ?: [],
            'handleLogList'      => $handleLogList,
            'historyStatus'      => $historyStatus,
            'historyStatusTitle' => $historyStatusTitle,
            'isCooperation'      => $this->companyInfo->is_cooperation,
        ];
    }

    /**
     * 预处理基础信息
     * @throws \Exception
     */
    private function processInfo()
    {
        if ($this->announcementInfo['periodDate'] == TimeHelper::ZERO_TIME) {
            $this->announcementInfo['periodDate'] = '详见正文';
        } else {
            $this->announcementInfo['periodDate'] = substr($this->announcementInfo['periodDate'], 0, 10);
        }
        $this->announcementInfo['deliveryTypeTxt']       = BaseAnnouncement::DELIVERY_TYPE_NAME[$this->announcementInfo['deliveryType']] ?? '-';
        $this->announcementInfo['isAttachmentNoticeTxt'] = BaseAnnouncement::IS_ATTACHMENT_NOTICE_LIST[$this->announcementInfo['isAttachmentNotice']] ?? '-';
        if (!$this->announcementInfo['templateId']) {
            $this->announcementInfo['templateId'] = '';
        }
        //隐藏邮箱文本
        $this->announcementInfo['addressHideStatusText']  = BaseAnnouncement::ADDRESS_HIDE_STATUS_TEXT[$this->announcementInfo['addressHideStatus']];
        $this->announcementInfo['isCooperation']          = $this->companyInfo->is_cooperation;
        $this->announcementInfo['companyDeliveryType']    = $this->companyInfo->delivery_type;
        $this->announcementInfo['companyDeliveryTypeTxt'] = BaseCompany::DELIVERY_TYPE_NAME[$this->companyInfo->delivery_type];
        $this->announcementInfo['homeColumnTxt']          = BaseHomeColumn::findOneVal(['id' => $this->announcementInfo['homeColumnId']],
            'name');
        if ($this->announcementInfo['homeSubColumnIds']) {
            // 获取副栏目
            $homeSubColumnArr     = BaseHomeColumn::find()
                ->select('name')
                ->where([
                    'in',
                    'id',
                    explode(',', $this->announcementInfo['homeSubColumnIds']),
                ])
                ->asArray()
                ->all();
            $homeSubColumnNameArr = [];
            foreach ($homeSubColumnArr as $v) {
                $homeSubColumnNameArr[] = $v['name'];
            }
            $this->announcementInfo['homeSubColumnTxt'] = implode(',', $homeSubColumnNameArr);
        } else {
            $this->announcementInfo['homeSubColumnTxt'] = '-';
        }

        $this->announcementInfo['applyTypeTxt'] = '';
        if ($this->announcementInfo['applyType']) {
            $applyTypeArr = explode(',', $this->announcementInfo['applyType']);
            if (count($applyTypeArr) > 1) {
                $applyTypeTxtArr = [];
                foreach ($applyTypeArr as $v) {
                    $applyTypeTxtArr[] = BaseDictionary::getSignUpName($v);
                }
                $this->announcementInfo['applyTypeTxt'] = implode(',', $applyTypeTxtArr);
            } else {
                $this->announcementInfo['applyTypeTxt'] = BaseDictionary::getSignUpName($this->announcementInfo['applyType']);
            }
        }
        // 公告学科与职位学科整合
        $this->announcementInfo['majorTxt'] = BaseAnnouncement::getAllMajorName($this->announcementId, 'text',
            $this->announcementInfo['status']) ?: '-';
        // 省份 - 城市
        $this->announcementInfo['areaProvinceTxt'] = BaseAnnouncement::getAllProvinceName($this->announcementId,
            $this->announcementInfo['status']) ?: '-';
        $this->announcementInfo['areaCityTxt']     = BaseAnnouncement::getAllCityName($this->announcementId,
            $this->announcementInfo['status']) ?: '-';

        //学历要求
        $this->announcementInfo['educationTxt'] = BaseAnnouncement::getMinEducationName($this->announcementId,
            $this->announcementInfo['status']) ?: '-';

        // 文档属性
        $comboAttributeInfo     = BaseArticleAttribute::getComboAttributeInfo($this->announcementInfo['articleId']);
        $this->announcementInfo = array_merge((array)$this->announcementInfo, $comboAttributeInfo);

        //背景图
        $this->announcementInfo['backgroundImg'] = FileHelper::getFullUrl(BaseFile::findOneVal(['id' => $this->announcementInfo['backgroundImgFileId']],
            'path'));

        //背景图2
        $this->announcementInfo['backgroundImg2'] = FileHelper::getFullUrl(BaseFile::findOneVal(['id' => $this->announcementInfo['backgroundImgFileId2']],
            'path'));

        //背景图3
        $this->announcementInfo['backgroundImg3'] = FileHelper::getFullUrl(BaseFile::findOneVal(['id' => $this->announcementInfo['backgroundImgFileId3']],
            'path'));

        // 获取调用栏目
        $this->announcementInfo['columnTxt'] = BaseArticleColumn::getArticleColumn($this->announcementInfo['articleId']);

        // 查询公告关联的活动
        $activityAnnouncementList = BaseHwActivityAnnouncement::find()
            ->alias('aa')
            ->leftJoin(['a' => BaseHwActivity::tableName()], 'aa.activity_id = a.id')
            ->where(['aa.announcement_id' => $this->announcementId])
            ->select(['a.name'])
            ->column();

        $this->announcementInfo['activityAnnouncementListText'] = implode("、", $activityAnnouncementList);
    }

    /**
     * 获取职位附件记录数据
     */
    private function getJobFileList($fileIds)
    {
        $fileIdsArr = explode(',', $fileIds);
        $select     = [
            'id',
            'name',
            'path',
            'suffix',
        ];
        // 修改前附件记录
        $data = BaseFile::find()
            ->select($select)
            ->where(['id' => $fileIdsArr])
            ->asArray()
            ->all();
        foreach ($data as &$v) {
            $v['path'] = FileHelper::getFullUrl($v['path']);
        }

        return $data;
    }

    /**
     * 获取审核的职位列表--没有审核历史的职位列表
     * @return array|\yii\db\ActiveRecord[]
     */
    private function getAddJobList()
    {
        $list = BaseJob::find()
            ->where([
                'announcement_id' => $this->announcementId,
                'status'          => BaseJob::STATUS_WAIT,
                'audit_status'    => BaseJob::AUDIT_STATUS_WAIT_AUDIT,
            ])
            ->select([
                'id',
                'name',
                'code',
                'amount',
                'department',
                'major_id as majorId',
                'status',
                'audit_status as auditStatus',
                'education_type as educationType',
                'province_id as provinceId',
                'city_id as cityId',
                'is_negotiable as isNegotiable',
                'min_wage as minWage',
                'max_wage as maxWage',
                'wage_type as wageType',
                'period_date as periodDate',
            ])
            ->orderBy('add_time desc')
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            //薪资wage_id回显
            if ($item['isNegotiable'] <> 1) {
                $item['wageId'] = (string)BaseJob::getWageId($item['minWage'], $item['maxWage']);
            }
            if ($item['periodDate'] == TimeHelper::ZERO_TIME) {
                $item['periodDate'] = '详见正文';
            } else {
                $item['periodDate'] = date('Y-m-d', strtotime($item['periodDate']));
            }

            // 空转'-'
            $item['code']       = StringHelper::isEmpty($item['code']);
            $item['department'] = StringHelper::isEmpty($item['department']);
            $information        = [];
            if ($item['cityId']) {
                $information[] = BaseArea::getAreaName($item['cityId']) ?: '-';
            }
            if ($item['amount']) {
                $information[] = "招{$item['amount']}人";
            }
            if ($item['educationType']) {
                $information[] = BaseDictionary::getEducationName($item['educationType']) ?: '-';
            }
            if (!$item['minWage'] && !$item['maxWage']) {
                $information[] = '面议';
            } else {
                $information[] = BaseJob::formatWage($item['minWage'], $item['maxWage'], $item['wageType']) ?: '-';
            }
            if ($item['majorId']) {
                $information[] = BaseMajor::getAllMajorNameRedis(explode(',', $item['majorId']));
            }
            $item['information']          = implode(' | ', $information);
            $item['jobContact']           = BaseJob::getJobContact($item['id']);
            $item['jobContactSynergy']    = BaseJob::getJobContactSynergy($item['id']);
            $item['jobContactSynergyNum'] = count($item['jobContactSynergy']);
        }

        return $list;
    }

    /**
     * 获取审核的职位列表--有审核历史的职位列表
     * @return array|\yii\db\ActiveRecord[]
     */
    private function getEditJobList()
    {
        $jobInfo = BaseJob::find()
            ->where([
                'announcement_id' => $this->announcementId,
                'status'          => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
                'audit_status'    => BaseJob::AUDIT_STATUS_WAIT_AUDIT,
            ])
            ->select([
                'id',
                'name',
                'status',
                'file_ids as fileIds',
                'duty',
                'requirement',
                'remark',
                'announcement_id as announcementId',
                'audit_status as auditStatus',
            ])
            ->orderBy('id desc')
            ->asArray()
            ->all();

        $data = [];
        foreach ($jobInfo as $jobVal) {
            //获取编辑的数据
            $jobEditInfo = JobEdit::find()
                ->where([
                    'announcement_id' => $this->announcementId,
                    'job_id'          => $jobVal['id'],
                ])
                ->select([
                    'job_id as jobId',
                    'edit_content as editContent',
                ])
                ->asArray()
                ->one();
            $editContent = json_decode($jobEditInfo['editContent'], true);
            $jobUid      = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $jobVal['id']);
            $editKey     = array_keys($editContent);
            if (!in_array('duty', $editKey)) {
                unset($jobVal['duty']);
            }
            if (!in_array('requirement', $editKey)) {
                unset($jobVal['requirement']);
            }
            if (!in_array('remark', $editKey)) {
                unset($jobVal['remark']);
            }
            if (!in_array('file_ids', $editKey)) {
                unset($jobVal['fileIds']);
            }
            if ($jobVal['fileIds']) {
                $jobVal['fileList'] = BaseAnnouncement::getAppendixList($jobVal['fileIds']);
            } else {
                $jobVal['fileList'] = [];
            }
            if ($editContent['file_ids']) {
                $editContent['fileList'] = BaseAnnouncement::getAppendixList($editContent['file_ids']);
            } else {
                $editContent['fileList'] = [];
            }
            $modifyBeforeList['jobName']         = $jobVal['name'] . '(' . $jobUid . ')';
            $modifyBeforeList['jobHandleBefore'] = $jobVal;
            $modifyBeforeList['jobHandleAfter']  = $editContent;
            $data[]                              = $modifyBeforeList;
        }

        return $data;
    }
}