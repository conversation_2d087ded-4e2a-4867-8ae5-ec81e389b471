<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use common\base\BaseActiveRecord;
use common\base\models\BaseAdminDownloadTask;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApplyRecordExtra;
use common\base\models\BaseJobCategoryRelation;
use common\base\models\BaseJobContact;
use common\base\models\BaseJobContactSynergy;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\components\MessageException;
use common\helpers\ArrayHelper;
use common\helpers\DebugHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\libs\Cache;
use Faker\Provider\Base;
use frontendPc\models\Area;
use yii\db\Expression;

/**
 * 职位列表
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class IndexService extends BaseService
{

    private $channel;
    // 渠道业务
    /** @var string 非合作 */
    const CHANNEL_UNCOOPERATION   = 'unCooperation';
    const CACHE_UNCOOPERATION_KEY = 'cmsJobList';
    /** @var string 合作 */
    const CHANNEL_COOPERATION   = 'cooperation';
    const CACHE_COOPERATION_KEY = 'jobQuery';
    /** @var string 非合作审核 */
    const CHANNEL_UNCOOPERATION_AUDIT   = 'unCooperationAudit';
    const CACHE_UNCOOPERATION_AUDIT_KEY = 'cmsJobListAudit';
    /** @var string 合作审核 */
    const CHANNEL_COOPERATION_AUDIT   = 'cooperationAudit';
    const CACHE_COOPERATION_AUDIT_KEY = 'jobQueryAudit';
    /** 缓存列表key */
    private $fieldCacheType;
    private $fieldCacheKey = [];

    private $select;
    private $orderBy;

    /**
     * 判断是否需要
     * @return bool
     */
    private function hasNeedJoinCompany()
    {
        // 单位名称. // 单位性质      // 单位类型
        if ((isset($this->params['companyName']) && $this->params['companyName'] != '') || (isset($this->params['companyNature']) && $this->params['companyNature'] != '') || (isset($this->params['companyType']) && $this->params['companyType'] != '')) {
            return true;
        }

        return false;
    }

    /** @var array 用户查询字段 */
    protected $userSelectField = [];

    const COOPERATION_WHERE = [
        'and',
        [
            'or',
            [
                'and',
                [
                    'or',
                    [
                        '=',
                        'j.status',
                        BaseJob::STATUS_ONLINE,
                    ],
                    [
                        '=',
                        'j.status',
                        BaseJob::STATUS_OFFLINE,
                    ],
                ],
                [
                    '=',
                    'j.is_job_cooperation',
                    BaseCompany::COOPERATIVE_UNIT_YES,
                ],
            ],
            [
                'and',
                [
                    '=',
                    'j.announcement_id',
                    0,
                ],
                [
                    '=',
                    'j.status',
                    BaseJob::STATUS_WAIT,
                ],
                [
                    '=',
                    'j.is_job_cooperation',
                    BaseCompany::COOPERATIVE_UNIT_YES,
                ],
                [
                    'or',
                    [
                        '=',
                        'j.audit_status',
                        BaseJob::AUDIT_STATUS_WAIT,
                    ],
                    [
                        '=',
                        'j.audit_status',
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT,
                    ],
                    [
                        '=',
                        'j.audit_status',
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT,
                    ],
                ],
                [
                    '=',
                    'j.create_type',
                    BaseJob::CREATE_TYPE_AGENT,
                ],
            ],
        ],
    ];

    const UNCOOPERATION_WHERE = [
        'and',
        [
            'or',
            [
                'and',
                [
                    'in',
                    'j.status',
                    [
                        BaseJob::STATUS_ONLINE,
                        BaseJob::STATUS_OFFLINE,
                    ],
                ],
                [
                    '=',
                    'j.is_job_cooperation',
                    BaseCompany::COOPERATIVE_UNIT_NO,
                ],
            ],
            [
                'and',
                [
                    '=',
                    'j.announcement_id',
                    0,
                ],
                [
                    '=',
                    'j.status',
                    BaseJob::STATUS_WAIT,
                ],
                [
                    '=',
                    'j.is_job_cooperation',
                    BaseCompany::COOPERATIVE_UNIT_NO,
                ],
                [
                    'in',
                    'j.audit_status',
                    [
                        BaseJob::AUDIT_STATUS_WAIT,
                        BaseJob::AUDIT_STATUS_REFUSE_AUDIT,
                        BaseJob::AUDIT_STATUS_WAIT_AUDIT,
                    ],
                ],
            ],
        ],
    ];

    public function __construct($channel = null)
    {
        $this->channel = $channel;
        parent::__construct();
    }

    /**
     * 检查用户选择字段存在
     * @param $field
     * @return bool
     */
    public function checkUserSelectFieldExist($field)
    {
        if ($this->operationPlatform == self::PLATFORM_TIMER) {
            return true;
        }

        $redisKey = [
            self::CHANNEL_UNCOOPERATION => 'cmsJobList',
            self::CHANNEL_COOPERATION   => 'jobQuery',
        ];

        if (!isset($redisKey[$this->channel])) {
            return false;
        }

        // 多次使用避免重复查询
        if (!$this->userSelectField) {
            $redisKey  = $redisKey[$this->channel];
            $selectKey = Cache::PC_ADMIN_TABLE_STAGING_FIELD_KEY . ':' . $redisKey . ':' . \Yii::$app->user->id;
            $selectKey = Cache::get($selectKey);
            $selectKey = explode(',', $selectKey);

            $this->userSelectField = $selectKey;
        }

        $field = StringHelper::changeStrToFilterArr($field);
        foreach ($field as $item) {
            if (!in_array($item, $this->userSelectField)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取缓存
     * @param $key
     * @return void
     */
    private function getFieldCacheKey($key)
    {
        $adminId              = \Yii::$app->user->id;
        $this->fieldCacheType = $key;
        $fieldCacheKey        = Cache::get(Cache::PC_ADMIN_TABLE_STAGING_FIELD_KEY . ':' . $key . ':' . $adminId);
        if ($fieldCacheKey) {
            $this->fieldCacheKey = explode(',', $fieldCacheKey);
        }
    }

    /**
     * 获取公共Query
     * @param $params
     * @return \yii\db\ActiveQuery
     */
    private function buildBaseQuery($params)
    {
        $commonSelect = [
            // 职位id、职位名称
            'j.id',
            'j.uuid',
            'j.name',
            // 职位基本信息(经验，教育，地址，薪资)
            'j.experience_type',
            'j.education_type',
            'j.city_id',
            'j.min_wage',
            'j.max_wage',
            'j.wage_type',
            // 发布模式
            'j.is_article',
            // 关联公告（id）
            'j.announcement_id',
            'j.company_id',
            // 点击
            'j.click',
            // 投递方式
            'j.delivery_way',
            // 招聘状态
            'j.status',
            // 审核状态
            'j.audit_status',
            // 初始发布时间
            'j.first_release_time',
            // 刷新时间
            'j.real_refresh_time',
            // 发布时间
            'j.refresh_time',
            // 发布人
            'j.creator',
            'j.create_type',
            'j.create_id',
            // 审核人
            'j.audit_admin_name',
            // 显示状态
            'j.is_show',
            // 招聘人数
            'j.amount',
            // 用人部门
            'j.department',
            // 是否是小程序
            'j.is_miniapp',
            // 下线时间
            'j.offline_time',
            // 预计到期时间
            'j.period_date',
            // 创建时间
            'j.add_time',
            // 投递限制
            'j.delivery_limit_type',
            // 申请审核时间
            'j.apply_audit_time',
            // 申请人
            'j.apply_name',
            'j.apply_id_type',
            'j.apply_id',
        ];

        if (in_array('majorId', $this->fieldCacheKey)) {
            $commonSelect[] = 'j.major_id';
        }

        $query = BaseJob::find()
            ->alias('j');

        if ($this->hasNeedJoinCompany()) {
            $query->innerJoin(['c' => BaseCompany::tableName()], 'c.id = j.company_id');
            $commonSelect[] = 'c.full_name';
        }

        // 公告关联
        if ((isset($this->params['announcementName']) && $this->params['announcementName'] != '') || (isset($this->params['deliveryType']) && $this->params['deliveryType'] != '') || (isset($this->params['deliveryWay']) && $this->params['deliveryWay'] != '')) {
            $query->innerJoin(['ann' => BaseAnnouncement::tableName()], 'ann.id = j.announcement_id');
            $announcementSelect = [
                'ann.delivery_way as announcement_delivery_way',
                'ann.title as announcement_title',
                'ann.audit_status as announcement_audit_status',
                'ann.status as announcement_status',
            ];
            $commonSelect       = ArrayHelper::merge($commonSelect, $announcementSelect);
        }

        // 投递总数（这里注意params 里有sortApplyTotal那必定是显示了applyTotal所以不用担心没有连接这个表）
        if (in_array('applyTotal', $this->fieldCacheKey) || $this->operationPlatform == self::PLATFORM_TIMER) {
            //在里面
            $query->leftJoin(['jare' => BaseJobApplyRecordExtra::tableName()], 'jare.job_id = j.id')
                ->groupBy('j.id');
            $commonSelect[] = 'jare.total as apply_total';
        }

        // 职位类型
        if (isset($params['jobCategoryId']) && $params['jobCategoryId'] != '') {
            $query->innerJoin(['jcr' => BaseJobCategoryRelation::tableName()], 'j.id = jcr.job_id')
                ->groupBy('j.id');
        }
        // 需求专业
        if (isset($params['majorId']) && $params['majorId'] != '') {
            $query->innerJoin(['jmr' => BaseJobMajorRelation::tableName()], 'j.id = jmr.job_id')
                ->groupBy('j.id');
        }
        // 联系人
        if (isset($params['contact']) && $params['contact'] != '') {
            $query->innerJoin(['jc' => BaseJobContact::tableName()], 'jc.job_id = j.id');
            $query->innerJoin(['jc_cmi' => BaseCompanyMemberInfo::tableName()],
                'jc_cmi.id = jc.company_member_info_id');
            $query->innerJoin(['jc_m' => BaseMember::tableName()], 'jc_m.id = jc_cmi.member_id')
                ->groupBy('j.id');
        }
        // 协同子账号
        if (isset($params['contactSynergy']) && $params['contactSynergy'] != '') {
            $query->innerJoin(['jcs' => BaseJobContactSynergy::tableName()], 'jcs.job_id = j.id');
            $query->innerJoin(['jcs_cmi' => BaseCompanyMemberInfo::tableName()],
                'jcs_cmi.id = jcs.company_member_info_id');
            $query->innerJoin(['jcs_m' => BaseMember::tableName()], 'jcs_m.id = jcs_cmi.member_id')
                ->groupBy('j.id');
        }

        // 设置order
        $order = BaseActiveRecord::getOrderByInParams($params, [
            [
                // 点击数量
                'sortClick',
                'j.click',
            ],
            [
                // 投递数量
                'sortApplyTotal',
                'jare.total',
            ],
            [
                // 发布时间
                'sortRefreshTime',
                'j.refresh_time',
            ],
            [
                // 刷新时间
                'sortRealRefreshTime',
                'j.real_refresh_time',
            ],
            [
                // 初始发布时间
                'sortFirstReleaseTime',
                'j.first_release_time',
            ],
            [
                // 创建时间
                'sortAddTime',
                'j.add_time',
            ],
            [
                // 申请审核时间
                'sortApplyAuditTime',
                'j.apply_audit_time',
            ],
        ]);

        if (count($order) < 1) {
            switch ($this->channel) {
                case self::CHANNEL_UNCOOPERATION_AUDIT:
                case self::CHANNEL_COOPERATION_AUDIT:
                    $order[] = 'j.apply_audit_time desc';
                    break;
                //                case self::CHANNEL_COOPERATION:
                //                case self::CHANNEL_UNCOOPERATION:
                //                    // FIELD(status, 1, 0, 3)
                //                    $order[] = 'FIELD(j.status,' . implode(',', [
                //                            BaseJob::STATUS_ONLINE,
                //                            BaseJob::STATUS_WAIT,
                //                            BaseJob::STATUS_OFFLINE,
                //                        ]) . ')';
                //                    $order[] = 'j.refresh_time desc';
                //                    break;
            }
        }

        $order[] = 'j.id desc';
        //        $orderBy       = new Expression(implode(',', $order));
        $orderBy       = implode(',', $order);
        $this->orderBy = $orderBy;

        // 设置where条件
        $query->andWhere($this->buildBaseQueryWhere($params));
        $this->select = $commonSelect;

        return $query;
    }

    /**
     * 获取非合作单位职位列表
     * @param $params
     * @return array
     */
    public function unCooperationRun($params)
    {
        $this->getFieldCacheKey(self::CACHE_UNCOOPERATION_KEY);

        return $this->getList($params, self::UNCOOPERATION_WHERE);
    }

    /**
     * 获取非合作单位未审核职位列表（纯职位）
     * @param $params
     * @return array
     */
    public function unCooperationAuditRun($params)
    {
        $this->getFieldCacheKey(self::CACHE_UNCOOPERATION_AUDIT_KEY);

        // 优先条件
        return $this->getList($params, [
            'and',
            [
                '<>',
                'j.status',
                BaseJob::STATUS_DELETE,
            ],
            [
                '=',
                'j.is_job_cooperation',
                BaseCompany::COOPERATIVE_UNIT_NO,
            ],
            [
                '=',
                'j.audit_status',
                BaseJob::AUDIT_STATUS_WAIT_AUDIT,
            ],
            [
                '=',
                'j.is_article',
                BaseJob::IS_ARTICLE_NO,
            ],
        ]);
    }

    /**
     * 获取合作单位职位列表
     * @param $params
     * @return array
     */
    public function cooperationRun($params)
    {
        $this->getFieldCacheKey(self::CACHE_COOPERATION_KEY);

        return $this->getList($params, self::COOPERATION_WHERE);
    }

    /**
     * 获取合作单位未审核职位列表（纯职位）
     * @param $params
     * @return array
     */
    public function cooperationAuditRun($params)
    {
        $this->getFieldCacheKey(self::CACHE_COOPERATION_AUDIT_KEY);

        // 优先条件
        return $this->getList($params, [
            'and',
            [
                '<>',
                'j.status',
                BaseJob::STATUS_DELETE,
            ],
            [
                '=',
                'j.is_job_cooperation',
                BaseCompany::COOPERATIVE_UNIT_YES,
            ],
            [
                '=',
                'j.audit_status',
                BaseJob::AUDIT_STATUS_WAIT_AUDIT,
            ],
            [
                '=',
                'j.is_article',
                BaseJob::IS_ARTICLE_NO,
            ],
        ]);
    }

    /**
     * 获取列表
     * @param $params
     * @return array
     */
    public function getList($params, $defaultWhere = [])
    {
        $this->params = $params;
        $query        = $this->buildBaseQuery($params);
        if ($defaultWhere) {
            $query->andWhere($defaultWhere);
        }

        $count = $query->select('j.id')
            ->count();

        $pageSize = $params['limit'] ?: \Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $params['page'], $pageSize);
        $jobList  = $query->offset($pages['offset'])
            ->select($this->select)
            ->orderBy($this->orderBy)
            ->limit($pages['limit'])
            ->asArray()
            ->all();

        DebugHelper::writeLog($query->createCommand()
            ->getRawSql(), '获取职位列表');

        // 返回的列表顺序
        $jobList = $this->formatIndex($jobList);

        return [
            'list' => $jobList,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$params['page'],
            ],
        ];
    }

    private function formatIndex($jobList)
    {
        $educationTypeList = BaseDictionary::getEducationList();
        $experienceList    = BaseDictionary::getExperienceList();
        $list              = [];
        // 联系人列表
        $jobContactList = [];
        // 单位名称
        $companyList = [];
        // 联系人协同列表
        $jobContactSynergyListWithJobId = [];
        $contactBool                    = in_array('contactSynergy', $this->fieldCacheKey) || in_array('contact',
                $this->fieldCacheKey) || in_array('contactSynergyNum',
                $this->fieldCacheKey) || in_array('contactSynergyInfo',
                $this->fieldCacheKey) || $this->operationPlatform == self::PLATFORM_TIMER;
        if ($jobList) {
            $jobIds = array_column($jobList, 'id');
            // 如果没有关联单位
            if (!$this->hasNeedJoinCompany() && (in_array('creator', $this->fieldCacheKey) || in_array('company', $this->fieldCacheKey) || in_array($this->channel, [
                        self::CHANNEL_UNCOOPERATION_AUDIT,
                        self::CHANNEL_COOPERATION_AUDIT,
                    ]))) {
                $companyIds  = array_column($jobList, 'company_id');
                $companyList = BaseCompany::find()
                    ->where([
                        'in',
                        'id',
                        $companyIds,
                    ])
                    ->select([
                        'id',
                        'full_name',
                    ])
                    ->asArray()
                    ->all();
                $companyList = array_column($companyList, null, 'id');
            }

            if ($contactBool) {
                // 职位联系人
                $jobContactList = BaseJobContact::find()
                    ->alias('jc')
                    ->select([
                        'jc.job_id',
                        'jc.company_member_info_id',
                        'jc_cmi.contact',
                    ])
                    ->where([
                        'and',
                        [
                            'in',
                            'jc.job_id',
                            $jobIds,
                        ],
                    ])
                    ->innerJoin(['jc_cmi' => BaseCompanyMemberInfo::tableName()],
                        'jc_cmi.id = jc.company_member_info_id')
                    ->asArray()
                    ->all();
                if ($jobContactList) {
                    $jobContactList = array_column($jobContactList, null, 'job_id');
                }
                // 子账号
                $jobContactSynergyList = BaseJobContactSynergy::find()
                    ->alias('jcs')
                    ->select([
                        'jcs.job_id',
                        'jcs_cmi.contact',
                        'jcs_cmi.department',
                        'jcs_m.email',
                        'jcs.company_member_info_id',
                    ])
                    ->where([
                        'and',
                        [
                            'in',
                            'jcs.job_id',
                            $jobIds,
                        ],
                    ])
                    ->leftJoin(['jcs_cmi' => BaseCompanyMemberInfo::tableName()],
                        'jcs_cmi.id = jcs.company_member_info_id')
                    ->leftJoin(['jcs_m' => BaseMember::tableName()], 'jcs_m.id = jcs_cmi.member_id')
                    ->asArray()
                    ->all();

                $jobContactSynergyListWithJobId = [];
                foreach ($jobContactSynergyList as $jobContactSynergy) {
                    $contactItem = [
                        'contact'    => $jobContactSynergy['contact'],
                        'email'      => $jobContactSynergy['email'],
                        'department' => $jobContactSynergy['department'],
                        'is_contact' => 0,
                    ];

                    if ($jobContactList[$jobContactSynergy['job_id']]['company_member_info_id'] == $jobContactSynergy['company_member_info_id']) {
                        $contactItem['is_contact'] = 1;
                    }
                    $jobContactSynergyListWithJobId[$jobContactSynergy['job_id']][] = $contactItem;
                }
            }
        }

        // 按钮组
        $btnKeys = [];
        switch ($this->channel) {
            case self::CHANNEL_UNCOOPERATION:
                $btnKeys = [
                    'edit',
                    'hide',
                    'show',
                    'republish',
                    'offline',
                    'refresh',
                    'delete',
                    'establishment',
                ];
                break;
            case self::CHANNEL_COOPERATION:
                $btnKeys = [
                    'refresh',
                    'show',
                    'hide',
                    'log',
                    'edit',
                    'republish',
                    'offline',
                    'addEducation',
                    'removeEducation',
                    'addAttachment',
                    'removeAttachment',
                    'contact',
                    'establishment',
                    'invite',
                    'delete',
                ];
                break;
            case self::CHANNEL_COOPERATION_AUDIT:
            case self::CHANNEL_UNCOOPERATION_AUDIT:
                break;
        }

        // 需要修改的日期字段
        $dateFields = [
            'addTime',
            'applyAuditTime',
            'offlineTime',
            'firstReleaseTime',
            'realRefreshTime',
            'publishTime',
        ];

        foreach ($jobList as $job) {
            if (!isset($job['announcement_status'])) {
                $job = $this->setJobAnnnouncement($job['announcement_id'], $job);
            }

            // 经验要求
            $experienceTypeTitle = $experienceList[intval($job['experience_type'])] ?? '-';
            // 学历要求
            $educationTypeTitle = $educationTypeList[$job['education_type']] ?? '-';
            $city               = Area::getAreaName($job['city_id']);
            //输出薪资
            $wage = BaseJob::formatWage($job['min_wage'], $job['max_wage'], $job['wage_type']);
            if ($contactBool) {
                // 联系人
                $contact = '';
                foreach ($jobContactList as $key => $jobContact) {
                    if ($jobContact['job_id'] == $job['id']) {
                        $contact = $jobContact['contact'];
                        unset($jobContactList[$key]);
                    }
                }
                $jobContactSynergy      = $jobContactSynergyListWithJobId[$job['id']] ?? [];
                $jobContactSynergyCount = count($jobContactSynergy);
            }

            $offlineTime = (($job['status'] == BaseJob::STATUS_OFFLINE) ? $job['offline_time'] : $job['period_date']);

            if (!$this->hasNeedJoinCompany()) {
                $job['full_name'] = $companyList[$job['company_id']]['full_name'] ?? '';
            }

            // 定义一个数组，存储职位信息
            $jobDetail = [
                // 职位ID/职位编号
                'jobId'                  => $job['id'],
                'jobUuid'                => $job['uuid'],
                // 职位名称
                'jobName'                => $job['name'],
                // 城市
                'city'                   => $city,
                // 学历要求
                'educationTypeTitle'     => $educationTypeTitle,
                // 经验要求
                'experienceType'         => $experienceTypeTitle,
                // 薪资
                'wage'                   => $wage,
                // 公告标题
                'announcementTitle'      => is_null($job['announcement_title']) ? '' : $job['announcement_title'],
                'announcementId'         => $job['announcement_id'],
                'announcementStatus'     => is_null($job['announcement_status']) ? '' : $job['announcement_status'],
                // 公司名称
                'companyName'            => $job['full_name'],
                'companyId'              => $job['company_id'],
                // 点击量
                'click'                  => $job['click'],
                // 投递数量
                'applyTotal'             => empty($job['apply_total']) ? 0 : $job['apply_total'],
                // 投递方式
                'deliveryWay'            => ($job['delivery_way'] ? BaseJob::DELIVERY_WAY_NAME[$job['delivery_way']] : BaseAnnouncement::DELIVERY_WAY_NAME[$job['announcement_delivery_way']]),
                // 职位状态
                'status'                 => BaseJob::JOB_STATUS_NAME[$job['status']] ?? '',
                // 审核状态
                'auditStatus'            => BaseJob::JOB_AUDIT_STATUS_NAME[$job['audit_status']],
                // 首次发布时间
                'firstReleaseTime'       => $job['first_release_time'],
                // 实际刷新时间
                'realRefreshTime'        => $job['real_refresh_time'],
                // 发布时间
                'publishTime'            => $job['refresh_time'],
                // 发布模式
                'publishMode'            => $job['is_article'] == BaseJob::IS_ARTICLE_YES ? '公告+职位' : '纯职位',
                // 创建人
                'creator'                => $job['create_type'] == BaseJob::CREATE_TYPE_SELF ? $job['full_name'] . '（' . $job['creator'] . '）' :$job['creator'],
                //审核人
                'auditAdminName'         => $job['audit_admin_name'],
                // 是否显示
                'isShow'                 => BaseJob::IS_SHOW_NAME[$job['is_show']],
                // 招聘人数
                'amount'                 => $job['amount'],
                // 部门
                'department'             => $job['department'],
                // 按钮组
                'btnList'                => $btnKeys ? $this->generateButtonList($job, $btnKeys) : [],
                // 学科专业
                'majorText'              => isset($job['major_id']) && $job['major_id'] ? BaseMajor::getAllMajorNameRedis($job['major_id']) : '',
                // 下线时间
                'offlineTime'            => $offlineTime,
                // 联系人
                'contact'                => $contact ?? '',
                // 子账号信息
                'jobContactSynergy'      => $jobContactSynergy ?? [],
                // 子账号数量
                'jobContactSynergyCount' => $jobContactSynergyCount ?? 0,
                // 是否是小程序
                'isMiniapp'              => $job['is_miniapp'],
                // 创建时间
                'addTime'                => $job['add_time'],
                // 申请审核时间
                'applyAuditTime'         => $job['apply_audit_time'],
                // 申请人
                'applyAdminName'         => $job['apply_id_type'] == BaseJob::APPLY_ID_TYPE_COMPANY ? $job['full_name'] . '（' . $job['apply_name'] . '）' :$job['apply_name'],
            ];

            // 计算日期逻辑
            foreach ($dateFields as $field) {
                $jobDetail[$field . 'Date'] = $this->formatTime($jobDetail[$field]);
                $jobDetail[$field]          = ($jobDetail[$field] == TimeHelper::ZERO_TIME) ? '-' : $jobDetail[$field];
            }

            $list[] = $jobDetail;
        }

        return $list;
    }

    /**
     * 批量生成操作按钮列表
     *
     * @param  $buttonKeys 所需的按钮组
     * @param  $jobData    职位数据
     * @return array 过滤后的按钮列表
     */
    public function generateButtonList(
        $jobData = [],
        $buttonKeys = []
    ) {
        // 学历限制
        $deliveryLimitType = explode(',', $jobData['delivery_limit_type']);
        if (in_array(BaseJob::DELIVERY_LIMIT_EDUCATION, $deliveryLimitType)) {
            $buttonKeys = array_diff($buttonKeys, ['addEducation']);
        } else {
            $buttonKeys = array_diff($buttonKeys, ['removeEducation']);
        }
        // 附件限制
        if (in_array(BaseJob::DELIVERY_LIMIT_MATERIAL, $deliveryLimitType)) {
            $buttonKeys = array_diff($buttonKeys, ['addAttachment']);
        } else {
            $buttonKeys = array_diff($buttonKeys, ['removeAttachment']);
        }

        $btnList = [];

        foreach ($buttonKeys as $buttonKey) {
            $button = (new OperateService())->run($buttonKey, $jobData['audit_status'], $jobData['status'],
                $jobData['is_article'], $jobData['is_show'], $jobData['announcement_status'],
                $jobData['announcement_audit_status'], $jobData['company_id']);

            // 修正：使用当前按钮变量名
            if ($button['disabled'] != 3) {
                $btnList[] = $button;
            }
        }

        return $btnList;
    }

    public function setJobAnnnouncement($id, $jobData)
    {
        if ($this->announcementInfo->id != $id) {
            $this->announcementInfo = BaseAnnouncement::find()
                ->where(['id' => $id])
                ->one();
        }

        return ArrayHelper::merge($jobData, [
            'announcement_delivery_way' => $this->announcementInfo->delivery_way,
            'announcement_title'        => $this->announcementInfo->title,
            'announcement_audit_status' => $this->announcementInfo->audit_status,
            'announcement_status'       => $this->announcementInfo->status,
        ]);
    }

    /**
     * 获取查询条件and和or
     * @param $params
     * @return array
     */
    private function buildBaseQueryWhere($params)
    {
        //        $query->where(
        //            [
        //                'and',
        //                ['=', 'j.name','1112'],
        //                ['or',
        //                 ['j.name' => 'wx'],
        //                 ['j.name' => '111111'],
        //                ],
        //                ['=', 'j.name', '1123444'],
        //            ])->limit(1)->all();

        //        var_dump($query->createCommand()->getRawSql());exit();

        // 无审核内容，只需要上线就行
        $andWhere = [
            'and',
        ];

        if (isset($params['isCooperation']) && $params['isCooperation'] != '') {
            $andWhere[] = [
                'in',
                'j.is_job_cooperation',
                StringHelper::changeStrToFilterArr($params['isCooperation']),
            ];
        }

        // 职位检索
        if (isset($params['jobName']) && $params['jobName']) {
            if ($params['jobName'] >= 10000000 && $params['jobName'] <= 99999999) {
                $andWhere[] = [
                    '=',
                    'j.uuid',
                    $params['jobName'],
                ];
            } else {
                $andWhere[] = [
                    'or',
                    [
                        'like',
                        'j.name',
                        $params['jobName'],
                    ],
                    [
                        'like',
                        'j.uuid',
                        $params['jobName'],
                    ],
                ];
            }
        }

        // 单位检索
        if (isset($params['companyName']) && $params['companyName'] != '') {
            if ($params['companyName'] >= 10000000 && $params['companyName'] <= 99999999) {
                $andWhere[] = [
                    '=',
                    'c.uuid',
                    $params['jobName'],
                ];
            } else {
                $andWhere[] = [
                    'or',
                    [
                        'like',
                        'c.uuid',
                        $params['companyName'],
                    ],
                    [
                        'like',
                        'c.full_name',
                        $params['companyName'],
                    ],
                ];
            }
        }

        // 职位类型
        if (isset($params['jobCategoryId']) && $params['jobCategoryId'] != '') {
            $andWhere[] = [
                'in',
                'jcr.category_id',
                StringHelper::changeStrToFilterArr($params['jobCategoryId']),
            ];
        }

        // 审核状态
        if (isset($params['auditStatus']) && $params['auditStatus'] != '') {
            $andWhere[] = [
                'in',
                'j.audit_status',
                StringHelper::changeStrToFilterArr($params['auditStatus']),
            ];
        }

        // 需求专业
        if (isset($params['majorId']) && $params['majorId'] != '') {
            $andWhere[] = [
                'in',
                'jmr.major_id',
                StringHelper::changeStrToFilterArr($params['majorId']),
            ];
        }

        // 招聘状态
        if (isset($params['status']) && $params['status'] != '') {
            $andWhere[] = [
                'in',
                'j.status',
                StringHelper::changeStrToFilterArr($params['status']),
            ];
        }

        // 学历要求
        if (isset($params['educationType']) && $params['educationType'] != '') {
            $andWhere[] = [
                'in',
                'j.education_type',
                StringHelper::changeStrToFilterArr($params['educationType']),
            ];
        }

        // 投递方式
        if (isset($params['deliveryWay']) && $params['deliveryWay'] != '') {
            $andWhere[] = [
                'or',
                [
                    'in',
                    'j.delivery_way',
                    StringHelper::changeStrToFilterArr($params['deliveryWay']),
                ],
                [
                    'and',
                    [
                        'in',
                        'ann.delivery_way',
                        StringHelper::changeStrToFilterArr($params['deliveryWay']),
                    ],
                    [
                        '=',
                        'j.delivery_way',
                        0,
                    ],
                ],
            ];
        }

        // 投递类型
        if (isset($params['deliveryType']) && $params['deliveryType'] != '') {
            $andWhere[] = [
                'or',
                [
                    'in',
                    'j.delivery_type',
                    StringHelper::changeStrToFilterArr($params['deliveryType']),
                ],
                [
                    'and',
                    [
                        'in',
                        'ann.delivery_type',
                        StringHelper::changeStrToFilterArr($params['deliveryType']),
                    ],
                    [
                        '=',
                        'j.delivery_type',
                        0,
                    ],
                ],
            ];
        }

        // 工作城市
        if (isset($params['city']) && $params['city'] != '') {
            $andWhere[] = [
                'in',
                'j.city_id',
                StringHelper::changeStrToFilterArr($params['city']),
            ];
        }

        // 用人部门
        if (isset($params['department']) && $params['department'] != '') {
            $andWhere[] = [
                'like',
                'j.department',
                $params['department'],
            ];
        }

        // 海外经历
        if (isset($params['abroadType']) && $params['abroadType'] != '') {
            $andWhere[] = [
                'in',
                'j.abroad_type',
                StringHelper::changeStrToFilterArr($params['abroadType']),
            ];
        }

        // 工作性质
        if (isset($params['natureType']) && $params['natureType'] != '') {
            $andWhere[] = [
                'in',
                'j.nature_type',
                StringHelper::changeStrToFilterArr($params['natureType']),
            ];
        }

        // 单位性质
        if (isset($params['companyNature']) && $params['companyNature'] != '') {
            $andWhere[] = [
                'in',
                'c.nature',
                StringHelper::changeStrToFilterArr($params['companyNature']),
            ];
        }

        // 单位类型
        if (isset($params['companyType']) && $params['companyType'] != '') {
            $andWhere[] = [
                'in',
                'c.type',
                StringHelper::changeStrToFilterArr($params['companyType']),
            ];
        }

        // 工作经验
        if (isset($params['experienceType']) && $params['experienceType'] != '') {
            $andWhere[] = [
                'in',
                'j.experience_type',
                StringHelper::changeStrToFilterArr($params['experienceType']),
            ];
        }

        // 职称类型
        if (isset($params['jobTitleType']) && $params['jobTitleType'] != '') {
            $andWhere[] = [
                'in',
                'j.title_type',
                StringHelper::changeStrToFilterArr($params['jobTitleType']),
            ];
        }

        // 政治面貌
        if (isset($params['politicsType']) && $params['politicsType'] != '') {
            $andWhere[] = [
                'in',
                'j.political_type',
                StringHelper::changeStrToFilterArr($params['politicsType']),
            ];
        }

        // 显示状态
        if (isset($params['isShow']) && $params['isShow'] != '') {
            $andWhere[] = [
                'in',
                'j.is_show',
                StringHelper::changeStrToFilterArr($params['isShow']),
            ];
        }

        // 发布人
        if (isset($params['creator']) && $params['creator'] != '') {
            $andWhere[] = [
                'like',
                'j.creator',
                $params['creator'],
            ];
        }

        // 申请审核时间
        if (!empty($params['applyAuditTimeStart']) && !empty($params['applyAuditTimeEnd'])) {
            $andWhere[] = [
                '>=',
                'j.apply_audit_time',
                TimeHelper::dayToBeginTime($params['applyAuditTimeStart']),
            ];
            $andWhere[] = [
                '<=',
                'j.apply_audit_time',
                TimeHelper::dayToEndTime($params['applyAuditTimeEnd']),
            ];
        }
        // 创建时间
        if (!empty($params['addTimeStart']) && !empty($params['addTimeEnd'])) {
            $andWhere[] = [
                '>=',
                'j.add_time',
                TimeHelper::dayToBeginTime($params['addTimeStart']),
            ];
            $andWhere[] = [
                '<=',
                'j.add_time',
                TimeHelper::dayToEndTime($params['addTimeEnd']),
            ];
        }

        // 联系人
        if (isset($params['contact']) && $params['contact'] != '') {
            $andWhere[] = [
                'or',
                [
                    '=',
                    'jc_m.id',
                    $params['contact'],
                ],
                [
                    '=',
                    'jc_m.email',
                    $params['contact'],
                ],
                [
                    '=',
                    'jc_m.mobile',
                    $params['contact'],
                ],
            ];
        }

        // 协同子账号
        if (isset($params['contactSynergy']) && $params['contactSynergy'] != '') {
            $andWhere[] = [
                'or',
                [
                    '=',
                    'jcs_m.id',
                    $params['contact'],
                ],
                [
                    '=',
                    'jcs_m.email',
                    $params['contact'],
                ],
                [
                    '=',
                    'jcs_m.mobile',
                    $params['contact'],
                ],
            ];
        }

        // 初始发布时间
        if (!empty($params['firstReleaseTimeStart']) && !empty($params['firstReleaseTimeEnd'])) {
            $andWhere[] = [
                '>=',
                'j.first_release_time',
                TimeHelper::dayToBeginTime($params['firstReleaseTimeStart']),
            ];
            $andWhere[] = [
                '<=',
                'j.first_release_time',
                TimeHelper::dayToEndTime($params['firstReleaseTimeEnd']),
            ];
        }

        // 刷新时间
        if (!empty($params['realRefreshTimeStart']) && !empty($params['realRefreshTimeEnd'])) {
            $andWhere[] = [
                '>=',
                'j.real_refresh_time',
                TimeHelper::dayToBeginTime($params['realRefreshTimeStart']),
            ];
            $andWhere[] = [
                '<=',
                'j.real_refresh_time',
                TimeHelper::dayToEndTime($params['realRefreshTimeEnd']),
            ];
        }

        // 是否是小程序
        if (isset($params['isMiniapp']) && $params['isMiniapp'] != '') {
            $andWhere[] = [
                'in',
                'j.is_miniapp',
                StringHelper::changeStrToFilterArr($params['isMiniapp']),
            ];
        }

        // 编制类型
        if (isset($params['establishmentType']) && $params['establishmentType'] != '') {
            // 这里会有几种可能性,-1和其他值(-1的情况下是没有选编制的类型,其他值是选了编制类型里面某个并且find_in_set
            $establishmentType = StringHelper::changeStrToFilterArr($params['establishmentType']);
            $orWhere           = [
                'or',
            ];
            foreach ($establishmentType as $itemKey => $establishmentItem) {
                if ($establishmentItem == -1) {
                    $orWhere[] = ['j.is_establishment' => BaseJob::IS_ESTABLISHMENT_NO];
                } else {
                    $establishmentItemKey = 'establishmentItem' . $itemKey;
                    $orWhere[]            = new Expression("FIND_IN_SET(:" . $establishmentItemKey . ", j.establishment_type)",
                        [$establishmentItemKey => $establishmentItem]);
                }
            }
            $andWhere[] = $orWhere;
        }

        // 发布时间
        if (!empty($params['refreshTimeStart']) && !empty($params['refreshTimeEnd'])) {
            $andWhere[] = [
                '>=',
                'j.refresh_time',
                TimeHelper::dayToBeginTime($params['refreshTimeStart']),
            ];
            $andWhere[] = [
                '<=',
                'j.refresh_time',
                TimeHelper::dayToEndTime($params['refreshTimeEnd']),
            ];
        }

        // 公告检索
        if (isset($params['announcementName']) && $params['announcementName'] != '') {
            if ($params['announcementName'] >= 10000000 && $params['announcementName'] <= 99999999) {
                $andWhere[] = [
                    '=',
                    'ann.uuid',
                    $params['announcementName'],
                ];
            } else {
                $andWhere[] = [
                    'or',
                    [
                        'like',
                        'ann.uuid',
                        $params['announcementName'],
                    ],
                    [
                        'like',
                        'ann.title',
                        $params['announcementName'],
                    ],
                ];
            }
        }

        // 发布模式
        if (isset($params['isArticle']) && $params['isArticle'] != '') {
            switch ($params['isArticle']) {
                case BaseJob::IS_ARTICLE_YES:
                    $andWhere[] = [
                        '=',
                        'j.is_article',
                        BaseJob::IS_ARTICLE_YES,
                    ];
                    break;
                default:
                    $andWhere[] = [
                        '=',
                        'j.is_article',
                        BaseJob::IS_ARTICLE_NO,
                    ];
                    break;
            }
        }

        // 年龄范围
        if (isset($params['ageType']) && $params['ageType'] != '') {
            $andWhere[] = [
                'like',
                'j.age_type',
                $params['ageType'],
            ];
        }

        // 审核人
        if (isset($params['auditAdminName']) && $params['auditAdminName'] != '') {
            $andWhere[] = [
                'like',
                'j.audit_admin_name',
                $params['auditAdminName'],
            ];
        }

        // 联系人
        if (isset($params['applyName']) && $params['applyName'] != '') {
            $andWhere[] = [
                'like',
                'j.apply_name',
                $params['applyName'],
            ];
        }

        if (count($andWhere) < 1) {
            throw new MessageException('查询条件不能为空');
        }

        return $andWhere;
    }

    public function exportCooperation($params)
    {
        $jobList = $this->buildBaseQuery($params)
            ->select($this->select)
            ->andWhere(self::COOPERATION_WHERE)
            ->asArray()
            ->all();
        $jobList = $this->formatIndex($jobList);
        $list    = [];
        foreach ($jobList as $job) {
            // 字账号列表
            $jobContactSynergy = [];
            foreach ($job['jobContactSynergy'] as $contactInfo) {
                $jobContactSynergy[] = implode('/', [
                    $contactInfo['account'],
                    $contactInfo['department'] . $contactInfo['email'],
                ]);
            }
            $jobContactSynergy = implode(PHP_EOL, $jobContactSynergy);

            $list[] = [
                //职位编号
                'jobUuid'           => $job['jobUuid'],
                // 职位名称
                'jobName'           => $job['jobName'],
                // 基本信息
                'baseInfo'          => implode('|', [
                    $job['city'],
                    $job['educationTypeTitle'],
                    $job['experienceType'],
                    $job['wage'],
                ]),
                // 关联公告
                'announcementTitle' => $job['announcementTitle'],
                // 所属单位
                'companyName'       => $job['companyName'],
                // 招聘状态
                'status'            => $job['status'],
                // 审核状态
                'auditStatus'       => $job['auditStatus'],
                // 发布模式
                'publishMode'       => $job['publishMode'],
                // 投递数量
                'applyTotal'        => $job['applyTotal'],
                // 点击量
                'click'             => $job['click'],
                // 创建人
                'creator'           => $job['creator'],
                // 创建时间
                'addTime'           => $job['addTime'],
                // 初始发布时间
                'firstReleaseTime'  => $job['firstReleaseTime'],
                // 刷新时间
                'realRefreshTime'   => $job['realRefreshTime'],
                // 下线时间
                'offlineTime'       => $job['offlineTime'],
                // 发布时间
                'publishTime'       => $job['publishTime'],
                // 显示状态
                'isShow'            => $job['isShow'],
                // 招聘人数
                'amount'            => $job['amount'],
                // 用人部门
                'department'        => $job['department'],
                // 学科专业
                'majorText'         => $job['majorText'],
                // 联系人
                'contact'           => $job['contact'],
                // 子账号信息
                'jobContactSynergy' => $jobContactSynergy,
                // 是否小程序
                'isMiniapp'         => BaseJob::IS_MINIAPP_LIST[$job['isMiniapp']] ?? '',
                //审核人
                'auditAdminName'    => $job['auditAdminName'],
                // 投递方式
                'deliveryWay'       => $job['deliveryWay'],
            ];
        }

        $chineseAnnotations = [
            '职位编号',
            '职位名称',
            '基本信息',
            '关联公告',
            '所属单位',
            '招聘状态',
            '审核状态',
            '发布模式',
            '投递数量',
            '点击量',
            '创建人',
            '创建时间',
            '初始发布时间',
            '刷新时间',
            '下线时间',
            '发布时间',
            '显示状态',
            '招聘人数',
            '用人部门',
            '学科专业',
            '联系人',
            '子账号信息',
            '是否小程序',
            '审核人',
            '投递方式',
        ];

        $fileName = BaseAdminDownloadTask::TYPE_JOB_LIST_NAME . '_' . date('YmdHis');

        return [
            'data'     => $list,
            'headers'  => $chineseAnnotations,
            'fileName' => $fileName,
        ];
    }

}