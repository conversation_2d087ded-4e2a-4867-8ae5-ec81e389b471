<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseJobLog;
use common\helpers\IpHelper;
use queue\Producer;
use Yii;
use yii\base\Exception;

/**
 * 职位修改编制
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class EstablishmentService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 职位修改编制、批量修改编制
     * @throws Exception
     */
    public function run()
    {
        $this->params = Yii::$app->request->post();
        if (!$this->params['jobId']) {
            throw new Exception('职位id不能为空');
        }
        if ($this->params['type'] == 1 && empty($this->params['set'])) {
            throw new Exception('请选择编制');
        }
        $this->initInfo();
        //循环职位id修改编制
        $ids = explode(',', $this->params['jobId']);
        if (count($ids) > 0 && $this->isBatch) {
            //批量
            foreach ($ids as $idItem) {
                $this->establishmentOne($idItem);
            }
        } else {
            //单个
            $this->establishmentOne($this->params['jobId']);
        }

        return true;
    }

    /**
     * 单条操作
     * @param $id
     * @throws Exception
     */
    private function establishmentOne($id)
    {
        $jobInfo = BaseJob::findOne($id);
        if (!$jobInfo) {
            throw new Exception('职位不存在');
        }
        $this->jobId                 = $id;
        $this->oldJobInfo            = $jobInfo;
        $oldJobInfoEstablishmentType = $jobInfo->establishment_type;
        $this->setCompany($jobInfo->company_id);
        $jobInfo->is_establishment = $this->params['type'];
        if ($this->params['type'] == 1) {
            $jobInfo->establishment_type = $this->params['set'];
        } else {
            $jobInfo->establishment_type = '';
        }
        if (!$jobInfo->save()) {
            throw new Exception('编制修改失败' . $jobInfo->getFirstErrorsMessage());
        }
        $this->jobInfo = $jobInfo;
        BaseJobHandleLog::createInfo([
            'add_time'        => date('Y-m-d H:i:s'),
            'job_id'          => $id,
            'handle_type'     => BaseJobHandleLog::HANDLE_TYPE_ESTABLISHMENT,
            'handler_type'    => $this->params['platformType'],
            'handler_id'      => $this->params['userId'],
            'handler_name'    => $this->params['username'],
            'handle_before'   => json_encode([
                '编制修改' => $oldJobInfoEstablishmentType ? BaseDictionary::getAllEstablishmentName(explode(',',
                    $oldJobInfoEstablishmentType)) : '',
            ]),
            'handle_after'    => json_encode([
                '编制修改' => $jobInfo->establishment_type ? BaseDictionary::getAllEstablishmentName(explode(',',
                    $jobInfo->establishment_type)) : '',
            ]),
            'ip'              => IpHelper::getIpInt(),
            'announcement_id' => $this->oldJobInfo->announcement_id ?: 0,
        ]);
        //写一下日志
        $this->log($this->isBatch ? BaseJobLog::TYPE_ESTABLISHMENT_BATCH : BaseJobLog::TYPE_ESTABLISHMENT);

        // 后置处理
        $this->after();

        return true;
    }

    /**
     * 后置处理
     */
    private function after()
    {
        //刷新公告编制字段
        if ($this->jobInfo->announcement_id) {
            $this->updateAnnouncementEstablishment();
            $this->runAutoColumnAfter();
        }
    }

}