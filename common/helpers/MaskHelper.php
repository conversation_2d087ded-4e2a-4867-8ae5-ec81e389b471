<?php
namespace common\helpers;

use common\base\models\BaseResume;

/**
 * 脱敏帮助类
 */
class MaskHelper
{

    const GENDER_MAN   = BaseResume::GENDER_MAN;
    const GENDER_WOMAN = BaseResume::GENDER_WOMAN;

    /**
     * 获取脱敏名字
     * 姓名：
     * 中文名：2个字脱敏最后一个字；3个字脱敏中间两个字；大于3个字，从第3个字开始全脱敏
     * 英文名：最后四个字符脱敏，字符不足四位的，除第一个字符外，其他字符脱敏；只有一个字符的，不脱敏；
     * @param $name
     * @param $gender
     */
    public static function getName($name, $gender = 0)
    {
        // 截取第一个字符
        // $first = mb_substr($name, 0, 1);
        // if (!$first) {
        //     return '';
        // }
        //
        // $genderTxt = '先生/女士';
        // switch ($gender) {
        //     case self::GENDER_MAN:
        //         $genderTxt = '先生';
        //         break;
        //     case self::GENDER_WOMAN:
        //         $genderTxt = '女士';
        //         break;
        // }
        //
        // // 判断中文?
        // if (ValidateHelper::isChinese($first)) {
        //     return $first . $genderTxt;
        // }
        //
        // // 英文
        // return $first . '*' . $genderTxt;

        if (!$name) {
            return '';
        }

        if (ValidateHelper::isChinese($name)) {
            // 中文
            $length = mb_strlen($name);
            if ($length == 2) {
                return mb_substr($name, 0, 1) . '*';
            } else {
                if ($length == 3) {
                    return mb_substr($name, 0, 1) . '**';
                } else {
                    return mb_substr($name, 0, 2) . str_repeat('*', $length - 2);
                }
            }
        }

        // 英文
        $length = mb_strlen($name);
        if ($length == 1) {
            return $name;
        } else {
            if ($length <= 4) {
                return mb_substr($name, 0, 1) . str_repeat('*', $length - 1);
            }

            // 最后四个字符脱敏
            return mb_substr($name, 0, $length - 4) . str_repeat('*', 4);
        }
    }

    /**
     * 获取脱敏手机号
     * 手机号：从第四个数字开始全部脱敏
     * @param $mobile
     */
    public static function getPhone($phone)
    {
        if (empty($phone)) {
            return '';
        }
        // $phone = substr_replace($phone, '****', 3, 4);

        $length = mb_strlen($phone);
        if ($length <= 4) {
            return $phone;
        }

        return mb_substr($phone, 0, 4) . str_repeat('*', $length - 4);
    }

    // 最后四位脱敏
    public static function getPhoneLast($phone)
    {
        if (empty($phone)) {
            return '';
        }
        $length = mb_strlen($phone);
        if ($length <= 4) {
            return $phone;
        }

        return mb_substr($phone, 0, $length - 4) . str_repeat('*', 4);
    }

    /**
     * 获取加上区号的完整脱敏手机号
     * @param $mobileCode
     * @param $mobile
     * @return string
     */
    public static function getFullPhone($mobileCode, $mobile)
    {
        if (empty($mobile)) {
            return '';
        }
        $length = mb_strlen($mobile);

        if ($mobileCode == '86' || $mobileCode == '+86' || $mobileCode == '') {
            return mb_substr($mobile, 0, 4) . str_repeat('*', $length - 4);
        } else {
            return '+' . $mobileCode . ' ' . mb_substr($mobile, 0, 1) . str_repeat('*',
                    $length - 2) . mb_substr($mobile, -1, 1);
        }
    }

    /**
     * 邮箱脱敏：最后四个字符脱敏，字符不足四位的，除第一个字符外，其他字符脱敏；只有一个字符的，不脱敏；
     * @param $email
     * @return array|string|string[]
     */
    public static function getEmail($email)
    {
        if (empty($email)) {
            return '';
        }
        $email = explode('@', $email);
        if (count($email) == 1) {
            return $email[0];
        }
        // 后四个字符脱敏
        $length = mb_strlen($email[0]);
        if ($length < 4) {
            // 除了第一个字符,其余都脱敏
            $email[0] = mb_substr($email[0], 0, 1) . str_repeat('*', $length - 1);
        } else {
            $email[0] = substr_replace($email[0], '****', -4);
        }
        // $email[1] = str_replace('.', '*', $email[1]);
        $email = implode('@', $email);

        return $email;
    }

    /**
     * @param $url
     * 图片马赛克
     */
    public static function getImage($url)
    {
        $params = 'imageMogr2/thumbnail/100x120/auto-orient/interlace/20/blur/10x10/quality/100/';

        return $url . '?' . $params;
    }

    public static function getIdCard($string)
    {
        if (empty($string)) {
            return '';
        }
        $string = substr_replace($string, '********', 6, 8);

        return $string;
    }

    /**
     * 获取脱敏名字
     * 姓名首字符+两颗星星
     */
    public static function getFistName($name): string
    {
        // 截取第一个字符
        $first = mb_substr($name, 0, 1);
        if (strlen($first) < 1) {
            return '';
        }

        //加两颗星星
        $starTxt = "**";

        return $first . $starTxt;
    }
}
