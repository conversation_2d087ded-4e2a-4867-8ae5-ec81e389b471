<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "company_package_change_detail_log".
 *
 * @property int $id
 * @property int $type 1：人才投递邀约；2:人才直聊通知；3:人才直聊框提示通知
 * @property int $resume_id 求职者id
 * @property int $member_id 操作人id
 * @property int $company_id 单位id
 * @property int $job_id 职位id
 * @property int $resource_id type为1，2，3时是短信日志id
 * @property int $change_log_id company_package_change_log表id
 * @property string $add_time 创建时间
 */
class CompanyPackageChangeDetailLog extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'company_package_change_detail_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['type', 'resume_id', 'member_id', 'company_id', 'job_id', 'resource_id', 'change_log_id'], 'integer'],
            [['add_time'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'type' => 'Type',
            'resume_id' => 'Resume ID',
            'member_id' => 'Member ID',
            'company_id' => 'Company ID',
            'job_id' => 'Job ID',
            'resource_id' => 'Resource ID',
            'change_log_id' => 'Change Log ID',
            'add_time' => 'Add Time',
        ];
    }
}
