<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job_apply_record".
 *
 * @property int $id 主键id
 * @property int $delivery_type 投递类型 1站外投递 2站内投递
 * @property int $delivery_way 投递方式 1平台投递 2邮箱投递 3网址投递 99自主录入投递
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $apply_id 投递ID 关联回站内投递表
 * @property int $apply_site_id 投递ID 关联回站外投递表
 * @property int $company_id 发布职位的单位id
 * @property int $resume_id 简历id
 * @property int $announcement_id 公告ID
 * @property int $job_id 职位id
 * @property int $source 投递来源(1自主投递2委托投递3自主录入)
 * @property int $platform 投递平台 1=pc 2=h5 3=mini
 * @property string $match_complete 匹配度
 */
class JobApplyRecord extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_apply_record';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['delivery_type', 'delivery_way', 'apply_id', 'apply_site_id', 'company_id', 'resume_id', 'announcement_id', 'job_id', 'source', 'platform'], 'integer'],
            [['add_time'], 'required'],
            [['add_time', 'update_time'], 'safe'],
            [['match_complete'], 'number'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'delivery_type' => 'Delivery Type',
            'delivery_way' => 'Delivery Way',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'apply_id' => 'Apply ID',
            'apply_site_id' => 'Apply Site ID',
            'company_id' => 'Company ID',
            'resume_id' => 'Resume ID',
            'announcement_id' => 'Announcement ID',
            'job_id' => 'Job ID',
            'source' => 'Source',
            'platform' => 'Platform',
            'match_complete' => 'Match Complete',
        ];
    }
}
