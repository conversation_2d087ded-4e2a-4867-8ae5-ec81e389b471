<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "off_site_job_apply".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $status 状态
 * @property string $title 公告标题
 * @property string $link 公告或职位链接
 * @property string $job_name 投递职位
 * @property int $apply_status 投递状态1:已投递,2:通过初筛,3:邀请面试,4:已面试,5:不合适,6:待应聘
 * @property string $apply_date 投递时间
 * @property string $salary 薪酬待遇
 * @property string $email 投递邮箱
 * @property string $content 描述备注
 * @property int $resume_id 简历id
 * @property int $member_id 会员id
 * @property int $source 投递来源（1本人录入  2网站投递  3报名应聘）
 * @property int $job_id 职位id
 * @property int $announcement_id 公告id
 * @property int $resume_attachment_id 附件简历id
 * @property string $stuff_file_id 材料id（id用逗号分割）
 * @property int $email_log_id 邮件发送记录id
 * @property string $company_tag 企业标识的tag(逗号隔开的文案)
 */
class OffSiteJobApply extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'off_site_job_apply';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time', 'apply_date'], 'safe'],
            [['status', 'apply_status', 'resume_id', 'member_id', 'source', 'job_id', 'announcement_id', 'resume_attachment_id', 'email_log_id'], 'integer'],
            [['title', 'email', 'stuff_file_id'], 'string', 'max' => 256],
            [['link', 'company_tag'], 'string', 'max' => 512],
            [['job_name'], 'string', 'max' => 128],
            [['salary'], 'string', 'max' => 64],
            [['content'], 'string', 'max' => 1024],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'title' => 'Title',
            'link' => 'Link',
            'job_name' => 'Job Name',
            'apply_status' => 'Apply Status',
            'apply_date' => 'Apply Date',
            'salary' => 'Salary',
            'email' => 'Email',
            'content' => 'Content',
            'resume_id' => 'Resume ID',
            'member_id' => 'Member ID',
            'source' => 'Source',
            'job_id' => 'Job ID',
            'announcement_id' => 'Announcement ID',
            'resume_attachment_id' => 'Resume Attachment ID',
            'stuff_file_id' => 'Stuff File ID',
            'email_log_id' => 'Email Log ID',
            'company_tag' => 'Company Tag',
        ];
    }
}
