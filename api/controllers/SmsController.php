<?php

namespace api\controllers;

use common\base\models\BaseSmsLog;
use common\helpers\DebugHelper;
use common\libs\SmsQueue;
use common\libs\WxWork;

class SmsController extends BaseApiController
{
    public function actionTxCallback()
    {
        /**
         * 一次回调请求里可能有多次的短信请求结果，以 json 数组的形式。
         * [
         * {
         * "user_receive_time": "2015-10-17 08:03:04",
         * "nationcode": "86",
         * "mobile": "13xxxxxxxxx",
         * "report_status": "SUCCESS",
         * "errmsg": "DELIVRD",
         * "description": "用户短信送达成功",
         * "sid": "xxxxxxx"
         * }
         * ]
         */

        // 暂时关闭短信失败提醒
        if (\Yii::$app->request->isPost) {
            $json = file_get_contents("php://input");
            $data = json_decode($json, true);

            DebugHelper::sms($data);

            // report_status是FAIL的时候，给机器人发一个信息
            foreach ($data as $item) {
                // array (
                //     0 =>
                //         array (
                //             'mobile' => '15902090572',
                //             'report_status' => 'SUCCESS',
                //             'description' => '用户短信送达成功',
                //             'errmsg' => 'DELIVRD',
                //             'user_receive_time' => '2025-06-11 17:09:50',
                //             'sid' => '99:167095503317496329873189057',
                //             'nationcode' => '86',
                //         ),
                // )

                $smsModel = BaseSmsLog::findOne(['sid' => $item['sid']]);

                if (!$smsModel) {
                    // 如果没有找到对应的短信记录，可能是因为sid不对，或者已经被删除了
                    continue;
                }

                $smsModel->receive_data = json_encode($item);

                if ($item['report_status'] === 'FAIL') {
                    $mobile      = $item['mobile'];
                    $errmsg      = $item['errmsg'];
                    $description = $item['description'];
                    $typeText    = SmsQueue::TYPE_LIST[$smsModel->type];
                    $content     = "腾讯云短信送达失败，手机号：{$mobile}，发送类型：{$typeText}，错误信息：{$errmsg}，描述：{$description}";
                    WxWork::getInstance()
                        ->robotMessageToSms($content);

                    $smsModel->receive_status = BaseSmsLog::RECEIVE_STATUS_FAIL;
                }

                if ($item['report_status'] === 'SUCCESS') {
                    $smsModel->receive_status = BaseSmsLog::RECEIVE_STATUS_SUCCESS;
                }

                $smsModel->save();
            }
        }
    }

    public function actionAlCallback()
    {
        $json = file_get_contents("php://input");
        $data = json_decode($json, true);

        /**
         * array (
         * 0 =>
         * array (
         * 'send_time' => '2017-08-30 00:00:00',
         * 'report_time' => '2017-08-30 00:00:00',
         * 'success' => true,
         * 'err_msg' => '用户接收成功',
         * 'err_code' => 'DELIVERED',
         * 'phone_number' => '18612345678',
         * 'sms_size' => '1',
         * 'biz_id' => '932702304080415357^0',
         * 'out_id' => '1184585343',
         * ),
         * )
         */

        foreach ($data as $item) {
            $smsModel = BaseSmsLog::findOne(['sid' => $item['biz_id']]);

            if (!$smsModel) {
                // 如果没有找到对应的短信记录，可能是因为sid不对，或者已经被删除了
                continue;
            }

            $smsModel->receive_data = json_encode($item);

            if ($item['success']) {
                $smsModel->receive_status = BaseSmsLog::RECEIVE_STATUS_SUCCESS;
            } else {
                $smsModel->receive_status = BaseSmsLog::RECEIVE_STATUS_FAIL;
                $mobile                   = $item['phone_number'];
                $errMsg                   = $item['err_msg'];
                $errCode                  = $item['err_code'];
                $typeText                 = SmsQueue::TYPE_LIST[$smsModel->type];
                $content                  = "阿里云短信送达失败，手机号：{$mobile}，发送类型：{$typeText}，错误信息：{$errMsg}，错误代码：{$errCode}";
                WxWork::getInstance()
                    ->robotMessageToSms($content);
            }

            $smsModel->save();
        }

        // {
        //       "code": 0,
        //       "msg": "成功"
        //     }

        echo json_encode([
            'code' => 0,
            'msg'  => '成功',
        ], JSON_THROW_ON_ERROR);
    }
}