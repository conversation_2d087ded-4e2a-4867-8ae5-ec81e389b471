<?php

namespace console\modules\chat\services;

use common\base\models\BaseChatHistoryJob;
use common\base\models\BaseChatMessage;
use common\base\models\BaseChatMessageCard;
use common\base\models\BaseChatMessageFile;
use common\base\models\BaseChatRoom;
use common\base\models\BaseChatRoomSession;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseFile;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseMember;
use common\base\models\BaseMemberMessage;
use common\base\models\BaseResume;
use common\helpers\DebugHelper;
use common\libs\Aliyun\Green;
use common\libs\JwtAuth;
use common\models\ChatRoom;
use common\service\chat\ChatApplication;
use common\service\messageCenter\MessageCenterApplication;
use GatewayWorker\Lib\Gateway;
use Workerman\Timer;
use yii\web\NotFoundHttpException;

/**
 * Class Events
 * @package console\modules\chat\services
 * <AUTHOR> <<EMAIL>>
 */
class Events
{

    static $memberId;
    static $toMemberId;
    // 消息的客户端唯一标识
    static $cuid;
    static $messageId;

    static $chatRoomId;
    static $chatUUId;
    static $actionType;
    static $clientId;
    static $fromUserType;
    static $toUserType;

    /**
     * @var BaseResume
     */
    static $resumeModel;

    /**
     * @var BaseCompanyMemberInfo
     */
    static $companyMemberModel;
    /**
     * @var BaseChatRoom
     */
    static $chatRoomModel;

    static $originalMessage;
    static $content;
    static $saveContent;

    static $showTime;

    static $errorCode = 0;

    static $isChangeJob = false;

    const FROM_TYPE_RESUME  = BaseMember::TYPE_PERSON;
    const FROM_TYPE_COMPANY = BaseMember::TYPE_COMPANY;
    const FROM_TYPE_ADMIN   = 3;

    const ACTION_TYPE_LOGIN               = 'login';
    const ACTION_TYPE_TEXT                = 'text';
    const ACTION_TYPE_JOB_CARD            = 'jobCard';
    const ACTION_TYPE_JOB_APPLY           = 'jobApply';
    const ACTION_TYPE_RESUME_CARD         = 'resumeCard';
    const ACTION_TYPE_REQUEST_FILE        = 'requestFile';
    const ACTION_TYPE_AGREE_REQUEST_FILE  = 'agreeRequestFile';
    const ACTION_TYPE_IGNORE_REQUEST_FILE = 'ignoreRequestFile';
    //    const ACTION_TYPE_AGREE_REQUEST_FILE_CARD     = 'agreeRequestFileCard';
    const ACTION_TYPE_FILE                        = 'file';
    const ACTION_TYPE_IS_READ                     = 'isRead';
    const ACTION_TYPE_RESUME_COMPLETE_CARD        = 'resumeCompleteCard';
    const ACTION_TYPE_INVITE_JOB_REQUEST_CARD     = 'inviteJobRequestCard';
    const ACTION_TYPE_INVITE_JOB_REQUEST_IGNORE   = 'inviteJobRequestIgnore';
    const ACTION_TYPE_INVITE_JOB_REQUEST_INTEREST = 'inviteJobRequestInterest';
    const ACTION_TYPE_SYSTEM                      = 'system';
    const ACTION_TYPE_PONG                        = 'pong';
    const ACTION_TYPE_CHANGE_JOB                  = 'changeJob';
    const ACTION_TYPE_UNREAD                      = 'unread';
    const ACTION_TYPE_CLEAR_ALL                   = 'clearAll';

    const ACTION_TYPE_LIST = [
        self::ACTION_TYPE_LOGIN                       => [
            'allow' => [
                self::FROM_TYPE_RESUME,
                self::FROM_TYPE_COMPANY,
            ],
        ],
        // 普通文本
        self::ACTION_TYPE_TEXT                        => [
            'allow'       => [
                self::FROM_TYPE_RESUME,
                self::FROM_TYPE_COMPANY,
            ],
            'messageType' => BaseChatMessage::TYPE_TEXT,
        ],
        // https://lanhuapp.com/web/#/item/project/product?tid=8d951de4-aefb-40f7-954e-366683d68331&pid=7703bb80-e4a9-4f33-98ad-da2157414a79&versionId=cf14da0b-f812-45e8-8464-a74c14bba006&docId=3906055e-157b-4b6c-979d-7c2881ee385e&docType=axure&pageId=75f8a37cdfbe41bf852987528f327794&image_id=3906055e-157b-4b6c-979d-7c2881ee385e&parentId=ac43a382763f4309921d48566c3f0944
        // 职位卡片(这个卡片有可能是求职者发起,也有可能是单位发起,甚至有可能是切换导致的)
        self::ACTION_TYPE_JOB_CARD                    => [
            'allow'       => [
                self::FROM_TYPE_RESUME,
                self::FROM_TYPE_COMPANY,
            ],
            'messageType' => BaseChatMessage::TYPE_JOB_CARD,
            'cardType'    => BaseChatMessageCard::CARD_TYPE_JOB,
        ],
        //切换职位卡片
        self::ACTION_TYPE_CHANGE_JOB                  => [
            'allow'       => [
                self::FROM_TYPE_RESUME,
                self::FROM_TYPE_COMPANY,
            ],
            'messageType' => BaseChatMessage::TYPE_JOB_CARD,
            'cardType'    => BaseChatMessageCard::CARD_TYPE_JOB,
        ],
        // 这个是职位投递成功后由求职者向单位端发送的一个系统消息
        self::ACTION_TYPE_JOB_APPLY                   => [
            'allow'       => [
                self::FROM_TYPE_RESUME,
            ],
            'messageType' => BaseChatMessage::TYPE_JOB_APPLY,
        ],
        // 这个是职位投递成功后由求职者向单位端发送的一个简历卡片
        self::ACTION_TYPE_RESUME_CARD                 => [
            'allow'       => [
                self::FROM_TYPE_RESUME,
            ],
            'systemType'  => BaseChatMessage::TYPE_JOB_APPLY,
            'messageType' => BaseChatMessage::TYPE_RESUME_CARD,
            'cardType'    => BaseChatMessageCard::CARD_TYPE_RESUME,
        ],
        // 求职者向单位请求发送附件申请
        self::ACTION_TYPE_REQUEST_FILE                => [
            'allow'       => [
                self::FROM_TYPE_RESUME,
            ],
            'systemType'  => BaseChatMessage::TYPE_REQUEST_FILE,
            'messageType' => BaseChatMessage::TYPE_REQUEST_FILE,
        ],
        // 单位端同意求职者的附件申请(这个是单位端主动或者被动发起的)
        self::ACTION_TYPE_AGREE_REQUEST_FILE          => [
            'allow'       => [
                self::FROM_TYPE_COMPANY,
            ],
            'systemType'  => BaseChatMessage::TYPE_AGREE_REQUEST_FILE,
            'messageType' => BaseChatMessage::TYPE_AGREE_REQUEST_FILE_CARD,
        ],
        self::ACTION_TYPE_IGNORE_REQUEST_FILE         => [
            'allow'       => [
                self::FROM_TYPE_COMPANY,
            ],
            'messageType' => BaseChatMessage::TYPE_AGREE_REQUEST_FILE_CARD,
        ],
        // 单位端同意求职者的附件申请卡片(这个是单位端主动或者被动发起的)
        //        self::ACTION_TYPE_AGREE_REQUEST_FILE_CARD     => [
        //            'allow'       => [
        //                self::FROM_TYPE_COMPANY,
        //            ],
        //            'messageType' => BaseChatMessage::TYPE_AGREE_REQUEST_FILE_CARD,
        //            'cardType'    => BaseChatMessageCard::CARD_TYPE_ATTACHMENT,
        //        ],
        // 求职者向单位端发送文件
        self::ACTION_TYPE_FILE                        => [
            'allow'       => [
                self::FROM_TYPE_RESUME,
            ],
            'messageType' => BaseChatMessage::TYPE_FILE,
        ],
        // 告诉对方全部已读
        self::ACTION_TYPE_IS_READ                     => [
            'allow' => [
                self::FROM_TYPE_RESUME,
                self::FROM_TYPE_COMPANY,
            ],
        ],
        // 单位端向求职者发送简历完善卡片
        self::ACTION_TYPE_RESUME_COMPLETE_CARD        => [
            'allow'       => [
                self::FROM_TYPE_COMPANY,
            ],
            'messageType' => BaseChatMessage::TYPE_RESUME_COMPLETE_CARD,
            'cardType'    => BaseChatMessageCard::CARD_TYPE_RESUME_COMPLETE,

        ],
        // 单位端向求职者发送职位邀约完善卡片
        self::ACTION_TYPE_INVITE_JOB_REQUEST_CARD     => [
            'allow'       => [
                self::FROM_TYPE_COMPANY,
            ],
            'messageType' => BaseChatMessage::TYPE_INVITE_JOB_REQUEST_CARD,
            'cardType'    => BaseChatMessageCard::CARD_TYPE_APPLY_INVITE,

        ],
        // 求职者忽略职位邀约
        self::ACTION_TYPE_INVITE_JOB_REQUEST_IGNORE   => [
            'messageType' => BaseChatMessage::TYPE_INVITE_JOB_REQUEST_CARD_IGNORE,
            'allow'       => [
                self::FROM_TYPE_RESUME,
            ],
        ],
        // 求职者感兴趣职位邀约
        self::ACTION_TYPE_INVITE_JOB_REQUEST_INTEREST => [
            'messageType' => BaseChatMessage::TYPE_INVITE_JOB_REQUEST_CARD_INTEREST,
            'allow'       => [
                self::FROM_TYPE_RESUME,
            ],
        ],
        // 系统消息
        self::ACTION_TYPE_SYSTEM                      => [
            'allow'       => [
                self::FROM_TYPE_RESUME,
                self::FROM_TYPE_COMPANY,
            ],
            'messageType' => BaseChatMessage::TYPE_SYSTEM,
        ],
        // 系统消息
        self::ACTION_TYPE_PONG                        => [
            'allow' => [
                self::FROM_TYPE_RESUME,
                self::FROM_TYPE_COMPANY,
            ],
        ],
        // 未读消息
        self::ACTION_TYPE_UNREAD                      => [
            'allow' => [
                self::FROM_TYPE_RESUME,
                self::FROM_TYPE_COMPANY,
            ],
        ],
        // 未读消息
        self::ACTION_TYPE_CLEAR_ALL                   => [
            'allow' => [
                self::FROM_TYPE_RESUME,
                self::FROM_TYPE_COMPANY,
            ],
        ],

    ];

    public static function onWorkerStart($businessWorker)
    {
        // 定时器,30秒执行一次
        Timer::add(30, function () {
            $sql = 'select 1';
            \Yii::$app->db->createCommand($sql)
                ->execute();
        });
    }

    public static function onWebSocketConnect($clientId)
    {
        // 30秒内没有消息发过来,就断开连接
        // 设置一个30秒后的定时器

        $_SESSION['authTimerId'] = Timer::add(60, function ($clientId) {
            // 执行定时器
            Gateway::closeClient($clientId);
        });
    }

    // 握手验证
    public static function onMessage($clientId, $message)
    {
        $messageArr = json_decode($message, true);
        try {
            if (!$messageArr) {
                throw new NotFoundHttpException('非法消息');
            }

            $type = $messageArr['type'];
            if (!isset(self::ACTION_TYPE_LIST[$type])) {
                throw new NotFoundHttpException('非法类型 ' . $type);
            }
            self::$actionType      = $type;
            self::$clientId        = $clientId;
            self::$originalMessage = $messageArr;
            $memberId              = $_SESSION['memberId'];

            switch ($type) {
                case 'pong':
                    return true;
                case 'login':
                    static::login();
                    break;
                case 'unread':
                    static::sendUnreadAmount($memberId);
                    break;
                case 'clearAll':
                    static::clearAll($memberId);
                    break;
                default:
                    static::message();
                    break;
            }
            // 事务提交
        } catch (\Exception $e) {
            self::errorToClient($clientId, $e->getMessage());
        }
    }

    public static function onClose($clientId)
    {
        // 关闭定时器
        // Timer::del($_SESSION['authTimerId']);
        // Timer::del($_SESSION['unread_timer_id']);
        // echo "client:{$_SERVER['REMOTE_ADDR']}:{$_SERVER['REMOTE_PORT']} gateway:{$_SERVER['GATEWAY_ADDR']}:{$_SERVER['GATEWAY_PORT']}  client_id:$clientId onClose:''\n";
        //
        // if (isset($_SESSION['room_id'])) {
        //     $roomId = $_SESSION['room_id'];
        //     $res    = [
        //         'type'             => 'logout',
        //         'from_client_id'   => $clientId,
        //         'from_client_name' => $_SESSION['client_name'],
        //         'time'             => date('Y-m-d H:i:s'),
        //     ];
        //     Gateway::sendToGroup($roomId, json_encode($res));
        // }
    }

    public static function login()
    {
        $arr      = self::$originalMessage;
        $clientId = self::$clientId;
        $token    = $arr['token'];

        try {
            $memberId = (new JwtAuth())->checkToken($token);
        } catch (\Exception $e) {
            throw new NotFoundHttpException('授权错误,请退出登录后重新登录');
        }
        if (!$memberId) {
            self::logout($clientId);
            self::$errorCode = 403;
            throw new NotFoundHttpException('授权错误,请退出登录后重新登录');
        }

        // 找用户信息
        $member = BaseMember::findOne($memberId);
        if (!$member) {
            self::logout($clientId);
            throw new NotFoundHttpException('授权错误');
        }

        // 这里对用户做一下权限限制
        if ($member->type == self::FROM_TYPE_RESUME) {
            // 求职者,求职者简历相关信息
            $resume           = BaseResume::findOne(['member_id' => $memberId]);
            $_SESSION['name'] = $resume->name ?: $member->username;
            // 加入求职者组
            Gateway::joinGroup($clientId, BaseChatRoom::CHAT_GROUP_RESUME);
        } elseif ($member->type == self::FROM_TYPE_COMPANY) {
            // 企业
            // 找到子账号信息?企业信息
            $companyInfo      = BaseCompanyMemberInfo::findOne(['member_id' => $memberId]);
            $_SESSION['name'] = $companyInfo->contact ?: $member->username;
            Gateway::joinGroup($clientId, BaseChatRoom::CHAT_GROUP_COMPANY);
        } else {
            throw new NotFoundHttpException('Unexpected user');
        }

        $_SESSION['memberId'] = $memberId;
        $_SESSION['type']     = $member->type;

        Gateway::bindUid($clientId, $memberId);

        // 清除定时器
        Timer::del($_SESSION['authTimerId']);
        //        todo:先暂时停止定时器
        //        $_SESSION['unread_timer_id'] = Timer::add(10, function () use ($memberId, $clientId) {
        //            self::sendUnreadAmountToClient($memberId, $clientId);
        //        });

        self::sendUnreadAmount($memberId);

        return true;
    }

    public static function message()
    {
        if (!$_SESSION['memberId']) {
            throw new NotFoundHttpException('登录失败');
        }

        if (self::$actionType != self::ACTION_TYPE_IS_READ) {
            // 这里做一个简单的禁止
            $isChat = BaseMember::findOneVal(['id' => $_SESSION['memberId']], 'is_chat');
            if ($isChat == BaseMember::IS_CHAT_BAN) {
                throw new NotFoundHttpException('直聊功能已禁用，暂无权限访问该页面！(您可联系平台客服进行申诉：020-85611139)');
            }
        }

        // 先初始化需要用到的数据
        self::$content  = self::$originalMessage['content'];
        $chatUUId       = self::$originalMessage['chatId'];
        $type           = self::$originalMessage['type'];
        $memberType     = $_SESSION['type'];
        $memberId       = $_SESSION['memberId'];
        self::$memberId = $memberId;
        self::$cuid     = self::$originalMessage['cuid'];

        // 1.判断是否是聊天室
        $chat = BaseChatRoom::findOne(['uuid' => $chatUUId]);

        if (!$chat) {
            self::logout(self::$clientId);
            throw new NotFoundHttpException('聊天室不存在,$chatUUId = ' . $chatUUId);
        }
        self::$chatRoomId = $chat->id;
        self::$chatUUId   = $chatUUId;
        // 2.判断是否是聊天室成员
        if ($memberType == self::FROM_TYPE_RESUME) {
            // 求职者
            if ($chat->resume_member_id != $memberId) {
                self::logout(self::$clientId);
                throw new NotFoundHttpException('您非聊天室成员');
            }
            self::$toMemberId = $chat->company_member_id;
            self::$toUserType = self::FROM_TYPE_COMPANY;
        } elseif ($memberType == self::FROM_TYPE_COMPANY) {
            // 单位
            if ($chat->company_member_id != $memberId) {
                self::logout(self::$clientId);
                throw new NotFoundHttpException('您非聊天室成员');
            }
            self::$toMemberId = $chat->resume_member_id;
            self::$toUserType = self::FROM_TYPE_RESUME;
        } else {
            self::logout(self::$clientId);
            throw new NotFoundHttpException('非正常聊天成员');
        }

        self::$resumeModel        = BaseResume::findOne(['member_id' => $chat->resume_member_id]);
        self::$companyMemberModel = BaseCompanyMemberInfo::findOne(['member_id' => $chat->company_member_id]);
        self::$fromUserType       = $memberType;
        self::$chatRoomModel      = $chat;

        $resumeMemberModel = BaseMember::findOne(self::$resumeModel->member_id);
        if ($resumeMemberModel->status == BaseMember::STATUS_RESUME_CANCELED && $type == self::ACTION_TYPE_TEXT) {
            throw new NotFoundHttpException('该用户已注销账号');
        }
        // 3.判断是否是允许的类型
        if (!isset(self::ACTION_TYPE_LIST[$type])) {
            throw new NotFoundHttpException('非法操作');
        }

        // 4.判断是否是允许的用户类型
        if (!in_array($memberType, self::ACTION_TYPE_LIST[$type]['allow'])) {
            throw new NotFoundHttpException('非法操作');
        }

        // 做一些数据初始化

        // 开始按照类型分发
        switch ($type) {
            case self::ACTION_TYPE_TEXT:
                static::text();
                break;
            case self::ACTION_TYPE_JOB_CARD:
                static::jobCard();
                break;
            case self::ACTION_TYPE_JOB_APPLY:
                static::jobApply();
                break;
            case self::ACTION_TYPE_RESUME_CARD:
                static::resumeCard();
                break;
            case self::ACTION_TYPE_REQUEST_FILE:
                static::requestFile();
                break;
            case self::ACTION_TYPE_AGREE_REQUEST_FILE:
                static::agreeRequestFile();
                break;
            case self::ACTION_TYPE_IGNORE_REQUEST_FILE:
                static::ignoreRequestFile();
                break;
            case self::ACTION_TYPE_FILE:
                static::file();
                break;
            case self::ACTION_TYPE_IS_READ:
                static::isRead();
                break;
            case self::ACTION_TYPE_RESUME_COMPLETE_CARD:
                static::resumeCompleteCard();
                break;
            case self::ACTION_TYPE_INVITE_JOB_REQUEST_CARD:
                static::inviteJobRequestCard();
                break;
            case self::ACTION_TYPE_INVITE_JOB_REQUEST_IGNORE:
                static::inviteJobRequestIgnore();
                break;
            case self::ACTION_TYPE_INVITE_JOB_REQUEST_INTEREST:
                static::inviteJobRequestInterest();
                break;
            case self::ACTION_TYPE_CHANGE_JOB:
                static::changeJob();
                break;
            default:
                throw new NotFoundHttpException('Unexpected type44');
        }

        // 这里做一些消息的后置处理
        if (self::$chatRoomModel->talk_progress == BaseChatRoom::TALK_PROGRESS_ONE_WAY) {
            // 双方都发过消息了
            if (BaseChatMessage::find()
                    ->where([
                        'to_member_id' => self::$memberId,
                        'chat_room_id' => self::$chatRoomId,
                    ])
                    ->exists() && BaseChatMessage::find()
                    ->where([
                        'from_member_id' => self::$memberId,
                        'chat_room_id'   => self::$chatRoomId,
                    ])
                    ->exists()) {
                self::$chatRoomModel->talk_progress = BaseChatRoom::TALK_PROGRESS_TWO_WAY;
            }
        }

        self::$chatRoomModel->save();

        if (self::$chatRoomModel->talk_progress == BaseChatRoom::TALK_PROGRESS_TWO_WAY) {
            //推消息
            $app = MessageCenterApplication::getInstance();
            $app->ChatWayTwoService(self::$chatRoomModel->id);
        }

        return true;
    }

    // 表情和文案,简单实现就可以了(需要走一下验证)
    public static function text()
    {
        $text = trim(self::$content['text']);

        // 判断空内容,但是可能是0
        if ($text === '') {
            throw new NotFoundHttpException('空内容不允许发送');
        }

        // 首先检查是否合法
        $green = new Green();
        if (!$green->checkText($text)) {
            $reason  = $green->reason;
            $content = '<p>该消息内容包含 <span style="color:red">' . $reason . '</span> 等敏感信息，不支持发送！<p>';
            self::errorToClient(self::$clientId, $content);

            return false;
        } else {
            // 合法后入库
            self::$saveContent = [
                'text' => $text,
            ];

            self::addOneMessage();
            self::sendToTarget();
        }

        self::sendToMyself();

        return true;
    }

    // 职位卡片,这里需要做一些判断
    public static function jobCard()
    {
        $jobId = self::$content['jobId'];

        // 看看这个职位是不是存在,
        $jobInfo = BaseJob::findOne([
            'id' => $jobId,
        ]);
        if (!$jobInfo) {
            throw new NotFoundHttpException('职位不存在');
        }
        // 看看这个职位是否属于这个单位账号的
        if ($jobInfo->company_id != self::$companyMemberModel->company_id) {
            throw new NotFoundHttpException('职位不属于当前单位');
        }

        // 找到聊天室的最后一个职位历史,进行对比
        $lastChatJobModel = BaseChatHistoryJob::find()
            ->where(['chat_room_id' => self::$chatRoomId])
            ->orderBy(['id' => SORT_DESC])
            ->one();
        if ($lastChatJobModel) {
            if ($lastChatJobModel->chat_message_id) {
                // 代表已经发了卡片了,无需再发了
                return true;
            }
        }

        self::$saveContent = [
            'jobId'       => $jobId,
            'nowMemberId' => self::$memberId,
        ];

        $messageId = self::addOneMessage($jobId);

        // 这里一定要及时更新,要不职位卡片那边对应不上
        $lastChatJobModel->chat_message_id = $messageId;
        $lastChatJobModel->save();

        self::sendToMyself();
        self::sendToTarget();

        return $messageId;
    }

    public static function changeJob()
    {
        $jobId = self::$content['jobId'];

        // 看看这个职位是不是存在,
        $jobInfo = BaseJob::findOne([
            'id' => $jobId,
        ]);
        if (!$jobInfo) {
            throw new NotFoundHttpException('职位不存在');
        }
        // 看看这个职位是否属于这个单位账号的
        if ($jobInfo->company_id != self::$companyMemberModel->company_id) {
            throw new NotFoundHttpException('职位不属于当前单位');
        }

        // 找到聊天室的最后一个职位历史,进行对比
        $lastChatJobModel = BaseChatHistoryJob::find()
            ->where(['chat_room_id' => self::$chatRoomId])
            ->orderBy(['id' => SORT_DESC])
            ->one();
        if ($lastChatJobModel->job_id == $jobId) {
            // 代表已经发了卡片了,无需再发了
            return true;
        }

        //更新当前房间的当前职位id
        self::$chatRoomModel->current_job_id = $jobId;
        self::$chatRoomModel->save();

        self::$saveContent = [
            'jobId'       => $jobId,
            'nowMemberId' => self::$memberId,
        ];

        $messageId = self::addOneMessage($jobId);

        // 要往BaseChatHistoryJob里面写一个
        $chatHistoryJobModel                    = new BaseChatHistoryJob();
        $chatHistoryJobModel->chat_room_id      = self::$chatRoomId;
        $chatHistoryJobModel->job_id            = $jobId;
        $chatHistoryJobModel->add_time          = date('Y-m-d H:i:s');
        $chatHistoryJobModel->creator_type      = self::$fromUserType;
        $chatHistoryJobModel->creator_member_id = self::$memberId;
        $chatHistoryJobModel->type              = BaseChatHistoryJob::TYPE_CHANGE;
        $chatHistoryJobModel->is_apply          = BaseChatHistoryJob::IS_APPLY_NO;
        $chatHistoryJobModel->chat_message_id   = $messageId;
        $chatHistoryJobModel->save();

        //保存后更新职位数量
        BaseChatHistoryJob::updateRoomTalkJobAmount(self::$chatRoomId);

        self::sendToMyself();
        self::sendToTarget();

        return $messageId;
    }

    // 职位投递,需要做一些判断,例如是不是这个人投递的,是不是这 个人的职位的
    public static function jobApply()
    {
        $jobApplyId = self::$content['jobApplyId'];

        if (!$jobApplyId) {
            throw new NotFoundHttpException('投递id非法');
        }

        // 看看这个投递是否已存在并且是发送方的
        $resumeId  = self::$resumeModel->id;
        $companyId = self::$companyMemberModel->company_id;

        if (!BaseJobApply::find()
            ->where([
                'id'         => $jobApplyId,
                'resume_id'  => $resumeId,
                'company_id' => $companyId,
            ])
            ->exists()) {
            throw new NotFoundHttpException('非法操作,你还没投递过该职位');
        }

        self::$saveContent = ['jobApplyId' => $jobApplyId];

        // 这里是测试代码,后续需要调整
        self::addOneMessage($jobApplyId);
    }

    // 简历卡片,这个和上面的职位投递是联动的
    public static function resumeCard()
    {
        $jobApplyId = self::$content['jobApplyId'];

        if (!$jobApplyId) {
            throw new NotFoundHttpException('投递id非法');
        }

        // 看看这个投递是否已存在并且是发送方的
        $resumeId  = self::$resumeModel->id;
        $companyId = self::$companyMemberModel->company_id;

        if (!BaseJobApply::find()
            ->where([
                'id'         => $jobApplyId,
                'resume_id'  => $resumeId,
                'company_id' => $companyId,
            ])
            ->exists()) {
            throw new NotFoundHttpException('非法操作,你还没投递过该职位');
        }

        self::$saveContent = ['jobApplyId' => $jobApplyId];

        // 把最新的那一个对应的投递改为成功
        $jobId            = BaseJobApply::findOne($jobApplyId)->job_id;
        $lastChatJobModel = BaseChatHistoryJob::find()
            ->where([
                'chat_room_id' => self::$chatRoomId,
                'job_id'       => $jobId,
            ])
            ->orderBy(['id' => SORT_DESC])
            ->one();

        if ($lastChatJobModel) {
            $lastChatJobModel->is_apply = BaseChatHistoryJob::IS_APPLY_YES;
            $lastChatJobModel->save();
        }

        // 先加系统消息
        self::addSystemMessage();
        self::addOneMessage($jobApplyId);
        self::sendToMyself();
        self::sendToTarget();
    }

    // 求职者向单位请求发送附件申请,这里需要做一些判断,例如是不是这个人投递的,是不是这个单位的职位,是否已经申请过了等等
    public static function requestFile()
    {
        $jobApplyId = self::$content['jobApplyId'];

        if (!$jobApplyId) {
            throw new NotFoundHttpException('投递id非法');
        }

        // 看看这个投递是否已存在并且是发送方的
        $resumeId  = self::$resumeModel->id;
        $companyId = self::$companyMemberModel->company_id;

        if (!BaseJobApply::find()
            ->where([
                'id'         => $jobApplyId,
                'resume_id'  => $resumeId,
                'company_id' => $companyId,
            ])
            ->exists()) {
            throw new NotFoundHttpException('非法操作,你还没投递过该职位');
        }

        self::$saveContent = ['jobApplyId' => $jobApplyId];

        //修改聊天室的附件发送状态
        //判断房间状态，如果同意过了，就不处理
        $model = ChatRoom::findOne(self::$chatRoomId);
        if ($model->is_agree_file == BaseChatRoom::IS_AGREE_FILE_YES) {
            return true;
        }
        $model->is_agree_file = BaseChatRoom::IS_AGREE_FILE_WAITING;
        $model->save();
        self::addSystemMessage($jobApplyId);
        // 最后还要给单位发送一个特殊的消息,告诉单位有人请求发送附件
        self::$messageId = BaseChatMessage::REQUEST_FILE_MESSAGE_ID;
        self::sendToTarget();

        // 更新单位左边session
        $sessionInfo                = BaseChatRoomSession::getLastSessionInfo(self::$toMemberId, self::$chatRoomId);
        $sessionFullInfo['content'] = $sessionInfo;
        $sessionFullInfo['type']    = 'updateSession';
        Gateway::sendToUid(self::$toMemberId, json_encode($sessionFullInfo));
    }

    // 有可能求职者之前发送过请求,也有可能单位主动发起的,两者写的逻辑可能会有点不一样?
    public static function agreeRequestFile()
    {
        // 查看投递信息是否正确
        $resumeId  = self::$resumeModel->id;
        $companyId = self::$companyMemberModel->company_id;

        if (!BaseJobApply::find()
            ->where([
                'resume_id'  => $resumeId,
                'company_id' => $companyId,
            ])
            ->exists()) {
            throw new NotFoundHttpException('非法操作,投递记录不存在');
        }

        self::$saveContent            = [];
        $chatRoomModel                = BaseChatRoom::findOne(self::$chatRoomId);
        $oldFileStatus                = $chatRoomModel->is_agree_file;
        $chatRoomModel->is_agree_file = BaseChatRoom::IS_AGREE_FILE_YES;
        $chatRoomModel->save();

        self::addSystemMessage();
        self::addOneMessage();
        self::sendToMyself();
        self::sendToTarget();
        // 这里有一种情况,如果求职者之前发起过请求,那么这里就要把之前的请求消息删除掉
        // 返回单位删除提示弹窗的消息
        if ($oldFileStatus == BaseChatRoom::IS_AGREE_FILE_WAITING) {
            $returnArr['chatId'] = self::$chatUUId;
            $returnArr['type']   = 'requestFile';
            Gateway::sendToUid(self::$memberId, json_encode($returnArr));
        }
    }

    public static function ignoreRequestFile()
    {
        // 查看投递信息是否正确
        $resumeId  = self::$resumeModel->id;
        $companyId = self::$companyMemberModel->company_id;

        if (!BaseJobApply::find()
            ->where([
                'resume_id'  => $resumeId,
                'company_id' => $companyId,
            ])
            ->exists()) {
            throw new NotFoundHttpException('非法操作,投递记录不存在');
        }

        self::$saveContent = [];
        //只更新状态
        $chatRoomModel                = BaseChatRoom::findOne(self::$chatRoomId);
        $chatRoomModel->is_agree_file = BaseChatRoom::IS_AGREE_FILE_WAIT;
        $chatRoomModel->save();
        // 返回单位删除提示弹窗的消息
        $returnArr['chatId'] = self::$chatUUId;
        $returnArr['type']   = 'requestFile';
        Gateway::sendToUid(self::$memberId, json_encode($returnArr));
    }

    public static function file()
    {
        $fileId   = self::$content['fileId'];
        $memberId = self::$memberId;

        // 去file表里面找是不是我的
        $fileModel = BaseFile::findOne([
            'id'           => $fileId,
            'creator_id'   => $memberId,
            'creator_type' => 2,
        ]);

        if (!$fileModel) {
            throw new NotFoundHttpException('文件不存在');
        }

        // 30天后就过期,是当前日+30天
        $expireTime = date('Y/m/d', strtotime('+30 day'));
        // 再截取掉前面的20
        $expireTime = '（' . substr($expireTime, 2) . ' 过期）';

        self::$saveContent = [
            'fileId'   => $fileId,
            'fileName' => $fileModel->name,
            'fileType' => substr(strrchr($fileModel->name, '.'), 1),
            'size'     => BaseFile::sizeToTxt($fileModel->size) . $expireTime,
        ];

        self::addOneMessage($fileId);

        // 写一个文件的记录
        $chatFileModel                  = new BaseChatMessageFile();
        $chatFileModel->chat_message_id = self::$messageId;
        $chatFileModel->file_id         = $fileId;
        $chatFileModel->chat_room_id    = self::$chatRoomId;
        $chatFileModel->file_url        = $fileModel->path;
        $chatFileModel->file_name       = $fileModel->name;
        $chatFileModel->save();

        self::sendToMyself();
        self::sendToTarget();
    }

    public static function isRead()
    {
        // 把这个房间号,我的信息,全部已读
        $chatId = self::$chatRoomId;

        BaseChatMessage::updateAll([
            'status'    => BaseChatMessage::STATUS_READ,
            'is_read'   => BaseChatMessage::IS_READ_YES,
            'read_time' => date('Y-m-d H:i:s'),
        ], [
            'chat_room_id' => $chatId,
            'to_member_id' => self::$memberId,
            'status'       => BaseChatMessage::STATUS_DELIVERY,
        ]);

        // 还的找到对方的session,给消息总数弄成数量为0
        $chatRoomSession = BaseChatRoomSession::findOne([
            'member_id'    => self::$memberId,
            'chat_room_id' => self::$chatRoomId,
        ]);

        if ($chatRoomSession) {
            $chatRoomSession->unread_amount = 0;
            $chatRoomSession->save();
        } else {
            return;
        }

        $content           = self::$originalMessage;
        $content['status'] = BaseChatMessage::STATUS_READ;

        // 通知对方已读
        Gateway::sendToUid(self::$toMemberId, json_encode($content));

        // 更新自己左边session
        $sessionInfo                = BaseChatRoomSession::getLastSessionInfo(self::$memberId, self::$chatRoomId);
        $sessionFullInfo['content'] = $sessionInfo;
        $sessionFullInfo['type']    = 'updateSession';
        Gateway::sendToUid(self::$memberId, json_encode($sessionFullInfo));
        // 更新对方左边session
        $sessionInfo                = BaseChatRoomSession::getLastSessionInfo(self::$toMemberId, self::$chatRoomId);
        $sessionFullInfo['content'] = $sessionInfo;
        $sessionFullInfo['type']    = 'updateSession';

        Gateway::sendToUid(self::$toMemberId, json_encode($sessionFullInfo));
        // 还多更新读取数量
        self::sendUnreadAmount(self::$memberId);
    }

    public static function resumeCompleteCard()
    {
        $resumeId = self::$content['resumeId'];

        // 查看是否存在
        $resumeInfo = BaseResume::findOne($resumeId);
        if (!$resumeInfo) {
            throw new NotFoundHttpException('简历不存在');
        }

        self::$saveContent = ['resumeId' => $resumeId];
        self::addOneMessage($resumeId);

        self::sendToMyself();
        self::sendToTarget();
    }

    /**
     * 职位邀约卡片
     * @return void
     * @throws NotFoundHttpException
     */
    public static function inviteJobRequestCard()
    {
        $jobId = self::$content['jobId'];

        //判断职位是否存在
        $jobInfo = BaseJob::findOne($jobId);
        if (!$jobInfo || $jobInfo->status != BaseJob::STATUS_ACTIVE) {
            throw new NotFoundHttpException('职位不存在');
        }
        // 看看这个职位是否属于这个单位账号的
        if ($jobInfo->company_id != self::$companyMemberModel->company_id) {
            throw new NotFoundHttpException('职位不属于当前单位');
        }

        self::$saveContent = ['jobId' => $jobId];
        self::addOneMessage($jobId);

        self::sendToMyself();
        self::sendToTarget();
    }

    /**
     * 求职者忽略投递邀约
     * @return void
     * @throws NotFoundHttpException
     */
    public static function inviteJobRequestIgnore()
    {
        $messageId = self::$content['messageId'];
        //判断职位是否存在
        //查看是否有推送投递邀约的
        $chatMessageInfo = BaseChatMessage::findOne([
            'type'         => BaseChatMessage::TYPE_INVITE_JOB_REQUEST_CARD,
            'to_member_id' => self::$memberId,
            'id'           => $messageId,
        ]);

        if (!$chatMessageInfo) {
            throw new NotFoundHttpException('投递邀约记录不存在！');
        }

        self::$saveContent = ['messageId' => $messageId];
        //        self::addOneMessage($jobId);
        //更新卡片状态，推送求职者
        $cardModel                   = BaseChatMessageCard::findOne(['chat_message_id' => $messageId]);
        $cardModel->operation_status = BaseChatMessageCard::INVITE_OPERATION_STATUS_IGNORE;
        if (!$cardModel->save()) {
            throw new NotFoundHttpException($cardModel->getFirstErrorsMessage());
        }

        // 这里是相对特殊的操作,要找到内容直接返回回去就可以了
        $app          = ChatApplication::getInstance();
        $data         = $app->getHistoryOne($messageId, $chatMessageInfo->to_member_id);
        $data['cuid'] = self::$cuid;

        Gateway::sendToUid($chatMessageInfo->to_member_id, json_encode($data));
        // 返回给自己

    }

    /**
     * 投递邀约卡片操作感兴趣
     * @return void
     * @throws NotFoundHttpException
     */
    public static function inviteJobRequestInterest()
    {
        $messageId = self::$content['messageId'];
        //判断职位是否存在
        //查看是否有推送投递邀约的
        $chatMessageInfo = BaseChatMessage::findOne([
            'type'         => BaseChatMessage::TYPE_INVITE_JOB_REQUEST_CARD,
            'to_member_id' => self::$memberId,
            'id'           => $messageId,
        ]);

        if (!$chatMessageInfo) {
            throw new NotFoundHttpException('投递邀约记录不存在！');
        }

        self::$saveContent = ['messageId' => $messageId];
        //只发给求职者就好
        $cardModel                   = BaseChatMessageCard::findOne(['chat_message_id' => $messageId]);
        $cardModel->operation_status = BaseChatMessageCard::INVITE_OPERATION_STATUS_INTEREST;
        if (!$cardModel->save()) {
            throw new NotFoundHttpException($cardModel->getFirstErrorsMessage());
        }

        // 这里是相对特殊的操作,要找到内容直接返回回去就可以了
        $app          = ChatApplication::getInstance();
        $data         = $app->getHistoryOne($messageId, $chatMessageInfo->to_member_id);
        $data['cuid'] = self::$cuid;

        Gateway::sendToUid($chatMessageInfo->to_member_id, json_encode($data));
        // 返回给自己
    }

    public static function addSystemMessage($mainId = 0)
    {
        $model                 = new BaseChatMessage();
        $model->content        = json_encode(self::$saveContent);
        $model->type           = self::ACTION_TYPE_LIST[self::$actionType]['systemType'];
        $model->main_id        = $mainId;
        $model->is_read        = BaseChatMessage::IS_READ_NO;
        $model->from_member_id = self::$memberId;
        $model->to_member_id   = self::$toMemberId;
        $model->chat_room_id   = self::$chatRoomId;
        $model->job_id         = self::$chatRoomModel->current_job_id;
        $model->add_time       = date('Y-m-d H:i:s');

        // 距离现在超过3分钟,需要给消息上时间
        $LastTalkTime = self::$chatRoomModel->last_talk_time;
        if (strtotime($LastTalkTime) < time() - 3 * 60 || !BaseChatMessage::find()
                ->where(['chat_room_id' => self::$chatRoomId])
                ->exists()) {
            // 时分
            self::$showTime      = date('H:i');
            $model->is_show_time = BaseChatMessage::IS_SHOW_TIME_YES;
        } else {
            $model->is_show_time = BaseChatMessage::IS_SHOW_TIME_NO;
            self::$showTime      = '';
        }

        if (!$model->save()) {
            throw new NotFoundHttpException($model->getFirstErrorsMessage());
        }
        $messageId = $model->id;

        // 去操作会话列表
        $sessionModel = BaseChatRoomSession::findOne([
            'chat_room_id' => self::$chatRoomId,
            'member_id'    => self::$toMemberId,
        ]);

        //对方的session记录
        if (!$sessionModel) {
            $sessionModel               = new BaseChatRoomSession();
            $sessionModel->chat_room_id = self::$chatRoomId;
            $sessionModel->member_id    = self::$toMemberId;
            $sessionModel->is_delete    = BaseChatRoomSession::IS_DELETE_NO;
            $sessionModel->is_top       = BaseChatRoomSession::IS_TOP_NO;
            $sessionModel->add_time     = date('Y-m-d H:i:s');
        } else {
            $sessionModel->update_time = date('Y-m-d H:i:s');
        }
        $sessionModel->unread_amount  += 1;
        $sessionModel->last_talk_time = date('Y-m-d H:i:s');
        $sessionModel->save();

        //自己的session记录
        $selfSessionModel = BaseChatRoomSession::findOne([
            'chat_room_id' => self::$chatRoomId,
            'member_id'    => self::$memberId,
        ]);
        if (!$selfSessionModel) {
            $selfSessionModel               = new BaseChatRoomSession();
            $selfSessionModel->chat_room_id = self::$chatRoomId;
            $selfSessionModel->member_id    = self::$memberId;
            $selfSessionModel->is_delete    = BaseChatRoomSession::IS_DELETE_NO;
            $selfSessionModel->is_top       = BaseChatRoomSession::IS_TOP_NO;
            $selfSessionModel->add_time     = date('Y-m-d H:i:s');
        } else {
            $selfSessionModel->update_time = date('Y-m-d H:i:s');
        }
        $selfSessionModel->last_talk_time = date('Y-m-d H:i:s');
        $selfSessionModel->save();
        // 这里还要更新一下聊天室的最后的talktime
        self::$chatRoomModel->last_talk_time = date('Y-m-d H:i:s');

        self::$messageId = $model->id;

        // 在添加某些消息前,是要发送一个系统消息的,这里面就需要做一个简单的判断了,例如简历卡片
        $arr               = [];
        $messageContentArr = self::getSystemMessageContent();
        $arr['content']    = $messageContentArr['fromContent'];

        $arr['status']    = BaseChatMessage::STATUS_DELIVERY;
        $arr['messageId'] = $model->id . '';
        $arr['type']      = self::ACTION_TYPE_SYSTEM;
        $arr['chatId']    = self::$chatUUId;
        $arr['cuid']      = self::$cuid;
        $arr['memberId']  = self::$memberId;
        $arr['time']      = self::$showTime;
        Gateway::sendToUid(self::$memberId, json_encode($arr));

        // 发给单位端
        $arr['content']  = $messageContentArr['toContent'];
        $arr['memberId'] = self::$toMemberId;
        Gateway::sendToUid(self::$toMemberId, json_encode($arr));
    }

    /**
     * 获取不同类型系统消息内容
     * @return array|\string[][]
     */
    public static function getSystemMessageContent()
    {
        switch (self::$actionType) {
            case self::ACTION_TYPE_RESUME_CARD:
                $content = [
                    'fromContent' => ['text' => '您已投递了该职位'],
                    'toContent'   => ['text' => '对方已投递该职位'],
                ];
                break;
            case self::ACTION_TYPE_REQUEST_FILE:
                $content = [
                    'toContent'   => ['text' => '对方正在向您请求发送附件'],
                    'fromContent' => ['text' => '您已向对方请求发送附件'],
                ];
                break;
            case self::ACTION_TYPE_AGREE_REQUEST_FILE:
                $content = [
                    'toContent'   => ['text' => '对方已同意您向其发送附件'],
                    'fromContent' => ['text' => '您已同意对方向您发送附件'],
                ];
                break;
            default:
                return [];
        }

        return $content;
    }

    public static function addOneMessage($mainId = 0)
    {
        $model                 = new BaseChatMessage();
        $model->content        = json_encode(self::$saveContent);
        $model->type           = self::ACTION_TYPE_LIST[self::$actionType]['messageType'];
        $model->main_id        = $mainId;
        $model->is_read        = BaseChatMessage::IS_READ_NO;
        $model->from_member_id = self::$memberId;
        $model->to_member_id   = self::$toMemberId;
        $model->chat_room_id   = self::$chatRoomId;
        $model->job_id         = self::$chatRoomModel->current_job_id;
        $model->add_time       = date('Y-m-d H:i:s');

        // 距离现在超过3分钟,需要给消息上时间
        $LastTalkTime = self::$chatRoomModel->last_talk_time;
        if (strtotime($LastTalkTime) < time() - 3 * 60 || !BaseChatMessage::find()
                ->where(['chat_room_id' => self::$chatRoomId])
                ->exists()) {
            // 时分
            self::$showTime      = date('H:i');
            $model->is_show_time = BaseChatMessage::IS_SHOW_TIME_YES;
        } else {
            $model->is_show_time = BaseChatMessage::IS_SHOW_TIME_NO;
            self::$showTime      = '';
        }

        if (!$model->save()) {
            throw new NotFoundHttpException($model->getFirstErrorsMessage());
        }
        $messageId = $model->id;

        // 去操作会话列表
        $sessionModel = BaseChatRoomSession::findOne([
            'chat_room_id' => self::$chatRoomId,
            'member_id'    => self::$toMemberId,
        ]);

        if (!$sessionModel) {
            $sessionModel               = new BaseChatRoomSession();
            $sessionModel->chat_room_id = self::$chatRoomId;
            $sessionModel->member_id    = self::$toMemberId;
            $sessionModel->is_delete    = BaseChatRoomSession::IS_DELETE_NO;
            $sessionModel->is_top       = BaseChatRoomSession::IS_TOP_NO;
            $sessionModel->add_time     = date('Y-m-d H:i:s');
        } else {
            $sessionModel->update_time = date('Y-m-d H:i:s');
        }
        $sessionModel->unread_amount  += 1;
        $sessionModel->last_talk_time = date('Y-m-d H:i:s');
        $sessionModel->save();

        //自己的session记录
        $selfSessionModel = BaseChatRoomSession::findOne([
            'chat_room_id' => self::$chatRoomId,
            'member_id'    => self::$memberId,
        ]);
        if (!$selfSessionModel) {
            $selfSessionModel               = new BaseChatRoomSession();
            $selfSessionModel->chat_room_id = self::$chatRoomId;
            $selfSessionModel->member_id    = self::$memberId;
            $selfSessionModel->is_delete    = BaseChatRoomSession::IS_DELETE_NO;
            $selfSessionModel->is_top       = BaseChatRoomSession::IS_TOP_NO;
            $selfSessionModel->add_time     = date('Y-m-d H:i:s');
        } else {
            $selfSessionModel->update_time = date('Y-m-d H:i:s');
        }
        $selfSessionModel->last_talk_time = date('Y-m-d H:i:s');
        $selfSessionModel->save();

        // 这里还要更新一下聊天室的最后的talktime
        self::$chatRoomModel->last_talk_time = date('Y-m-d H:i:s');

        $cardType = self::ACTION_TYPE_LIST[self::$actionType]['cardType'];
        if ($cardType) {
            //如果当前消息是卡片类型，新增记录
            $cardModel                  = new BaseChatMessageCard();
            $cardModel->add_time        = date('Y-m-d H:i:s');
            $cardModel->type            = $cardType;
            $cardModel->chat_room_id    = self::$chatRoomId;
            $cardModel->chat_message_id = $messageId;
            $cardModel->save();
        }

        self::$messageId = $messageId;

        return $messageId;
    }

    // 成功后给自己会一个已送达并且处理过的消息
    public static function sendToMyself()
    {
        $arr              = [];
        $arr['content']   = BaseChatMessage::getRealContent(self::ACTION_TYPE_LIST[self::$actionType]['messageType'],
            self::$fromUserType, self::$saveContent, self::$messageId) ?: [];
        $arr['status']    = BaseChatMessage::STATUS_DELIVERY;
        $arr['messageId'] = self::$messageId . '';
        // 这里有可能是卡片操作,所以返回去的类型要按照情况来判断
        $arr['type']     = BaseChatMessage::TYPE_TO_SHOW_TYPE_LIST[self::ACTION_TYPE_LIST[self::$actionType]['messageType']];
        $arr['chatId']   = self::$chatUUId;
        $arr['cuid']     = self::$cuid;
        $arr['memberId'] = self::$memberId;
        $arr['avatar']   = BaseChatMessage::getMessageAvatar(self::$memberId);
        $arr['time']     = self::$showTime ?: '';
        Gateway::sendToUid(self::$memberId, json_encode($arr));

        //再发一条侧边栏卡片信息
        if (self::$messageId != BaseChatMessage::REQUEST_FILE_MESSAGE_ID) {
            $sessionInfo                = BaseChatRoomSession::getSessionInfo(self::$memberId, self::$messageId);
            $sessionFullInfo['content'] = $sessionInfo;
            $sessionFullInfo['type']    = 'updateSession';
            Gateway::sendToUid(self::$memberId, json_encode($sessionFullInfo));
        }

        // 还多更新读取数量
        self::sendUnreadAmount(self::$memberId);
    }

    // 成功也给对方发一个已送达并且处理过的消息
    public static function sendToTarget()
    {
        $arr = [];
        // 这里会有一个很奇怪的逻辑,需要知道当前接收人
        self::$saveContent['nowMemberId'] = self::$toMemberId;
        $arr['content']                   = BaseChatMessage::getRealContent(self::ACTION_TYPE_LIST[self::$actionType]['messageType'],
            self::$toUserType, self::$saveContent, self::$messageId);
        $arr['status']                    = BaseChatMessage::STATUS_DELIVERY;
        $arr['messageId']                 = self::$messageId . '';
        $arr['type']                      = BaseChatMessage::TYPE_TO_SHOW_TYPE_LIST[self::ACTION_TYPE_LIST[self::$actionType]['messageType']];
        $arr['chatId']                    = self::$chatUUId;
        $arr['cuid']                      = self::$cuid;
        $arr['memberId']                  = self::$memberId . '';
        //判断接收用户是不是单位，如果是求职者发给单位，判断头像是否要模糊
        $toMemberRole  = BaseMember::findOneVal(['id' => self::$toMemberId], 'type');
        $companyId     = $toMemberRole == BaseMember::TYPE_COMPANY ? BaseCompanyMemberInfo::findOneVal(['member_id' => self::$toMemberId],
            'company_id') : '';
        $arr['avatar'] = BaseChatMessage::getMessageAvatar(self::$memberId, $companyId);
        $arr['time']   = self::$showTime;

        Gateway::sendToUid(self::$toMemberId, json_encode($arr));
        //发送多1条session信息
        if (self::$messageId != BaseChatMessage::REQUEST_FILE_MESSAGE_ID) {
            $sessionInfo = BaseChatRoomSession::getSessionInfo(self::$toMemberId, self::$messageId);

            $sessionFullInfo['content'] = $sessionInfo;
            $sessionFullInfo['type']    = 'updateSession';
            Gateway::sendToUid(self::$toMemberId, json_encode($sessionFullInfo));
        }

        // 这里如果对方已经把我们的session删除了,就要改为非删除的
        $sessionModel = BaseChatRoomSession::findOne([
            'chat_room_id' => self::$chatRoomId,
            'member_id'    => self::$toMemberId,
        ]);
        if ($sessionModel && $sessionModel->is_delete == BaseChatRoomSession::IS_DELETE_YES) {
            $sessionModel->is_delete = BaseChatRoomSession::IS_DELETE_NO;
            $sessionModel->save();
        }

        // 还多更新读取数量
        self::sendUnreadAmount(self::$toMemberId);
    }

    public static function logout($clientId)
    {
        Gateway::closeClient($clientId);
    }

    public static function errorToClient($clientId, $content)
    {
        $content = [
            'type'    => 'errorMessage',
            'content' => [
                'title'      => '提示',
                'buttonText' => '确定',
                'content'    => $content,
                'code'       => self::$errorCode,
            ],
        ];

        Gateway::sendToClient($clientId, json_encode($content));
    }

    public static function sendUnreadAmount($memberId)
    {
        // 有三个数字,直聊未读总数
        $data = BaseMemberMessage::getUnreadData($memberId);
        // 需要一个新的类型,unread
        $content = [
            'type'    => self::ACTION_TYPE_UNREAD,
            'content' => $data,
        ];

        Gateway::sendToUid($memberId, json_encode($content));
    }

    public static function sendUnreadAmountToClient($memberId, $clientId)
    {
        // 有三个数字,直聊未读总数
        $data = BaseMemberMessage::getUnreadData($memberId);
        // 需要一个新的类型,unread
        $content = [
            'type'    => self::ACTION_TYPE_UNREAD,
            'content' => $data,
        ];

        Gateway::sendToClient($clientId, json_encode($content));
    }

    public static function clearAll($memberId)
    {
        $baseWhere = [
            'to_member_id' => $memberId,
            'status'       => BaseChatMessage::STATUS_DELIVERY,
        ];

        //. 找到全部的里面的对方的memberId和roomId,需要把session更新,并且通知对方
        $fromMemberIds = BaseChatMessage::find()
            ->select([
                'from_member_id',
                'chat_room_id',
            ])
            ->where($baseWhere)
            ->groupBy('chat_room_id')
            ->asArray()
            ->all();

        BaseChatMessage::updateAll([
            'status'    => BaseChatMessage::STATUS_READ,
            'read_time' => date('Y-m-d H:i:s'),
        ], $baseWhere);

        $content = [
            'type' => self::ACTION_TYPE_CLEAR_ALL,
        ];

        Gateway::sendToUid($memberId, json_encode($content));

        foreach ($fromMemberIds as $item) {
            $chatRoomId = $item['chat_room_id'];

            $sessionModel                = BaseChatRoomSession::findOne([
                'chat_room_id' => $chatRoomId,
                'member_id'    => $memberId,
            ]);
            $sessionModel->unread_amount = 0;
            $sessionModel->save();

            $chatUUid = BaseChatRoom::findOneVal(['id' => $chatRoomId], 'uuid');

            // 通知对方已读啦
            $arr = [
                'type'   => self::ACTION_TYPE_IS_READ,
                'status' => BaseChatMessage::STATUS_READ,
                'chatId' => $chatUUid,
            ];

            Gateway::sendToUid($item['from_member_id'], json_encode($arr));

            $sessionInfo                = BaseChatRoomSession::getLastSessionInfo($item['from_member_id'], $chatRoomId);
            $sessionFullInfo['content'] = $sessionInfo;
            $sessionFullInfo['type']    = 'updateSession';
            Gateway::sendToUid($item['from_member_id'], json_encode($sessionFullInfo));
        }

        self::sendUnreadAmount($memberId);
    }

}
