<?php

namespace queue;

use common\helpers\DebugHelper;
use common\libs\Cache;
use common\libs\EmailQueue;
use common\libs\SmsQueue;
use Yii;

/**
 * 队列生产者,统一管理,以后所有的队列都在这里来生成,而消费,就在分别的job里面消费,便于以后迁移
 */
class Producer
{
    public static function sms($mobile, $type, $smsType, $mobileCode = '', $extParams = '')
    {
        $sms = new SmsQueue($mobile, $type, $smsType, $mobileCode, $extParams);

        $id    = $sms->add();
        $queue = Yii::$app->smsQueue;
        if (in_array($smsType, SmsQueue::RELAY_SEND_TYPE)) {
            $queue->delay(5);
        }

        $queue->push(new SmsJob([
            'id'       => $id,
            'userType' => $type,
        ]));

        return $id;
    }

    public static function email($email, $type, $emailType, $content, $pageData = [])
    {
        // 本地暂时改为发给我自己
        if (Yii::$app->params['environment'] === 'local' && Yii::$app->params['localEmail']) {
            $email = Yii::$app->params['localEmail'];
        }

        $content = $content ? json_encode($content) : '';

        $smtp = new EmailQueue($email, $type, $emailType, $content, $pageData);

        $id = $smtp->add();

        // 在正式环境区分一下队列,用三组队列去处理邮件,这样给每个渠道的压力不至于太大(这里的类型是结合到邮件发送里面的类型判断的,如果修改就需要两边都修改)
        if (Yii::$app->params['environment'] === 'prod') {
            switch ($emailType) {
                case EmailQueue::EMAIL_TYPE_REGISTER:
                case EmailQueue::EMAIL_TYPE_CHANGE_PASSWORD:
                case EmailQueue::EMAIL_TYPE_CHANGE_EMAIL:
                case EmailQueue::EMAIL_TYPE_BIND_EMAIL:
                    Yii::$app->emailQueue2->delay(5)
                        ->push(new EmailJob([
                            'id'       => $id,
                            'userType' => $type,
                            'pageData' => $pageData,
                        ]));
                    break;
                case EmailQueue::EMAIL_SEEKER_DELIVERY:
                    Yii::$app->emailQueue3->delay(5)
                        ->push(new EmailJob([
                            'id'       => $id,
                            'userType' => $type,
                            'pageData' => $pageData,
                        ]));
                    break;
                case EmailQueue::EMAIL_JOB_SUBSCRIBE:
                    //职位订阅的
                    Yii::$app->emailQueue4->delay(5)
                        ->push(new EmailJob([
                            'id'       => $id,
                            'userType' => $type,
                            'pageData' => $pageData,
                        ]));
                    break;
                case EmailQueue::EMAIL_RESUME_INVITE_JOB_APPLY:
                    //职位订阅的
                    Yii::$app->emailQueue5->delay(5)
                        ->push(new EmailJob([
                            'id'       => $id,
                            'userType' => $type,
                            'pageData' => $pageData,
                        ]));
                    break;
                default:
                    Yii::$app->emailQueue->delay(5)
                        ->push(new EmailJob([
                            'id'       => $id,
                            'userType' => $type,
                            'pageData' => $pageData,
                        ]));
                    break;
            }
        } else {
            Yii::$app->emailQueue->delay(5)
                ->push(new EmailJob([
                    'id'       => $id,
                    'userType' => $type,
                    'pageData' => $pageData,
                ]));
        }

        return $id;
    }

    public static function showcaseBrowseLog($data)
    {
        Yii::$app->showcaseBrowseLogQueue->push(new ShowcaseBrowseLogJob([
            'data' => $data,
        ]));
    }

    public static function click($data)
    {
        // 不再记录蜘蛛的点击
        // $uaList = [
        //     'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.143 Safari/537.36',
        //     'Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko; compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm) Chrome/116.0.1938.76 Safari/537.36',
        // ];
        //
        // if (in_array($data['useragent'], $uaList)) {
        //     DebugHelper::click($data);
        //
        //     return;
        // } else {
        //     DebugHelper::click($data['useragent']);
        // }

        try {
            $type        = $data['type'];
            $memberId    = $data['memberId'];
            $mainId      = $data['mainId'];
            $source      = $data['source'];
            $ip          = $data['ip'];
            $useragent   = $data['useragent'];
            $userCookies = $data['userCookies'];
            //防止队列停止时候时间不对，所以将时间放到这里记录，执行时候直接写入这个时间
            $addTime = CUR_DATETIME;
            Yii::$app->clickQueue->push(new ClickJob([
                'type'        => $type,
                'memberId'    => $memberId,
                'mainId'      => $mainId,
                'source'      => $source,
                'ip'          => $ip,
                'useragent'   => $useragent,
                'userCookies' => $userCookies,
                'addTime'     => $addTime,
            ]));
        } catch (\Exception $e) {
            // Yii::error($e->getMessage());
            Yii::error($data['ip']);
        }
    }

    public static function memberSearch($data)
    {
        try {
            $type        = $data['type'];
            $memberId    = $data['memberId'];
            $keyword     = $data['keyword'];
            $ip          = $data['ip'];
            $useragent   = $data['useragent'];
            $userCookies = $data['userCookies'];
            $params      = $data['params'];
            $url         = $data['url'];
            Yii::$app->memberSearchQueue->push(new MemberSearchLogJob([
                'type'        => $type,
                'memberId'    => $memberId,
                'keyword'     => $keyword,
                'ip'          => $ip,
                'useragent'   => $useragent,
                'userCookies' => $userCookies,
                'params'      => $params,
                'url'         => $url,
            ]));
        } catch (\Exception $e) {
            Yii::error($data['ip']);
        }
    }

    /**
     * 这里完全交给任务去执行了,队列只做简单的承接
     */
    public static function adminDownloadTask($id)
    {
        try {
            // 这个任务的时间职位为1个小时
            //            Yii::$app->adminDownloadTaskQueue->ttr = 7200;
            //            Yii::$app->adminDownloadTaskQueue->push(new AdminDownloadTaskJob(['id' => $id]));
            Yii::$app->adminDownloadTaskNewQueue->ttr = 7200;
            Yii::$app->adminDownloadTaskNewQueue->push(new AdminDownloadTaskJob(['id' => $id]));
        } catch (\Exception $e) {
            Yii::error($e->getMessage());
        }
    }

    /**
     * 更新搜索引擎表统计
     * @param $data
     */
    public static function engineUpdateStatisticsTable($data)
    {
        Yii::$app->engineUpdateStatisticsTableQueue->push(new EngineUpdateStatisticsTable([
            'data' => $data,
        ]));
    }

    /**
     * 缓存人匹岗预热数据队列
     * @param $data
     */
    public static function personResumeIntentionMatchJob($resumeId, $pageMax = 20)
    {
        try {
            Yii::$app->personResumeIntentionMatchJobQueue->push(new PersonResumeIntentionMatchJob([
                'resume_id' => $resumeId,
                'page_max'  => $pageMax,
            ]));
        } catch (\Exception $e) {
            Yii::error($e->getMessage());
        }
    }

    /**
     * 缓存岗匹人预热数据队列
     * @param $data
     */
    public static function companyJobMatchPerson($companyId, $pageMax = 20)
    {
        return true;
        try {
            Yii::$app->companyJobMatchPersonQueue->ttr = 1000;
            Yii::$app->companyJobMatchPersonQueue->push(new CompanyJobMatchPerson([
                'companyId' => $companyId,
                'pageMax'   => $pageMax,
            ]));
        } catch (\Exception $e) {
            Yii::error($e->getMessage());
        }
    }

    /**
     * 公告新增/更新后的一些操作
     */
    public static function afterAnnouncementUpdateJob($id, $type = AfterAnnouncementUpdateJob::TYPE_ANNOUNCEMENT)
    {
        try {
            // 在这里做一个记录，这个公告正在执行更新，后面的就没有必要进来了
            // $key = Cache::ALL_ANNOUNCEMENT_AFTER_RUN_ID_KEY . ':' . $announcementId;
            //
            // if (Cache::get($key)) {
            //     return;
            // }

            Yii::$app->afterAnnouncementUpdateJobQueue->push(new AfterAnnouncementUpdateJob([
                'id'   => $id,
                'type' => $type,
            ]));

            // 就算没执行 ， 1个小时候也要处理了
            //            Cache::set($key, 3600);
        } catch (\Exception $e) {
            Yii::error($e->getMessage());
        }
    }

    /**
     *  PV统计---按日维度统计
     */
    public static function companyResumePvTotal($params)
    {
        try {
            Yii::$app->companyResumePvTotalQueue->push(new CompanyResumePvTotal([
                'resumeId' => $params['resumeId'],
                'date'     => $params['date'],
            ]));
        } catch (\Exception $e) {
            Yii::error($e->getMessage());
        }
    }

    public static function meilisearch($id, $type)
    {
        //开辟临时内存
        ini_set('memory_limit', '2048M');

        try {
            // 临时存到redis里面，后面一个一个取出来再保存
            // $cacheKey = 'TMP:MEILISEARH_RELOAD';
            //
            // $data     = json_encode([
            //     'id'   => $id,
            //     'type' => $type,
            // ]);
            //
            // Cache::lPush($cacheKey, $data);

            // 延时10s入库
            Yii::$app->meilisearchQueue->delay(10)
                ->push(new MeilisearchJob([
                    'mainId' => $id,
                    'type'   => $type,
                ]));
        } catch (\Exception $e) {
            Yii::error($e->getMessage());
        }
    }

    /**
     * 更新redis缓存队列
     * @param $id
     * @param $type
     * @return void
     */
    public static function redisUpdate($id, $type)
    {
        //        return true;
        try {
            // 延时10s入库
            Yii::$app->redisUpdateQueue->push(new RedisUpdate([
                'mainId' => $id,
                'type'   => $type,
            ]));
        } catch (\Exception $e) {
            Yii::error($e->getMessage());
        }
    }

    /**
     * 更新单位群组分值系统队列
     * @return void
     */
    public static function companyGroupScoreSystem($groupId)
    {
        try {
            Yii::$app->companyGroupScoreSystemQueue->push(new CompanyGroupScoreSystem(['group_id' => $groupId]));
        } catch (\Exception $e) {
            Yii::error($e->getMessage());
        }
    }

    /**
     * 更新单位群组分值系统队列
     * @return void
     */
    public static function hwActivityCompanyQueue($activityId)
    {
        try {
            Yii::$app->hwActivityCompanyQueue->push(new HwActivityCompany(['activityId' => $activityId]));
        } catch (\Exception $e) {
            Yii::error($e->getMessage());
        }
    }

}