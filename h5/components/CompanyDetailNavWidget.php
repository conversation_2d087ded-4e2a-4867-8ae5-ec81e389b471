<?php

namespace h5\components;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseCompany;
use common\base\models\BaseJob;
use h5\models\Company;
use yii\base\Widget;

class CompanyDetailNavWidget extends Widget
{
    public $companyId;
    public $isCooperation;
    public $info;

    public function init()
    {
        $this->info = Company::getCompanyDetailBannerInfo($this->companyId);

        parent::init();
    }

    public function run()
    {
        $detailUrl             = '/company/detail/' . $this->companyId . '.html';
        $detailJobUrl          = '/company/detail/' . $this->companyId . '_j.html';
        $detailAnnouncementUrl = '/company/detail/' . $this->companyId . '_a.html';
        $detailActivityUrl     = '/company/detail/' . $this->companyId . '_h.html';

        $isCooperation = BaseCompany::checkIsCooperation($this->companyId);

        //获取单位发布公告数量
        $onlineAnnouncementCount = BaseAnnouncement::getCompanyOnLineAnnouncementAmount($this->companyId);
        if ($onlineAnnouncementCount > 99) {
            $onlineAnnouncementCount = '99+';
        }

        //获取单位在招职位数量
        $onlineJobCount = BaseJob::getCompanyJobAmount($this->companyId);
        if ($onlineJobCount > 99) {
            $onlineJobCount = '99+';
        }

        $activityCount = BaseCompany::getActivityList(['companyId' => $this->companyId])['page']['count'];
        $activityCount = $activityCount > 99 ? '99+' : $activityCount;

        $action = \Yii::$app->controller->action->id;
        switch ($action) {
            case 'detail-announcement-list':
                $announcementActive = true;
                break;
            case 'detail-job-list':
                $jobActive = true;
                break;
            case 'detail-activity-list':
                $activityActive = true;
                break;
            case 'detail':
                $detailActive = true;
                break;
        }

        return $this->render('company_detail_nav.html', [
            'info'                    => $this->info,
            'detailUrl'               => $detailUrl,
            'detailJobUrl'            => $detailJobUrl,
            'detailAnnouncementUrl'   => $detailAnnouncementUrl,
            'detailActivityUrl'       => $detailActivityUrl,
            'isCooperation'           => $isCooperation,
            'onlineJobCount'          => $onlineJobCount,
            'activityCount'           => $activityCount,
            'onlineAnnouncementCount' => $onlineAnnouncementCount,
            'announcementActive'      => $announcementActive ?: '',
            'jobActive'               => $jobActive ?: '',
            'detailActive'            => $detailActive ?: '',
            'activityActive'          => $activityActive ?: false,
        ]);
    }

}