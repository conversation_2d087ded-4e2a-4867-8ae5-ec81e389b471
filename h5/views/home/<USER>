
    <link rel="stylesheet" href="/static/css/resultList.min.css?v=20240925">

    <header class="header-container record-header">
        <a href="javascript:history.back();" class="to-back"></a>
    
        <form id="searchForm" class="primary-search header-search" action="">
            <input type="search" name="keyword" placeholder="请输入关键词" value="<?=$keyword?>">
        </form>
    
        <div class="header-search-tab">
            <input id="searchFormType1" form="searchForm" type="radio" name="type" value="1" onchange="this.form.submit()" <?php if($type == 1):?>checked<?php endif?>>
            <label for="searchFormType1">搜公告</label>
    
            <input id="searchFormType2" form="searchForm" type="radio" name="type" value="2" onchange="this.form.submit()" <?php if($type == 2):?>checked<?php endif?>>
            <label for="searchFormType2">搜资讯</label>
        </div>
    
        <script>
            $(function () {
                $('#searchForm').on('submit', function () {
                    var formData = {}
                    var recordKey = ''
                    var recordValue = []
                    var recordLength = 10

                    $.each($(this).serializeArray(), function (index, item) {
                        formData[item.name] = item.value
                    })

                    recordKey = Object.keys(formData).includes('jobCategoryId') ? 'jobs' : (/^(1)$/.test(formData.type) ? 'announcements' : 'news')

                    if (formData['keyword']) {
                        recordValue = Array.from(new Set([formData['keyword'], ...getRecordStorage(recordKey)]))

                        if (recordValue.length > recordLength) {
                            recordValue.length = recordLength
                        }

                        setRecordStorage(recordKey, recordValue)
                    }
                })
            })
        </script>
    </header>

    <!-- 搜索无数据展示 -->
    <?php if($notData):?>
        <!-- 搜索无数据展示 -->
<!--       <div class="search-main noresult-container">-->
<!--            <span class="weui-loadmore__tips">-->
<!--                   <img src="/static/assets/home/<USER>" class="no-result">-->
<!--            </span>-->

<!--            <div class="search-hot">-->
<!--                <h4 class="search-label is-hot">热门搜索</h4>-->

<!--                <div class="search-value">-->
<!--                    <?php if($type == 1):?>-->
<!--                    <?php foreach($hotAnnouncementList as $item):?>-->
<!--                    <a href="<?=$item['url']?>" class="showcase-browse"-->
<!--                       data-showcase-number="<?php echo $item['number'] ?>"-->
<!--                       data-showcase-id="<?php echo $item['id'] ?>"><?=$item['title']?></a>-->
<!--                    <?php endforeach;?>-->
<!--                    <?php elseif($type == 2):?>-->
<!--                    <?php foreach($hotNewsList as $item):?>-->
<!--                    <a href="<?=$item['url']?>" class="showcase-browse"-->
<!--                       data-showcase-number="<?php echo $item['number'] ?>"-->
<!--                       data-showcase-id="<?php echo $item['id'] ?>"><?=$item['title']?></a>-->
<!--                    <?php endforeach;?>-->
<!--                    <?php endif?>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->



    <div class="search-main noresult-container">
        <span class="weui-loadmore__tips">
            <img src="/static/assets/home/<USER>" class="no-result">
        </span>

        <?php if($type == 1):?>
            <div class="search-hot">
                <h4 class="search-label">热搜公告</h4>

                <div class="search-value">
                    <?php foreach($hotAnnouncementList as $k => $item):?>
                    <a href="<?=$item['url']?>" class="recommend-job showcase-browse" data-showcase-number="<?php echo $item['number'] ?>" data-showcase-id="<?php echo $item['id'] ?>">
                    <span class="idx"><?=$k+1?></span>
                    <span class="txt <?php if($k <6):?>is-hot<?php endif?>"><?=$item['title']?></span>
                    </a>
                    <?php endforeach;?>
                </div>
            </div>
        <?php else:?>
            <div class="search-hot is-news">
                <h4 class="search-label">热搜资讯</h4>

                <div class="search-value">
                    <?php foreach($hotNewsList as $k => $item):?>
                    <a href="<?=$item['url']?>" class="recommend-job showcase-browse" data-showcase-number="<?php echo $item['number'] ?>" data-showcase-id="<?php echo $item['id'] ?>">
                    <span class="idx"><?=$k+1?></span>
                    <span class="txt <?php if($k <6):?>is-hot<?php endif?>"><?=$item['title']?></span>
                    </a>
                    <?php endforeach;?>

                </div>
            </div>
        <?php endif?>


<?php endif;?>


<!-- 搜索有数据展示 -->
    <?php if(!$notData):?>
    <div class="search-result">
        <div class="result-total">共检索到 <?=$totalAmount?> 条结果</div>

        <ul class="result-list">
            <?php foreach($list as $item):?>
            <li class="result-item  <?=$item['status']==1?'':' position-invalid' ?>">
                <a class="result-link" href="<?=$item['url']?>">
                    <h5 class="title"><?=$item['title']?></h5>
                    <?php if(!empty($item['highlightsDescribe'])):?>
                    <div class="praise"><?=$item['highlightsDescribe']?></div>
                    <?php endif?>
                    <div class="tips">
                        <div class="tag-view">
                            <?php if($type == 1):?>
                            <?php if($item['isEstablishment'] == 1):?><span class="label">编</span><?php endif?>
                            <span class="tag"><?=$item['jobAmount']?:0?>个职位</span>
                            <span class="tag"><?=$item['recruitAmount']?:0?>人</span>
                            <?php endif?>
                            <span class="view"><?=$item['clickAmount']?:0?></span>
                        </div>
                        <span class="datetime"><?=$item['refreshTime']?></span>
                    </div>
                </a>
            </li>
            <?php endforeach;?>
        </ul>

        <div class="weui-loadmore" style="display: none;">
            <i class="weui-loading"></i>
            <span class="weui-loadmore__tips">正在加载</span>
        </div>

        <div class="weui-loadmore weui-loadmore_line" style="display: none;">
            <span class="weui-loadmore__tips">暂无数据</span>
        </div>
    </div>

    <?php endif;?>


    <script>
        $(function () {
            // 如果数据小于等于一页 loading: true 触底阻止加载数据
            var loading = false
            var currentPage = 1

            var $container = $('.main-container')
            var $resultList = $('.result-list')
            var $loading = $('.weui-loadmore')

            function renderList(data) {
                return data.reduce((previous, current) => {
                    const { title, refreshTime, isEstablishment, jobAmount, recruitAmount, url, status, clickAmount, highlightsDescribe } = current
                    const labelText = isEstablishment == 1  ? '<span class="label">编</span>' : ''
                    const jobAmountText = jobAmount ? `<span class="tag">${jobAmount}个职位</span>` : ''
                    const recruitAmountText = recruitAmount ? `<span class="tag">${recruitAmount}人</span>` : ''
                    const noticeStatus = status == 2 ? ' position-invalid' : ''
                    return (previous += `
                    <li class="result-item ${noticeStatus}">
                        <a class="result-link" href="${url}" >
                            <h5 class="title">${title}</h5>
                            ${highlightsDescribe ? '<div class="praise">' + highlightsDescribe + '</div>' : ''}
                            <div class="tips">
                                <div class="tag-view">
                                    ${labelText}
                                    ${jobAmountText}
                                    ${recruitAmountText}
                                    <span class="view">${clickAmount}</span>
                                </div>
                                <span class="datetime">${refreshTime}</span>
                            </div>
                        </a>
                    </li>
                `)
                }, '')
            }

            function fetchData() {
                // 需要后端判断是否是公告
                var isNotice = <?= $type?> === 1
                var api = isNotice ? '/home/<USER>' : '/home/<USER>'
                // 需要后端渲染搜索关键词
                var params = { page: currentPage + 1, keyword: "<?= $keyword ?: ''?>" }

                loading = true
                $loading.hide()
                $loading.eq(0).show()

                httpGet(api, params).then(data => {
                    $loading.hide()
                    if (data.length) {
                        $resultList.append(renderList(data))
                    } else {
                        $loading.eq(1).show()
                    }

                    loading = data.length === 0
                    currentPage += 1
                })
            }

            $container.infinite().on('infinite', function () {
                if (loading) return
                fetchData()
            })
        })
    </script>