<link rel="stylesheet" href="/static/css/signup.min.css?t=20240411">
<script src="/static/js/validation.js"></script>
<script>
    if (isMiniapp()) {
        // 直接去的小程序的登录页 packages/person/login
        redirectToMiniApp('/packages/person/login')
    }
</script>

<div class="sign-container is-special">
    <div class="sign-header">
        <a href="/"><img class="logo" src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_1.png"></a>
        <h3 class="header-tips">高校人才网欢迎您</h3>
    </div>

    <div class="form-container signup">
        <div class="weui-cell mobile-cell">
            <!-- 号段 -->
            <div class="weui-cell__hd mobile-code">+86</div>

            <ul class="mobile-code-select">
                <li class="item">
                    <div>中国大陆</div>
                    <div>+86</div>
                </li>
                <li class="item">
                    <div>非中国大陆手机号</div>
                </li>
            </ul>

            <div class="weui-cell__bd">
                <input class="weui-input" pattern="[0-9]*" placeholder="请输入手机号" type="number" id="mobile"
                       maxlength="11">
            </div>

            <i class="weui-icon-clear" style="display: none;"></i>

            <div class="weui-cell__ft">
                <button id="getCodeBtn" class="weui-vcode-btn">获取验证码</button>
            </div>
        </div>

        <div class="weui-cell">
            <div class="weui-cell__bd">
                <input class="weui-input" placeholder="请输入验证码" type="number" id="mobildCode" maxlength="4" pattern="[0-9]*" autocomplete="one-time-code">
            </div>

            <i class="weui-icon-clear" style="display: none;"></i>
        </div>

        <div class="form-button">
            <button class="weui-btn weui-btn_primary" id="loginBtn">登录/注册</button>
        </div>

        <div class="account-login">
            <a href="/login">账号密码登录</a>
        </div>
    </div>

    <div class="account-tips">
        <label for="agreement">
            <input id="agreement" type="checkbox" class="checkbox-btn">
            <span class="checkbox-icon"></span>
            首次登录将自动注册，即代表同意
        </label>
        <span>
            <a href="<?=$serviceAgreementUrl?>">用户协议</a>和<a href="<?=$privacyPolicyUrl?>">隐私条款</a>
        </span>
    </div>
</div>

<script>
    $(function () {
        var search = document.location.search
        var captchaAppId = ''
        var redirect = (function () {
            var path = window.localStorage.getItem('redirect')
            if (/redirect=([^&]+)/.test(search)) {
                return decodeURIComponent(RegExp.$1)
            }
            if (path) {
                return path
            }
            return '/'
        })()

        httpGet('/config/get-captcha').then(function (data) {
            captchaAppId = data.captchaAppId
        })


        window.localStorage.setItem('redirect', redirect)

        var countdownTime = 60
        var $getCodeBtn = $('#getCodeBtn')
        var $container = $('.container')
        var $mobileCode = $('.mobile-code')
        var $mobileCodeItem = $('.mobile-code-select .item')
        var hasSend = false
        var countdown = ''

        function sendCodeSuccess() {
            countdownTime--
            $getCodeBtn.html("<span>重新发送 </span>(" + countdownTime + "s)")
            $getCodeBtn.attr('disabled', true)
            $('#mobile').attr('disabled', true)
            $('#mobildCode').focus()
            hasSend = true
            countdown = setInterval(function () {
                if (countdownTime == 1) {
                    clearInterval(countdown)
                    $('#mobile').removeAttr('disabled')
                    $getCodeBtn.removeAttr('disabled')
                    $getCodeBtn.text("获取验证码")
                    countdownTime = 60
                    return
                }
                countdownTime--
                $getCodeBtn.html("<span>重新发送 </span>(" + countdownTime + "s)")
            }, 1000)
        }

        //  开始检查发送验证码
        function sendCode() {

            let mobileDom = $('#mobile')
            let mobile = mobileDom.val()
            if (!mobile) {
                $.toast('手机号不能为空', 'text')
                return
            }
            if (!isMobile(mobile)) {
                $.toast('手机号码格式错误', 'text')
                return
            }

            handleCaptcha(function (data) {
                httpPost('/home/<USER>', {mobile, mobileCode: '+86', ...data}).then(r => {
                    sendCodeSuccess()
                }).catch(r => {
                })
            })

            // httpPost('/home/<USER>', {mobile, mobileCode: '+86'}).then(r => {
            //     sendCodeSuccess()
            // }).catch(r => {
            // })
        }

        function handleCaptcha(callback) {
            try {
                this.captcha = new TencentCaptcha(captchaAppId, (res) => {
                    const { ret, ticket, randstr } = res
                    if (ret === 0) {
                        callback && callback({ ticket, randstr })
                    }
                })
                this.captcha.show()
            } catch (err) {
                console.log(err)
            }
        }

        // 发送验证码按钮
        $getCodeBtn.on('click', function () {
            sendCode()
        })

        $mobileCode.on('click', function (e) {
            $('.mobile-code-select').show()
            e.stopPropagation()
        })
        $container.on('click', function () {
            $('.mobile-code-select').hide()
        })
        $mobileCodeItem.on('click', function () {
            $('.mobile-code-select').hide()
            const index = $(this).index()
            // 非大陆号段
            if (index === 1) {
                window.signupPopup.showGuideMiniAppDialog()
            }
        })

        function checkAccount(data, callback = () => {}) {
            const { cancelStatus, token, msg } = data
            if (cancelStatus === 9) {
                $('body').find('#accountCancelDialog .content').html(msg)
                $('body').find('#accountCancelDialog .cancel').attr('data-token', token)
                $('body').find('#accountCancelDialog').fadeIn(100)
                return
            }
            if (cancelStatus === 10) {
                toastText(msg, 3000)
                return
            }
            callback && callback()
        }

        function handleLogin() {
            let mobileDom = $('#mobile')
            let mobileCodeDom = $('#mobildCode')
            const isAgreement = $('.checkbox-btn').prop('checked')
            let mobile = mobileDom.val()
            let code = mobileCodeDom.val()

            if (!mobile) {
                $.toast('手机号不能为空', 'text')
                return
            }
            if (!isMobile(mobile)) {
                $.toast('手机号码格式错误', 'text')
                return
            }
            // 发送登录请求
            if (!hasSend) {
                $.toast('请先发送验证码', 'text')
                return
            }
            if (!code) {
                $.toast('验证码不能为空', 'text')
                return
            }
            if (!isAgreement) {
                window.signupPopup.showAgreementDialog({
                    confirmCallback: function () {
                        $('.checkbox-btn').prop('checked', true)
                        handleLogin()
                    }
                })
                return
            }

            httpPost('/home/<USER>', {
                mobile,
                mobileCode: '+86',
                code
            })
                .then((r) => {
                    checkAccount(r, () => {
                        var redirect = window.localStorage.getItem('redirect')
                        window.localStorage.removeItem('redirect')
                        // 未完善简历信息
                        if (r.status === 9) {
                            window.location.href = redirect || '/resume/index'
                        }
                        // 已完善简历信息
                        if (r.status === 1) {
                            window.location.href = redirect || '/'
                        }
                    })
                })
                .catch((r) => {})
        }

        // login
        $('#loginBtn').on('click', function () {
            handleLogin()
        })

        httpGet('/config/get-private').then(function (data) {
            const {private, privateTitle, privateMessage} = data

            if (private) {
                $.alert(privateMessage, privateTitle)
            }
        })
    })
</script>

