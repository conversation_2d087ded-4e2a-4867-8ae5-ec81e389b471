<link rel="stylesheet" href="/static/css/reset.min.css">
<script src="/static/js/validation.js"></script>

<div class="sign-container is-special">
    <div class="sign-header">
        <h3>验证通过后可修改登录密码</h3>
    </div>

    <div class="form-container">
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <input class="weui-input" placeholder="请输入注册手机号或邮箱" type="text" id="keyword">
            </div>

            <i class="weui-icon-clear" style="display: none;"></i>
        </div>

        <div class="weui-cell">
            <div class="weui-cell__bd">
                <input class="weui-input" pattern="[0-9]*" placeholder="请输入验证码" type="text" maxlength="4"
                       id="code">
            </div>

            <i class="weui-icon-clear" style="display: none;"></i>

            <div class="weui-cell__ft">
                <button id="getCodeBtn" class="weui-vcode-btn">获取验证码</button>
            </div>
        </div>

        <div class="weui-cell">
            <div class="weui-cell__bd">
                <input class="weui-input" placeholder="请输入密码/6-26位大小写字母、数字组合密码" type="text" id="password">
            </div>
            <i class="weui-icon-clear" style="display: none;"></i>
            <i class="weui-icon-password is-show"></i>
        </div>

        <div class="form-button">
            <button class="submit-button weui-btn weui-btn_primary">确定</button>
        </div>
    </div>
</div>

<script>
    $(function () {
        var countdownTime = 60
        var $getCodeBtn = $('#getCodeBtn')
        var hasSend = false
        var countdown = ''
        var type = 0

        function sendCodeSuccess() {
            countdownTime--
            $getCodeBtn.html("<span>重新发送 </span>(" + countdownTime + "s)")
            $getCodeBtn.attr('disabled', true)
            $('#keyword').attr('disabled', true)
            hasSend = true
            countdown = setInterval(function () {
                if (countdownTime == 1) {
                    clearInterval(countdown)
                    $('#keyword').removeAttr('disabled')
                    $getCodeBtn.removeAttr('disabled')
                    $getCodeBtn.text("获取验证码")
                    countdownTime = 60
                    return
                }
                countdownTime--
                $getCodeBtn.html("<span>重新发送 </span>(" + countdownTime + "s)")
            }, 1000)
        }

        /**
         * 开始检查发送验证码
         */
        function sendCode() {
            let keywordDom = $('#keyword')
            let keyword = keywordDom.val()
            let data = {}
            if (!keyword) {
                $.toast('手机号或邮箱不能为空', 'text')
                keywordDom.focus();
                return
            }
            if (!isMobile(keyword)) {
                if (!isEmail(keyword)) {
                    // 必须是手机或邮箱
                    $.toast('输入的必须是手机号或邮箱', 'text')
                    keywordDom.focus();
                    return
                }
                data = { email: keyword }
                type = 2
            } else {
                // 手机号
                data = { mobile: keyword };
                type = 1
            }


            httpPost('/home/<USER>', data).then(r => {
                sendCodeSuccess()
            }).catch(r => {
                return
            })
        }

        $getCodeBtn.on('click', function () {
            sendCode()
        })

        function checkAccount(data, callback = () => {}) {
            const { cancelStatus, token, msg } = data
            if (cancelStatus === 9) {
                $('body').find('#accountCancelDialog .content').html(msg)
                $('body').find('#accountCancelDialog .cancel').attr('data-token', token)
                $('body').find('#accountCancelDialog').fadeIn(100)
                return
            }
            if (cancelStatus === 10) {
                toastText(msg, 3000)
                return
            }
            callback && callback()
        }

        $('.submit-button').on('click', function () {
            let keywordDom = $('#keyword')
            let passwordDom = $('#password')
            let codeDom = $('#code')
            let keyword = keywordDom.val()
            let password = passwordDom.val()
            let code = codeDom.val()
            let data = {
                password, code
            }
            if (!hasSend) {
                $.toast('请先发送验证码', 'text')
                return
            }
            if (!isPassword(password)) {
                $.toast('密码格式错误', 'text')
                passwordDom.focus();
                return
            }
            if (!code) {
                $.toast('请输入验证码', 'text')
                codeDom.focus();
                return
            }

            if (!keyword) {
                $.toast('手机号或邮箱不能为空', 'text')
                keywordDom.focus();
                return
            }

            if (!isMobile(keyword)) {
                if (!isEmail(keyword)) {
                    // 必须是手机或邮箱
                    $.toast('输入的必须是手机号或邮箱', 'text')
                    keywordDom.focus();
                    return
                }
                data.email = keyword
            } else {
                // 手机号
                data.mobile = keyword
            }


            httpPost('/home/<USER>', data).then(r => {
                checkAccount(r, () => {
                    window.location.href = '/code-login'
                })
            }).catch(r => {
                return
            })
        })
    })
</script>