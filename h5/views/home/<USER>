<link rel="stylesheet" href="/static/css/login.min.css">

<div class="sign-container">
    <div class="sign-header">
        <a href="/"><img class="logo" src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_1.png"></a>
        <h3 class="header-tips">高校人才网欢迎您</h3>
    </div>

    <div class="form-container">
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <input class="weui-input" id="account" placeholder="请输入用户名/手机号/邮箱" type="text">
            </div>
            <i class="weui-icon-clear" style="display: none;"></i>
        </div>

        <div class="weui-cell">
            <div class="weui-cell__bd">
                <input class="weui-input" id="password" placeholder="请输入密码" type="password">
            </div>
            <i class="weui-icon-clear" style="display: none;"></i>
            <i class="weui-icon-password"></i>
        </div>

        <div class="form-button">
            <button class="login-button weui-btn weui-btn_primary">登录</button>
        </div>

        <div class="other-button">
            <a href="/code-login">手机号登录/注册</a>
            <a href="/reset-password">忘记密码</a>
        </div>
        <script>
            $(function () {
                var search = document.location.search
                var redirect = (function () {
                    var path = window.localStorage.getItem('redirect')
                    if (/redirect=([^&]+)/.test(search)) {
                        return decodeURIComponent(RegExp.$1)
                    }
                    if (path) {
                        return path
                    }
                    return '/'
                })()

                window.localStorage.setItem('redirect', redirect)

                function checkAccount(data, callback = () => {}) {
                    const { cancelStatus, token, msg } = data
                    if (cancelStatus === 9) {
                        $('body').find('#accountCancelDialog .content').html(msg)
                        $('body').find('#accountCancelDialog .cancel').attr('data-token', token)
                        $('body').find('#accountCancelDialog').fadeIn(100)
                        return
                    }
                    if (cancelStatus === 10) {
                        toastText(msg, 3000)
                        return
                    }
                    callback && callback()
                }

                $('.login-button').on('click', function () {
                    // 开始检查逻辑
                    let account = $('#account')
                    let password = $('#password')
                    let accountVal = $('#account').val()
                    let passwordVal = $('#password').val()

                    if (!accountVal) {
                        $.toast("请输入用户名/手机号/邮箱", 'text')
                        account.focus()
                        return
                    }

                    if (!passwordVal) {
                        $.toast("请输入密码", 'text')
                        password.focus()
                        return
                    }

                    httpPost('/home/<USER>', {account: accountVal, password: passwordVal}).then(r => {
                        checkAccount(r, () => {
                            // 未完善简历信息
                            if (r.status === 9) {
                                window.location.href = '/resume/index'
                            }
                            // 已完善简历信息
                            if (r.status === 1) {
                                window.localStorage.removeItem('redirect')
                                window.location.href = redirect
                            }
                        })
                    }).catch(r => {
                    })
                })

                httpGet('/config/get-private').then(function (data) {
                    const {private, privateTitle, privateMessage} = data

                    if (private) {
                        $.alert(privateMessage, privateTitle)
                        // $.alert(,'西安站现场招聘会延期举办通知')
                    }
                })
            })
        </script>