<link rel="stylesheet" href="/static/css/companyDetail.min.css?v=20241009" />
<script src="//img.gaoxiaojob.com/uploads/h5Static/lib/previewImage/previewImage.min.js"></script>


<?= h5\components\CompanyDetailBannerWidget::widget(['companyId'=>$info['companyId']]) ?>

<div class="content">
    <?= h5\components\CompanyDetailNavWidget::widget(['companyId'=>$info['companyId']]) ?>


<div class="tab-content search-container job is-active">
    <div class="select-nav">
        <div class="item">
            <span id="positionArea">地区</span>
            <span id="positionJob">职位类型</span>
            <span id="majorId">专业</span>
        </div>
        <span class="clear-btn">清除筛选</span>
    </div>
    <ul class="position-style">
        <?php foreach($info['jobList'] as $job):?>
        <li class="element <?php if($job['status'] == 0):?>position-invalid<?php endif?>">
            <a href="<?=$job['url']?>">
                <div class="position">
                    <span class="job-name"><?=$job['jobName']?></span>
                    <span class="pay"><?=$job['wage']?></span>
                </div>
                <div class="article"><?=$job['announcementName']?></div>
                <div class="required">
                    <?php if($job['education']):?><span><?=$job['education']?></span><?php endif;?>
                    <?php if($job['amount']):?><span><?=$job['amount']?>人</span><?php endif;?>
                    <?php if($job['experience']):?><span><?=$job['experience']?></span><?php endif;?>
                    <span><?=$job['city']?></span>
                </div>
                <div class="time"><?=$job['refreshTime']?>发布</div>
            </a>
        </li>
        <?php endforeach?>
    </ul>
    <div class="nothing" style="display: <?=$info['jobList']?'none':''?>;">
        <img src="/static/assets/layout/nothing.png" alt="" />
        <p>暂无相关内容</p>
    </div>
</div>

<div class="weui-loadmore" style="display: none">
    <i class="weui-loading"></i>
    <span class="weui-loadmore__tips">正在加载</span>
</div>

<!--<div class="weui-loadmore weui-loadmore_line" style="display: none">-->
<!--    <span class="weui-loadmore__tips">暂无数据</span>-->
<!--</div>-->
</div>

<script>
$(function () {
    /* userStatus: 0 -> 未登录; 1 -> 未完成简历前三步; 2 -> 可以投递 */
    var userStatus = <?=$info['userStatus'] ?>;
    var companyId = <?=$info['companyId']?>;

    function openSignupPopup() {
        window.signupPopup.show()
    }

    var $followBtn = $('.follow')

    var $nav = $('nav')
    var $content = $('.content')

    var $selectBtn = $('.select-nav span')
    var $tabContent = $('.tab-content')
    var $clearBtn = $('.clear-btn')

    var $header = $('.header-container')
    var $container = $('.main-container')
    var $loading = $('.weui-loadmore')
    var loading = false
    var $positionList = $('.position-style')
    var $nothing = $('.nothing')

    var contentMarginTop = $content.offset().top - $header.offset().height

    var positionAreaSelector = new MobileSelect({
        trigger: '#positionArea',
        title: '地区',
        wheels: [
            {
                data: [
                    {
                        id: '',
                        value: '全部'
                    },
                    <?php foreach($info['jobCityList'] as $k=>$city):?>
                    {
                        id: '<?= $city["k"]?>',
                            value: '<?= $city["v"]?>',
                    },
                    <?php endforeach;?>
                ]
            }
        ],
        callback: function (index, data) {
            jobQuery.areaId = data.pop().id
            refetchData()
        }
    })

    var positionJobSelector = new MobileSelect({
        trigger: '#positionJob',
        title: '职业类型',
        wheels: [
            {
                data: [
                    {
                        id: '',
                        value: '全部'
                    },
                    <?php foreach($info['jobCategoryList'] as $jobCategory):?>
                    {
                        id: '<?= $jobCategory["k"]?>',
                            value: '<?= $jobCategory["v"]?>',
                    },
                    <?php endforeach;?>
                ]
            }
        ],
        callback: function (index, data) {
            jobQuery.jobCategoryId = data.pop().id
            refetchData()
        }
    })

    var majorIdSelector = new MobileSelect({
        trigger: '#majorId',
        title: '专业',
        wheels: [
            {
                data: [
                    {
                        id: '',
                        value: '全部'
                    },
                    <?php foreach($info['majorList'] as $major):?>
                    {
                        id: '<?= $major["k"]?>',
                            value: '<?= $major["v"]?>',
                    },
                    <?php endforeach;?>
                ]
            }
        ],
        callback: function (index, data) {
            jobQuery.majorId = data.pop().id
            refetchData()
        }
    })

    // 职位
    var jobQuery = {
        areaId: '',
        jobCategoryId: '',
        majorId: '',
        page: 1
    }

    function resetText(txt) {
        var indexLength = this.curIndexArr.length
        this.trigger.innerText = txt

        if (indexLength > 1) {
            this.checkRange(0, [])
        } else {
            this.setCurDistance(this.resetPosition(0, []))
        }
    }

    var defaultText = [
        {
            positionArea: function () {
                resetText.call(positionAreaSelector, '地区')
            },
            positionJob: function () {
                resetText.call(positionJobSelector, '职位类型')
            },
            majorId: function () {
                resetText.call(majorIdSelector, '专业')
            }
        }
    ]

    // tab栏吸顶 start
    $container.on('scroll', function () {
        var isFixed = $content.offset().top - $nav.height() <= 15

        if (isFixed) {
            $nav.addClass('nav-fixed')
        } else {
            $nav.removeClass('nav-fixed')
        }
    })
    // tab栏吸顶 end

    function renderList(data) {
        return data.reduce((previous, current) => {
            const { jobName, experience, education, amount, city, wage, url, announcementName, status, releaseTime } = current

            const jobStatus = status == 0 ? ' position-invalid' : ''
            const announcementText = announcementName ? `<div class="article">${announcementName}</div>` : ''
            const educationText = education ? `<span>${education}</span>` : ''
            const amountText = amount ? `<span>${amount}人</span>` : ''
            const experienceText = experience ? `<span>${experience}</span>` : ''
            const cityText = city ? `<span>${city}</span>` : ''

            return (previous += `
                    <li class="element${jobStatus}">
                        <a href="${url}">
                            <div class="position">
                                <span class="job-name">${jobName}</span>
                                <span class="pay">${wage}</span>
                            </div>
                            ${announcementText}
                            <div class="required">
                                ${educationText}
                                ${amountText}
                                ${experienceText}
                                ${cityText}
                            </div>
                            <div class="time">${releaseTime}发布</div>
                        </a>
                    </li>
                `)
        }, '')
    }

    function fetchData(status) {
        var isReplace = status === true
        var operate = isReplace ? 'html' : 'append'

        var query = jobQuery
        var api = '/company/get-job-list'
        var params = { companyId, ...query, page: isReplace ? query.page : query.page + 1 }

        loading = true
        $loading.hide()
        $loading.eq(0).show()
        httpGet(api, params).then((data) => {
            $loading.hide()
            if (data.length) {
                $positionList.eq(0)[operate](renderList(data))
                jobQuery.page += 1
            } else {
                $positionList.eq(0)[operate]('')
                // $loading.eq(1).show()
            }
            loading = data.length === 0
            if ($positionList.eq(0).children().length < 1 && status) {
                $nothing.show()
            } else {
                $nothing.hide()
            }
        })
    }

    function refetchData() {
        loading = false
        jobQuery.page = 1
        fetchData(true)
    }

    /* 触底加载 */
    $container.infinite().on('infinite', function () {
        if (loading) return

        fetchData()
    })

    /* 点击关注按钮 */
    $followBtn.on('click', function () {
        if (userStatus === 0) {
            openSignupPopup()
            return
        }

        var isActive = $(this).hasClass('is-follow')
        httpPost('/company/collect', { companyId }).then(() => {
            $(this).toggleClass('is-follow')
        })
    })

    // 清除筛选项 start
    $clearBtn.on('click', function () {
        var trigger = defaultText[0]

        Object.keys(trigger).forEach(function (item) {
            trigger[item]()
        })

        for (var item in jobQuery) {
            jobQuery[item] = ''
        }

        refetchData()
    })
    // 清除筛选项 end
})
</script>

