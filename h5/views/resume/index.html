<link rel="stylesheet" href="/static/css/required.min.css?v=1.0.9">
<script src="/static/js/validation.js"></script>

<div class="primary-container">请完善个人信息，以便招聘方更好地了解您！</div>

<form id="required" class="required-form">
    <div class="basic page-bd-15">
        <div class="required-subtitle is-required">基本信息</div>

        <div class="weui-cell">
            <div class="weui-cell__hd">
                <label class="weui-label">姓名</label>
            </div>
            <div class="weui-cell__bd">
                <input class="weui-input" name="name" placeholder="请输入姓名"  autocomplete="off" value="<?=$userInfo['name']?:''?>">
            </div>
        </div>

        <div class="weui-cell weui-cell_select">
            <div class="weui-cell__hd">
                <label class="weui-label">性别</label>
            </div>
            <div class="weui-cell__bd">
                <input class="weui-input weui-input__select" id="gender" name="gender" type="text" value="<?=$userInfo['genderText']?:''?>"
                       readonly>
            </div>
        </div>

        <div class="weui-cell weui-cell_select">
            <div class="weui-cell__hd">
                <label class="weui-label">出生日期</label>
            </div>
            <div class="weui-cell__bd">
                <input class="weui-input weui-input__select" id="birthday" name="birthday" type="text" value="<?=$userInfo['birthday']?:''?>"
                       readonly placeholder="请选择出生日期">
            </div>
        </div>

        <div class="weui-cell weui-cell_select">
            <div class="weui-cell__hd">
                <label class="weui-label">户籍/国籍</label>
            </div>
            <div class="weui-cell__bd">
                <input class="weui-input weui-input__select open-popup" data-target="#householdPopup" name="householdRegisterId" readonly value="<?=$userInfo['householdRegisterText']?:''?>" data-values="<?=$userInfo['householdRegisterId']?:''?>" placeholder="请选择户籍/国籍">
            </div>
        </div>

        <div class="weui-cell weui-cell_select">
            <div class="weui-cell__hd">
                <label class="weui-label">政治面貌</label>
            </div>
            <div class="weui-cell__bd">
                <input class="weui-input weui-input__select" id="politicalStatusId" name="politicalStatusId"
                       type="text" value="<?=$userInfo['politicalStatusText']?:''?>" readonly placeholder="请选择政治面貌">
            </div>
        </div>

        <div class="weui-cell">
            <div class="weui-cell__hd">
                <label class="weui-label">手机号码</label>
            </div>
            <div class="weui-cell__bd">
                <input class="weui-input" name="mobile" value="<?=$userInfo['mobile']?:''?>" readonly>
            </div>
        </div>

        <div class="weui-cell">
            <div class="weui-cell__hd">
                <label class="weui-label">联系邮箱</label>
            </div>
            <div class="weui-cell__bd">
                <input class="weui-input" name="email" value="<?=$userInfo['email']?:''?>" placeholder="方便用人单位联系您" autocomplete="off">
            </div>
        </div>

        <div class="weui-cell identity-type">
            <div class="weui-cell__hd">
                <label class="weui-label">我的身份</label>
            </div>
            <div class="weui-cell__bd">
                <div class="weui-cell weui-cells_checkbox">
                    <label class="weui-cell weui-check__label">
                        <div class="weui-cell__hd">
                            <input class="weui-check" name="identityType" value="1" type="radio" placeholder="请选择您的身份"  <?php if($userInfo['identityType'] == 1):?>checked="checked"<?php endif;?>/>
                            <i class="weui-icon-checked"></i>
                        </div>
                        <div class="weui-cell__bd weui-form-text">
                            <p>职场人</p>
                        </div>
                    </label>
                    <label class="weui-cell weui-check__label">
                        <div class="weui-cell__hd">
                            <input class="weui-check" name="identityType" value="2" type="radio" placeholder="请选择您的身份" <?php if($userInfo['identityType'] == 2):?>checked="checked"<?php endif;?>/>
                            <i class="weui-icon-checked"></i>
                        </div>
                        <div class="weui-cell__bd weui-form-text">
                            <p>应届生/在校生</p>
                        </div>
                    </label>
                </div>
            </div>
        </div>

        <div class="weui-cell work-date">
            <div class="weui-cell__hd">
                <label class="weui-label">参加工作时间</label>
            </div>
            <div class="weui-cell__bd">
                <input class="weui-input" id="beginWorkDate" name="beginWorkDate" data-values="<?= $userInfo['beginWorkDate']?>" value="<?= $userInfo['beginWorkDateText']?:$userInfo['beginWorkDate']?>" placeholder="请选择参加工作时间" readonly />
            </div>
        </div>
    </div>

    <div class="education page-bd-15">
        <div class="required-subtitle">最高教育经历</div>

        <div class="weui-cell">
            <div class="weui-cell__hd">
                <label class="weui-label is-required">学校名称</label>
            </div>
            <div class="weui-cell__bd">
                <input class="weui-input" name="school" placeholder="请输入学校名称" value="<?= $educationInfo['school']?:''?>" autocomplete="off">
            </div>
        </div>

        <div class="weui-cell weui-cells_checkbox">
            <label class="weui-cell weui-check__label">
                <div class="weui-cell__hd">
                    <input class="weui-form-checkbox" name="isRecruitment" <?php if($educationInfo['isRecruitment']):?>checked="checked"<?php endif;?> type="checkbox">
                    <i class="weui-icon-checkbox"></i>
                </div>
                <div class="weui-cell__bd weui-form-text">
                    <p>统招</p>
                </div>
            </label>

            <label class="weui-cell weui-check__label">
                <div class="weui-cell__hd">
                    <input class="weui-form-checkbox" name="isProjectSchool" <?php if($educationInfo['isProjectSchool']):?>checked="checked"<?php endif;?> type="checkbox">
                    <i class="weui-icon-checkbox"></i>
                </div>
                <div class="weui-cell__bd weui-form-text">
                    <p>985/211</p>
                </div>
            </label>

            <label class="weui-cell weui-check__label">
                <div class="weui-cell__hd">
                    <input class="weui-form-checkbox" name="isOverseasStudy" <?php if($educationInfo['isOverseasStudy']):?>checked="checked"<?php endif;?> type="checkbox">
                    <i class="weui-icon-checkbox"></i>
                </div>
                <div class="weui-cell__bd weui-form-text">
                    <p>留学</p>
                </div>
            </label>
        </div>

        <div class="weui-cell college">
            <div class="weui-cell__hd">
                <label class="weui-label">二级院系（机构）名称</label>
            </div>
            <div class="weui-cell__bd">
                <input class="weui-input" name="college" placeholder="二级院系（机构）名称" autocomplete="off">
            </div>
        </div>

        <div class="weui-cell weui-cell_select range-date">
            <div class="weui-cell__hd">
                <label class="weui-label is-required">就读时间</label>
            </div>
            <div class="weui-cell__bd">
                <input class="weui-input weui-input__select" id="studyBeginDate" name="studyBeginDate"
                       type="text" value="<?= $educationInfo['studyBeginDate']?:''?>" readonly placeholder="入学时间">
                <span class="range-line"></span>
                <input class="weui-input weui-input__select" id="studyEndDate" name="studyEndDate" type="text"
                       value="<?= $educationInfo['studyEndDate']?:''?>" readonly placeholder="毕业时间">
            </div>
        </div>

        <div class="weui-cell weui-cell_select">
            <div class="weui-cell__hd">
                <label class="weui-label is-required">学历水平</label>
            </div>
            <div class="weui-cell__bd">
                <input class="weui-input weui-input__select" id="educationId" name="educationId" type="text"
                       value="<?= $educationInfo['educationText']?:''?>" readonly data-values="<?= $educationInfo['educationId']?:''?>">
            </div>
        </div>

        <div class="weui-cell weui-cell_select major-select">
            <div class="weui-cell__hd">
                <label class="weui-label is-required">所学专业</label>
            </div>
            <div class="weui-cell__bd">
                <input id="major" class="weui-input weui-input__select open-popup" data-target="#majorPopup" name="majorId" readonly value="<?= $educationInfo['majorText']?:''?>" readonly data-values="<?= $educationInfo['majorId']?:''?>" placeholder="请选择所学专业">
            </div>
        </div>

        <div class="weui-cell major-custom">
            <div class="weui-cell__hd">
                <label class="weui-label is-required">所学专业</label>
            </div>
            <div class="weui-cell__bd">
                <input class="weui-input" id="majorCustom"  name="majorCustom" type="text" value="<?= $educationInfo['majorCustom']?:''?>"  data-values="<?= $educationInfo['majorCustom']?:''?>" placeholder="请输入平台未收录专业" />
            </div>
        </div>

        <div class="weui-cell major-custom-operate">
            <div class="weui-cell__bd">
                <div class="weui-input">
                    <span class="label">若无您所需专业，请点击</span>
                    <a class="major-custom-btn" href="javascript:;">自定义录入专业</a>
                </div>
            </div>
        </div>

        <div class="weui-cell mentor-cell">
            <div class="weui-cell__hd">
                <label class="weui-label">导师</label>
            </div>
            <div class="weui-cell__bd">
                <input class="weui-input" name="mentor" type="text" value="<?= $educationInfo['mentor']?:''?>" maxlength="50" placeholder="请填写您的导师信息" />
            </div>
        </div>

        <div class="weui-cell research">
            <div class="weui-cell__hd">
                <label class="weui-label">研究方向</label>
            </div>
            <div class="weui-cell__bd">
                <textarea class="weui-textarea" name="researchDirection" rows="3"><?=$researchDirection?:''?></textarea>
            </div>
        </div>
    </div>

    <div class="intention page-bd-15">
        <div class="required-subtitle is-required">求职意向<span>(方便我们向您进行更精准的职位推荐)</span></div>

        <div class="weui-cell weui-cell_select">
            <div class="weui-cell__hd">
                <label class="weui-label">意向职位</label>
            </div>
            <div class="weui-cell__bd">
                <input id="jobCategory" class="weui-input weui-input__select open-popup" data-target="#jobCategoryPopup" name="jobCategoryId" readonly value="" readonly data-values="" placeholder="">
            </div>
        </div>

        <div class="weui-cell weui-cell_select">
            <div class="weui-cell__hd">
                <label class="weui-label">工作性质</label>
            </div>
            <div class="weui-cell__bd">
                <input class="weui-input weui-input__select" id="natureType" name="natureType" type="text"
                       value="" readonly>
            </div>
        </div>

        <div class="weui-cell weui-cell_select">
            <div class="weui-cell__hd">
                <label class="weui-label">意向城市</label>
            </div>
            <div class="weui-cell__bd">
                <input id="areaId" class="weui-input weui-input__select open-popup" data-target="#areaPopup" name="areaId" readonly value="" readonly data-values="" placeholder="">
            </div>
        </div>

        <div class="weui-cell weui-cell_select">
            <div class="weui-cell__hd">
                <label class="weui-label">期望月薪</label>
            </div>
            <div class="weui-cell__bd">
                <input class="weui-input weui-input__select" id="wageType" name="wageType" type="text"
                       value="" readonly>
            </div>
        </div>

        <div class="weui-cell weui-cell_select">
            <div class="weui-cell__hd">
                <label class="weui-label">求职状态</label>
            </div>
            <div class="weui-cell__bd">
                <input class="weui-input weui-input__select" id="workStatus" name="workStatus" type="text"
                       value="" readonly>
            </div>
        </div>

        <div class="weui-cell weui-cell_select">
            <div class="weui-cell__hd">
                <label class="weui-label">到岗时间</label>
            </div>
            <div class="weui-cell__bd">
                <input class="weui-input weui-input__select" id="arriveDateType" name="arriveDateType"
                       type="text" value="" readonly>
            </div>
        </div>
    </div>

    <div class="form-confirm">
        <button class="weui-btn weui-btn_primary" id="confirm"><i class="weui-loading"></i>提交</button>
    </div>
</form>

<script>
    $(function () {
        var search = document.location.search
        var redirect = (function () {
            var path = window.localStorage.getItem('redirect')
            if (/redirect=([^&]+)/.test(search)) {
                return decodeURIComponent(RegExp.$1)
            }
            if (path) {
                return path
            }
            return '/'
        })()

        window.localStorage.setItem('redirect', redirect)

        // 简历完善
        var $form = $('#required')
        var $confirm = $('#confirm')

        var $gender = $('#gender')
        var $birthday = $('#birthday')
        var $identityType = $('input[name="identityType"]')
        var $workDateCell = $('.work-date')
        var $beginWorkDate = $('#beginWorkDate')
        var $politicalStatusId = $('#politicalStatusId')

        var $studyBeginDate = $('#studyBeginDate')
        var $studyEndDate = $('#studyEndDate')
        var $educationId = $('#educationId')

        var $natureType = $('#natureType')
        var $wageType = $('#wageType')
        var $workStatus = $('#workStatus')
        var $arriveDateType = $('#arriveDateType')

        var showMentorEducationIds = ['3', '4']

        var genderSelector = $gender.select({
            closeText: '取消',
            items: [
                {title: '男', value: '1'},
                {title: '女', value: '2'}
            ]
        })

        var birthdayPicker = $birthday.picker({
            title: '出生日期',
            cols: pickerDefaultCols,
            value: $birthday.val() ? pickerCustomDateValue($birthday.val()) : birthdayYearMonth
        })

        var politicalSelector = $politicalStatusId.select({
            closeText: '取消',
            items: [
                <?php foreach($politicalList as $k=>$v):?>
                { title: '<?=$v["v"]?>', value: '<?=$v["k"]?>' },
                <?php endforeach;?>
            ]
        })

        var studyBeginDatePicker = $studyBeginDate.picker({
            title: '就读时间',
            cols: pickerDefaultCols,
            value: formatPickerValue($studyBeginDate.val())
        })

        var studyEndDatePicker = $studyEndDate.picker({
            title: '就读时间',
            cols: pickerEightYearLaterCols,
            value: formatPickerValue($studyEndDate.val())
        })

        var beginWorkDatePickerData = mobileSelectPicker({ endDate: new Date(), appendData: { id: '0000-00', value: '暂无工作经验' }, value: "<?= $userInfo['beginWorkDate']?>" })

        var beginWorkDatePicker = new MobileSelect({
            trigger: '#beginWorkDate',
            title: '参加工作时间',
            wheels: beginWorkDatePickerData.wheels,
            position: beginWorkDatePickerData.position,
            callback: function (position, values) {
                var valueObj = values.reduce(
                    function (prev, cur) {
                        var { value, label } = prev
                        value = value ? value + '-' : value
                        label = label ? label + ' - ' : label
                        return {
                            value: value + cur.id,
                            label: label + cur.value
                        }
                    },
                    { value: '', label: '' }
                )
                $beginWorkDate.val(valueObj.label)
                $beginWorkDate.attr('data-values', valueObj.value)
            }
        })

        // var isMajorCustom = "<?= $educationInfo['majorCustom']?:''?>" ? true : false
        // var educationId = "<?= $educationInfo['educationId']?:''?>"
        var isMajorCustom = false
        var educationId = ""

        function handleMajorCustomReview(isCustom) {
            if (isCustom) {
                $('.major-custom').show()
                $('.major-select').hide()
                $('.major-custom-operate .label').text('点击恢复')
                $('.major-custom-operate a').text('选择专业')
            } else {
                $('.major-custom').hide()
                $('.major-select').show()
                $('.major-custom-operate .label').text('若无您所需专业，请点击 ')
                $('.major-custom-operate a').text('自定义录入专业')
            }
        }

        function handleMajorCustomOperate(isCustom) {
            if (isCustom) {
                $('.major-custom-operate .label').text('点击恢复')
                $('.major-custom-operate a').text('选择专业')
                $('.major-select').hide()
                $('.major-custom').show()
            } else {
                $('.major-custom-operate .label').text('若无您所需专业，请点击 ')
                $('.major-custom-operate a').text('自定义录入专业')
                $('.major-select').show()
                $('.major-custom').hide()
            }
        }

        function handleEducationChage(educationId) {
            isShowMajorCustom(educationId)
            isShowMentor(educationId)
        }

        function isShowMajorCustom(educationId) {
            var showMajorCustomEducationIds = ['1', '2', '5']
            var isShow = showMajorCustomEducationIds.includes(educationId)

            if (isShow) {
                $('.major-custom-operate').show()
            } else {
                $('.major-custom-operate').hide()
                $('.major-custom').hide()
                $('.major-select').show()

                isMajorCustom = false
                handleMajorCustomReview(false)
            }
        }

        function isShowMentor(educationId) {
            var flag = showMentorEducationIds.includes(educationId)
            flag ? $('.mentor-cell').show() : $('.mentor-cell').hide()
        }

        var educationIdSelector = $educationId.select({
            closeText: '取消',
            items: [
                <?php foreach($educationList as $k=>$v):?>
                { title: '<?=$v["v"]?>', value: '<?=$v["k"]?>' },
                <?php endforeach;?>
            ],
            onChange(data) {
                handleEducationChage(data.values)
            }
        })

        var natureTypeSelector = $natureType.select({
            closeText: '取消',
            items: [
                <?php foreach($natureList as $k=>$v):?>
                { title: '<?=$v["v"]?>', value: '<?=$v["k"]?>' },
                <?php endforeach;?>
            ]
        })

        var wageTypeSelector = $wageType.select({
            closeText: '取消',
            items: [
                <?php foreach($wageList as $k=>$v):?>
                { title: '<?=$v["v"]?>', value: '<?=$v["k"]?>' },
                <?php endforeach;?>
            ]
        })

        var workStatusSelector = $workStatus.select({
            closeText: '取消',
            items: [
                <?php foreach($jobStatusList as $k=>$v):?>
                { title: '<?=$v["v"]?>', value: '<?=$v["k"]?>' },
                <?php endforeach;?>
            ]
        })

        var arriveDateTypeSelector = $arriveDateType.select({
            closeText: '取消',
            items: [
                <?php foreach($arriveDateList as $k=>$v):?>
                { title: '<?=$v["v"]?>', value: '<?=$v["k"]?>' },
                <?php endforeach;?>
            ]
        })

        handleEducationChage(educationId)
        handleMajorCustomReview(isMajorCustom)

        $('body').on('click', '.major-custom-btn', function () {
            isMajorCustom = !isMajorCustom
            handleMajorCustomOperate(isMajorCustom)
        })

        $identityType.on('change', function (e) {
            var val = $(this).val()
            val == 1 ? $workDateCell.removeClass('hide') : $workDateCell.addClass('hide')
        })

        $form.submit(function (e) {
            e.preventDefault()

            function toast(name, text) {
                if (name) {
                    var cur = $('input[name=' + name + ']')
                    var str = text ? text : cur.prop('placeholder')
                    var isReadonly = cur.prop('readonly')

                    $.toast(str, 'text')

                    if (isReadonly) {
                        cur.click()
                    } else {
                        cur.focus()
                    }
                }
            }

            var dataArray = $(this).serializeArray()
            var formData = {
                isRecruitment: 0,
                isProjectSchool: 0,
                isOverseasStudy: 0
            }

            dataArray.forEach(function (val) {
                var key = val.name
                var value = val.value

                if (key === 'gender') {
                    formData[key] = value === '男' ? 1 : 2
                    return
                }

                if (/isRecruitment|isProjectSchool|isOverseasStudy/.test(key)) {
                    formData[key] = $('input[name="' + key + '"]').prop('checked') ? 1 : 0
                    return
                }

                if (/educationId|natureType|politicalStatusId|wageType|workStatus|arriveDateType|householdRegisterId|majorId|jobCategoryId|areaId|beginWorkDate/.test(key)) {
                    formData[key] = $('input[name="' + key + '"]').attr('data-values')
                    return
                }

                if (/birthday|studyBeginDate|studyEndDate/.test(key)) {
                    formData[key] = trimSpace(value)
                    return
                }

                if (isMajorCustom && key === 'majorId') {
                    formData[key] = ''
                    return
                }

                if (!isMajorCustom && key === 'majorCustom') {
                    formData[key] = ''
                    return
                }

                if (!showMentorEducationIds.includes(formData.educationId) && key === 'mentor') {
                    formData[key] = ''
                    return
                }

                formData[key] = value
            })

            if (!formData.name) {
                toast('name')
                return
            }

            if (!formData.birthday) {
                toast('birthday')
                return
            }

            if (!formData.householdRegisterId) {
                toast('householdRegisterId')
                return
            }

            if (!formData.politicalStatusId) {
                toast('politicalStatusId')
                return
            }

            if (!formData.email) {
                toast('email', '请填写联系邮箱')
                return
            } else {
                if (!isEmail(formData.email)) {
                    toast('email', '请填写正确的邮箱')
                    return
                }
            }

            if (!formData.identityType) {
                toast('identityType')
                return
            }

            if (formData.identityType == 1 && !formData.beginWorkDate) {
                toast('beginWorkDate')
                return
            }

            if (!formData.school) {
                toast('school')
                return
            }

            if (!formData.studyBeginDate) {
                toast('studyBeginDate', '请选择入学时间')
                return
            }

            if (!formData.studyEndDate) {
                toast('studyEndDate', '请选择毕业时间')
                return
            }

            if (!formData.educationId) {
                toast('educationId', '请选择学历水平')
                return
            }

            if (isMajorCustom) {
                if (!formData.majorCustom) {
                    toast('majorCustom', '请输入所学专业')
                    return
                }
            } else {
                if (!formData.majorId) {
                    toast('majorId', '请选择所学专业')
                    return
                }
            }

            if (!formData.jobCategoryId) {
                toast('jobCategoryId', '请选择意向职位')
                return
            }

            if (!formData.natureType) {
                toast('natureType', '请选择工作性质')
                return
            }

            if (!formData.areaId) {
                toast('areaId', '请选择意向城市')
                return
            }

            if (!formData.wageType) {
                toast('wageType', '请选择期望月薪')
                return
            }

            if (!formData.workStatus) {
                toast('workStatus', '请选择求职状态')
                return
            }

            if (!formData.arriveDateType) {
                toast('arriveDateType', '请选择到岗时间')
                return
            }

            $confirm.prop('disabled', true)
            $confirm.addClass('weui-btn_loading')

            httpPost('/resume/save-info', formData).then(function (res) {
                backResumeEdit()
            }).catch(function () {
                $confirm.prop('disabled', false)
                $confirm.removeClass('weui-btn_loading')
            })
        })
    })
</script>

<!-- 户籍国籍弹窗 start -->
<div id="householdPopup" class="weui-popup__container filter-container">
    <div class="weui-popup__overlay"></div>
    <div class="weui-popup__modal">
        <form action="" class="filter-form" is-radio is-close-route>
            <div class="filter-header">
                <span>选择城市</span>
                <i class="close-button close-popup"></i>
            </div>

            <div class="filter-main is-primary is-household navigation-main">
                <ul class="navigation-nav">
                    <?php foreach($nativeCityAreaList as $item):?>
                    <li><span><?=$item["v"]?></span></li>
                    <?php endforeach;?>
                </ul>

                <div class="navigation-box">
                    <?php foreach($nativeCityAreaList as $item):?>
                    <ul class="navigation-panel is-area">
                        <li class="panel-title"><span><?=$item['v']?></span></li>
                        <li class="panel-links is-active filter-value">
                            <?php foreach($item['children'] as $value):?>
                            <label>
                                <input type="checkbox" name="householdId" value="<?=$value['k']?>">
                                <span><?=$value["v"]?></span>
                            </label>
                            <?php endforeach;?>
                        </li>
                    </ul>
                    <?php endforeach;?>
                </div>
            </div>
        </form>
    </div>
</div>
<!-- 户籍国籍弹窗 end -->

<!-- 所学专业弹窗 start -->
<div id="majorPopup" class="weui-popup__container filter-container">
    <div class="weui-popup__overlay"></div>
    <div class="weui-popup__modal">
        <form action="" class="filter-form" is-radio is-close-route>
            <div class="filter-header">
                <span>选择专业</span>
                <i class="close-button close-popup"></i>
            </div>

            <div class="filter-main is-primary is-major-third navigation-main">
                <ul class="navigation-nav">
                    <?php foreach($majorList as $k=>$item):?>
                    <li><span><?=$item["v"]?></span></li>
                    <?php endforeach;?>
                </ul>

                <div class="navigation-box">
                    <?php foreach($majorList as $k=>$item):?>
                    <ul class="navigation-panel">
                        <?php foreach($item['children'] as $key=>$item2):?>
                        <li class="panel-title"><span><?=$item2["v"]?></span></li>
                        <li class="panel-links filter-value">
                            <?php foreach($item2['children'] as $item3):?>
                            <label>
                                <input type="checkbox" name="major" value="<?=$item3['k']?>">
                                <span><?=$item3["v"]?></span>
                            </label>
                            <?php endforeach;?>
                        </li>
                        <?php endforeach;?>
                    </ul>
                    <?php endforeach;?>
                </div>
            </div>
        </form>
    </div>
</div>
<!-- 所学专业弹窗 end -->

<!-- 意向职位弹窗 start -->
<div id="jobCategoryPopup" class="weui-popup__container filter-container job-category-popup">
    <div class="weui-popup__overlay"></div>
    <div class="weui-popup__modal">
        <form action="" class="filter-form" is-radio is-close-route>
            <div class="filter-header">
                <span>选择职位</span>
                <i class="close-button close-popup"></i>
            </div>

            <div class="filter-main is-primary is-job-category navigation-main">
                <ul class="navigation-nav">
                    <?php foreach($jobCategoryList as $k=>$v):?>
                    <li><span><?=$v["v"]?></span></li>
                    <?php endforeach;?>
                </ul>

                <div class="navigation-box">
                    <?php foreach($jobCategoryList as $k=>$v):?>
                    <ul class="navigation-panel">
                        <li class="panel-title"><span><?=$v["v"]?></span></li>
                        <li class="panel-links filter-value">
                            <?php foreach($v['children'] as $key=>$val):?>
                            <label>
                                <input type="checkbox" name="jobCategory" value="<?=$val['k']?>">
                                <span><?=$val["v"]?></span>
                            </label>
                            <?php endforeach;?>
                        </li>
                    </ul>
                    <?php endforeach;?>
                </div>
            </div>
        </form>
    </div>
</div>
<!-- 意向职位弹窗 end -->

<!-- 意向城市弹窗 start -->
<div id="areaPopup" class="weui-popup__container filter-container">
    <div class="weui-popup__overlay"></div>
    <div class="weui-popup__modal">
        <form action="" class="filter-form" is-close-route >
            <div class="filter-header">
                <span>选择城市</span>
                <i class="close-button close-popup"></i>
            </div>

            <div class="filter-select">
                <div class="label">已选(<span>0</span>/5)</div>
                <div class="select-values"></div>
            </div>

            <div class="filter-main is-primary is-intention-of-city navigation-main">
                <ul class="navigation-nav">
                    <?php foreach($hierarchyCityList as $k=>$v):?>
                    <li><span><?=$v["v"]?></span></li>
                    <?php endforeach;?>
                </ul>

                <div class="navigation-box">
                    <?php foreach($hierarchyCityList as $k=>$v):?>
                    <ul class="navigation-panel">
                        <li class="panel-title"><span><?=$v["v"]?></span></li>
                        <li class="panel-links filter-value">
                            <?php foreach($v['children'] as $key=>$val):?>
                            <label>
                                <input type="checkbox" name="area" data-label="<?=$val['v']?>" value="<?=$val['k']?>">
                                <span><?=$val["v"]?></span>
                            </label>
                            <?php endforeach;?>
                        </li>
                    </ul>
                    <?php endforeach;?>
                </div>
            </div>

            <div class="filter-operate">
                <button class="weui-btn" type="reset">重置</button>
                <button class="weui-btn weui-btn_primary close-popup" type="submit">确定</button>
            </div>
        </form>
    </div>
</div>
<!-- 意向城市弹窗 end -->

<script>
    $(function () {
        // 户籍国籍
        var householdRegisterEl = $('input[name="householdRegisterId"]')
        var householdRegisterId = householdRegisterEl.attr('data-values')
        filterCascaderPopup(householdRegisterId ? [householdRegisterId] : [], '#householdPopup', '#householdPopup','householdId',function(data){
            var { value,label } = data
            householdRegisterEl.val(label)
            householdRegisterEl.attr('data-values', value)
        })

        // 所学专业
        var majorEl = $('input[name="majorId"]')
        var majorId = majorEl.attr('data-values')
        filterCascaderPopup(majorId ? [majorId] : [], '#majorPopup', '#majorPopup','major',function(data){
            var { value,label } = data
            majorEl.val(label)
            majorEl.attr('data-values', value)
        })

        // 意向职位
        var jobCategoryEl = $('input[name="jobCategoryId"]')
        var jobCategoryId = majorEl.attr('data-values')
        filterCascaderPopup(jobCategoryId ? [jobCategoryId] : [], '#jobCategoryPopup', '#jobCategoryPopup','jobCategory',function(data){
            var { value,label } = data
            jobCategoryEl.val(label)
            jobCategoryEl.attr('data-values', value)
        })

        // 意向城市
        var areaEl = $('input[name="areaId"]')
        var areaId = areaEl.attr('data-values')
        filterCascaderPopup(areaId ? areaId.split() : [], '#areaPopup', '#areaPopup','area',function(data){
            var {length} = data
            if(!length) {
                areaEl.val('')
                areaEl.attr('data-values', '')
                return
            }
            var valueObj = data.reduce((previous, current) => {
                var {value, label} = previous
                return {
                    value: value + ','+current.value,
                    label: label + ','+current.label,
                }
            })
            areaEl.val(valueObj.label)
            areaEl.attr('data-values', valueObj.value)
        })
    })
</script>

