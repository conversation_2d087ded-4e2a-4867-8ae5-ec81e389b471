<link rel="stylesheet" href="/static/css/resumeTextarea.min.css">

<div class="resume-container">
    <form class="resume-form">
        <input type="hidden" name="id" value="<?=$resumeResearchDirection['id'];?>">
        <h1 class="resume-title">研究方向</h1>

        <p class="resume-title-tips">研究方向是用人部门筛选人才的重要依据</p>

        <div class="weui-cell">
            <div class="weui-cell__bd">
                <div class="textarea-cell">
                            <textarea class="weui-textarea" name="researchDirection" placeholder="请输入您的研究方向"
                                    maxlength="500"><?=$resumeResearchDirection['content'];?></textarea>
                </div>
                <div class="weui-textarea-counter">
                    <span>0</span> / <i>500</i>
                </div>
            </div>
        </div>

        <div class="resume-button">
            <button class="weui-btn weui-btn_primary" type="submit">保存</button>
        </div>
    </form>
</div>

<script>
    $(function () {
        var $textarea = $('.weui-textarea')

        textAreaLimitSubmit($textarea)

        resumeOptimization(function (formEl, formData) {
            var isUpdate = formData.id !== ''
            var api = isUpdate ? '/resume/edit-resume-research-direction' : '/resume/add-resume-research-direction'

            httpPost(api, formData).then(function () {
                backResumeEdit()
            })
        })
    })
</script>