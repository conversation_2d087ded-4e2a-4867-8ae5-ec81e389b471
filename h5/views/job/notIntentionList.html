<?php
use h5\components;
?>

<link rel="stylesheet" href="/static/css/job.min.css?v=1.9">

<?= components\PageBannerWidget::widget(['page'=>'jobList']) ?>

<div class="search-content  search-main ">
    <div class="header-container">
        <div class="category-search header-search">
            <a href="/job/search?searchType=1" class="search-category">职位类型</a>
            <a href="/job/search?searchType=2" class="category-input">
                <input type="search" placeholder="搜职位" readonly>
                <button class="search-btn">搜索</button>
            </a>
        </div>
    </div>

    <div class="search-hot">
        <h4 class="search-label is-hot">热门搜索</h4>

        <div class="search-value">
            <?php foreach($jobHotSearchList as $item):?>
            <a href="<?=$item['url']?>"><?=$item['title']?></a>
            <?php endforeach?>
        </div>
    </div>
</div>

<?php if(!empty($recommendCompanyListOne) || !empty($recommendCompanyListTwo) || !empty($recommendCompanyListThree)):?>
<div class="switch-bar recommond-company">
    <h3 class="title">推荐单位</h3>

    <div class="switch-item swiper recommend-swiper">
        <div class="swiper-wrapper">
            <?php if(!empty($recommendCompanyListOne)):?>
            <div class="swiper-slide">
                <?php foreach($recommendCompanyListOne as $item):?>
                    <a href="<?=$item['target_link']?>" class="recommend-job">
                            <img src="<?=$item['image_link']?>" alt="<?=$item['image_alt']?>">

                            <div class="recommend-title">
                                <h4 class="recommend-school"><?=$item['title']?></h4>
                                <p class="recommend-notice"><?=$item['sub_title']?></p>
                            </div>
                    </a>
                <?php endforeach?>

            </div>
            <?php endif?>


            <?php if(!empty($recommendCompanyListTwo)):?>
            <div class="swiper-slide">
                <?php foreach($recommendCompanyListTwo as $item):?>
                <a href="<?=$item['target_link']?>" class="recommend-job">
                    <img src="<?=$item['image_link']?>" alt="<?=$item['image_alt']?>">

                    <div class="recommend-title">
                        <h4 class="recommend-school"><?=$item['title']?></h4>
                        <p class="recommend-notice"><?=$item['sub_title']?></p>
                    </div>
                </a>
                <?php endforeach?>
            </div>
            <?php endif?>

            <?php if(!empty($recommendCompanyListThree)):?>
            <div class="swiper-slide">
                <?php foreach($recommendCompanyListThree as $item):?>
                <a href="<?=$item['target_link']?>" class="recommend-job">
                    <img src="<?=$item['image_link']?>" alt="<?=$item['image_alt']?>">

                    <div class="recommend-title">
                        <h4 class="recommend-school"><?=$item['title']?></h4>
                        <p class="recommend-notice"><?=$item['sub_title']?></p>
                    </div>
                </a>
                <?php endforeach?>
            </div>
            <?php endif?>

        </div>


        <div class="swiper-pagination"></div>
    </div>

    <script>
        $(function () {
            new Swiper(".recommend-swiper", {
                autoplay: {
                    disableOnInteraction: false
                },
                autoHeight: true,
                pagination: {
                    el: ".swiper-pagination",
                    clickable: true,
                }
            })
        })
    </script>
</div>
<?php endif?>

<div class="switch-bar job-list">
    <div class="fix-bar">
        <div class="fix-title">
            <h3 class="title">
                职位列表
                <?php if(!$isLogin):?><a class="to-login" href="javascript:;">登录后查看更匹配职位</a><?php endif?>
            </h3>
            <a href="/job/search" class="primary-search header-search search-fix">
                <input type="search" name="keyword" placeholder="搜职位" />
            </a>
        </div>

        <div class="select-nav">
            <div class="item">
                <span class="major-option open-popup" data-target="#majorPopup">学科要求</span>
                <span class="type-option open-popup" data-target="#typePopup">单位类型</span>
                <span class="area-option open-popup" data-target="#areaPopup">地点</span>
            </div>

            <span class="more open-popup gaocai-vip" data-target="#filterPopup">更多筛选</span>
        </div>
    </div>

    <ul class="position-style">
        <!-- 演示结构 -->
<!--        <li class="element">-->
<!--            <a href="#">-->
<!--                <div class="position">-->
<!--                    <div class="name-tag-cell">-->
<!--                        <span class="job-name">大学教师大学</span>-->
<!--                        <div class="tag-cell">-->
<!--                            <span class="help-wanted">急聘</span>-->
<!--                            <span class="fast-feedback">反馈快</span>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <span class="pay">15-20K</span>-->
<!--                </div>-->
<!--                <div class="article">清华大学珠海分校招聘简章</div>-->
<!--                <div class="required">-->
<!--                    <span>博士</span>-->
<!--                    <span>2人</span>-->
<!--                    <span>哲学等</span>-->
<!--                    <span>3-5年</span>-->
<!--                </div>-->
<!--                <div class="introduce">-->
<!--                    <span class="company">清华大学</span>-->
<!--                    <span class="type">高等院校</span>-->
<!--                    <span class="nature">公办</span>-->
<!--                </div>-->
<!--                <div class="time">07-06发布</div>-->
<!--            </a>-->
<!--        </li>-->
        
        <?php if(!empty($jobList)):?>
        <?php if($isLogin):?>
        <div class="intention-tips">
            <div class="left-tips">
                <h3>你想找什么工作？</h3>
                <span>根据地区、职位类型等精准推荐职位</span>
            </div>
            <a href="<?=$addIntentionUrl?>">添加意向</a>
        </div>
        <?php endif?>
            <?php foreach($jobList as $job):?>
            <li class="element  <?php if($job['status'] == 0):?>position-invalid<?php endif?>">
                <a href="<?=$job['url']?>">
                    <div class="position">
                        <div class="name-tag-cell">
                            <span class="job-name"><?=$job['jobName']?></span>
                            <?php if($job['isTop'] == 1 || $job['isEstablishment'] == 1 || $job['isFast'] == 1):?>
                            <div class="tag-cell">
                                <?php if($job['isTop'] == 1):?><span class="help-wanted">急</span><?php endif;?>
                                <?php if($job['isEstablishment'] == 1):?><span class="establishment">编</span><?php endif;?>
                                <?php if($job['isFast'] == 1):?><span class="fast-feedback"></span><?php endif;?>
                            </div>
                            <?php endif;?>
                        </div>
                        <span class="pay"><?=$job['wage']?></span>
                    </div>
                    <?php if($job['announcementName']):?><div class="article"  ><?=$job['announcementName']?></div><?php endif?>
                    <div class="required">
                        <?php if(!empty($job['education'])):?><span><?=$job['education']?></span><?php endif;?>
                        <?php if(!empty($job['amount'])):?><span><?=$job['amount']?>人</span><?php endif;?>
                        <?php if(!empty($job['experience'])):?><span><?=$job['experience']?></span><?php endif;?>
                        <?php if(!empty($job['city'])):?><span><?=$job['city']?></span><?php endif;?>
                    </div>
                    <div class="introduce">
                        <span class="company"><?=$job['companyName']?></span>
                        <span class="type"><?=$job['companyTypeName']?></span>
                        <span class="nature"><?=$job['companyNatureName']?></span>
                    </div>
                    <div class="time"><?=$job['refreshDate']?>发布</div>
                </a>
            </li>
            <?php endforeach?>
        <?php else:?>

        <div class="noresult-container">
            <div class="weui-loadmore__tips">
                <img src="/static/assets/home/<USER>" class="no-result" />
            </div>
            <div class="empty-tips">对不起，暂无符合条件的职位，试试搜索吧</div>
            <a class="search" href="/job/search?searchType=2">去搜索</a>
        </div>
        <?php endif;?>
    </ul>
</div>

<div class="weui-loadmore" style="display: none;">
    <i class="weui-loading"></i>
    <span class="weui-loadmore__tips">正在加载</span>
</div>

<div class="weui-loadmore weui-loadmore_line" style="display: none;">
    <span class="weui-loadmore__tips">暂无数据</span>
</div>

<!-- 学科要求弹窗 start -->
<div id="majorPopup" class="weui-popup__container filter-container">
    <div class="weui-popup__overlay"></div>
    <div class="weui-popup__modal">
        <form action="" class="filter-form">
            <div class="filter-header">
                <span>学科要求</span>
                <i class="close-button close-popup"></i>
            </div>

            <div class="filter-main is-primary is-major navigation-main">
                <ul class="navigation-nav">
                    <li><span>不限</span></li>
                    <?php foreach($majorList as $major):?>
                    <li><span><?=$major["v"]?></span></li>
                    <?php endforeach;?>

                </ul>

                <div class="navigation-box">
                    <ul class="navigation-panel is-major">
                        <li class="panel-links is-active filter-value">
                            <label>
                                <input type="checkbox" name="majorId" value="0">
                                <span>不限</span>
                            </label>
                        </li>
                    </ul>

                    <?php foreach($majorList as $major):?>
                        <ul class="navigation-panel is-major">
                            <li class="panel-title"><span><?=$major["v"]?></span></li>
                            <li class="panel-links is-active filter-value">
                                <?php foreach($major["children"] as $children):?>
                                <label>
                                    <input type="checkbox" name="majorId" value="<?=$children['k']?>">
                                    <span><?=$children['v']?></span>
                                </label>
                                <?php endforeach;?>
                            </li>
                        </ul>
                    <?php endforeach;?>

                </div>
            </div>

            <div class="filter-operate">
                <button class="weui-btn" type="reset">重置</button>
                <button class="weui-btn weui-btn_primary close-popup" type="submit">确定</button>
            </div>
        </form>
    </div>
</div>

<script>
    $(function () {
        filterCascaderPopup(<?=$majorIdList?:'[]'?>, '#majorPopup', '#majorPopup', 'majorId')
    })
</script>
<!-- 学科要求弹窗 end -->

<!-- 单位类型弹窗 start -->
<div id="typePopup" class="weui-popup__container filter-container">
    <div class="weui-popup__overlay"></div>
    <div class="weui-popup__modal show-mask">
        <form action="" class="filter-form">
            <div class="filter-header">
                <span>单位类型</span>
                <i class="close-button close-popup"></i>
            </div>

            <div class="filter-main is-primary">
                <div class="filter-value">
                    <label>
                        <input type="checkbox" name="companyType" value="0">
                        <span>不限</span>
                    </label>
                    <?php foreach($companyTypeList as $k=>$v):?>
                        <label>
                            <input type="checkbox" name="companyType" value="<?= $k?>">
                            <span><?= $v?></span>
                        </label>
                    <?php endforeach;?>
                </div>
            </div>

            <div class="filter-operate">
                <button class="weui-btn" type="reset">重置</button>
                <button class="weui-btn weui-btn_primary close-popup" type="submit">确定</button>
            </div>
        </form>
    </div>
</div>

<script>
    $(function () {
        filterPlainPopup(<?=$companyTypeIdList?:'[]'?>, '#typePopup', '#typePopup', 'companyType')
    })
</script>
<!-- 单位类型弹窗 start -->

<!-- 地点弹窗 start -->
<div id="areaPopup" class="weui-popup__container filter-container">
    <div class="weui-popup__overlay"></div>
    <div class="weui-popup__modal">
        <form action="" class="filter-form">
            <div class="filter-header">
                <span>地点</span>
                <i class="close-button close-popup"></i>
            </div>

            <div class="filter-main is-primary is-area navigation-main">
                <ul class="navigation-nav">
                    <li><span>不限</span></li>
                    <li><span>热门地区</span></li>
                    <?php foreach($cityList as $k=>$city):?>
                    <li><span><?= $city["v"]?></span></li>
                    <?php endforeach;?>
                </ul>

                <div class="navigation-box">
                    <ul class="navigation-panel is-area">
                        <li class="panel-links is-active filter-value">
                            <label>
                                <input type="checkbox" name="areaId" value="0">
                                <span>不限</span>
                            </label>
                        </li>
                    </ul>

                    <ul class="navigation-panel is-area">
                        <li class="panel-title"><span>热门地区</span></li>
                        <li class="panel-links is-active filter-value">
                            <?php foreach($hotCityList as $k=>$city):?>
                                <label>
                                    <input type="checkbox" name="areaId" value="<?= $city['id']?>">
                                    <span><?= $city['name']?></span>
                                </label>
                            <?php endforeach;?>
                        </li>
                    </ul>

                    <?php foreach($cityList as $k=>$city):?>
                    <ul class="navigation-panel is-area">
                        <li class="panel-title"><span><?= $city["v"]?></span></li>
                        <li class="panel-links is-active filter-value">
                            <?php foreach($city["children"] as $children):?>
                            <label>
                                <input type="checkbox" name="areaId" value="<?= $children['k']?>">
                                <span><?= $children["v"]?></span>
                            </label>
                            <?php endforeach;?>
                        </li>
                    </ul>
                    <?php endforeach;?>

                </div>
            </div>

            <div class="filter-operate">
                <button class="weui-btn" type="reset">重置</button>
                <button class="weui-btn weui-btn_primary close-popup" type="submit">确定</button>
            </div>
        </form>
    </div>
</div>

<script>
    $(function () {
        filterCascaderPopup(<?=$areaIdList?:'[]'?>, '#areaPopup', '#areaPopup', 'areaId')
    })
</script>
<!-- 地点弹窗 end -->

<!-- 更多筛选弹窗 start -->
<div id="filterPopup" class="weui-popup__container filter-container">
    <div class="weui-popup__overlay"></div>
    <div class="weui-popup__modal">
        <form action="" class="filter-form">
            <div class="filter-header">
                <span>更多筛选</span>
                <i class="close-button close-popup"></i>
            </div>

            <div class="filter-main">
                <div class="filter-item">
                    <div class="filter-label is-expand is-vip">
                        <span class="label" data-title="编制查询说明" data-content="可查询包含行政编制/事业编制/备案制等编制类型的优质职位" data-description="">编制筛选</span>

                        <span class="value">“上岸”快人一步</span>
                    </div>
                    <div class="filter-value filter-value-radio">
                        <label>
                            <input type="checkbox" name="isEstablishment" value="1" />
                            <span>编制</span>
                        </label>
                    </div>
                </div>

                <div class="filter-item">
                    <div class="filter-label is-expand is-vip">
                        <span
                            class="label"
                            data-title="职位热度说明"
                            data-content="根据职位的关注度、招录人数竞争比综合分析得出。"
                            data-description="一般：表示关注人数较少，可重点关注；<br />较热：表示该职位备受欢迎，可持续关注；<br />火爆：表示该职位竞争激烈，可抓紧机会争取。"
                            >职位热度</span>

                        <span class="value">追踪职位实时热度</span>
                    </div>
                    <div class="filter-value filter-value-radio">
                        <?php foreach($jobHeatList as $k=>$v):?>
                        <label>
                            <input type="radio" name="applyHeat" value="<?=$k?>" />
                            <span><?=$v?></span>
                        </label>
                        <?php endforeach?>
                    </div>
                </div>

                <div class="filter-item">
                    <div class="filter-label">发布时间</div>
                    <div class="filter-value filter-value-radio">
                        <?php foreach($releaseTimeList as $k=>$v):?>
                        <label><input type="radio" name="releaseTimeType" value="<?=$k?>"><span><?=$v?></span></label>
                        <?php endforeach?>
                    </div>
                </div>

                <div class="filter-item">
                    <div class="filter-label">学历要求</div>
                    <div class="filter-value">
                        <?php foreach($educationRequire as $k=>$v):?>
                        <label><input type="checkbox" name="educationType" value="<?=$k?>"><span><?=$v?></span></label>
                        <?php endforeach?>
                    </div>
                </div>

                <div class="filter-item">
                    <div class="filter-label is-select">
                        <span>行业类别</span>
                        <span id="industryId" class="value">查看全部</span>
                    </div>
                </div>

                <div class="filter-item">
                    <div class="filter-label">单位性质</div>
                    <div class="filter-value">
                        <?php foreach($companyNatureList as $k=>$v):?>
                        <label><input type="checkbox" name="companyNature" value="<?=$k?>"><span><?=$v?></span></label>
                        <?php endforeach?>
                    </div>
                </div>

                <div class="filter-item">
                    <div class="filter-label">单位规模</div>
                    <div class="filter-value">
                        <?php foreach($companyScaleList as $k=>$v):?>
                        <label><input type="checkbox" name="companyScaleType" value="<?=$k?>"><span><?=$v?></span></label>
                        <?php endforeach?>
                    </div>
                </div>

                <div class="filter-item">
                    <div class="filter-label">薪资范围</div>
                    <div class="filter-value">
                        <?php foreach($wageRangeList as $k=>$v):?>
                        <label><input type="checkbox" name="wageId" value="<?=$k?>"><span><?=$v?></span></label>
                        <?php endforeach?>
                    </div>
                </div>

                <div class="filter-item">
                    <div class="filter-label">经验要求</div>
                    <div class="filter-value">
                        <?php foreach($experienceList as $k=>$v):?>
                        <label><input type="checkbox" name="experienceType" value="<?=$k?>"><span><?=$v?></span></label>
                        <?php endforeach?>
                    </div>
                </div>

                <div class="filter-item">
                    <div class="filter-label">工作性质</div>
                    <div class="filter-value">
                        <?php foreach($jobNatureList as $k=>$v):?>
                        <label><input type="checkbox" name="natureType" value="<?=$k?>"><span><?=$v?></span></label>
                        <?php endforeach?>
                    </div>
                </div>

                <div class="filter-item">
                    <div class="filter-label">职称要求</div>
                    <div class="filter-value">
                        <?php foreach($titleTypeList as $k=>$v):?>
                        <label><input type="checkbox" name="titleType" value="<?=$v['k']?>"><span><?=$v['v']?></span></label>
                        <?php endforeach?>
                    </div>
                </div>

                <div class="filter-item">
                    <div class="filter-label">职位福利</div>
                    <div class="filter-value">
                        <?php foreach($welfareLabelList as $k=>$v):?>
                        <label><input type="checkbox" name="welfareLabelId" value="<?=$v['id']?>"><span><?=$v['name']?></span></label>
                        <?php endforeach?>
                    </div>
                </div>
            </div>

            <div class="filter-operate">
                <button class="weui-btn" type="reset">重置</button>
                <button class="weui-btn weui-btn_primary close-popup" type="submit">确定</button>
            </div>
        </form>
    </div>
</div>

<script>
    $(function () {
        var isLogin = "<?=$isLogin?>"
        var isVip = "<?=$isVip?>"
        var vipPageUrl = "/vip.html"

        var tempApplyHeatRadioValue = null
        var tempTimeTypeRadioValue = null
        /* 是否点击了重置 */
        var isReset = false
        /* 更多筛选 -> 行业类别 */
        var tempIndustryId = {
            id: '',
            value: ''
        }
        var defaultIndustryText = '查看全部'


        var checkedList = {
            releaseTimeType: '<?=$searchData["releaseTimeType"]?:""?>',
            educationType: <?=$educationTypeList?:'[]'?>,
            industryId: '<?=$searchData["industryId"]?:""?>',
            industryText: '<?=$industryText?:""?>',
            companyNature: <?=$companyNatureIdList?:'[]'?>,
            companyScaleType: <?=$companyScaleTypeList?:'[]'?>,
            wageId: <?=$wageIdList?:'[]'?>,
            experienceType: <?=$experienceTypeList?:'[]'?>,
            natureType: <?=$natureTypeList?:'[]'?>,
            titleType: <?=$titleTypeIdList?:'[]'?>,
            welfareLabelId: <?=$welfareLabelIdList?:'[]'?>,
            isEstablishment: '<?=$searchData["isEstablishment"]?:""?>',
            applyHeat: '<?=$searchData["applyHeat"]?:""?>'
        }

        var $target = $('[data-target="#filterPopup"]')
        var $filterPopup = $('#filterPopup')
        var $filterForm = $filterPopup.find('.filter-form')
        var $filterLabel = $filterForm.find('.filter-label')

        var $applyHeat = $filterForm.find('[name="applyHeat"]')

        var $releaseTimeType = $filterForm.find('[name="releaseTimeType"]')

        var industrySelector = new MobileSelect({
            trigger: '#industryId',
            title: '行业类别',
            wheels: [
                {
                    data: [
                        <?php foreach($tradeList as $k=>$trade):?>
                        {
                            id: '<?=$trade["k"]?>',
                                value: '<?=$trade["v"]?>',
                            childs: [
                            <?php foreach($trade["children"] as $children):?>
                            {
                                id: '<?=$children["k"]?>',
                                    value: '<?=$children["v"]?>'
                            },
                            <?php endforeach;?>
                        ]
                        },
                        <?php endforeach;?>
                    ]
                }
            ],
            callback: function (index, data) {
                var val = data.pop()

                tempIndustryId.id = val.id === '0' ? '' : val.id
                tempIndustryId.value = val.id === '0' ? defaultIndustryText : val.value
                industrySelector.trigger.innerText = tempIndustryId.value
            }
        })   

        function resetParams() {
            return {
                releaseTimeType: '',
                educationType: [],
                industryId: '',
                industryText: '',
                companyNature: [],
                companyScaleType: [],
                wageId: [],
                experienceType: [],
                natureType: [],
                titleType: [],
                welfareLabelId: [],
                isEstablishment: '',
                applyHeat: ''
            }
        }
        
        function showVipModal(title, content, description) {
            $.modal({
                text: description ? `<div class="custom-modal__content">${content}</div><div class="custom-modal__description">${description}</div>` : content,
                title: title,
                buttons: [
                    {
                        text: '关闭',
                        className: 'default'
                    },
                    {
                        text: '升级VIP',
                        className: 'primary',
                        onClick: function () {
                            window.location.href = vipPageUrl
                        }
                    }
                ]
            })
        }

        function initChecked(target, value) {
            target.filter(`[value="${value}"]`).prop('checked', true)
            target.parents('.filter-value').siblings('.filter-label').addClass('is-expand')
        }

        function initHandler(name, data) {
            var $target = $filterForm.find('[name="' + name + '"]')

            if (isString(data)) {
                initChecked($target, data)
            }

            if (isArray(data)) {
                data.forEach(function (item) {
                    initChecked($target, item)
                })
            }
        }

        function init() {
            var hasFilterArr = []
            $filterForm.find('input').prop('checked', false)

            Object.keys(checkedList).forEach(function (key) {
                if (checkedList[key].length) {
                    hasFilterArr.push(key)

                    if (key === 'industryId') {
                        tempIndustryId.id = checkedList[key]
                        return
                    }
                    if (key === 'industryText') {
                        tempIndustryId.value = checkedList[key]
                        industrySelector.trigger.innerText = checkedList[key]
                        return
                    }

                    initHandler(key, checkedList[key])
                }
            })

            if (hasFilterArr.length) {
                $target.addClass('is-active')
            }
        }

        init()

        // VIP filter label 阻止冒泡
        $('.filter-label.is-expand.is-vip')
            .find('.label')
            .on('click', function (e) {
                e.stopPropagation()

                var title = $(this).data('title')
                var content = $(this).data('content')
                var description = $(this).data('description')

                showVipModal(title, content, description)
            })

        $('.filter-label.is-expand.is-vip')
            .siblings('.filter-value')
            .on('click', 'input', function () {
                if (!isLogin) {
                    window.location.href = `/code-login?redirect=${encodeURIComponent(window.location.href)}`
                    return
                }

                if (!isVip) {
                    $(this).prop('checked', false)
                    showVipModal('提示', '开通VIP服务即可使用高级筛选功能，精准捕捉目标职位&公告', '高级筛选功能包括：职位&公告 编制查询、热度情况筛选等')
                    return
                }
            })

        $target.on('click', function () {
            init()
        })

        $filterLabel.on('click', function () {
            var $target = $(this)

            // 如果点击的是行业类别，则不做任何处理
            if ($target.hasClass('is-select')) {
                return
            }

            $target.toggleClass('is-expand')
        })

        $applyHeat.on('click', function () {
            var targetValue = $(this).val()

            if (targetValue === tempApplyHeatRadioValue) {
                $(this).prop('checked', false)
                tempApplyHeatRadioValue = null
            } else {
                tempApplyHeatRadioValue = targetValue
            }
        })

        $releaseTimeType.on('click', function () {
            var targetValue = $(this).val()

            if (targetValue === tempTimeTypeRadioValue) {
                $(this).prop('checked', false)
                tempTimeTypeRadioValue = null
            } else {
                tempTimeTypeRadioValue = targetValue
            }
        })

        $filterForm.on('reset', function () {
            isReset = true
            tempApplyHeatRadioValue = null
            tempTimeTypeRadioValue = null
            tempIndustryId.id = ''
            tempIndustryId.value = ''
            industrySelector.trigger.innerText = defaultIndustryText
        }).on('submit', function (e) {
            e.preventDefault()

            var nLink = window.location.href

            var formData = $(this).serializeArray().reduce((previous, current) => {
                var key = current.name
                var value = current.value

                if (previous[key]) {
                    previous[key] = [...previous[key], value]
                } else {
                    previous[key] = value
                }
                return previous
            }, {})

            var formDataKeys = []

            if (tempIndustryId.id && tempIndustryId.value) {
                formData.industryId = tempIndustryId.id
                formData.industryText = tempIndustryId.value
            }

            if (isReset) {
                checkedList = resetParams()
            }

            formDataKeys = Object.keys(formData)

            Object.keys(checkedList).forEach(function (key) {
                var value = ''

                if (formDataKeys.indexOf(key) > -1) {
                    checkedList[key] = formData[key]
                } else {
                    checkedList[key] = resetParams()[key]
                }

                value = isArray(checkedList[key]) ? checkedList[key].join('_') : checkedList[key]
                nLink = replaceUrlParam(nLink, key, value)
            })

            window.location.href = nLink
        })
    })
</script>
<!-- 更多筛选弹窗 end -->

<script>
    $(function () {
        var loading = false
        var currentPage = 1

        var showcaseApiData = {
            src: "<?=$listShowcaseInfo['img']?>",
            url: "<?=$listShowcaseInfo['url']?>",
            index: <?=$listShowcaseInfo['position']?:0?>
        }

        var $loading = $('.weui-loadmore')
        var $positionList = $('.position-style')
        var $container = $('.main-container')
         var $fixBar = $('.fix-bar')
        var $fitTitle = $('.fix-title')
        var $jobList = $('.job-list')
        var $loginBtn = $('.to-login')

        // 职位搜索结果列表页面需要渲染检索值
        var query = {
            keyword: '<?=$searchData["keyword"]?:""?>',
            jobCategoryId: '<?=$searchData["jobCategoryId"]?:""?>',
            majorId: <?=$majorIdList?:'[]'?>,
            companyType: <?=$companyTypeIdList?:'[]'?>,
            areaId: <?=$areaIdList?:'[]'?>,
            releaseTimeType: '<?=$searchData["releaseTimeType"]?:""?>',
            educationType: <?=$educationTypeList?:'[]'?>,
            industryId: '<?=$searchData["industryId"]?:""?>',
            companyNature: <?=$companyNatureIdList?:'[]'?>,
            companyScaleType: <?=$companyScaleTypeList?:'[]'?>,
            wageId: <?=$wageIdList?:'[]'?>,
            experienceType: <?=$experienceTypeList?:'[]'?>,
            natureType: <?=$natureTypeList?:'[]'?>,
            titleType: <?=$titleTypeIdList?:'[]'?>,
            welfareLabelId: <?=$welfareLabelIdList?:'[]'?>,
            isEstablishment: '<?=$searchData["isEstablishment"]?:""?>'
        }

        function renderList(data) {
            return data.reduce((previous, current) => {
                const {
                    url,
                    jobName,
                    wage,
                    announcementName,
                    education,
                    amount,
                    experience,
                    companyName,
                    companyTypeName,
                    companyNatureName,
                    status,
                    city,
                    releaseTime,
                    isTop,
                    isFast,
                    refreshDate,
                    isEstablishment
                } = current

                let jobTags = '<div class="tag-cell">'
                const hasTopTag = isTop === '1'
                const hasFastTag = isFast === '1'
                const hasEstablishmentTag = isEstablishment === '1'

                const jobClassName = status == 0 ? ' position-invalid' : ''
                const announcementText = announcementName ? `<div class="article">${announcementName}</div>` : ''
                const educationText = education ? `<span>${education}</span>` : ''
                const amountText = amount ? `<span>${amount}人</span>` : ''
                const experienceText = experience ? `<span>${experience}</span>` : ''
                const cityText = city ? `<span>${city}</span>` : ''

                if (hasTopTag || hasFastTag || hasEstablishmentTag) {
                    if (hasTopTag) {
                        jobTags += '<span class="help-wanted">急</span>'
                    }

                    if (hasEstablishmentTag) {
                        jobTags += '<span class="establishment">编</span>'
                    }

                    if (hasFastTag) {
                        jobTags += '<span class="fast-feedback"></span>'
                    }

                    jobTags += '</div>'
                } else {
                    jobTags = ''
                }

                return (previous += `<li class="element${jobClassName}">
                        <a href="${url}">
                            <div class="position">
                                <div class="name-tag-cell">
                                    <span class="job-name">${jobName}</span>
                                    ${jobTags}
                                </div>
                                <span class="pay">${wage}</span>
                            </div>
                            ${announcementText}
                            <div class="required">
                                ${educationText}
                                ${amountText}
                                ${experienceText}
                                ${cityText}
                            </div>
                            <div class="introduce">
                                <span class="company">${companyName}</span>
                                <span class="type">${companyTypeName}</span>
                                <span class="nature">${companyNatureName}</span>
                            </div>
                            <div class="time">${refreshDate}发布</div>
                        </a>
                    </li>`)
            }, '')
        }

        function handleShowcaseCard() {
            if (showcaseApiData.src) {
                $positionList
                    .find('li')
                    .eq(showcaseApiData.index)
                    .after($(`<li class="element"><a class="showcase-link" href="${showcaseApiData.url}"><img src="${showcaseApiData.src}" /></a></li>`))
            }
        }

        handleShowcaseCard()

        // tab栏吸顶 start
        $container.on('scroll', function () {
            var isFixed = $jobList.offset().top <= $('.header-container').height()

            if (isFixed) {
                $fixBar.addClass('nav-fixed')
                $fitTitle.addClass('fix-search')
            } else {
                $fixBar.removeClass('nav-fixed')
                $fitTitle.removeClass('fix-search')
            }
        })

        function fetchData() {
            var params = {
                p: currentPage + 1,
            }

            Object.keys(query).map(key => {
                params[key] = Array.isArray(query[key]) ? query[key].join('_') : query[key]
            })

            loading = true
            $loading.hide()
            $loading.eq(0).show()

            httpGet('/job/get-list', params).then(data => {
                $loading.hide()
                if (data.length) {
                    $positionList.append(renderList(data))
                } else {
                    $loading.eq(1).show()
                }
                loading = data.length === 0
                currentPage += 1
            })
        }

        isScrollToFilter('.job-list')

        <?php if(!empty($jobList)):?>
        $container.infinite().on('infinite', function () {
            if (loading) return
            fetchData()
        })
        <?php endif?>

        $loginBtn.on('click', function () {
            window.signupPopup.show()
        })
    })
</script>
