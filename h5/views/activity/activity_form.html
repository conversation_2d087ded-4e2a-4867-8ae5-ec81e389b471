<link rel="stylesheet" href="/static/css/dbMeetingApply.min.css?v=20240911">

<div class="empty-main"></div>

<div class="activity-container">
    <?php if($info['background_url']):?>
    <div class="activity-banner">
        <img src="<?=$info['background_url'];?>" alt=""/>
    </div>
    <?php endif;?>

    <div class="article">
        <h3 class="article-title"><?=$info['name'];?></h3>
        <div class="content">
            <?=$info['introduction'];?>
        </div>
    </div>

    <form class="activity-form">
        <input type="hidden" name="activityFormId" value="<?=$info['id']?>"/>
        <div class="activity-wrapper">
            <div class="wrapper-title required">
                <span>报名意向选择</span>
                <span class="required-tips"><?php if (!$optionId): ?>(<?=$info['optionTips'];?>)<?php endif; ?>
                </span>
            </div>
            <div class="activity-row">
                <p class="form-item-label is-required"><?=$info['intent_explain'];?></p>
                <div class="checkbox-content options-intention">
                    <?php foreach($info['intentionOption'] as $k=>$v):?>
                    <div class="checkbox-list <?php if(in_array($v['id'],$info['oldOptionIds'])){echo 'is-disabled';}?>">
                        <label class="weui-check__label">
                            <div class="weui-cell__hd">
                                <?php if ($optionId && $optionId==$v['id']): ?>
                                <!-- 从签到过来，默认旋中 只是多加了一个checked -->
                                <input class="weui-form-checkbox intention-option" name="optionIds" value="<?=$v['id']?>" type="checkbox" checked placeholder="请填写报名意向" data-validate-error="请填写必填项(*)后再提交！" required <?php if(in_array($v['id'],$info['oldOptionIds'])){echo 'disabled';}?> />
                                <i class="weui-icon-checkbox"></i>
                                <?php else: ?>
                                <input class="weui-form-checkbox intention-option" name="optionIds" value="<?=$v['id']?>" type="checkbox"  placeholder="请填写报名意向" data-validate-error="请填写必填项(*)后再提交！" required <?php if(in_array($v['id'],$info['oldOptionIds']) || $info['allIntentionOptionDisabled'] || $optionId){echo 'disabled';}?> />
                                <i class="weui-icon-checkbox"></i>
                                <?php endif; ?>
                            </div>
                            <div class="weui-cell__bd weui-form-text">
                                <div class="name">
                                    <?=$v['title'];?>
                                    <?php if(in_array($v['id'],$info['oldOptionIds'])):?>
                                    <span class="tag">已报名</span>
                                    <?php endif;?>
                                </div>
                            </div>
                        </label>
                        <div class="checkbox-bottom">
                            <?php if($v['type']==3):?>
                            <a href="<?=$v['link'];?>" target="_blank"><?=$v['link_version']?:'查看详情';?></a>
                            <?php else:?>
                            <?php if(strlen($v['version'])>0):?>
                            <span><?=$v['version'];?></span>
                            <?php endif;?>
                            <?php endif;?>
                        </div>
                    </div>
                    <?php endforeach;?>
                </div>
            </div>
        </div>

        <div class="resume-steps is-show">
            <div class="activity-wrapper">
                <div class="wrapper-title required">
                    <span>基本信息</span>
                </div>

                <div class="activity-row">
                    <p class="form-item-label is-required">姓名</p>
                    <div class="weui-cell">
                        <div class="weui-cell__bd">
                            <input class="weui-input" type="text" name="name" value="<?=$baseInfo['name']?>"
                                   placeholder="请输入姓名" required/>
                        </div>
                        <i class="weui-icon-clear"></i>
                    </div>
                </div>

                <div class="activity-row gender-wrapper">
                    <p class="form-item-label is-required">性别</p>
                    
                    <div class="activity-row weui-cells_checkbox">
                        <label class="weui-check__label">
                            <div class="weui-cell__hd">
                                <input class="weui-check" name="gender" value="1" type="radio" placeholder="请选择性别" required <?php if($baseInfo['gender']==1){?>checked<?php }?>/>
                                <i class="weui-icon-checked"></i>
                            </div>
                            <div class="weui-cell__bd weui-form-text">
                                <p>男</p>
                            </div>
                        </label>
                        <label class="weui-check__label">
                            <div class="weui-cell__hd">
                                <input class="weui-check" name="gender" value="2" type="radio" placeholder="请选择性别" required <?php if($baseInfo['gender']==2){?>checked<?php }?>/>
                                <i class="weui-icon-checked"></i>
                            </div>
                            <div class="weui-cell__bd weui-form-text">
                                <p>女</p>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="activity-row">
                    <p class="form-item-label is-required">出生日期</p>
                    <div class="weui-cell">
                        <div class="weui-cell__bd">
                            <input class="weui-input weui-input-select" id="birthday" name="birthday" type="text"
                                   value="<?=$baseInfo['birthday'];?>" readonly required placeholder="请选择出生日期"/>
                        </div>
                        <i class="icon-arrow-down"></i>
                    </div>
                </div>

                <div class="activity-row">
                    <p class="form-item-label is-required">户籍/国籍</p>
                    <div class="weui-cell">
                        <div class="weui-cell__bd">
                            <input
                                    class="weui-input weui-input-select open-popup"
                                    data-target="#householdPopup"
                                    name="householdRegisterId"
                                    data-values="<?=$baseInfo['household_register_id'];?>"
                                    value="<?=$baseInfo['householdRegisterText'];?>"
                                    readonly
                                    required
                                    placeholder="请选择户籍/国籍"
                            />
                        </div>
                        <i class="icon-arrow-down"></i>
                    </div>
                </div>

                <div class="activity-row">
                    <p class="form-item-label is-required">政治面貌</p>
                    <div class="weui-cell">
                        <div class="weui-cell__bd">
                            <input
                                    class="weui-input weui-input-select"
                                    id="politicalStatusId"
                                    name="politicalStatusId"
                                    type="text"
                                    data-values="<?=$baseInfo['political_status_id'];?>"
                                    value="<?=$baseInfo['politicalStatusTxt'];?>"
                                    readonly
                                    required
                                    placeholder="请选择政治面貌"
                            />
                        </div>
                        <i class="icon-arrow-down"></i>
                    </div>
                </div>

                <div class="activity-row">
                    <p class="form-item-label is-required">手机号</p>
                    <div class="weui-cell">
                        <div class="weui-cell__bd">
                            <input class="weui-input" type="text" name="mobile" value="<?=$baseInfo['mobile'];?>"
                                   placeholder="请输入手机号" required <?php if($resumeStep<4):?>readonly<?php endif;?>/>
                        </div>
                    </div>
                </div>

                <div class="activity-row">
                    <p class="form-item-label is-required">联系邮箱</p>
                    <div class="weui-cell">
                        <div class="weui-cell__bd">
                            <input class="weui-input" type="text" name="email" value="<?=$baseInfo['email'];?>"
                                   placeholder="用于接收信息、安全认证" required/>
                        </div>
                    </div>
                </div>

                <div class="activity-row">
                    <p class="form-item-label is-required">职称(选填)</p>
                    <div class="weui-cell">
                        <div class="weui-cell__bd">
                            <input class="weui-input weui-input-select open-popup" data-target="#titlePopup" type="text"
                                   name="titleId" data-values="<?=$baseInfo['title_id']?>" value="<?=$baseInfo['titleTxt'];?>" readonly
                                   placeholder="请选择职称"/>
                        </div>
                        <i class="icon-arrow-down"></i>
                    </div>
                </div>

                <div class="activity-row">
                    <p class="form-item-label is-required">现居住地(选填)</p>
                    <div class="weui-cell">
                        <div class="weui-cell__bd">
                            <input
                                class="weui-input weui-input-select open-popup"
                                data-target="#residencePopup"
                                type="text"
                                name="residence"
                                value="<?=$baseInfo['residenceTxt'];?>"
                                data-values="<?=$baseInfo['residence'];?>"
                                readonly
                                placeholder="请选择您的现居住地"
                            />
                        </div>
                        <i class="icon-arrow-down"></i>
                    </div>
                </div>
            </div>

            <div class="activity-wrapper education-wrapper">
                <div class="wrapper-title required">
                    <span>最高教育经历</span>
                </div>

                <div class="activity-row">
                    <p class="form-item-label is-required">学校名称</p>
                    <div class="weui-cell">
                        <div class="weui-cell__bd">
                            <input class="weui-input" name="school" value="<?=$educationList['school'];?>"
                                   placeholder="请输入学校名称" autocomplete="off" required/>
                        </div>
                        <i class="weui-icon-clear"></i>
                    </div>
                    <div class="education-type">
                        <label class="weui-check__label">
                            <div class="weui-cell__hd">
                                <input class="weui-form-checkbox" name="isRecruitment" <?php if($educationList['is_recruitment']==1):?>checked="checked"<?php endif;?>
                                       type="checkbox"/>
                                <i class="weui-icon-checkbox"></i>
                            </div>
                            <div class="weui-cell__bd weui-form-text">
                                <p>统招</p>
                            </div>
                        </label>

                        <label class="weui-check__label">
                            <div class="weui-cell__hd">
                                <input class="weui-form-checkbox" name="isProjectSchool" <?php if($educationList['is_project_school']==1):?>checked="checked"<?php endif;?> type="checkbox"/>
                                <i class="weui-icon-checkbox"></i>
                            </div>
                            <div class="weui-cell__bd weui-form-text">
                                <p>985/211</p>
                            </div>
                        </label>

                        <label class="weui-check__label">
                            <div class="weui-cell__hd">
                                <input class="weui-form-checkbox" name="isAbroad" <?php if($educationList['is_abroad']==1):?>checked="checked"<?php endif;?> type="checkbox"/>
                                <i class="weui-icon-checkbox"></i>
                            </div>
                            <div class="weui-cell__bd weui-form-text">
                                <p>留学</p>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="activity-row">
                    <p class="form-item-label is-required">二级院系(机构)名称(选填)</p>
                    <div class="weui-cell">
                        <div class="weui-cell__bd">
                            <input class="weui-input" name="college" value="<?=$educationList['college'];?>"
                                   placeholder="二级院系（机构）名称" autocomplete="off"/>
                        </div>
                        <i class="weui-icon-clear"></i>
                    </div>
                </div>

                <div class="activity-row">
                    <p class="form-item-label is-required">学历水平</p>
                    <div class="weui-cell">
                        <div class="weui-cell__bd">
                            <input class="weui-input weui-input-select" id="educationId" name="educationId"
                                   value="<?=$educationList['educationTxt'];?>" data-values="<?=$educationList['education_id']?>" type="text"
                                   readonly required placeholder="请选择学历水平"/>
                        </div>
                        <i class="icon-arrow-down"></i>
                    </div>
                </div>

                <div class="activity-row date-row">
                    <p class="form-item-label is-required">就读时间</p>
                    <div class="weui-cell">
                        <div class="weui-cell__bd">
                            <input class="weui-input weui-input__select" id="studyBeginDate" name="beginDate"
                                   type="text" value="<?=$educationList['begin_date'];?>" readonly required
                                   placeholder="入学时间"/>
                            <span class="range-line"></span>
                            <input class="weui-input weui-input-select" id="studyEndDate" name="endDate" type="text"
                                   value="<?=$educationList['end_date'];?>" readonly required placeholder="毕业时间"/>
                        </div>
                        <i class="icon-arrow-down"></i>
                    </div>
                </div>

                <div class="activity-row major-row">
                    <p class="form-item-label is-required">所学专业</p>
                    <div class="weui-cell">
                        <div class="weui-cell__bd">
                            <input
                                    id="major"
                                    class="weui-input weui-input-select open-popup major-select"
                                    data-target="#majorPopup"
                                    name="majorId"
                                    readonly
                                    value="<?=$educationList['majorTxt'];?>"
                                    data-values="<?=$educationList['major_id'];?>"
                                    placeholder="请选择所学专业"

                            />
                            <input class="weui-input major-custom" name="majorCustom" type="text"
                                   value="<?=$educationList['major_custom'];?>"
                                   placeholder="请输入平台未收录专业" />
                        </div>
                        <i class="icon-arrow-down"></i>
                    </div>
                    <div class="switch-major">若无您所需专业，请点击<span class="switch-btn">自定义录入专业</span></div>
                </div>
            </div>

            <div class="activity-wrapper">
                <div class="wrapper-title required">
                    <span>求职意向</span>
                </div>

                <div class="activity-row">
                    <p class="form-item-label is-required">求职状态</p>
                    <div class="weui-cell">
                        <div class="weui-cell__bd">
                            <input class="weui-input weui-input__select" id="workStatus" name="workStatus" type="text"
                                   value="<?=$intentionList['workStatusTxt'];?>" data-values="<?=$intentionList['work_status'];?>" readonly placeholder="请选择求职状态" required/>
                        </div>
                        <i class="icon-arrow-down"></i>
                    </div>
                </div>

                <div class="activity-row">
                    <p class="form-item-label is-required">到岗时间</p>
                    <div class="weui-cell">
                        <div class="weui-cell__bd">
                            <input
                                    class="weui-input weui-input__select"
                                    id="arriveDateType"
                                    name="arriveDateType"
                                    type="text"
                                    value="<?=$intentionList['arriveDateTypeTxt'];?>"
                                    data-values="<?=$intentionList['arrive_date_type'];?>"
                                    readonly
                                    placeholder="请选择到岗时间"
                                    required
                            />
                        </div>
                        <i class="icon-arrow-down"></i>
                    </div>
                </div>

                <div class="activity-row">
                    <p class="form-item-label is-required">意向职位</p>
                    <div class="weui-cell">
                        <div class="weui-cell__bd">
                            <input
                                    id="jobCategory"
                                    class="weui-input weui-input__select open-popup"
                                    data-target="#jobCategoryPopup"
                                    name="jobCategoryId"
                                    readonly
                                    value="<?=$intentionList['jobCategoryTxt'];?>"
                                    data-values="<?=$intentionList['job_category_id'];?>"
                                    placeholder="请选择意向职位"
                                    required
                            />
                        </div>
                        <i class="icon-arrow-down"></i>
                    </div>
                </div>

                <div class="activity-row">
                    <p class="form-item-label is-required">意向城市</p>
                    <div class="weui-cell">
                        <div class="weui-cell__bd">
                            <input
                                    id="areaId"
                                    class="weui-input weui-input__select open-popup"
                                    data-target="#areaPopup"
                                    name="areaId"
                                    readonly
                                    value="<?=$intentionList['areaTxt'];?>"
                                    data-values="<?=$intentionList['area_id'];?>"
                                    placeholder="请选择意向城市"
                                    required
                            />
                        </div>
                        <i class="icon-arrow-down"></i>
                    </div>
                </div>

                <div class="activity-row">
                    <p class="form-item-label is-required">工作性质</p>
                    <div class="weui-cell">
                        <div class="weui-cell__bd">
                            <input class="weui-input weui-input__select" id="natureType" name="natureType" type="text"
                                   value="<?=$intentionList['natureTxt'];?>" data-values="<?=$intentionList['nature_type'];?>" readonly placeholder="请选择工作性质" required/>
                        </div>
                        <i class="icon-arrow-down"></i>
                    </div>
                </div>

                <div class="activity-row">
                    <p class="form-item-label is-required">期望月薪</p>
                    <div class="weui-cell">
                        <div class="weui-cell__bd">
                            <input class="weui-input weui-input__select" id="wageType" name="wageType" type="text"
                                   value="<?=$intentionList['wageTxt'];?>" data-values="<?=$intentionList['wage_type'];?>" readonly placeholder="请选择期望月薪" required/>
                        </div>
                        <i class="icon-arrow-down"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="activity-wrapper switch-resume-wrapper">
            <a class="switch-btn" href="javascript:;">收起个人信息模块</a>
        </div>

        <?php foreach($info['showMessageList'] as $item){?>

            <?php  if($item['id']==1){?>
                <div class="activity-wrapper options-wrapper">
                    <div class="wrapper-title <?php if($item['isRequired']==1){echo 'required';}?>">
                        <span>您的意向就业单位类型</span>
                        <span class="required-tips">(至少选择1项)</span>
                    </div>

                    <div class="activity-row  checkbox-content">
                        <?php foreach ($parameter['unitTypeList'] as $k=>$v):?>
                        <label class="weui-check__label">
                            <div class="weui-cell__hd">
                                <input class="weui-form-checkbox" name="unitType" value="<?=$v['k'];?>"
                                        type="checkbox"
                                       placeholder="请填写意向就业单位类型" data-validate-error="请填写必填项(*)后再提交！" <?php if($item['isRequired']==1){echo 'required';}?>/>
                                <i class="weui-icon-checkbox"></i>
                            </div>
                            <div class="weui-cell__bd weui-form-text">
                                <p><?=$v['v'];?></p>
                            </div>
                        </label>
                        <?php endforeach;?>
                    </div>
                </div>
            <?php }?>

            <?php if($item['id']==2){?>
                <div class="activity-wrapper options-wrapper">
                    <div class="wrapper-title <?php if($item['isRequired']==1){echo 'required';}?>">
                        <span>您是通过什么渠道了解到这场活动的？</span><br/>
                        <span class="required-tips">(至少选择1项)</span>
                    </div>

                    <div class="activity-row  checkbox-content">
                        <?php foreach ($parameter['channelList'] as $k=>$v):?>
                        <label class="weui-check__label">
                            <div class="weui-cell__hd">
                                <input class="weui-form-checkbox" name="channels" value="<?=$v['k']?>" type="checkbox"
                                       placeholder="请填写了解渠道" <?php if($item['isRequired']==1){echo 'required';}?>/>
                                <i class="weui-icon-checkbox"></i>
                            </div>
                            <div class="weui-cell__bd weui-form-text">
                                <p><?=$v['v']?></p>
                            </div>
                        </label>
                        <?php endforeach;?>
                    </div>
                </div>
            <?php }?>

            <?php if($item['id']==3){?>
                <div class="activity-wrapper share-wrapper">
                    <div class="wrapper-title <?php if($item['isRequired']==1){echo 'required';}?>">
                        <span>您是否愿意让高校人才网的招聘顾问根据您的专业/意向就业单位类型/意向就业城市等将您的简历推荐给其他的相关用人单位，让您的求职快人一步抢占先机？</span>
                    </div>

                    <div class="activity-row weui-cells_checkbox">
                        <label class="weui-check__label">
                            <div class="weui-cell__hd">
                                <input class="weui-check" name="isShare" value="1" type="radio" placeholder="请填写简历代投意愿" data-validate-error="请填写必填项(*)后再提交！"
                                <?php if($item['isRequired']==1){echo 'required';}?>/>
                                <i class="weui-icon-checked"></i>
                            </div>
                            <div class="weui-cell__bd weui-form-text">
                                <p>是</p>
                            </div>
                        </label>
                        <label class="weui-check__label">
                            <div class="weui-cell__hd">
                                <input class="weui-check" name="isShare" value="2" type="radio" placeholder="请填写简历代投意愿" data-validate-error="请填写必填项(*)后再提交！"
                                <?php if($item['isRequired']==1){echo 'required';}?>/>
                                <i class="weui-icon-checked"></i>
                            </div>
                            <div class="weui-cell__bd weui-form-text">
                                <p>否</p>
                            </div>
                        </label>
                    </div>
                </div>
            <?php }?>

            <?php if($item['id']==4){?>
                <div class="activity-wrapper upload-wrapper">
                    <div class="wrapper-title <?php if($item['isRequired']==1){echo 'required';}?>">
                        <span>上传附件简历</span>
                        <span class="required-tips">（大小15M以内，最多5份）</span>
                    </div>

                    <div class="activity-row">
                        <p class="form-item-label">
                            <?=$info['upload_instructions'];?>
                        </p>
                        <div class="resume-file">
                            <div class="upload-file">
                                <input id="uploadResume" type="file" />
                                <i class="icon"></i>
                                上传文件
                            </div>
                            <?php if(sizeof($parameter['resumeFileList'])>0):?>
                            <div class="select-file">
                                <input class="open-popup" id="existResume" data-target="#resumeFilePopup"/>
                                选择已有附件
                                <i class="icon"></i>
                            </div>
                            <?php endif;?>
                        </div>
                        <div class="selected-file-content"></div>
                        <input type="hidden" name="fileToken" value="" placeholder="请上传附件简历" data-validate-error="请填写必填项(*)后再提交！" <?php if($item['isRequired']==1){echo 'required';}?> />
                    </div>
                </div>
            <?php }?>

            <?php if($item['id']==5){?>
                <div class="activity-wrapper wechat-wrapper">
                    <div class="wrapper-title <?php if($item['isRequired']==1){echo 'required';}?>">
                        <span>您的微信号</span>
                    </div>

                    <div class="activity-row">
                        <p class="form-item-label"><?=$info['wechat_instructions'];?></p>
                        <div class="weui-cell">
                            <div class="weui-cell__bd">
                                <input class="weui-input weui-input__select" name="wechatNumber" type="text" value=""
                                       placeholder="请填写您的微信号，方便我们联系您" data-validate-error="请填写必填项(*)后再提交！" <?php if($item['isRequired']==1){echo 'required';}?>/>
                            </div>
                        </div>
                    </div>
                </div>
            <?php }?>

            <?php if($item['id']==6){?>
                <div class="activity-wrapper reference-wrapper">
                    <div class="wrapper-title <?php if($item['isRequired']==1){echo 'required';}?>">
                        <span>您的推荐人真实姓名及相应场次</span>
                    </div>

                    <div class="activity-row">
                        <p class="form-item-label"><?=$info['reference_instructions'];?></p>
                        <div class="weui-cell">
                            <div class="weui-cell__bd">
                                <input class="weui-input weui-input__select" name="reference" type="text" value=""
                                       placeholder="格式：推荐人真实姓名+场次，如张三+成都站" data-validate-error="请填写必填项(*)后再提交！" <?php if($item['isRequired']==1){echo 'required';}?>/>
                            </div>
                        </div>
                    </div>
                </div>
            <?php }?>

            <?php if($item['id']==7){?>
                <div class="activity-wrapper employment-wrapper">
                    <div class="wrapper-title <?php if($item['isRequired']==1){echo 'required';}?>">
                        <span>您目前的学业/就业状态是</span>
                        <span class="required-tips">(单选题)</span>
                    </div>

                    <div class="activity-row weui-cells_checkbox">
                        <?php foreach ($parameter['employmentStatusList'] as $k=>$v):?>
                        <label class="weui-check__label">
                            <div class="weui-cell__hd">
                                <input
                                    class="weui-check"
                                    name="employmentStatus"
                                    value="<?=$v['k'];?>"
                                    type="radio"
                                    placeholder="请选择目前的学业/就业状态"
                                    data-validate-error="请填写必填项(*)后再提交！"
                                    <?php if($item['isRequired']==1){echo 'required';}?>
                                />
                                <i class="weui-icon-checked"></i>
                            </div>
                            <div class="weui-cell__bd weui-form-text">
                                <p><?=$v['v'];?></p>
                            </div>
                        </label>
                        <?php endforeach;?>
                    </div>
                </div>
            <?php }?>

            <?php if($item['id']==8){?>
                <div class="activity-wrapper postdoctor-wrapper">
                    <div class="wrapper-title <?php if($item['isRequired']==1){echo 'required';}?>">
                        <span>您的博士后培养机构是</span>
                    </div>

                    <div class="activity-row">
                        <p class="form-item-label"><?=$info['postdoctor_institution_instructions'];?></p>
                        <div class="weui-cell">
                            <div class="weui-cell__bd">
                                <input
                                    class="weui-input"
                                    name="postdoctorInstitution"
                                    type="text"
                                    value=""
                                    placeholder="请填写"
                                    data-validate-error="请填写必填项(*)后再提交！"
                                    maxlength="50"
                                    <?php if($item['isRequired']==1){echo 'required';}?>
                                />
                            </div>
                        </div>
                    </div>
                </div>
            <?php }?>

            <?php if($item['id']==9){?>
                <div class="activity-wrapper working-hours-wrapper">
                    <div class="wrapper-title <?php if($item['isRequired']==1){echo 'required';}?>">
                        <span>您博士毕业后的海外连续工作时长（含海外博士后）是</span>
                        <span class="required-tips">(单选题)</span>
                    </div>

                    <div class="activity-row">
                        <p class="form-item-label"><?=$info['postdoctor_overseas_duration_instructions'];?></p>
                        <div class="weui-cells_checkbox">
                            <?php foreach ($parameter['overseasWorkingTimeList'] as $k=>$v):?>
                            <label class="weui-check__label">
                                <div class="weui-cell__hd">
                                    <input
                                        class="weui-check"
                                        name="postdoctorOverseasDuration"
                                        value="<?=$v['k'];?>"
                                        type="radio"
                                        placeholder="请选择工作时长"
                                        data-validate-error="请填写必填项(*)后再提交！"
                                        <?php if($item['isRequired']==1){echo 'required';}?>
                                    />
                                    <i class="weui-icon-checked"></i>
                                </div>
                                <div class="weui-cell__bd weui-form-text">
                                    <p><?=$v['v'];?></p>
                                </div>
                            </label>
                            <?php endforeach;?>
                        </div>
                    </div>
                </div>
            <?php }?>

        <?php }?>


        <div class="activity-end">
            <?=$info['conclusion'];?>
        </div>

        <div class="activity-submit">
            <button class="weui-btn weui-btn_primary" type="submit">提交报名</button>
            <?php if($resumeStep < 4):?>
            <div class="submit-tips">提示：报名成功后您的信息将同步至在线简历</div>
            <?php endif;?>
        </div>
    </form>

    <!-- 户籍/国籍弹窗 start -->
    <div id="householdPopup" class="weui-popup__container filter-container">
        <div class="weui-popup__overlay"></div>
        <div class="weui-popup__modal">
            <form action="" class="filter-form" is-radio>
                <div class="filter-header">
                    <span>选择户籍/国籍</span>
                    <i class="close-button close-popup"></i>
                </div>

                <div class="filter-main is-primary is-household navigation-main">
                    <ul class="navigation-nav">
                        <?php foreach($parameter['allCityAreaList'] as $k=>$v):?>
                            <li><span><?=$v['v']?></span></li>
                        <?php endforeach;?>
                    </ul>

                    <div class="navigation-box">
                        <?php foreach($parameter['allCityAreaList'] as $k=>$v):?>
                            <ul class="navigation-panel is-area">
                                <li class="panel-title"><span><?=$v['v']?></span></li>
                                <li class="panel-links filter-value">
                                    <?php foreach($v['children'] as $key=>$item):?>
                                    <label>
                                        <input type="checkbox" name="householdId" value="<?=$item['k']?>"/>
                                        <span><?=$item['v']?></span>
                                    </label>
                                    <?php endforeach;?>
                                </li>
                            </ul>
                        <?php endforeach;?>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- 户籍/国籍弹窗 end -->

    <!-- 职称弹窗 start -->
    <div id="titlePopup" class="weui-popup__container filter-container" data-limit="3">
        <div class="weui-popup__overlay"></div>
        <div class="weui-popup__modal">
            <form action="" class="filter-form" is-close-route>
                <div class="filter-header">
                    <span>选择职称</span>
                    <i class="close-button close-popup"></i>
                </div>

                <div class="filter-select">
                    <div class="label">已选(<span>0</span>/3)</div>
                    <div class="select-values"></div>
                </div>

                <div class="filter-main is-primary is-intention-of-city navigation-main">
                    <ul class="navigation-nav">
                        <?php foreach($parameter['titleList'] as $k=>$v):?>
                            <li><span><?=$v['v'];?></span></li>
                        <?php endforeach;?>
                    </ul>

                    <div class="navigation-box">
                        <?php foreach($parameter['titleList'] as $k=>$v):?>
                            <ul class="navigation-panel">
                                <li class="panel-title"><span><?=$v['v'];?></span></li>
                                <li class="panel-links filter-value">
                                    <?php foreach($v['children'] as $key=>$item):?>
                                        <label>
                                        <input type="checkbox" name="title" data-label="<?=$item['v'];?>" value="<?=$item['code'];?>"/>
                                        <span><?=$item['v'];?></span>
                                    </label>
                                    <?php endforeach;?>
                                </li>
                            </ul>
                        <?php endforeach;?>
                    </div>
                </div>

                <div class="filter-operate">
                    <button class="weui-btn" type="reset">重置</button>
                    <button class="weui-btn weui-btn_primary close-popup" type="submit">确定</button>
                </div>
            </form>
        </div>
    </div>
    <!-- 职称弹窗 end -->

    <!-- 现居住地弹窗 start -->
    <div id="residencePopup" class="weui-popup__container filter-container">
        <div class="weui-popup__overlay"></div>
        <div class="weui-popup__modal">
            <form action="" class="filter-form" is-radio>
                <div class="filter-header">
                    <span>选择现居住地</span>
                    <i class="close-button close-popup"></i>
                </div>

                <div class="filter-main is-primary is-household navigation-main">
                    <ul class="navigation-nav">
                        <?php foreach($parameter['allCityAreaList'] as $k=>$v):?>
                            <li><span><?=$v['v']?></span></li>
                        <?php endforeach;?>
                    </ul>

                    <div class="navigation-box">
                        <?php foreach($parameter['allCityAreaList'] as $k=>$v):?>
                            <ul class="navigation-panel is-area">
                                <li class="panel-title"><span><?=$v['v']?></span></li>
                                <li class="panel-links filter-value">
                                    <?php foreach($v['children'] as $key=>$item):?>
                                    <label>
                                        <input type="checkbox" name="residenceId" value="<?=$item['k']?>"/>
                                        <span><?=$item['v']?></span>
                                    </label>
                                    <?php endforeach;?>
                                </li>
                            </ul>
                        <?php endforeach;?>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- 现居住地弹窗 end -->

    <!-- 所学专业弹窗 start -->
    <div id="majorPopup" class="weui-popup__container filter-container">
        <div class="weui-popup__overlay"></div>
        <div class="weui-popup__modal">
            <form action="" class="filter-form" is-radio>
                <div class="filter-header">
                    <span>选择专业</span>
                    <i class="close-button close-popup"></i>
                </div>

                <div class="filter-main is-primary is-major-third navigation-main">
                    <ul class="navigation-nav">
                        <?php foreach($parameter['majorList'] as $v):?>
                        <li><span><?=$v['v']?></span></li>
                        <?php endforeach;?>
                    </ul>

                    <div class="navigation-box">
                        <?php foreach($parameter['majorList'] as $list):?>
                        <ul class="navigation-panel">
                            <?php foreach($list['children'] as $v):?>
                            <li class="panel-title"><span><?=$v['v'];?></span></li>
                            <li class="panel-links filter-value">
                                <?php foreach($v['children'] as $item):?>
                                <label>
                                    <input type="checkbox" name="mojor" value="<?=$item['k'];?>" />
                                    <span><?=$item['v'];?></span>
                                </label>
                                <?php endforeach;?>
                            </li>
                            <?php endforeach;?>
                        </ul>
                        <?php endforeach;?>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- 所学专业弹窗 end -->

    <!-- 意向职位弹窗 start -->
    <div id="jobCategoryPopup" class="weui-popup__container filter-container job-category-popup">
        <div class="weui-popup__overlay"></div>
        <div class="weui-popup__modal">
            <form action="" class="filter-form" is-radio>
                <div class="filter-header">
                    <span>选择职位</span>
                    <i class="close-button close-popup"></i>
                </div>

                <div class="filter-main is-primary is-job-category navigation-main">
                    <ul class="navigation-nav">
                        <?php foreach($parameter['categoryJobList'] as $k=>$v):?>
                            <?php if($k==0):?>
                                <li class="is-active"><span><?=$v['v']?></span></li>
                            <?php else:?>
                                <li><span><?=$v['v']?></span></li>
                            <?php endif;?>
                        <?php endforeach;?>
                    </ul>

                    <div class="navigation-box">
                        <?php foreach($parameter['categoryJobList'] as $k=>$v):?>
                        <ul class="navigation-panel">
                            <li class="panel-title"><span><?=$v['v'];?></span></li>
                            <li class="panel-links filter-value">
                                <?php foreach($v['children'] as $key=>$item):?>
                                <label>
                                    <input type="checkbox" name="jobCategory" data-label="<?=$item['v']?>" value="<?=$item['k']?>"/>
                                    <span><?=$item['v']?></span>
                                </label>
                                <?php endforeach;?>
                            </li>
                        </ul>
                        <?php endforeach;?>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- 意向职位弹窗 end -->

    <!-- 意向城市弹窗 start -->
    <div id="areaPopup" class="weui-popup__container filter-container">
        <div class="weui-popup__overlay"></div>
        <div class="weui-popup__modal">
            <form action="" class="filter-form" is-close-route>
                <div class="filter-header">
                    <span>选择意向城市</span>
                    <i class="close-button close-popup"></i>
                </div>

                <div class="filter-select">
                    <div class="label">已选(<span>0</span>/5)</div>
                    <div class="select-values"></div>
                </div>

                <div class="filter-main is-primary is-intention-of-city navigation-main">
                    <ul class="navigation-nav">
                        <?php foreach($parameter['areaList'] as $k=>$v):?>
                        <li><span><?=$v['v']?></span></li>
                        <?php endforeach;?>
                    </ul>

                    <div class="navigation-box">
                        <?php foreach($parameter['areaList'] as $k=>$v):?>
                        <ul class="navigation-panel">
                            <li class="panel-title"><span><?=$v['v']?></span></li>
                            <li class="panel-links filter-value">
                                <?php foreach($v['children'] as $key=>$item):?>
                                <label>
                                    <input type="checkbox" name="area" data-label="<?=$item['v'];?>" value="<?=$item['k'];?>"/>
                                    <span><?=$item['v'];?></span>
                                </label>
                                <?php endforeach;?>
                            </li>
                        </ul>
                        <?php endforeach;?>
                    </div>
                </div>

                <div class="filter-operate">
                    <button class="weui-btn" type="reset">重置</button>
                    <button class="weui-btn weui-btn_primary close-popup" type="submit">确定</button>
                </div>
            </form>
        </div>
    </div>
    <!-- 意向城市弹窗 end -->

    <!-- 已有附件简历弹窗 start -->
    <div id="resumeFilePopup" class="weui-popup__container filter-container">
        <div class="weui-popup__overlay"></div>
        <div class="weui-popup__modal">
            <form action="" class="filter-form" is-close-route data-limit="5">
                <div class="filter-header">
                    <span>请选择</span>
                    <i class="close-button close-popup"></i>
                </div>

                <div class="filter-main is-checkbox">
                    <ul class="checkbox-content">
                        <?php foreach($parameter['resumeFileList'] as $k=>$v):?>
                        <li class="checkbox-list">
                            <label class="weui-check__label checkbox-label">
                                <div class="weui-cell__hd">
                                    <input class="weui-form-checkbox" name="resumeFileToken" value="<?=$v['token'];?>"
                                           data-label="<?=$v['file_name'];?>" type="checkbox"/>
                                    <i class="weui-icon-checkbox"></i>
                                </div>
                                <div class="weui-cell__bd weui-form-text"><?=$v['file_name'];?></div>
                            </label>
                        </li>
                        <?php endforeach;?>
                    </ul>
                </div>

                <div class="filter-operate">
                    <button class="weui-btn weui-btn_primary close-popup" type="submit">确定</button>
                </div>
            </form>
        </div>
    </div>
    <!-- 已有附件简历弹窗 end -->
</div>

<div class="non-login">
    <button>开始报名</button>
</div>

<script src="/static/js/validation.js"></script>
<script>
    $(function () {
        var activityFormId = "<?=$info['id']?>"
        var resumeStep = '<?=$resumeStep;?>'
        var isLogin = "<?=$isLogin?>" == 1 
        var maxCount = "<?=$info['max_count']?>"
        var myMaxCount = "<?=$info['myMaxCount']?>"
        var optionTipsErrorMessage = "<?=$info['optionTipsErrorMessage']?>"
        var optionId = "<?=$optionId?>"

        var resumeFileList = []

        if (isMiniapp()) {
            $('header.header-container').hide()
        }

        // 1通过报名表单链接访问，2 通过专题页访问
        var fromType = (function () {
            var href = window.location.href
            if (/fromType=\d+/.test(href)) {
                var search = window.location.search
                var URLParams = new URLSearchParams(search)
                return URLParams.get('fromType')
            } else {
                return ''
            }
        })()

        if (!isLogin) {
            $('.non-login').show()
            $('.activity-form').hide()
            $('.activity-container').css('padding-bottom', '75px')
        }

        $('.non-login button').on('click', function () {
            window.localStorage.setItem('redirect', window.location.href)
            toastText('请登录后再进行活动报名！', 3000, function () {
                window.signupPopup.show()
            })
        })
        $('.intention-option').on('click', function () {
            // 触发点击方法
            if (!optionId && maxCount != '0') {
                // 看一下现在选中了多少个 + 已报名的
                var selectedCount = $('.intention-option:checked').length
                if (selectedCount > myMaxCount) {
                    $.alert(optionTipsErrorMessage,"提示");
                    return false
                }
            }

        })

        var $resumeSteps = $('.resume-steps')
        var $resumeStepsIsShowBtn = $('.switch-resume-wrapper .switch-btn')

        function switchResumeToggle() {
            if (+resumeStep > 3) {
                $resumeSteps.removeClass('is-show')
                $resumeStepsIsShowBtn.text('展开个人信息模块')
                $resumeStepsIsShowBtn.removeClass('is-reverse')
            } else {
                $resumeSteps.addClass('is-show')
                $resumeStepsIsShowBtn.text('收起个人信息模块')
                $resumeStepsIsShowBtn.addClass('is-reverse')
            }
        }

        switchResumeToggle()

        $resumeStepsIsShowBtn.on('click', function () {
            $(this).toggleClass('is-reverse')
            $resumeSteps.toggleClass('is-show')

            $(this).text($(this).hasClass('is-reverse') ? '收起个人信息模块' : '展开个人信息模块')
        })

        // 出生日期
        var $birthday = $('#birthday')

        var birthdayPicker = $birthday.picker({
            title: '出生日期',
            cols: pickerDefaultCols,
            value: formatPickerValue($birthday.val())
        })

        // 户籍/国籍
        var householdRegisterEl = $('input[name="householdRegisterId"]')
        var householdRegisterId = householdRegisterEl.attr('data-values')
        filterCascaderPopup(householdRegisterId ? [householdRegisterId] : [], '#householdPopup', '#householdPopup', 'householdId', function (data) {
            var {value, label} = data
            householdRegisterEl.val(label)
            householdRegisterEl.attr('data-values', value)
        })

        // 政治面貌
        var $politicalStatusId = $('#politicalStatusId')
        inputSelector($politicalStatusId, [
            <?php foreach($parameter['politicalList'] as $k=>$v):?>
            {title: '<?=$v['v']?>', value: '<?=$v['k']?>'},
            <?php endforeach;?>
        ])

        // 职称
        var titleEl = $('input[name="titleId"]')
        var titleId = titleEl.attr('data-values')
        filterCascaderPopup(titleId ? titleId.split(',') : [], '#titlePopup', '#titlePopup', 'title', function (data) {
            var {length} = data
            if (!length) {
                titleEl.val('')
                titleEl.attr('data-values', '')
                return
            }
            var valueObj = data.reduce((previous, current) => {
                var {value, label} = previous
                return {
                    value: value + ',' + current.value,
                    label: label + ',' + current.label
                }
            })
            titleEl.val(valueObj.label)
            titleEl.attr('data-values', valueObj.value)
        })

        // 现居住地
        var residenceEl = $('input[name="residence"]')
        var residenceId = residenceEl.attr('data-values')
        filterCascaderPopup(residenceId ? [residenceId] : [], '#residencePopup', '#residencePopup', 'residenceId', function (data) {
            var { value, label } = data
            residenceEl.val(label)
            residenceEl.attr('data-values', value)
        })

        // 学历水平
        // var isMajorCustom = "<?= $educationInfo['majorCustom']?:''?>" ? true : false
        // var educationId = "<?= $educationInfo['educationId']?:''?>"
        var isMajorCustom = "<?= $educationList['major_custom']?:''?>" ? true : false
        var educationId = "<?=$educationList['education_id'];?>"
        var $majorCustom = $('.major-custom')
        var $majorSelect = $('.major-select')
        var $majorSwitch = $('.switch-major')

        function handleMajorCustomOperate(isCustom) {
            var majorSwitchHtml = isCustom ? '点击恢复<span class="switch-btn">选择专业</span>' : '若无您所需专业，请点击<span class="switch-btn">自定义录入专业</span>'
            var selectValue = isCustom ? 'hide' : 'show'
            var customValue = isCustom ? 'show' : 'hide'

            $majorCustom.prop('required', isCustom)
            $majorSelect.prop('required', !isCustom)

            $majorSwitch.html(majorSwitchHtml)
            $majorSelect[selectValue]()
            $majorCustom[customValue]()
            $('.major-row .icon-arrow-down')[isCustom ? 'hide' : 'show']()
        }

        function handleEducationChage(educationId) {
            var showMajorCustomEducationIds = ['1', '2', '5']
            var isShow = showMajorCustomEducationIds.includes(educationId)

            if (isShow) {
                $majorSwitch.show()
            } else {
                $majorSwitch.hide()

                isMajorCustom = false
                handleMajorCustomOperate(false)
            }
        }

        var $educationId = $('#educationId')
        inputSelector(
            $educationId,
            [
                <?php foreach($parameter['educationList'] as $k=>$v):?>
                {title: '<?=$v['v'];?>', value: '<?=$v['k'];?>'},
                <?php endforeach;?>
            ],
            function (data) {
                handleEducationChage(data.values)
            }
        )

        // 就读时间
        var $studyBeginDate = $('#studyBeginDate')
        var $studyEndDate = $('#studyEndDate')

        var studyBeginDatePicker = $studyBeginDate.picker({
            title: '就读时间',
            cols: pickerDefaultCols,
            value: formatPickerValue($studyBeginDate.val())
        })

        var studyEndDatePicker = $studyEndDate.picker({
            title: '就读时间',
            cols: pickerEightYearLaterCols,
            value: formatPickerValue($studyEndDate.val())
        })

        // 所学专业
        var majorEl = $('input[name="majorId"]')
        var majorId = majorEl.attr('data-values')
        filterCascaderPopup(majorId ? [majorId] : [], '#majorPopup', '#majorPopup', 'mojor', function (data) {
            var {value, label} = data
            majorEl.val(label)
            majorEl.attr('data-values', value)
        })
        $majorSwitch.on('click', '.switch-btn', function () {
            isMajorCustom = !isMajorCustom
            handleMajorCustomOperate(isMajorCustom)
        })
        handleEducationChage(educationId)
        handleMajorCustomOperate(isMajorCustom)

        // 求职状态
        var $workStatus = $('#workStatus')
        var workStatusSelector = $workStatus.select({
            closeText: '取消',
            items: [
                <?php foreach($parameter['jobStatusList'] as $k=>$v):?>
                {title: '<?=$v['v'];?>', value: '<?=$v['k'];?>'},
                <?php endforeach;?>
            ]
        })

        // 到岗时间
        var $arriveDateType = $('#arriveDateType')
        var arriveDateTypeSelector = $arriveDateType.select({
            closeText: '取消',
            items: [
                <?php foreach($parameter['arriveDateList'] as $k=>$v):?>
                    {title: '<?=$v['v'];?>', value: '<?=$v['k'];?>'},
                <?php endforeach;?>
            ]
        })

        // 意向职位
        var jobCategoryEl = $('input[name="jobCategoryId"]')
        var jobCategoryId = jobCategoryEl.attr('data-values')
        filterCascaderPopup(jobCategoryId ? [jobCategoryId] : [], '#jobCategoryPopup', '#jobCategoryPopup', 'jobCategory', function (data) {
            var { value, label } = data
            jobCategoryEl.val(label)
            jobCategoryEl.attr('data-values', value)
        })

        // 意向城市
        var areaEl = $('input[name="areaId"]')
        var areaId = areaEl.attr('data-values')
        filterCascaderPopup(areaId ? areaId.split(',') : [], '#areaPopup', '#areaPopup', 'area', function (data) {
            var value = data.map((item) => item.value).join()
            var label = data.map((item) => item.label).join()
            areaEl.val(label)
            areaEl.attr('data-values', value)
        })

        // 工作性质
        var $natureType = $('#natureType')
        var natureTypeSelector = $natureType.select({
            closeText: '取消',
            items: [
                <?php foreach($parameter['natureList'] as $k=>$v):?>
                {title: '<?=$v['v'];?>', value: '<?=$v['k'];?>'},
                <?php endforeach;?>
            ]
        })

        // 期望月薪
        var $wageType = $('#wageType')
        var wageTypeSelector = $wageType.select({
            closeText: '取消',
            items: [
                <?php foreach($parameter['wageList'] as $k=>$v):?>
                {title: '<?=$v['v'];?>', value: '<?=$v['k'];?>'},
                <?php endforeach;?>
            ]
        })

        // 生成附件列表
        var $selectResumeFile = $('.selected-file-content')
        var $resumeFileInput = $('input[name="fileToken"]')
        var $resumeFilePopup = $('#resumeFilePopup')
        var $resumeFileForm = $resumeFilePopup.find('.filter-form')

        function createResumeListHTML(file) {
            var resumeFileValues = $resumeFileInput.val()
            var valueArray = resumeFileValues ? resumeFileValues.split(',') : []
            var name = file.name
            var token = file.token
            var nameArray = name.split('.')
            var length = nameArray.length
            var fileType = nameArray[length - 1]
            var type = file.type
            var isIncludes = valueArray.includes(token)

            if (isIncludes) return

            if (type === 'upload') {
                var dataLimit = $resumeFileForm.attr('data-limit')
                $resumeFileForm.attr('data-limit', dataLimit - 1)
            }

            var fileListHTML = `
                    <div class="list">
                        <span class="file-name">${name}</span>
                        <i class="icon-close" data-type="${type}" data-token="${token}"></i>
                    </div>`
            $selectResumeFile.append(fileListHTML)
            valueArray.push(token)
            $resumeFileInput.val(valueArray.join())
            if (valueArray.length >= 5) {
                 $('#uploadResume').prop('disabled', true)
            } else {
                $('#uploadResume').prop('disabled', false)
            }
        }

        // 已有附件
        filterCheckboxPopup('#resumeFilePopup', '#resumeFilePopup', 'resumeFileToken', function (data) {
            $.each(data, function (index, file) {
                var name = file.label
                var token = file.value

                var resumeFileValues = $resumeFileInput.val()
                var valueArray = resumeFileValues ? resumeFileValues.split(',') : []
                if (!valueArray.includes(token)) {
                    resumeFileList.push({ value: token, uploadType: 2 })
                }

                createResumeListHTML({name, token, type: 'exist'})
                var $selectItem = $resumeFilePopup.find('input[value="' + token + '"]')
                $selectItem.prop('disabled', true)
            })
        })

        // 上传附件简历
        $('#uploadResume').on('change', function () {
            var file = this.files[0]
            var size = file.size
            var maxSize = 15 * 1024 * 1024
            if (size > maxSize) {
                toastText('大小15M以内')
                return
            }
            var formData = new FormData()
            formData.append('file', file)

            httpPost('/upload/activity', formData).then(function (res) {
                var data = res
                createResumeListHTML({token: data.id, name: data.name, type: 'upload'})
                resumeFileList.push({ value: data.id, uploadType: 1 })
            })
        })

        // 删除附件简历
        var $existResume = $('#existResume')
        $selectResumeFile.on('click', '.icon-close', function () {
            var $this = $(this)
            var token = $this.attr('data-token')
            var type = $this.attr('data-type')

            var resumeFileValues = $resumeFileInput.val()
            var valueArray = resumeFileValues ? resumeFileValues.split(',') : []
            valueArray = valueArray.filter(function (item) {
                return item !== token
            })
            $resumeFileInput.val(valueArray.join())

            var existResumeCheckedValues = $existResume.attr('data-values')
            var existResumeCheckedArray = existResumeCheckedValues ? existResumeCheckedValues.split(',') : []
            existResumeCheckedArray = existResumeCheckedArray.filter(function (item) {
                return item !== token
            })
            $existResume.attr('data-values', existResumeCheckedArray.join())

            if (type === 'upload') {
                var dataLimit = $resumeFileForm.attr('data-limit')
                $resumeFileForm.attr('data-limit', dataLimit + 1)
            } else {
                var $recovery = $resumeFilePopup.find('input[value="' + token + '"]')
                $recovery.prop('disabled', false)
            }
            $this.parent('.list').remove()
            resumeFileList = resumeFileList.filter(function (file) {
                return file.value != token
            })
            $('#uploadResume').prop('disabled', false)
        })

        resumeOptimization(
            function (formEl, formData) {
                var postData = formData
                var api = '/activity-form-registration-form/registration-form'

                postData.isRecruitment = postData.isRecruitment ? 1 : 2
                postData.isProjectSchool = postData.isProjectSchool ? 1 : 2
                postData.isAbroad = postData.isAbroad ? 1 : 2

                postData.beginDate = trimSpace(postData.beginDate)
                postData.endDate = trimSpace(postData.endDate)
                postData.birthday = trimSpace(postData.birthday)

                postData[isMajorCustom ? 'majorId' : 'majorCustom'] = ''

                Object.keys(postData).forEach(function (key) {
                    var value = postData[key]
                    postData[key] = Array.isArray(value) ? value.join() : value
                })

                var mobile = postData.mobile
                var email = postData.email

                if (!isMobile(mobile)) {
                    toastText('手机号格式有误')
                    return
                }
                if (!isEmail(email)) {
                    toastText('联系邮箱格式有误')
                    return
                }

                postData['fileToken'] = resumeFileList

                httpPost(api, postData).then(function () {
                    window.location.replace(`/activity-form-registration-form/registration-form-success?activityFormId=${activityFormId}&resumeStep=${resumeStep}&fromType=${fromType}`)
                })
            },
            function () {
            },
            '.activity-form',
            '',
            '',
            function (invalidFields) {
                var resumeStepsValidKeyWords = [
                    'name',
                    'gender',
                    'birthday',
                    'householdRegisterId',
                    'politicalStatusId',
                    'mobile',
                    'email',
                    'school',
                    'educationId',
                    'beginDate',
                    'endDate',
                    'majorId',
                    'majorCustom',
                    'workStatus',
                    'arriveDateType',
                    'jobCategoryId',
                    'natureType',
                    'areaId',
                    'wageType'
                ]
                var invalid = invalidFields.some(function (item) {
                    return resumeStepsValidKeyWords.includes(item.target.prop('name'))
                })
                if (invalid) {
                    $resumeSteps.addClass('is-show')
                    $resumeStepsIsShowBtn.text('收起个人信息模块')
                    $resumeStepsIsShowBtn.addClass('is-reverse')
                }
            }
        )
    })
</script>
