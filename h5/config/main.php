<?php
$params = array_merge(require __DIR__ . '/../../common/config/params.php',
    require __DIR__ . '/../../common/config/params-local.php', require __DIR__ . '/params.php',
    require __DIR__ . '/params-local.php');

return [
    'id'                  => 'app-h5',
    'basePath'            => dirname(__DIR__),
    'controllerNamespace' => 'h5\controllers',
    'bootstrap'           => ['log'],
    'defaultRoute'        => 'home',
    'components'          => [
        'request' => [
            'csrfParam' => '_csrf-h5',
        ],
        'session' => [
            // this is the name of the session cookie used for login on the frontendPc
            'name' => 'advanced-h5',
        ],

        'user'         => [
            'class'           => 'yii\web\User',
            'identityClass'   => 'common\base\models\BaseMember',
            'enableAutoLogin' => true,
            'identityCookie'  => [
                'name'     => '_identity-h5',
                'httpOnly' => true,
            ],
            'idParam'         => '__member',
        ],
        'errorHandler' => [
            'errorAction' => 'site/error',
        ],

        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName'  => false,
            'rules'           => [
                // 详情,做成伪静态
                'index.html'                                         => 'home/index',
                'job/detail/<id:\d+>.html'                           => 'job/detail',
                'company/detail/<id:\d+>.html'                       => 'company/detail',
                'company/detail/<id:\d+>_a.html'                     => 'company/detail-announcement-list',
                'company/detail/<id:\d+>_j.html'                     => 'company/detail-job-list',
                'company/detail/<id:\d+>_h.html'                     => 'company/detail-activity-list',
                'news/detail/<id:\d+>.html'                          => 'news/detail',
                'daily/detail/<id:\d+>.html'                         => 'home/daily-summary-detail',
                'announcement/detail/<id:\d+>.html'                  => 'announcement/detail',
                'announcement/preview/<id:\d+>'                      => 'announcement/preview',
                'column/<id:\d+>.html'                               => 'home/column',
                'view.php'                                           => 'home/old-article',
                'list.php'                                           => 'home/old-column',
                'freelist.php'                                       => 'home/old-article',
                'major.html'                                         => 'home/major-column',
                '1.gif'                                              => 'showcase-browse-log/add-showcase-browse-log',
                'region.html'                                        => 'home/area-column',
                'plus/count.php'                                     => 'home/old-count',
                'daily.html'                                         => 'home/daily',
                'search'                                             => 'home/search',
                'search-result'                                      => 'home/search-result',
                'person'                                             => 'home/person',
                'setting'                                            => 'home/setting',
                'code-login'                                         => 'home/code-login',
                'login'                                              => 'home/login',
                'reset-password'                                     => 'home/reset-password',
                'rczhaopin/?'                                        => 'engine/index',
                'rczhaopin/<p:p[\d]+>/?'                             => 'engine/index',
                'rczhaopin/<tj:tj_[^\n]+>/?'                         => 'engine/index',
                'rczhaopin/<p:p[\d]+>/<tj:tj_[^\n]+>/?'              => 'engine/index',
                'rczhaopin/<level1:\w+>/?'                           => 'engine/index',
                'rczhaopin/<level1:\w+>/<p:p[\d]+>/?'                => 'engine/index',
                'rczhaopin/<level1:\w+>/<tj:tj_[^\n]+>/?'            => 'engine/index',
                'rczhaopin/<level1:\w+>/<p:p[\d]+>/<tj:tj_[^\n]+>/?' => 'engine/index',
                'rczhaopin/<level1:\w+>/<level2:\w+>/?'              => 'engine/index',
                'rczhaopin/<level1:\w+>/<level2:\w+>/<p:p[\d]+>/?'   => 'engine/index',
                'a/<token:\w+>/'                                     => 'activity/index',
                // 支付回调(H5)
                'payment/notify/payway/<payway:\d+>'                 => 'payment/notify',
                // 公告分析报告
                'announcement/report/<id:\d+>.html'                  => 'announcement/report',
                // 职位分析报告
                'job/report/<id:\d+>.html'                           => 'job/report',
                // vip介绍页
                'vip.html'                                           => 'introduction-page/vip',
                // 竞争力介绍页
                'competitive-power.html'                             => 'introduction-page/competition',
                // vip介绍页(活动)
                'activity-vip.html'                                  => 'introduction-page/activity-vip',
                // 求职快介绍页
                'job-fast.html'                                      => 'introduction-page/job-fast',
                //                '3.gif'                                              => 'showcase-browse-log/add-pay-buried-point-log',
                // 关键字热词搜索
                '/hotword/<code:[a-z0-9]+>'                          => 'hot-word/index',
                'n/<token:[\w-]+>/'                                  => 'new-resume-activity/accept-detail',
                'bk_jobs/<code:[a-z0-9A-Z]+>/?'                        => 'seo-job-wiki/index',
            ],
        ],
    ],
    'params'              => $params,
];
