<?php
namespace h5\controllers;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseArticleColumn;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCompanyResumePvTotal;
use common\base\models\BaseDailyAnnouncementSummary;
use common\base\models\BaseDictionary;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomePosition;
use common\base\models\BaseJob;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseMemberLoginForm;
use common\base\models\BaseNews;
use common\base\models\BaseResumeComplete;
use common\base\models\BaseResumeEquityPackage;
use common\base\models\BaseResumeEquityPackageCategorySetting;
use common\base\models\BaseResumeEquitySetting;
use common\base\models\BaseShowcase;
use common\base\models\BaseResume;
use common\helpers\ArrayHelper;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\libs\BaiduTimeFactor;
use common\libs\Cache;
use common\libs\Captcha;
use common\libs\EmailQueue;
use common\libs\HomeColumnCache;
use common\libs\SmsQueue;
use common\libs\ToutiaoTimeFactor;
use common\libs\WxMiniApp;
use common\models\GxjobArctype;
use frontendPc\models\ResumeEquity;
use frontendPc\models\ResumeEquitySetting;
use h5\models\Announcement;
use h5\models\DailyAnnouncementSummary;
use h5\models\HomeColumn;
use h5\models\HomePosition;
use h5\models\Member;
use h5\models\News;
use h5\models\Resume;
use Yii;
use yii\base\BaseObject;
use yii\base\Exception;
use yii\helpers\Url;
use yii\web\Response;
use function GuzzleHttp\Psr7\build_query;

class HomeController extends BaseH5Controller
{
    /**
     * 首页
     * @return string
     */
    public function actionIndex()
    {
        $this->checkFromBaiduSearch();

        $cacheKey = HomeColumnCache::getKey(HomeColumnCache::TYPE_H5_HOME);

        $isUpdateCache = false;
        if (Yii::$app->request->get(HomeColumnCache::CACHE_URL_KEY)) {
            $isUpdateCache = true;
        }

        $this->setSeo(['title' => Yii::$app->params['seo']['home']['title']]);

        $rs = Cache::get($cacheKey);
        if ($rs && !$isUpdateCache) {
            $data = json_decode($rs, true);
        } else {
            //获取首页所有的广告信息
            $showcaseList = HomePosition::getAllShowcase('index');

            foreach ($showcaseList as $number => $showcase) {
                switch ($number) {
                    case 'B1_m':
                    case 'B2_m':
                    case 'A3_m':
                    case 'C1_m':
                    case 'C4_m':
                    case 'C5_m':
                    case 'HF_m':
                        $showcaseBrowseList[$number] = $this->renderPartial('position/' . $number . '.html', [
                            'showcase' => $showcase,
                        ]);
                        break;
                }
            }
            //获取所有首页头条
            $allHead = HomePosition::getHeadAnnouncement();
            //获取头条部分5条
            $headAnnouncement = array_slice($allHead, 0, 5);
            //获取热门公告
            $c4Head = array_slice($allHead, 5, 27);

            //获取热门公告（分三个子栏目：三日热门、一周热门、一月热门，切换查看，分别展示最近3天、7天、30天页面"浏览量"最高的10条公告信息）
            $threeDayHotAnnouncementList = Announcement::getHotList(3, 10);
            $weekHotAnnouncementList     = Announcement::getHotList(7, 10);
            $monthHotAnnouncementList    = Announcement::getHotList(30, 10);

            //获取最新更新，获取所有栏目下的公告，按时间倒序展示9条，下来展示更多，最多限制200条
            $newestAnnouncementList = Announcement::getNewestList(1);
            //获取每日汇总，按发布时间倒序，展示最新发布的前27条每日汇总信息（暂时展示9条，然后跳走）
            $dailyList = DailyAnnouncementSummary::getHomeList();

            $depthInvestigationList = News::getDeepHomeList(1);
            //这里是写死的，因为深度观察调用的是属性，没有栏目id
            $depthInvestigationUrl = Url::toRoute([
                '/home/<USER>',
                'id' => 279,
            ]);

            $strategyColumnInfo = Yii::$app->params['homePosition']['index']['columnContent']['strategy'];

            //            $strategyList = News::getStrategyList(1, $strategyColumnInfo['id']);
            $strategyList = BaseArticleColumn::getForHomeByColumnId($strategyColumnInfo['id'], 9);

            foreach ($strategyList as &$item) {
                $item['url']         = HomePosition::getDetailUrl($item['type'], $item['id']);
                $item['refreshTime'] = TimeHelper::short($item['refresh_time']);
            }

            $strategyUrl = Url::toRoute([
                '/home/<USER>',
                'id' => $strategyColumnInfo['id'],
            ]);

            $data = array_merge([
                'showcase'                    => $showcase,
                'headAnnouncement'            => $headAnnouncement,
                'c4Head'                      => $c4Head,
                'threeDayHotAnnouncementList' => $threeDayHotAnnouncementList,
                'weekHotAnnouncementList'     => $weekHotAnnouncementList,
                'monthHotAnnouncementList'    => $monthHotAnnouncementList,
                'dailyList'                   => $dailyList,
                'strategyList'                => $strategyList,
                'strategyUrl'                 => $strategyUrl,
                'depthInvestigationList'      => $depthInvestigationList,
                'depthInvestigationUrl'       => $depthInvestigationUrl,
                'newestAnnouncementList'      => $newestAnnouncementList,
            ], $showcaseBrowseList);

            Cache::set($cacheKey, json_encode($data, true));
        }
        //        $listShowcaseInfo         = BaseJob::getListShowcaseInfo(count($data['newestAnnouncementList']),
        //            Yii::$app->user->id, BaseJob::VIP_SHOWCASE_POSITION_TYPE_H5_INDEX);
        //        $data['listShowcaseInfo'] = $listShowcaseInfo;

        return $this->render('index.html', $data);
    }

    public function actionColumn()
    {
        //这里要接栏目ID，暂无链接过来
        $id = Yii::$app->request->get('id');
        if (!$id) {
            $this->notFound();
        }
        $model = HomeColumn::findOne($id);
        if (!$model) {
            $this->notFound();
        }

        if ($model->status != 1) {
            $this->notFound();
        }

        if (in_array($id, BaseHomeColumn::OLD_ABROAD_ID)) {
            $newId = BaseHomeColumn::ABROAD_HOME_ID;
            $url   = HomeColumn::getDetailUrl($newId);

            return $this->redirect($url);
        }

        return $this->distributeColumn($model);
    }

    public function actionOldColumn()
    {
        $tid       = Yii::$app->request->get('tid');
        $oldColumn = GxjobArctype::findOne($tid);

        $path = str_replace('{cmspath}/', '', $oldColumn->typedir) . '/';
        if ($path == 'zhaopin/diqu/') {
            return $this->redirect('home/area-column');
        }
        if ($path == 'zhaopin/xuqiuxueke/') {
            return $this->actionMajorColumn();
        }

        $homeColumn = HomeColumn::findOne(['path' => $path]);

        if (!$homeColumn) {
            $this->notFound();
        }

        $homeColumn = HomeColumn::findOne(['path' => $path]);
        $url        = HomeColumn::getDetailUrl($homeColumn->id);

        return $this->redirect($url, 301);
    }

    public function actionOldCount()
    {
        // 随机100到500之间的数字
        $count = rand(100, 500);
        echo "document.write('$count');";
        exit;
        // 找到?后面的参数
        $url   = Yii::$app->request->getUrl();
        $v1Url = Yii::$app->params['v1PcUrl'] . $url;

        $content = file_get_contents($v1Url);
        $content = iconv('GBK', 'UTF-8', $content);

        echo $content;
        exit;
    }

    public function actionOldArticle()
    {
        $id      = Yii::$app->request->get('aid');
        $oldUrl  = Yii::$app->params['v1H5Url'];
        $url     = "$oldUrl/view.php?aid=$id";
        $content = file_get_contents($url);
        $content = iconv('GBK', 'UTF-8', $content);

        // 先把v1连接替换成现在的网址连接
        $content = str_replace('v1.m.gaoxiaojob.com', 'm.gaoxiaojob.com', $content);

        $content = str_replace('type="text/javascript" src="/', 'type="text/javascript" src="//v1.m.gaoxiaojob.com/',
            $content);
        $content = str_replace('rel="stylesheet" media="screen" href="/',
            'rel="stylesheet" media="screen" href="//v1.m.gaoxiaojob.com/', $content);

        echo $content;
        exit;
    }

    public function actionOldFarticle()
    {
        $params = Yii::$app->request->get();

        $oldUrl = Yii::$app->params['v1H5Url'];
        // 组成url

        $params  = http_build_query($params);
        $url     = "$oldUrl/freelist.php?$params";
        $content = file_get_contents($url);
        $content = iconv('GBK', 'UTF-8', $content);

        $content = str_replace('v1.m.gaoxiaojob.com', 'm.gaoxiaojob.com', $content);

        $content = str_replace('type="text/javascript" src="/', 'type="text/javascript" src="//v1.m.gaoxiaojob.com/',
            $content);
        $content = str_replace('rel="stylesheet" media="screen" href="/',
            'rel="stylesheet" media="screen" href="//v1.m.gaoxiaojob.com/', $content);

        echo $content;
        exit;
    }

    private function distributeColumn(HomeColumn $homeColumn)
    {
        $id = $homeColumn->id;
        // 海外的直接跳过
        // if (in_array($id, BaseHomeColumn::ABROAD_ID)) {
        //     $this->notFound();
        // }

        if (in_array($id, BaseHomeColumn::AREA_ID_LIST)) {
            // 地区栏目
            $seoConfig = Yii::$app->params['seo']['areaColumn'];
        } elseif (in_array($id, BaseHomeColumn::MAJOR_ID_LIST)) {
            // 学科栏目
            $seoConfig = Yii::$app->params['seo']['majorColumn'];
        } else {
            $seoConfig = Yii::$app->params['seo']['column'];
        }
        $seoTitle       = $homeColumn->seo_title ?: str_replace('【栏目名称】', $homeColumn->name, $seoConfig['title']);
        $seoKeywords    = $homeColumn->seo_keywords ?: str_replace('【栏目名称】', $homeColumn->name,
            $seoConfig['keywords']);
        $seoDescription = $homeColumn->seo_description ?: str_replace('【栏目名称】', $homeColumn->name,
            $seoConfig['description']);

        $this->setSeo([
            'title'       => $seoTitle,
            'keywords'    => $seoKeywords,
            'description' => $seoDescription,
        ]);

        $cacheKey = HomeColumnCache::getKey(HomeColumnCache::TYPE_H5_COLUMN, $id);

        $isUpdateCache = false;
        if (Yii::$app->request->get(HomeColumnCache::CACHE_URL_KEY)) {
            $isUpdateCache = true;
        }

        $data = Cache::get($cacheKey);

        if ($data && !$isUpdateCache) {
            $data = json_decode($data, true);
            switch ($homeColumn->template_type) {
                case BaseHomeColumn::TEMPLATE_TYPE_NEWS:
                    // 资讯
                    //判断资讯是一级还是二级页面
                    if ($homeColumn->level == BaseHomeColumn::LEVEL_FIRST) {
                        //一级页面
                        $showHtml = 'news-column.html';
                    } elseif ($homeColumn->level == BaseHomeColumn::LEVEL_SECOND) {
                        $showHtml = 'second-column-list.html';
                    }

                    break;

                case BaseHomeColumn::TEMPLATE_TYPE_LEVEL1_A:
                case BaseHomeColumn::TEMPLATE_TYPE_LEVEL1_B:
                    $showHtml = 'announcement-column.html';
                    break;
                case BaseHomeColumn::TEMPLATE_TYPE_LEVEL2   :
                    //二级公告栏目
                    //判断是否有参数查询，如果有的话，还是需要走一次查询
                    $searchData = Yii::$app->request->get();
                    if (!empty($searchData['areaId']) || !empty($searchData['majorId']) || !empty($searchData['educationId']) || !empty($searchData['jobCategoryId']) || !empty($searchData['isEstablishment']) || !empty($searchData['announcementHeat'])) {
                        $data = $this->secondColumn($homeColumn);
                    }

                    $showHtml = 'second-column-list.html';
                    break;
                case BaseHomeColumn::TEMPLATE_TYPE_AREA:
                default:
                    //判断地区是一级还是二级页面
                    if ($homeColumn->level == BaseHomeColumn::LEVEL_FIRST) {
                        //一级页面
                        $showHtml = 'announcement-column.html';
                    } elseif ($homeColumn->level == BaseHomeColumn::LEVEL_SECOND) {
                        //判断是否有参数查询，如果有的话，还是需要走一次查询
                        $searchData = Yii::$app->request->get();
                        if (!empty($searchData['areaId']) || !empty($searchData['majorId']) || !empty($searchData['educationId']) || !empty($searchData['jobCategoryId']) || !empty($searchData['isEstablishment']) || !empty($searchData['announcementHeat'])) {
                            $data = $this->secondColumn($homeColumn);
                        }
                        $showHtml = 'second-column-list.html';
                    }
                    break;
            }
        } else {
            switch ($homeColumn->template_type) {
                case BaseHomeColumn::TEMPLATE_TYPE_NEWS:
                    // 资讯
                    //判断资讯是一级还是二级页面
                    if ($homeColumn->level == BaseHomeColumn::LEVEL_FIRST) {
                        //一级页面
                        $showHtml = 'news-column.html';
                        $data     = $this->newsColumn($homeColumn);
                    } elseif ($homeColumn->level == BaseHomeColumn::LEVEL_SECOND) {
                        $showHtml = 'second-column-list.html';

                        $data = $this->secondColumn($homeColumn);
                    }
                    break;
                case BaseHomeColumn::TEMPLATE_TYPE_LEVEL1_A:
                case BaseHomeColumn::TEMPLATE_TYPE_LEVEL1_B:

                    $data     = $this->announceColumn($homeColumn);
                    $showHtml = 'announcement-column.html';
                    break;
                case BaseHomeColumn::TEMPLATE_TYPE_LEVEL2   :
                    //二级公告栏目

                    $showHtml = 'second-column-list.html';

                    $data = $this->secondColumn($homeColumn);
                    break;
                case BaseHomeColumn::TEMPLATE_TYPE_AREA:
                default:
                    //判断地区是一级还是二级页面
                    if ($homeColumn->level == BaseHomeColumn::LEVEL_FIRST) {
                        //一级页面
                        $data     = $this->announceColumn($homeColumn);
                        $showHtml = 'announcement-column.html';
                    } elseif ($homeColumn->level == BaseHomeColumn::LEVEL_SECOND) {
                        $showHtml = 'second-column-list.html';
                        $data     = $this->secondColumn($homeColumn);
                    }
                    break;
            }

            // 缓存一下时间因子
            $refreshTime = BaseHomeColumn::getHomeColumnFactorTime($id);

            if ($refreshTime) {
                $data['timeFactorTime'] = $refreshTime;
            }

            Cache::set($cacheKey, json_encode($data, true));
        }
        //获取职位热度列表
        $data['jobHeatList'] = BaseJob::APPLY_HEAT_TYPE_TEXT_LIST;

        $data['isLogin'] = false;
        $data['isVip']   = false;
        if (!empty(Yii::$app->user->id)) {
            $data['isLogin'] = true;
            //判断是否是VIP
            $data['isVip'] = BaseResume::checkVip(Yii::$app->user->id);
        }

        //广告信息获取
        //判断栏目模板，是一级还是二级
        if ($showHtml == 'second-column-list.html') {
            $data['listShowcaseInfo'] = BaseJob::getListShowcaseInfo(count($data['list']), Yii::$app->user->id,
                BaseJob::VIP_SHOWCASE_POSITION_TYPE_H5_SECOND_COLUMN);
        } elseif ($showHtml == 'announcement-column.html') {
            foreach ($data['secondColumnInfoList'] as &$item) {
                $item['showcaseInfo'] = BaseJob::getListShowcaseInfo(count($item['list']), Yii::$app->user->id,
                    BaseJob::VIP_SHOWCASE_POSITION_TYPE_H5_FIRST_COLUMN);
            }
        }
        if (!$showHtml) {
            $this->notFound();
        }

        HomeColumn::click($id);

        if ($data['timeFactorTime']) {
            BaiduTimeFactor::create('', $data['timeFactorTime']);
            ToutiaoTimeFactor::create('', $data['timeFactorTime']);
        }

        return $this->render($showHtml, $data);
    }

    /**
     * @param $homeColumn
     */
    public function announceColumn(BaseHomeColumn $homeColumn)
    {
        // 首先去拿广告位
        $showcaseList = HomePosition::getAllShowcase('column');
        foreach ($showcaseList as $number => $showcase) {
            switch ($number) {
                case 'HF_m':
                    $showcaseBrowseList[$number] = $this->renderPartial('position/' . $number . '.html', [
                        'showcase' => $showcase,
                    ]);
                    break;
                case 'yijilanmuye_m':
                    $showcaseBrowseList[$number] = $this->renderPartial('position/' . $number . '.html', [
                        'showcase' => $showcase,
                    ]);
                    break;
            }
        }
        // 头条三个
        $columnHeadList = HomeColumn:: getHeadList($homeColumn->id);

        // 推荐36个
        if ($homeColumn->template_type == BaseHomeColumn::TEMPLATE_TYPE_AREA) {
            //如果是地区，规则有变化
            $recommendList = HomeColumn:: getAreaAnnouncementRecommendList($homeColumn->id);
        } else {
            $recommendList = HomeColumn:: getAnnouncementRecommendList($homeColumn->id);
        }
        $recommendOneList   = array_slice($recommendList, 0, 9);
        $recommendTwoList   = array_slice($recommendList, 9, 9);
        $recommendThreeList = array_slice($recommendList, 18, 9);
        $recommendFourList  = array_slice($recommendList, 27, 9);

        // 热门36个
        $hotList      = HomeColumn::getAnnouncementHotList($homeColumn->id);
        $hotOneList   = array_slice($hotList, 0, 9);
        $hotTwoList   = array_slice($hotList, 9, 9);
        $hotThreeList = array_slice($hotList, 18, 9);
        $hotFourList  = array_slice($hotList, 27, 9);
        // 最新公告和简章
        $secondColumnInfoList = HomeColumn::getNewestAnnouncementList($homeColumn->id);

        $data = array_merge([
            'name'                 => '这里是栏目页:' . $homeColumn->name,
            'columnId'             => $homeColumn->id,
            'columnHeadList'       => $columnHeadList,
            'recommendOneList'     => $recommendOneList,
            'recommendTwoList'     => $recommendTwoList,
            'recommendThreeList'   => $recommendThreeList,
            'recommendFourList'    => $recommendFourList,
            'hotOneList'           => $hotOneList,
            'hotTwoList'           => $hotTwoList,
            'hotFourList'          => $hotFourList,
            'hotThreeList'         => $hotThreeList,
            'secondColumnInfoList' => $secondColumnInfoList,
        ], $showcaseBrowseList);

        return $data;
    }

    /**
     * 渲染公告一级栏目页面
     */
    // public function announceColumn1($homeColumn)
    // {
    //     //获取一级栏目广告
    //     $showcaseList = HomePosition::getAllShowcase('column');
    //
    //     foreach ($showcaseList as $number => $showcase) {
    //         switch ($number) {
    //             case 'HF_m':
    //                 $showcaseBrowseList[$number] = $this->renderPartial('position/' . $number . '.html', [
    //                     'showcase' => $showcase,
    //                 ]);
    //                 break;
    //         }
    //     }
    //
    //     //获取"栏目头条"公告（3条）
    //     $columnHeadSearchData = [
    //         'columnId' => BaseArticleAttribute::ATTRIBUTE_COLUMN_HEAD,
    //         'pageSize' => Yii::$app->params['homePosition']['column']['columnHead']['count'],
    //     ];
    //     $columnHeadList       = Announcement::getList($columnHeadSearchData, false, false)['list'];
    //     //获取推荐公告1、展示本栏目下，近7天（包含当前日）发布的  公告属性为“滚动”、“推荐”、“焦点”、“置顶”、“首头”等的公告信息（优先展示”置顶“属性公告）；按发布时间倒序排列；当前模块展示32条最新发布的推荐信息；
    //     $recommendSearchData['attribute'] = [
    //         BaseArticleAttribute::ATTRIBUTE_ROLLING,
    //         BaseArticleAttribute::ATTRIBUTE_RECOMMEND,
    //         BaseArticleAttribute::ATTRIBUTE_FOCUS,
    //         BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP,
    //         BaseArticleAttribute::ATTRIBUTE_HOME_TOP_ONE,
    //         BaseArticleAttribute::ATTRIBUTE_HOME_TOP_TWO,
    //         BaseArticleAttribute::ATTRIBUTE_HOME_TOP_THREE,
    //         BaseArticleAttribute::ATTRIBUTE_HOME_TOP_FOUR,
    //         BaseArticleAttribute::ATTRIBUTE_HOME_TOP_FIVE,
    //     ];
    //     $recommendSearchData['pageSize']  = 36;
    //     //查询近7天的
    //     $recommendSearchData['startReleaseTime'] = date('Y-m-d', strtotime('-7 days'));
    //     $recommendSearchData['endReleaseTime']   = date('Y-m-d', time());
    //     $recommendSearchData['columnId']         = $homeColumn->id;
    //
    //     $recommendList = Announcement::getList($recommendSearchData, false, false)['list'];
    //
    //     $recommendOneList   = array_slice($recommendList, 0, 9);
    //     $recommendTwoList   = array_slice($recommendList, 9, 9);
    //     $recommendThreeList = array_slice($recommendList, 18, 9);
    //     $recommendFourList  = array_slice($recommendList, 27, 9);
    //     //获取热门公告1、默认展示最近15天内（包含当前日）本栏目下发布的、公告详情页面浏览量前16位的公告信息；点击标题，新页面打开公告详情页面；
    //     $hotList      = Announcement::getHotList(15, 31);
    //     $hotOneList   = array_slice($hotList, 0, 9);
    //     $hotTwoList   = array_slice($hotList, 9, 9);
    //     $hotThreeList = array_slice($hotList, 18, 9);
    //     $hotFourList  = array_slice($hotList, 18, 9);
    //     //获取最新公告简章，展示当前一级栏目下的二级栏目更新的最新公告,按发布时间倒序排列；tab切换展示二级栏目信息；
    //     //        $newAnnounceList =
    //     //获取当前栏目下的二级栏目信息
    //     $secondColumnInfoList = BaseHomeColumn::getSecondList($keywords['columnId']);
    //     foreach ($secondColumnInfoList as &$secondColumn) {
    //         $secondColumn['list'] = Announcement::getList(['columnId' => $secondColumn['id']], true, false)['list'];
    //     }
    //
    //     $data = array_merge([
    //         'name'                 => '这里是栏目页:' . $homeColumn->name,
    //         'columnId'             => $homeColumn->id,
    //         'columnHeadList'       => $columnHeadList,
    //         'recommendOneList'     => $recommendOneList,
    //         'recommendTwoList'     => $recommendTwoList,
    //         'recommendThreeList'   => $recommendThreeList,
    //         'recommendFourList'    => $recommendFourList,
    //         'hotOneList'           => $hotOneList,
    //         'hotTwoList'           => $hotTwoList,
    //         'hotFourList'          => $hotFourList,
    //         'hotThreeList'         => $hotThreeList,
    //         'secondColumnInfoList' => $secondColumnInfoList,
    //     ], $showcaseBrowseList);
    //
    //     return $data;
    // }

    /**
     * 渲染资讯一级栏目页面
     */
    public function newsColumn(BaseHomeColumn $column)
    {
        //获取一级栏目广告
        $showcaseList = HomePosition::getAllShowcase('column');
        foreach ($showcaseList as $number => $showcase) {
            switch ($number) {
                case 'HF_m':
                    $showcaseBrowseList[$number] = $this->renderPartial('position/' . $number . '.html', [
                        'showcase' => $showcase,
                    ]);
                    break;
                case 'yijilanmuye_m':
                    $showcaseBrowseList[$number] = $this->renderPartial('position/' . $number . '.html', [
                        'showcase' => $showcase,
                    ]);
                    break;
            }
        }

        $columnHeadList = HomeColumn:: getHeadList($column->id);

        $recommendList = HomeColumn:: getNewsRecommendList($column->id);

        $hotList = HomeColumn::getNewsHotList($column->id);

        //获取最新资讯，展示当前一级栏目下的二级栏目更新的最新公告,按发布时间倒序排列；tab切换展示二级栏目信息；
        $secondColumnInfoList = HomeColumn::getNewestNewsList($column->id);

        $hotOneList   = array_slice($hotList, 0, 9);
        $hotTwoList   = array_slice($hotList, 9, 9);
        $hotThreeList = array_slice($hotList, 18, 9);
        $hotFourList  = array_slice($hotList, 27, 9);

        $recommendOneList   = array_slice($recommendList, 0, 9);
        $recommendTwoList   = array_slice($recommendList, 9, 9);
        $recommendThreeList = array_slice($recommendList, 18, 9);
        $recommendFourList  = array_slice($recommendList, 27, 9);

        $data = array_merge([
            'columnId'             => $column->id,
            'name'                 => '这里是栏目页:' . $column->name,
            'columnHeadList'       => $columnHeadList,
            'recommendList'        => $recommendList,
            'hotList'              => $hotList,
            'hotOneList'           => $hotOneList,
            'hotTwoList'           => $hotTwoList,
            'hotThreeList'         => $hotThreeList,
            'hotFourList'          => $hotFourList,
            'recommendOneList'     => $recommendOneList,
            'recommendTwoList'     => $recommendTwoList,
            'recommendThreeList'   => $recommendThreeList,
            'recommendFourList'    => $recommendFourList,
            'secondColumnInfoList' => $secondColumnInfoList,
        ], $showcaseBrowseList);

        return $data;
    }

    /**
     * 资讯二级栏目页面
     * @param $homeColumn
     * @param $keywords
     */
    public function secondColumn($homeColumn)
    {
        $HF_m = $this->renderPartial('position/HF_m.html', [
            'showcase' => HomePosition::getShowCaseList('HF_m', 'index'),
        ]);
        if (in_array($homeColumn->id, HomeColumn::NEWS_ID_LIST)) {
            // 资讯栏目
            $rs   = HomeColumn::getSimpleNewstList($homeColumn->id, 1, 9);
            $data = [
                'HF_m'         => $HF_m,
                'name'         => '这里是栏目页:' . $homeColumn->name,
                'list'         => $rs,
                'type'         => BaseArticle::TYPE_NEWS,
                'homeColumnId' => $homeColumn->id,
                'columnId'     => $homeColumn->id,
            ];
        } else {
            $searchData = Yii::$app->request->get();

            //判断求职者权限
            $resumeId = BaseMember::getMainId(Yii::$app->user->id);
            if (ResumeEquity::checkEquity($resumeId, ResumeEquitySetting::ID_ESTABLISHMENT_QUERY) === false) {
                unset($searchData['isEstablishment'], $searchData['announcementHeat']);
            }
            //            $list       = HomeColumn::getSimpleAnnouncementList($homeColumn->id, 1, 9);
            $searchData['columnId'] = $homeColumn->id;
            $list                   = HomeColumn::getSecondAnnouncementChildrenListBySearch($searchData);
            $selectList             = HomeColumn::getLevel2SelectList($homeColumn->id);

            $allSelect = [
                [
                    'k' => '',
                    'v' => '全选',
                ],
            ];
            //获取学历列表
            $educationList = array_merge($allSelect, ArrayHelper::obj2Arr(BaseDictionary::getEducationList()));

            // 按照里面的k排序和去重
            $cityList = ArrayHelper::objMoreArr($selectList['cityList']);
            $cityList = ArrayHelper::arraySort($cityList, 'k');
            $cityList = ArrayHelper::assocUnique($cityList, 'k');
            // 为每一个二级添加一个省份的选择
            foreach ($cityList as $key => $city) {
                if ($city['children'][0]['v'] == $city['v']) {
                    continue;
                }
                $children                   = array_merge([
                    [
                        'k' => $city['k'],
                        'v' => $city['v'],
                    ],
                ], $city['children']);
                $cityList[$key]['children'] = $children;
            }

            $cityList = array_merge($allSelect, $cityList);

            //获取职位类型
            $jobCategoryList = ArrayHelper::objMoreArr($selectList['categoryList']);
            $jobCategoryList = ArrayHelper::arraySort($jobCategoryList, 'k');
            $jobCategoryList = array_merge($allSelect, ArrayHelper::assocUnique($jobCategoryList, 'k'));

            $majorList = ArrayHelper::objMoreArr($selectList['majorList']);
            $majorList = ArrayHelper::arraySort($majorList, 'k');
            $majorList = array_merge($allSelect, ArrayHelper::assocUnique($majorList, 'k'));
            //获取选中值，做回显
            if (!empty($searchData['areaId'])) {
                $searchData['areaName'] = BaseArea::getAreaName($searchData['areaId']);
            }
            if (!empty($searchData['majorId'])) {
                $searchData['majorName'] = BaseMajor::getMajorName($searchData['majorId']);
            }
            if (!empty($searchData['educationId'])) {
                $searchData['educationName'] = BaseDictionary::getEducationName($searchData['educationId']);
            }
            if (!empty($searchData['jobCategoryId'])) {
                $searchData['jobCategoryName'] = BaseCategoryJob::getName($searchData['jobCategoryId']);
            }

            $data = [
                'HF_m'            => $HF_m,
                'name'            => '这里是栏目页:' . $homeColumn->name,
                'list'            => $list,
                'cityList'        => $cityList,
                'majorList'       => $majorList,
                'educationList'   => $educationList,
                'jobCategoryList' => $jobCategoryList,
                'baseUrl'         => HomeColumn::getDetailUrl($homeColumn->id),
                'type'            => BaseArticle::TYPE_ANNOUNCEMENT,
                'homeColumnId'    => $homeColumn->id,
                'columnId'        => $homeColumn->id,
                'searchData'      => $searchData,
                //重复一个字段，用于面包屑部分显示
            ];
        }

        return $data;
    }

    /**
     * 资讯二级栏目页面
     * @param $homeColumn
     * @param $keywords
     */
    // public function secondColumn1($homeColumn, $keywords)
    // {
    //     if ($keywords['listType'] == 'announcement') {
    //         $data = Announcement::getList($keywords, true);
    //         //获取学历列表
    //         $educationList = ArrayHelper::obj2Arr(BaseDictionary::getEducationList());
    //         //获取职位类型
    //         $jobCategoryList = ArrayHelper::objMoreArr(BaseCategoryJob::getCompanyCategoryJobList());
    //
    //         $data = [
    //
    //             'name'            => '这里是栏目页:' . $homeColumn->name,
    //             'list'            => $data['list'],
    //             'cityList'        => $data['cityList'],
    //             'majorList'       => $data['majorList'],
    //             'educationList'   => $educationList,
    //             'jobCategoryList' => $jobCategoryList,
    //             'type'            => 'annoouncement',
    //         ];
    //     } elseif ($keywords['listType'] == 'news') {
    //         $rs = News::getList($keywords);
    //
    //         $data = [
    //             'name' => '这里是栏目页:' . $homeColumn->name,
    //             'list' => $rs['list'],
    //             'type' => 'news',
    //         ];
    //     }
    //
    //     return $data;
    // }

    /**
     * 地区栏目页面
     * @return string
     */
    public function actionAreaColumn()
    {
        $provinceList = HomeColumn::getAllProvinceColumn();
        $cityList     = HomeColumn::getAllCityColumn();
        $hotAreaList  = HomeColumn::getHotAreaColumn();
        $this->setSeo(['title' => Yii::$app->params['seo']['region']['title']]);

        return $this->render('area-column.html', [
            'provinceList' => $provinceList,
            'cityList'     => $cityList,
            'hotAreaList'  => $hotAreaList,
        ]);
    }

    /**
     * 获取学科栏目
     * @return string
     */
    public function actionMajorColumn()
    {
        $majorList = HomeColumn::getAllMajorColumn();

        return $this->render('major-column.html', ['majorList' => $majorList]);
    }

    /**
     * 每日汇总页面
     */
    public function actionDaily(): string
    {
        $searchData = Yii::$app->request->get();
        $data       = DailyAnnouncementSummary::getList($searchData);

        $HF_m = $this->renderPartial('position/HF_m.html', [
            'showcase' => HomePosition::getShowCaseList('HF_m', 'index'),
        ]);

        $this->setSeo(['title' => Yii::$app->params['seo']['daily']['title']]);

        return $this->render('daily-summary-list.html', [
            'list' => $data['list'],
            'HF_m' => $HF_m,
        ]);
    }

    /**
     * 渲染搜索页面
     */
    public function actionSearch()
    {
        $searchData = Yii::$app->request->get();

        //这里取公告、资讯热门搜索广告位广告
        $announcementNumber        = 'gonggao_remensousuo_m';
        $announcementHotId         = BaseHomePosition::findOneVal(['number' => $announcementNumber], 'id');
        $announcementHotSearchList = BaseShowcase::getByPositionConfig($announcementHotId, $announcementNumber, 10);
        foreach ($announcementHotSearchList as $k => $value) {
            if (strlen($value['real_target_link']) < 1) {
                $announcementHotSearchList[$k]['url'] = Url::toRoute([
                    'home/search-result',
                    'keyword' => $value['title'],
                    'type'    => 1,
                ]);
            }
        }

        $newsNumber        = 'zixun_remensousuo_m';
        $newsHotId         = BaseHomePosition::findOneVal(['number' => $newsNumber], 'id');
        $newsHotSearchList = BaseShowcase::getByPositionConfig($newsHotId, $newsNumber, 10);
        foreach ($newsHotSearchList as $k => $value) {
            if (strlen($value['real_target_link']) < 1) {
                $newsHotSearchList[$k]['url'] = Url::toRoute([
                    'home/search-result',
                    'keyword' => $value['title'],
                    'type'    => 2,
                ]);
            }
        }

        $this->setSeo(['title' => Yii::$app->params['seo']['search']['title']]);

        return $this->render('search.html', [
            'hotAnnouncementList' => $announcementHotSearchList,
            'hotNewsList'         => $newsHotSearchList,
        ]);
    }

    /**
     * 搜索结果页面
     * @return string
     */
    public function actionSearchResult()
    {
        $searchData = Yii::$app->request->get();
        //1为公告，2为资讯
        $type = $searchData['type'];
        if ($type == 1) {
            $data = Announcement::getSearchKeywordList($searchData);
        } elseif ($type == 2) {
            $data = News::getSearchList($searchData);
        } else {
            $this->notFound();
        }

        //判断是否需要触底加载
        if ($data['totalNum'] <= 10) {
            $loading = 'true';
        } else {
            $loading = 'false';
        }
        //判断是否没有数据
        if ($data['totalNum'] == 0) {
            $notData = true;
        } else {
            $notData = false;
        }

        //这里取公告、资讯热门搜索广告位广告
        $announcementNumber        = 'gonggao_remensousuo_m';
        $announcementHotId         = BaseHomePosition::findOneVal(['number' => $announcementNumber], 'id');
        $announcementHotSearchList = BaseShowcase::getByPositionConfig($announcementHotId, $announcementNumber);
        foreach ($announcementHotSearchList as $k => $value) {
            if (strlen($value['real_target_link']) < 1) {
                $announcementHotSearchList[$k]['url'] = Url::toRoute([
                    'home/search-result',
                    'keyword' => $value['title'],
                    'type'    => 1,
                ]);
            }
        }

        $newsNumber        = 'zixun_remensousuo_m';
        $newsHotId         = BaseHomePosition::findOneVal(['number' => $newsNumber], 'id');
        $newsHotSearchList = BaseShowcase::getByPositionConfig($newsHotId, $newsNumber);
        foreach ($newsHotSearchList as $k => $value) {
            if (strlen($value['real_target_link']) < 1) {
                $newsHotSearchList[$k]['url'] = Url::toRoute([
                    'home/search-result',
                    'keyword' => $value['title'],
                    'type'    => 2,
                ]);
            }
        }

        $this->setSeo(['title' => Yii::$app->params['seo']['search']['title']]);

        return $this->render('search-result.html', [
            'list'                => $data['list'],
            'totalAmount'         => $data['totalNum'],
            'loading'             => $loading,
            'notData'             => $notData,
            'hotAnnouncementList' => $announcementHotSearchList,
            'hotNewsList'         => $newsHotSearchList,
            'type'                => $type,
            'keyword'             => $searchData['keyword'],
        ]);
    }

    /**
     * 获取公告列表数据(条件查询)
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetAnnouncementList()
    {
        $searchData = Yii::$app->request->get();

        $columnId = $searchData['columnId'];
        if ($columnId && in_array($columnId, HomeColumn::NEWS_ID_LIST)) {
            // 这里其实是拿资讯了
            return $this->success(HomeColumn::getSimpleNewstList($columnId, $searchData['page'], 9));
        }

        //判断求职者权限
        $memberId = Yii::$app->user->id ?: 0;
        $resumeId = BaseMember::getMainId($memberId);
        if (ResumeEquity::checkEquity($resumeId, ResumeEquitySetting::ID_ESTABLISHMENT_QUERY) === false) {
            unset($searchData['isEstablishment'], $searchData['announcementHeat']);
        }

        $data = HomeColumn::getSecondAnnouncementChildrenListBySearch($searchData);

        return $this->success($data);
    }

    /**
     * 获取公告列表(顶部搜索用的，勿改)
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetAnnouncementSearchList()
    {
        $searchData = Yii::$app->request->get();
        $data       = Announcement::getSearchKeywordList($searchData);

        return $this->success($data['list']);
    }

    /**
     * 获取资讯列表数据
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetNewsList()
    {
        $searchData = Yii::$app->request->get();
        $data       = News::getSearchList($searchData);

        return $this->success($data['list']);
    }

    /**
     * 获取最新更新公告数据
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetNewestAnnouncementList()
    {
        $page     = Yii::$app->request->get('page');
        $columnId = Yii::$app->request->get('columnId');

        // 在这里判断一下是公告还是资讯
        if ($columnId) {
            if (in_array($columnId, HomeColumn::NEWS_ID_LIST)) {
                $data = News::getNewestList($page, $columnId);

                return $this->success($data);
            }
        }

        return $this->success(Announcement::getNewestList($page, $columnId));
    }

    /**
     * ”热招单位”的广告信息
     */
    public function actionGetC4mList()
    {
        $page = Yii::$app->request->get('page');

        $html = $this->renderPartial('position/C4_m.html', [
            'showcase' => HomePosition::getShowCaseList('C4_m', 'index', $page),
        ]);

        $data['html'] = $html;
        json_encode($data);

        return $this->success($data);
    }

    /**
     * 获取"推荐单位"广告信息
     * @return string
     */
    public function actionGetC5mList()
    {
        $page = Yii::$app->request->get('page');

        $html = $this->renderPartial('position/C5_m.html', [
            'showcase' => HomePosition::getShowCaseList('C5_m', 'index', $page),
        ]);

        $data['html'] = $html;
        json_encode($data);

        return $this->success($data);
    }

    /**
     * 获取每日汇总列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetDailySummaryList()
    {
        $searchData = Yii::$app->request->get();
        $data       = DailyAnnouncementSummary::getList($searchData);

        return $this->success($data['list']);
    }

    /**
     * 每日汇总详情页面
     * @return string
     */
    public function actionDailySummaryDetail(): string
    {
        $id = Yii::$app->request->get('id');
        //判断文章状态
        $detail = DailyAnnouncementSummary::findOneVal(['id' => $id], 'status');
        if (empty($detail) || $detail['status'] != DailyAnnouncementSummary::STATUS_ACTIVE) {
            $this->notFound();
        }
        //获取文章内容
        $info = DailyAnnouncementSummary::getDetail($id);

        // 这里替换一下域名,如果存在旧的域名就换成新的
        $old = 'gcjob.new.gaoxiaojob.com';
        $new = Yii::$app->params['pcHost'];

        $info['content'] = str_replace($old, $new, $info['content']);

        if (!empty($memberId)) {
            //判断用户当前的状态（是否完成简历前三步）
            $info['userStatus'] = BaseMember::getUserResumeStatus($memberId);
        } else {
            $info['userStatus'] = BaseMember::USER_STATUS_UN_LOGIN;
        }

        $seoTitle = str_replace('【标题】', $info['title'], Yii::$app->params['seo']['dailyDetail']['title']);
        $this->setSeo([
            'title' => $seoTitle,
        ]);

        return $this->render('/news/detail.html', [
            'info'          => $info,
            'type'          => 'daily',
            'prev'          => DailyAnnouncementSummary::getPrev($id),
            'next'          => DailyAnnouncementSummary::getNext($id),
            'recommendList' => DailyAnnouncementSummary::getRecommend(),
        ]);
    }

    /**
     * 个人页面
     * @return string
     */
    public function actionPerson()
    {
        $memberId = Yii::$app->user->id;
        if (!empty($memberId)) {
            $isLogin = true;
        } else {
            $isLogin = false;
        }

        $userInfo   = Member::getH5MemberInfo($memberId);
        $userStatus = BaseMember::getUserResumeStatus($memberId);
        //判断用户是否完成前三步，获取不同跳转链接
        if ($userStatus == BaseMember::USER_STATUS_COMPLETE_RESUME) {
            $editUrl      = 'resume/edit';
            $intentionUrl = 'resume/resume-intention-view';
        } else {
            $editUrl      = 'resume/index';
            $intentionUrl = $editUrl;
        }

        $isVip                   = BaseResume::checkVip($memberId);
        $vipInfo                 = BaseResume::getVipInfo($memberId);
        $vipInfo['vipLevelText'] = '高才';
        //处理一下文案
        if ($isVip) {
            $vipInfo['vipLevelText'] = str_replace('VIP', '', BaseResume::VIP_LEVEL_LIST[$vipInfo['vipLevel']]);
        }

        //处理一下--登录
        $isRefresh         = false;
        $isResumeTop       = false;
        $isDeliveryTop     = false;
        $isEquityPackage   = false;
        $resumeTopAmount   = 0;
        $deliveryTopAmount = 0;
        $pv_data           = [];
        if ($isLogin) {
            //是否有刷新的权益在生效
            $refresh_data = BaseResumeEquityPackage::equityEffectData(BaseResumeEquitySetting::ID_RESUME_REFRESH,
                $vipInfo['id']);
            if (count($refresh_data) > 0) {
                $isRefresh = true;
            }
            $resume_top_data = BaseResumeEquityPackage::equityEffectData(BaseResumeEquitySetting::ID_RESUME_TOP,
                $vipInfo['id']);
            if (count($resume_top_data) > 0) {
                $isResumeTop     = true;
                $resumeTopAmount = array_sum(array_column($resume_top_data, 'amount'));
            }
            $delivery_top_data = BaseResumeEquityPackage::equityEffectData(BaseResumeEquitySetting::ID_DELIVERY_TOP,
                $vipInfo['id']);
            if (count($delivery_top_data) > 0) {
                $isDeliveryTop     = true;
                $deliveryTopAmount = array_sum(array_column($delivery_top_data, 'amount'));
            }
            $pv_data         = BaseCompanyResumePvTotal::getPvExposure($vipInfo['id'], 7);
            $isEquityPackage = BaseResumeEquityPackage::isPackageEffect(BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP,
                    $vipInfo['id']) || BaseResumeEquityPackage::isPackageEffect(BaseResumeEquityPackageCategorySetting::ID_JOB_FAST,
                    $vipInfo['id']);
        }

        $loginUrl            = '/home/<USER>' . urlencode(Yii::$app->request->hostInfo . Yii::$app->request->getUrl());
        $vipUrl              = BaseResume::BUY_URL_VIP;
        $competitivePowerUrl = BaseResume::BUY_URL_COMPETITIVE_POWER;
        $jobListUrl          = '/job';
        $resumePreViewUrl    = '/resume/preview';
        $jobFastUrl          = BaseResume::BUY_URL_JOB_FAST;
        $resumeId            = BaseMember::getMainId(Yii::$app->user->id);
        $vipGuideBuyInfo     = Resume::getPersonCenterVipTips($resumeId);
        $popInfo             = [];
        if ($memberId) {
            $popInfo = BaseResume::getResumeCompletePopInfo($memberId);
            if (!empty($popInfo)) {
                //设置缓存
                BaseResume::setResumeCompletePopInfo($memberId);
            }
        }

        return $this->render('person.html', [
            'userInfo'               => $userInfo,
            'isLogin'                => $isLogin,
            'editUrl'                => $editUrl,
            'intentionUrl'           => $intentionUrl,
            'isVip'                  => $isVip,
            'vipInfo'                => $vipInfo,
            'jobAnalysisUrl'         => $competitivePowerUrl,
            'announcementHeatUrl'    => !$isLogin ? $vipUrl : $competitivePowerUrl,
            'establishmentSearchUrl' => !$isLogin ? $vipUrl : ($isVip ? $jobListUrl : $vipUrl),
            'resumePreviewUrl'       => !$isLogin ? $vipUrl : ($userInfo['unPerfect'] ? $editUrl : ($isVip ? $resumePreViewUrl : $vipUrl)),
            'isRefresh'              => $isRefresh,
            'isResumeTop'            => $isResumeTop,
            'isDeliveryTop'          => $isDeliveryTop,
            'refreshUrl'             => $isLogin ? ($isRefresh ? $jobFastUrl . '#privilege' : $jobFastUrl) : $jobFastUrl,
            'resumeTopUrl'           => $isLogin ? $jobFastUrl : $jobFastUrl,
            'deliveryTopUrl'         => $isLogin ? ($isDeliveryTop ? $jobFastUrl . '#privilege' : $jobFastUrl) : $jobFastUrl,
            'pvExposureUrl'          => $isLogin ? $jobFastUrl : $loginUrl,
            'resumeTopAmount'        => $resumeTopAmount,
            'deliveryTopAmount'      => $deliveryTopAmount,
            'pvData'                 => $pv_data,
            'isEquityPackage'        => $isEquityPackage,
            'vipGuideBuyInfo'        => $vipGuideBuyInfo,
            'popInfo'                => $popInfo,
        ]);
    }

    /**
     * 获取简历完善弹窗
     * @return \yii\console\Response|Response
     */
    public function actionGetResumeCompletePopInfo()
    {
        try {
            $memberId = Yii::$app->user->id;
            $info     = BaseResume::getResumeCompletePopInfo($memberId);

            return $this->success($info);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 账号密码登录
     * @return string
     */
    public function actionLogin()
    {
        Yii::$app->user->logout();

        return $this->render('login.html');
    }

    /**
     * 退出登录
     * @return \yii\console\Response|Response
     */
    public function actionLogout()
    {
        Yii::$app->user->logout();

        return $this->success();
    }

    /**
     * 手机验证码登录
     * @return string
     */
    public function actionCodeLogin()
    {
        Yii::$app->user->logout();
        $privacyPolicyUrl    = Yii::$app->params['privacyPolicyUrl'];
        $serviceAgreementUrl = Yii::$app->params['serviceAgreementUrl'];
        //获取跳转小程序scheme
        $miniApp   = WxMiniApp::getInstance();
        $schemeUrl = $miniApp->getPlaintextSchemeUrl();

        return $this->render('codeLogin.html', [
            'privacyPolicyUrl'    => $privacyPolicyUrl,
            'serviceAgreementUrl' => $serviceAgreementUrl,
            'schemeUrl'           => $schemeUrl,
        ]);
    }

    /**
     * 重置密码
     * @return string
     */
    public function actionResetPassword()
    {
        return $this->render('resetPassword.html');
    }

    /**
     * 账号密码登录
     * @return \yii\console\Response|Response
     */
    public function actionAccountLogin()
    {
        $account  = Yii::$app->request->post('account');
        $password = Yii::$app->request->post('password');

        $loginForm = new BaseMemberLoginForm();

        $loginForm->account  = $account;
        $loginForm->password = $password;
        $loginForm->type     = Member::TYPE_PERSON;

        try {
            $data = $loginForm->accountLogin();

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取登录的验证码
     * @return \yii\console\Response|Response
     */
    public function actionSendMobileLoginCode()
    {
        $mobile     = Yii::$app->request->post('mobile');
        $mobileCode = Yii::$app->request->post('mobileCode');
        $ticket     = Yii::$app->request->post('ticket');
        $randstr    = Yii::$app->request->post('randstr');

        // 校验登录ticket
        if (!$randstr || !$ticket) {
            return $this->fail('图形验证码验证失败');
        }

        $captcha = new Captcha();
        if (!$captcha->check($ticket, $randstr)) {
            return $this->fail('图形验证码验证失败');
        }

        $type = BaseMember::TYPE_PERSON;

        $loginForm = new BaseMemberLoginForm();

        $loginForm->mobileCode = $mobileCode;
        $loginForm->mobile     = $mobile;
        $loginForm->loginType  = BaseMemberLoginForm::LOGIN_TYPE_MOBILE;
        $loginForm->type       = $type;
        $loginForm->smsType    = SmsQueue::TYPE_LOGIN;

        try {
            $loginForm->sendMobileCode();

            return $this->success('验证码已发送，请注意查收');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 验证登录验证码
     * @return \yii\console\Response|Response
     */
    public function actionValidateMobileLoginCode()
    {
        $mobile     = Yii::$app->request->post('mobile');
        $mobileCode = Yii::$app->request->post('mobileCode') ?: BaseMemberLoginForm::DEFAULT_MOBILE_CODE;
        $code       = Yii::$app->request->post('code');
        $type       = BaseMember::TYPE_PERSON;

        $loginForm = new BaseMemberLoginForm();

        $loginForm->mobileCode = $mobileCode;
        $loginForm->mobile     = $mobile;
        $loginForm->code       = $code;
        $loginForm->loginType  = BaseMemberLoginForm::LOGIN_TYPE_MOBILE;
        $loginForm->type       = $type;

        try {
            $data = $loginForm->validateMobileCode();

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 发送找回密码验证码(短信/邮件)
     */
    public function actionSendChangePasswordCode()
    {
        $mobile = Yii::$app->request->post('mobile');
        $email  = Yii::$app->request->post('email');
        $type   = BaseMember::TYPE_PERSON;

        $memberLoginForm         = new BaseMemberLoginForm();
        $memberLoginForm->mobile = $mobile;
        $memberLoginForm->email  = $email;
        $memberLoginForm->type   = $type;

        $transaction = Yii::$app->db->beginTransaction();
        try {
            if ($mobile) {
                $memberLoginForm->smsType = SmsQueue::TYPE_CHANGE_PASSWORD;

                $memberLoginForm->sendMobileCode();
            } elseif ($email) {
                $memberLoginForm->emailType = EmailQueue::EMAIL_TYPE_CHANGE_PASSWORD;

                $memberLoginForm->sendEmailCode();
            } else {
                return $this->fail();
            }

            $transaction->commit();

            return $this->success('验证码已发送，请注意查收');
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 找回密码提交
     */
    public function actionChangePassword()
    {
        $password = Yii::$app->request->post('password');
        $mobile   = Yii::$app->request->post('mobile');
        $email    = Yii::$app->request->post('email');
        $code     = Yii::$app->request->post('code');
        $type     = BaseMember::TYPE_PERSON;

        $memberLoginForm           = new BaseMemberLoginForm();
        $memberLoginForm->password = $password;
        $memberLoginForm->mobile   = $mobile;
        $memberLoginForm->code     = $code;
        $memberLoginForm->email    = $email;
        $memberLoginForm->type     = $type;

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $res = $memberLoginForm->validateChangePasswordCode();

            $transaction->commit();

            return $this->success($res === true ? '密码已重新设置' : $res);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    public function actionSetting()
    {
        $memberId = Yii::$app->user->id;

        $info = \h5\models\Member::getSettingInfo($memberId);

        return $this->render('setting.html', ['info' => $info]);
    }

    private function checkFromBaiduSearch()
    {
        $referrer = Yii::$app->request->getReferrer();

        if (!$referrer) {
            return;
        }

        $params = parse_url($referrer);
        parse_str($params['query'], $query);
        // 解析query

        if ($params['host'] == 'www.baidu.com' && $query['eqid']) {
            // 301到pc端
            $pcHost    = Yii::$app->params['pcHost'];
            $pcFullUrl = 'http://' . $pcHost;
            header("Location: $pcFullUrl");

            exit;
        }
    }
}