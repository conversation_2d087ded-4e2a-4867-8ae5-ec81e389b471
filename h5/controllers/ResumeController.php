<?php

namespace h5\controllers;

use admin\models\Area;
use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCertificate;
use common\base\models\BaseCompanyResumePvTotal;
use common\base\models\BaseDictionary;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAcademicBook;
use common\base\models\BaseResumeAcademicPage;
use common\base\models\BaseResumeAcademicPatent;
use common\base\models\BaseResumeAcademicReward;
use common\base\models\BaseResumeAdditionalInfo;
use common\base\models\BaseResumeAttachment;
use common\base\models\BaseResumeComplete;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeEquityPackage;
use common\base\models\BaseResumeIntention;
use common\base\models\BaseResumeOtherReward;
use common\base\models\BaseResumeOtherSkill;
use common\base\models\BaseResumeRefresh;
use common\base\models\BaseResumeResearchDirection;
use common\base\models\BaseResumeResearchProject;
use common\base\models\BaseResumeSetting;
use common\base\models\BaseResumeSkill;
use common\base\models\BaseResumeTemplateConfig;
use common\base\models\BaseResumeTemplateDownloadRecord;
use common\base\models\BaseResumeWork;
use common\base\models\BaseSkill;
use common\base\models\BaseUploadForm;
use common\helpers\ArrayHelper;
use common\helpers\FileHelper;
use common\helpers\TimeHelper;
use common\models\Skill;
use common\service\CommonService;
use common\service\memberCancel\ResumeCancelService;
use common\service\resume\GetEditInfoService;
use common\service\resume\ResumeService;
use frontendPc\models\Dictionary;
use frontendPc\models\ResumeAttachment;
use h5\models\ResumeEquity;
use h5\models\ResumeEquitySetting;
use h5\models\Resume;
use WhichBrowser\Model\Primitive\Base;
use \Yii;
use yii\base\Exception;

class ResumeController extends BaseH5Controller
{
    /**
     * 取消注销申请
     */
    public function actionCancelApplyCancel()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $token = Yii::$app->request->post('token');
            if (!$token) {
                return $this->fail('参数错误');
            }

            $resumeCancelService = new ResumeCancelService();
            $resumeCancelService->withdrawCancel($token);

            // 提交事务
            $transaction->commit();

            return $this->success('您的放弃注销已申请，请5分钟后重新登录');
        } catch (\Throwable $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    public function actionJumpWx()
    {
        return $this->render('jump-mp.html');
    }

    public function actionIndex()
    {
        $memberId        = \Yii::$app->user->id;
        $educationList   = ArrayHelper::obj2Arr(BaseDictionary::getEducationList());
        $politicalList   = ArrayHelper::obj2Arr(BaseDictionary::getPoliticalStatusList());
        $majorList       = BaseMajor::getPersonMajorListByLevel3();
        $jobCategoryList = ArrayHelper::objMoreArr(BaseCategoryJob::getCompanyCategoryJobList());
        $natureList      = ArrayHelper::obj2Arr(BaseDictionary::getNatureList());
        $wageList        = ArrayHelper::obj2Arr(BaseDictionary::getWageRangeList());
        $arriveDateList  = ArrayHelper::obj2Arr(BaseDictionary::getArriveDateList());
        $jobStatusList   = ArrayHelper::obj2Arr(Dictionary::getJobStatusList());
        $areaList        = BaseArea::getAreaList();
        //        $userInfo          = BaseResume::getUserBaseInfo();

        $service = new ResumeService();

        $userInfo = $service->setPlatform(CommonService::PLATFORM_H5)
            ->setOparetion(ResumeService::OPERATION_TYPE_GET_RESUME_STEP_ONE)
            ->init()
            ->run();

        $educationInfo = BaseResumeEducation::getHighestEducationInfo($memberId);        //获取最高教育经历
        // 如果没有，默认统招
        if (empty($educationInfo['school'])) {
            $educationInfo['isRecruitment'] = BaseResumeEducation::IS_RECRUITMENT_YES . '';
        }
        $researchDirection = BaseResumeResearchDirection::getResearchDirectionInfo($memberId)['researchDirection'];
        //$nativePlaceList    = BaseArea::getNativePlaceAreaList();

        //户籍国籍
        $allCityAreaList    = BaseArea::getAllCityAreaList();
        $nativeCityAreaList = BaseArea::getNativeCityAreaList();

        //获取默认值时间数组
        $birthdayArr           = TimeHelper::setH5DefaultDateArr($userInfo['birthday']);
        $educationStartDateArr = TimeHelper::setH5DefaultDateArr($educationInfo['studyBeginDate']);
        $educationEndDateArr   = TimeHelper::setH5DefaultDateArr($educationInfo['studyEndDate']);

        return $this->render('index.html', [
            'educationList'         => $educationList,
            'politicalList'         => $politicalList,
            'majorList'             => $majorList,
            'jobCategoryList'       => $jobCategoryList,
            'areaList'              => $areaList,
            'natureList'            => $natureList,
            'wageList'              => $wageList,
            'arriveDateList'        => $arriveDateList,
            'jobStatusList'         => $jobStatusList,
            'userInfo'              => $userInfo,
            'birthdayArr'           => $birthdayArr,
            'educationInfo'         => $educationInfo,
            'researchDirection'     => $researchDirection,
            //'nativePlaceList'       => $nativePlaceList,
            'educationStartDateArr' => $educationStartDateArr,
            'educationEndDateArr'   => $educationEndDateArr,
            'nativeCityAreaList'    => $allCityAreaList,
            'hierarchyCityList'     => $nativeCityAreaList,
        ]);
    }

    /**
     * 保存简历信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSaveInfo()
    {
        $memberId         = \Yii::$app->user->id;
        $data             = \Yii::$app->request->post();
        $data['memberId'] = $memberId;
        //对求职意向参数进行校验
        if (empty($data['arriveDateType']) || empty($data['workStatus'])) {
            return $this->fail('求职意向缺失必填参数！');
        }
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            //            BaseResume::saveUserBaseInfo($data);
            $service = new ResumeService();
            $service->setPlatform(CommonService::PLATFORM_H5)
                ->setOparetion(ResumeService::OPERATION_TYPE_SAVE_RESUME_STEP_ONE)
                ->init()
                ->run();
            BaseResume::saveIntentionInfo($data);
            BaseResumeEducation::saveInfo($data);
            BaseResumeIntention::saveInfo($data);
            BaseResumeResearchDirection::saveInfo($data);

            //同时更新用户的简历状态
            BaseResume::updateUserRealStatus();
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取
     * @return string
     * @throws \Exception
     */
    public function actionPreview1()
    {
        $memberId = \Yii::$app->user->id;
        $data     = BaseResume::getInfo($memberId);

        return $this->render('preview.html', [
            'userInfo'          => $data['userInfo'],
            'intentionList'     => $data['intentionList'],
            'educationList'     => $data['educationList'],
            'workList'          => $data['workList'],
            'projectList'       => $data['projectList'],
            'pageList'          => $data['pageList'],
            'patentList'        => $data['patentList'],
            'bookList'          => $data['bookList'],
            'rewardList'        => $data['rewardList'],
            'otherRewardList'   => $data['otherRewardList'],
            'certificateList'   => $data['certificateList'],
            'skillList'         => $data['skillList'],
            'otherSkillList'    => $data['otherSkillList'],
            'researchDirection' => $data['researchDirection'],
            'addInfoList'       => $data['addInfoList'],
            'resumePercent'     => $data['resumePercent'],
        ]);
    }

    public function actionPreview()
    {
        $memberId = \Yii::$app->user->id;
        //获取用户基本信息
        $userInfo = Resume::getMobileInfo($memberId);

        return $this->render('preview.html', [
            'info' => $userInfo,
        ]);
    }

    public function actionUpload()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            //            BaseResumeAttachment::checkLimit(\Yii::$app->user->id);
            //
            //            $model = new BaseUploadForm();
            //
            //            $data = $model->resume(Yii::$app->user->id);
            //
            //            //保存附件简历记录
            //            $token = BaseResumeAttachment::saveAttachment($data)['token'];

            //            $transaction->commit();

            //            return $this->success([
            //                'name'  => $data['name'],
            //                'token' => $token,
            //            ]);
            $model = new BaseUploadForm();
            $res   = $model->resumeNew([
                'memberId' => Yii::$app->user->id,
                'resumeId' => $this->getResumeId(),
            ]);
            $transaction->commit();

            return $this->success($res);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 添加学术论文
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAddPaper()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_PAPER_ADD)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 删除学术论文
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelPaper()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_PAPER_DELETE)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 编辑学术论文
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditPaper()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_PAPER_EDIT)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 添加学术专利
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAddPatent()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_PATENT_ADD)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 删除学术专利
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelPatent()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_PATENT_DELETE)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 编辑学术专利
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditPatent()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_PATENT_EDIT)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 添加学术专著
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAddBook()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_BOOK_ADD)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 删除学术专著
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelBook()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_BOOK_DELETE)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 编辑学术专著
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditBook()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_BOOK_EDIT)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 新增学术奖励
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAddReward()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_REWARD_ADD)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 删除学术奖励
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelReward()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_REWARD_DELETE)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 编辑学术奖励
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditReward()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_REWARD_EDIT)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 新增其他奖励
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAddOtherReward()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_OTHER_REWARD_ADD)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 删除其他奖励
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelOtherReward()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_OTHER_REWARD_DELETE)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 编辑其他奖励
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditOtherReward()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_OTHER_REWARD_EDIT)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 新增技能
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAddSkill()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_SKILL_ADD)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 删除技能
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelSkill()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_SKILL_DELETE)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 编辑技能
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditSkill()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_SKILL_EDIT)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 新增其他技能
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAddOtherSkill()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_OTHER_SKILL_ADD)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 删除其他技能
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelOtherSkill()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_OTHER_SKILL_DELETE)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 编辑其他技能
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditOtherSkill()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_OTHER_SKILL_EDIT)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 新增资质证书
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAddCertificate()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_CERTIFICATE_ADD)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 删除资质证书
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelCertificate()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_CERTIFICATE_DELETE)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 编辑资质证书
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditCertificate()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_CERTIFICATE_EDIT)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 新增附加信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAddAdditional()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_ADDITIONAL_ADD)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 删除附加信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelAdditional()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_ADDITIONAL_DELETE)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 编辑附加信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditAdditional()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_ADDITIONAL_EDIT)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 新增工作经历
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAddWorkRecord()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_WORK_ADD)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 删除附加信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelWorkRecord()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_WORK_DELETE)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 编辑工作经历
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditWorkRecord()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_WORK_EDIT)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 个人优势页面渲染
     * @throws \yii\base\Exception
     * @throws \Exception
     */
    public function actionResumeAdvantageView(): string
    {
        $memberId  = \Yii::$app->user->id;
        $advantage = BaseResume::findOneVal(['member_id' => $memberId], 'advantage');

        return $this->render('resume-advantage.html', ['advantage' => $advantage]);
    }

    /**
     * 编辑个人优势
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditAdvantage()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_ADVANTAGE_EDIT)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 管理求职意向页面渲染
     * @throws \yii\base\Exception
     * @throws \Exception
     */
    public function actionResumeIntentionView(): string
    {
        //求职意向列表
        $memberId            = \Yii::$app->user->id;
        $service             = new ResumeService();
        $resumeIntentionList = $service->setOparetion(ResumeService::OPERATION_TYPE_INTENTION_INDEX)
            ->setPlatform(CommonService::PLATFORM_H5)
            ->init()
            ->run();

        $resume             = BaseResume::findOne(['member_id' => $memberId]);
        $workStatusName     = Dictionary::getJobStatusName($resume['work_status']) ?: '';
        $arriveDateTypeName = $resume['arrive_date'];
        if ($resume['arrive_date_type'] != BaseResume::CUSTOM_ARRIVE_DATE_TYPE) {
            $arriveDateTypeName = Dictionary::getArriveDateName($resume['arrive_date_type']);
        }

        $arriveDateList = ArrayHelper::obj2Arr(BaseDictionary::getArriveDateList());
        $jobStatusList  = ArrayHelper::obj2Arr(Dictionary::getJobStatusList());

        return $this->render('resume-intention.html', [
            'resumeIntentionList' => $resumeIntentionList,
            'count'               => sizeof($resumeIntentionList),
            'workStatusName'      => $workStatusName,
            'arriveDateTypeName'  => $arriveDateTypeName,
            'resume'              => $resume,
            'arriveDateList'      => $arriveDateList,
            'jobStatusList'       => $jobStatusList,
        ]);
    }

    /**
     * 添加/编辑求职意向页面
     * 意向职位、意向城市、工作性质、工作性质、期望月薪、求助状态、到岗时间
     * @throws \yii\base\Exception
     */
    public function actionResumeIntentionListView(): string
    {
        //这里接收一下页面层级
        $hierarchy           = Yii::$app->request->get('hierarchy') ?: 1;
        $service             = new ResumeService();
        $resumeIntentionList = $service->setOparetion(ResumeService::OPERATION_TYPE_INTENTION_INDEX)
            ->setPlatform(CommonService::PLATFORM_H5)
            ->init()
            ->run();

        $resumeIntentionId   = Yii::$app->request->get('resumeIntentionId');
        $resumeIntentionInfo = [];
        if ($resumeIntentionId) {
            $resumeIntentionInfo = BaseResumeIntention::getIntentionInfo($resumeIntentionId);
        }
        //这里输出形式待定
        $jobCategoryList = ArrayHelper::objMoreArr(BaseCategoryJob::getCompanyCategoryJobList());//意向职位
        $areaList        = BaseArea::getAreaList();//意向城市
        $natureList      = ArrayHelper::obj2Arr(BaseDictionary::getNatureList());//工作性质
        $wageList        = ArrayHelper::obj2Arr(BaseDictionary::getWageRangeList());//期望月薪

        return $this->render('resume-intention-item.html', [
            'count'               => count($resumeIntentionList),
            'jobCategoryList'     => $jobCategoryList,
            'areaList'            => $areaList,
            'natureList'          => $natureList,
            'wageList'            => $wageList,
            'resumeIntentionId'   => $resumeIntentionId,
            'resumeIntentionInfo' => $resumeIntentionInfo,
            'hierarchy'           => $hierarchy,
        ]);
    }

    /**
     * 添加求职意向
     */
    public function actionAddResumeIntention()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_INTENTION_ADD)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 编辑求职意向
     */
    public function actionEditResumeIntention()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_INTENTION_EDIT)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 删除求职意向
     */
    public function actionDeleteResumeIntention()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_INTENTION_DELETE)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 添加/编辑教育经历页面
     * @throws \Exception
     */
    public function actionResumeEducationView(): string
    {
        $resumeEducationId   = Yii::$app->request->get('id');
        $resumeEducationInfo = [];
        $educationName       = '';
        $majorName           = '';
        if ($resumeEducationId) {
            $resumeEducationInfo               = BaseResumeEducation::findOne(['id' => $resumeEducationId]);
            $resumeEducationInfo['begin_date'] = TimeHelper::formatToYearMonth($resumeEducationInfo['begin_date']);
            $resumeEducationInfo['end_date']   = TimeHelper::formatToYearMonth($resumeEducationInfo['end_date']);
            $educationName                     = BaseDictionary::getEducationName($resumeEducationInfo['education_id']);
            $majorName                         = BaseMajor::getMajorName($resumeEducationInfo['major_id']);
            if ($resumeEducationInfo['major_id'] == 0) {
                $resumeEducationInfo['major_id'] = '';
            }
        } else {
            // 默认统招
            $resumeEducationInfo['is_recruitment'] = BaseResumeEducation::IS_PROJECT_SCHOOL_YES;
        }

        $educationList = ArrayHelper::obj2Arr(BaseDictionary::getEducationList());
        $majorList     = BaseMajor::getPersonMajorListByLevel3();
        $service       = new ResumeService();
        $list          = $service->setOparetion(ResumeService::OPERATION_TYPE_EDUCATION_INDEX)
            ->setPlatform(CommonService::PLATFORM_H5)
            ->init()
            ->run();

        return $this->render('resume-education.html', [
            'info'              => $resumeEducationInfo,
            'educationList'     => $educationList,
            'majorList'         => $majorList,
            'resumeEducationId' => $resumeEducationId,
            'educationName'     => $educationName,
            'majorName'         => $majorName,
            'count'             => count($list),
        ]);
    }

    /**
     * 添加教育经历操作
     */
    public function actionAddResumeEducation()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_EDUCATION_ADD)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 编辑教育经历操作
     */
    public function actionEditResumeEducation()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_EDUCATION_EDIT)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 删除教育经历
     */
    public function actionDeleteResumeEducation()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_EDUCATION_DELETE)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 编辑研究方向页面
     * @throws \yii\base\Exception
     */
    public function actionResumeResearchDirectionView(): string
    {
        $service                 = new ResumeService();
        $resumeResearchDirection = $service->setOparetion(ResumeService::OPERATION_TYPE_RESEARCH_INDEX)
            ->setPlatform(CommonService::PLATFORM_H5)
            ->init()
            ->run();

        return $this->render('resume-research.html', [
            'resumeResearchDirection' => $resumeResearchDirection,
        ]);
    }

    /**
     * 保存研究方向
     */
    public function actionAddResumeResearchDirection()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_RESEARCH_ADD)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 编辑研究方向
     */
    public function actionEditResumeResearchDirection()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_RESEARCH_EDIT)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 添加/编辑科研项目页面
     * @throws \yii\base\Exception
     * @throws \Exception
     */
    public function actionResumeResearchProjectView(): string
    {
        $resumeResearchId   = Yii::$app->request->get('id');
        $resumeResearchInfo = [];
        if ($resumeResearchId) {
            $resumeResearchInfo = BaseResumeResearchProject::getInfo($resumeResearchId);
        }

        $service            = new ResumeService();
        $resumeResearchList = $service->setOparetion(ResumeService::OPERATION_TYPE_PROJECT_INDEX)
            ->setPlatform(CommonService::PLATFORM_H5)
            ->init()
            ->run();

        $projectCateList = ArrayHelper::obj2Arr(Dictionary::getProjectCateList());

        return $this->render('resume-project.html', [
            'count'            => sizeof($resumeResearchList),
            'info'             => $resumeResearchInfo,
            'projectCateList'  => $projectCateList,
            'resumeResearchId' => $resumeResearchId,
        ]);
    }

    /**
     * 新增科研项目
     */
    public function actionAddResumeResearchProject()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_PROJECT_ADD)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 编辑科研项目
     */
    public function actionEditResumeResearchProject()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_PROJECT_EDIT)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 删除科研项目
     */
    public function actionDeleteResumeResearchProject()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_PROJECT_DELETE)
                ->setPlatform(CommonService::PLATFORM_H5)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 上传并保存头像
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionUploadAvatar()
    {
        $model = new BaseUploadForm();

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $data     = $model->uploadAvatar();
            $memberId = Yii::$app->user->id;
            Resume::saveAvatar($memberId, $data['path']);

            $transaction->commit();

            return $this->success(['fullUrl' => FileHelper::getFullUrl($data['fullUrl'])]);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 编辑简历页码渲染
     * @return string
     * @throws Exception
     */
    public function actionEdit()
    {
        $memberId = Yii::$app->user->id;
        $resumeId = BaseMember::getMainId($memberId);
        //获取未完善的模块
        $resumeUnCompleteList   = BaseResumeComplete::getUnCompleteList($memberId, 'H5');
        $identityTipList        = BaseResume::getIdentityTipList($resumeId);
        $resumeUnCompleteAmount = count($resumeUnCompleteList);
        //获取用户基本信息
        $userInfo = Resume::getMobileInfo($memberId);

        return $this->render('edit.html', [
            'resumeUnCompleteList'   => $resumeUnCompleteList,
            'identityTipList'        => $identityTipList,
            'resumeUnCompleteAmount' => $resumeUnCompleteAmount,
            'info'                   => $userInfo,
        ]);
    }

    /**
     * 获取简历基本信息，页面渲染
     * @return string
     * @throws \Exception
     */
    public function actionBaseInfo()
    {
        $memberId = Yii::$app->user->id;
        $info     = Resume::getBaseInfo($memberId);
        //获取民族
        $nationList          = BaseDictionary::getNationList();
        $marriageList        = BaseResume::MARRIAGE_LIST;
        $politicalStatusList = BaseDictionary::getPoliticalStatusList();
        //户籍国籍
        $allCityAreaList    = BaseArea::getAllCityAreaList();
        $nativeCityAreaList = BaseArea::getNativeCityAreaList();
        //职称
        $titleList = BaseDictionary::getTitleList();

        $identityType = Yii::$app->request->get('identityType');

        return $this->render('base-info.html', [
            'info'                => $info,
            'identityType'        => $identityType,
            'nationList'          => $nationList,
            'marriageList'        => $marriageList,
            'politicalStatusList' => $politicalStatusList,
            'allCityAreaList'     => $allCityAreaList,
            'nativeCityAreaList'  => $nativeCityAreaList,
            'titleList'           => $titleList,
        ]);
    }

    /**
     * 保存基本信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSaveBaseInfo()
    {
        $data = Yii::$app->request->post();

        $transaction = \Yii::$app->db->beginTransaction();

        try {
            //判断必填参数
            if (!$data['name'] || !$data['gender'] || !$data['birthday'] || !$data['householdRegisterId'] || !$data['politicalStatusId'] || !$data['email'] || !$data['identityType']) {
                throw  new Exception('缺少必填参数');
            }
            if ($data['identityType'] == BaseResume::IDENTITY_TYPE_WORKER && empty($data['beginWorkDate'])) {
                throw  new Exception('缺少必填参数');
            }
            if (strlen($data['name']) > 32) {
                throw  new Exception('限制输入32个字符');
            }
            $data['memberId'] = Yii::$app->user->id;
            Resume::saveBaseInfo($data);

            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 编辑工作经历页面
     * @return string
     */
    public function actionEditWork()
    {
        $id       = Yii::$app->request->get('id');
        $memberId = Yii::$app->user->id;
        $workInfo = [];
        if (!empty($id)) {
            $service  = new GetEditInfoService();
            $workInfo = $service->setOparetion(GetEditInfoService::INFO_WORK)
                ->init([
                    'id'       => $id,
                    'memberId' => $memberId,
                ])
                ->run();
        }

        $modelName = new BaseResumeWork();
        $delBtn    = Resume::checkDelBtn($modelName, $memberId);

        return $this->render('edit-work.html', [
            'data'   => $workInfo,
            'delBtn' => $delBtn,
        ]);
    }

    /**
     * 学术论文页面
     * @return string
     * @throws Exception
     */
    public function actionPaper()
    {
        $id       = Yii::$app->request->get('id');
        $memberId = Yii::$app->user->id;
        $info     = [];
        if (!empty($id)) {
            $service = new GetEditInfoService();
            $info    = $service->setOparetion(GetEditInfoService::INFO_PAPER)
                ->init([
                    'id'       => $id,
                    'memberId' => $memberId,
                ])
                ->run();
        }
        $positionList = BaseDictionary::getPaperRankList();

        return $this->render('edit-paper.html', [
            'info'         => $info,
            'positionList' => $positionList,
        ]);
    }

    /**
     * 学术专利页面
     * @return string
     * @throws Exception
     */
    public function actionPatent()
    {
        $id       = Yii::$app->request->get('id');
        $memberId = Yii::$app->user->id;
        $info     = [];
        if (!empty($id)) {
            $service = new GetEditInfoService();
            $info    = $service->setOparetion(GetEditInfoService::INFO_PATENT)
                ->init([
                    'id'       => $id,
                    'memberId' => $memberId,
                ])
                ->run();
        }
        $positionList = BaseDictionary::getPatentRankList();

        return $this->render('edit-patent.html', [
            'info'         => $info,
            'positionList' => $positionList,
        ]);
    }

    /**
     * 学术专著页面
     * @return string
     * @throws Exception
     */
    public function actionBook()
    {
        $id       = Yii::$app->request->get('id');
        $memberId = Yii::$app->user->id;
        $info     = [];
        if (!empty($id)) {
            $service = new GetEditInfoService();
            $info    = $service->setOparetion(GetEditInfoService::INFO_BOOK)
                ->init([
                    'id'       => $id,
                    'memberId' => $memberId,
                ])
                ->run();
        }

        return $this->render('edit-book.html', [
            'info' => $info,
        ]);
    }

    /**
     * 学术荣誉页面
     * @return string
     * @throws Exception
     */
    public function actionReward()
    {
        $id       = Yii::$app->request->get('id');
        $memberId = Yii::$app->user->id;
        $info     = [];
        if (!empty($id)) {
            $service = new GetEditInfoService();
            $info    = $service->setOparetion(GetEditInfoService::INFO_REWARD)
                ->init([
                    'id'       => $id,
                    'memberId' => $memberId,
                ])
                ->run();
        }

        return $this->render('edit-reward.html', [
            'info' => $info,
        ]);
    }

    /**
     * 其他奖励页面
     * @return string
     * @throws Exception
     */
    public function actionOtherReward()
    {
        $id       = Yii::$app->request->get('id');
        $memberId = Yii::$app->user->id;
        $info     = [];
        if (!empty($id)) {
            $service = new GetEditInfoService();
            $info    = $service->setOparetion(GetEditInfoService::INFO_OTHER_REWARD)
                ->init([
                    'id'       => $id,
                    'memberId' => $memberId,
                ])
                ->run();
        }
        $modelName = new BaseResumeOtherReward();
        $delBtn    = Resume::checkDelBtn($modelName, $memberId);

        return $this->render('edit-other-reward.html', [
            'info'   => $info,
            'delBtn' => $delBtn,
        ]);
    }

    /**
     *  技能页面
     * @return string
     * @throws Exception
     */
    public function actionSkill()
    {
        $id       = Yii::$app->request->get('id');
        $memberId = Yii::$app->user->id;
        $info     = [];
        if (!empty($id)) {
            $service = new GetEditInfoService();
            $info    = $service->setOparetion(GetEditInfoService::INFO_SKILL)
                ->init([
                    'id'       => $id,
                    'memberId' => $memberId,
                ])
                ->run();
        }
        $degreeTypeList = BaseDictionary::getDegreeTypeList();
        $skillList      = BaseSkill::getList();

        return $this->render('edit-skill.html', [
            'info'           => $info,
            'degreeTypeList' => $degreeTypeList,
            'skillList'      => $skillList,
        ]);
    }

    /**
     *  其他技能页面
     * @return string
     * @throws Exception
     */
    public function actionOtherSkill()
    {
        $id       = Yii::$app->request->get('id');
        $memberId = Yii::$app->user->id;
        $info     = [];
        if (!empty($id)) {
            $service = new GetEditInfoService();
            $info    = $service->setOparetion(GetEditInfoService::INFO_OTHER_SKILL)
                ->init([
                    'id'       => $id,
                    'memberId' => $memberId,
                ])
                ->run();
        }
        $degreeTypeList = BaseDictionary::getDegreeTypeList();

        return $this->render('edit-other-skill.html', [
            'info'           => $info,
            'degreeTypeList' => $degreeTypeList,
        ]);
    }

    /**
     * 附加信息页面
     * @return string
     * @throws Exception
     */
    public function actionAdditionalInfo()
    {
        $id       = Yii::$app->request->get('id');
        $memberId = Yii::$app->user->id;
        $info     = [];
        if (!empty($id)) {
            $service = new GetEditInfoService();
            $info    = $service->setOparetion(GetEditInfoService::INFO_ADDITIONAL)
                ->init([
                    'id'       => $id,
                    'memberId' => $memberId,
                ])
                ->run();
        }
        $themeList = BaseDictionary::getThemeList();

        return $this->render('edit-additional-info.html', [
            'info'      => $info,
            'themeList' => $themeList,
        ]);
    }

    /**
     * 编辑资质证书页面
     * @return string
     * @throws Exception
     */
    public function actionCertificate()
    {
        $id       = Yii::$app->request->get('id');
        $memberId = Yii::$app->user->id;
        $info     = [];
        if (!empty($id)) {
            $service = new GetEditInfoService();
            $info    = $service->setOparetion(GetEditInfoService::INFO_CERTIFICATE)
                ->init([
                    'id'       => $id,
                    'memberId' => $memberId,
                ])
                ->run();
        }
        $certificateList = BaseCertificate::getDropDownList();

        return $this->render('edit-certificate.html', [
            'info'            => $info,
            'certificateList' => $certificateList,
        ]);
    }

    /**
     * 修改用户求职状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditWorkStatus()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $memberId   = Yii::$app->user->id;
            $workStatus = Yii::$app->request->post('workStatus');
            if (!$workStatus) {
                throw new Exception('参数错误');
            }

            BaseResume::editWorkStatus($memberId, $workStatus);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 修改用户到岗时间类型
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditArriveDateType()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $memberId       = Yii::$app->user->id;
            $arriveDateType = Yii::$app->request->post('arriveDateType');
            if (!$arriveDateType) {
                throw new Exception('参数错误');
            }

            BaseResume::editArriveDateType($memberId, $arriveDateType);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    // 刷新简历
    public function actionRefresh()
    {
        $resumeId = $this->getResumeId();
        $data     = BaseResumeRefresh::create($resumeId, BaseResumeRefresh::PLATFORM_H5);

        return $this->success($data);
    }

    /**
     * 获取简历模板配置
     */
    public function actionGetResumeTemplateConfig()
    {
        $data = BaseResumeTemplateConfig::getListNotLimit();

        //处理数据
        foreach ($data as &$value) {
            $value['template_image'] = FileHelper::getFullUrl($value['template_image']);
            unset($value['fileInfo'], $value['file_id'], $value['id']);
        }

        //是否会员
        $resume_info = BaseResume::findOne($this->getResumeId());
        $info        = [
            'is_vip'   => $resume_info->vip_type == 1 ? 1 : 2,
            'vip_text' => '升级VIP会员免费使用所有模版',
        ];

        return $this->render('template-download.html', [
            'data' => $data,
            'info' => $info,
        ]);
    }

    /**
     * 模板权益
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionResumeTemplateEquity()
    {
        $code = Yii::$app->request->get('code');
        if (!$code) {
            return $this->fail('参数错误');
        }
        //验证code
        $config = BaseResumeTemplateConfig::findOne([
            'code'      => $code,
            'is_delete' => BaseResumeTemplateConfig::IS_DELETE_NO,
            'is_show'   => BaseResumeTemplateConfig::IS_SHOW_YES,
        ]);
        if (!$config) {
            throw new Exception('模板已经下架');
        }
        //验证一下是否是会员模板
        $is_vip   = false;
        $resumeId = $this->getResumeId();
        if ($config->is_vip == BaseResumeTemplateConfig::IS_VIP_YES && ResumeEquity::checkEquity($resumeId,
                ResumeEquitySetting::ID_RESUME_TEMPLATE) === false) {
            $is_vip = true;
        }

        return $this->success(['is_vip' => $is_vip]);
    }

    /**
     * 获取简历模板PDF下载
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionResumeTemplatePdfDownload()
    {
        try {
            $code = Yii::$app->request->get('code');
            if (!$code) {
                return $this->fail('参数错误');
            }
            $resume_id = $this->getResumeId();
            BaseResumeTemplateConfig::resumeTemplatePdfDownload($resume_id, $code,
                BaseResumeTemplateDownloadRecord::PLATFORM_H5);
            exit();
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取PV曝光数据
     */
    public function actionPvExposure()
    {
        $resume_id = $this->getResumeId();
        $data      = BaseCompanyResumePvTotal::getPvExposure($resume_id);

        return $this->success($data);
    }

    /**
     * 获取是否有某个套餐生效
     */
    public function actionIsPackageEffect()
    {
        $package_id = Yii::$app->request->get('packageId');
        if ($package_id <= 0) {
            return $this->fail('参数错误');
        }
        $resume_id = $this->getResumeId();
        $isEquity  = BaseResumeEquityPackage::isPackageEffect($package_id, $resume_id);

        return $this->success(['isEquity' => $isEquity]);
    }

    /**
     * 修改简历显示状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionChangeShowStatus()
    {
        $memberId = \Yii::$app->user->id;
        $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id');

        try {
            $res = BaseResumeSetting::changeHideStatus($resumeId);

            return $this->success($res['msg']);
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }
}