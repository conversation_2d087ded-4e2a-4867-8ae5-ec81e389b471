<?php

namespace h5\controllers;

use common\base\models\BaseMemberLoginForm;
use common\base\models\BasePayTransformBuriedPointLog;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEquityPackageSetting;
use common\helpers\HtmlHelper;
use common\libs\WxPublic;
use h5\models\ResumeEquityPackageSetting;
use h5\models\ResumeEquityPackageCategorySetting;
use h5\models\Resume;
use h5\models\Member;

class IntroductionPageController extends BaseH5Controller
{
    /**
     * vip介绍页
     */
    public function actionVip()
    {
        // 判断是否登录
        $isLogin  = !\Yii::$app->user->isGuest;
        $resumeId = 0;
        // 查询求职者信息
        if ($isLogin) {
            $memberId                   = \Yii::$app->user->id;
            $vipTypeActive              = Resume::VIP_TYPE_ACTIVE;
            $vipTypeNormal              = Resume::VIP_TYPE_NORMAL;
            $userInfo                   = Member::find()
                ->alias('m')
                ->leftJoin(['r' => Resume::tableName()], 'm.id = r.member_id')
                ->where(['m.id' => $memberId])
                ->select([
                    'm.avatar',
                    'r.id',
                    'r.name',
                    'r.vip_type',
                    'r.vip_level',
                    'r.gender',
                    "DATE_FORMAT(r.vip_begin_time, '%Y-%m-%d') AS vip_begin_date",
                    "DATE_FORMAT(r.vip_expire_time, '%Y-%m-%d') AS vip_expire_date",
                    "(CASE r.vip_type WHEN $vipTypeActive THEN $vipTypeActive ELSE $vipTypeNormal END) AS is_vip",
                ])
                ->asArray()
                ->one();
            $userInfo['avatar']         = BaseMemberLoginForm::getAvatar($userInfo['avatar'], $userInfo['gender']);
            $userInfo['vip_level_name'] = $userInfo['vip_type'] == BaseResume::VIP_TYPE_ACTIVE ? BaseResume::VIP_LEVEL_LIST[$userInfo['vip_level']] : '';
            $resumeId                   = $userInfo['id'];
        } else {
            $userInfo = [];
        }

        // 查询权益组合信息
        $equityPackageInfoGold    = ResumeEquityPackageSetting::getPackageListByCidForBuy(ResumeEquityPackageCategorySetting::ID_GOLD_VIP,
            $resumeId);
        $equityPackageInfoDiamond = ResumeEquityPackageSetting::getPackageListByCidForBuy(ResumeEquityPackageCategorySetting::ID_DIAMOND_VIP,
            $resumeId);
        //隐藏小程序模块的一些内容
        $isMiniShow = true;
        if (HtmlHelper::isMiniapp() && HtmlHelper::isIos()) {
            //是小程序
            $isMiniShow = false;
        }

        //小程序用的扫码购买vip服务二维码
        $buyGuideQrcode = \Yii::$app->params['wx']['buyGuideQrcode'];
        $uuid           = (new BasePayTransformBuriedPointLog())->setPlatform(BasePayTransformBuriedPointLog::PLATFORM_TYPE_H5)
            ->setActionId(BasePayTransformBuriedPointLog::ACTION_ID_H5_VIP_VIEW)
            ->setActionType(BasePayTransformBuriedPointLog::ACTION_TYPE_QUEST)
            ->setEventParams([
                'stopTimes'   => 0,
                'sourceEntry' => \Yii::$app->request->getReferrer() ?: '',
            ])
            ->createLog();

        $data = [
            'is_login'                    => $isLogin,
            'equity_package_info_gold'    => $equityPackageInfoGold,
            'equity_package_info_diamond' => $equityPackageInfoDiamond,
            'user_info'                   => $userInfo,
            'page_type'                   => 1,
            'isMiniShow'                  => $isMiniShow,
            'buyGuideQrcode'              => $buyGuideQrcode,
            'uuid'                        => $uuid,
        ];

        // 设置 SEO 信息
        $config = \Yii::$app->params['seo']['vip'];

        $this->setSeo($config);

        return $this->render('vipIntroduce.html', ['data' => $data]);
    }

    /**
     * 竞争力介绍页
     */
    public function actionCompetition()
    {
        // 判断是否登录
        $isLogin = !\Yii::$app->user->isGuest;
        //如果登录了就获取一下resumeId
        $resumeId = 0;
        if ($isLogin) {
            $resumeId = $this->getResumeId();
        }
        // 查询权益组合信息
        $equityPackageInfo = ResumeEquityPackageSetting::getPackageListByCidForBuy(ResumeEquityPackageCategorySetting::ID_INSIGHT,
            $resumeId);
        //隐藏小程序模块的一些内容
        $isMiniShow = true;
        if (HtmlHelper::isMiniapp() && HtmlHelper::isIos()) {
            //是小程序
            $isMiniShow = false;
        }

        //小程序用的扫码购买vip服务二维码
        $buyGuideQrcode = \Yii::$app->params['wx']['buyGuideQrcode'];
        $uuid           = (new BasePayTransformBuriedPointLog())->setPlatform(BasePayTransformBuriedPointLog::PLATFORM_TYPE_H5)
            ->setActionId(BasePayTransformBuriedPointLog::ACTION_ID_H5_INSIGHT_VIEW)
            ->setActionType(BasePayTransformBuriedPointLog::ACTION_TYPE_QUEST)
            ->setEventParams([
                'stopTimes'   => 0,
                'sourceEntry' => \Yii::$app->request->getReferrer() ?: '',
            ])
            ->createLog();

        $data = [
            'is_login'            => $isLogin,
            'equity_package_info' => $equityPackageInfo,
            'page_type'           => 2,
            'isMiniShow'          => $isMiniShow,
            'buyGuideQrcode'      => $buyGuideQrcode,
            'uuid'                => $uuid,
        ];

        // 设置 SEO 信息
        $config = \Yii::$app->params['seo']['competitivePower'];
        $this->setSeo($config);

        return $this->render('competitivePower.html', ['data' => $data]);
    }

    /**
     * 求职快服务介绍页
     * @return string
     */
    public function actionJobFast()
    {
        // 判断是否登录
        $isLogin = !\Yii::$app->user->isGuest;
        //如果登录了就获取一下resumeId
        $resumeId = 0;
        if ($isLogin) {
            $resumeId = $this->getResumeId();
        }
        // 查询权益组合信息
        $equityPackageInfo = ResumeEquityPackageSetting::getPackageListByCidForBuy(ResumeEquityPackageCategorySetting::ID_JOB_FAST,
            $resumeId);
        //隐藏小程序模块的一些内容
        $isMiniShow = true;
        if (HtmlHelper::isMiniapp() && HtmlHelper::isIos()) {
            //是小程序
            $isMiniShow = false;
        }

        //小程序用的扫码购买vip服务二维码
        $buyGuideQrcode = \Yii::$app->params['wx']['buyGuideQrcode'];
        $uuid           = (new BasePayTransformBuriedPointLog())->setPlatform(BasePayTransformBuriedPointLog::PLATFORM_TYPE_H5)
            ->setActionId(BasePayTransformBuriedPointLog::ACTION_ID_H5_HUNT_JOB_VIEW)
            ->setActionType(BasePayTransformBuriedPointLog::ACTION_TYPE_QUEST)
            ->setEventParams([
                'stopTimes'   => 0,
                'sourceEntry' => \Yii::$app->request->getReferrer() ?: '',
            ])
            ->createLog();
        $data           = [
            'is_login'            => $isLogin,
            'equity_package_info' => $equityPackageInfo,
            'page_type'           => 4,
            'isMiniShow'          => $isMiniShow,
            'buyGuideQrcode'      => $buyGuideQrcode,
            'uuid'                => $uuid,
        ];

        // 设置 SEO
        $config = \Yii::$app->params['seo']['jobFast'];
        $this->setSeo($config);

        return $this->render('jobFast.html', ['data' => $data]);
    }

    /**
     * vip介绍页
     * 活动
     */
    public function actionActivityVip()
    {
        // 判断是否登录
        $isLogin  = !\Yii::$app->user->isGuest;
        $resumeId = 0;
        // 查询求职者信息
        if ($isLogin) {
            $memberId                   = \Yii::$app->user->id;
            $vipTypeActive              = Resume::VIP_TYPE_ACTIVE;
            $vipTypeNormal              = Resume::VIP_TYPE_NORMAL;
            $userInfo                   = Member::find()
                ->alias('m')
                ->leftJoin(['r' => Resume::tableName()], 'm.id = r.member_id')
                ->where(['m.id' => $memberId])
                ->select([
                    'm.avatar',
                    'r.id',
                    'r.name',
                    'r.vip_type',
                    'r.vip_level',
                    'r.gender',
                    "DATE_FORMAT(r.vip_begin_time, '%Y-%m-%d') AS vip_begin_date",
                    "DATE_FORMAT(r.vip_expire_time, '%Y-%m-%d') AS vip_expire_date",
                    "(CASE r.vip_type WHEN $vipTypeActive THEN $vipTypeActive ELSE $vipTypeNormal END) AS is_vip",
                ])
                ->asArray()
                ->one();
            $userInfo['avatar']         = BaseMemberLoginForm::getAvatar($userInfo['avatar'], $userInfo['gender']);
            $userInfo['vip_level_name'] = $userInfo['vip_type'] == BaseResume::VIP_TYPE_ACTIVE ? BaseResume::VIP_LEVEL_LIST[$userInfo['vip_level']] : '';
            $resumeId                   = $userInfo['id'];
        } else {
            $userInfo = [];
        }

        // 查询权益组合信息
        $equityPackageInfoGold    = ResumeEquityPackageSetting::getPackageListByCidForBuy(ResumeEquityPackageCategorySetting::ID_GOLD_VIP,
            $resumeId, true);
        $equityPackageInfoDiamond = ResumeEquityPackageSetting::getPackageListByCidForBuy(ResumeEquityPackageCategorySetting::ID_DIAMOND_VIP,
            $resumeId, true);
        //隐藏小程序模块的一些内容
        $isMiniShow = true;
        if (HtmlHelper::isMiniapp() && HtmlHelper::isIos()) {
            //是小程序
            $isMiniShow = false;
        }
        $data = [
            'is_login'                    => $isLogin,
            'equity_package_info_gold'    => $equityPackageInfoGold,
            'equity_package_info_diamond' => $equityPackageInfoDiamond,
            'user_info'                   => $userInfo,
            'page_type'                   => 101,
            'isMiniShow'                  => $isMiniShow,
        ];

        return $this->render('vipIntroduce.html', ['data' => $data]);
    }

}