<?php

namespace h5\controllers;

use common\base\BaseController;
use common\base\models\BaseMember;
use common\base\models\BaseMemberLoginForm;
use common\base\models\BaseResume;
use common\base\models\BaseSeoUserAgent;
use common\helpers\DebugHelper;
use common\helpers\HtmlHelper;
use common\helpers\IpHelper;
use common\libs\Cache;
use common\libs\JwtAuth;
use Yii;
use yii\base\Action;

class BaseH5Controller extends BaseController
{
    public function beforeAction($action)
    {
        //对蜘蛛行为进行处理
        if (!BaseSeoUserAgent::isAllowAccess()) {
            BaseSeoUserAgent::redirect503();
        }
        if (!parent::beforeAction($action)) {
            return false;
        }

        $this->check($action);

        $this->checkMini($action);

        $token = Yii::$app->request->get('authorization');

        if (isset($token)) {
            if ($token) {
                // 自动token登录逻辑
                $jwt = new JwtAuth();
                $id  = $jwt->checkToken($token);
                if ($id) {
                    // 解不了id
                    if (Yii::$app->user->isGuest) {
                        if ($id) {
                            $loginModel = new BaseMemberLoginForm();
                            $loginModel->loginById($id);
                        }
                    } else {
                        $memberId = Yii::$app->user->id;
                        if ($memberId != $id) {
                            $loginModel = new BaseMemberLoginForm();
                            $loginModel->loginById($id);
                        }
                    }
                }
            } else {
                // 退出登录
                Yii::$app->user->logout();
            }
        }
        //服务号自动登录
        $autoAuth    = Yii::$app->request->get('autoAuth');
        $autoAuthKey = Yii::$app->params['autoAuthKey'];
        if ($autoAuth && $autoAuth == $autoAuthKey) {
            //执行服务号自动登录
            $jwt            = new JwtAuth();
            $authToken      = Yii::$app->request->get('wechatAuthorization');
            $cacheAuthToken = Cache::get($authToken);
            if ($authToken && $cacheAuthToken) {
                $resumeId = $jwt->checkToken($authToken);
                if ($resumeId && $resumeInfo = BaseResume::findOne($resumeId)) {
                    $loginModel = new BaseMemberLoginForm();
                    $loginModel->loginById($resumeInfo->member_id);
                    //登录成功
                    if ($cacheAuthToken === 1) {
                        Cache::set($authToken, 2, 86400);//第一次登录之后重新计算时间
                    }
                }
            }
        }

        if (!Yii::$app->request->isAjax) {
            $this->setSeo();
        }

        $this->logAll();

        if (in_array($action->uniqueID, $this->ignoreLogin())) {
            return parent::beforeAction($action);
        }

        if (Yii::$app->user->isGuest) {
            // 跳转到登录页
            if (Yii::$app->request->isAjax) {
                echo json_encode([
                    'code'   => 403,
                    'result' => 0,
                    'msg'    => '未授权用户',
                ]);
                exit;
            }

            $this->redirect('/home/<USER>');

            return false;
        } else {
            $model = new BaseMemberLoginForm();
            $model->setMemberInfo(true);
            Yii::$app->params['user'] = BaseMember::getLoginInfo();
            $this->setActive();
        }

        // 判断用户是否完成简历完善了
        $this->checkUrl($action->uniqueID);

        return parent::beforeAction($action);
    }

    /**
     * 已经完成简历前三步的，不需要再进入的链接
     * @return string[]
     */
    public function getPerfectResumeList()
    {
        return [
            //简历前三步
            'resume/index',
        ];
    }

    /**
     * 根据情况来检查这个用户有没有权限操作
     */
    public function checkUrl($actionId)
    {
        $info   = BaseMember::getLoginInfo();
        $status = $info['status'];

        if ($status == BaseMember::STATUS_ACTIVE) {
            // 正常用户
            if (in_array($actionId, $this->getPerfectResumeList())) {
                return $this->redirect('/home/<USER>');
            }
        }
    }

    public function check(Action $action)
    {
        $ua = Yii::$app->request->getUserAgent();
        // 来源
        $referer = Yii::$app->request->getReferrer();

        // 游客才限制
        if (Yii::$app->user->isGuest) {
            if ($referer == 'https://google.com') {
                $this->notPermissions();
            }

            $ip         = IpHelper::getIp();
            $controller = $action->uniqueId;

            // 有几个控制器需要限制
            if (in_array($controller, [
                'job/detail',
                'company/detail',
                'hot-word/index',
                'engine/index',
                'home/column',
            ])) {
                try {
                    $ip        = explode('.', $ip);
                    $ip        = $ip[0] . '.' . $ip[1];
                    $banIpList = [
                        '14.153',
                        // '14.155',
                        '43.130',
                        '43.133',
                        '43.134',
                        '43.135',
                        '43.153',
                        '43.159',
                    ];
                    // ban掉 14.153.x.x 14.155.x.x 43.130.x.x 43.133.x.x 43.134.x.x 43.135.x.x 43.153.x.x 43.159.x.x
                    if (in_array($ip, $banIpList)) {
                        // 抛出个500
                        return $this->badPage();
                    }
                } catch (\Exception $e) {
                }
            }

            // 获取ip的面两段

            if (in_array($controller, [
                'job/index',
                'company/index',
            ])) {
                $banUa = [
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Mozilla/5.0 (Linux; Android 6.0.1; Nexus 6P Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.2526.83 Mobile Safari/537.36; 360Spider',
                    'Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0',
                    'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/* Safari/537.36 Edg/114.0.1823.43',
                ];

                if (in_array($ua, $banUa)) {
                    // 去到登录页
                    return $this->redirect('/home/<USER>');
                    $this->notFound();
                }
            }

            // 只判断ua
            // if (in_array($ua,
            //     ['Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/114.0.1823.43'])) {
            //     return $this->redirect('/home/<USER>');
            //     $this->notFound();
            // }
        }

        return true;
    }

    /**
     * 无需登录就可以操作的控制器
     * @return string[]
     */
    public function ignoreLogin()
    {
        return [
            'home/index',
            'home/login',
            'home/code-login',
            'home/old-column',
            'home/account-login',
            'home/validate-mobile-login-code',
            'home/send-mobile-login-code',
            'home/reset-password',
            'home/send-change-password-code',
            'home/change-password',
            'job/get-list',
            'job/index',
            'job/detail',
            'job/search',
            'news/detail',
            'company/index',
            'company/get-list',
            'company/detail',
            'company/detail-announcement-list',
            'company/detail-job-list',
            'company/detail-activity-list',
            'company/search',
            'home/get-announcement-list',
            'home/get-news-list',
            'home/search',
            'home/search-result',
            'home/daily-summary-list',
            'home/get-daily-summary-list',
            'home/daily-summary-detail',
            'home/get-c4m-list',
            'home/get-c5m-list',
            'announcement/detail',
            'home/get-newest-announcement-list',
            'home/column',
            'home/area-column',
            'home/major-column',
            'home/daily',
            'company/get-announcement-list',
            'company/get-job-list',
            'company/get-activity-list',
            'announcement/get-job-list',
            'announcement/check-announcement-apply',
            'announcement/preview',
            'config/get-private',
            'config/get-captcha',
            'home/get-announcement-search-list',
            'home/person',
            'engine/index',
            'engine/get-list',
            'hot-word/index',
            'news/get-time-new-list',
            'job/check-member-complete-status',
            'home/old-article',
            'home/old-column',
            'home/old-article',
            'activity/index',
            'job/search-result',
            'job/get-has-intention-list',
            // 支付回调
            'payment/notify',
            // vip
            'introduction-page/vip',
            'introduction-page/activity-vip',
            // 竞争力
            'introduction-page/competition',
            'introduction-page/job-fast',
            // 增值服务协议
            'agreement/value-added-services',
            'activity-form-registration-form/sign-success',
            'activity-form-option-sign/get-option-sign-statistic',
            'new-resume-activity/accept-detail',
            'new-resume-activity/share-detail',
            'new-resume-activity/send-register-code',
            'new-resume-activity/register',
            'new-resume-activity/check-share',
            'new-resume-activity/placard-detail',
            'showcase-browse-log/vip-click-package-point-log',
            'showcase-browse-log/vip-open-button-point-log',
            'showcase-browse-log/vip-tab-point-log',
            'showcase-browse-log/update-vip-view-point-log',
            'showcase-browse-log/update-competitive-power-view-point-log',
            'showcase-browse-log/competitive-power-click-package-point-log',
            'showcase-browse-log/competitive-power-open-button-point-log',
            'showcase-browse-log/update-job-fast-view-point-log',
            'showcase-browse-log/job-fast-click-package-point-log',
            'showcase-browse-log/job-fast-open-button-point-log',
            'resume/jump-wx',
            'seo-job-wiki/index',
            'resume/cancel-apply-cancel',
        ];
    }

    public function h5ToMini()
    {
        // 暂时关闭配置
        return [
            // 'job/detail'          => [
            //     'type' => 'switchTab',
            //     'url'  => '/pages/discover/index',
            // ],
            // 'announcement/detail' => [
            //     'type' => 'switchTab',
            //     'url'  => '/pages/discover/index',
            // ],
            // 'company/detail'      => [
            //     'type' => 'switchTab',
            //     'url'  => '/pages/discover/index',
            // ],
        ];
    }

    public function notFound()
    {
        Yii::$app->response->setStatusCode(404)
            ->send();
        echo $this->renderPartial('/home/<USER>');
        exit();
    }

    public function setSeo($data = [])
    {
        // 避免有些页面没有
        $config                 = Yii::$app->params['seo']['default'];
        $this->getView()->title = $data['title'] ?: $config["title"];

        // 特殊设置,找到当前的控制器
        $h5Config = Yii::$app->params['seo']['h5']['specialRoute'];

        $actionId = Yii::$app->controller->action->uniqueId;

        if ($h5Config && isset($h5Config[$actionId])) {
            $config = array_merge($config, $h5Config[$actionId]);
        }

        $this->checkMiniResume();

        $this->getView()->title = $data['title'] ?: $config["title"];

        $keywordsFlag = 'keywords'; //关键点是这个标记！！！
        $this->view->registerMetaTag([
            'name'    => 'keywords',
            'content' => $data['keywords'] ?: $config["keywords"],
        ], $keywordsFlag);

        $descFlag = 'desc'; //关键点也是这个标记！！！
        $this->view->registerMetaTag([
            'name'    => 'description',
            'content' => $data['description'] ?: $config["description"],
        ], $descFlag);

        $descFlag = 'Copyright'; //关键点也是这个标记！！！
        $this->view->registerMetaTag([
            'name'    => 'Copyright',
            'content' => $config["Copyright"],
        ], $descFlag);
    }

    public function logAll()
    {
        // $ua = Yii::$app->request->getUserAgent();
        //
        // Cache::lPush('ALL:USER_ACTIVE', json_encode([
        //     'ua'     => $ua,
        //     'ip'     => IpHelper::getIp(),
        //     'userId' => Yii::$app->user->id ?: 0,
        //     'time'   => CUR_DATETIME,
        //     'isAjax' => Yii::$app->request->isAjax ? 1 : 0,
        //     'url'    => Yii::$app->request->getHostInfo() . Yii::$app->request->url,
        // ]));
    }

    public function setActive()
    {
        // // 做一个简单的测试,把用户的id和现在的时间保存到缓存里面去,一段时间后再取出来,用于更新用户的活跃时间
        $userId   = Yii::$app->user->id;
        $actionId = Yii::$app->controller->action->uniqueId;

        if ($userId && $actionId) {
            $key  = Cache::ALL_RESUME_ACTION_CONTROLLER_KEY;
            $time = CUR_TIMESTAMP;
            // 写集合
            Cache::zadd($key, $time, $userId);
        }
    }

    public function checkMini($action)
    {
        $ua = Yii::$app->request->getUserAgent();
        if (strpos($ua, 'MicroMessenger') !== false) {
            // 在小程序环境中
            //$this->isMiniPayPages();
            $config = $this->h5ToMini();
            if ($data = $config[$action->uniqueID]) {
                $this->layout = false;
                Yii::$app->response->setStatusCode(301)
                    ->send();
                echo $this->render('/home/<USER>', [
                    'type' => $data['type'],
                    'url'  => $data['url'],
                ]);
                exit();
            }
        }
    }

    /**
     * 如果是小程序跳转h5简历模块的，把title去掉
     * @return void
     */
    public function checkMiniResume()
    {
        $controllerId = \Yii::$app->controller->id;
        $ua           = Yii::$app->request->getUserAgent();
        if (strpos($ua, 'MicroMessenger') !== false && $controllerId == 'resume') {
            $this->getView()->title = ' ';
        }
    }

    /**
     * 是否是小程序来的介绍页
     * @return mixed
     */
    public function isMiniPayPages()
    {
        $controllerId = \Yii::$app->controller->id;
        $ua           = Yii::$app->request->getUserAgent();
        if (strpos($ua,
                'MicroMessenger') !== false && $controllerId == 'introduction-page' && HtmlHelper::isMiniapp()) {
            Yii::$app->response->setStatusCode(301)
                ->send();
            echo $this->render('/home/<USER>', [
                'type' => 'switchTab',
                'url'  => '/pages/person/index',
            ]);
            exit();
        }
    }

    public function getResumeId()
    {
        $memberId = Yii::$app->user->id;
        $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id');

        return $resumeId;
    }

    // 没有权限
    public function notPermissions()
    {
        Yii::$app->response->setStatusCode(403)
            ->send();
        exit();
    }

}