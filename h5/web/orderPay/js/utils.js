var defaultLimitValue = 5
var defaultDuration = 3000
var defaultToastDuration = $.toast.prototype.defaults.duration || 1000

var startYear = 1900
var currentDate = new Date()
var currentYear = currentDate.getFullYear()

var soFar = '至今'
var soFarValue = '0000-00'

var pickerYearMonth = [currentYear.toString(), '-', '01']
var birthdayYearMonth = [currentYear - 18 + '', '-', '01']

var pickerStartYear = range(startYear, currentYear)
var pickerEightYearLater = range(startYear, currentYear + 8)

var pickerEndYear = (function () {
    var data = range(startYear, currentYear)
    data.push(soFar)
    return data
})()

var pickerMonth = (function () {
    var data = []
    var arr = range(1, 12)
    arr.forEach(function (val, idx) {
        var value = '' + val
        if (value.length === 1) {
            value = '0' + value
        }
        data[idx] = value
    })
    return data
})()

var getPickerCols = function (values) {
    return [
        { textAlign: 'center', values: values ? values : pickerStartYear },
        { textAlign: 'center', values: '-' },
        { textAlign: 'center', values: pickerMonth }
    ]
}

var pickerDefaultCols = getPickerCols()

var pickerSoFarCols = getPickerCols(pickerEndYear)

var pickerEightYearLaterCols = getPickerCols(pickerEightYearLater)

/**
 * trimSpace
 * @param { string } str
 * @returns { string }
 */
function trimSpace(str) {
    return str.replace(/\s/g, '')
}

/**
 * pickerCustomDateValue
 * @param { string } val
 * @returns { string[] }
 */
function pickerCustomDateValue(val) {
    return trimSpace(val).split('-').join(' - ').split(' ')
}

// 判断是否为字符串
function isString(val) {
    return Object.prototype.toString.call(val).slice(8, -1) === 'String'
}

// 判断是否为数组
function isArray(val) {
    return Object.prototype.toString.call(val).slice(8, -1) === 'Array'
}

// 判断是否为不限或者全部
function isUnlimited(val) {
    return /^0$/.test(val)
}

function formatPickerValue(val) {
    if (val) {
        if (val === soFar) {
            return [soFar, '-', '01']
        } else {
            return pickerCustomDateValue(val)
        }
    }
    return pickerYearMonth
}

function pickerSoFarCallback(picker, value) {
    var $container = picker.container
    var $input = picker.input
    var $pickerItem = $container.find('.picker-items-col')

    if (value[0] === soFar) {
        setTimeout(function () {
            $input.val(soFar)
            $input.attr('data-values', soFarValue)
        }, 0)
        $pickerItem.hide()
        $pickerItem.eq(0).show()
    } else {
        $pickerItem.show()
        $input.attr('data-values', isArray(value) ? value.join(' ') : value)
    }
}

function toastText(text, duration, callback) {
    $.toast.prototype.defaults.duration = duration || defaultDuration
    $.toast(text, 'text', function () {
        $.toast.prototype.defaults.duration = defaultToastDuration
        callback && callback()
    })
}

function limitToast(value) {
    toastText(`最多可选${value || defaultLimitValue}项`)
}

function replaceRoute(path) {
    window.location.replace(path)
}

function backResumeEdit() {
    replaceRoute('/resume/edit')
}

function backResumeIntention() {
    replaceRoute('/resume/resume-intention-view')
}

// 判断是否存在搜索条件页面滚动至筛选项
function isScrollToFilter(className) {
    var $container = $('.main-container')
    var $target = $(className)
    var headerHeight = $container.offset().top
    var search = window.location.search.replace(/^\?/, '')

    if (search && $target.length) {
        var top = $target.offset().top - headerHeight
        $container.scrollTop(top)
    }
}

// 查找替换URL中的参数
function replaceUrlParam(url, paramName, replaceWith) {
    var href = url || window.location.href
    var re = new RegExp('(' + paramName + '=)([^&]*)', 'gi')
    var nUrl = ''

    if (href.indexOf('?') > -1) {
        if (re.test(href)) {
            if (replaceWith.length) {
                nUrl = href.replace(re, paramName + '=' + replaceWith)
            } else {
                nUrl = href.replace(re, '').replace('&&', '&').replace(/&$/, '')
            }
        } else {
            if (replaceWith.length) {
                nUrl = href + '&' + paramName + '=' + replaceWith
            }
        }
    } else {
        if (replaceWith.length) {
            nUrl = href + '?' + paramName + '=' + replaceWith
        }
    }
    return (nUrl || href).replace(/\?&/, '?').replace(/\?$/, '')
}

/* 职位、公告、资讯 历史记录 start */
function getRecords() {
    return localStorage.getItem('records') || '{}'
}

/**
 * @param { 'jobs' | 'companies' | 'announcements' | 'news' } key
 * @returns { any[] }
 */
function getRecordStorage(key) {
    const records = JSON.parse(getRecords())
    return records[key] ? records[key] : []
}

function setRecordStorage(key, value) {
    const records = JSON.parse(getRecords())
    records[key] = value
    localStorage.setItem('records', JSON.stringify(records))
}
/* 职位、公告、资讯 历史记录 end */

/**
 * inputSelector
 * @param { $el } target
 * @param { [{title: string, value: string | number}] } itemList
 * @param { Function } changeCallback
 * @param { string? } closeText
 */
function inputSelector(target, itemList, changeCallback, closeText) {
    target.select({
        closeText: closeText || '取消',
        items: itemList,
        onChange(data) {
            changeCallback && changeCallback(data)
        }
    })
}

/**
 * 文本域字数限制及交互
 * @param { $el } $textareaEl 文本域
 * @param { $el? } $submitEl 提交按钮 (可选)
 */
function textAreaLimitSubmit($textareaEl, $submitEl) {
    function handleButton() {
        if ($submitEl) {
            var state = $textareaEl.val().length > 0

            $submitEl.prop('disabled', !state)
            $submitEl.toggleClass('weui-btn_disabled', !state)
        }
    }

    handleButton()

    if ($textareaEl) {
        var $cell = $textareaEl.parent('.textarea-cell')
        var $cellSpan = $cell.next('.weui-textarea-counter').find('span')
        var len = $textareaEl.val().length

        if (len > 0) {
            $cellSpan.text(len)
        }

        $textareaEl.on('input', function () {
            var length = $(this).val().length

            $cellSpan.text(length)
            handleButton()
        })
    }
}

/**
 * 简历编辑
 * @param { Function } confirmCallback (FormEl, FormData)
 * @param { Function? } cancelCallback (FormEl, FormData)
 * @param { string? } formClassName
 * @param { string? } confirmButtonClassName
 * @param { string? } cancelButtonClassName
 * @param { Function? } invalidFieldsCallback
 * @returns { void }
 */
function resumeOptimization(confirmCallback, cancelCallback, formClassName, confirmButtonClassName, cancelButtonClassName, invalidFieldsCallback) {
    var $formContainer = $(formClassName || '.resume-form')
    var $confirmButton = $formContainer.find(confirmButtonClassName || 'button[type="submit"]')
    var $cancelButton = $formContainer.find(cancelButtonClassName || 'button[type="reset"]')

    var $buttonCell = $formContainer.find('.resume-button')
    var $buttonItem = $buttonCell.find('.weui-btn')

    function getFormData() {
        var originFormArray = $formContainer.serializeArray()
        var originFormData = {}

        $.each(originFormArray, function (index, item) {
            var keyName = item.name
            var keyValue = item.value
            var $keyInput = $formContainer.find(`input[name="${keyName}"]`)
            var value = $keyInput.attr('data-values')

            if (originFormData.hasOwnProperty(keyName)) {
                var originKeyValue = originFormData[keyName]
                var valueArray = typeof originKeyValue === 'string' ? originKeyValue.split() : originKeyValue
                valueArray.push(value || keyValue)
                originFormData[keyName] = valueArray
            } else {
                originFormData[keyName] = value || keyValue
            }
        })

        return originFormData
    }

    $buttonCell.toggleClass('is-special', $buttonItem.length > 1)

    $confirmButton.on('click', function (event) {
        event.preventDefault()

        var $requiredInputs = $formContainer.find('[required]')
        var formData = getFormData()
        var requiredArray = []

        $requiredInputs.each(function (index, item) {
            var $item = $(item)
            var name = $item.attr('name')
            var readonly = $item.prop('readonly')
            var placeholder = $item.attr('placeholder')
            var trigger = readonly ? 'click' : 'focus'
            var prefix = readonly ? '请选择' : '请输入'
            var text = placeholder ? (placeholder.indexOf('请') >= 0 ? placeholder : prefix + placeholder) : ''

            if (!formData[name]) {
                requiredArray.push({ target: $item, text: text, trigger: trigger })
            }
        })

        if (requiredArray.length) {
            var required = requiredArray[0]
            var $target = required.target
            var text = required.text
            var trigger = required.trigger

            invalidFieldsCallback(requiredArray)

            toastText(text, defaultToastDuration)

            setTimeout(function () {
                $target[trigger]()
            }, 0)

            return
        }

        confirmCallback($formContainer, getFormData())
    })

    $cancelButton.on('click', function (event) {
        event.preventDefault()
        cancelCallback($formContainer, getFormData())
    })
}

/**
 * 单一选项选择
 * @param { string[] } data 选中value值 > ['1']
 * @param { string } dataTarget data-target="#majorPopup" > #majorPopup
 * @param { string } popupTarget id="majorPopup" > #majorPopup
 * @param { string } inputName name="majorId" > majorId
 */
function filterPlainPopup(data, dataTarget, popupTarget, inputName) {
    var checkedList = data
    var $target = $(`[data-target="${dataTarget}"]`)
    var $popup = $(popupTarget)
    var $form = $popup.find('.filter-form')
    var $item = $form.find(`input[name=${inputName}]`)
    var $unlimited = $form.find(`input[name=${inputName}][value="0"]`)

    var limitValue = $popup.attr('data-limit') || defaultLimitValue

    function init() {
        $item.prop('checked', false)

        if (checkedList.length) {
            $target.addClass('is-active')
            $item.each(function () {
                if (checkedList.indexOf($(this).val()) > -1) {
                    $(this).prop('checked', true)
                }
            })
        } else {
            $unlimited.prop('checked', true)
        }
    }

    init()

    $target.on('click', function () {
        init()
    })

    $item.on('click', function () {
        var val = $(this).val()
        var data = $form.serializeArray()

        if (isUnlimited(val)) {
            $item.prop('checked', false)
            $(this).prop('checked', true)
            return
        }

        if (data.length > limitValue) {
            limitToast(limitValue)
            $(this).prop('checked', false)
        } else {
            $unlimited.prop('checked', !data.length)
        }
    })

    $form
        .on('reset', function (e) {
            e.preventDefault()

            $item.prop('checked', false)
            $unlimited.prop('checked', true)
        })
        .on('submit', function (e) {
            e.preventDefault()
            var params = []
            var data = $(this).serializeArray()

            $.each(data, function (index, item) {
                if (item.value !== '0') {
                    params.push(item.value)
                }
            })

            window.location.href = replaceUrlParam(null, inputName, params.join('_'))
        })
}
/**
 * 单一选项级联选择
 * @param { string[] } data 选中value值 > ['1']
 * @param { string } dataTarget data-target="#majorPopup" > #majorPopup
 * @param { string } popupTarget id="majorPopup" > #majorPopup
 * @param { string } inputName name="majorId" > majorId
 * @param { Function? } callback
 */
function filterCascaderPopup(data, dataTarget, popupTarget, inputName, callback) {
    var triggerClick = false
    var checkedList = data
    var checkedData = []

    var $target = $(`[data-target="${dataTarget}"]`)
    var $popup = $(popupTarget)
    var $form = $popup.find('.filter-form')
    var $nav = $popup.find('.navigation-nav li')
    var $box = $popup.find('.navigation-box')
    var $panel = $popup.find('.navigation-panel')
    var $item = $form.find(`input[name=${inputName}]`)
    var $unlimited = $form.find(`input[name=${inputName}][value="0"]`)
    var $isRadio = $form.attr('is-radio') !== undefined
    var $isCloseRoute = $form.attr('is-close-route') !== undefined
    var $filterSelect = $popup.find('.filter-select')
    var $select = $popup.find('.select-values')[0]
    var $selectNumber = $popup.find('.filter-select .label span')

    var limitValue = $popup.attr('data-limit') || defaultLimitValue

    var closeClass = 'checked-close'

    function isShowSelect() {
        if (!$select) return
        var { length = 0 } = $select.children
        !!length ? $filterSelect.show() : $filterSelect.hide()
    }

    function checkedActive(checkedIndex) {
        var $panel = this.parents('.navigation-panel')
        var panelIndex = $panel.index()

        checkedIndex.push(panelIndex)
        $nav.eq(panelIndex).addClass('has-checked')
    }

    function initCheckedList(isInit) {
        var hasCheckedIndex = []

        $nav.removeClass('has-checked')

        $.each($form.serializeArray(), function (index, item) {
            var $checked = $item.filter('[value="' + item.value + '"]')

            if ($checked.length > 1) {
                $.each($checked, function (index, item) {
                    checkedActive.call($(item), hasCheckedIndex)
                })
            }
            checkedActive.call($checked, hasCheckedIndex)
        })

        if (isInit) {
            setTimeout(() => {
                $nav.eq(hasCheckedIndex[0] || 0).click()
            }, 300)
        }
    }

    function navScroll(index) {
        var $navScroll = $nav.parent()
        var scrollTop = $navScroll.scrollTop()
        var scrollHeight = $navScroll.height()
        var currentTop = $nav.eq(index).offset().top

        $navScroll.scrollTop(scrollTop + currentTop - scrollHeight / 2)
    }

    function valuesScroll() {
        var { scrollWidth, clientWidth } = $select
        if (scrollWidth > clientWidth) {
            $select.scrollTo({
                left: scrollWidth,
                behavior: 'smooth'
            })
        }
    }

    function getCheckedData() {
        if (!$select) return
        checkedData = []

        var newArray = []
        $.each($form.serializeArray(), function (index, item) {
            var $checked = $item.filter('[value="' + item.value + '"]')
            $.each($checked, function (index, item) {
                const value = item.value
                const label = $(item).attr('data-label')
                if (newArray.indexOf(value) === -1) {
                    newArray.push(value)
                    checkedData.push({
                        value,
                        label
                    })
                }
            })
        })

        var selectHtml = ''
        $.each(checkedData, function (index, item) {
            selectHtml += `<div class="select-item">${item.label}<i class="close checked-close" data-value="${item.value}"></i></div>`
        })

        $select.innerHTML = selectHtml
        $selectNumber.html(checkedData.length)

        valuesScroll()
        isShowSelect()
    }

    function init() {
        $item.prop('checked', false)

        if (checkedList.length) {
            $target.addClass('is-active')
            $item.each(function () {
                if (checkedList.indexOf($(this).val()) > -1) {
                    $(this).prop('checked', true)
                }
            })
        } else {
            $unlimited.prop('checked', true)
        }
        initCheckedList(true)
        getCheckedData()
    }

    init()

    $target.on('click', function () {
        init()
    })

    $nav.on('click', function () {
        var index = $(this).index()
        var top = $panel.eq(index).offset().top - $box.offset().top
        var scrollTop = $box.scrollTop()

        // if ($(this).hasClass('is-active')) return

        triggerClick = true

        $nav.removeClass('is-active').eq(index).addClass('is-active')
        navScroll(index)

        $box.scrollTo({
            toT: scrollTop + top,
            callback: function () {
                setTimeout(function () {
                    triggerClick = false
                }, 300)
            }
        })
    })

    $item.on('click', function () {
        var val = $(this).val()
        var checked = $(this).prop('checked')

        if (isUnlimited(val)) {
            $item.prop('checked', false)
            $(this).prop('checked', true)
        } else {
            var data = []
            var values = []
            var $checked = $item.filter('[value="' + val + '"]')

            if ($isRadio) {
                $item.prop('checked', false)
                $checked.prop('checked', true)
            } else {
                $checked.prop('checked', checked)
            }

            data = $form.serializeArray()

            $.each(data, function (index, item) {
                if (values.indexOf(item.value) > -1) {
                    return
                }
                values.push(item.value)
            })

            if ($isRadio) {
                var radiovalue = {
                    value: $(this).val(),
                    label: $(this).siblings('span').text()
                }
                checkedList = values
                callback && callback(radiovalue)
                $.closePopup()
                return
            }

            if (values.length > limitValue) {
                limitToast(limitValue)
                $checked.prop('checked', false)
            } else {
                $checked.prop('checked', checked)
                $unlimited.prop('checked', !data.length)
            }
        }

        initCheckedList(false)
        getCheckedData()
    })

    $box.on('scroll', function () {
        var indexList = []

        if (triggerClick) return

        $.each($panel, function (index, item) {
            var height = $(item).height()
            var marginTop = $(item).offset().top - $box.offset().top

            if (height + marginTop > 0) {
                indexList.push(index)
            }
        })

        $nav.removeClass('is-active')
        $nav.eq(indexList[0]).addClass('is-active')
        navScroll(indexList[0])
    })

    $popup.on('click', '.' + closeClass, function () {
        var value = $(this).attr('data-value')
        $(`input[name="${inputName}"][value="${value}"]`).prop('checked', false)
        checkedData = checkedData.filter((item) => item.value != value)
        $(this).parent().remove()
        $selectNumber.html(checkedData.length)
        initCheckedList()
        isShowSelect()
    })

    $form
        .on('reset', function (e) {
            e.preventDefault()

            $item.prop('checked', false)
            $unlimited.prop('checked', true)
            initCheckedList(true)

            checkedData = []
            $select.innerHTML = ''
            $selectNumber.html(checkedData.length)
        })
        .on('submit', function (e) {
            e.preventDefault()
            var params = []
            var data = $(this).serializeArray()

            $.each(data, function (index, item) {
                if (item.value !== '0') {
                    if (params.indexOf(item.value) > -1) {
                        return
                    }
                    params.push(item.value)
                }
            })

            if ($isCloseRoute) {
                callback && callback(checkedData)
                checkedList = checkedData.map((item) => item.value)
                return
            }

            window.location.href = replaceUrlParam(null, inputName, params.join('_'))
        })
}

/**
 * checkbox选择
 * @param { string[] } data 选中value值 > ['1']
 * @param { string } dataTarget data-target="#majorPopup" > #majorPopup
 * @param { string } popupTarget id="majorPopup" > #majorPopup
 * @param { string } inputName name="majorId" > majorId
 * @param { Function? } callback
 */
function filterCheckboxPopup(dataTarget, popupTarget, inputName, callback) {
    var dataValues = []
    var checkedData = []

    var $target = $(`[data-target="${dataTarget}"]`)
    var $popup = $(popupTarget)
    var $form = $popup.find('.filter-form')
    var $item = $form.find(`input[name=${inputName}]`)

    var limitMax = 0

    var noLimitProp = $form.attr('data-limit') == undefined

    function setValue() {
        if (!checkedData.length) {
            $target.val('')
            $target.attr('data-values', '')
            return
        } else {
            var valueObj = checkedData.reduce((previous, current) => {
                var { value, label } = previous
                return {
                    value: value + ',' + current.value,
                    label: label + ',' + current.label
                }
            })
            $target.val(valueObj.label)
            $target.attr('data-values', valueObj.value)
        }

        callback && callback(checkedData)
    }

    function getCheckedData() {
        $item.prop('checked', false)
        checkedData = []

        var values = $target.attr('data-values')
        dataValues = values ? values.split(',') : []
        $.each(dataValues, function (index, value) {
            var $checkedItem = $item.filter('[value="' + value + '"]')
            $checkedItem.prop('checked', true)
            const label = $checkedItem.attr('data-label')
            checkedData.push({
                value,
                label
            })
        })
        setValue()
    }

    function init() {
        limitMax = $form.attr('data-limit') || defaultLimitValue
        getCheckedData()
    }

    init()

    $target.on('click', function () {
        init()
    })

    $item.on('click', function () {
        var checkList = $item.filter(':checked')
        var length = checkList.length
        if (length > limitMax && !noLimitProp) {
            limitToast(limitMax)
            $(this).prop('checked', false)
        }
    })

    $form.on('submit', function (e) {
        e.preventDefault()
        checkedData = []
        var data = $item.filter(':checked')

        $.each(data, function (index, item) {
            var $checkedItem = $item.filter('[value="' + item.value + '"]')
            var label = $checkedItem.attr('data-label')
            var value = item.value
            checkedData.push({ label, value })
        })
        setValue()
    })
}

/**
 * 单一选项级联选择（a链接）
 * @param {string} dataTarget data-target="#majorPopup" > #majorPopup
 * @param {string} popupTarget id="majorPopup" > #majorPopup
 */
function filterCascaderLinkPopup(dataTarget, popupTarget) {
    var triggerClick = false

    var $target = $(`[data-target="${dataTarget}"]`)
    var $popup = $(popupTarget)
    var $nav = $popup.find('.navigation-nav li')
    var $box = $popup.find('.navigation-box')
    var $panel = $popup.find('.navigation-panel')
    var $panel = $popup.find('.navigation-panel')
    var $selectItem = $popup.find('.navigation-panel a.is-select')
    var $hasCheckedNav = $popup.find('.navigation-nav .has-checked')

    function navScroll(index) {
        var $navScroll = $nav.parent()
        var scrollTop = $navScroll.scrollTop()
        var scrollHeight = $navScroll.height()
        var currentTop = $nav.eq(index).offset().top

        $navScroll.scrollTop(scrollTop + currentTop - scrollHeight / 2)
    }

    function init() {
        if ($hasCheckedNav.length) {
            setTimeout(function () {
                $hasCheckedNav[0].click()
            }, 300)
        } else {
            $nav[0].click()
        }
    }

    init()

    $target.on('click', function () {
        init()
    })

    $nav.on('click', function () {
        var index = $(this).index()
        var top = $panel.eq(index).offset().top - $box.offset().top
        var scrollTop = $box.scrollTop()

        triggerClick = true

        $nav.removeClass('is-active').eq(index).addClass('is-active')
        navScroll(index)

        $box.scrollTo({
            toT: scrollTop + top,
            callback: function () {
                setTimeout(function () {
                    triggerClick = false
                }, 300)
            }
        })
    })

    $box.on('scroll', function () {
        var indexList = []

        if (triggerClick) return

        $.each($panel, function (index, item) {
            var height = $(item).height()
            var marginTop = $(item).offset().top - $box.offset().top

            if (height + marginTop > 0) {
                indexList.push(index)
            }
        })

        $nav.removeClass('is-active')
        $nav.eq(indexList[0]).addClass('is-active')
        navScroll(indexList[0])
    })
}
