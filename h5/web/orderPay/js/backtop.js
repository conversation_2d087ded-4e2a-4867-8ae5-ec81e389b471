/* scrollTo start */
!(function ($) {
    $.fn.scrollTo = function (options) {
        var defaults = {
            toT: 0, //滚动目标位置
            durTime: 500, //过渡动画时间
            delay: 30, //定时器时间
            callback: null //回调函数
        }
        var opts = $.extend(defaults, options),
            timer = null,
            _this = this,
            curTop = _this.scrollTop(), //滚动条当前的位置
            subTop = opts.toT - curTop, //滚动条目标位置和当前位置的差值
            index = 0,
            dur = Math.round(opts.durTime / opts.delay),
            smoothScroll = function (t) {
                index++
                var per = Math.round(subTop / dur)
                if (index >= dur) {
                    _this.scrollTop(t)
                    window.clearInterval(timer)
                    if (opts.callback && typeof opts.callback == 'function') {
                        opts.callback()
                    }
                    return
                } else {
                    _this.scrollTop(curTop + index * per)
                }
            }
        timer = window.setInterval(function () {
            smoothScroll(opts.toT)
        }, opts.delay)
        return _this
    }
})($)
/* scrollTo end */

/* backtop start */
$(function () {
    var timer = null
    var scrollValue = 0
    var $mainContainer = $('.main-container')
    var $backtopButton = $('<div class="backtop-container"></div>')

    $backtopButton.on('click', function () {
        $mainContainer.scrollTo({ toT: 0 })
    })

    function scrollBehavior() {
        var viewportHeight = $mainContainer.height()
        var scrollTop = $(this).scrollTop()
        var isDown = scrollTop > scrollValue
        var isShow = $backtopButton.is(':visible')

        scrollValue = scrollTop

        if (timer) clearTimeout(timer)
        timer = setTimeout(function () {
            if (isDown && isShow) return
            if (scrollTop > viewportHeight) {
                $backtopButton.fadeIn()
            } else {
                $backtopButton.fadeOut()
            }
        }, 300)
    }

    $mainContainer.append($backtopButton)

    $mainContainer.on('scroll', function () {
        scrollBehavior.call(this)
    })
})
/* backtop end */
