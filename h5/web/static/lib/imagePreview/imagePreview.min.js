var imagePreviewModule=function(t){"use strict";var e,i=(e=function(t,i){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,i)},function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}),n=function(t,e,i,n){return new(i||(i=Promise))((function(r,o){function s(t){try{h(n.next(t))}catch(t){o(t)}}function a(t){try{h(n.throw(t))}catch(t){o(t)}}function h(t){var e;t.done?r(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(s,a)}h((n=n.apply(t,e||[])).next())}))},r=function(t,e){var i,n,r,o,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(i)throw new TypeError("Generator is already executing.");for(;s;)try{if(i=1,n&&(r=2&o[0]?n.return:o[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,o[1])).done)return r;switch(n=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(r=s.trys,(r=r.length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){s.label=o[1];break}if(6===o[0]&&s.label<r[1]){s.label=r[1],r=o;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(o);break}r[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{i=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}},o=function(t){return function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.mouseDown=!1,e}return i(e,t),e.prototype.pcInitial=function(){this.ref.querySelector("."+this.prefix+"close").addEventListener("mousedown",this.close.bind(this)),this.ref.addEventListener("mousedown",this.handleMouseDown.bind(this)),this.ref.addEventListener("mousemove",this.handleMouseMove.bind(this)),this.ref.addEventListener("mouseup",this.handleMouseUp.bind(this)),this.ref.addEventListener("wheel",this.handleWheel.bind(this)),this.handleResize=this.handleResize.bind(this),window.addEventListener("resize",this.handleResize)},e.prototype.handleMouseUp=function(){this.mouseDown=!1,this.actionExecutor.isEnlargement?this.ref.style.cursor="grab":this.ref.style.cursor="initial"},e.prototype.handleMouseMove=function(t){var e=this.actionExecutor;if(e.isEnlargement&&this.mouseDown&&!this.isAnimating){clearTimeout(this.performerClick);var i=t.clientX,n=t.clientY,r=i-this.startX,o=n-this.startY;e.eventsHanlder.handleMoveEnlage(r,o,0),this.startX=i,this.startY=n}},e.prototype.handleWheel=function(t){return n(this,void 0,void 0,(function(){var e,i,n,o,s,a,h,l,c,u;return r(this,(function(r){return e=t.clientX,i=t.clientY,this.isZooming=!0,this.isAnimating=!0,n=this.actionExecutor,o=n.viewWidth/(2*n.dpr),s=n.viewHeight/(2*n.dpr),a=0,h=0,l=1,c=1,u=2*this.zoomScale,t.deltaY>0?(h=-(i-s)*u,a=-(e-o)*u,l=1+u,c=1+u):(h=(i-s)*u,a=(e-o)*u,l=1-u,c=1-u),n.eventsHanlder.handleZoom(l,c,a,h),this.isZooming=!1,this.isAnimating=!1,this.actionExecutor.isEnlargement?this.ref.style.cursor="grab":this.ref.style.cursor="initial",[2]}))}))},e.prototype.handlePCDoubleClick=function(t){return n(this,void 0,void 0,(function(){return r(this,(function(e){switch(e.label){case 0:return this.isAnimating?[2]:(this.isAnimating=!0,[4,this.actionExecutor.eventsHanlder.handleDoubleClick({clientX:t.clientX,clientY:t.clientY})]);case 1:return e.sent(),this.isAnimating=!1,this.actionExecutor.isEnlargement?this.ref.style.cursor="grab":this.ref.style.cursor="initial",[2]}}))}))},e.prototype.handleMouseDown=function(t){var e=this,i=t.target.dataset.type;this[i]?this[i](t):(this.mouseDown=!0,this.actionExecutor.isEnlargement?(this.startX=t.clientX,this.startY=t.clientY,this.ref.style.cursor="grabbing"):this.ref.style.cursor="initial",Date.now()-this.lastClick<this.doubleClickDuration?(clearTimeout(this.performerClick),this.handlePCDoubleClick(t)):this.performerClick=setTimeout((function(){e.handleClick(t)}),this.doubleClickDuration),this.lastClick=Date.now())},e.prototype.slideBefore=function(){return n(this,void 0,void 0,(function(){return r(this,(function(t){switch(t.label){case 0:return this.isAnimating?[2]:(this.isAnimating=!0,[4,this.actionExecutor.slideBefore()]);case 1:return t.sent()[0]?this.ref.querySelectorAll("."+this.prefix+"bottom ."+this.prefix+"item ")[0].style.cursor="not-allowed":this.ref.querySelectorAll("."+this.prefix+"bottom ."+this.prefix+"item ")[1].style.cursor="pointer",this.isAnimating=!1,[2]}}))}))},e.prototype.slideNext=function(){return n(this,void 0,void 0,(function(){return r(this,(function(t){switch(t.label){case 0:return this.isAnimating?[2]:(this.isAnimating=!0,[4,this.actionExecutor.slideNext()]);case 1:return t.sent()[0]?this.ref.querySelectorAll("."+this.prefix+"bottom ."+this.prefix+"item ")[1].style.cursor="not-allowed":this.ref.querySelectorAll("."+this.prefix+"bottom ."+this.prefix+"item ")[0].style.cursor="pointer",this.isAnimating=!1,[2]}}))}))},e}(t)},s=function(){function t(){}return t.prototype.handleMove=function(t){var e=this;if(t.preventDefault(),2==t.touches.length)return clearTimeout(this.performerClick),void this.handleZoom(t);var i=this.actionExecutor.IsBoundaryLeft,n=this.actionExecutor.isBoundaryRight,r=i||n;if(t.touches[0].clientX-this.startXForDirection!=0){var o=t.touches[0].clientX-this.startXForDirection>0?"right":"left",s=this.actionExecutor.viewRect,a=s.left,h=s.right,l=this.actionExecutor.viewWidth/this.actionExecutor.dpr;if(this.fingerDirection)if(this.actionExecutor.isEnlargement)if(a>=0&&h<=l)if("right"!=o&&"left"!=o&&!this.isEnlargeMove||"horizontal"!=this.fingerDirection)this.handleMoveEnlage(t);else{this.isEnlargeMove=!0,this.handleMoveNormal(t);var c="resetEnlargeMove";this.addTouchEndTask(c,{priority:1,callback:function(){e.isEnlargeMove=!1}})}else if(i&&"right"==o||n&&"left"==o||this.isEnlargeMove){this.isEnlargeMove=!0,this.handleMoveNormal(t);c="resetEnlargeMove";this.addTouchEndTask(c,{priority:1,callback:function(){return e.isEnlargeMove=!1}})}else this.handleMoveEnlage(t);else this.handleMoveNormal(t);else{if(this.actionExecutor.isEnlargement&&(a<0||h>l)||!this.actionExecutor.isEnlargement)return void(!this.actionExecutor.isEnlargement||r||this.normalMoved?this.actionExecutor.isEnlargement?(r||this.normalMoved)&&(this.normalMoved?this.handleMoveNormal(t):"right"==o?i?this.handleMoveNormal(t):this.handleMoveEnlage(t):"left"==o&&n?this.handleMoveNormal(t):this.handleMoveEnlage(t)):this.handleMoveNormal(t):this.handleMoveEnlage(t));if(this.getMovePoints(t),this.movePoints.length<this.maxMovePointCounts)return;this.decideMoveDirection()}}},t.prototype.handleMoveNormal=function(t){var e=this;if(!this.isAnimating&&!this.isZooming){this.isNormalMove||(this.touchStartX=this.startX=t.touches[0].clientX,this.touchStartY=this.startY=t.touches[0].clientY),this.isNormalMove=!0;this.addTouchEndTask("normalMove",{priority:1,callback:function(){return e.isNormalMove=!1}});var i=this.actionExecutor.eventsHanlder,n=t.touches[0].clientX,r=n-this.touchStartX;if(this.imgContainerMoveX+=r,this.startX=n,0!==r){this.normalMoved=!0;this.addTouchEndTask("normalMoved",{priority:10,callback:function(t){e.normalMoved=!1;var n=t.changedTouches[0].clientX-e.touchStartX;i.handleMoveNormal(t,n),e.handleTEndEnNormal.bind(e)(t)}})}i.handleMoveNormal(t,r)}},t.prototype.handleMoveEnlage=function(t){if(!this.actionExecutor.isLoadingError()&&!this.isZooming){this.moveStartTime||(this.moveStartTime=Date.now(),this.touchStartX=this.startX=t.touches[0].clientX,this.touchStartY=this.startY=t.touches[0].clientY);var e=this.actionExecutor;if(e.eventsHanlder.curBehaviorCanBreak){if(e.curAimateBreaked=!0,this.isAnimating)return this.touchStartX=t.touches[0].clientX,void(this.touchStartY=t.touches[0].clientY)}else if(this.isAnimating)return;this.isNormalMove=!1,this.actionExecutor.isBoudriedSide=!1;var i=this.imgContainer.getBoundingClientRect(),n=i.width,r=i.height,o=e.viewRect;o.height;var s,a,h=o.left,l=o.right,c=o.top,u=o.bottom,d=t.touches[0].clientX,f=t.touches[0].clientY,p=d-this.startX,g=f-this.startY;a=Math.round(h)<0||Math.round(l)>n?p:0,s=Math.round(c)<0||Math.round(u)>r?g:0,e.eventsHanlder.handleMoveEnlage(a,s,0);this.addTouchEndTask("handleTendEnlarte",{priority:10,callback:this.handleTEndEnlarge.bind(this)}),this.startX=d,this.startY=f}},t.prototype.autoMove=function(t,e,i,n){var r=n.maxTop,o=n.minTop,s=n.maxLeft,a=n.minLeft,h=this.imgContainer.getBoundingClientRect(),l=h.width,c=h.height,u=this.actionExecutor,d=u.viewRect,f=u.eventsHanlder,p=d,g=p.top,v=p.bottom,m=p.left,w=p.right;t=t/180*Math.PI;var x=e+300*Math.cos(t),y=i+300*Math.sin(t);x>s?x=s:x<a&&(x=a),y>r?y=r:y<o&&(y=o);var M=0,b=0;return m>=0&&w<=l||(M=x-e),g>=0&&v<=c||(b=y-i),f.moveCurPlaneTo(M,b,0)},t}(),a=function(){function t(){}return t.prototype.handleZoom=function(t){if(!(this.isNormalMove&&this.normalMoved||this.isAnimating||this.actionExecutor.isLoadingError())){this.isZooming||(this.curStartPoint1={x:this.curPoint1.x,y:this.curPoint1.y},this.curStartPoint2={x:this.curPoint2.x,y:this.curPoint2.y}),this.isZooming=!0,this.isAnimating=!0;var e=this.actionExecutor,i=Math.pow(this.curPoint1.x-this.curPoint2.x,2)+Math.pow(this.curPoint1.y-this.curPoint2.y,2),n=Math.pow(t.touches[0].clientX-t.touches[1].clientX,2)+Math.pow(t.touches[0].clientY-t.touches[1].clientY,2),r=(this.curStartPoint1.x+this.curStartPoint2.x)/2,o=(this.curStartPoint1.y+this.curStartPoint2.y)/2,s=e.viewWidth/(2*e.dpr),a=e.viewHeight/(2*e.dpr);this.curPoint1.x=t.touches[0].clientX,this.curPoint1.y=t.touches[0].clientY,this.curPoint2.x=t.touches[1].clientX,this.curPoint2.y=t.touches[1].clientY;var h=0,l=0,c=1,u=1;if(i>n)l=(o-a)*this.zoomScale,h=(r-s)*this.zoomScale,c=1-this.zoomScale,u=1-this.zoomScale;else{if(!(i<n))return this.isZooming=!1,void(this.isAnimating=!1);l=-(o-a)*this.zoomScale,h=-(r-s)*this.zoomScale,c=1+this.zoomScale,u=1+this.zoomScale}e.eventsHanlder.handleZoom(c,u,h,l),this.isZooming=!1,this.isAnimating=!1}},t}(),h=function(t,e,i,n){return new(i||(i=Promise))((function(r,o){function s(t){try{h(n.next(t))}catch(t){o(t)}}function a(t){try{h(n.throw(t))}catch(t){o(t)}}function h(t){var e;t.done?r(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(s,a)}h((n=n.apply(t,e||[])).next())}))},l=function(t,e){var i,n,r,o,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(i)throw new TypeError("Generator is already executing.");for(;s;)try{if(i=1,n&&(r=2&o[0]?n.return:o[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,o[1])).done)return r;switch(n=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(r=s.trys,(r=r.length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){s.label=o[1];break}if(6===o[0]&&s.label<r[1]){s.label=r[1],r=o;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(o);break}r[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{i=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}},c=function(){function t(){}return t.prototype.rotateLeft=function(t){return h(this,void 0,void 0,(function(){var t;return l(this,(function(e){switch(e.label){case 0:return this.isAnimating||this.actionExecutor.isLoadingError()?[2]:(t=-1*Math.PI/2,this.isAnimating=!0,[4,this.actionExecutor.rotateZ(t)]);case 1:return e.sent(),this.isAnimating=!1,[2]}}))}))},t.prototype.rotateRight=function(t){return h(this,void 0,void 0,(function(){var t;return l(this,(function(e){switch(e.label){case 0:return this.isAnimating||this.actionExecutor.isLoadingError()?[2]:(t=1*Math.PI/2,this.isAnimating=!0,[4,this.actionExecutor.rotateZ(t)]);case 1:return e.sent(),this.isAnimating=!1,[2]}}))}))},t}(),u=function(t,e){for(var i=0,n=e.length,r=t.length;i<n;i++,r++)t[r]=e[i];return t},d={multiplyPoint:function(t,e){for(var i=[],n=2;n<arguments.length;n++)i[n-2]=arguments[n];for(var r=[],o=0;o<4;o++)r[o]=e[o]*t[0]+e[o+4]*t[1]+e[o+8]*t[2]+e[o+12]*t[3];return i.length?d.multiplyPoint.apply(d,u([r,i.splice(0,1)[0]],i)):r},multiplyMatrices:function(t,e){for(var i=[],n=2;n<arguments.length;n++)i[n-2]=arguments[n];for(var r=[],o=0;o<4;o++)for(var s=0;s<4;s++)r[4*o+s]=t[4*o]*e[s]+t[4*o+1]*e[s+4]+t[4*o+2]*e[s+8]+t[4*o+3]*e[s+12];return i.length?d.multiplyMatrices.apply(d,u([r,i.splice(0,1)[0]],i)):r},rotateByArbitrayAxis:function(t,e,i,n){var r=Math.cos,o=Math.sin,s=Math.pow,a=1-r(n),h=r(n),l=o(n);return[a*s(t,2)+h,a*t*e-l*i,a*t*i+l*e,0,a*t*e+l*i,a*s(e,2)+h,a*e*i-l*t,0,a*t*i-l*e,a*e*i+l*t,a*s(i,2)+h,0,0,0,0,1]},multiplyArrayOfMatrices:function(t){for(var e=t[0],i=1;i<t.length;i++)e=d.multiplyMatrices(e,t[i]);return e},rotateXMatrix:function(t){var e=Math.cos,i=Math.sin;return[1,0,0,0,0,e(t),-i(t),0,0,i(t),e(t),0,0,0,0,1]},rotateYMatrix:function(t){var e=Math.cos,i=Math.sin;return[e(t),0,i(t),0,0,1,0,0,-i(t),0,e(t),0,0,0,0,1]},rotateZMatrix:function(t){var e=Math.cos,i=Math.sin;return[e(t),-i(t),0,0,i(t),e(t),0,0,0,0,1,0,0,0,0,1]},translateMatrix:function(t,e,i){return[1,0,0,0,0,1,0,0,0,0,1,0,t,e,i,1]},scaleMatrix:function(t,e,i){return[t,0,0,0,0,e,0,0,0,0,i,0,0,0,0,1]}},f=function(){function t(t,e,i,n){this.cachedY=new Map,this.precision=1e-5,this.p1={x:t,y:e},this.p2={x:i,y:n}}return t.prototype.getX=function(t){var e=this.p1.x,i=this.p2.x;return 3*e*t*Math.pow(1-t,2)+3*i*Math.pow(t,2)*(1-t)+Math.pow(t,3)},t.prototype.getY=function(t){var e=this.p1.y,i=this.p2.y;return 3*e*t*Math.pow(1-t,2)+3*i*Math.pow(t,2)*(1-t)+Math.pow(t,3)},t.prototype.solveCurveX=function(t){var e,i,n,r=t,o=this.p1.x,s=this.p2.x,a=3*o-3*s+1,h=3*s-6*o,l=3*o;for(var c=0;c<8;c++){if(i=this.getX(r)-t,Math.abs(i)<this.precision)return r;if(e=(3*a*(n=r)+2*h)*n+l,Math.abs(e)<this.precision)break;r-=i/e}var u=1,d=0;for(r=t;u>d;){if(i=this.getX(r)-t,Math.abs(i)<this.precision)return r;i>0?u=r:d=r,r=(u+d)/2}return r},t.prototype.solve=function(t){return this.cachedY.get(t)||this.cachedY.set(t,this.getY(this.solveCurveX(t))),this.cachedY.get(t)},t}(),p=new f(0,0,1,1);new f(.25,.1,.25,1),new f(.42,0,1,1),new f(0,0,.58,1),new f(.42,0,.58,1);var g=function(t,e,i,n){return new(i||(i=Promise))((function(r,o){function s(t){try{h(n.next(t))}catch(t){o(t)}}function a(t){try{h(n.throw(t))}catch(t){o(t)}}function h(t){var e;t.done?r(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(s,a)}h((n=n.apply(t,e||[])).next())}))},v=function(t,e){var i,n,r,o,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(i)throw new TypeError("Generator is already executing.");for(;s;)try{if(i=1,n&&(r=2&o[0]?n.return:o[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,o[1])).done)return r;switch(n=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(r=s.trys,(r=r.length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){s.label=o[1];break}if(6===o[0]&&s.label<r[1]){s.label=r[1],r=o;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(o);break}r[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{i=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}},m=function(){function t(t){this.curBehaviorCanBreak=!1,this.throldDeg=.1*Math.PI,this.viewInstance=t}return t.prototype.handleResize=function(){var t=this.viewInstance,e=this.resizeTimer;clearTimeout(e);this.resizeTimer=setTimeout((function(){var e=t.ref;e.style.width=window.innerWidth+"px",e.style.height=window.innerHeight+"px",e.width=window.innerWidth*t.dpr,e.height=window.innerHeight*t.dpr,t.viewWidth=e.width,t.viewHeight=e.height,t.gl.viewport(0,0,t.viewWidth,t.viewHeight);var i=t.createPerspectiveMatrix();t.gl.uniformMatrix4fv(t.gl.getUniformLocation(t.shaderProgram,"uProjectionMatrix"),!1,i),t.draw(t.curIndex)}),100)},t.prototype.handleDoubleClick=function(t){var e=t.clientX,i=t.clientY,n=this.viewInstance,r=n.decideScaleRatio(e,i),o=r[0],s=r[1],a=r[2],h=r[3];return n.scaleZPosition({scaleX:o,scaleY:s,dx:a,dy:h})},t.prototype.handleMoveEnlage=function(t,e,i){var n=this.viewInstance;t*=n.dpr,e*=-n.dpr,n.dpr,n.curPlane=n.positions.slice(n.curPointAt,n.curPointAt+16),n.transformCurplane(d.translateMatrix(t,e,0)),n.bindPostion(),n.drawPosition()},t.prototype.handleMoveNormal=function(t,e){var i=this.viewInstance,n=Math.PI/2,r=-e/(i.viewWidth/i.dpr)*n;i.rotatePosition(r)},t.prototype.handleZoom=function(t,e,i,n){var r=this.viewInstance,o=r.imgShape,s=o[0];o[1];var a=r.imgShapeInitinal,h=a[0];a[1],s=Math.abs(s),h=Math.abs(h);var l=r.viewRect.width*r.dpr;l*t>4*s||(l*t<h||(i*=r.dpr,n*=-r.dpr,r.zoomCurPlan(t,e,i,n)))},t.prototype.handleTEndEnNormal=function(t,e){return g(this,void 0,void 0,(function(){var t,i,n,r,o,s;return v(this,(function(a){switch(a.label){case 0:return t=this.viewInstance,i=Math.PI/2,n=-e/(t.viewWidth/t.dpr)*i,r=n/Math.abs(n),t.baseModel=t.modelMatrix,Math.abs(n)>=this.throldDeg?(o=t.curIndex,-1!=(s=t.curIndex+1*r)&&s!=t.imgUrls.length?[3,2]:(t.curIndex=o,[4,t.rotate(-n)])):[3,6];case 1:return a.sent(),[3,5];case 2:return[4,t.rotate(r*Math.PI/2-n)];case 3:return a.sent(),t.curIndex=s,t.modelMatrix=t.baseModel=t.initialModel,t.gl.uniformMatrix4fv(t.gl.getUniformLocation(t.shaderProgram,"uModelViewMatrix"),!1,t.modelMatrix),[4,t.draw(s)];case 4:a.sent(),a.label=5;case 5:return[3,8];case 6:return[4,t.rotate(-n)];case 7:a.sent(),a.label=8;case 8:return t.modelMatrix=t.baseModel=t.initialModel,[2,"handled"]}}))}))},t.prototype.handleTEndEnlarge=function(t,e,i,n){return g(this,void 0,void 0,(function(){var t;return v(this,(function(n){switch(n.label){case 0:return t=this.viewInstance,e*=t.dpr,i*=-t.dpr,t.dpr,this.curBehaviorCanBreak=!0,[4,t.moveCurPlane(e,i,0)];case 1:return n.sent(),this.curBehaviorCanBreak=!1,0!==e&&(t.isBoudriedSide=!0),[2]}}))}))},t.prototype.moveCurPlaneTo=function(t,e,i){return g(this,void 0,void 0,(function(){var i;return v(this,(function(n){switch(n.label){case 0:return i=this.viewInstance,t*=i.dpr,e*=-i.dpr,i.dpr,this.curBehaviorCanBreak=!0,[4,i.moveCurPlane(t,e,0)];case 1:return n.sent(),this.curBehaviorCanBreak=!1,[2]}}))}))},t}();var w=function(t,e,i){var n=t.naturalWidth,r=t.naturalHeight,o=document.createElement("canvas"),s=o.getContext("2d"),a=window.devicePixelRatio||1;return o.width=e*a,o.height=i*a,s.drawImage(t,0,0,n,r,0,0,e*a,i*a),o},x=function(t,e,i,n){return new(i||(i=Promise))((function(r,o){function s(t){try{h(n.next(t))}catch(t){o(t)}}function a(t){try{h(n.throw(t))}catch(t){o(t)}}function h(t){var e;t.done?r(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(s,a)}h((n=n.apply(t,e||[])).next())}))},y=function(t,e){var i,n,r,o,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(i)throw new TypeError("Generator is already executing.");for(;s;)try{if(i=1,n&&(r=2&o[0]?n.return:o[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,o[1])).done)return r;switch(n=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(r=s.trys,(r=r.length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){s.label=o[1];break}if(6===o[0]&&s.label<r[1]){s.label=r[1],r=o;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(o);break}r[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{i=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}},M=function(t,e){for(var i=0,n=e.length,r=t.length;i<n;i++,r++)t[r]=e[i];return t},b=new f(.18,.96,.18,.96);function E(t){return 0==(t&t-1)}var T,P=function(){function t(t){var e=t.images;this.dpr=window.devicePixelRatio||1,this.fieldOfViewInRadians=.25*Math.PI,this.zNear=100,this.zFar=1e4,this.curIndex=0,this.defaultAnimateTime=300,this.initialModel=[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1],this.baseModel=[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1],this.modelMatrix=[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1],this.indinces=new Map,this.positions=[],this.imgs=[],this.imgUrls=[],this.imgShape=[],this.imgShapeInitinal=[],this.textures=new Map,this.texturesOther=new Map,this.positionBuffer=null,this.curPlane=[],this.isBoudriedSide=!1,this.curAimateBreaked=!1,this.imgId=0,this.gl=this.intialView(),this.gl.pixelStorei(this.gl.UNPACK_FLIP_Y_WEBGL,1),this.imgUrls=e;var i=this.gl;i.enable(i.DEPTH_TEST),i.depthFunc(i.LEQUAL),this.readyWebgl(),this.initData(),this.contextHandle(),this.eventsHanlder=new m(this)}return t.prototype.contextHandle=function(){var t=this,e=this.ref;e.addEventListener("webglcontextlost",(function(e){t.textures.clear(),t.texturesOther.clear(),t.ref.parentNode.removeChild(t.ref)})),e.addEventListener("webglcontextrestored",(function(e){t.gl=t.intialView(),t.gl.pixelStorei(t.gl.UNPACK_FLIP_Y_WEBGL,1);var i=t.gl;i.enable(i.DEPTH_TEST),i.depthFunc(i.LEQUAL),t.readyWebgl(),t.initData(),t.contextHandle()}))},t.prototype.readyWebgl=function(){this.shaderProgram=this.bindShader(this.gl,"precision mediump float;\n\nvarying vec2 vTextureCoord;\nuniform sampler2D uSampler0;\nuniform vec2 iResolution;\nvoid main() {\n\n    // vec2 uv = vec2(gl_FragCoord.xy / iResolution.xy);\n    vec4 color0 = texture2D(uSampler0, vTextureCoord) ;\n    gl_FragColor = color0;\n}","attribute vec4 aVertexPosition;\nattribute vec2 aTextureCoord;\n\nuniform mat4 uModelViewMatrix;\nuniform mat4 uProjectionMatrix;\n\nvarying mediump vec2 vTextureCoord;\n\nvoid main(void) {\n    gl_Position = uProjectionMatrix * uModelViewMatrix * aVertexPosition;\n    vTextureCoord = aTextureCoord;\n}");var t=this.createPerspectiveMatrix();this.gl.useProgram(this.shaderProgram),this.gl.uniformMatrix4fv(this.gl.getUniformLocation(this.shaderProgram,"uProjectionMatrix"),!1,t),this.gl.uniformMatrix4fv(this.gl.getUniformLocation(this.shaderProgram,"uModelViewMatrix"),!1,this.modelMatrix),this.setTextureCordinate(),this.initOtherTexture()},t.prototype.addImg=function(t,e){var i=this,n=this.imgUrls.length,r=e+1;if(e<=-1?(e=-1,r=0):e>n&&(e=n-1,r=n),this.imgUrls.splice(e+1,0,t),e+1>this.imgs.length?this.imgs[r]=null:this.imgs.splice(e+1,0,null),t instanceof Image)if(void 0===t._id&&(t._id=this.imgId++),t.complete)this.imgUrls[e+1]=this.validateImg(t);else{var o=function(){var e=i.imgUrls.indexOf(t);t.loadError=!0,~[-2,-1,0].indexOf(e-i.curIndex)&&i.draw(i.curIndex)};t.addEventListener("load",(function(){var e=i.imgUrls.indexOf(t);i.imgUrls[e]=i.validateImg(t),~[-2,-1,0].indexOf(e-i.curIndex)&&i.draw(i.curIndex)})),t.addEventListener("error",o),t.addEventListener("abort",o)}~[-2,-1,0].indexOf(e-this.curIndex)&&this.draw(this.curIndex)},t.prototype.delImg=function(t){var e=this.imgUrls.length;t<=-1?t=0:t>=e&&(t=e-1),this.imgUrls.splice(t,1),this.imgs[t]&&this.textures.delete(this.imgs[t]._id),this.imgs.splice(t,1),t-=this.curIndex,~[-1,0,1].indexOf(t)&&this.draw(this.curIndex)},t.prototype.initOtherTexture=function(){var t=this,e=this.gl,i=e.createTexture();this.texturesOther.set(0,i),e.bindTexture(e.TEXTURE_2D,i),i.cubicBgd=!0;e.texImage2D(e.TEXTURE_2D,0,e.RGBA,1,1,0,e.RGBA,e.UNSIGNED_BYTE,new Uint8Array([0,0,0,255]));var n=new Image;n.onload=function(){var i=e.createTexture();t.texturesOther.set(1,i),e.bindTexture(e.TEXTURE_2D,i),t.texImage(n),t.setTexParameteri(n.width,n.height)},n.src="data:image/svg+xml;base64,PHN2ZyB0PSIxNjI1ODExNDgwNTgyIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEzNDIgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjYwNjkiIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIj48cGF0aCBkPSJNMTIxNi4zNTcgMTM5LjAzYy0xMC4xNTctMTEuNDI3LTI0Ljc1OS0xNy43NzUtMzkuOTk1LTE4LjQxTDc0My40IDEwMy40OGwtMzIuMzc3IDczLjAwNiA0NS4wNzQgMTM1Ljg1Ni04MS4yNiAxNTQuMjY3IDMzLjAxMiAxMjQuNDI5IDgyLjUzIDEwNi42NTMgMTE5LjM1LTEwOS44MjdjMTEuNDI3LTEwLjc5MyAyOS44MzctMTAuMTU4IDM5Ljk5NCAxLjkwNGwxNTIuOTk3IDE2NS42OTRjMTAuNzkzIDExLjQyNyAxMC4xNTggMjkuODM3LTEuMjcgNDAuNjMtNS43MTMgNS4wNzgtMTIuNjk2IDguMjUzLTIwLjMxNCA3LjYxOGwtNDE5LjYzLTE2LjUwNi0yMC45NSA2MC4zMSAyMi44NTQgNTMuOTYyIDQ4Mi40OCAxOC40MWMzMS43NDIgMS4yNyA1OC40MDUtMjMuNDkgNTkuMDQtNTUuMjMxbDI2LjY2My02ODQuMzZjMC42MzUtMTUuMjM2LTQuNDQ0LTMwLjQ3Mi0xNS4yMzYtNDEuMjY1ek05MDYuNTU0IDQ1My4yNzdjLTQ3LjYxMy0xLjkwNC04NC40MzQtNDEuOS04Mi41My04OC44NzggMS45MDUtNDcuNjEzIDQxLjktODQuNDM0IDg4Ljg3OS04Mi41MyA0Ni45NzggMS45MDUgODQuNDM0IDQxLjkgODIuNTMgODguODc5LTEuOTA1IDQ2Ljk3OC00MS45IDg0LjQzNC04OC44NzkgODIuNTN6TTU5NS40ODIgODQ4LjE1bDE0LjYwMS02My40ODQtMzQwLjkxIDIzLjQ4OWMtMTUuODcxIDEuMjctMjkuMjAzLTEwLjE1OC0zMC40NzItMjYuMDI5YTI4LjEyIDI4LjEyIDAgMCAxIDYuOTgzLTIwLjk1TDQ5OC4zNSA0NzEuMDUzYzUuMDc5LTYuMzQ5IDEyLjY5Ny05LjUyMyAyMC45NS05LjUyMyA3LjYxOCAwIDE1LjIzNiAzLjE3NCAyMC45NSA4Ljg4OGw4NC40MzMgODguMjQzLTM2LjE4Ni05My45NTcgNjQuNzU0LTE2Mi41Mi01OS4wNC0xMzAuMTQyIDI0LjEyNC03NC45MTEtNDY0LjcwNCAzMi4zNzdjLTMxLjc0MiAxLjkwNC01NS4yMzIgMjkuMjAyLTUzLjMyNyA2MC45NDVsNDYuOTc4IDY4NC4zNmMwLjYzNSAxNS4yMzUgNy42MTggMjkuMjAyIDE5LjY4IDM4LjcyNSAxMS40MjggMTAuMTU3IDI2LjAyOSAxNS4yMzYgNDEuMjY1IDEzLjk2Nmw0MTUuMTg3LTI4LjU2OC0yNy45MzMtNTAuNzg3eiIgcC1pZD0iNjA3MCIgZmlsbD0iI2JmYmZiZiIvPjwvc3ZnPg=="},t.prototype.initData=function(){this.draw(this.curIndex)},t.prototype.slideNext=function(){return x(this,void 0,void 0,(function(){var t=this;return y(this,(function(e){return this.curIndex==this.imgUrls.length-1?[2,[!0]]:[2,this.slide(.5*Math.PI,(function(){return t.curIndex++}))]}))}))},t.prototype.slideBefore=function(){return x(this,void 0,void 0,(function(){var t=this;return y(this,(function(e){return 0==this.curIndex?[2,[!0]]:[2,this.slide(-.5*Math.PI,(function(){return t.curIndex--}))]}))}))},t.prototype.slide=function(t,e){return x(this,void 0,void 0,(function(){return y(this,(function(i){switch(i.label){case 0:return this.baseModel=this.modelMatrix,[4,this.rotate(t)];case 1:return i.sent(),e(),this.modelMatrix=this.baseModel=this.initialModel,this.gl.uniformMatrix4fv(this.gl.getUniformLocation(this.shaderProgram,"uModelViewMatrix"),!1,this.modelMatrix),[4,this.draw(this.curIndex)];case 2:return i.sent(),this.modelMatrix=this.baseModel=this.initialModel,[2,[!1]]}}))}))},t.prototype.rotate=function(t){var e,i=this;return this.animate({allTime:this.defaultAnimateTime,timingFun:p,ends:[t],playGame:(e=i.rotatePosition.bind(i),function(t){i.clear(),e(t)})})},t.prototype.rotateZ=function(t){var e=this;this.curPlane=this.positions.slice(this.curPointAt,this.curPointAt+16);var i=this.imgShape,n=this.imgShapeInitinal;this.imgShape=d.multiplyPoint(i,d.rotateZMatrix(t)),this.imgShapeInitinal=d.multiplyPoint(n,d.rotateZMatrix(t));var r=this.curCenterCoordinate,o=-r[0],s=-r[1];return this.animate({allTime:this.defaultAnimateTime,timingFun:p,ends:[t,o,s],playGame:function(){for(var t=[],i=0;i<arguments.length;i++)t[i]=arguments[i];e.transformCurplane(d.translateMatrix(t[1],t[2],0),d.rotateZMatrix(t[0])),e.bindPostion(),e.drawPosition()}})},t.prototype.genPostion=function(t,e,i){var n,r=2*Math.tan(this.fieldOfViewInRadians/2),o=-this.viewHeight/r-0,s=this.viewWidth,a=o-(s-t)/2,h=[[-s/2,-e/2,a-t,1,-s/2,-e/2,a,1,-s/2,e/2,a,1,-s/2,e/2,a-t,1],[-t/2,-e/2,o,1,t/2,-e/2,o,1,t/2,e/2,o,1,-t/2,e/2,o,1],[s/2,-e/2,a,1,s/2,-e/2,a-t,1,s/2,e/2,a-t,1,s/2,e/2,a,1]],l=i-this.curIndex;l+=1,(n=this.positions).push.apply(n,h[l])},t.prototype.updatePosition=function(t,e){var i=-this.viewHeight/(2*Math.tan(this.fieldOfViewInRadians/2))-0,n=this.viewWidth,r=t.naturalWidth,o=t.naturalHeight;t.loadError&&(r=o=200);var s=this.decideImgViewSize(r*this.dpr,o*this.dpr),a=s[0],h=s[1];0==e&&(this.imgShape=[r*this.dpr,o*this.dpr,0,1],this.imgShapeInitinal=[a,h,0,1]);for(var l=i-(n-a)/2,c=[[-n/2,-h/2,l-a,1,-n/2,-h/2,l,1,-n/2,h/2,l,1,-n/2,h/2,l-a,1],[-a/2,-h/2,i,1,a/2,-h/2,i,1,a/2,h/2,i,1,-a/2,h/2,i,1],[n/2,-h/2,l,1,n/2,-h/2,l-a,1,n/2,h/2,l-a,1,n/2,h/2,l,1]],u=e,d=this.curPointAt+16*u,f=c[u+=1],p=d;p<d+16;p++)this.positions[p]=f[p-d]},t.prototype.bindPostion=function(){var t=this.gl,e=this.positions;if(this.positionBuffer)this.gl.bufferData(this.gl.ARRAY_BUFFER,new Float32Array(e),this.gl.DYNAMIC_DRAW);else{var i=this.gl.createBuffer();this.positionBuffer=i,this.gl.bindBuffer(this.gl.ARRAY_BUFFER,i),this.gl.bufferData(this.gl.ARRAY_BUFFER,new Float32Array(e),this.gl.DYNAMIC_DRAW);var n=t.FLOAT,r=t.getAttribLocation(this.shaderProgram,"aVertexPosition");t.vertexAttribPointer(r,4,n,!1,0,0),t.enableVertexAttribArray(r)}},t.prototype.drawPosition=function(){this.clear();var t=this.gl;t.bindTexture(t.TEXTURE_2D,this.texturesOther.get(0));for(var e=0;e<12;e+=4)this.bindIndex(e);var i=(this.positions.length/4-12)/4,n=this.curIndex-1;n<0&&(n=0);for(e=0;e<i;e++,n++){var r=this.imgs[n];r?this.bindTexture(r,r._id):console.log("shouldn't have"),this.bindIndex(12+4*e)}},t.prototype.rotatePosition=function(t){var e=-this.viewHeight/(2*Math.tan(this.fieldOfViewInRadians/2))-0,i=this.viewWidth/2;this.modelMatrix=d.multiplyMatrices(this.baseModel,d.translateMatrix(0,0,i-e),d.rotateYMatrix(t),d.translateMatrix(0,0,e-i)),this.gl.uniformMatrix4fv(this.gl.getUniformLocation(this.shaderProgram,"uModelViewMatrix"),!1,this.modelMatrix),this.drawPosition()},t.prototype.scaleZPosition=function(t){var e=this,i=t.scaleX,n=t.scaleY,r=t.dx,o=t.dy;this.curPlane=this.positions.slice(this.curPointAt,this.curPointAt+16);return this.animate({allTime:this.defaultAnimateTime,timingFun:p,ends:[i,n,r,o],playGame:function(){for(var t=[],i=0;i<arguments.length;i++)t[i]=arguments[i];t[0]+=1,t[1]+=1,e.transformCurplane(d.scaleMatrix(t[0],t[1],1),d.translateMatrix(t[2],t[3],0)),e.bindPostion(),e.drawPosition()}})},t.prototype.moveCurPlane=function(t,e,i){var n=this,r=t,o=e;this.curPlane=this.positions.slice(this.curPointAt,this.curPointAt+16);return this.animate({allTime:800,timingFun:b,ends:[r,o],playGame:function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];n.transformCurplane(d.translateMatrix(t[0],t[1],0)),n.bindPostion(),n.drawPosition()}})},t.prototype.transformCurplane=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];for(var n=this.positions,r=this.curPlane,o=this.curPointAt;o<this.curPointAt+16;o+=4)for(var s=o-this.curPointAt,a=r[s],h=r[s+1],l=r[s+2],c=r[s+3],u=d.multiplyPoint.apply(d,M([[a,h,l,c],t],e)),f=o;f<4+o;f++)n[f]=u[f-o]},t.prototype.zoomCurPlan=function(t,e,i,n){this.curPlane=this.positions.slice(this.curPointAt,this.curPointAt+16),this.transformCurplane(d.scaleMatrix(t,e,1),d.translateMatrix(i,n,0)),this.bindPostion(),this.drawPosition()},t.prototype.setTextureCordinate=function(){var t=this.gl,e=this.gl.createBuffer();t.bindBuffer(this.gl.ARRAY_BUFFER,e);t.bufferData(this.gl.ARRAY_BUFFER,new Float32Array([0,0,1,0,1,1,0,1,0,0,1,0,1,1,0,1,0,0,1,0,1,1,0,1,0,0,1,0,1,1,0,1,0,0,1,0,1,1,0,1,0,0,1,0,1,1,0,1]),this.gl.STATIC_DRAW);var i=t.FLOAT;t.bindBuffer(t.ARRAY_BUFFER,e);var n=t.getAttribLocation(this.shaderProgram,"aTextureCoord");t.vertexAttribPointer(n,2,i,!1,0,0),t.enableVertexAttribArray(n),t.activeTexture(t.TEXTURE0),t.uniform1i(t.getUniformLocation(this.shaderProgram,"uSampler0"),0)},t.prototype.bindTexture=function(t,e){if(t.loadError)this.updateOtherTexture(1);else if(t.complete)if(this.textures.get(e))this.updateTexture(e,t);else{var i=this.gl,n=i.createTexture();i.bindTexture(i.TEXTURE_2D,n),this.textures.set(e,n),this.texImage(t),this.setTexParameteri(t.width,t.height)}else this.updateOtherTexture(0)},t.prototype.updateTexture=function(t,e){var i=this.gl;i.bindTexture(i.TEXTURE_2D,this.textures.get(t)),this.setTexParameteri(e.width,e.height)},t.prototype.updateOtherTexture=function(t){var e=this.gl;e.bindTexture(e.TEXTURE_2D,this.texturesOther.get(t)),this.setTexParameteri(0,3)},t.prototype.texImage=function(t){var e=this.gl,i=e.RGBA,n=e.RGBA,r=e.UNSIGNED_BYTE;e.texImage2D(e.TEXTURE_2D,0,i,n,r,t)},t.prototype.setTexParameteri=function(t,e){var i=this.gl;E(t)&&E(e)?i.generateMipmap(i.TEXTURE_2D):(i.texParameteri(i.TEXTURE_2D,i.TEXTURE_WRAP_S,i.CLAMP_TO_EDGE),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_WRAP_T,i.CLAMP_TO_EDGE),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_MIN_FILTER,i.LINEAR),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_MAG_FILTER,i.LINEAR))},t.prototype.bindIndex=function(t){var e=this.gl,i=[t,t+1,t+2,t,t+2,t+3],n=e.TRIANGLES;if(this.indinces.has(t)){var r=this.indinces.get(t);e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,r);var o=i.length,s=e.UNSIGNED_SHORT,a=0;e.drawElements(n,o,s,a)}else{var h=this.gl.createBuffer();this.indinces[t]=h,this.indinces.set(t,h),e.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER,h),this.gl.bufferData(this.gl.ELEMENT_ARRAY_BUFFER,new Uint16Array(i),this.gl.STATIC_DRAW),e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,h);o=i.length,s=e.UNSIGNED_SHORT,a=0;e.drawElements(n,o,s,a)}},t.prototype.generateCube=function(t,e){var i;t=this.viewWidth,e=this.viewHeight;var n=-this.viewHeight/(2*Math.tan(this.fieldOfViewInRadians/2))-0-.1,r=[-(t-=.1)/2,-(e-=.1)/2,n-t,1,-t/2,-e/2,n,1,-t/2,e/2,n,1,-t/2,e/2,n-t,1,-t/2,-e/2,n,1,t/2,-e/2,n,1,t/2,e/2,n,1,-t/2,e/2,n,1,t/2,-e/2,n,1,t/2,-e/2,n-t,1,t/2,e/2,n-t,1,t/2,e/2,n,1];(i=this.positions).splice.apply(i,M([0,0],r))},t.prototype.decideScaleRatio=function(t,e){var i=0,n=0,r=this.viewWidth/2,o=this.viewHeight/2,s=this.viewRect,a=s.width*this.dpr,h=s.height*this.dpr,l=this.imgShape,c=l[0],u=l[1];c=Math.abs(c),u=Math.abs(u);var d,f,p=0,g=0;if(t*=this.dpr,e*=this.dpr,this.isEnlargementForScale){var v=this.imgShapeInitinal,m=v[0],w=v[1];d=(i=Math.abs(m))/a-1,f=(n=Math.abs(w))/h-1;var x=this.curCenterCoordinate;p=-x[0]*(1+d),g=-x[1]*(1+f)}else this.curIsLongImg()?n=u/c*(i=this.viewWidth>c?c:this.viewWidth):(i=c,n=u),p=-(t-r)*(d=i/a-1),g=(e-o)*(f=n/h-1),this.curIsLongImg()&&(p=0);return[d,f,p,g]},t.prototype.decideImgViewSize=function(t,e){var i=0,n=0;return(n=e/t*(i=this.viewWidth>=t?t:this.viewWidth))>this.viewHeight&&(i=(n=this.viewHeight)*t/e),[i,n]},t.prototype.draw=function(t){this.positions=[];for(var e=this.imgUrls.length,i=0,n=0,r=t-1;r<=t+1;r++)if(-1!==r&&r<=e-1){var o=void 0;this.imgs[r]?o=this.imgs[r]:"string"==typeof this.imgUrls[r]?o=this.loadImage(this.imgUrls[r],r):void 0===(o=this.imgUrls[r])._id&&(this.imgUrls[r]=this.validateImg(o),o=this.imgUrls[r]),this.imgs[r]=o;var s=o.naturalWidth,a=o.naturalHeight;o.loadError&&(s=a=200);var h=this.decideImgViewSize(s*this.dpr,a*this.dpr),l=h[0],c=h[1];r==this.curIndex&&(this.imgShape=[s*this.dpr,a*this.dpr,0,1],this.imgShapeInitinal=[l,c,0,1]),this.genPostion(l,c,r),i=Math.max(l,i),n=Math.max(c,n)}this.generateCube(i,n),this.bindPostion(),this.drawPosition()},t.prototype.createPerspectiveMatrix=function(){var t=this.fieldOfViewInRadians,e=this.viewWidth/this.viewHeight,i=this.zNear,n=this.zFar,r=1/Math.tan(t/2),o=1/(i-n);return[r/e,0,0,0,0,r,0,0,0,0,(i+n)*o,-1,0,0,i*n*o*2,0]},Object.defineProperty(t.prototype,"curPointAt",{get:function(){var t=64;return 0==this.curIndex&&(t=48),t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"IsBoundaryLeft",{get:function(){var t=this.viewRect;return Math.round(t.left)>=0&&this.isBoudriedSide},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isBoundaryRight",{get:function(){var t=this.viewRect;return Math.round(t.right*this.dpr)<=Math.round(this.viewWidth/1)&&this.isBoudriedSide},enumerable:!1,configurable:!0}),t.prototype.curIsLongImg=function(){var t=this.imgShape,e=t[0],i=t[1];return 2*Math.abs(e)<=Math.abs(i)},Object.defineProperty(t.prototype,"curCenterCoordinate",{get:function(){var t=this.curPointAt;return[(this.positions[t]+this.positions[t+8])/2,(this.positions[t+1]+this.positions[t+9])/2]},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"viewRect",{get:function(){for(var t=-this.viewWidth/2,e=this.viewHeight/2,i=this.curPointAt,n=1/0,r=-1/0,o=1/0,s=-1/0,a=i;a<i+16;a+=4){var h=this.positions[a],l=this.positions[a+1];n=Math.min(h,n),r=Math.max(h,r),o=Math.min(l,o),s=Math.max(l,s)}var c=Math.abs(n-r),u=Math.abs(o-s);return{left:(n-t)/this.dpr,right:(r-t)/this.dpr,width:c/this.dpr,height:u/this.dpr,top:-(s-e)/this.dpr,bottom:-(o-e)/this.dpr}},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"curPlanePosition",{get:function(){return this.curPointAt,[]},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isEnlargement",{get:function(){var t=this.imgShapeInitinal;t[0],t[1];var e=this.viewRect;return e.width*this.dpr-1>this.viewWidth||e.height*this.dpr-1>this.viewHeight},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isEnlargementForScale",{get:function(){var t=this.imgShapeInitinal,e=t[0],i=t[1],n=this.viewRect;return Math.round(n.width*this.dpr)>Math.round(Math.abs(e))||Math.round(n.height*this.dpr)>Math.round(Math.abs(i))},enumerable:!1,configurable:!0}),t.prototype.isLoadingError=function(t){return 0==arguments.length&&(t=this.curIndex),this.imgs[t].loadError},t.prototype.loadImage=function(t,e){var i=this,n=new Image;return n._id=this.imgId++,this.imgs[e]=n,n.onload=function(){i.handleImgLoaded(n,e)},n.onerror=function(){n.loadError=!0,i.handleImgLoaded(n,e)},n.onabort=function(){n.loadError=!0,i.handleImgLoaded(n,e)},n.crossOrigin="anonymous",n.src=t,n},t.prototype.handleImgLoaded=function(t,e){e=this.imgs.indexOf(t),t.loadError||(t=this.validateImg(t),this.imgs[e]=t),~[-1,0,1].indexOf(e-this.curIndex)&&(this.updatePosition(t,e-this.curIndex),this.bindPostion(),this.drawPosition())},t.prototype.validateImg=function(t){var e=this.gl.MAX_TEXTURE_SIZE,i=t.naturalWidth,n=t.naturalHeight;if(Math.max(n,i)>=e){var r=this.dpr,o=e/r,s=n/i*o;s>=e&&(o=i/n*(s=e/r));var a=w(t,o,s);return a._id=this.imgId++,a.naturalHeight=s,a.naturalWidth=o,a.complete=!0,a}return t},t.prototype.clear=function(){var t=this.gl;t.clearColor(0,0,0,1),t.clearDepth(1),t.clear(t.COLOR_BUFFER_BIT|t.DEPTH_BUFFER_BIT)},t.prototype.bindShader=function(t,e,i){var n=this.loadShader(t,t.VERTEX_SHADER,i),r=this.loadShader(t,t.FRAGMENT_SHADER,e),o=t.createProgram();return t.attachShader(o,n),t.attachShader(o,r),t.linkProgram(o),t.getProgramParameter(o,t.LINK_STATUS)?o:(console.error("Unable to initialize the shader program: "+t.getProgramInfoLog(o)),null)},t.prototype.loadShader=function(t,e,i){var n=t.createShader(e);return t.shaderSource(n,i),t.compileShader(n),t.getShaderParameter(n,t.COMPILE_STATUS)?n:(console.error("An error occurred compiling the shaders: "+t.getShaderInfoLog(n)),t.deleteShader(n),null)},t.prototype.createPlane=function(t){return t.x,t.y,t.width,t.height,{}},t.prototype.intialView=function(){var t=document.createElement("canvas");t.style.cssText="\n            position: absolute;\n            top: 0;\n            left:0;\n            z-index: 9;\n            width:"+window.innerWidth+"px;\n            height:"+window.innerHeight+"px;\n            user-select:none;\n            font-size:0;\n        ",t.width=window.innerWidth*this.dpr,t.height=window.innerHeight*this.dpr,this.ref=t;var e=t.getContext("webgl",{antialias:!0});return e||console.error("webgl is not supported. please use before version."),this.viewWidth=t.width,this.viewHeight=t.height,e},t.prototype.animate=function(t){var e,i=this,n=t.allTime,r=t.timingFun,o=t.ends,s=t.playGame,a=t.callback,h=Date.now(),l=h,c=new Promise((function(t){return e=t})),u=o.length,d=function(){if(i.curAimateBreaked)return e([!1,3]),void(i.curAimateBreaked=!1);var t=l-h;t>n&&(t=n);var c=t/n;c>1&&(c=1);var f=r.solve(c);f>=1&&(f=1);var p=new Array(u);o.forEach((function(t,e){p[e]=t*f})),s.apply(void 0,p),c<1?requestAnimationFrame(d):(a&&a(),e([!1,1])),l=Date.now()};return d(),c},t}(),I=function(t,e,i,n){var r,o=arguments.length,s=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(r=t[a])&&(s=(o<3?r(s):o>3?r(e,i,s):r(e,i))||s);return o>3&&s&&Object.defineProperty(e,i,s),s},A=function(t,e,i,n){return new(i||(i=Promise))((function(r,o){function s(t){try{h(n.next(t))}catch(t){o(t)}}function a(t){try{h(n.throw(t))}catch(t){o(t)}}function h(t){var e;t.done?r(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(s,a)}h((n=n.apply(t,e||[])).next())}))},S=function(t,e){var i,n,r,o,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(i)throw new TypeError("Generator is already executing.");for(;s;)try{if(i=1,n&&(r=2&o[0]?n.return:o[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,o[1])).done)return r;switch(n=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(r=s.trys,(r=r.length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){s.label=o[1];break}if(6===o[0]&&s.label<r[1]){s.label=r[1],r=o;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(o);break}r[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{i=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}},D=function(){function t(t){this.options=t,this.showTools=!0,this.lastClick=-1/0,this.curIndex=0,this.imgContainerMoveX=0,this.imgContainerMoveY=0,this.slideTime=300,this.zoomScale=.05,this.isZooming=!1,this.isAnimating=!1,this.isEnlargeMove=!1,this.isNormalMove=!1,this.normalMoved=!1,this.maxMovePointCounts=3,this.touchIdentifier=0,this.prefix="__",this.defToggleClass="defToggleClass",this.movePoints=[],this.fingerDirection="",this.moveStartTime=0,this.moveEndTime=0,this.doubleClickDuration=300,this.initalMatrix=[[1,0,0,0],[0,1,0,0],[0,0,1,0],[0,0,1,1]],t.selector&&this.bindTrigger(),this.options.imgs||(this.options.imgs=[]),this.actionExecutor=new P({images:this.options.imgs}),this.taskExecuteAfterTEnd=new Map,this.envClient=this.testEnv(),this.genFrame(),this.handleReausetAnimate(),this.imgContainer=this.ref.querySelector("."+this.prefix+"imgContainer"),this.imgContainer.matrix=this.initalMatrix,this[this.envClient+"Initial"]()}return t.prototype.handleZoom=function(t){},t.prototype.handleMove=function(t){},t.prototype.handleMoveNormal=function(t){},t.prototype.handleMoveEnlage=function(t){},t.prototype.rotateLeft=function(t){},t.prototype.rotateRight=function(t){},t.prototype.autoMove=function(t,e,i,n){return n.maxTop,n.minTop,n.maxLeft,n.minLeft,Promise.resolve(1)},t.prototype.insertImageAfter=function(t,e){this.actionExecutor.addImg(t,e)},t.prototype.delImage=function(t){this.actionExecutor.delImg(t)},t.prototype.mobileInitial=function(){this.ref.addEventListener("touchstart",this.handleTouchStart.bind(this)),this.ref.addEventListener("touchmove",this.handleMove.bind(this)),this.ref.addEventListener("touchend",this.handleToucnEnd.bind(this)),this.ref.querySelector("."+this.prefix+"close").addEventListener("touchstart",this.close.bind(this)),this.handleResize=this.handleResize.bind(this),window.addEventListener("resize",this.handleResize),window.addEventListener("orientationchange",this.handleResize)},t.prototype.handleResize=function(){this.actionExecutor.eventsHanlder.handleResize()},t.prototype.bindTrigger=function(){var t=[],e=document.querySelectorAll(this.options.selector);e.length,e.forEach((function(e,i){t.push(e.dataset.src||e.src)})),this.options.imgs=t;var i=this;e.forEach((function(t,e){t.addEventListener("click",(function(t){i.show(e)}))}))},t.prototype.addTouchEndTask=function(t,e){this.taskExecuteAfterTEnd.has(t)||this.taskExecuteAfterTEnd.set(t,e)},t.prototype.handleTouchStart=function(t){switch(t.preventDefault(),t.touches.length){case 1:this.handleOneStart(t);break;case 2:this.handleTwoStart(t)}},t.prototype.handleTwoStart=function(t){this.curPoint1={x:t.touches[0].clientX,y:t.touches[0].clientY},this.curPoint2={x:t.touches[1].clientX,y:t.touches[1].clientY}},t.prototype.handleOneStart=function(t){var e=this,i=t.target.dataset.type;this[i]?this[i](t):(Date.now()-this.lastClick<this.doubleClickDuration?(clearTimeout(this.performerClick),this.handleDoubleClick(t)):this.performerClick=setTimeout((function(){e.handleClick(t)}),this.doubleClickDuration),this.lastClick=Date.now(),this.getMovePoints(t),this.startXForDirection=t.touches[0].clientX)},t.prototype.handleClick=function(t){var e=this.ref.querySelector("."+this.prefix+"close"),i=this.ref.querySelector("."+this.prefix+"bottom");this.showTools=!this.showTools,this.isAnimating,this.showTools?(e.style.display="block",i.style.display="block"):(e.style.display="none",i.style.display="none")},t.prototype.handleDoubleClick=function(t){return A(this,void 0,void 0,(function(){return S(this,(function(e){switch(e.label){case 0:return this.isAnimating?[2]:(this.isAnimating=!0,[4,this.actionExecutor.eventsHanlder.handleDoubleClick(t.touches[0])]);case 1:return e.sent(),this.isAnimating=!1,[2]}}))}))},t.prototype.handleToucnEnd=function(t){t.preventDefault(),Array.from(this.taskExecuteAfterTEnd.values()).sort((function(t,e){return e.priority-t.priority})).forEach((function(e){e.callback(t)})),this.taskExecuteAfterTEnd.clear()},t.prototype.handleTEndEnlarge=function(t){return A(this,void 0,void 0,(function(){var e,i,n,r,o,s,a,h,l,c,u,d,f,p,g,v,m,w,x,y,M,b,E,T,P;return S(this,(function(I){switch(I.label){case 0:return e=this.actionExecutor,i=e.viewRect,n=e.viewWidth/e.dpr,r=e.viewHeight/e.dpr,o=i.width,s=i.height,a=i.top,h=i.left,l=i.right,c=0,u=r-s,d=0,f=n-o,p=i.top,g=i.left,v=!1,m=!1,w=0,x=0,g>d?(w=d-g,m=!0):g<f&&(w=f-g,m=!0),p>c?(x=c-p,v=!0):p<u&&(x=u-p,v=!0),h>=0&&l<=n&&(m=!1,w=0),s<=r&&(v=!1,x=0),m||v?(this.isAnimating=!0,[4,e.eventsHanlder.handleTEndEnlarge(t,w,x,0)]):[3,2];case 1:return I.sent(),this.isAnimating=!1,[3,4];case 2:return this.moveEndTime=Date.now(),y={x:this.startX,y:this.startY},M={x:this.touchStartX,y:this.touchStartY},b=y.x-M.x,E=y.y-M.y,T=180*Math.atan2(E,b)/Math.PI,this.moveEndTime-this.moveStartTime<90&&Math.abs(b)+Math.abs(E)>5?(P={maxTop:c,minTop:u,maxLeft:d,minLeft:n-o},this.isAnimating=!0,[4,this.autoMove(T,h,a,P)]):[3,4];case 3:I.sent(),this.isAnimating=!1,I.label=4;case 4:return this.moveStartTime=0,[2]}}))}))},t.prototype.handleTEndEnNormal=function(t){return A(this,void 0,void 0,(function(){var e,i,n;return S(this,(function(r){switch(r.label){case 0:return this.isAnimating?[2]:(e=t.changedTouches[0].clientX,i=this.actionExecutor.eventsHanlder,0===(n=e-this.touchStartX)?[2]:(this.isAnimating=!0,[4,i.handleTEndEnNormal(t,n)]));case 1:return r.sent(),this.isAnimating=!1,[2]}}))}))},t.prototype.genFrame=function(){var t=this,e=this.options.imgs;!e||e.length,this.imgsNumber=e.length,this.curIndex=0;var i='\n                <div class="'+this.prefix+'close">\n                    <svg t="1563161688682" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5430">\n                        <path d="M10.750656 1013.12136c-13.822272-13.822272-13.822272-36.347457 0-50.169729l952.200975-952.200975c13.822272-13.822272 36.347457-13.822272 50.169729 0 13.822272 13.822272 13.822272 36.347457 0 50.169729l-952.200975 952.200975c-14.334208 14.334208-36.347457 14.334208-50.169729 0z" fill="#ffffff" p-id="5431"></path><path d="M10.750656 10.750656c13.822272-13.822272 36.347457-13.822272 50.169729 0L1013.633296 963.463567c13.822272 13.822272 13.822272 36.347457 0 50.169729-13.822272 13.822272-36.347457 13.822272-50.169729 0L10.750656 60.920385c-14.334208-14.334208-14.334208-36.347457 0-50.169729z" fill="#ffffff" p-id="5432">\n                        </path>\n                    </svg>\n                </div>\n                <div class="'+this.prefix+'imgContainer"></div>\n                <div class="'+this.prefix+'bottom">\n                    '+("pc"==this.envClient?'<div class="'+this.prefix+'item" title="before">\n                        <svg data-type="slideBefore" t="1563884004339" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1099" width="200" height="200"><path d="M170.666667 477.866667L349.866667 298.666667l29.866666 29.866666-149.333333 149.333334h669.866667v42.666666H128l42.666667-42.666666z" p-id="1100" fill="#ffffff"></path></svg>\n                    </div>\n                    <div class="'+this.prefix+'item " title="next">\n                        <svg data-type="slideNext" t="1563884004339" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1099" width="200" height="200"><path d="M849.066667 512l-179.2 179.2-29.866667-29.866667 149.333333-149.333333H128v-42.666667h763.733333l-42.666666 42.666667z" p-id="1100" fill="#ffffff"></path></svg>\n                    </div>':"")+'\n                    <div class="'+this.prefix+'item ">\n                        <svg data-type="rotateLeft" t="1563884004339" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1099" width="200" height="200"><path d="M520.533333 285.866667c140.8 12.8 251.733333 132.266667 251.733334 277.333333 0 153.6-123.733333 277.333333-277.333334 277.333333-98.133333 0-192-55.466667-238.933333-140.8-4.266667-8.533333-4.266667-21.333333 8.533333-29.866666 8.533333-4.266667 21.333333-4.266667 29.866667 8.533333 42.666667 72.533333 119.466667 119.466667 204.8 119.466667 128 0 234.666667-106.666667 234.666667-234.666667s-98.133333-230.4-226.133334-234.666667l64 102.4c4.266667 8.533333 4.266667 21.333333-8.533333 29.866667-8.533333 4.266667-21.333333 4.266667-29.866667-8.533333l-89.6-145.066667c-4.266667-8.533333-4.266667-21.333333 8.533334-29.866667L597.333333 187.733333c8.533333-4.266667 21.333333-4.266667 29.866667 8.533334 4.266667 8.533333 4.266667 21.333333-8.533333 29.866666l-98.133334 59.733334z" p-id="1100" fill="#ffffff"></path></svg>\n                    </div>\n                    <div class="'+this.prefix+'item">\n                        <svg data-type="rotateRight"  t="1563884064737" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1251" width="200" height="200"><path d="M503.466667 285.866667L405.333333 226.133333c-8.533333-8.533333-12.8-21.333333-8.533333-29.866666 8.533333-8.533333 21.333333-12.8 29.866667-8.533334l145.066666 89.6c8.533333 4.266667 12.8 17.066667 8.533334 29.866667l-89.6 145.066667c-4.266667 8.533333-17.066667 12.8-29.866667 8.533333-8.533333-4.266667-12.8-17.066667-8.533333-29.866667l64-102.4c-123.733333 4.266667-226.133333 106.666667-226.133334 234.666667s106.666667 234.666667 234.666667 234.666667c85.333333 0 162.133333-46.933333 204.8-119.466667 4.266667-8.533333 17.066667-12.8 29.866667-8.533333 8.533333 4.266667 12.8 17.066667 8.533333 29.866666-51.2 85.333333-140.8 140.8-238.933333 140.8-153.6 0-277.333333-123.733333-277.333334-277.333333 0-145.066667 110.933333-264.533333 251.733334-277.333333z" p-id="1252" fill="#ffffff"></path></svg>\n                    </div>\n                </div>\n        ',n=/iphone/gi.test(window.navigator.userAgent)&&window.devicePixelRatio&&3===window.devicePixelRatio&&375===window.screen.width&&812===window.screen.height,r=/iphone/gi.test(window.navigator.userAgent)&&window.devicePixelRatio&&3===window.devicePixelRatio&&414===window.screen.width&&896===window.screen.height,o=/iphone/gi.test(window.navigator.userAgent)&&window.devicePixelRatio&&2===window.devicePixelRatio&&414===window.screen.width&&896===window.screen.height,s=n||r||o,a="\n            ."+this.prefix+"imagePreviewer{\n                position: fixed;\n                top:0;\n                left: 100%;\n                width: 100%;\n                height: 100%;\n                background: "+function(e){switch(e){case"conBackground":return"pc"==t.envClient?"rgba(0,0,0,0.8)":"rgba(0,0,0,1)";case"imgWidth":return t.envClient,"100%";case"itemHeight":return"pc"==t.envClient?"100%":"auto";case"itemScroll":return"pc"==t.envClient?"auto ":"hidden";case"item-text-align":return"pc"==t.envClient?"center ":"initial";default:return""}}("conBackground")+";\n                color:#fff;\n                transform: translate3d(0,0,0);\n                transition: left 0.5s;\n                overflow:hidden;\n                user-select: none;\n            }\n            \n            ."+this.prefix+"imagePreviewer."+this.defToggleClass+"{\n                left: 0%;\n            }\n            ."+this.prefix+"imagePreviewer ."+this.prefix+"close{\n                position: absolute;\n                top: 20px;\n                right: 20px;\n                z-index: 10;\n                box-sizing: border-box;\n                width: 22px;\n                height: 22px;\n                cursor:pointer;\n            }\n            ."+this.prefix+"imagePreviewer ."+this.prefix+"close svg{\n                width: 100%;\n                height: 100%;             \n            }\n            ."+this.prefix+"imagePreviewer svg{\n                overflow:visible;\n            }\n            ."+this.prefix+"imagePreviewer svg path{\n                stroke: #948888;\n                stroke-width: 30px;\n            }\n            \n            ."+this.prefix+"imagePreviewer "+this.prefix+".close."+this.prefix+"scroll{\n                height: 0;\n            }\n            ."+this.prefix+"imagePreviewer ."+this.prefix+"imgContainer{\n                position: relative;\n                height: 100%;\n                font-size: 0;\n                white-space: nowrap;\n            }\n            \n            ."+this.prefix+"imagePreviewer ."+this.prefix+"bottom{\n                position: absolute;\n                bottom: "+(s?20:0)+"px;\n                left: 20px;\n                right: 20px;\n                z-index: 10;\n                padding: 0 10px;\n                text-align: center;\n                border-top: 1px solid rgba(255, 255, 255, .2);\n            }\n            ."+this.prefix+"imagePreviewer ."+this.prefix+"bottom ."+this.prefix+"item{\n                display:inline-block;\n                width: 42px;\n                height: 42px;\n                cursor:pointer;\n            }\n            ."+this.prefix+"imagePreviewer ."+this.prefix+"bottom ."+this.prefix+"item svg{\n                box-sizing: border-box;\n                width: 100%;\n                height: 100%;\n                padding:10px;\n            }\n        ";if(this.ref=document.createElement("div"),this.ref.className=this.prefix+"imagePreviewer",this.ref.innerHTML=i,!document.querySelector("#"+this.prefix+"style")){var h=document.createElement("style");h.id=this.prefix+"style",h.innerHTML=a,document.querySelector("head").appendChild(h)}this.ref.querySelector("."+this.prefix+"imgContainer").append(this.actionExecutor.ref),document.body.appendChild(this.ref)},t.prototype.handleReausetAnimate=function(){window.requestAnimationFrame||(window.requestAnimationFrame=window.webkitRequestAnimationFrame||function(t){return window.setTimeout(t,1e3/60),0})},t.prototype.close=function(t){t.stopImmediatePropagation(),clearTimeout(this.performerClick),this[this.envClient+"BeforeClose"](),this.toggleClass(this.ref,this.defToggleClass)},t.prototype.pcBeforeClose=function(){document.body.style.overflow=document.body.dataset.imgPreOverflow},t.prototype.mobileBeforeClose=function(){},t.prototype.show=function(t){this.actionExecutor.curIndex=t,this.actionExecutor.draw(t),this.toggleClass(this.ref,this.defToggleClass)},t.prototype.mobileReadyShow=function(){},t.prototype.pcReadyShow=function(){var t=window.getComputedStyle(document.body);document.body.dataset.imgPreOverflow=t.overflow,document.body.style.overflow="hidden"},t.prototype.toggleClass=function(t,e){var i=t.className.split(" "),n=i.indexOf(e);-1!==n?i.splice(n,1):i.push(e),t.className=i.join(" ")},t.prototype.getMovePoints=function(t){var e=this;if(!(this.movePoints.length>this.maxMovePointCounts)){this.movePoints.push({x:t.touches[0].clientX,y:t.touches[0].clientY});this.addTouchEndTask("resetMovePoints",{priority:1,callback:function(){return e.movePoints=[]}})}},t.prototype.decideMoveDirection=function(){var t=this,e=this.movePoints.length,i=this.movePoints[e-1],n=this.movePoints[0],r=i.x-n.x,o=i.y-n.y,s=180*Math.atan2(o,r)/Math.PI;Math.abs(90-Math.abs(s))<30?this.fingerDirection="vertical":this.fingerDirection="horizontal";this.addTouchEndTask("resetFingerDirection",{priority:1,callback:function(){t.fingerDirection=""}})},t.prototype.destroy=function(){this.ref.parentNode.removeChild(this.ref),window.removeEventListener("resize",this.handleResize),window.removeEventListener("orientationchange",this.handleResize)},t.prototype.testEnv=function(){return/Android|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)?"mobile":"pc"},t=I([o],t)}();return T=D,[s,a,c].forEach((function(t){Object.getOwnPropertyNames(t.prototype).forEach((function(e){T.prototype[e]=t.prototype[e]}))})),t.ImagePreview=D,Object.defineProperty(t,"__esModule",{value:!0}),t}({});
