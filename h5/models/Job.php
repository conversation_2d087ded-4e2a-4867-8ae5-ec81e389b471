<?php

namespace h5\models;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobCategoryRelation;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseOffSiteJobApply;
use common\base\models\BaseSystemConfig;
use common\base\models\BaseWelfareLabel;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use yii\base\Exception;
use yii\db\Expression;
use yii\helpers\Url;

class Job extends BaseJob
{
    /**
     * 搜索职位
     * @throws \Exception
     */

    public static function search($data, $needPageInfo = false, $needOffLine = false): array
    {
        $ids = [];
        if (!empty($data['wageId'])) {
            $wageArr = explode('_', $data['wageId']);
            $jobId   = [];
            foreach ($wageArr as $wageId) {
                $jobId = array_merge($jobId, self::getJobIdListByWage($wageId));
            }
            $ids[] = $jobId;
        }

        if (count($ids) > 0) {
            //合并
            if (count($ids) > 1) {
                //数组大于1，取交集
                $ids = call_user_func_array('array_intersect', $ids);
            } elseif (count($ids) == 1) {
                $ids = $ids[0];
            }
        }

        //学科查询特殊处理
        if (!empty($data['majorId'])) {
            //majorId此时可多选的
            $majorIdArr = explode('_', $data['majorId']);
            $majorWhere = self::formatFindInSetQuery($majorIdArr, 'major_id');
        }

        $searchModel = self::find()
            ->alias('j')
            ->leftJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id = j.announcement_id')
            ->where(['j.is_show' => self::IS_SHOW_YES]);
        if ($needOffLine) {
            $searchModel->andWhere([
                'in',
                'j.status',
                [
                    self::STATUS_ONLINE,
                    self::STATUS_OFFLINE,
                ],
            ]);
        } else {
            $searchModel->andWhere(['j.status' => self::STATUS_ONLINE]);
        }

        if (!empty($ids)) {
            $searchModel->andWhere([
                'in',
                'j.id',
                $ids,
            ]);
        }

        $searchModel->andFilterWhere([
            'or',
            [
                'like',
                'j.name',
                $data['keyword'],
            ],
            [
                'like',
                'c.full_name',
                $data['keyword'],
            ],
            [
                'like',
                'a.title',
                $data['keyword'],
            ],
        ]);

        $searchModel->andFilterWhere([
            'like',
            'j.department',
            $data['department'],
        ]);//用人部门查询

        //城市查询
        if ($data['areaId']) {
            $areaId = array_filter(explode('_', $data['areaId']));
        }
        if (!empty($data['areaId'])) {
            $searchModel->andWhere(['j.city_id' => explode('_', $data['areaId'])]);
        }
        //单位性质查询
        if (!empty($data['companyNature'])) {
            $searchModel->andFilterWhere(['c.nature' => explode('_', $data['companyNature'])]);
        }
        //单位类型查询
        if (!empty($data['companyType'])) {
            $searchModel->andFilterWhere(['c.type' => explode('_', $data['companyType'])]);
        }
        //单位查询
        if (!empty($data['companyId'])) {
            $searchModel->andFilterWhere(['c.id' => $data['companyId']]);
        }

        //学科分类查询
        if (!empty($data['majorId']) && !empty($majorWhere)) {
            $searchModel->andWhere($majorWhere);
        }

        //职位福利查询
        if (!empty($data['welfareLabelId'])) {
            $welfareLabelArr = explode('_', $data['welfareLabelId']);
            foreach ($welfareLabelArr as $k => $v) {
                $searchModel->andWhere(new Expression("FIND_IN_SET(:tags_{$v}, welfare_tag)", [":tags_{$v}" => $v]));
            }
        }

        //学历查询
        if (count(explode('_', $data['educationType'])) > 1) {
            $searchModel->andFilterWhere([
                'in',
                'j.education_type',
                explode('_', $data['educationType']),
            ]);
        } else {
            $searchModel->andFilterWhere(['j.education_type' => $data['educationType']]);
        }

        //工作经验查询
        if (count(explode('_', $data['experienceType'])) > 1) {
            $searchModel->andFilterWhere([
                'in',
                'j.experience_type',
                explode('_', $data['experienceType']),
            ]);
        } else {
            $searchModel->andFilterWhere(['j.experience_type' => $data['experienceType']]);
        }

        //职位性质查询
        if (count(explode('_', $data['natureType'])) > 1) {
            $searchModel->andFilterWhere([
                'in',
                'j.nature_type',
                explode('_', $data['natureType']),
            ]);
        } else {
            $searchModel->andFilterWhere(['j.nature_type' => $data['natureType']]);
        }

        //单位规模查询
        if (count(explode('_', $data['scale'])) > 1) {
            $searchModel->andFilterWhere([
                'in',
                'c.scale',
                explode('_', $data['scale']),
            ]);
        } else {
            $searchModel->andFilterWhere(['c.scale' => $data['scale']]);
        }

        //行业类别查询
        if (count(explode('_', $data['industryId'])) > 1) {
            $searchModel->andFilterWhere([
                'in',
                'c.industry_id',
                explode('_', $data['industryId']),
            ]);
        } else {
            $searchModel->andFilterWhere(['c.industry_id' => $data['industryId']]);
        }

        //职称查询
        if (count(explode('_', $data['titleType'])) > 1) {
            $searchModel->andFilterWhere([
                'in',
                'j.title_type',
                explode('_', $data['titleType']),
            ]);
        } else {
            $searchModel->andFilterWhere(['j.title_type' => $data['titleType']]);
        }

        if ($data['jobCategoryId'] && $data['jobCategoryId'] != 'undefined') {
            if (count(explode('_', $data['jobCategoryId'])) > 1) {
                $searchModel->andFilterWhere([
                    'in',
                    'j.job_category_id',
                    explode('_', $data['jobCategoryId']),
                ]);
            } else {
                $searchModel->andFilterWhere(['j.job_category_id' => $data['jobCategoryId']]);
            }
        }

        if ($data['jobType'] && $data['jobType'] != 'undefined') {
            $count = count(explode('_', $data['jobType']));
            if ($count > 1) {
                $searchModel->andFilterWhere([
                    'in',
                    'j.job_category_id',
                    explode('_', $data['jobType']),
                ]);
            } else {
                $searchModel->andFilterWhere(['j.job_category_id' => $data['jobType']]);
            }
        }

        //发布时间查询
        if (!empty($data['releaseTimeType'])) {
            $releaseTimeInfo = BaseDictionary::getReleaseTimeListInfo($data['releaseTimeType']);

            if (!empty($releaseTimeInfo)) {
                $searchModel->andFilterWhere([
                    'between',
                    'j.release_time',
                    $releaseTimeInfo,
                    date('Y-m-d H:i:s'),
                ]);
            } else {
                throw new Exception('发布时间参数错误');
            }
        }

        //获取总数量
        $count = $searchModel->count();

        $pageSize = $data['pageSize'] ?: \Yii::$app->params['defaultPageSize'];

        $pages = self::setPage($count, $data['page'], $pageSize);

        //获取排序方式
        if ($data['sort'] == 'new') {
            //最新排序
            $sort = 'j.status desc,j.refresh_time desc,j.id desc';
        } elseif ($data['sort'] == 'default') {
            //综合排序
            $sort = 'j.status desc,j.click desc,j.id desc';
        } else {
            $sort = 'j.status desc,j.refresh_time desc,j.id desc';
        }

        $list = $searchModel->select([
            'j.id as jobId',
            'j.status',
            'j.name as jobName',
            'j.company_id as companyId',
            'j.min_wage as minWage',
            'j.max_wage as maxWage',
            'j.wage_type as wageType',
            'j.experience_type as experienceType',
            'j.education_type as educationType',
            'j.amount',
            'j.job_category_id as jobCategoryId',
            'j.province_id as provinceId',
            'j.city_id as cityId',
            'c.full_name as companyName',
            'c.type as companyType',
            'c.nature as companyNature',
            'j.announcement_id as announcementId',
            'a.title as announcementName',
            'j.release_time as releaseTime',
            'j.major_id',

        ])
            ->orderBy($sort)
            ->asArray()
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->all();

        foreach ($list as $k => &$jobRecord) {
            $jobRecord['major']   = BaseMajor::getMajorName(explode(',', $jobRecord['major_id'])[0]);
            $jobRecord['jobName'] = str_replace(PHP_EOL, '', $jobRecord['jobName']);
            //拼接工资
            if ($jobRecord['minWage'] == 0 && $jobRecord['maxWage'] == 0) {
                $jobRecord['wage'] = '面议';
            } else {
                $jobRecord['wage'] = self::formatWage($jobRecord['minWage'], $jobRecord['maxWage'],
                    $jobRecord['wageType']);
            }
            //获取经验要求
            $jobRecord['experience'] = BaseDictionary::getExperienceName($jobRecord['experienceType']);
            //获取学历水平
            $jobRecord['education'] = BaseDictionary::getEducationName($jobRecord['educationType']);
            //获取意向职能
            $jobRecord['jobCategory'] = BaseCategoryJob::getName($jobRecord['jobCategoryId']);

            //获取地区名称
            $jobRecord['areaName'] = BaseArea::getAreaName($jobRecord['provinceId']) . '-' . BaseArea::getAreaName($jobRecord['cityId']);
            $jobRecord['city']     = BaseArea::getAreaName($jobRecord['cityId']);
            //获取单位类型
            $jobRecord['companyTypeName'] = BaseDictionary::getCompanyTypeName($jobRecord['companyType']);
            //获取单位性质
            $jobRecord['companyNatureName'] = BaseDictionary::getCompanyNatureName($jobRecord['companyNature']);

            $jobRecord['url']             = Url::toRoute([
                'job/detail',
                'id' => $jobRecord['jobId'],
            ]);
            $jobRecord['companyUrl']      = Url::toRoute([
                'company/detail',
                'id' => $jobRecord['companyId'],
            ]);
            $jobRecord['announcementUrl'] = Url::toRoute([
                'announcement/detail',
                'id' => $jobRecord['announcementId'],
            ]);
            //判断职位是否是合作单位的职位
            $jobRecord['applyStatus']  = BaseJob::JOB_APPLY_STATUS_NO;
            $jobRecord['userEmail']    = '';
            $jobRecord['isEmailApply'] = "false";

            $cooperationInfo = BaseCompany::findOneVal(['id' => $jobRecord['companyId']], 'is_cooperation');
            if ($cooperationInfo == BaseCompany::COOPERATIVE_UNIT_YES) {
                //如果是合作单位，站内投递
                $jobRecord['isCooperation'] = "true";
                //如果用户已经登录了，获取用户投递信息
                if (!empty($data['memberId'])) {
                    //获取用户对该职位投递情况
                    $jobRecord['applyStatus'] = BaseJobApply::checkJobApplyStatus($data['memberId'],
                        $jobRecord['jobId']);
                }
            } else {
                //站外投递
                $jobRecord['isCooperation'] = "false";
                if (!empty($data['memberId'])) {
                    //获取用户对该职位投递情况
                    $jobRecord['applyStatus'] = BaseOffSiteJobApply::checkJobApplyStatus($data['memberId'],
                        $jobRecord['jobId']);

                    $jobRecord['userEmail'] = BaseMember::findOneVal(['id' => $data['memberId']], 'email');;
                }
            }
            $jobRecord['releaseTime'] = TimeHelper::formatDateByYear($jobRecord['releaseTime']);
        }
        if ($needPageInfo) {
            return [
                'list'        => $list,
                'pageSize'    => $pageSize,
                'currentPage' => $data['page'],
                'totalNum'    => $count,

            ];
        } else {
            return $list;
        }
    }

    /**
     * 获取公告下职位列表
     * @throws \Exception
     */
    public static function getAnnouncementNextJobList($searchData, $needPage = true)
    {
        $query = self::find()
            ->alias('j')
            ->leftJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
            ->where([
                'j.announcement_id' => $searchData['announcementId'],
                'j.is_show'         => self::IS_SHOW_YES,
            ])
            ->groupBy('j.id')
            ->andWhere([
                'in',
                'j.status',
                [
                    self::STATUS_OFFLINE,
                    self::STATUS_ONLINE,
                ],
            ])
            ->select([
                'j.name as jobName',
                'c.full_name as companyName',
                'c.is_cooperation',
                'j.id as jobId',
                'j.wage_type',
                'j.min_wage',
                'j.max_wage',
                'j.education_type',
                'j.amount',
                'j.experience_type',
                'j.city_id',
                'j.major_id',
                'j.status',
                'j.apply_type',
                'j.apply_address',
                'j.release_time as releaseTime',
                'j.refresh_time as refreshTime',
            ]);

        // 职位类型
        if (!empty($searchData['categoryId'])) {
            $searchData['categoryId'] = explode(',', $searchData['categoryId']);
            $query->leftJoin(['cr' => BaseJobCategoryRelation::tableName()], 'cr.job_id=j.id');
            $query->andWhere([
                'in',
                'cr.category_id',
                $searchData['categoryId'],
            ]);
        }
        // 职位学历
        if (!empty($searchData['educationId'])) {
            $searchData['educationType'] = explode(',', $searchData['educationId']);
            $query->andWhere([
                'in',
                'j.education_type',
                $searchData['educationType'],
            ]);
        }
        // 职位专业
        if (!empty($searchData['majorId'])) {
            $searchData['majorId'] = explode(',', $searchData['majorId']);
            $query->leftJoin(['mr' => BaseJobMajorRelation::tableName()], 'mr.job_id=j.id');
            $query->andWhere([
                'in',
                'mr.major_id',
                $searchData['majorId'],
            ]);
        }

        $count = $query->count();
        $onlineCount = (clone $query)->andWhere(['j.status' => self::STATUS_ONLINE])->count();

        if ($needPage) {
            $pageSize = $searchData['pageSize'] ?: 9;

            $pages = self::setPage($count, $searchData['page'], $pageSize);

            $query->offset($pages['offset'])
                ->limit($pages['limit']);
        }

        $list             = $query->orderBy('j.status desc,j.refresh_time desc,j.id asc')
            ->asArray()
            ->all();
        $announcementName = BaseAnnouncement::findOneVal(['id' => $searchData['announcementId']], 'title');
        foreach ($list as &$job) {
            $job['url']              = UrlHelper::createJobDetailPath($job['jobId']);
            $job['wage']             = BaseJob::formatWage($job['min_wage'], $job['max_wage'], $job['wage_type']);
            $job['education']        = BaseDictionary::getEducationName($job['education_type']);
            $job['experience']       = BaseDictionary::getExperienceName($job['experience_type']);
            $job['city']             = BaseArea::getAreaName($job['city_id']);
            $job['major']            = BaseMajor::getMajorName(explode(',', $job['major_id'])[0]);
            $job['announcementName'] = $announcementName;
            //判断合作单位
            if ($job['is_cooperation'] == BaseCompany::COOPERATIVE_UNIT_YES) {
                $job['isCooperation'] = 'true';
            } else {
                $job['isCooperation'] = 'false';
            }

            $job['isOnlineApply'] = 'false';
            $job['isOtherApply']  = 'false';
            $job['isEmailApply']  = 'false';
            if (!empty($job['apply_type'])) {
                $applyTypeArr = explode(',', $job['apply_type']);
                if (in_array(self::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr)) {
                    $job['isEmailApply'] = 'true';
                } elseif (in_array(self::ATTRIBUTE_APPLY_ONLINE, $applyTypeArr)) {
                    $job['isOnlineApply'] = 'true';
                    $job['onlineApply']   = $job['apply_address'];
                } else {
                    $job['isOtherApply'] = 'true';

                    $applyTypeName = '';
                    foreach ($applyTypeArr as $item) {
                        $applyTypeName .= BaseDictionary::getSignUpName($item) . '/';
                    }
                    $job['otherTxt'] = substr($applyTypeName, '0', '-1');
                }
            }
            $job['releaseTime'] = TimeHelper::formatDateByYear($job['releaseTime']);
            $job['refreshTime'] = TimeHelper::formatDateByYear($job['refreshTime']);
        }

        return [
            'list'  => $list,
            'count' => intval($count),
            'onlineCount' => intval($onlineCount),
        ];
    }

    public static function filterJobList($memberId, $jobList)
    {
        $resultArr = [];
        if (!empty($jobList)) {
            foreach ($jobList as $job) {
                if (BaseJobApply::checkJobApplyStatus($memberId, $job['jobId']) == BaseJob::JOB_APPLY_STATUS_NO) {
                    array_push($resultArr, $job);
                }
            }

            return $resultArr;
        } else {
            return [];
        }
    }

    /**
     * 获取职位热门搜索列表
     * @return array
     */
    public static function getHotList()
    {
        // $hotList = \Yii::$app->params['hotSearchList']['job'];
        $hotList = BaseSystemConfig::getH5HotSearchListJob();
        $list    = [];
        foreach ($hotList as $k => $item) {
            $list[$k]['name']  = $item;
            $list[$k]['url']   = Url::toRoute([
                'job/index',
                'keyword' => $item,
            ]);
            $list[$k]['title'] = $item;
        }

        return $list;
    }

    public static function getJobIdListByWage($wageId)
    {
        $searchJobModel = self::find()
            ->where(1);
        //薪资范围查询
        $wageInfo = BaseDictionary::getMinAndMaxWage($wageId);
        //面议
        if ($wageInfo['min'] == 0 && $wageInfo['max'] == 0) {
            $searchJobModel->andWhere([
                'min_wage' => 0,
            ]);
            $searchJobModel->andWhere([
                'max_wage' => 0,
            ]);
        }
        //其他
        if ($wageInfo['min'] > 0) {
            $searchJobModel->andWhere([
                'or',
                [
                    'and',
                    ['wage_type' => BaseJob::WAGE_TYPE_MONEY],
                    [
                        '>=',
                        'min_wage',
                        (int)$wageInfo['min'],
                    ],
                ],

                [
                    'and',
                    ['wage_type' => BaseJob::WAGE_TYPE_DAY],
                    [
                        '>=',
                        'min_wage',
                        ($wageInfo['min'] / 30),
                    ],

                ],
                [
                    'and',
                    ['wage_type' => BaseJob::WAGE_TYPE_YEAR],
                    [
                        '>=',
                        'min_wage',
                        ($wageInfo['min'] * 12),
                    ],
                ],
            ]);
        }

        if ($wageInfo['max'] > 0) {
            $searchJobModel->andWhere([
                'or',
                [
                    'and',
                    ['wage_type' => BaseJob::WAGE_TYPE_MONEY],
                    [
                        '<=',
                        'max_wage',
                        (int)$wageInfo['max'],
                    ],
                    [
                        '>',
                        'max_wage',
                        0,
                    ],
                ],

                [
                    'and',
                    ['wage_type' => BaseJob::WAGE_TYPE_DAY],
                    [
                        '<=',
                        'max_wage',
                        ($wageInfo['max'] / 30),
                    ],
                    [
                        '>',
                        'max_wage',
                        0,
                    ],

                ],
                [
                    'and',
                    ['wage_type' => BaseJob::WAGE_TYPE_YEAR],
                    [
                        '<=',
                        'min_wage',
                        ($wageInfo['max'] * 12),
                    ],
                    [
                        '>',
                        'max_wage',
                        0,
                    ],
                ],
            ]);
        }
        $jobListInfo = $searchJobModel->select('id')
            ->asArray()
            ->indexBy('id')
            ->all();
        $ids         = array_keys($jobListInfo);

        return $ids;
    }

    public static function getRecommendList($searchData)
    {
        $surplusAmount = $searchData['count'];

        $where              = [
            'j.status'          => self::STATUS_ONLINE,
            'j.job_category_id' => $searchData['jobCategoryId'],
        ];
        $where1             = [
            '<>',
            'j.id',
            $searchData['jobId'],
        ];
        $jobIds             = [];
        $sameCompanyJobId   = [];
        $seniorCompanyJobId = [];
        $otherCompanyJobId  = [];
        //先查id合集，再查字段
        //1、优先展示该单位最新发布、与该职位类型相同的在线职位，最多6条。
        $sameCompanyJobList = self::find()
            ->alias('j')
            ->where($where)
            ->andWhere($where1)
            ->andWhere([
                'j.company_id' => $searchData['companyId'],
            ])
            ->select('j.id')
            ->asArray()
            ->indexBy('j.id')
            ->orderBy('refresh_time desc')
            ->limit($surplusAmount)
            ->column();

        if (!empty($sameCompanyJobList)) {
            $surplusAmount    = $surplusAmount - count($sameCompanyJobList);
            $sameCompanyJobId = array_keys($sameCompanyJobList);
            $jobIds           = $sameCompanyJobId;
        }

        //2、展示与该职位职位类型相同、工作地点相同的，会员类型为高级会员的合作单位，最新发布的在线职位，最多6条。
        if ($surplusAmount > 0) {
            //剩余数量大于0，继续查下一个条件
            $seniorCompanyJobList = self::find()
                ->alias('j')
                ->innerJoin(['c' => Company::tableName()], 'c.id=j.company_id')
                ->where($where)
                ->andWhere($where1)
                ->andWhere([
                    'j.city_id'      => $searchData['cityId'],
                    'c.package_type' => 2,
                ])
                ->andFilterWhere([
                    'not in',
                    'j.id',
                    $jobIds,
                ])
                ->select('j.id')
                ->asArray()
                ->indexBy('j.id')
                ->orderBy('j.refresh_time desc')
                ->limit($surplusAmount)
                ->column();

            if (!empty($seniorCompanyJobList)) {
                $surplusAmount      = $surplusAmount - count($seniorCompanyJobList);
                $seniorCompanyJobId = array_keys($seniorCompanyJobList);
                $jobIds             = array_merge($jobIds, $seniorCompanyJobId);
            }
        }

        //3、展示与该职位职位类型相同、工作地点相同的，最新发布的在线职位。
        if ($surplusAmount > 0) {
            $otherCompanyJobList = self::find()
                ->alias('j')
                ->where($where)
                ->andWhere($where1)
                ->andWhere([
                    'j.city_id' => $searchData['cityId'],
                ])
                ->andFilterWhere([
                    'not in',
                    'j.id',
                    $jobIds,
                ])
                ->select('j.id')
                ->asArray()
                ->indexBy('j.id')
                ->orderBy('j.refresh_time desc')
                ->limit($surplusAmount)
                ->column();

            if (!empty($otherCompanyJobList)) {
                $otherCompanyJobId = array_keys($otherCompanyJobList);
                $jobIds            = array_merge($jobIds, $otherCompanyJobId);
            }
        }
        //根据职位id获取推荐职位列表
        //        print_r($jobIds);die;
        //判断3个数组的id是否存在，存在的话，要做拼接，用来排序
        $sortNum = 1;
        $sortSql = '';
        if (!empty($sameCompanyJobId)) {
            $sameCompanyJobIdText = (implode(',', $sameCompanyJobId));
            $sortSql              = "(CASE  WHEN j.id in ($sameCompanyJobIdText) THEN $sortNum ";
        }
        if (!empty($seniorCompanyJobId)) {
            $seniorCompanyJobIdText = (implode(',', $seniorCompanyJobId));
            if (!empty($sortSql)) {
                $sortNum += 1;
                $sortSql .= "WHEN j.id in ($seniorCompanyJobIdText) THEN $sortNum ";
            } else {
                $sortSql = "(CASE  WHEN j.id in ($seniorCompanyJobIdText) THEN $sortNum ";
            }
        }
        if (!empty($otherCompanyJobId)) {
            $otherCompanyJobIdText = (implode(',', $otherCompanyJobId));
            if (!empty($sortSql)) {
                $sortNum += 1;
                $sortSql .= "WHEN j.id in ($otherCompanyJobIdText) THEN $sortNum ";
            } else {
                $sortSql = "(CASE  WHEN j.id in ($otherCompanyJobIdText) THEN $sortNum ";
            }
        }
        if (!empty($sortSql)) {
            $sortSql .= " END )";
        } else {
            $sortSql = '';
        }
        if (!empty($jobIds)) {
            $query = self::find()
                ->alias('j')
                ->leftJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
                ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id = j.announcement_id')
                ->where([
                    'j.id' => $jobIds,
                ])
                ->select([
                    'j.id as jobId',
                    'j.status',
                    'j.name as jobName',
                    'j.company_id as companyId',
                    'j.min_wage as minWage',
                    'j.max_wage as maxWage',
                    'j.wage_type as wageType',
                    'j.experience_type as experienceType',
                    'j.education_type as educationType',
                    'j.amount',
                    'j.job_category_id as jobCategoryId',
                    'j.province_id as provinceId',
                    'j.city_id as cityId',
                    'c.full_name as companyName',
                    'c.type as companyType',
                    'c.nature as companyNature',
                    'j.announcement_id as announcementId',
                    'a.title as announcementName',
                    'j.major_id',
                    'j.release_time as releaseTime',
                    " $sortSql as sortNum",
                ]);

            $list = $query->asArray()
                ->orderBy('sortNum asc,releaseTime desc')
                ->all();

            foreach ($list as &$job) {
                $job['education']   = BaseDictionary::getEducationName($job['educationType']);
                $job['experience']  = BaseDictionary::getExperienceName($job['experienceType']);
                $job['city']        = BaseArea::getAreaName($job['cityId']);
                $job['releaseTime'] = TimeHelper::formatDateByYear($job['releaseTime']);
                //获取单位类型
                $job['companyTypeName'] = BaseDictionary::getCompanyTypeName($job['companyType']);
                //获取单位性质
                $job['companyNatureName'] = BaseDictionary::getCompanyNatureName($job['companyNature']);
                $job['url']               = BaseJob::getDetailUrl($job['jobId']);

                //拼接工资
                if ($job['minWage'] == 0 && $job['maxWage'] == 0) {
                    $job['wage'] = '面议';
                } else {
                    $job['wage'] = self::formatWage($job['minWage'], $job['maxWage'], $job['wageType']);
                }
            }

            return $list;
        } else {
            return [];
        }
    }

}