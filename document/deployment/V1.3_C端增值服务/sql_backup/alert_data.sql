CREATE TABLE IF NOT EXISTS `resume_order`
(
    `id`                int(11)                 NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`          datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime                NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `pay_time`          datetime                NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '支付时间',
    `status`            tinyint(1)              NOT NULL DEFAULT '0' COMMENT '状态,0未支付,1已支付,-1已取消',
    `equity_status`     tinyint(1)              NOT NULL DEFAULT '0' COMMENT '权益修改状态,0未录入,1已录入（支付状态为1,录入状态为0,则为异常订单）',
    `resume_id`         int(11)                 NOT NULL DEFAULT '0' COMMENT '在线简历表主键id',
    `equity_package_id` int(11)                 NOT NULL DEFAULT '0' COMMENT '权益组合表主键id',
    `payway`            tinyint(1)              NOT NULL DEFAULT '0' COMMENT '支付方式:1:微信,2:支付宝',
    `platform`          tinyint(1)              NOT NULL DEFAULT '0' COMMENT '下单渠道:1:PC,2:H5,3:MINI',
    `pay_channel`       tinyint(1)              NOT NULL DEFAULT '0' COMMENT '支付渠道:1:微信扫码,2:微信H5,3:微信JSAPI',
    `ip`                varchar(255)            NOT NULL DEFAULT '' COMMENT 'IP',
    `original_amount`   decimal(10, 2) unsigned NOT NULL DEFAULT '0.00' COMMENT '原始金额',
    `real_amount`       decimal(10, 2) unsigned NOT NULL DEFAULT '0.00' COMMENT '真实金额',
    `order_no`          varchar(255)            NOT NULL DEFAULT '' COMMENT '平台订单号',
    `trade_no`          varchar(255)            NOT NULL DEFAULT '' COMMENT '交易订单号',
    `remark`            varchar(255)            NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uniq_order_no` (`order_no`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_pay_time` (`pay_time`) USING BTREE,
    KEY `idx_status` (`status`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_equity_package_id` (`equity_package_id`) USING BTREE,
    KEY `idx_payway` (`payway`) USING BTREE,
    KEY `idx_platform` (`platform`) USING BTREE,
    KEY `idx_order_no` (`order_no`) USING BTREE,
    KEY `idx_trade_no` (`trade_no`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='支付订单表';

CREATE TABLE IF NOT EXISTS `order_notify_log`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `order_no`    varchar(255) NOT NULL DEFAULT '' COMMENT '平台订单号',
    `member_type` tinyint(1)   NOT NULL DEFAULT '0' COMMENT '1求职者,2单位端',
    `notify`      text         NOT NULL COMMENT '回调参数',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='支付订单回调日志表';

CREATE TABLE IF NOT EXISTS `resume_equity_setting`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `name`        varchar(255) NOT NULL DEFAULT '' COMMENT '名称',
    `status`      tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态,0待审核,1已上线,-1已下线',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='求职者权益配置';

CREATE TABLE IF NOT EXISTS `resume_equity_package_category_setting`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `name`        varchar(255) NOT NULL DEFAULT '' COMMENT '名称',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='求职者权益组合分类（大类）';

CREATE TABLE IF NOT EXISTS `resume_equity_package_setting`
(
    `id`                         int(11)                 NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`                   datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                datetime                NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `name`                       varchar(255)            NOT NULL DEFAULT '' COMMENT '名称',
    `subname`                    varchar(255)            NOT NULL DEFAULT '' COMMENT '名称',
    `status`                     tinyint(1)              NOT NULL DEFAULT '0' COMMENT '状态,0待审核,1已上线,-1已下线',
    `equity_package_category_id` int(11)                 NOT NULL DEFAULT '0' COMMENT '权益组合分类表主键id',
    `original_amount`            decimal(10, 2) unsigned NOT NULL DEFAULT '0.00' COMMENT '原始金额',
    `real_amount`                decimal(10, 2) unsigned NOT NULL DEFAULT '0.00' COMMENT '真实金额',
    `days`                       int(11)                 NOT NULL DEFAULT '0' COMMENT '有效天数',
    `buy_desc`                   varchar(255)            NOT NULL DEFAULT '' COMMENT '购买描述',
    `buy_type`                   tinyint(1)              NOT NULL DEFAULT '0' COMMENT '购买类型,1:体验版,2:热销,3:超值,4:内测专享,5:推荐',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='求职者权益组合（小类）';

CREATE TABLE IF NOT EXISTS `resume_equity_package_relation_setting`
(
    `id`                int(11)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`          datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `equity_package_id` int(11)  NOT NULL DEFAULT '0' COMMENT '权益组合表主键id',
    `equity_id`         int(11)  NOT NULL DEFAULT '0' COMMENT '权益表主键id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='求职者权益与组合中间表';

CREATE TABLE IF NOT EXISTS `resume_equity_action_record`
(
    `id`              int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`        datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `resume_id`       int(11)      NOT NULL DEFAULT '0' COMMENT '在线简历表主键id',
    `equity_id`       int(11)      NOT NULL DEFAULT '0' COMMENT '权益表主键id',
    `equity_type`     tinyint(1)   NOT NULL DEFAULT '0' COMMENT '1:增加,2:减少',
    `action_type`     tinyint(1)   NOT NULL DEFAULT '0' COMMENT '1:购买vip,2:购买竞争力洞察,3:查看职位,4:查看公告,5:服务过期',
    `relation_id`     int(11)      NOT NULL DEFAULT '0' COMMENT '关联的ID,用于显示备注',
    `relation_remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注文案显示',
    `expire_time`     datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '会员过期时间，购买行为才会有值',
    `operation_type`  tinyint(1)   NOT NULL DEFAULT '0' COMMENT '操作者类型,1:求职者,2:系统,3:运营人员',
    `operation_id`    int(11)      NOT NULL DEFAULT '0' COMMENT '操作者id',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_equity_id` (`equity_id`) USING BTREE,
    KEY `idx_equity_type` (`equity_type`) USING BTREE,
    KEY `idx_action_type` (`action_type`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='权益产出/消耗明细表';

CREATE TABLE IF NOT EXISTS `resume_equity`
(
    `id`            int(11)    NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`      datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `resume_id`     int(11)    NOT NULL DEFAULT '0' COMMENT '在线简历表主键id',
    `equity_id`     int(11)    NOT NULL DEFAULT '0' COMMENT '权益表主键id',
    `begin_time`    datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '生效时间',
    `expire_time`   datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '过期时间',
    `expire_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态,0未过期,1已过期',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_equity_id` (`equity_id`) USING BTREE,
    KEY `idx_expire_time` (`begin_time`) USING BTREE,
    KEY `idx_expire_status` (`expire_status`) USING BTREE,
    KEY `idx_begin_time` (`begin_time`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='会员权益明细表';

CREATE TABLE IF NOT EXISTS `resume_announcement_report_record`
(
    `id`              int(11)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`        datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `resume_id`       int(11)  NOT NULL DEFAULT '0' COMMENT '在线简历表主键id',
    `announcement_id` int(11)  NOT NULL DEFAULT '0' COMMENT '公告表主键id',
    `token`           char(32) NOT NULL DEFAULT '' COMMENT '唯一标识',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='求职者公告使用分析报告记录表';

CREATE TABLE IF NOT EXISTS `resume_job_report_record`
(
    `id`          int(11)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`    datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `resume_id`   int(11)  NOT NULL DEFAULT '0' COMMENT '在线简历表主键id',
    `job_id`      int(11)  NOT NULL DEFAULT '0' COMMENT '职位表主键id',
    `token`       char(32) NOT NULL DEFAULT '' COMMENT '唯一标识',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='求职者职位使用分析报告记录表';

-- 简历主表
ALTER TABLE `resume`
    ADD COLUMN `vip_type`        tinyint(1) NOT NULL DEFAULT '0' COMMENT '会员类型,0:普通用户,1:生效会员,-1:过期会员',
    ADD COLUMN `vip_begin_time`  datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '会员生效时间',
    ADD COLUMN `vip_expire_time` datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '会员过期时间';
-- 权益设置
INSERT INTO `resume_equity_setting`
VALUES (1, '2023-05-19 07:45:33', '0000-00-00 00:00:00', '竞争力分析', 1);
INSERT INTO `resume_equity_setting`
VALUES (2, '2023-05-19 07:45:33', '0000-00-00 00:00:00', '公告热度', 1);
INSERT INTO `resume_equity_setting`
VALUES (3, '2023-05-19 07:45:33', '0000-00-00 00:00:00', '编制查询', 1);
INSERT INTO `resume_equity_setting`
VALUES (4, '2023-05-19 07:45:33', '0000-00-00 00:00:00', '简历模版', 1);
INSERT INTO `resume_equity_setting`
VALUES (5, '2023-05-19 07:45:33', '0000-00-00 00:00:00', '浏览足迹', 1);
INSERT INTO `resume_equity_setting`
VALUES (6, '2023-05-19 07:45:33', '0000-00-00 00:00:00', '收藏查看数量', 1);
INSERT INTO `resume_equity_setting`
VALUES (7, '2023-05-19 07:45:33', '0000-00-00 00:00:00', '求职资源', 1);
INSERT INTO `resume_equity_setting`
VALUES (8, '2023-05-19 07:45:33', '0000-00-00 00:00:00', '专属标识', 1);
-- 权益组合设置
INSERT INTO `resume_equity_package_setting`
VALUES (1, '2023-05-17 03:26:42', '0000-00-00 00:00:00', '高才VIP(30天)', '高才VIP', 1, 1, 128.00, 88.00, 30, '尝鲜体验丰富会员权益',
        1);
INSERT INTO `resume_equity_package_setting`
VALUES (2, '2023-05-17 03:26:42', '0000-00-00 00:00:00', '高才VIP(90天)', '高才VIP', 1, 1, 328.00, 198.00, 90, '省心畅享整个招聘季',
        2);
INSERT INTO `resume_equity_package_setting`
VALUES (3, '2023-05-17 03:26:42', '0000-00-00 00:00:00', '高才VIP(180天)', '高才VIP', 1, 1, 628.00, 368.00, 180,
        '加赠价值¥318求职课程包', 0);
INSERT INTO `resume_equity_package_setting`
VALUES (4, '2023-05-17 03:26:42', '0000-00-00 00:00:00', '竞争力洞察(7天)', '竞争力洞察', 1, 2, 48.00, 28.00, 7, '', 1);
INSERT INTO `resume_equity_package_setting`
VALUES (5, '2023-05-17 03:26:42', '0000-00-00 00:00:00', '竞争力洞察(15天)', '竞争力洞察', 1, 2, 88.00, 48.00, 15, '', 5);
INSERT INTO `resume_equity_package_setting`
VALUES (6, '2023-05-17 03:26:42', '0000-00-00 00:00:00', '竞争力洞察(30天)', '竞争力洞察', 1, 2, 168.00, 68.00, 30, '', 3);
INSERT INTO `resume_equity_package_setting`
VALUES (7, '2023-05-17 03:26:42', '0000-00-00 00:00:00', '高才VIP(7天)', '高才VIP', 1, 1, 28.00, 1.00, 7, '专享福利价抢先体验', 4);
-- 权益组合分类设置
INSERT INTO `resume_equity_package_category_setting`
VALUES (1, '2023-05-17 02:03:12', '0000-00-00 00:00:00', 'VIP服务');
INSERT INTO `resume_equity_package_category_setting`
VALUES (2, '2023-05-17 02:03:12', '0000-00-00 00:00:00', '竞争力洞察');
-- 权益组合与权益关系设置
INSERT INTO `resume_equity_package_relation_setting`
VALUES (1, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 1, 1);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (2, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 1, 2);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (3, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 1, 3);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (4, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 1, 4);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (5, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 1, 5);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (6, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 1, 6);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (7, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 1, 7);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (8, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 1, 8);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (9, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 2, 1);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (10, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 2, 2);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (11, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 2, 3);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (12, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 2, 4);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (13, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 2, 5);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (14, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 2, 6);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (15, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 2, 7);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (16, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 2, 8);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (17, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 3, 1);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (18, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 3, 2);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (19, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 3, 3);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (20, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 3, 4);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (21, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 3, 5);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (22, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 3, 6);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (23, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 3, 7);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (24, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 3, 8);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (25, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 4, 1);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (26, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 4, 2);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (27, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 5, 1);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (28, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 5, 2);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (29, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 6, 1);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (30, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 6, 2);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (31, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 7, 1);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (32, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 7, 2);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (33, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 7, 3);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (34, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 7, 4);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (35, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 7, 5);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (36, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 7, 6);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (37, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 7, 7);
INSERT INTO `resume_equity_package_relation_setting`
VALUES (38, '2023-05-19 07:44:03', '0000-00-00 00:00:00', 7, 8);

-- 短信发送记录
ALTER TABLE `sms_log`
    ADD COLUMN `ext_params` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '扩展参数';

CREATE TABLE `resume_template_download_record`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT,
    `resume_id`   int(11)          NOT NULL DEFAULT '0' COMMENT '简历ID',
    `template_id` int(11)          NOT NULL DEFAULT '0' COMMENT '模板ID',
    `type`        tinyint(2)       NOT NULL DEFAULT '1' COMMENT '下载类型 1PDF 2DOC',
    `add_time`    datetime         NOT NULL COMMENT '下载时间',
    `platform`    tinyint(2)       NOT NULL DEFAULT '0' COMMENT '下载平台 1PC 2 h5  3mini',
    PRIMARY KEY (`id`),
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_template_id` (`template_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历模板下载记录';


CREATE TABLE `resume_template_config`
(
    `id`                  int(11) unsigned NOT NULL AUTO_INCREMENT,
    `name`                varchar(128)     NOT NULL DEFAULT '' COMMENT '模板名称',
    `code`                varchar(64)      NOT NULL DEFAULT '' COMMENT '模板编号',
    `pdf_download_number` int(11)          NOT NULL COMMENT 'pdf下载次数',
    `doc_download_number` int(11)          NOT NULL COMMENT 'doc下载次数',
    `is_show`             tinyint(2)       NOT NULL DEFAULT '1' COMMENT '是否显示 1显示  2隐藏',
    `is_delete`           tinyint(2)       NOT NULL DEFAULT '2' COMMENT '是否删除 1 是 2否',
    `add_time`            datetime         NOT NULL COMMENT '添加时间',
    `update_time`         datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `description`         varchar(128)     NOT NULL DEFAULT '' COMMENT '模板描述',
    `template_image`      varchar(255)     NOT NULL COMMENT '模板图片',
    `is_vip`              tinyint(2)       NOT NULL DEFAULT '2' COMMENT '是否会员 1 是 2 不是',
    `sort_number`         int(11)          NOT NULL DEFAULT '0' COMMENT '排序 序号越大越前',
    `file_id`             int(11)          NOT NULL DEFAULT '0' COMMENT '模板文件在file表的关联ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_code` (`code`) USING BTREE,
    KEY `idx_file_id` (`file_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历模板配置';

INSERT INTO `resume_template_config` (`id`, `name`, `code`, `pdf_download_number`, `doc_download_number`, `is_show`,
                                      `is_delete`, `add_time`, `update_time`, `description`, `template_image`, `is_vip`,
                                      `sort_number`, `file_id`)
VALUES (null, '默认模板', 'CVIP_jlmb_00', 49, 44, 1, 2, '2023-07-07 15:15:34', '2023-08-09 21:20:55', '',
        'https://img.gaoxiaojob.com/uploads/static/image/resumeTemplate/1.jpg', 2, 100, 196357);
INSERT INTO `resume_template_config` (`id`, `name`, `code`, `pdf_download_number`, `doc_download_number`, `is_show`,
                                      `is_delete`, `add_time`, `update_time`, `description`, `template_image`, `is_vip`,
                                      `sort_number`, `file_id`)
VALUES (null, '黄色经典简约', 'CVIP_jlmb_01', 20, 7, 1, 2, '2023-07-07 15:19:03', '2023-08-14 14:36:16', '高层次人才专属简历模板，会员免费使用',
        'https://img.gaoxiaojob.com/uploads/static/image/resumeTemplate/4.jpg', 1, 70, 196375);
INSERT INTO `resume_template_config` (`id`, `name`, `code`, `pdf_download_number`, `doc_download_number`, `is_show`,
                                      `is_delete`, `add_time`, `update_time`, `description`, `template_image`, `is_vip`,
                                      `sort_number`, `file_id`)
VALUES (null, '绿色清新淡雅', 'CVIP_jlmb_02', 33, 17, 1, 2, '2023-07-07 15:19:46', '2023-08-09 21:20:55',
        '高层次人才专属简历模板，会员免费使用', 'https://img.gaoxiaojob.com/uploads/static/image/resumeTemplate/3.jpg', 1, 90, 196374);
INSERT INTO `resume_template_config` (`id`, `name`, `code`, `pdf_download_number`, `doc_download_number`, `is_show`,
                                      `is_delete`, `add_time`, `update_time`, `description`, `template_image`, `is_vip`,
                                      `sort_number`, `file_id`)
VALUES (null, '蓝色星空沉稳', 'CVIP_jlmb_03', 28, 7, 1, 2, '2023-07-07 15:20:18', '2023-08-14 14:36:23', '高层次人才专属简历模板，会员免费使用',
        'https://img.gaoxiaojob.com/uploads/static/image/resumeTemplate/2.jpg', 1, 80, 196350);



ALTER TABLE `new_gaoxiaojob`.`job_apply_record`
    ADD COLUMN `match_complete` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '匹配度' AFTER `platform`;

alter table system_config
    modify value text;

INSERT INTO `system_config` (`id`, `add_time`, `platform_type`, `name`, `value`, `remark`,
                             `is_business`, `is_secret`)
VALUES (null, '2023-07-10 11:40:50', 0, 'service_agreement',
        '<p style=\"text-align:center;\"><b><span style=\"font-family: 宋体; font-size: 24pt;\">高校人才网 增值服务协议</span></b><b><span style=\"font-family: 宋体; font-size: 24pt;\"></span></b></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">欢迎您使用高校人才网增值服务！</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">为使用高校人才网增值服务（简称“本服务”），您应当阅读并遵守《高校人才网增值服务协议》（简称“本协议”）。</span><b><span style=\"font-family: 宋体; font-size: 14pt;\">请您务必审慎阅读、充分理解各条款内容，特别是涉及免除或限制责任的相应条款和争议解决条款（尤其是加粗的内容）</span></b><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">除非您已阅读并接受本协议所有条款，否则您不应当继续购买或使用本服务。您对本服务的任何购买或使用行为即视为您已阅读并同意本协议的约束。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><b><span style=\"font-family: 宋体; font-size: 18pt;\">一、协议范围</span></b><b><span style=\"font-family: 宋体; font-size: 18pt;\"></span></b></p><p><span style=\"font-family: Calibri; font-size: 14pt;\">1</span><span style=\"font-family: 宋体; font-size: 14pt;\">.</span><span style=\"font-family: Calibri; font-size: 14pt;\">1 &nbsp;</span><span style=\"font-family: 宋体; font-size: 14pt;\">协议内容</span><span style=\"font-family: 宋体; font-size: 14pt;\"></span></p><p><b><span style=\"font-family: 宋体; font-size: 14pt;\">本协议为高校人才网</span></b><b><u><span style=\"font-family: 宋体; color: rgb(91, 155, 213); font-size: 14pt;\"><a target=\"_blank\" href=\"https://agreement.gaoxiaojob.com/docs/service-agreement/\">《用户服务协议》</a></span></u></b><b><span style=\"font-family: 宋体; font-size: 14pt;\">的补充协议，是其不可分割的组成部分，与其构成统一整体。</span></b><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">本协议中未约定事项以《用户服务协议》中的约定为准；本协议与前述协议存在冲突的，以本协议为准。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">本协议内容同时包括高校人才网可能不断发布的关于本服务的相关协议、业务规则等内容。上述内容一经正式发布，即为本协议不可分割的组成部分，用户同样应当遵守。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">1</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">.</span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">2 &nbsp;</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">协议适用主体</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">高校人才网</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">提供的</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">在线增值</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">服务采用免费与收费结合的方式，用户须先成为</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">高校人才网</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">普通注册用户之后，才能购买本服务。通过购买程序支付费用后，用户就可以</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">享有高校人才网提供的</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">增值服务</span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><b><span style=\"font-family: 宋体; font-size: 18pt;\">二、增值服务说明</span></b><b><span style=\"font-family: 宋体; font-size: 18pt;\"></span></b></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">2.1 </span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">&nbsp;</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">增值服务，指按照高校人才网的指定方式支付一定服务费用之后，用户可以享有由高校人才网或第三方提供的在高校人才网平台虚拟道具、招聘/求职方面特权等服务。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">2.2 </span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">&nbsp;</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">高校人才网可能会根据不同的产品及服务类型，推出不同的增值服务类型。目前，高校人才网提供的增值服务包括但不限于</span><b><span style=\"font-family: 宋体; font-size: 14pt;\">高才VIP套餐、竞争力洞察套餐、职位竞争力分析、公告热度分析、编制查询、简历模版</span></b><b><span style=\"font-family: 宋体; font-size: 14pt;\">等</span></b><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">2.3 </span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">&nbsp;</span><b><span style=\"font-family: 宋体; font-size: 14pt;\">高校人才网可能会根据用户及市场的需求、产品及服务类型的变化等各种因素，对现有增值服务种类、功能、价格、权益进行调整以及不断推出新的增值服务种类。</span></b><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">具体增值服务种类及服务内容及费用标准以相关服务页面公布、实际提供的内容为准。用户可以自行根据需要选择相应服务。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">2.4 </span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">&nbsp;</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">用户可通过登录高校人才网，免费查询其消费的服务的详情（包括其购买的增值服务名称、购买时间、标示价格、即时服务状态等）。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">2.5 </span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">&nbsp;</span><b><span style=\"font-family: 宋体; font-size: 14pt;\">请注意：高校人才网提供的增值服务均非结果类付费服务，除服务产品有特别说明外，高校人才网对于用户购买及使用本服务后的实际招聘结果、求职结果、知识能力提升等不作任何承诺、不承担任何责任。</span></b><b><span style=\"font-family: 宋体; font-size: 14pt;\"></span></b></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">2.6 </span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">&nbsp;</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">用户理解并同意，高校人才网有权基于交易安全、求职安全等方面的考虑，就违规购买增值服务或严重违反诚实信用原则不当获取平台服务的部分用户设置适当的限制，包括但不限于交易时间、交易限额、交易次数等。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><b><span style=\"font-family: 宋体; font-size: 18pt;\">三、 服务购买、开通与使用</span></b><b><span style=\"font-family: 宋体; font-size: 18pt;\"></span></b></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">3.1&nbsp; 本服务是高校人才网提供的增值服务。用户须在按照本服务的收费标准支付相应费用后，方可使用本服务。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">3.2&nbsp; 服务费用标准：服务具体收费标准以高校人才网相应页面届时公布的内容为准。高校人才网有权随时修改各类服务的资费政策（包括但不限于服务价格）、订购方式，并在高校人才网相关页面予以公布。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><b><span style=\"font-family: 宋体; font-size: 14pt;\">3.3 </span></b><b><span style=\"font-family: Calibri; font-size: 14pt;\">&nbsp;</span></b><b><span style=\"font-family: 宋体; font-size: 14pt;\">如用户不同意按照届时高校人才网制定（修改）并公布的资费政策（包括但不限于服务价格）支付服务费用或不同意按照届时高校人才网制定（修改）并公布的订购方式订购相应服务，则用户将不能继续获得相应服务（部分或全部），高校人才网有权不予退还用户已支付的任何服务费用。</span></b><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">但是用户已经付费取得的服务内容、服务时长等，可以在原有范围内使用；如因服务内容变更导致无法继续使用的，用户可以申请退还未使用部分的费用。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">3.4 </span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">&nbsp;</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">服务开通</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">用户通过服务页面指定的支付方式进行付款。用户使用第三方支付工具购买本网站付费服务的，还应相应遵守第三方服务提供商的相应规则。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><b><span style=\"font-family: 宋体; font-size: 14pt;\">风险提示</span></b><b><span style=\"font-family: Calibri; font-size: 14pt;\">：</span></b><b><span style=\"font-family: 宋体; font-size: 14pt;\">用户应妥善保存并使用自己的账户密码、支付工具及密码等，非高校人才网原因致使用户账户密码泄漏以及因用户保管、使用、维护不当造成损失的，高校人才网无须承担与此有关的任何责任。</span></b><b><span style=\"font-family: 宋体; font-size: 14pt;\"></span></b></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">3.5 </span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">&nbsp;</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">用户不得实施的开通行为</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">非经高校人才网许可，用户不得通过以下方式开通/获取本服务，否则将构成违约。</span><b><span style=\"font-family: 宋体; font-size: 14pt;\">用户在使用本服务时，如有如下情况的，或高校人才网有理由怀疑用户存在下述情形的，高校人才网有权采取一定的处理措施，包括但不限于要求整改、删除信息、部分或全部终止用户使用本服务的资格，由此造成的损失由用户承担，用户不得因此要求高校人才网任何补偿或赔偿，用户已支付的费用不予退还：</span></b><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">（1）以营利、经营等非个人使用的目的为自己或他人开通本服务；</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">（2）通过任何机器人软件、蜘蛛软件、爬虫软件、刷屏软件等任何程序、软件方式为自己或他人开通本服务；</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">（3）通过任何不正当手段或以违反诚实信用原则的方式（包括但不限于利用高校人才网可能存在的漏洞、绕开高校人才网的商业规则/技术规则等）为自己或他人开通/获取本服务；</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">（4）通过非高校人才网指定的方式为自己或他人开通本服务；</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">（5）通过侵犯高校人才网或他人合法权益的方式为自己或他人开通本服务，如通过第三方平台由他人代充代付；</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">（6）通过其他违反高校人才《用户服务协议》或相关法律、行政法规、国家政策等的方式为自己或他人开通/获取本服务。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">3.6&nbsp;</span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">&nbsp;</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">费用退还</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><b><span style=\"font-family: 宋体; font-size: 14pt;\">高校人才网平台提供的增值服务均属数字化商品，基于互联网服务的特殊性和国家相关管理规定，数字化商品不支持七天无理由</span></b><b><span style=\"font-family: 宋体; font-size: 14pt;\">退货</span></b><b><span style=\"font-family: 宋体; font-size: 14pt;\">。</span></b><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">&nbsp;</span><b><span style=\"font-family: 宋体; font-size: 14pt;\">用户付款成功后，原则上高校人才网不提供任何退款或变更订单服务。无论是否已使用相关服务，用户均不得向高校人才网要求退还支付的金额</span></b><b><span style=\"font-family: Calibri; font-size: 14pt;\">，</span></b><b><span style=\"font-family: 宋体; font-size: 14pt;\">除非因高校人才网原因导致所支付金额无法使用到任何服务中。</span></b><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">若发生异议</span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">，</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">用户</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">可通过客服专线020-85611139</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">进行</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">咨询。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">3.7 </span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">&nbsp;</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">服务期限</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><b><span style=\"font-family: 宋体; font-size: 14pt;\">服务存在相关有效期限，用户购买服务后，请在有效期内使用完毕。有效期届满，服务终止。若用户未使用或未使用完服务，在任何情况下，都不进行退款或者延期处理。请您合理安排自己的时间，并尽快使用。</span></b><b><span style=\"font-family: 宋体; font-size: 14pt;\"></span></b></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">3.8 </span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">&nbsp;</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">服务使用</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">（1）高校人才网将在用户购买本服务并支付相应价款后及时提供服务，本服务仅限购买账户使用，无法转让。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">（2）用户购买使用求职类服务仅可用于自身求职使用，不得利用服务进行除自身求职以外的行为或损害任何第三方合法利益，包括但不限于：发送虚假简历，将所购买的服务以任何形式进行二次售卖或商业获利活动，利用求职道具进行推销、引流、欺诈、骚扰、借贷、营销等。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">3.9 </span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">&nbsp;</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">申请开票</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">用户在高校人才网购买增值服务后，可联系高校人才网客服（020-85611139），申请开具电子发票（增值税普通发票）。请您注意以下情况：</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">(1) 发票金额不能高于订单实际支付金额，多个订单可以对应一张发票。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">(2) 用户在服务订单页面完成支付后（1年期限内）均可提交开票申请。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">(3) 开票成功后不可修改发票申请，用户应保证提供给高校人才网的开票资料真实、合法、有效；开票主体应与用户在高校人才网所认证的身份保持一致，如用户提交的开票资料有误或开票主体不一致等原因，高校人才网有权直接拒绝用户的开票请求。用户如遇开票问题，例如更换发票、重开发票等，可咨询人工客服。请用户保证提供信息确认无误，以避免后续产生不必要的纠纷。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">(</span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">4</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">) 若用户开具发票后出现退款的情况，高校人才网将会对之前已经开具的电子发票进行作废处理</span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">(6) 用户提交开票申请后，高校人才网会尽快完成审核，正常情况下会在15个工作日内完成。如遇特殊情况需要延长期限的（如审核驳回等），高校人才网客服会及时通知用户。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">3</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">.</span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">10 &nbsp;责任声明</span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\"></span></p><p><b><span style=\"font-family: Calibri; font-size: 14pt;\">用户必须为自己注册账号下的一切行为负责，包括但不限于所发表的任何内容以及由此产生的任何后果。用户应对本服务中的内容自行加以判断，并承担因使用内容而引起的所有风险，包括因对内容的正确性、完整性或实用性的依赖而产生的风险。</span></b><b><span style=\"font-family: 宋体; font-size: 14pt;\"></span></b></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><b><span style=\"font-family: 宋体; font-size: 18pt;\">四、协议的变更、中断与终止</span></b><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">4.1</span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">&nbsp;</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">&nbsp;协议的更新和修改</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">高校人才网保留随时修正、更新本协议的权利。用户可以随时访问本协议页面来获知最新版本。如果不同意高校人才网对本协议相关条款所做的修改，用户有权停止使用高校人才网服务，已收款项不进行退款。如果继续使用高校人才网相关服务，则视为用户接受高校人才网对本协议相关条款所做的修改。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">4.2</span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">&nbsp;</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">&nbsp;协议的解除和终止</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">有以下情形的，本协议归于解除或终止：</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">（1）如用户严重违反本服务条款中的内容，或违反高校人才网《用户服务协议》，高校人才网有权在不通知用户的情况下立即终止用户已购买的部分或所有服务，以及暂时或永久取消用户的高校人才网账户和使用权限，但不退还任何已缴纳的服务费用。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">（2）高校人才网基于法律法规等规范性文件、高校人才网的各项规则和协议以及社会公序良俗，终止对用户提供服务的。该情形的协议终止并不免除用户应承担的相应责任。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">（3）一方主体归于消灭、撤销等无法有效存续状态的。依据法律法规的规定处理未决问题。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">（4）</span><b><span style=\"font-family: 宋体; font-size: 14pt;\">因高校人才网业务调整、国家或监管部门要求或发生不可抗力事件时，导致服务无法继续，高校人才网将尽快通知用户，但不承担由此对用户造成的任何损失并不退还任何费用。</span></b><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">“不可抗力”是指高校人才网不能合理控制、不可预见或即使预见亦无法避免的事件。该事件包括但不限于政府行为、骨干通信线路故障、地震、台风、洪水、火灾及其它天灾、战争或任何其它类似事件。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">（5）用户通过未经高校人才网授权的第三方平台或渠道（如淘宝店铺）进行代购高校人才网增值服务的，将构成严重违约，高校人才网有权终止用户所购产品的使用权限，用户无权向高校人才网要求退款、主张赔偿或补偿。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">（6）本协议终止或更新时，用户不接受新的协议。用户应当立即停止使用本网站的服务。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><b><span style=\"font-family: 宋体; font-size: 18pt;\">五、违约责任</span></b><b><span style=\"font-family: 宋体; font-size: 18pt;\"></span></b></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">5.1 </span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">&nbsp;</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">任何一方违反本协议约定的行为均构成违约行为，均应承担相应的责任。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">5.2 </span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">&nbsp;</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">对于用户的任何违反本协议规定的行为，高校人才网有权采取相应处理措施，方式包括但不限于记录不良行为、调整用户信用等级、黑白名单限制、暂停服务、下线信息、封禁用户账号等。形式包括系统后台记录或网站公示公开。该等处理措施将影响用户使用本网站的服务，涉及违法、犯罪的，本网站将移交司法机关处理。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><b><span style=\"font-family: 宋体; font-size: 14pt;\">请注意：用户违反本协议约定的，或被系统检测出异常操作行为的（如短时间内多次或超过合理程度购买/取消订单，发布职位或沟通频率数量等明显超出合理程度等），利用平台规则漏洞获取服务的，或任何导致平台其他用户正当投诉或举报达三次及以上的事宜或行为，视为严重违约，高校人才网有权立即停止提供服务并对其在平台上的账号、公司主页和/或发布的信息进行封禁，且不予退还任何已支付的费用，给高校人才网造成其他损失的（包括但不限于因此高校人才网需对第三方承担的损失），应由该用户承担全部责任。</span></b><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">5.3 </span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">&nbsp;</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">如因高校人才网违反法律、法规规定或本协议约定而给用户造成损失，高校人才网应赔偿用户因此遭受的损失（赔偿责任以用户就使用服务已支付的价款为限）。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><b><span style=\"font-family: 宋体; font-size: 18pt;\">六、其他</span></b><b><span style=\"font-family: 宋体; font-size: 18pt;\">条款</span></b><b><span style=\"font-family: 宋体; font-size: 18pt;\"></span></b></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">6.1</span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">&nbsp;</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">&nbsp;高校人才网的所有权、运营权、</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">最终</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">解释权归广州高才信息科技有限公司所有。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">6.2</span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">&nbsp;</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">&nbsp;本协议项下所有的通知均可通过软件内通知、电子邮件、短信或常规的信件传送等方式进行；该等通知于发送之日视为已送达收件人。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">6.3 </span><span style=\"mso-spacerun:\'yes\';font-family:Calibri;mso-fareast-font-family:宋体;\nmso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;mso-font-kerning:1.0000pt;\">&nbsp;</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\">本协议的订立、执行和解释及争议的解决均应适用中国法律并受中国法院管辖。如本服务条款与中华人民共和国法律相抵触时，则该等条款将按法律规定重新修订，而其它条款则依旧有效并具有法律效力。</span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"><font>6.4&nbsp; </font><font>如双方就本协议内容或其执行发生任何争议，双方应尽量友好协商解决；协商不成时，任何一方均可向高校人才网运营方广州高才信息科技有限公司住所地有管辖权的人民法院提起诉讼。</font></span><span style=\"mso-spacerun:\'yes\';font-family:宋体;mso-ascii-font-family:Calibri;\nmso-hansi-font-family:Calibri;mso-bidi-font-family:\'Times New Roman\';font-size:14.0000pt;\nmso-font-kerning:1.0000pt;\"></span></p>',
        '增值服务协议', 0, 0);

