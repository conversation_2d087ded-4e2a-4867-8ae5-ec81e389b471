CREATE TABLE `chat_common_greeting`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT,
    `add_time`    datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `content`     varchar(512)     NOT NULL DEFAULT '' COMMENT '内容',
    `member_id`   int(11)          NOT NULL DEFAULT '0' COMMENT '所属member_id',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 10000 #这一行不要删除
  DEFAULT CHARSET = utf8mb4 COMMENT ='用户聊天打招呼语';


CREATE TABLE `chat_common_greeting_system`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT,
    `add_time`    datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `content`     varchar(512)     NOT NULL DEFAULT '' COMMENT '内容',
    `type`        tinyint(2)       NOT NULL COMMENT '类型(跟随member表type含义):1个人/2企业',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='系统聊天打招呼语';


ALTER TABLE member
    ADD COLUMN `is_chat_window` tinyint(1) NOT NULL DEFAULT '2' COMMENT '是否打开聊天窗 1是 2否';
ALTER TABLE member
    ADD COLUMN `is_greeting` tinyint(1) NOT NULL DEFAULT '2' COMMENT '是否启用招呼语 1启用 2关闭';
ALTER TABLE member
    ADD COLUMN `greeting_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '默认招呼语类型 1系统 2自定义';
ALTER TABLE member
    ADD COLUMN `greeting_default_id` int(11) NOT NULL DEFAULT '0' COMMENT '默认招呼语ID';

INSERT INTO `chat_common_greeting_system` (`id`, `add_time`, `update_time`, `content`, `type`)
VALUES (1, '2024-08-12 16:02:26', '0000-00-00 00:00:00', '您好，您与我们正在寻找的人才高度契合，非常期待与您深入交流。',
        2);
INSERT INTO `chat_common_greeting_system` (`id`, `add_time`, `update_time`, `content`, `type`)
VALUES (2, '2024-08-12 16:02:26', '0000-00-00 00:00:00', '您好，我们对您的才能和经验非常感兴趣，非常期待与您深入交流。',
        2);
INSERT INTO `chat_common_greeting_system` (`id`, `add_time`, `update_time`, `content`, `type`)
VALUES (3, '2024-08-12 16:02:26', '0000-00-00 00:00:00',
        '您好，看了您的简历，觉得比较符合我们的职位要求，期待有机会与您进一步交流。', 2);


# 最后执行 - 单位账账号默认给一个系统招呼语
update member
set is_chat_window      = 2,
    is_greeting         = 1,
    greeting_type       = 1,
    greeting_default_id = (select id from chat_common_greeting_system where type = 2 order by id desc limit 1)
where type = 2;


