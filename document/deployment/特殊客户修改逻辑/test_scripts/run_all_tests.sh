#!/bin/bash

# 特殊需求功能完整测试执行脚本
# 使用方法: ./run_all_tests.sh [base_url] [db_host] [db_user] [db_password] [db_name] [session_cookie]

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置参数
BASE_URL=${1:-"http://localhost"}
DB_HOST=${2:-"localhost"}
DB_USER=${3:-"root"}
DB_PASSWORD=${4:-""}
DB_NAME=${5:-"test_db"}
SESSION_COOKIE=${6:-"your_session_cookie"}

# 测试结果目录
TEST_RESULTS_DIR="test_results_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$TEST_RESULTS_DIR"

# 日志函数
log() {
    echo -e "$1" | tee -a "$TEST_RESULTS_DIR/test_summary.log"
}

# 检查依赖
check_dependencies() {
    log "${BLUE}检查测试依赖...${NC}"
    
    # 检查curl
    if ! command -v curl &> /dev/null; then
        log "${RED}错误: curl 未安装${NC}"
        exit 1
    fi
    
    # 检查mysql
    if ! command -v mysql &> /dev/null; then
        log "${RED}错误: mysql 客户端未安装${NC}"
        exit 1
    fi
    
    # 检查php
    if ! command -v php &> /dev/null; then
        log "${RED}错误: php 未安装${NC}"
        exit 1
    fi
    
    # 检查bc（用于计算）
    if ! command -v bc &> /dev/null; then
        log "${RED}错误: bc 未安装${NC}"
        exit 1
    fi
    
    log "${GREEN}✓ 依赖检查通过${NC}"
}

# 数据库连接测试
test_database_connection() {
    log "${BLUE}测试数据库连接...${NC}"
    
    if [ -z "$DB_PASSWORD" ]; then
        mysql -h "$DB_HOST" -u "$DB_USER" -e "SELECT 1;" "$DB_NAME" &> /dev/null
    else
        mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" "$DB_NAME" &> /dev/null
    fi
    
    if [ $? -eq 0 ]; then
        log "${GREEN}✓ 数据库连接成功${NC}"
        return 0
    else
        log "${RED}✗ 数据库连接失败${NC}"
        return 1
    fi
}

# API连接测试
test_api_connection() {
    log "${BLUE}测试API连接...${NC}"
    
    response=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/admin/special-need/config-index" \
        -H "Content-Type: application/json" \
        -b "$SESSION_COOKIE")
    
    if [ "$response" = "200" ] || [ "$response" = "401" ]; then
        log "${GREEN}✓ API连接成功${NC}"
        return 0
    else
        log "${RED}✗ API连接失败 (HTTP $response)${NC}"
        return 1
    fi
}

# 执行数据库测试
run_database_tests() {
    log "${YELLOW}========== 执行数据库测试 ==========${NC}"
    
    if [ -z "$DB_PASSWORD" ]; then
        mysql -h "$DB_HOST" -u "$DB_USER" "$DB_NAME" < database_test.sql > "$TEST_RESULTS_DIR/database_test_results.txt" 2>&1
    else
        mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < database_test.sql > "$TEST_RESULTS_DIR/database_test_results.txt" 2>&1
    fi
    
    if [ $? -eq 0 ]; then
        log "${GREEN}✓ 数据库测试完成${NC}"
        
        # 统计测试结果
        pass_count=$(grep -c "PASS" "$TEST_RESULTS_DIR/database_test_results.txt" || echo "0")
        fail_count=$(grep -c "FAIL" "$TEST_RESULTS_DIR/database_test_results.txt" || echo "0")
        
        log "数据库测试结果: 通过 $pass_count, 失败 $fail_count"
        return 0
    else
        log "${RED}✗ 数据库测试失败${NC}"
        return 1
    fi
}

# 执行API测试
run_api_tests() {
    log "${YELLOW}========== 执行API测试 ==========${NC}"
    
    chmod +x api_test.sh
    ./api_test.sh "$BASE_URL" "$SESSION_COOKIE" > "$TEST_RESULTS_DIR/api_test_results.txt" 2>&1
    
    if [ $? -eq 0 ]; then
        log "${GREEN}✓ API测试完成${NC}"
        
        # 复制测试日志
        cp test_results_*.log "$TEST_RESULTS_DIR/" 2>/dev/null || true
        cp test_report_*.html "$TEST_RESULTS_DIR/" 2>/dev/null || true
        
        return 0
    else
        log "${RED}✗ API测试失败${NC}"
        return 1
    fi
}

# 执行业务逻辑测试
run_business_logic_tests() {
    log "${YELLOW}========== 执行业务逻辑测试 ==========${NC}"

    # 设置PHP路径（如果需要）
    export PATH="/usr/local/php/bin:$PATH"

    php business_logic_test.php > "$TEST_RESULTS_DIR/business_logic_test_results.txt" 2>&1

    if [ $? -eq 0 ]; then
        log "${GREEN}✓ 业务逻辑测试完成${NC}"

        # 复制测试报告
        cp business_logic_test_report_*.txt "$TEST_RESULTS_DIR/" 2>/dev/null || true

        return 0
    else
        log "${RED}✗ 业务逻辑测试失败${NC}"
        return 1
    fi
}

# 执行职称字段专项测试
run_title_field_tests() {
    log "${YELLOW}========== 执行职称字段专项测试 ==========${NC}"

    # 设置PHP路径（如果需要）
    export PATH="/usr/local/php/bin:$PATH"

    php title_field_test.php > "$TEST_RESULTS_DIR/title_field_test_results.txt" 2>&1

    if [ $? -eq 0 ]; then
        log "${GREEN}✓ 职称字段专项测试完成${NC}"

        # 统计测试结果
        pass_count=$(grep -c "✓" "$TEST_RESULTS_DIR/title_field_test_results.txt" || echo "0")
        fail_count=$(grep -c "✗" "$TEST_RESULTS_DIR/title_field_test_results.txt" || echo "0")

        log "职称字段测试结果: 通过 $pass_count, 失败 $fail_count"
        return 0
    else
        log "${RED}✗ 职称字段专项测试失败${NC}"
        return 1
    fi
}

# 执行缓存功能测试
run_cache_tests() {
    log "${YELLOW}========== 执行缓存功能测试 ==========${NC}"

    # 设置PHP路径（如果需要）
    export PATH="/usr/local/php/bin:$PATH"

    php cache_test.php > "$TEST_RESULTS_DIR/cache_test_results.txt" 2>&1

    if [ $? -eq 0 ]; then
        log "${GREEN}✓ 缓存功能测试完成${NC}"

        # 统计测试结果
        pass_count=$(grep -c "✓" "$TEST_RESULTS_DIR/cache_test_results.txt" || echo "0")
        fail_count=$(grep -c "✗" "$TEST_RESULTS_DIR/cache_test_results.txt" || echo "0")

        log "缓存功能测试结果: 通过 $pass_count, 失败 $fail_count"
        return 0
    else
        log "${RED}✗ 缓存功能测试失败${NC}"
        return 1
    fi
}

# 执行缓存性能测试
run_cache_performance_tests() {
    log "${YELLOW}========== 执行缓存性能测试 ==========${NC}"

    # 设置PHP路径（如果需要）
    export PATH="/usr/local/php/bin:$PATH"

    php cache_performance_test.php > "$TEST_RESULTS_DIR/cache_performance_test_results.txt" 2>&1

    if [ $? -eq 0 ]; then
        log "${GREEN}✓ 缓存性能测试完成${NC}"

        # 提取性能数据
        improvement=$(grep "性能提升:" "$TEST_RESULTS_DIR/cache_performance_test_results.txt" | head -1 | grep -o "[0-9.]*%" || echo "N/A")

        log "缓存性能提升: $improvement"
        return 0
    else
        log "${RED}✗ 缓存性能测试失败${NC}"
        return 1
    fi
}

# 执行小程序jobBasicsInfo字段测试
run_job_basics_info_tests() {
    log "${YELLOW}========== 执行小程序jobBasicsInfo字段测试 ==========${NC}"

    # 设置PHP路径（如果需要）
    export PATH="/usr/local/php/bin:$PATH"

    php job_basics_info_test.php > "$TEST_RESULTS_DIR/job_basics_info_test_results.txt" 2>&1

    if [ $? -eq 0 ]; then
        log "${GREEN}✓ 小程序jobBasicsInfo字段测试完成${NC}"

        # 统计测试结果
        pass_count=$(grep -c "✓" "$TEST_RESULTS_DIR/job_basics_info_test_results.txt" || echo "0")
        fail_count=$(grep -c "✗" "$TEST_RESULTS_DIR/job_basics_info_test_results.txt" || echo "0")

        log "jobBasicsInfo测试结果: 通过 $pass_count, 失败 $fail_count"
        return 0
    else
        log "${RED}✗ 小程序jobBasicsInfo字段测试失败${NC}"
        return 1
    fi
}

# 生成综合测试报告
generate_comprehensive_report() {
    log "${BLUE}生成综合测试报告...${NC}"
    
    report_file="$TEST_RESULTS_DIR/comprehensive_test_report.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>特殊需求功能综合测试报告</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .passed { color: green; font-weight: bold; }
        .failed { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .summary { background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .test-results { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; }
        @media (max-width: 768px) { .test-results { grid-template-columns: 1fr; } }
    </style>
</head>
<body>
    <div class="header">
        <h1>特殊需求功能综合测试报告</h1>
        <p><strong>测试时间:</strong> $(date)</p>
        <p><strong>测试环境:</strong> $BASE_URL</p>
        <p><strong>数据库:</strong> $DB_HOST/$DB_NAME</p>
    </div>
    
    <div class="summary">
        <h2>测试概览</h2>
        <div class="test-results">
            <div>
                <h3>数据库测试</h3>
EOF

    # 添加数据库测试结果
    if [ -f "$TEST_RESULTS_DIR/database_test_results.txt" ]; then
        db_pass=$(grep -c "PASS" "$TEST_RESULTS_DIR/database_test_results.txt" || echo "0")
        db_fail=$(grep -c "FAIL" "$TEST_RESULTS_DIR/database_test_results.txt" || echo "0")
        echo "                <p class=\"passed\">通过: $db_pass</p>" >> "$report_file"
        echo "                <p class=\"failed\">失败: $db_fail</p>" >> "$report_file"
    else
        echo "                <p class=\"warning\">未执行</p>" >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF
            </div>
            <div>
                <h3>API测试</h3>
EOF

    # 添加API测试结果
    if [ -f "$TEST_RESULTS_DIR/api_test_results.txt" ]; then
        api_pass=$(grep -c "✓ PASSED" "$TEST_RESULTS_DIR/api_test_results.txt" || echo "0")
        api_fail=$(grep -c "✗ FAILED" "$TEST_RESULTS_DIR/api_test_results.txt" || echo "0")
        echo "                <p class=\"passed\">通过: $api_pass</p>" >> "$report_file"
        echo "                <p class=\"failed\">失败: $api_fail</p>" >> "$report_file"
    else
        echo "                <p class=\"warning\">未执行</p>" >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF
            </div>
            <div>
                <h3>业务逻辑测试</h3>
EOF

    # 添加业务逻辑测试结果
    if [ -f "$TEST_RESULTS_DIR/business_logic_test_results.txt" ]; then
        bl_pass=$(grep -c "✓ PASSED" "$TEST_RESULTS_DIR/business_logic_test_results.txt" || echo "0")
        bl_fail=$(grep -c "✗ FAILED" "$TEST_RESULTS_DIR/business_logic_test_results.txt" || echo "0")
        echo "                <p class=\"passed\">通过: $bl_pass</p>" >> "$report_file"
        echo "                <p class=\"failed\">失败: $bl_fail</p>" >> "$report_file"
    else
        echo "                <p class=\"warning\">未执行</p>" >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF
            </div>
        </div>
    </div>
    
    <div class="section">
        <h2>详细测试结果</h2>
        
        <h3>数据库测试详情</h3>
        <pre>$(cat "$TEST_RESULTS_DIR/database_test_results.txt" 2>/dev/null || echo "数据库测试未执行")</pre>
        
        <h3>API测试详情</h3>
        <pre>$(cat "$TEST_RESULTS_DIR/api_test_results.txt" 2>/dev/null || echo "API测试未执行")</pre>
        
        <h3>业务逻辑测试详情</h3>
        <pre>$(cat "$TEST_RESULTS_DIR/business_logic_test_results.txt" 2>/dev/null || echo "业务逻辑测试未执行")</pre>
    </div>
    
    <div class="section">
        <h2>测试文件</h2>
        <ul>
EOF

    # 列出所有测试文件
    for file in "$TEST_RESULTS_DIR"/*; do
        if [ -f "$file" ]; then
            filename=$(basename "$file")
            echo "            <li><a href=\"$filename\">$filename</a></li>" >> "$report_file"
        fi
    done
    
    cat >> "$report_file" << EOF
        </ul>
    </div>
    
    <div class="section">
        <h2>建议和后续步骤</h2>
        <ul>
            <li>如果有测试失败，请查看详细日志进行排查</li>
            <li>确保所有配置正确生效</li>
            <li>验证不同平台的显示效果</li>
            <li>测试投递限制功能</li>
            <li>检查性能指标</li>
        </ul>
    </div>
</body>
</html>
EOF

    log "${GREEN}✓ 综合测试报告已生成: $report_file${NC}"
}

# 主执行流程
main() {
    log "${BLUE}特殊需求功能完整测试开始${NC}"
    log "测试时间: $(date)"
    log "测试环境: $BASE_URL"
    log "数据库: $DB_HOST/$DB_NAME"
    log "=========================================="
    
    # 检查依赖
    check_dependencies
    
    # 测试连接
    db_connection_ok=false
    api_connection_ok=false
    
    if test_database_connection; then
        db_connection_ok=true
    fi
    
    if test_api_connection; then
        api_connection_ok=true
    fi
    
    # 执行测试
    test_count=0
    success_count=0
    
    if [ "$db_connection_ok" = true ]; then
        test_count=$((test_count + 1))
        if run_database_tests; then
            success_count=$((success_count + 1))
        fi
    fi
    
    if [ "$api_connection_ok" = true ]; then
        test_count=$((test_count + 1))
        if run_api_tests; then
            success_count=$((success_count + 1))
        fi
    fi
    
    # 业务逻辑测试（需要数据库连接）
    if [ "$db_connection_ok" = true ]; then
        test_count=$((test_count + 1))
        if run_business_logic_tests; then
            success_count=$((success_count + 1))
        fi
    fi

    # 职称字段专项测试（需要数据库连接）
    if [ "$db_connection_ok" = true ]; then
        test_count=$((test_count + 1))
        if run_title_field_tests; then
            success_count=$((success_count + 1))
        fi
    fi

    # 缓存功能测试（需要数据库和Redis连接）
    if [ "$db_connection_ok" = true ]; then
        test_count=$((test_count + 1))
        if run_cache_tests; then
            success_count=$((success_count + 1))
        fi
    fi

    # 缓存性能测试（需要数据库和Redis连接）
    if [ "$db_connection_ok" = true ]; then
        test_count=$((test_count + 1))
        if run_cache_performance_tests; then
            success_count=$((success_count + 1))
        fi
    fi

    # 小程序jobBasicsInfo字段测试
    test_count=$((test_count + 1))
    if run_job_basics_info_tests; then
        success_count=$((success_count + 1))
    fi
    
    # 生成综合报告
    generate_comprehensive_report
    
    # 输出最终结果
    log "=========================================="
    log "测试完成！"
    log "执行的测试模块: $test_count"
    log "成功的测试模块: $success_count"
    log "失败的测试模块: $((test_count - success_count))"
    
    if [ $success_count -eq $test_count ]; then
        log "${GREEN}✓ 所有测试模块执行成功${NC}"
    else
        log "${RED}✗ 部分测试模块执行失败${NC}"
    fi
    
    log "测试结果目录: $TEST_RESULTS_DIR"
    log "综合报告: $TEST_RESULTS_DIR/comprehensive_test_report.html"
    log "=========================================="
    
    # 返回适当的退出码
    if [ $success_count -eq $test_count ]; then
        exit 0
    else
        exit 1
    fi
}

# 显示使用说明
show_usage() {
    echo "特殊需求功能完整测试脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [base_url] [db_host] [db_user] [db_password] [db_name] [session_cookie]"
    echo ""
    echo "参数说明:"
    echo "  base_url      - API基础URL (默认: http://localhost)"
    echo "  db_host       - 数据库主机 (默认: localhost)"
    echo "  db_user       - 数据库用户 (默认: root)"
    echo "  db_password   - 数据库密码 (默认: 空)"
    echo "  db_name       - 数据库名称 (默认: test_db)"
    echo "  session_cookie - 会话Cookie (默认: your_session_cookie)"
    echo ""
    echo "示例:"
    echo "  $0 http://test.example.com localhost root password test_db 'session_id=abc123'"
    echo ""
}

# 检查是否需要显示帮助
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_usage
    exit 0
fi

# 执行主流程
main
