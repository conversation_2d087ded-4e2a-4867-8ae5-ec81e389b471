# ReadMe

### 禅道bug(罗列bug地址或者id)
- [BUG #1036 【求职者PC】资讯的文档属性设置为跳转链接，没有跳转到指定链接地址 - 新官网 - 禅道 (ideaboat.cn)](http://zentao.gc.ideaboat.cn/index.php?m=bug&f=view&bugID=1036)

***

### 参与人员

- 龚传栋
- xxx
- xxx

***

### 分支

|仓库|bug分支|备注|
|:----:|:----:|:----:|
|new_gaoxiao_yii|hotfix/1036_【求职者PC】资讯的文档属性设置为跳转链接，没有跳转到指定链接地址_|-|



***

### 上线部署步骤

`内容为空,填写"-"即可`

|步骤|是否执行|执行内容|
|:----|:----:|:----|
|提醒前端提前合并代码到master和添加权限节点|-|-|
|执行sql语句|-|见下方"执行sql语句"|
|更新后端代码|是|-|
|composer安装|-|见下方"composer安装"|
|更新配置|-|见下方"更新配置"|
|创建队列|-|见下方"创建队列"|
|执行脚本|-|见下方"执行脚本"|
|删除redis缓存|-|见下方"删除redis缓存"|
|重启队列|-|上线前, 暂停所有队列, 上线后再重启|
|更新前端代码|-|-|
|添加定时任务|-|见下方"定时任务"|
|群内通知部署完毕|-|-|

#### composer安装
执行以下两条命令(按顺序), 安装对应的包
```
composer require "xxxxx"
```
#### 执行sql语句(按顺序执行)

* alter_data.sql

#### 更新配置

```shell

```

#### 创建队列
```shell

```

#### 


#### 执行脚本(普通脚本建议支持后台运行,特殊脚本需要注明执行顺序)
xxx功能脚本
```shell
php timer.php
```
#### 删除redis缓存
``` shell

```


#### 定时任务

|填写项|内容|
|:----|:----:|
|任务名称|xxx|
|标签|xx,xx|
|crontab表达式|0 0 20 * * 0|
|命令|xxxx|
|任务超时时间|0|
|备注|xxxx|
