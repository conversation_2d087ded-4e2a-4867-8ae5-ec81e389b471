<?php

namespace hai<PERSON>ai\controllers;

use common\base\BaseController;
use common\base\models\BaseMemberLoginForm;
use common\libs\Cache;
use Yii;

class BaseHaiWaiController extends BaseController
{

    public $memberId;
    public $token;

    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }

        if (in_array($action->uniqueID, $this->ignoreLogin())) {
            return parent::beforeAction($action);
        }

        if (Yii::$app->user->isGuest) {
            $token = \Yii::$app->request->getHeaders()
                ->get('token');
            if (!$token) {
                header('Content-Type: application/json');
                $ret = [
                    'code'   => 403,
                    'result' => 0,
                    'msg'    => '请您先进行登录后再操作',
                ];

                echo json_encode($ret);
                exit;
            } else {
                $jwtAuth  = new \common\libs\JwtAuth();
                $memberId = $jwtAuth->checkToken($token);

                if ($memberId) {
                    $this->token    = $token;
                    $this->memberId = $memberId;

                    (new BaseMemberLoginForm())->loginById($memberId);

                    $this->setActive();
                }
            }
        }

        return parent::beforeAction($action);
    }

    /**
     * 无需登录就可以操作的控制器
     * @return string[]
     */
    public function ignoreLogin()
    {
        return [
            'test/test',
            'home/index',
            'home/get-all',
            'home/oversea-talent',
            'home/back-country',
            'home/recruitment-announcement',
            'home/oversea-youth',
            'home/company-list',
            'home/about-us',
            'home/get-info',
            'home/get-tmp-token',
            'home/get-tmp-token',
            'home/check-login-info',
            'company/get-search-list',
            'recruit-announcement/get-all',
            'recruit-announcement/get-announcement-list',
            'recruit-announcement/get-job-list',
            'overseas-talent/get-all',
            'come-home/get-all',
            'overseas-talent/get-activity-list',
            'come-home/get-activity-list',
            'excellent-youth/get-all',
            'common/get-overseas-tdk-info',
            'common/get-come-home-tdk-info',
            'common/other-page-tdk-info',
            'common/get-showcase-token',
            'excellent-youth/get-rely-announcement-list',
            'company/get-all',
            'about-us/get-all',
        ];
    }

    public function notFound()
    {
        Yii::$app->response->setStatusCode(404)
            ->send();
        echo $this->renderPartial('/home/<USER>');
        exit();
    }

    public function setActive()
    {
        // // 做一个简单的测试,把用户的id和现在的时间保存到缓存里面去,一段时间后再取出来,用于更新用户的活跃时间
        $userId   = Yii::$app->user->id;
        $actionId = Yii::$app->controller->action->uniqueId;

        if ($userId && $actionId) {
            $key  = Cache::ALL_RESUME_ACTION_CONTROLLER_KEY;
            $time = CUR_TIMESTAMP;
            // 写集合
            Cache::zadd($key, $time, $userId);
        }
    }

}