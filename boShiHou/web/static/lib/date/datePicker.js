var datePickerTemplate = `
    <div class="popover-container date-picker-container" ref="popoverContainer">
        <el-popover
        :width="width"
        :visible="visible"
        :append-to-body="false"
        transition="el-zoom-in-top"
        >
        <template #reference>
            <div @click="handleInit">
                <el-input
                    ref="dateInput"
                    @input="handleInputValue"
                    @blur="handleInputBlur"
                    v-model="inputValue"
                    :disabled="disabled"
                    :placeholder="placeholder"
                    :clearable="clearable"
                >
                    <template #suffix>
                    <i class="datepicker-suffix-icon"></i>
                    </template>
                </el-input>
            </div>
        </template>
        <div class="date-container">
            <!-- 年选择器 -->
            <div v-show="isShowYearPanel" class="year-picker-container">
                <div class="year-picker-top">
                <span class="year-picker-top-prev">
                    <el-button :disabled="prevYearBtnDisabled" @click="onYearChange('prev')" text>
                    <i class="el-icon-d-arrow-left"></i>
                    </el-button>
                </span>
                <span class="year-picker-top-label">{{ rangeYearStart }} - {{ rangeYearEnd }}</span>
                <span class="year-picker-top-next">
                    <el-button :disabled="nextYearBtnDisabled" @click="onYearChange('next')" text>
                    <i class="el-icon-d-arrow-right"></i>
                    </el-button>
                </span>
                </div>
                <div class="year-picker-content">
                <div
                    v-for="(item, index) in years"
                    :key="index"
                    @click="handleSelectYear(item)"
                    class="year-picker-content-item"
                    :class="{ current: item.isCurrent, selected: item.isSelected, disabled: item.disabled }"
                >
                    <slot name="default" :cell="item">
                    <span class="cell">{{ item.label }}</span>
                    </slot>
                </div>
                </div>
            </div>

            <!-- 月选择器 -->
            <div v-show="!isShowYearPanel" class="month-picker-container">
                <div class="month-picker-top">
                    <span class="month-picker-top-prev">
                        <el-button @click="onMonthChange('prev')" text>
                        <i class="el-icon-d-arrow-left"></i>
                        </el-button>
                    </span>
                    <span @click="showYearPanel" class="month-picker-top-label">{{ monthPanelYear }}</span>
                    <span class="month-picker-top-next">
                        <el-button :disabled="nextMonthBtnDisabled" @click="onMonthChange('next')" text>
                        <i class="el-icon-d-arrow-right"></i>
                        </el-button>
                    </span>
                </div>
                <div class="month-picker-content">
                    <div
                        v-for="(item, index) in months"
                        :key="index"
                        @click="handleSelectMonth(item)"
                        class="month-picker-content-item"
                        :class="{ current: item.isCurrent, selected: item.isSelected, disabled: item.disabled }"
                    >
                        <slot name="default">
                        <span class="cell">{{ item.label }}</span>
                        </slot>
                    </div>
                </div>
            </div>
        </div>
        </el-popover>
    </div>
`
/**
 * 时间日期转换
 * @param date 当前时间，new Date() 格式
 * @param format 需要转换的时间格式字符串
 * @description format 字符串随意，如 `YYYY-mm、YYYY-mm-dd`
 * @description format 季度："YYYY-mm-dd HH:MM:SS QQQQ"
 * @description format 星期："YYYY-mm-dd HH:MM:SS WWW"
 * @description format 几周："YYYY-mm-dd HH:MM:SS ZZZ"
 * @description format 季度 + 星期 + 几周："YYYY-mm-dd HH:MM:SS WWW QQQQ ZZZ"
 * @returns 返回拼接后的时间字符串
 */
function formatDate(date, format) {
    const we = date.getDay() // 星期
    const z = getWeek(date) // 周
    const qut = Math.floor((date.getMonth() + 3) / 3).toString() // 季度
    const opt = {
        'Y+': date.getFullYear().toString(), // 年
        'm+': (date.getMonth() + 1).toString(), // 月(月份从0开始，要+1)
        'd+': date.getDate().toString(), // 日
        'H+': date.getHours().toString(), // 时
        'M+': date.getMinutes().toString(), // 分
        'S+': date.getSeconds().toString(), // 秒
        'q+': qut // 季度
    }
    // 中文数字 (星期)
    const week = {
        0: '日',
        1: '一',
        2: '二',
        3: '三',
        4: '四',
        5: '五',
        6: '六'
    }
    // 中文数字（季度）
    const quarter = {
        1: '一',
        2: '二',
        3: '三',
        4: '四'
    }
    if (/(W+)/.test(format)) format = format.replace(RegExp.$1, RegExp.$1.length > 1 ? (RegExp.$1.length > 2 ? `星期${week[we]}` : `周${week[we]}`) : week[we])
    if (/(Q+)/.test(format)) format = format.replace(RegExp.$1, RegExp.$1.length === 4 ? `第${quarter[qut]}季度` : quarter[qut])
    if (/(Z+)/.test(format)) format = format.replace(RegExp.$1, RegExp.$1.length === 3 ? `第${z}周` : `${z}`)
    for (const k in opt) {
        const r = new RegExp(`(${k})`).exec(format)
        // 若输入的长度不为1，则前面补零
        if (r) format = format.replace(r[1], RegExp.$1.length == 1 ? opt[k] : opt[k].padStart(RegExp.$1.length, '0'))
    }
    return format
}

/**
 * 获取当前日期是第几周
 * @param dateTime 当前传入的日期值
 * @returns 返回第几周数字值
 */
function getWeek(dateTime) {
    const temptTime = new Date(dateTime.getTime())
    // 周几
    const weekday = temptTime.getDay() || 7
    // 周1+5天=周六
    temptTime.setDate(temptTime.getDate() - weekday + 1 + 5)
    let firstDay = new Date(temptTime.getFullYear(), 0, 1)
    const dayOfWeek = firstDay.getDay()
    let spendDay = 1
    if (dayOfWeek !== 0) spendDay = 7 - dayOfWeek + 1
    firstDay = new Date(temptTime.getFullYear(), 0, 1 + spendDay)
    const d = Math.ceil((temptTime.valueOf() - firstDay.valueOf()) / 86400000)
    const result = Math.ceil(d / 7)
    return result
}

const MONTHMAP = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']

const datePickerComponent = {
    name: 'datePicker',
    template: datePickerTemplate,
    props: {
        clearable: {
            type: Boolean,
            default: () => false
        },
        /**
         * popover props start
         */
        disabled: {
            type: Boolean,
            default: () => false
        },
        placeholder: {
            type: String,
            default: () => '选择日期'
        },
        width: {
            type: [Number, String],
            default: () => 'auto'
        },
        /**
         * popover props end
         */
        showUpToNow: {
            type: Boolean,
            default: () => false
        },
        upToNowLabel: {
            type: String,
            default: () => '至今'
        },
        upToNowValue: {
            type: String,
            default: () => '0000-00'
        },
        format: {
            type: String,
            default: () => 'YYYY-mm'
        },
        valueFormat: {
            type: String,
            default: () => 'YYYY-mm'
        },
        type: {
            type: String,
            default: () => 'month'
        },
        modelValue: {
            type: String,
            default: () => ''
        },
        defaultValue: {
            type: Date,
            default: () => new Date()
        },
        disabledDate: {
            type: Function,
            default: () => false
        }
    },
    computed: {
        // 选中或回显的值
        selectValue: {
            get() {
                // 处理成new Date()格式 或者是'isCurrent'
                const { modelValue, upToNowValue } = this

                let value = ''
                const dateReg = /^(\d{0,4})$|^(\d{0,4}(-|\/)\d{1,2})$|^(\d{0,4}(-|\/)\d{1,2}(-|\/)\d{1,2})$/
                const upToNowReg = new RegExp(upToNowValue)
                if (upToNowReg.test(modelValue)) {
                    value = upToNowValue
                } else if (modelValue && dateReg.test(modelValue)) {
                    const dateArray = modelValue.replace(/-|\//gi, ',').split(',')
                    const [year, month = 0, date = 1] = dateArray
                    value = new Date(`${year}/${month}/${date}`)
                }
                return value
            },
            set(value) {
                const { upToNowValue, currentYear } = this
                let date = ''
                const upToNowReg = new RegExp(upToNowValue)
                if (!value) {
                    date = ''
                } else if (upToNowReg.test(value)) {
                    date = upToNowValue
                } else if (value) {
                    date = formatDate(new Date(value), this.valueFormat)
                }
                this.$emit('update:modelValue', date)
                this.visible = false
                this.currentPanelYear = upToNowReg.test(value) ? currentYear : new Date(value).getFullYear()
            }
        },

        selectValueText() {
            const { selectValue, upToNowValue, upToNowLabel, format } = this
            let value = ''
            if (selectValue === upToNowValue) {
                value = upToNowLabel
            } else {
                value = selectValue ? formatDate(new Date(selectValue), format) : ''
            }
            return value
        },

        rangeYearStart() {
            const [first = {}] = this.years
            return first.year
        },
        rangeYearEnd() {
            const { upToNowValue } = this
            const upToNowReg = new RegExp(upToNowValue)
            const realYears = this.years.filter((item) => !upToNowReg.test(item.year))
            const { length } = realYears
            return realYears[length - 1]?.year
        },
        prevYearBtnDisabled() {
            const [first] = this.years
            return first ? first.year - 12 < 0 : false
        },
        nextYearBtnDisabled() {
            const { years, upToNowValue } = this
            const upToNowReg = new RegExp(upToNowValue)
            const disable = years.some((item) => upToNowReg.test(item.year))
            return disable
        },
        /**
         * 年份相关计算属性 end
         */

        /**
         * 月份相关计算属性 start
         */
        months() {
            const { monthPanelYear } = this
            return this.createMonthData(monthPanelYear)
        },
        nextMonthBtnDisabled() {
            const { showUpToNow } = this
            const { currentYear, monthPanelYear } = this
            const disable = showUpToNow && currentYear === monthPanelYear
            return disable
        }

        /**
         * 月份相关计算属性 end
         */
    },
    watch: {
        selectValue: {
            handler(value) {
                this.createYearDate()
                const { upToNowValue } = this
                if (value !== upToNowValue) {
                    this.currentPanelYear = new Date(value).getFullYear()
                }
            }
        },
        selectValueText: {
            handler(value) {
                this.inputValue = value
            },
            immediate: true
        }
    },
    data() {
        return {
            visible: false,

            inputValue: '',

            /**
             * 年份面板相关数据 start
             */
            // 是否展示年份面板
            isShowYearPanel: true,
            // 今年
            currentYear: new Date().getFullYear(),
            // 选中年份
            selectYear: '',
            // 以这个年份展开年份面板
            currentPanelYear: '',
            // 年份列表数据
            years: [],
            /**
             * 年份面板相关数据 end
             */

            /**
             * 月份面板相关数据 start
             */
            monthPanelYear: 0
            /**
             * 月份面板相关数据 end
             */
        }
    },
    mounted() {
        this.createYearDate()
        document.body.addEventListener('click', this.onClickBody)
    },
    beforeUnmount() {
        document.body.addEventListener('click', this.onClickBody)
    },
    methods: {
        onClickBody(e) {
            const picker = this.$refs.popoverContainer
            if (!picker) return
            const isTarget = picker.contains(e.target)
            const { visible } = this
            if (visible) {
                this.visible = isTarget
            }
        },

        handleInit() {
            const { disabled } = this
            if (disabled) return
            this.createYearDate()
            this.isShowYearPanel = true
            this.visible = true
        },

        /**
         * 年份相关事件 start
         */
        // 获取每月最后一天
        getMonthLastDate(thisYear, month) {
            const setDate = new Date(thisYear, month, 0)
            const lastDate = setDate.getDate()
            return lastDate
        },

        handleYearDisable(thisYear) {
            const { disabledDate } = this
            let disabled = true
            for (let month = 1; month <= 12; month += 1) {
                const lastDate = this.getMonthLastDate(thisYear, month)
                let isDisabled = true
                for (let date = 1; date <= lastDate; date += 1) {
                    const setDate = new Date(`${thisYear}/${month}/${date}`)
                    isDisabled = disabledDate(setDate)
                    if (!isDisabled) {
                        disabled = isDisabled
                        break
                    }
                }
                if (!isDisabled) break
            }
            return disabled
        },

        onYearChange(type) {
            const { currentPanelYear } = this
            switch (type) {
                case 'prev':
                    this.currentPanelYear = Number(currentPanelYear) - 12
                    break
                case 'next':
                    this.currentPanelYear = Number(currentPanelYear) + 12
                    break
                default:
                    break
            }
            this.createYearDate()
        },

        createYearDate() {
            let startYear,
                endYear = 0
            let yearData = []
            // 设置为每年一月一号
            const month = 1
            const date = 1

            const { showUpToNow, upToNowValue, upToNowLabel, defaultValue, currentYear, selectValue } = this
            let { currentPanelYear } = this
            const upToNowReg = new RegExp(upToNowValue)

            let defaultYear = new Date(defaultValue).getFullYear()
            let selectYear = upToNowReg.test(selectValue) ? upToNowValue : new Date(selectValue).getFullYear()
            const isUpToNow = upToNowReg.test(selectValue)

            // 显示’至今‘
            if (showUpToNow) {
                // 显示至今，默认日期大于当前年份，重置为当前年份
                defaultYear = defaultYear > currentYear ? currentYear : defaultYear
                currentPanelYear = currentPanelYear || (isUpToNow ? currentYear : selectYear) || defaultYear

                // 年份所在位置
                const index = 12 - ((currentYear + 1 - currentPanelYear) % 12) - 1
                startYear = currentPanelYear - index
            } else {
                currentPanelYear = currentPanelYear || selectYear || defaultYear
                // 保证选中项或者当前年份在第二行第三列, 余数为6
                const remainder = 6
                startYear = currentPanelYear - remainder
            }
            this.currentPanelYear = currentPanelYear
            endYear = startYear + 12

            for (let i = startYear; i < endYear; i += 1) {
                const isSelected = (i - 1 === currentYear && showUpToNow && isUpToNow) || i === selectYear
                const year = i - 1 === currentYear && showUpToNow ? upToNowValue : i
                const label = i - 1 === currentYear && showUpToNow ? upToNowLabel : i
                const setDate = new Date(`${i}/${month}/${date}`)
                const timestamp = setDate.getTime()

                const disabled = this.handleYearDisable(i)
                yearData.push({
                    disabled,
                    isCurrent: i === currentYear,
                    isSelected,
                    timestamp,
                    year,
                    label,
                    date: setDate
                })
            }
            this.years = yearData
        },

        handleSelectYear(data) {
            const { disabled, year } = data
            const { upToNowValue } = this
            const upToNowReg = new RegExp(upToNowValue)
            if (disabled) return
            this.years = this.years.map((item) => {
                const { isSelected, ...other } = item
                return {
                    ...other,
                    isSelected: other.year === year
                }
            })
            if (upToNowReg.test(year)) {
                this.selectValue = year
                this.triggerBlur()
            } else {
                this.selectYear = year
                this.monthPanelYear = year
                this.isShowYearPanel = false
            }
        },
        /**
         * 年份相关事件 end
         */

        /**
         * 月份事件 start
         */
        createMonthData(selectYear) {
            const { disabledDate, selectValue } = this
            const selectYearValue = new Date(selectValue).getFullYear()
            const selectMonthValue = new Date(selectValue).getMonth() + 1

            // 规定为每月1号，不能取当前日期，例如30号，2月暂无1号
            const date = 1
            const list = MONTHMAP.map((item, i) => {
                const month = i + 1
                const setDate = new Date(`${selectYear}/${month}/${date}`)
                const disabled = disabledDate(setDate)
                return {
                    disabled,
                    isCurrent: false,
                    isSelected: `${selectYearValue}/${selectMonthValue}` === `${selectYear}/${month}`,
                    year: selectYear,
                    label: item,
                    timestamp: setDate.getTime(),
                    date: setDate
                }
            })
            return list
        },
        showYearPanel() {
            this.createYearDate()
            this.isShowYearPanel = true
        },
        onMonthChange(type = '') {
            const { monthPanelYear } = this
            switch (type) {
                case 'prev':
                    this.monthPanelYear = monthPanelYear - 1
                    break
                case 'next':
                    this.monthPanelYear = monthPanelYear + 1
                    break
                default:
                    break
            }
            this.currentPanelYear = monthPanelYear
        },
        handleSelectMonth(data) {
            const { date, disabled, ...other } = data
            const { valueFormat } = this
            if (disabled) return
            const value = formatDate(new Date(date), valueFormat)
            this.$emit('change', {
                ...other,
                date: value
            })
            this.selectValue = value
            this.triggerBlur()
        },
        /**
         * 月份事件 end
         */
        handleInputValue(value) {
            if (value === '') {
                this.selectValue = ''
            }
        },
        handleInputBlur() {
            this.inputValue = this.selectValueText
        },
        triggerBlur() {
            this.$refs.dateInput.focus()
            this.$nextTick(() => {
                this.$refs.dateInput.blur()
            })
        }
    }
}
