<?php

namespace miniApp\controllers;

use common\base\models\BaseArea;
use common\base\models\BaseCompanyResumePvTotal;
use common\base\models\BaseDictionary;
use common\base\models\BaseFile;
use common\base\models\BaseMember;
use common\base\models\BaseMemberActionLog;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAttachment;
use common\base\models\BaseResumeComplete;
use common\base\models\BaseResumeEquityPackage;
use common\base\models\BaseResumeEquityPackageCategorySetting;
use common\base\models\BaseResumeRefresh;
use common\base\models\BaseUploadForm;
use common\helpers\DebugHelper;
use common\helpers\FileHelper;
use common\helpers\IpHelper;
use common\service\CommonService;
use common\service\memberCancel\ResumeCancelService;
use common\service\resume\GetEditInfoService;
use common\service\resume\ResumeService;
use frontendPc\models\Member;
use frontendPc\models\ResumeAttachment;
use h5\models\Resume;
use miniApp\models\ResumeComplete;
use miniApp\models\ResumeIntention;
use miniApp\models\ResumeSetting;
use miniApp\models\UploadForm;
use yii\base\Exception;
use Yii;

class ResumeController extends BaseMiniAppController
{

    /**
     * 取消注销申请
     */
    public function actionCancelApplyCancel()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $token = Yii::$app->request->post('token');
            if (!$token) {
                return $this->fail('参数错误');
            }

            $resumeCancelService = new ResumeCancelService();
            $resumeCancelService->withdrawCancel($token);

            // 提交事务
            $transaction->commit();

            return $this->success('您的放弃注销已申请，请5分钟后重新登录');
        } catch (\Throwable $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取编辑页面信息
     * @throws Exception
     */
    public function actionGetEditInfo()
    {
        $memberId = $this->memberId;
        $resumeId = $this->getResumeId();
        //获取未完善的模块
        $resumeUnCompleteList   = ResumeComplete::getMiniAppUnCompleteList($memberId);
        $resumeUnCompleteAmount = count($resumeUnCompleteList);
        $identityTipList        = BaseResume::getIdentityTipList($resumeId);
        //获取用户基本信息
        $userInfo = BaseResume::getMobileInfo($memberId);

        return $this->success([
            'resumeUnCompleteList'   => $resumeUnCompleteList,
            'identityTipList'        => $identityTipList,
            'resumeUnCompleteAmount' => $resumeUnCompleteAmount,
            'info'                   => $userInfo,
        ]);
    }

    /**
     * 获取简历步数
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetStepNum()
    {
        try {
            $service = new ResumeService();
            $info    = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_GET_RESUME_STEP)
                ->init()
                ->run();

            $resumeCompletePercent = BaseResume::getComplete($this->memberId);

            return $this->success([
                'step'                  => $info,
                'resumeCompletePercent' => $resumeCompletePercent,
            ]);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取用户基本信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetUserBaseInfo()
    {
        try {
            $service = new ResumeService();
            $info    = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_GET_RESUME_STEP_ONE)
                ->init()
                ->run();

            return $this->success($info);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 保存用户基本信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSaveUserBaseInfo()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_SAVE_RESUME_STEP_ONE)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取简历第三步下拉框字典
     */
    public function actionGetStepThreeParams()
    {
        $service = new ResumeService();
        $info    = $service->setPlatform(CommonService::PLATFORM_MINI)
            ->setOparetion(ResumeService::OPERATION_TYPE_GET_RESUME_STEP_THREE_DROP_LIST)
            ->init()
            ->run();

        return $this->success($info);
    }

    /**
     * 获取简历第二步资料
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetStepTwoInfo()
    {
        $service = new ResumeService();
        $info    = $service->setPlatform(CommonService::PLATFORM_MINI)
            ->setOparetion(ResumeService::OPERATION_TYPE_GET_MINIAPP_RESUME_STEP_TWO)
            ->init()
            ->run();

        return $this->success($info);
    }

    /**
     * 保存简历第二步资料
     * @return void|\yii\console\Response|\yii\web\Response
     */
    public function actionSaveStepTwoInfo()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_SAVE_MINIAPP_RESUME_STEP_TWO)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取简历第三步内容
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetStepThreeInfo()
    {
        $service = new ResumeService();

        $info = $service->setPlatform(CommonService::PLATFORM_MINI)
            ->setOparetion(ResumeService::OPERATION_TYPE_GET_MINIAPP_RESUME_STEP_THREE)
            ->init()
            ->run();

        return $this->success($info);
    }

    /**
     * 保存简历第三步内容
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSaveStepThreeInfo()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            //获取服务对象
            $service = new ResumeService();

            $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_SAVE_MINIAPP_RESUME_STEP_THREE)
                ->init()
                ->run();

            //返回用户的简历完成度百分比
            $resumeComplete = (int)BaseResume::getComplete($this->memberId);
            //求职者解锁所有功能简历完成度
            $completePercent = Yii::$app->params['completeResumePercent'];

            $transaction->commit();

            return $this->success([
                'title'   => '创建简历成功！',
                'content' => "当前简历完成度{$resumeComplete}%，简历完善度达{$completePercent}%方可投递。",
            ]);
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取附件简历列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetResumeAttachmentList()
    {
        $memberId = $this->memberId;

        return $this->success(BaseResumeAttachment::getList($memberId));
    }

    /**
     * 上传附件简历
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionUpload()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        $memberId    = $this->memberId;
        $name        = Yii::$app->request->post('name');
        try {
            /* BaseResumeAttachment::checkLimit($memberId);

             $model = new BaseUploadForm();

             $data = $model->resume($memberId);

             //保存附件简历记录
             $data['memberId'] = $memberId;
             $data['resumeId'] = $this->getResumeId();
             $token            = BaseResumeAttachment::saveAttachment($data)['token'];

             //获取去除后缀后的文件名
             $trueName = FileHelper::getFileName($name);
             BaseResumeAttachment::changeFileName($token, $trueName, $memberId);

             $transaction->commit();

             return $this->success([
                 'name'     => $data['name'],
                 'token'    => $token,
                 //返回不带后缀的名称
                 'trueName' => $trueName,
             ]);*/
            $trueName = FileHelper::getFileName($name);
            $model    = new BaseUploadForm();
            $res      = $model->resumeNew([
                'memberId' => $memberId,
                'resumeId' => $this->getResumeId(),
                'trueName' => $trueName,
            ]);
            //获取去除后缀后的文件名
            $trueName = FileHelper::getFileName($name);
            BaseResumeAttachment::changeFileName($res['token'], $trueName, $memberId);

            $transaction->commit();
            $res['trueName'] = $trueName;

            return $this->success($res);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 上传附件材料
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionUploadFile()
    {
        $model = new BaseUploadForm();
        $name  = Yii::$app->request->post('name');

        $memberId = $this->memberId;
        if (!$memberId) {
            return $this->error('上传失败,没有权限');
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $data = $model->resumeAttachment($memberId);

            //获取去除后缀后的文件名
            $trueName = FileHelper::getFileName($name);
            BaseFile::changeFileName($data['id'], $trueName, $memberId);

            $transaction->commit();

            return $this->success([
                'id'   => (string)$data['id'],
                'name' => $name,
            ]);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->error($e->getMessage());
        }
    }

    /**
     * 重命名附件简历
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionRenameAttachment()
    {
        $transaction = \Yii::$app->db->beginTransaction();

        $token   = Yii::$app->request->post('token');
        $newName = Yii::$app->request->post('name');
        try {
            if (empty($token) || empty($newName)) {
                return $this->fail('缺少参数');
            }
            BaseResumeAttachment::changeFileName($token, $newName, $this->memberId, true);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 删除附件简历
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelAttachment()
    {
        $token    = \Yii::$app->request->post('token');
        $memberId = $this->memberId;
        //获取附件简历信息
        $resumeAttachmentInfo = ResumeAttachment::find()
            ->where(['token' => $token])
            ->select([
                'status',
                'member_id',
            ])
            ->asArray()
            ->one();
        if (empty($resumeAttachmentInfo)) {
            return $this->fail('附件简历不存在');
        }
        if ($resumeAttachmentInfo['status'] == ResumeAttachment::STATUS_DELETE) {
            return $this->fail('附件简历状态错误');
        }
        if ($resumeAttachmentInfo['member_id'] != $memberId) {
            return $this->fail('附件简历不属于当前用户');
        }
        try {
            ResumeAttachment::del($token, $memberId);

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 获取附件简历页面提示文案信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetResumeAttachmentInfo()
    {
        //最多能上传多少份简历
        $data['limitAmount'] = BaseResumeAttachment::LIMIT_RECORD_AMOUNT;
        //上传大小限制
        $data['size']     = 15;
        $data['errorMsg'] = "最多只能有{$data['limitAmount']}份附件简历，请删除一份后再上传。";

        return $this->success($data);
    }

    /**
     * 刷新简历
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionRefresh()
    {
        $resumeId = $this->getResumeId();
        $data     = BaseResumeRefresh::create($resumeId, BaseResumeRefresh::PLATFORM_MINI_APP);

        return $this->success($data);
    }

    /**
     * 获取隐私设置
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetPrivacyInfo()
    {
        $memberId = $this->memberId;
        try {
            $info = ResumeSetting::getPrivacyInfo($memberId);

            return $this->success($info);
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 修改匿名显示状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionChangeAnonymousStatus()
    {
        $memberId = $this->memberId;

        try {
            ResumeSetting::changeAnonymousStatus($memberId);

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 修改简历显示状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionChangeShowStatus()
    {
        $memberId = $this->memberId;
        $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id');

        try {
            ResumeSetting::changeHideStatus($resumeId);

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 获取简历基本信息，页面渲染
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionBaseInfo()
    {
        $memberId = $this->memberId;
        $info     = Resume::getBaseInfo($memberId);

        return $this->success($info);
    }

    /**
     * 保存基本信息页面
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSaveBaseInfo()
    {
        $data = Yii::$app->request->post();

        $transaction = \Yii::$app->db->beginTransaction();

        try {
            //判断必填参数
            if (!$data['name'] || !$data['gender'] || !$data['birthday'] || !$data['householdRegisterId'] || !$data['politicalStatusId'] || !$data['email']) {
                throw  new Exception('缺少必填参数');
            }
            if (strlen($data['name']) > 32) {
                throw  new Exception('限制输入32个字符');
            }
            $data['memberId'] = $this->memberId;
            Resume::saveBaseInfo($data);

            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 获取个人优势数据
     * @return \yii\console\Response|\yii\web\Response
     * @throws Exception
     */
    public function actionGetAdvantageInfo()
    {
        $service = new GetEditInfoService();
        $info    = $service->setOparetion(GetEditInfoService::INFO_ADVANTAGE)
            ->init([
                'memberId' => $this->memberId,
            ])
            ->run();

        return $this->success($info);
    }

    /**
     * 编辑个人优势
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditAdvantageInfo()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setOparetion(ResumeService::OPERATION_TYPE_ADVANTAGE_EDIT)
                ->setPlatform(CommonService::PLATFORM_MINI)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 上传头像
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionUploadAvatar()
    {
        $model = new BaseUploadForm();

        $transaction = Yii::$app->db->beginTransaction();
        $memberId    = $this->memberId;
        try {
            $data = $model->uploadAvatar();

            //新增操作日志
            $log_data = [
                'content'     => '上传头像，memberId：' . $memberId,
                'member_id'   => $memberId,
                'member_type' => BaseMember::TYPE_PERSON,
                'is_login'    => BaseMember::IS_LOGIN_YES,
            ];
            // 写日志
            BaseMemberActionLog::log($log_data);
            //更新简历最后更新时间
            $resumeId = BaseMember::getMainId($memberId);
            BaseResume::updateLastUpdateTime($resumeId);

            //修改头像
            BaseMember::changeAvatar([
                'memberId' => $memberId,
                'avatar'   => $data['path'],
            ]);
            $transaction->commit();

            return $this->success([
                'id'      => (string)$data['id'],
                'url'     => $data['path'],
                'fullUrl' => FileHelper::getFullUrl($data['path']),
            ]);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 更新简历到岗时间与工作状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionUpdatePostWork()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用编辑服务
            $result = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_UPDATE_RESUME_POST_WORK)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success($result);
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 修改用户求职状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditWorkStatus()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $memberId   = $this->memberId;
            $workStatus = Yii::$app->request->post('workStatus');
            if (!$workStatus) {
                throw new Exception('参数错误');
            }

            BaseResume::editWorkStatus($memberId, $workStatus);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 修改用户到岗时间类型
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditArriveDateType()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $memberId       = $this->memberId;
            $arriveDateType = Yii::$app->request->post('arriveDateType');
            if (!$arriveDateType) {
                throw new Exception('参数错误');
            }

            BaseResume::editArriveDateType($memberId, $arriveDateType);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 生成下载的临时token
     * @return void|\yii\console\Response|\yii\web\Response
     */
    public function actionCreateDownloadAttachmentToken()
    {
        $token = Yii::$app->request->post('token');

        try {
            $return = ResumeAttachment::createTmpMineDownloadToken($this->memberId, $token);

            return $this->success([
                'token'    => $return['token'],
                'fileType' => $return['fileType'],
            ]);
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 下载个人简历
     * @return void|\yii\console\Response|\yii\web\Response
     */
    public function actionDownloadAttachment()
    {
        $token = Yii::$app->request->get('token');

        try {
            $data = ResumeAttachment::decodeTmpMineDownloadToken($token);

            $memberId = $data['memberId'];
            $token    = $data['token'];

            ResumeAttachment::downloadMine($memberId, $token);

            exit;
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    public function actionGetVipInfo()
    {
        $memberId       = $this->memberId;
        $info           = Resume::getVipInfo($memberId);
        $info['buyUrl'] = Resume::BUY_URL;

        unset($info['id']);

        // 这里多加一个字段，获取是否有求职快的权限
        $resumeId           = $this->getResumeId();
        $info['hasJobFast'] = BaseResumeEquityPackage::isPackageEffect(BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP,
            $resumeId);

        $isShowJobFastBuy = true;
        if ($info['hasJobFast']) {
            $isShowJobFastBuy = false;
        }

        if ($info['isVip'] && $info['isVip'] == 2) {
            $isShowJobFastBuy = false;
        }

        $info['isShowJobFastBuy'] = $isShowJobFastBuy;

        return $this->success($info);
    }

    public function actionGetVipPvChart()
    {
        $resumeId = $this->getResumeId();
        $pvData   = BaseCompanyResumePvTotal::getPvExposure($resumeId);

        return $this->success($pvData);
    }

    /**
     * 检查用户状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCheckUserStatus()
    {
        $isLogin = $this->checkLogin();

        $token = \Yii::$app->request->getHeaders()
            ->get('authorization-token');

        return $this->success(['isLogin' => $isLogin]);
    }

}